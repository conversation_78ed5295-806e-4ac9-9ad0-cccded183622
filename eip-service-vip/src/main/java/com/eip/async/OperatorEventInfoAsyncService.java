package com.eip.async;

import com.eip.common.enums.EventKindEnum;
import com.eip.common.enums.EventOperateTypeEnum;
import com.eip.common.enums.OperateTypeEnum;
import com.eip.common.util.PublicUtil;
import com.eip.facade.vip.dto.OperatorDto;
import com.eip.facade.vip.entity.*;
import com.eip.facade.vip.service.EventInfoService;
import com.eip.service.vip.orgdao.InviteTemplateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2025-05-22 15:32
 * @DESCRIPTION: 服务操作日志
 */
@Slf4j
@Component
public class OperatorEventInfoAsyncService {

    @Autowired
    private EventInfoService eventInfoService;
    @Autowired
    private InviteTemplateMapper inviteTemplateMapper;

    /**
     * 构建日志主体
     * @param taskDetail
     * @return
     */
    public String buildObjName(TaskDetail taskDetail) {
        if(Objects.isNull(taskDetail)) return null;
        String objOperName = taskDetail.getF_task_dtl_id()+" ";
        if(StringUtils.isNotBlank(taskDetail.getTaskTopic())) objOperName+=taskDetail.getTaskTopic()+" ";
        if(StringUtils.isNotBlank(taskDetail.getCompanyName())) objOperName+=taskDetail.getCompanyName()+" ";
        if(StringUtils.isNotBlank(taskDetail.getProjectName())) objOperName+=taskDetail.getProjectName()+" ";
        return objOperName;
    }

    /**
     * 进馆证
     */
    @Async
    public void serveCertEventInfoAsync(String operatorType, OperatorDto operatorDto, TaskDetail taskDetail, ServeCert oldServe_cert, ServeCert newServe_cert){
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        operatorDto.setF_org_num(taskDetail.getOrgNum());
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getF_task_kind_code());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.EXHIBITOR_CUSTOMER_SERVICE_CENTER.getId();
        String operateTypeName = OperateTypeEnum.MODIFY.getName();

        StringBuilder builder = new StringBuilder();

        if(EventOperateTypeEnum.Add.getCode().equals(operatorType) || EventOperateTypeEnum.Modify.getCode().equals(operatorType)){
            builder.append("工单:"+taskDetail.getTaskTopic()+"("+taskDetail.getF_task_dtl_id()+")").append("\r\n");
            String certName = StringUtils.isNotBlank(newServe_cert.getF_cert_name()) ? newServe_cert.getF_cert_name() : "空";
            String fullNamePinyin = StringUtils.isNotBlank(newServe_cert.getFullNamePinyin()) ? newServe_cert.getFullNamePinyin() : "空";
            String surnNamePinyin = StringUtils.isNotBlank(newServe_cert.getSurnamePinyin()) ? newServe_cert.getSurnamePinyin() : "空";
            String namePinyin = StringUtils.isNotBlank(newServe_cert.getNamePinyin()) ? newServe_cert.getNamePinyin() : "空";
            String position = StringUtils.isNotBlank(newServe_cert.getF_cert_position()) ? newServe_cert.getF_cert_position() : "空";
            String sex = StringUtils.isNotBlank(newServe_cert.getF_sex()) ? PublicUtil.getSexByState(Integer.valueOf(newServe_cert.getF_sex()),"") : "空";
            String country = StringUtils.isNotBlank(newServe_cert.getCountry()) ? newServe_cert.getCountry() : "空";
            String province = StringUtils.isNotBlank(newServe_cert.getProvince()) ? newServe_cert.getProvince() : "空";
            String city = StringUtils.isNotBlank(newServe_cert.getCity()) ? newServe_cert.getCity() : "空";
            String districtCounty = StringUtils.isNotBlank(newServe_cert.getDistrictCounty()) ? newServe_cert.getDistrictCounty() : "空";
            String address = StringUtils.isNotBlank(newServe_cert.getAddress()) ? newServe_cert.getAddress() : "空";
            String tel = StringUtils.isNotBlank(newServe_cert.getF_tel()) ? newServe_cert.getF_tel() : "空";
            String mobilePhoneNum = StringUtils.isNotBlank(newServe_cert.getMobilePhoneNum()) ? newServe_cert.getMobilePhoneNum() : "空";
            String email = StringUtils.isNotBlank(newServe_cert.getF_email()) ? newServe_cert.getF_email() : "空";
            String passNumber = StringUtils.isNotBlank(newServe_cert.getF_pass_number()) ? newServe_cert.getF_pass_number() : "空";
            String idTypeName = newServe_cert.getF_id_type_name();
            String idCard = StringUtils.isNotBlank(newServe_cert.getF_id_card()) ? newServe_cert.getF_id_card() : "空";

            if(Objects.isNull(oldServe_cert)){
                operateTypeName = OperateTypeEnum.ADD.getName();
                Integer f_serve_cert_id = newServe_cert.getF_serve_cert_id();
                builder.append("数据Id："+f_serve_cert_id).append("\r\n");
                builder.append("姓名："+certName).append("\r\n");builder.append("姓名全拼："+fullNamePinyin).append("\r\n");
                builder.append("姓氏全拼："+surnNamePinyin).append("\r\n");builder.append("名字全拼："+namePinyin).append("\r\n");
                builder.append("性别："+sex).append("\r\n");builder.append("职位："+position).append("\r\n");
                builder.append("国家："+country).append("\r\n");builder.append("省份："+province).append("\r\n");builder.append("城市："+city).append("\r\n");
                builder.append("区县："+districtCounty).append("\r\n");builder.append("地址："+address).append("\r\n");
                builder.append("电话："+tel).append("\r\n");
                builder.append("护照号："+passNumber).append("\r\n");
                builder.append("手机号："+mobilePhoneNum).append("\r\n");builder.append("邮箱："+email).append("\r\n");
                builder.append("证件类型："+idTypeName).append("\r\n");builder.append("证件号码："+idCard).append("\r\n");
            }else{
                Integer f_serve_cert_id = oldServe_cert.getF_serve_cert_id();
                String oldCertName = StringUtils.isNotBlank(oldServe_cert.getF_cert_name()) ? oldServe_cert.getF_cert_name() : "空";
                String oldFullNamePinyin = StringUtils.isNotBlank(oldServe_cert.getFullNamePinyin()) ? oldServe_cert.getFullNamePinyin() : "空";
                String oldSurnNamePinyin = StringUtils.isNotBlank(oldServe_cert.getSurnamePinyin()) ? oldServe_cert.getSurnamePinyin() : "空";
                String oldNamePinyin = StringUtils.isNotBlank(oldServe_cert.getNamePinyin()) ? oldServe_cert.getNamePinyin() : "空";
                String oldPosition = StringUtils.isNotBlank(oldServe_cert.getF_cert_position()) ? oldServe_cert.getF_cert_position() : "空";
                String oldSex = StringUtils.isNotBlank(oldServe_cert.getF_sex()) ? PublicUtil.getSexByState(Integer.valueOf(oldServe_cert.getF_sex()),"") : "空";
                String oldCountry = StringUtils.isNotBlank(oldServe_cert.getCountry()) ? oldServe_cert.getCountry() : "空";
                String oldProvince = StringUtils.isNotBlank(oldServe_cert.getProvince()) ? oldServe_cert.getProvince() : "空";
                String oldCity = StringUtils.isNotBlank(oldServe_cert.getCity()) ? oldServe_cert.getCity() : "空";
                String oldDistrictCounty = StringUtils.isNotBlank(oldServe_cert.getDistrictCounty()) ? oldServe_cert.getDistrictCounty() : "空";
                String oldAddress = StringUtils.isNotBlank(oldServe_cert.getAddress()) ? oldServe_cert.getAddress() : "空";
                String oldTel = StringUtils.isNotBlank(oldServe_cert.getF_tel()) ? oldServe_cert.getF_tel() : "空";
                String oldMobilePhoneNum = StringUtils.isNotBlank(oldServe_cert.getMobilePhoneNum()) ? oldServe_cert.getMobilePhoneNum() : "空";
                String oldEmail = StringUtils.isNotBlank(oldServe_cert.getF_email()) ? oldServe_cert.getF_email() : "空";
                String oldPassNumber = StringUtils.isNotBlank(oldServe_cert.getF_pass_number()) ? oldServe_cert.getF_pass_number() : "空";
                String oldIdTypeName = oldServe_cert.getF_id_type_name();
                String oldIdCard = StringUtils.isNotBlank(oldServe_cert.getF_id_card()) ? oldServe_cert.getF_id_card() : "空";
                builder.append("数据Id："+f_serve_cert_id).append("\r\n");
                if(!oldCertName.equals(certName)) builder.append("姓名由'"+oldCertName).append("'变成'").append(certName).append("'\r\n");
                if(!oldFullNamePinyin.equals(fullNamePinyin)) builder.append("姓名全拼由'"+oldFullNamePinyin).append("'变成'").append(fullNamePinyin).append("\r\n");
                if(!oldSurnNamePinyin.equals(surnNamePinyin)) builder.append("姓氏全拼由'"+oldSurnNamePinyin).append("'变成'").append(surnNamePinyin).append("\r\n");
                if(!oldNamePinyin.equals(namePinyin)) builder.append("名字全拼由'"+oldNamePinyin).append("'变成'").append(namePinyin).append("\r\n");
                if(!oldPosition.equals(position)) builder.append("职位由'"+oldPosition).append("'变成'").append(position).append("\r\n");
                if(!oldSex.equals(sex)) builder.append("性别由'"+oldSex).append("'变成'").append(sex).append("\r\n");
                if(!oldCountry.equals(country)) builder.append("国家由'"+oldCountry).append("'变成'").append(country).append("\r\n");
                if(!oldProvince.equals(province)) builder.append("省份由'"+oldProvince).append("'变成'").append(province).append("\r\n");
                if(!oldCity.equals(city)) builder.append("城市由'"+oldCity).append("'变成'").append(city).append("\r\n");
                if(!oldDistrictCounty.equals(districtCounty)) builder.append("区县由'"+oldDistrictCounty).append("'变成'").append(districtCounty).append("\r\n");
                if(!oldAddress.equals(address)) builder.append("地址由'"+oldAddress).append("'变成'").append(address).append("\r\n");
                if(!oldTel.equals(tel)) builder.append("电话由'"+oldTel).append("'变成'").append(tel).append("\r\n");
                if(!oldMobilePhoneNum.equals(mobilePhoneNum)) builder.append("手机号由'"+oldMobilePhoneNum).append("'变成'").append(mobilePhoneNum).append("\r\n");
                if(!oldEmail.equals(email)) builder.append("邮箱由'"+oldEmail).append("'变成'").append(email).append("\r\n");
                if(!oldPassNumber.equals(passNumber)) builder.append("护照号由'"+oldPassNumber).append("'变成'").append(passNumber).append("\r\n");
                if(!oldIdTypeName.equals(idTypeName)) builder.append("证件类型由'"+oldIdTypeName).append("'变成'").append(idTypeName).append("\r\n");
                if(!oldIdCard.equals(idCard)) builder.append("证件号码由'"+oldIdCard).append("'变成'").append(idCard).append("\r\n");
            }
        }else if(EventOperateTypeEnum.Delete.getCode().equals(operatorType)){
            String memo = "删除进馆证 "+oldServe_cert.getF_cert_name()+"("+oldServe_cert.getF_serve_cert_id()+")"+" 信息成功";
            builder.append(memo).append("\r\n");
            operateTypeName = OperateTypeEnum.DELETE.getName();
        }
        eventInfoService.saveEventOrEventChild(operatorDto,builder.toString(),eventKindId,
                operateTypeName, Objects.nonNull(taskDetail) ? String.valueOf(taskDetail.getF_task_dtl_id()) : null,buildObjName(taskDetail));

    }


    /**
     * 楣板
     */
    @Async
    public void serveFasciaEventInfoAsync(OperatorDto operatorDto, TaskDetail taskDetail, ServeFascia oldServeFascia, ServeFascia newServeFasicia) {
        if(Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        StringBuilder builder = new StringBuilder();
        String operateTypeName = OperateTypeEnum.MODIFY.getName();
        builder.append("工单:"+taskDetail.getTaskTopic()+" - 楣板("+taskDetail.getF_task_dtl_id()+")").append("\r\n");
        String fasciaName = StringUtils.isNotBlank(newServeFasicia.getFasciaName()) ? newServeFasicia.getFasciaName() : "空";
        String fasciaNameEn = StringUtils.isNotBlank(newServeFasicia.getFasciaNameEn()) ? newServeFasicia.getFasciaNameEn() : "空";
        String fasciaAmount = newServeFasicia.getFasciaAmount() != null ? String.valueOf(newServeFasicia.getFasciaAmount()) : "空";
        if(Objects.isNull(oldServeFascia)){
            operateTypeName = OperateTypeEnum.ADD.getName();
            builder.append("中文楣板："+fasciaName).append("\r\n");
            builder.append("英文楣板："+fasciaNameEn).append("\r\n");
            builder.append("楣板条数："+fasciaAmount).append("\r\n");
        }else{
            String oldFasciaName = StringUtils.isNotBlank(oldServeFascia.getFasciaName()) ? oldServeFascia.getFasciaName() : "空";
            String oldFasciaNameEn = StringUtils.isNotBlank(oldServeFascia.getFasciaNameEn()) ? oldServeFascia.getFasciaNameEn() : "空";
            String oldFasciaAmount = oldServeFascia.getFasciaAmount() != null ? String.valueOf(oldServeFascia.getFasciaAmount()) : "空";
            if(!oldFasciaName.equals(fasciaName)) builder.append("中文楣板由'"+oldFasciaName).append("'变成'").append(fasciaName).append("'\r\n");
            if(!oldFasciaNameEn.equals(fasciaNameEn)) builder.append("英文楣板由'"+oldFasciaNameEn).append("'变成'").append(fasciaNameEn).append("'\r\n");
            if(!oldFasciaAmount.equals(fasciaAmount)) builder.append("楣板条数由'"+oldFasciaAmount).append("'变成'").append(fasciaAmount).append("'\r\n");
        }
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getF_task_kind_code());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.EXHIBITOR_CUSTOMER_SERVICE_CENTER.getId();
        eventInfoService.saveEventOrEventChild(operatorDto,String.valueOf(builder),eventKindId,
                operateTypeName , String.valueOf(taskDetail.getF_task_dtl_id()),buildObjName(taskDetail));
    }



    /**
     * 展商邀请函
     * @param operatorType
     * @param invitation
     * @param operatorDto
     */
    @Async
    public void buildExhibitorInvitationEventInfo(String operatorType, Invitation oldInvitation,Invitation invitation, OperatorDto operatorDto) {
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto)) return;
        String operateTypeName = OperateTypeEnum.MODIFY.getName();

        StringBuilder builder = new StringBuilder();
        Integer invitationId = invitation.getInvitationId();
        Integer serveBoothId = invitation.getServeBoothId();
        builder.append("邀请函Id:"+invitationId).append("\r\n");
        builder.append("参展记录Id:"+serveBoothId!=null ? serveBoothId : "空").append("\r\n");

        String companyName = StringUtils.isNotBlank(invitation.getCompanyName()) ? invitation.getCompanyName() : "空";
        String companyNameAbbr = StringUtils.isNotBlank(invitation.getCompanyNameAbbr()) ? invitation.getCompanyNameAbbr() : "空";
        String companyProfile = StringUtils.isNotBlank(invitation.getCompanyProfile()) ? invitation.getCompanyProfile() :"空";
        String logo = StringUtils.isNotBlank(invitation.getLogo()) ? invitation.getLogo() : "空";
        String invitationTitle = StringUtils.isNotBlank(invitation.getInvitationTitle()) ? invitation.getInvitationTitle() : "空";
        Integer version = invitation.getVersion() !=null ? invitation.getVersion() : 1;
        String versionName = 1 == version ? "中文" : "英文";
        Long templateId = invitation.getTemplateId();
        if(EventOperateTypeEnum.Add.getCode().equals(operatorType)){
            operateTypeName = OperateTypeEnum.ADD.getName();
            builder.append("公司名称："+companyName).append("\r\n");
            builder.append("公司简称："+companyNameAbbr).append("\r\n");
            builder.append("公司简介："+companyProfile).append("\r\n");
            builder.append("展商Logo："+logo).append("\r\n");
            builder.append("邀请函标题："+invitationTitle).append("\r\n");
            builder.append("邀请函版本："+versionName).append("\r\n");
            if(templateId!=null){
                InviteTemplate inviteTemplate = inviteTemplateMapper.selectByPrimaryKey(templateId);
                if(Objects.nonNull(inviteTemplate)){
                    builder.append("选择模板："+inviteTemplate.getInviteTemplateName()+templateId).append("\r\n");
                }
            }else{
                builder.append("选择模板：空").append("\r\n");
            }
        }else{
            String oldCompanyName = StringUtils.isNotBlank(oldInvitation.getCompanyName()) ? oldInvitation.getCompanyName() : "空";
            String oldCompanyNameAbbr = StringUtils.isNotBlank(oldInvitation.getCompanyNameAbbr()) ? oldInvitation.getCompanyNameAbbr() : "空";
            String oldCompanyProfile = StringUtils.isNotBlank(oldInvitation.getCompanyProfile()) ? oldInvitation.getCompanyProfile() :"空";
            String oldLogo = StringUtils.isNotBlank(oldInvitation.getLogo()) ? oldInvitation.getLogo() : "空";
            String oldInvitationTitle = StringUtils.isNotBlank(oldInvitation.getInvitationTitle()) ? oldInvitation.getInvitationTitle() : "空";
            Integer oldVersion = oldInvitation.getVersion() !=null ? oldInvitation.getVersion() : 1;
            String oldVersionName = 1 == oldVersion ? "中文" : "英文";
            Long oldTemplateId = oldInvitation.getTemplateId();
            if(!oldCompanyName.equals(companyName)) builder.append("公司名称由'"+oldCompanyName).append("'变成'").append(companyName).append("'\r\n");
            if(!oldCompanyNameAbbr.equals(companyNameAbbr)) builder.append("公司简称由'"+oldCompanyNameAbbr).append("'变成'").append(companyNameAbbr).append("'\r\n");
            if(!oldCompanyProfile.equals(companyProfile)) builder.append("公司简介由'"+oldCompanyProfile).append("'变成'").append(companyProfile).append("'\r\n");
            if(!oldLogo.equals(logo)) builder.append("展商Logo由'"+oldLogo).append("'变成'").append(logo).append("'\r\n");
            if(!oldInvitationTitle.equals(invitationTitle)) builder.append("邀请函标题由'"+oldInvitationTitle).append("'变成'").append(invitationTitle).append("'\r\n");
            if(!oldVersionName.equals(versionName)) builder.append("邀请函版本由'"+oldVersionName).append("'变成'").append(versionName).append("'\r\n");
            if(
                (oldTemplateId==null && templateId != null)
                ||
                (oldTemplateId!=null && templateId!=null && !oldTemplateId.equals(templateId))
            ){
                InviteTemplate inviteTemplate = inviteTemplateMapper.selectByPrimaryKey(templateId);
                if(Objects.nonNull(inviteTemplate)){
                    builder.append("选择模板："+inviteTemplate.getInviteTemplateName()+templateId).append("\r\n");
                }
            }
            if(
                    (oldTemplateId == null && templateId ==null)
                    ||
                    (oldTemplateId!=null && templateId == null)
            ){
                builder.append("选择模板：空").append("\r\n");
            }
        }

        String operObjName = invitationId + " " + companyName;
        Integer eventKindId = EventKindEnum.EXHIBITOR_INVITATION.getId();
        eventInfoService.saveEventOrEventChild(operatorDto,String.valueOf(builder),eventKindId,
                operateTypeName ,String.valueOf(invitationId) ,operObjName);
    }

    /**
     * 邀请函
     * @param operatorType
     * @param operatorDto
     * @param taskDetail
     * @param oldSInvite
     * @param newSInvite
     */
    @Async
    public void serveInviteEventInfo(String operatorType, OperatorDto operatorDto, TaskDetail taskDetail, ServeInvite oldSInvite, ServeInvite newSInvite) {
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        operatorDto.setF_org_num(taskDetail.getOrgNum());
        String operateTypeName = OperateTypeEnum.MODIFY.getName();
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getF_task_kind_code());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.EXHIBITOR_CUSTOMER_SERVICE_CENTER.getId();

        StringBuilder builder = new StringBuilder();

        if(EventOperateTypeEnum.Add.getCode().equals(operatorType) || EventOperateTypeEnum.Modify.getCode().equals(operatorType)){
            builder.append("工单:"+taskDetail.getTaskTopic()+"("+taskDetail.getF_task_dtl_id()+")").append("\r\n");
            Integer f_serve_invite_id = newSInvite.getF_serve_invite_id();
            builder.append("数据Id："+f_serve_invite_id).append("\r\n");
            String f_name_cn = StringUtils.isNotBlank(newSInvite.getF_name_cn()) ? newSInvite.getF_name_cn() : "空";
            String mobile = StringUtils.isNotBlank(newSInvite.getF_mobile()) ? newSInvite.getF_mobile() : "空";
            String email = StringUtils.isNotBlank(newSInvite.getF_email()) ? newSInvite.getF_email() : "空";
            String passNumber = StringUtils.isNotBlank(newSInvite.getF_passport_num()) ? newSInvite.getF_passport_num() : "空";
            if(Objects.isNull(oldSInvite)){
                operateTypeName = OperateTypeEnum.ADD.getName();
                builder.append("姓名："+f_name_cn).append("\r\n");
                builder.append("手机："+mobile).append("\r\n");
                builder.append("邮箱："+email).append("\r\n");
                builder.append("护照号码："+passNumber).append("\r\n");
            }else{
                String oldF_name_cn = StringUtils.isNotBlank(oldSInvite.getF_name_cn()) ? oldSInvite.getF_name_cn() : "空";
                String oldMobile = StringUtils.isNotBlank(oldSInvite.getF_mobile()) ? oldSInvite.getF_mobile() : "空";
                String oldEmail = StringUtils.isNotBlank(oldSInvite.getF_email()) ? oldSInvite.getF_email() : "空";
                String oldPassNumber = StringUtils.isNotBlank(oldSInvite.getF_passport_num()) ? oldSInvite.getF_passport_num() : "空";
                if(!oldF_name_cn.equals(f_name_cn)) builder.append("姓名由'"+oldF_name_cn).append("'变成'").append(f_name_cn).append("'\r\n");
                if(!oldMobile.equals(mobile)) builder.append("手机由'"+oldMobile).append("'变成'").append(mobile).append("'\r\n");
                if(!oldEmail.equals(email)) builder.append("邮箱由'"+oldEmail).append("'变成'").append(email).append("'\r\n");
                if(!oldPassNumber.equals(passNumber)) builder.append("护照号由'"+oldPassNumber).append("'变成'").append(passNumber).append("'\r\n");
            }
        }else if(EventOperateTypeEnum.Delete.getCode().equals(operatorType)){
            String memo = "删除邀请函 "+oldSInvite.getF_name_cn()+"("+oldSInvite.getF_serve_invite_id()+")"+" 信息成功";
            builder.append(memo).append("\r\n");
            operateTypeName =  OperateTypeEnum.DELETE.getName();
        }
        eventInfoService.saveEventOrEventChild(operatorDto,builder.toString(),eventKindId,
                operateTypeName , Objects.nonNull(taskDetail) ? String.valueOf(taskDetail.getF_task_dtl_id()) : null,buildObjName(taskDetail));
    }

    /**
     * 待办签证
     * @param operatorType
     * @param operatorDto
     * @param taskDetail
     * @param oldServeVisa
     * @param newServeVisa
     */
    @Async
    public void serveVisaEventInfo(String operatorType, OperatorDto operatorDto, TaskDetail taskDetail, ServeVisa oldServeVisa, ServeVisa newServeVisa) {
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        operatorDto.setF_org_num(taskDetail.getOrgNum());
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getF_task_kind_code());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.EXHIBITOR_CUSTOMER_SERVICE_CENTER.getId();
        String operateTypeName = OperateTypeEnum.MODIFY.getName();

        StringBuilder builder = new StringBuilder();
        if(EventOperateTypeEnum.Add.getCode().equals(operatorType) || EventOperateTypeEnum.Modify.getCode().equals(operatorType)){
            builder.append("工单:"+taskDetail.getTaskTopic()+"("+taskDetail.getF_task_dtl_id()+")").append("\r\n");
            builder.append("数据Id:"+newServeVisa.getF_serve_visa_id()).append("\r\n");
            String f_name = StringUtils.isNotBlank(newServeVisa.getF_name()) ? newServeVisa.getF_name() : "空";
            String mobile = StringUtils.isNotBlank(newServeVisa.getF_mobile()) ? newServeVisa.getF_mobile() : "空";
            String email = StringUtils.isNotBlank(newServeVisa.getF_email()) ? newServeVisa.getF_email() : "空";
            String idNumber = StringUtils.isNotBlank(newServeVisa.getF_id_number()) ? newServeVisa.getF_id_number() : "空";
            String passNumber = StringUtils.isNotBlank(newServeVisa.getF_pass_number()) ? newServeVisa.getF_pass_number() : "空";
            if(Objects.isNull(oldServeVisa)){
                operateTypeName = OperateTypeEnum.ADD.getName();
                builder.append("姓名："+f_name).append("\r\n");
                builder.append("手机："+mobile).append("\r\n");
                builder.append("邮箱："+email).append("\r\n");
                builder.append("身份证号："+idNumber).append("\r\n");
                builder.append("护照号码："+passNumber).append("\r\n");
            }else{
                String oldName = StringUtils.isNotBlank(oldServeVisa.getF_name()) ? oldServeVisa.getF_name() : "空";
                String oldMobile = StringUtils.isNotBlank(oldServeVisa.getF_mobile()) ? oldServeVisa.getF_mobile() : "空";
                String oldEmail = StringUtils.isNotBlank(oldServeVisa.getF_email()) ? oldServeVisa.getF_email() : "空";
                String oldIdNumber = StringUtils.isNotBlank(oldServeVisa.getF_id_number()) ? oldServeVisa.getF_id_number() : "空";
                String oldPassNumber = StringUtils.isNotBlank(oldServeVisa.getF_pass_number()) ? oldServeVisa.getF_pass_number() : "空";
                if(!oldName.equals(f_name)) builder.append("姓名由'"+oldName).append("'变成'").append(f_name).append("'\r\n");
                if(!oldMobile.equals(mobile)) builder.append("手机由'"+oldMobile).append("'变成'").append(mobile).append("'\r\n");
                if(!oldEmail.equals(email)) builder.append("邮箱由'"+oldEmail).append("'变成'").append(email).append("'\r\n");
                if(!oldIdNumber.equals(idNumber)) builder.append("身份证号由'"+oldIdNumber).append("'变成'").append(idNumber).append("'\r\n");
                if(!oldPassNumber.equals(passNumber)) builder.append("护照号由'"+oldPassNumber).append("'变成'").append(passNumber).append("'\r\n");
            }
        }else if(EventOperateTypeEnum.Delete.getCode().equals(operatorType)){
            String memo = "删除代办签证数据 "+oldServeVisa.getF_name()+"("+oldServeVisa.getF_serve_visa_id()+")"+" 信息成功";
            builder.append(memo).append("\r\n");
            operateTypeName = OperateTypeEnum.DELETE.getName();
        }
        eventInfoService.saveEventOrEventChild(operatorDto,builder.toString(),eventKindId,
                operateTypeName, Objects.nonNull(taskDetail) ? String.valueOf(taskDetail.getF_task_dtl_id()) : null,buildObjName(taskDetail));
    }


    /**
     * 人员和签证
     * @param operatorType
     * @param operatorDto
     * @param taskDetail
     * @param oldPersonDtl
     * @param newPersonDtl
     */
    @Async
    public void servePersonEventInfo(String operatorType, OperatorDto operatorDto, TaskDetail taskDetail, PersonDtl oldPersonDtl, PersonDtl newPersonDtl) {
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        operatorDto.setF_org_num(taskDetail.getOrgNum());
        String operateTypeName = OperateTypeEnum.MODIFY.getName();
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getF_task_kind_code());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.EXHIBITOR_CUSTOMER_SERVICE_CENTER.getId();
        StringBuilder builder = new StringBuilder();
        if(EventOperateTypeEnum.Add.getCode().equals(operatorType) || EventOperateTypeEnum.Modify.getCode().equals(operatorType)){
            Integer f_person_dtl_id = newPersonDtl.getF_person_dtl_id();
            builder.append("工单:"+taskDetail.getTaskTopic()+"("+taskDetail.getF_task_dtl_id()+")").append("\r\n");
            builder.append("数据Id："+f_person_dtl_id).append("\r\n");
            String name = StringUtils.isNotBlank(newPersonDtl.getF_name()) ? newPersonDtl.getF_name() : "空";
            String mobile = StringUtils.isNotBlank(newPersonDtl.getF_mobile()) ? newPersonDtl.getF_mobile() : "空";
            String email = StringUtils.isNotBlank(newPersonDtl.getF_email()) ? newPersonDtl.getF_email() : "空";
            String idNumber = StringUtils.isNotBlank(newPersonDtl.getF_id_number()) ? newPersonDtl.getF_id_number() : "空";
            String passNumber = StringUtils.isNotBlank(newPersonDtl.getF_pass_number()) ? newPersonDtl.getF_pass_number() : "空";
            String sexName = StringUtils.isNotBlank(newPersonDtl.getF_sex_id()) ? PublicUtil.getSexByState(Integer.valueOf(newPersonDtl.getF_sex_id()),"") : "空";
            String isVisa = newPersonDtl.getF_is_visa()!=null ? newPersonDtl.getF_is_visa() ? "是" : "否" : "空";
            String isHeel = newPersonDtl.getF_is_heel()!=null ? newPersonDtl.getF_is_heel() ? "是" : "否" : "空";
            if(Objects.isNull(oldPersonDtl)){
                operateTypeName = OperateTypeEnum.ADD.getName();
                builder.append("姓名："+name).append("\r\n");
                builder.append("手机："+mobile).append("\r\n");
                builder.append("邮箱："+email).append("\r\n");
                builder.append("身份证号："+idNumber).append("\r\n");
                builder.append("护照号："+passNumber).append("\r\n");
                builder.append("性别："+sexName).append("\r\n");
                builder.append("是否签证："+isVisa).append("\r\n");
                builder.append("是否跟团："+isHeel).append("\r\n");
            }else{
                String oldName = StringUtils.isNotBlank(oldPersonDtl.getF_name()) ? oldPersonDtl.getF_name() : "空";
                String oldMobile = StringUtils.isNotBlank(oldPersonDtl.getF_mobile()) ? oldPersonDtl.getF_mobile() : "空";
                String oldEmail = StringUtils.isNotBlank(oldPersonDtl.getF_email()) ? oldPersonDtl.getF_email() : "空";
                String oldIdNumber = StringUtils.isNotBlank(oldPersonDtl.getF_id_number()) ? oldPersonDtl.getF_id_number() : "空";
                String oldPassNumber = StringUtils.isNotBlank(oldPersonDtl.getF_pass_number()) ? oldPersonDtl.getF_pass_number() : "空";
                String oldSexName = StringUtils.isNotBlank(oldPersonDtl.getF_sex_id()) ? PublicUtil.getSexByState(Integer.valueOf(oldPersonDtl.getF_sex_id()),"") : "空";
                String oldIsVisa = oldPersonDtl.getF_is_visa()!=null ? oldPersonDtl.getF_is_visa() ? "是" : "否" : "空";
                String oldIsHeel = oldPersonDtl.getF_is_heel()!=null ? oldPersonDtl.getF_is_heel() ? "是" : "否" : "空";
                if(!oldName.equals(name)) builder.append("姓名由'"+oldName).append("'变成'").append(name).append("'\r\n");
                if(!oldMobile.equals(mobile)) builder.append("手机由'"+oldMobile).append("'变成'").append(mobile).append("'\r\n");
                if(!oldEmail.equals(email)) builder.append("邮箱由'"+oldEmail).append("'变成'").append(email).append("'\r\n");
                if(!oldIdNumber.equals(idNumber)) builder.append("身份证号由'"+oldIdNumber).append("'变成'").append(idNumber).append("'\r\n");
                if(!oldPassNumber.equals(passNumber)) builder.append("护照号由'"+oldPassNumber).append("'变成'").append(passNumber).append("'\r\n");
                if(!oldSexName.equals(sexName)) builder.append("性别由'"+oldSexName).append("'变成'").append(sexName).append("'\r\n");
                if(!oldIsVisa.equals(isVisa)) builder.append("是否签证由'"+oldIsVisa).append("'变成'").append(isVisa).append("'\r\n");
                if(!oldIsHeel.equals(isHeel)) builder.append("是否跟团由'"+oldIsHeel).append("'变成'").append(isHeel).append("'\r\n");

            }
        }else if(EventOperateTypeEnum.Delete.getCode().equals(operatorType)){
            String memo = "删除人员和签证数据 "+oldPersonDtl.getF_name()+"("+oldPersonDtl.getF_person_dtl_id()+")"+" 信息成功";
            builder.append(memo).append("\r\n");
            operateTypeName = OperateTypeEnum.DELETE.getName();
        }
        eventInfoService.saveEventOrEventChild(operatorDto,String.valueOf(builder),eventKindId,
                operateTypeName , Objects.nonNull(taskDetail) ? String.valueOf(taskDetail.getF_task_dtl_id()) : null,buildObjName(taskDetail));
    }
}
