package com.eip.async;

import com.eip.common.enums.ExhibitorTaskTypeEnum;
import com.eip.common.exception.enums.BusinessExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.entity.PersonDtl;
import com.eip.facade.vip.entity.ServeCert;
import com.eip.facade.vip.entity.ServeInvite;
import com.eip.facade.vip.entity.ServeVisa;
import com.eip.facade.vip.vo.HistoryPersonInfoVo;
import com.eip.service.vip.orgdao.PersonDtlMapper;
import com.eip.service.vip.orgdao.ServeCertMapper;
import com.eip.service.vip.orgdao.ServeInviteMapper;
import com.eip.service.vip.orgdao.ServeVisaMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2025-06-09 13:31
 * @DESCRIPTION: 展商人员校验
 */
@Slf4j
@Component
public class ExhibitorPersonInfoAsyncService {

    @Autowired
    private PersonDtlMapper personDtlMapper;
    @Autowired
    private ServeVisaMapper serve_visaMapper;
    @Autowired
    private ServeInviteMapper serve_inviteMapper;
    @Autowired
    private ServeCertMapper serve_certMapper;


    /**
     * 检查是否存在重复数据
     * @param objBean
     * @return
     */
    public AjaxResponse checkExistRepeatCert(String operatorSource,Object objBean, String taskKindCode) {
        if(StringUtils.isBlank(operatorSource) || Objects.isNull(objBean) || StringUtils.isBlank(taskKindCode)) return AjaxResponse.success();
        Integer projectId = null;
        String mobile = null;
        String idNumber = null;
        String passNumber = null;
        Integer notEqCurrentId = null;
        boolean normalState = true;
        if("refPersonInfo".equals(operatorSource)){//调用人员信息
            HistoryPersonInfoVo historyPersonInfoVo = (HistoryPersonInfoVo)objBean;
            projectId = historyPersonInfoVo.getProjectId();
            mobile = StringUtils.trim(historyPersonInfoVo.getMobile());
            idNumber = StringUtils.trim(historyPersonInfoVo.getIdNumber());
            passNumber = StringUtils.trim(historyPersonInfoVo.getPassNumber());
        }else if(ExhibitorTaskTypeEnum.CERT.getTaskCode().equals(operatorSource)){
            ServeCert serve_cert = (ServeCert)objBean;
            projectId = serve_cert.getF_project_id();
            mobile = StringUtils.trim(serve_cert.getMobilePhoneNum());
            idNumber = StringUtils.trim(serve_cert.getF_id_card());
            passNumber = StringUtils.trim(serve_cert.getF_pass_number());
            notEqCurrentId = serve_cert.getF_serve_cert_id();
            normalState = false;
        }else if(ExhibitorTaskTypeEnum.PERSON.getTaskCode().equals(operatorSource)){
            PersonDtl personDtl = (PersonDtl)objBean;
            projectId = personDtl.getF_project_id();
            mobile = StringUtils.trim(personDtl.getF_mobile());
            idNumber = StringUtils.trim(personDtl.getF_id_number());
            passNumber = StringUtils.trim(personDtl.getF_pass_number());
            notEqCurrentId = personDtl.getF_person_dtl_id();
            normalState = false;
        }else if(ExhibitorTaskTypeEnum.INVITE.getTaskCode().equals(operatorSource)){
            ServeInvite serve_invite = (ServeInvite)objBean;
            projectId = serve_invite.getF_project_id();
            mobile = StringUtils.trim(serve_invite.getF_mobile());
            passNumber = StringUtils.trim(serve_invite.getF_passport_num());
            notEqCurrentId = serve_invite.getF_serve_invite_id();
            normalState = false;
        }else if(ExhibitorTaskTypeEnum.VISA.getTaskCode().equals(operatorSource)){
            ServeVisa serve_visa = (ServeVisa)objBean;
            projectId = serve_visa.getF_project_id();
            mobile = StringUtils.trim(serve_visa.getF_mobile());
            idNumber = StringUtils.trim(serve_visa.getF_id_number());
            passNumber = StringUtils.trim(serve_visa.getF_pass_number());
            notEqCurrentId = serve_visa.getF_serve_visa_id();
            normalState = false;
        }
        if (projectId == null) return AjaxResponse.success();
        int mobileRepeatState = normalState ? -1 : BusinessExceptionEnum.DUPLICATE_MOBILE.getCode();
        int DuplicateIDNumberRepeatState = normalState ? -2 : BusinessExceptionEnum.DUPLICATE_ID_NUMBER.getCode();
        Map<String, Object> map = Maps.newHashMap();
        if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
            //判断手机号是否重复
            if(StringUtils.isNotBlank(mobile)){
                map.put("projectId", projectId);
                map.put("eqMobilePhoneNum", mobile);
                if(notEqCurrentId!=null) map.put("notCertId",notEqCurrentId);
                int count = serve_certMapper.countByMap(map);
                if (count > 0) return AjaxResponse.create(mobileRepeatState, "手机号重复");
            }
            //判断证件号码是否重复
            if(StringUtils.isNotBlank(idNumber) || StringUtils.isNotBlank(passNumber)){
                map.clear();
                map.put("projectId", projectId);
                if(notEqCurrentId!=null) map.put("notCertId",notEqCurrentId);
                if (StringUtils.isNotBlank(idNumber)) {
                    map.put("eqIdCard", idNumber);
                    int count = serve_certMapper.countByMap(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
                if (StringUtils.isNotBlank(passNumber)) {
                    map.put("eqPassNumber", passNumber);
                    int count = serve_certMapper.countByMap(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
            }
        }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
            //判断手机号是否重复
            if(StringUtils.isNotBlank(mobile)) {
                map.put("projectId", projectId);
                map.put("personMobile", mobile);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                int count = personDtlMapper.countPersonDtl(map);
                if (count > 0) return AjaxResponse.create(mobileRepeatState, "手机号重复");
            }
            //判断证件号码是否重复
            if(StringUtils.isNotBlank(idNumber) || StringUtils.isNotBlank(passNumber)){
                map.clear();
                map.put("projectId", projectId);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                if(StringUtils.isNotBlank(idNumber)){
                    map.put("eqIdNumber", idNumber);
                    int count = personDtlMapper.countPersonDtl(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
                if(StringUtils.isNotBlank(passNumber)){
                    map.put("eqPassNumber",passNumber);
                    int count = personDtlMapper.countPersonDtl(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
            }
        }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
            //判断手机号是否重复
            if(StringUtils.isNotBlank(mobile)) {
                map.put("pid", projectId);
                map.put("mobile", mobile);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                int count = serve_inviteMapper.count(map);
                if (count > 0) return AjaxResponse.create(mobileRepeatState, "手机号重复");
            }
            //判断证件号码是否重复
            if(StringUtils.isNotBlank(passNumber)){
                map.clear();
                map.put("projectId", projectId);
                map.put("eqPassNumber",passNumber);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                int count = serve_inviteMapper.count(map);
                if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
            }
        }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
            //判断手机号是否重复
            if(StringUtils.isNotBlank(mobile)) {
                map.put("projectId", projectId);
                map.put("mobile", mobile);
                map.put("f_visa_flag", 2);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                int count = serve_visaMapper.count(map);
                if (count > 0) return AjaxResponse.create(mobileRepeatState, "手机号重复");
            }
            //判断证件号码是否重复
            if(StringUtils.isNotBlank(idNumber) || StringUtils.isNotBlank(passNumber)){
                map.clear();
                map.put("f_visa_flag",2);
                map.put("projectId", projectId);
                if(notEqCurrentId!=null) map.put("notEqCurrentId",notEqCurrentId);
                if(StringUtils.isNotBlank(idNumber)){
                    map.put("eqIdNumber", idNumber);
                    int count = serve_visaMapper.count(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
                if(StringUtils.isNotBlank(passNumber)){
                    map.put("eqPassNumber",passNumber);
                    int count = serve_visaMapper.count(map);
                    if (count > 0) return AjaxResponse.create(DuplicateIDNumberRepeatState, "证件号码重复");
                }
            }
        }
        return AjaxResponse.success();
    }

}
