package com.eip.service.vip.biz;

import com.eip.facade.vip.dto.MenuDto;
import com.eip.facade.vip.entity.Menu;
import com.eip.facade.vip.vo.ExhibitorCenterMenuVo;
import com.eip.facade.vip.vo.MenuVo;
import com.eip.facade.vip.vo.TraderTemplateRefVo;

import java.util.List;

/**
 * @USER: zwd
 * @DATE: 2025-04-01 18:52
 * @DESCRIPTION:
 */
public interface MenuBiz {

    List<MenuVo> getPersonalCenterMenus(MenuDto menuDto);

    List<MenuVo> getEnterpriseCenterMenus(MenuDto menuDto);

    List<Menu> getMenu(MenuDto menuDto);

    List<ExhibitorCenterMenuVo> getExhibitorCenterMenu(MenuDto menuDto);

    ExhibitorCenterMenuVo getExhibitorCenterContent(MenuDto menuDto);

    List<TraderTemplateRefVo> getExhibitorCenterWorkBenchContent(MenuDto menuDto);

}
