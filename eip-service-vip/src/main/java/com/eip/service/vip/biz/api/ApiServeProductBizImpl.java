package com.eip.service.vip.biz.api;

import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.eip.async.ServeCompanyAsyncService;
import com.eip.common.biz.BaseBiz;
import com.eip.common.constant.PageConstant;
import com.eip.common.entity.PageParam;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.entity.CustomizedField;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.entity.api.ApiServeCatalogue;
import com.eip.facade.vip.entity.api.ApiServeProduct;
import com.eip.facade.vip.entity.api.ApiServeProductPic;
import com.eip.facade.vip.vo.FavoriteProductVo;
import com.eip.facade.vip.vo.api.brandWebSiteVo.BrandExhibitorProductVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeProductDtlVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeProductVo;
import com.eip.service.vip.orgdao.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


@Component("apiServeProductBiz")
public class ApiServeProductBizImpl extends BaseBiz implements ApiServeProductBiz {

    @Autowired
    private ApiServeProductDao serveProductDao;
    @Autowired
    private ApiServeProductPicDao serveProductPicDao;
    @Autowired
    private ApiServeCatalogueDao serveCatalogueDao;
    @Autowired
    private ApiTradeClassBiz tradeClassBiz;
    @Autowired
    private ServeCompanyAsyncService serveCompanyAsyncService;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private CustomizedFieldMapper customizedFieldMapper;


    @Override
    public PageInfo<ApiServeProductVo> getServeProductPage(PageParam pageParam, ApiServeProduct serveProduct){
        Map<String, Object> paramMap = new HashMap<>();
        Integer fisRecommend = serveProduct.getIsRecommend();
        Integer brandProjectId = serveProduct.getBrandProjectId();
        if(brandProjectId!=null && brandProjectId > 0){
            paramMap.put("brandProjectId",brandProjectId);
            paramMap.put("queryProjectId",serveProduct.getProjectId());
            if(serveProduct.getFIsRecommendFlag()!=null){
                if(BooleanUtils.isTrue(serveProduct.getFIsRecommendFlag())){
                    fisRecommend = 1;
                }else{
                    fisRecommend = 0;
                }
            }
        }else{
            paramMap.put("confirm",4);
            paramMap.put("exhibitCode",serveProduct.getExhibitCode());
        }
        paramMap.put("fisRecommend", fisRecommend);
        paramMap.put("sectionCode", serveProduct.getSectionCode());
        paramMap.put("productTypeBase", serveProduct.getProductTypeBase());
        paramMap.put("productTypeBaseEn", serveProduct.getProductTypeBaseEn());
        if (serveProduct.getTradeId() != null) {
            List<Integer> tradeIds = tradeClassBiz.getAllTradeIdsTree(serveProduct.getTradeId());
            tradeIds.add(serveProduct.getTradeId());
            paramMap.put("tradeIds", tradeIds);
        }
        if(serveProduct.getTradeIdEn()!=null){
            List<Integer> tradeIdEns = tradeClassBiz.getAllTradeIdsTree(serveProduct.getTradeIdEn());
            tradeIdEns.add(serveProduct.getTradeIdEn());
            paramMap.put("tradeIdEns",tradeIdEns);
        }
        //关键词查询
        String languageType = serveProduct.getLanguageType();
        paramMap.put("languageType",StringUtils.isNotBlank(languageType) ? languageType : "CN");

        String keyWord = serveProduct.getKeyWord();
        if(StringUtils.isNotBlank(keyWord)){
            boolean matchChineseFlag = ReUtil.isMatch(PatternPool.CHINESES, keyWord);
            if(matchChineseFlag){//是否是全中文 true
                paramMap.put("keyWord",keyWord);
            }else{
                paramMap.put("keyWordIgnoreCase",keyWord);
            }
        }
        List<ApiServeProductVo> apiServeProductList = Lists.newArrayList();
        //超过这个则直接只返回这么多数据
        int count = serveProductDao.countServeProductList(paramMap);
        if(count > 0){
            pageParam.initLimitPageParam(count);
            if(count >= PageConstant.requestTotalNum) count = PageConstant.requestTotalNum;
            int rows = pageParam.getRows();
            int offset = pageParam.getOffset();
            paramMap.put("rows",rows);
            paramMap.put("offset",offset);
            apiServeProductList = serveProductDao.getServeProductList(paramMap);
            try {
                Future<Boolean> booleanFuture = serveCompanyAsyncService.queryServeProductOtherInfo(apiServeProductList,true);
                booleanFuture.get();
            }catch (Exception e){
                logger.error("查询展品列表异常:{}",e);
            }
        }
        PageInfo<ApiServeProductVo> pageInfo = new PageInfo<>();
        pageInfo.setTotal(count);
        pageInfo.setList(apiServeProductList);
        return pageInfo;
    }

    @Override
    public ApiServeProductDtlVo getServeProductDtl(ApiServeProduct apiServeProduct) {
        ApiServeProductDtlVo serveProductDtl = serveProductDao.getServeProductDtl(apiServeProduct.getServeProductId());
        if (Objects.nonNull(serveProductDtl)) {
            //展位号去重
            if(StringUtils.isNotBlank(serveProductDtl.getBoothNum())){
                String newBoothNums = Lists.newArrayList(StringUtils.split(serveProductDtl.getBoothNum(), ','))
                        .stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(","));
                serveProductDtl.setBoothNum(newBoothNums);
            }
            //展馆去重
            if(StringUtils.isNotBlank(serveProductDtl.getSectionName())){
                String newSectionNames = Lists.newArrayList(StringUtils.split(serveProductDtl.getSectionName(), ','))
                        .stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(","));
                serveProductDtl.setSectionName(newSectionNames);
            }
            Map<String,Object> parammap = Maps.newHashMapWithExpectedSize(10);
            //自定义字段
            String customFieldJson = serveProductDtl.getCustomFieldJson();
            if(StringUtils.isNotBlank(customFieldJson)){
                Map<String, String> customJsonMap = JSON.parseObject(customFieldJson, new TypeReference<Map<String, String>>() {});
                Map<String, String> finalCustomJsonMap = customJsonMap.entrySet()
                        .stream()
                        .filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                if(MapUtils.isNotEmpty(finalCustomJsonMap)){
                    Set<String> keys = customJsonMap.keySet();
                    parammap.put("tableName","t_serve_catalogue");
                    parammap.put("formName","product");
                    parammap.put("fieldNameKeys",keys);
                    List<CustomizedField> customizedFieldList = customizedFieldMapper.getByMap(parammap);
                    if(CollectionUtils.isNotEmpty(customizedFieldList)){
                        Map<String, Object> finalCustomFieldMap = Maps.newHashMapWithExpectedSize(customizedFieldList.size());
                        customizedFieldList.stream().forEach(customizedField -> {
                            if(finalCustomJsonMap.containsKey(customizedField.getF_field_name())){
                                finalCustomFieldMap.put(customizedField.getF_field_label(),finalCustomJsonMap.get(customizedField.getF_field_name()));
                            }
                        });
                        if(MapUtils.isNotEmpty(finalCustomFieldMap)){
                            serveProductDtl.setCustomFieldJson(JSON.toJSONString(finalCustomFieldMap));
                        }
                    }
                }else{
                    serveProductDtl.setCustomFieldJson(null);
                }
            }
            List<ApiServeProductPic> serveProductPicList = serveProductPicDao.selectProductPicByServeProductId(serveProductDtl.getServeProductId());
            if(CollectionUtils.isNotEmpty(serveProductPicList)){
                //产品图片实体集合
                serveProductDtl.setServeProductPiclist(serveProductPicList);
                //产品图片集合
                List<String> productPics = serveProductPicList.stream()
                        .filter(Objects::nonNull)
                        .filter(apiServeProductPic -> StringUtils.isNotBlank(apiServeProductPic.getPic()))
                        .map(ApiServeProductPic::getPic)
                        .collect(Collectors.toList());
                serveProductDtl.setProductPics(productPics);
            }

            //该公司下的其它展品
            if(BooleanUtils.isTrue(apiServeProduct.getShowOtherProducts())){
                parammap.clear();
                parammap.put("notEqCurrentProductId",serveProductDtl.getServeProductId());
                parammap.put("taskDtlId",serveProductDtl.getTaskDtlId());
                List<BrandExhibitorProductVo> otherProductList = serveProductDao.selectExhibitorOtherProduct(parammap);
                serveProductDtl.setOtherProducts(otherProductList);
            }
            //查询当前展商同品牌不同项目下是否存在最新会刊
            Boolean queryBrandNewCata = apiServeProduct.getQueryBrandNewCata();
            Integer contractProjectId = serveProductDtl.getProjectId();
            Integer clientId = serveProductDtl.getClientId();
            if(BooleanUtils.isTrue(queryBrandNewCata) && contractProjectId!=null && clientId!=null){
                Project byId = projectDao.getById(contractProjectId);
                if(Objects.nonNull(byId) && byId.getOwnedBrandId()!=null){
                    parammap.clear();
                    parammap.put("brandProjectId",byId.getOwnedBrandId());
                    parammap.put("clientId",clientId);
                    parammap.put("exhibitionProjectId",contractProjectId);//参展项目Id
                    Integer newCataId = serveCatalogueDao.selectNewestServeCatalogue(parammap);
                    serveProductDtl.setNewCataId(newCataId);
                }
            }
        }
        return serveProductDtl;
    }


    @Override
    public void updateReadNumUpOne(Integer serveProductId) {
        serveProductDao.updateReadNumUpOne(serveProductId);
    }


    @Override
    public List<ApiServeProductPic> selectPicByProductId(Integer serveProductId) {
        Map<String, Object> map = new HashMap<>();
        map.put("serveProductId", serveProductId);
        return serveProductPicDao.selectByMap(map);
    }

    @Override
    public PageInfo<ApiServeProduct> selectBykeyword(PageParam pageParam, ApiServeProduct serveProduct,String languageType) throws ParseException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("projectId", serveProduct.getProjectId());
        paramMap.put("languageType",languageType);
        String keyWord = serveProduct.getKeyWord();
        if(StringUtils.isNotBlank(keyWord)){
            boolean matchChineseFlag = ReUtil.isMatch(PatternPool.CHINESES, keyWord);
            if(matchChineseFlag){//是否是全中文 true
                paramMap.put("keyWord",keyWord);
            }else{
                paramMap.put("keyWordIgnoreCase",keyWord);
            }
        }
        paramMap.put("confirm",4);
        PageHelper.startPage(pageParam.getPage(),pageParam.getRows());
        return new PageInfo<>(serveProductDao.selectBykeyword(paramMap));
    }

    @Override
    public int countServeProductByCataId(Integer serveCatalogueId) {
        Integer cataRefTaskId = getCataRefTaskId(serveCatalogueId);
        if(cataRefTaskId == null) return 0;
        return serveProductDao.countServeProductByTaskDtlId(cataRefTaskId);
    }

    @Override
    public List<ApiServeProductVo> getServeProductByCataId(PageParam param, Integer serveCatalogueId) {
        Integer cataRefTaskId = getCataRefTaskId(serveCatalogueId);
        if(cataRefTaskId == null) return Collections.emptyList();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(8);
        paramMap.put("taskDtlId", cataRefTaskId);
        paramMap.put("offset", param.getOffset());
        paramMap.put("rows", param.getRows());
        List<ApiServeProductVo> apiServeProducts = serveProductDao.getServeProductList(paramMap);
        if(CollectionUtils.isNotEmpty(apiServeProducts)){
            try {
                Future<Boolean> booleanFuture = serveCompanyAsyncService.queryServeProductOtherInfo(apiServeProducts,false);
                booleanFuture.get();
            }catch (Exception e){
                logger.error("查询展品列表异常:{}",e);
            }
        }
        return apiServeProducts;
    }

    /**
     * 公共查询
     * @param serveCatalogueId
     * @return
     */
    private Integer getCataRefTaskId(Integer serveCatalogueId){
        if(serveCatalogueId == null) return null;
        ApiServeCatalogue serveCatalogue = serveCatalogueDao.selectByServeCatalogueId(serveCatalogueId);
        if(Objects.isNull(serveCatalogue)) return null;
        return serveCatalogue.getTaskId();
    }

    @Override
    public PageInfo<FavoriteProductVo> selectMyCollectProduct(PageParam pageParam, ProjectDto projectDto) {
        List<FavoriteProductVo> favoriteProductVos = Lists.newArrayList();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("confirm",4);
        paramMap.put("exhibitCode",projectDto.getExhibitCode());
        paramMap.put("productTypeId",projectDto.getProductTypeId());
        paramMap.put("sectionCode",projectDto.getSectionCode());
        paramMap.put("zzyUserId",projectDto.getZzyUserId());
        paramMap.put("keyWord",projectDto.getKeyword());
        String languageType = StringUtils.isNotBlank(projectDto.getLanguageType()) ? projectDto.getLanguageType() : "CN";
        paramMap.put("languageType",languageType);
        int count = serveProductDao.countMyCollectProduct(paramMap);
        if(count > 0){
            paramMap.put("rows", pageParam.getRows());
            paramMap.put("offset", pageParam.getOffset());
            favoriteProductVos = serveProductDao.selectMyCollectProduct(paramMap);
            if(CollectionUtils.isNotEmpty(favoriteProductVos)) {
                favoriteProductVos.stream().filter(Objects::nonNull).forEach(apiServeProductVo -> {
                    // 查询产品图片
                    List<ApiServeProductPic> serveProductPicList = serveProductPicDao.selectProductPicByServeProductId(apiServeProductVo.getServeProductId());
                    if (CollectionUtils.isNotEmpty(serveProductPicList)) {
                        //产品图片集合
                        List<String> productPics = serveProductPicList.stream()
                                .filter(Objects::nonNull)
                                .filter(apiServeProductPic -> StringUtils.isNotBlank(apiServeProductPic.getPic()))
                                .map(ApiServeProductPic::getPic)
                                .collect(Collectors.toList());
                        //图片集合 逗号分割
                        apiServeProductVo.setPic(StringUtils.join(productPics, ","));
                    }
                });
            }
        }
        PageInfo<FavoriteProductVo> pageInfo = new PageInfo<>();
        pageInfo.setTotal(count);
        pageInfo.setList(favoriteProductVos);
        return pageInfo;
    }
}
