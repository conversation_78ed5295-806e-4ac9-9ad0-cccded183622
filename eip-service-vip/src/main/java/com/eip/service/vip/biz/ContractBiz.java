package com.eip.service.vip.biz;

import com.eip.common.entity.PageParam;
import com.eip.facade.vip.dto.ContractDto;
import com.eip.facade.vip.entity.Contract;
import com.github.pagehelper.PageInfo;

public interface ContractBiz {
	
	String SelectByclientID(PageParam param, Integer clientID);
	
	int  countselBycid(PageParam param, Integer clientID);
	
	String loadpage(Integer clientID);
	
	 Contract LoadByPidCid(Integer Pid,Integer Cid);

	PageInfo<Contract> selectExhibitorContract(PageParam pageParam,ContractDto contractDto);

    int updateContractFile(ContractDto contractDto);

	int resetContractFile(ContractDto contractDto);

}
