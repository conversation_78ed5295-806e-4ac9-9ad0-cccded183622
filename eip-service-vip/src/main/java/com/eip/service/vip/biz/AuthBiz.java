package com.eip.service.vip.biz;

import com.eip.common.exception.BusinessException;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.AuthLoginReqDto;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.dto.SocialUserBindDto;
import com.eip.facade.vip.dto.api.ApiMemberLoginTokenDto;
import com.eip.facade.vip.entity.ApiUser;
import com.eip.facade.vip.entity.BuyerReg;
import com.eip.facade.vip.entity.ZzyUser;
import com.eip.facade.vip.vo.AuthLoginRespVo;

import java.util.List;
import java.util.Map;

/**
 * @USER: zwd
 * @DATE: 2023-06-20 10:51
 * @DESCRIPTION:
 */
public interface AuthBiz {

    AjaxResponse accountLogin(AuthLoginReqDto authLoginReqDto);

    AjaxResponse captchaLogin(AuthLoginReqDto authLoginReqDto);

    AjaxResponse buildAccessTokenToRedis(AuthLoginReqDto authLoginReqDto, ZzyUser zzyUser, Map<String, Object> redisHashMap) throws BusinessException;

    void logout(String accessToken);

    AjaxResponse register(AuthLoginReqDto authLoginReqDto);

    AjaxResponse confirmRefZzyUser(AuthLoginReqDto authLoginReqDto);

    AjaxResponse socialLogin(SocialUserBindDto socialUserBindDto) throws BusinessException;

    AjaxResponse confirmBindZzyUser(SocialUserBindDto socialUserBindDto);

    ZzyUser checkZzyUserIfCerfified(ZzyUser zUser, String username);

    AjaxResponse updateBuyerRegBindZzyUser(AuthLoginReqDto authLoginReqDto);

    List<BuyerReg> selectWxUserBuyerInfo(Map<String, Object> paramMap);

    AjaxResponse updateCertifiedMember(AuthLoginReqDto authLoginReqDto);

    AjaxResponse getMemberLoginUserInfo(AuthLoginRespVo userInfo,ProjectDto projectDto);

    AjaxResponse getMemberAuthToken(ApiMemberLoginTokenDto apiMemberLoginTokenDto);

    void addApiLog(Object objBean, AjaxResponse resp, ApiUser apiUser, String method,Integer zzyUserId);
}
