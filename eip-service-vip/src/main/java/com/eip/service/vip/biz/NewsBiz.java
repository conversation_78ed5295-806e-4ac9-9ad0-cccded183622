package com.eip.service.vip.biz;

import com.eip.common.entity.PageParam;
import com.eip.facade.vip.dto.NewDto;
import com.eip.facade.vip.entity.News;
import com.eip.facade.vip.vo.api.brandWebSiteVo.BrandNewsVo;
import com.eip.facade.vip.vo.api.brandWebSiteVo.BrandSearchVo;
import com.eip.facade.vip.vo.api.brandWebSiteVo.SearchLabelVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface NewsBiz {

    /**
     * 查询新闻列表
     * @param news
     * @param pageParam
     * @return
     */
    PageInfo<News> selectNewsList(News news, PageParam pageParam);

    PageInfo<News> getPage( PageParam pageParam,NewDto newDto);

    News getById(NewDto newDto);

    BrandNewsVo getSingleNews(NewDto newDto);

    List<BrandNewsVo> getBrandNewsList(NewDto newDto);

    PageInfo<BrandNewsVo> getBrandNewPage(PageParam pageParam, NewDto newDto);

    Map<String, Object> getBrandNewLabels(NewDto newDto);

    List<SearchLabelVo> getSearchLabels(Map<String, Object> paramMap);

    int selectCount(Map<String, Object> paramMap);

    PageInfo<BrandSearchVo> search(PageParam pageParam, NewDto newDto);

    List<Map<String, Object>> getNewsTypeOrGroup(NewDto newDto);

}
