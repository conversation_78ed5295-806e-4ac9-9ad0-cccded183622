package com.eip.service.vip.biz.api;


import com.eip.service.vip.orgdao.ApiTradeClassDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("apiTradeClassBiz")
public class ApiTradeClassBizImpl implements ApiTradeClassBiz {
	
	@Autowired
	private ApiTradeClassDao dao;

	@Override
	public List<Integer> getAllTradeIdsTree(Integer parentId) {
		List<Integer> tree = new ArrayList<>();
		List<Integer> parentIds = new ArrayList<>();
		parentIds.add(parentId);
		tree = getChildTradeIds(tree,parentIds);
		return tree;
	}

	private List<Integer> getChildTradeIds(List<Integer> tree, List<Integer> parentIds) {
		// 获取传入所有父id的所有一级子id
		List<Integer> tradeIds = dao.selectByParentId(parentIds);
		// 递归获取所有的子id
		if (CollectionUtils.isNotEmpty(tradeIds)) {
			for (Integer tradeId : tradeIds) {
				tree.add(tradeId);
			}
			getChildTradeIds(tree, tradeIds);
		}
		return tree;
	}

}
