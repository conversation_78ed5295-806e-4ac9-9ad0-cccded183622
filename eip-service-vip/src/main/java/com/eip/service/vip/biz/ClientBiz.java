package com.eip.service.vip.biz;

import com.eip.facade.vip.entity.Client;

import java.util.List;
import java.util.Map;

public interface ClientBiz {
	
	 Client select(Integer cid);
	 
	int selectProjCltId(int projectId, int clientId);

	int selectOrgNum(Integer cid);

	List<Client> selectByMap(Map<String, Object> map);

	/**
	 * 查找联系人
	 * @param client
	 * @return
	 */
	Client selectLinkman(Client client);

	/**
	 * 根据联系人查找
	 * @param client
	 * @return
	 */
	Client selectByLinkman(Client client);

	/**
	 * 新增一个客户
	 * @param client
	 * @param projectId
	 * @param orgNum
	 * @return
	 */
	void insertNewClient(Client client,Integer projectId,Integer orgNum,Integer zzyuserId);

	Client getClientById(Map<String, Object> hashMap);

	int updateClientInfo(Client client);

	int updateZzyUserId(Map<String, Object> hashMap);

}
