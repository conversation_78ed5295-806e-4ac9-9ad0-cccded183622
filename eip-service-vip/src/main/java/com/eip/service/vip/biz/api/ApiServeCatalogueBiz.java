package com.eip.service.vip.biz.api;

import com.eip.common.entity.PageParam;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.dto.api.ApiServeCatalogueDto;
import com.eip.facade.vip.vo.FavoriteCompanyVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeCataDtlVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeCatalogueVo;
import com.github.pagehelper.PageInfo;

import java.text.ParseException;
import java.util.List;

public interface ApiServeCatalogueBiz {
	

	/**
	 * 根据行业类别或产品类别获取展商列表（关联会刊的信息）api
	 * @param pageParam
	 * @param apiServeCatalogueDto
	 * @return
	 */
	PageInfo<ApiServeCatalogueVo> selectByTypeFocusCata(PageParam pageParam, ApiServeCatalogueDto apiServeCatalogueDto) throws ParseException;


	/**
	 * 查询单个展商（关联会刊的信息）
	 * @param apiServeCatalogueDto
	 * @return
	 */
	ApiServeCataDtlVo getServeCataDtlById(ApiServeCatalogueDto apiServeCatalogueDto);

	/**
	 * 获取首页的热门推荐展品展商
	 * @param pid
	 * @param page
	 * @param rows
	 * @return
	 */
	PageInfo<ApiServeCatalogueDto> getHotComAndProduct(Integer pid, Integer page, Integer rows);

	/**
	 * 根据关键词搜索商户
	 * @param pageParam
	 * @param serveCatalogueDto
	 * @param languageType CN中文版 EN英文版
	 * @return
	 */
	List<ApiServeCatalogueDto> selectByKeyword(PageParam pageParam, ApiServeCatalogueDto serveCatalogueDto, String languageType) throws ParseException;


	PageInfo<ApiServeCatalogueDto> selectMorePageFocusCata(PageParam pageParam, ApiServeCatalogueDto apiServeCatalogueDto);

    PageInfo<FavoriteCompanyVo> selectMyCollectCompany(PageParam pageParam, ProjectDto projectDto);
}
