package com.eip.service.vip.biz;

import com.eip.common.entity.PageParam;
import com.eip.facade.vip.dto.CatalogueDto;
import com.eip.facade.vip.entity.Catalogue;
import com.eip.facade.vip.vo.MerchantVo;

import java.util.List;
import java.util.Map;

public interface CatalogueBiz {
	
	int insert(Catalogue catalogue);
	
	int update(Catalogue catalogue);
	
	Catalogue get(CatalogueDto catalogueDto);
	
	String FindReason(Integer cid);

	/**
	 * 根据项目ID，客户ID，任务ID获取唯一会刊
	 * @param catalogue
	 * @return
	 */
	Catalogue selectOne(Catalogue catalogue);

	List<Catalogue> getBySupplyAndDemandMatching(String keyWord, Integer projectId);

	int countMerchant(CatalogueDto catalogueDto);

	List<MerchantVo> getMerchantPage(PageParam pageParam, CatalogueDto catalogueDto);

	MerchantVo queryCataDetail(Integer cataId);

    int insertServeBoothToCataAndCompany(Map<String, Object> parammap);

    Catalogue getServeCataById(Integer cataId);

}
