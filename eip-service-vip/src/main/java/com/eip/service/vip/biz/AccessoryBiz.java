package com.eip.service.vip.biz;


import com.eip.facade.vip.entity.Accessory;
import com.eip.facade.vip.entity.Documents;

import java.util.List;

public interface AccessoryBiz {

	int insert(Accessory accessory);

	int deleteBatch(List<Accessory> list);

	List<Accessory> getList(String tableName, String recordCode);

    List<Documents> getDocumentsByfOrgNum(Integer fOrgNum, String recordCode, String tableName);
}
