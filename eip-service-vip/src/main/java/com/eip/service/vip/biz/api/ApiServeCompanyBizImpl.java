package com.eip.service.vip.biz.api;

import com.eip.service.vip.orgdao.ApiServeCompanyDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component("apiServeCompanyBiz")
public class ApiServeCompanyBizImpl implements ApiServeCompanyBiz {
	
	@Autowired
	private ApiServeCompanyDao dao;

	@Override
	public void updateReadNumUpOne(Integer serveProductId) {
		dao.updateReadNumUpOne(serveProductId);
	}


}
