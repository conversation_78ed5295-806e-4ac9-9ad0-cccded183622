package com.eip.service.vip.biz;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.eip.common.biz.BaseBiz;
import com.eip.facade.vip.entity.Graph;
import com.eip.service.vip.orgdao.GraphDao;

@Component("graphBiz")
public class GraphBizImpl extends BaseBiz implements GraphBiz {

	@Autowired
	private GraphDao graphDao;
	
	@Override
	public List<Graph> selectBySectionMapId(int sectionMapId) {
		return graphDao.selectBySectionMapId(sectionMapId);
	}

	@Override
	public int insert(List<Graph> list) {
		try{
			graphDao.insert(list);
			return 1;
		}catch(Exception e){
			logger.error(e.toString(), e);
			return 0;
		}
	}

	@Override
	public int update(Graph graph) {
		try{
			graphDao.update(graph);
			return 1;
		}catch(Exception e){
			logger.error(e.toString(), e);
			return 0;
		}
	}

	@Override
	public int deleteByGraphId(int graphId) {
		try{
			graphDao.deleteByGraphId(graphId);
			return 1;
		}catch(Exception e){
			logger.error(e.toString(), e);
			return 0;
		}
	}

}
