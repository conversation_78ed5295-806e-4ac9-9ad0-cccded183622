package com.eip.service.vip.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.lang.Validator;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.eip.common.biz.BaseBiz;
import com.eip.common.constant.CommonConstant;
import com.eip.common.constant.SignLoginConstant;
import com.eip.common.constant.SystemBusinessUrlConstant;
import com.eip.common.enums.*;
import com.eip.common.exception.BusinessException;
import com.eip.common.exception.enums.SystemAuthExceptionEnum;
import com.eip.common.util.RedisUtil;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.encryDecry.AESSecretUtil;
import com.eip.common.util.onlyId.OnlyNumberUtil;
import com.eip.common.util.onlyId.UuidUtil;
import com.eip.facade.vip.dto.AuthLoginReqDto;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.dto.SocialUserBindDto;
import com.eip.facade.vip.dto.api.ApiMemberLoginTokenDto;
import com.eip.facade.vip.entity.*;
import com.eip.facade.vip.service.*;
import com.eip.facade.vip.service.api.ApiLogService;
import com.eip.facade.vip.service.api.ApiUserService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.facade.vip.vo.UserCompanyVo;
import com.eip.facade.vip.vo.api.ApiMemberLoginTokenInfo;
import com.eip.service.vip.orgdao.*;
import com.eip.service.vip.zzysysdao.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @USER: zwd
 * @DATE: 2023-06-20 10:51
 * @DESCRIPTION:
 */
@Component("authBiz")
public class AuthBizImpl extends BaseBiz implements AuthBiz {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ZzyuserService zzyuserService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private CheckLogService checkLogService;
    @Autowired
    private EmailLogMapper emailLogMapper;
    @Autowired
    private ZzyuserFocusZzOrgMapper focusZzOrgMapper;
    @Autowired
    private NewIntegerIdBiz gBiz;
    @Autowired
    private ClientMapper clientMapper;
    @Autowired
    private UserFocusProjService userFocusProjService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private SysSettingOrgService sysSettingOrgService;
    @Autowired
    private BuyerRegMapper buyerRegMapper;
    @Autowired
    private UserCompanyMapper userCompanyMapper;
    @Autowired
    private SocialBindService socialBindService;
    @Autowired
    private ZzyUserSocialMapper zzyUserSocialMapper;
    @Autowired
    private NewIntegerIdService newIntegerIdService;
    @Autowired
    private TraderRegMapper traderRegMapper;
    @Autowired
    private ServeBoothDao serveBoothDao;
    @Autowired
    private TradeProjectDao tradeProjectDao;
    @Autowired
    private ApiUserService apiUserService;
    @Autowired
    private ApiLogService apiLogService;
    @Autowired
    private EnterpriseMapper enterpriseMapper;
    @Autowired
    private ZzyuserLoginLogMapper zzyuserLoginLogMapper;


    /**
     * 插入会员登录日志
     * @param authLoginReqDto
     */
    public void insertZzyUserLog(AuthLoginReqDto authLoginReqDto,ZzyuserLoginLog zzyuserLoginLog){
        if(Objects.isNull(authLoginReqDto)) return;
        Long loginId = zzyuserLoginLog.getLoginId();
        if(loginId == null){
            Long newLoginId = newIntegerIdService.snowNextId();
            zzyuserLoginLog.setLoginId(newLoginId);
        }
        zzyuserLoginLog.setLoginAccount(authLoginReqDto.getUsername());
        zzyuserLoginLog.setLoginTime(DateUtil.date());
        zzyuserLoginLog.setLoginType(authLoginReqDto.getLoginType());
        zzyuserLoginLog.setClientType(authLoginReqDto.getClientType());
        zzyuserLoginLog.setOrgNum(authLoginReqDto.getOrgNum());
        zzyuserLoginLog.setLoginIp(authLoginReqDto.getLoginIp());
        zzyuserLoginLog.setServerIp(authLoginReqDto.getLocalAddr());
        zzyuserLoginLog.setServerPort(authLoginReqDto.getServerPort()+"");
        zzyuserLoginLog.setInternalIp(authLoginReqDto.getInternalIpb());
        zzyuserLoginLog.setSysCode(authLoginReqDto.getSources());
        zzyuserLoginLog.setThirdPartyLogin(authLoginReqDto.getThirdLoginFlag());
        zzyuserLoginLogMapper.insertSelective(zzyuserLoginLog);
    }

    @Override
    public AjaxResponse accountLogin(AuthLoginReqDto authLoginReqDto){
        Map<String, Object> paramMap = Maps.newHashMap();
        //是否是第三方调取
        Boolean thirdLoginFlag = authLoginReqDto.getThirdLoginFlag();
        //密码强度
        Boolean passwordStrength = authLoginReqDto.getPasswordStrength();
        String userName = StringUtils.trim(authLoginReqDto.getUsername());
        paramMap.put("loginAccount",userName);
        List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
        if(CollectionUtils.isEmpty(zzyUserList)){
            paramMap.clear();
            if(Validator.isEmail(userName)){//邮箱
                paramMap.put("queryMemberEmail",StringUtils.lowerCase(userName));
            }else{
                paramMap.put("queryMemberMobile",userName);
            }
            paramMap.put("certifiedMobileOrEmail",true);//账号密码登录 只查询存在的已认证的账号
            zzyUserList = zzyuserService.selectZzyUser(paramMap);
            if(CollectionUtils.isEmpty(zzyUserList)){
                paramMap.put("certifiedMobileOrEmail", false);//未认证的
                zzyUserList = zzyuserService.selectZzyUser(paramMap);
                if (CollectionUtils.isEmpty(zzyUserList)) {//未认证都没有找到会员
                    String isRegister = checkIsRegister(authLoginReqDto.getOrgNum());
                    if(StringUtils.isNotBlank(isRegister) && BooleanUtils.toBoolean(isRegister)){//是否允许注册新会员
                        if(BooleanUtils.isTrue(thirdLoginFlag)){
                            return AjaxResponse.create(SystemAuthExceptionEnum.MOBILE_OR_EMAIL_NOT_REGISTER_WHETHER_REGISTER_NEW_USER.getCode(), SystemAuthExceptionEnum.MOBILE_OR_EMAIL_NOT_REGISTER_WHETHER_REGISTER_NEW_USER.getMessage());
                        }else{
                            return AjaxResponse.create(3, SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
                        }
                    }else{//不允许注册
                        String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，账号未注册会员";
                        ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                                .memo(loginMemo)
                                .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                                .loginState(0)
                                .build();
                        if(BooleanUtils.isTrue(thirdLoginFlag)){
                            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                            return AjaxResponse.create(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getCode(), SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
                        }else{
                            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                            return AjaxResponse.create(-3,"会员账号不存在");
                        }
                    }
                }else{
                    //密码强度中等以上
                    if(BooleanUtils.isFalse(thirdLoginFlag)){
                        if(!BooleanUtils.isTrue(passwordStrength))
                            return AjaxResponse.create(-10, SystemAuthExceptionEnum.ACCOUNT_SECURITY_IS_TOO_LOW.getMessage());
                    }
                }
            }
        }
        if(CollectionUtils.isEmpty(zzyUserList)){
            String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，账号未注册会员";
            ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                    .memo(loginMemo)
                    .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                    .loginState(0)
                    .build();
            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
            return AjaxResponse.failure(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
        }
        Optional<ZzyUser> zzyuserFirst = zzyUserList.stream().filter(Objects::nonNull)
                .filter(zzy_user -> StringUtils.isNotBlank(zzy_user.getF_user_password()) && authLoginReqDto.getPassword().equalsIgnoreCase(zzy_user.getF_user_password()))
                .findFirst();
        if(!zzyuserFirst.isPresent()){
            String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，密码错误";
            ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                    .memo(loginMemo)
                    .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                    .loginState(0)
                    .build();
            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
            String errMsg = SystemAuthExceptionEnum.PWD_ERROR.getMessage();
//            paramMap.clear();
//            paramMap.put("loginAccount",authLoginReqDto.getUsername());
//            paramMap.put("orgNum",authLoginReqDto.getOrgNum());
//            paramMap.put("loginState",0);
//            paramMap.put("currentDateLoginCount",true);
//            int existCount = zzyuserLoginLogMapper.count(paramMap);
//            if(CommonConstant.MAX_NUMBER_LOGIN_FAIL > existCount && existCount >= CommonConstant.LOGIN_ERROR_COUNT_THRESHOLD){
//                errMsg = "密码错误，您还剩" + (CommonConstant.MAX_NUMBER_LOGIN_FAIL - existCount) + "次重试的机会";
//            }else if(CommonConstant.MAX_NUMBER_LOGIN_FAIL <= existCount){
//                errMsg = "密码错误，今天登录错误次数过多，该帐号[" + authLoginReqDto.getUsername() + "]被禁止登录";
//            }
            return AjaxResponse.create(SystemAuthExceptionEnum.PWD_ERROR.getCode(),errMsg);
        }
        ZzyUser zzyUser = zzyuserFirst.get();
        zzyUser = selectZzyUserInfo(paramMap, zzyUser, authLoginReqDto);
        zzyUser.setLoginModeType(1);
        return AjaxResponse.success(zzyUser);
    }


    /**
     * 查询未认证的公司信息
     * @return
     */
    private List<Map<String,Object>> buildRefZzyUser(List<ZzyUser> zzyUserList,Integer orgNum){
        if(CollectionUtils.isEmpty(zzyUserList)) return Collections.emptyList();
        List<Map<String,Object>> mapList = Lists.newArrayList();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        for(ZzyUser zzyUser : zzyUserList){
            Map<String,Object> resultMap = Maps.newHashMap();
            resultMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
            resultMap.put("userName",zzyUser.getF_user_name());
            resultMap.put("mobile",zzyUser.getF_mobile());
            resultMap.put("email",zzyUser.getF_email());
            resultMap.put("nikeName",zzyUser.getF_name());
            resultMap.put("headImage",zzyUser.getHeadImage());
            resultMap.put("orgNum",orgNum);

            paramMap.put("zzyuserId",zzyUser.getF_zzyuser_id());
            UserCompanyVo userCompanyVo = userCompanyMapper.queryMemberCompany(paramMap);
            if(Objects.nonNull(userCompanyVo)){
                resultMap.put("companyName",userCompanyVo.getCompanyName());//公司名称
                resultMap.put("companyNameEn",userCompanyVo.getCompanyNameEn());//公司名称
                resultMap.put("contactsName",userCompanyVo.getContacts());//联系人名称
                resultMap.put("contactsNameEn",userCompanyVo.getContactsEn());//联系人名称
            }else{
                resultMap.put("companyName","");//公司名称
                resultMap.put("companyNameEn","");//公司名称
                resultMap.put("contactsName","");//联系人名称
                resultMap.put("contactsNameEn","");//联系人名称
            }
            mapList.add(resultMap);
        }
        return mapList;
    }

    private String checkIsRegister(Integer orgNum){
        SysSettingOrg build = SysSettingOrg.builder().orgNum(orgNum)
                .settingCode("allowMemerReg").build();
        return sysSettingOrgService.getSysSettingOrgValue(build);
    }

    private String queryUpdateCompanySet(Integer orgNum){
        SysSettingOrg build = SysSettingOrg.builder().orgNum(orgNum)
                .settingCode("allowModCompanyName").build();
        return sysSettingOrgService.getSysSettingOrgValue(build);
    }

    /**
     * 抽取公共方法
     * @param paramMap
     * @param zzyUser
     * @param authLoginReqDto
     * @return
     */
    private ZzyUser selectZzyUserInfo(Map<String, Object> paramMap,ZzyUser zzyUser,AuthLoginReqDto authLoginReqDto){
        //处理是否存在机构关联
        ZzyuserFocusZzorg zzyuserFocusZzorg = new ZzyuserFocusZzorg();
        zzyuserFocusZzorg.setZzyuserId(zzyUser.getF_zzyuser_id());
        zzyuserFocusZzorg.setFocusOrgNum(authLoginReqDto.getOrgNum());
        int count = focusZzOrgMapper.selectCount(zzyuserFocusZzorg);
        if(count == 0){
            Long focusId = gBiz.snowNextId();
            zzyuserFocusZzorg.setFoucsId(focusId);
            zzyuserFocusZzorg.setFocusTime(new Date());
            zzyuserFocusZzorg.setUpdateTime(new Date());
            focusZzOrgMapper.insertSelective(zzyuserFocusZzorg);
        }

        //重新查询会员账号
        paramMap.put("focusOrgNum",authLoginReqDto.getOrgNum());
        List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
        if(CollectionUtils.isEmpty(zzyUserList)) throw new BusinessException(0,"会员账号不存在");
        zzyUser = zzyUserList.get(0);
        if(Objects.isNull(zzyUser)) throw new BusinessException(0,"会员账号不存在");
        return zzyUser;
    }




    @Override
    public AjaxResponse captchaLogin(AuthLoginReqDto authLoginReqDto){
        Project project = null;
        String userName = StringUtils.trim(authLoginReqDto.getUsername());
        Integer projectId = authLoginReqDto.getProjectId();
        String code = authLoginReqDto.getCode();
        Integer orgNum = authLoginReqDto.getOrgNum();
        Boolean thirdLoginFlag = authLoginReqDto.getThirdLoginFlag();
        Map<String, Object> paramMap = Maps.newHashMap();
        if(projectId!=null && projectId > 0){
            project = projectService.selectByProjectId(projectId, orgNum);
        }
        CheckLog checkLog = new CheckLog();
        checkLog.setOrgNum(orgNum);
        checkLog.setWayNumber(userName);
        if(Objects.nonNull(project)){
            checkLog.setProjectId(projectId);
            checkLog.setExhibitCode(StringUtils.trim(project.getF_exhibit_code()));
        }
        String msg = checkLogService.checkCode(checkLog,code);

        if (StringUtils.isNotBlank(msg)){
            String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，验证码错误";
            ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                    .memo(loginMemo)
                    .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                    .loginState(0)
                    .build();
            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);

            Integer errCode = -2;
            if(BooleanUtils.isTrue(thirdLoginFlag)){
                errCode = SystemAuthExceptionEnum.CAPTCHA_CODE_ERROR.getCode();
            }
//            paramMap.clear();
//            paramMap.put("loginAccount",authLoginReqDto.getUsername());
//            paramMap.put("orgNum",authLoginReqDto.getOrgNum());
//            paramMap.put("loginState",0);
//            paramMap.put("currentDateLoginCount",true);
//            int existCount = zzyuserLoginLogMapper.count(paramMap);
//            if(CommonConstant.MAX_NUMBER_LOGIN_FAIL > existCount && existCount >= CommonConstant.LOGIN_ERROR_COUNT_THRESHOLD){
//                msg = "验证码错误，您还剩" + (CommonConstant.MAX_NUMBER_LOGIN_FAIL - existCount) + "次重试的机会";
//            }else if(CommonConstant.MAX_NUMBER_LOGIN_FAIL <= existCount){
//                msg = "验证码错误，今天登录错误次数过多，该帐号[" + authLoginReqDto.getUsername() + "]被禁止登录";
//            }
            return AjaxResponse.create(errCode,msg);
        }
        paramMap.clear();
        if(Validator.isEmail(userName)){//邮箱
            paramMap.put("queryMemberEmail",StringUtils.lowerCase(userName));
        }else{
            paramMap.put("queryMemberMobile",userName);
        }
        paramMap.put("certifiedMobileOrEmail",true);//账号密码登录 只查询存在的已认证的账号
        List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
        if(CollectionUtils.isEmpty(zzyUserList)){
            paramMap.put("certifiedMobileOrEmail",false);//未认证
            zzyUserList = zzyuserService.selectZzyUser(paramMap);
            if(CollectionUtils.isNotEmpty(zzyUserList)){
                List<Map<String,Object>> zzyUserVoList = buildRefZzyUser(zzyUserList,authLoginReqDto.getOrgNum());
                if(BooleanUtils.isTrue(thirdLoginFlag)){
                    return AjaxResponse.create(SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getCode(), SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getMessage(),zzyUserVoList);
                }else{
                    return AjaxResponse.create(-9,"存在未认证的会员关联账号",zzyUserVoList);
                }
            }
            String isRegister = checkIsRegister(authLoginReqDto.getOrgNum());
            if(StringUtils.isNotBlank(isRegister) && BooleanUtils.toBoolean(isRegister)){
                if(BooleanUtils.isTrue(thirdLoginFlag)){
                    return AjaxResponse.create(SystemAuthExceptionEnum.MOBILE_OR_EMAIL_NOT_REGISTER_WHETHER_REGISTER_NEW_USER.getCode(), SystemAuthExceptionEnum.MOBILE_OR_EMAIL_NOT_REGISTER_WHETHER_REGISTER_NEW_USER.getMessage());
                }else{
                    return AjaxResponse.create(-1, SystemAuthExceptionEnum.MOBILE_OR_EMAIL_NOT_REGISTER_WHETHER_REGISTER_NEW_USER.getMessage());
                }
            }else{
                String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，账号未注册会员";
                ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                        .memo(loginMemo)
                        .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                        .loginState(0)
                        .build();
                if(BooleanUtils.isTrue(thirdLoginFlag)){
                    insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                    return AjaxResponse.create(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getCode(), SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
                }else{
                    insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                    return AjaxResponse.failure(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
                }
            }
        }
        ZzyUser zzyUser = zzyUserList.get(0);
        if(Objects.isNull(zzyUser)){
            String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，账号未注册会员";
            ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                    .memo(loginMemo)
                    .operatorType(ZzyuserLoginOperatorTypeEnum.LOGIN.getCode())
                    .loginState(0)
                    .build();
            insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
            return AjaxResponse.failure(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS.getMessage());
        }
        zzyUser = updateZzyUserCer(zzyUser, userName);
        zzyUser = selectZzyUserInfo(paramMap, zzyUser, authLoginReqDto);
        if(Validator.isEmail(userName)){
            zzyUser.setLoginModeType(3);
        }else{
            zzyUser.setLoginModeType(2);
        }
        return AjaxResponse.success(zzyUser);
    }


    @Override
    public AjaxResponse buildAccessTokenToRedis(AuthLoginReqDto authLoginReqDto, ZzyUser zzyUser, Map<String, Object> redisHashMap) throws BusinessException {
        //这里直接判断会员是否存在名片
        if(zzyUser.getMemberInfoId() == null){
            Long memberInfoId = zzyuserService.insertOrUpdateMemberInfo(zzyUser.getF_zzyuser_id());
            if(memberInfoId!=null && memberInfoId > 0) zzyUser.setMemberInfoId(memberInfoId);
        }
        String accessToken =UuidUtil.generateAccessToken();
        //构建redis存放实体
        AuthLoginRespVo authLoginRespVo = buildAuthLoginRespVo(zzyUser);
        if(Objects.nonNull(authLoginReqDto)){
            //登录日志Id
            Long loginId = newIntegerIdService.snowNextId();
            authLoginRespVo.setLoginId(loginId);
        }

        //redis存储tokenkey
        Object from = redisHashMap.get("from");
        String redisKey = formatClientTypeToken(authLoginRespVo,String.valueOf(redisHashMap.get("loginClientType")),null,null,from);
        if(Objects.nonNull(from)) redisHashMap.remove("from");

        //默认不启用会员互踢模式
        boolean close_member_mutual_kick_mode = true;
        if(authLoginRespVo.getFocusOrgNum()!=null){
            SysSettingOrg build = SysSettingOrg.builder()
                    .settingCode(SysSettingEnum.MEMBER_MUTUAL_KICK_MODE.getCode())
                    .orgNum(authLoginRespVo.getFocusOrgNum())
                    .build();
            String member_mutual_kick_mode = sysSettingOrgService.getSysSettingOrgValue(build);
            if(BooleanUtils.isTrue(BooleanUtils.toBoolean(StringUtils.isNotBlank(member_mutual_kick_mode)))){
                close_member_mutual_kick_mode = false;
            }
        }
        boolean existAccessTokenFlag = true;
        //判断是否存在业务登录token（CRM展商参展详情服务工单）
        Integer sponsorOperatorId = zzyUser.getSponsorOperatorId();
        if((sponsorOperatorId!=null && sponsorOperatorId > 0) || close_member_mutual_kick_mode){
            Boolean existLoginFlag = redisUtil.hasKey(redisKey);
            if(BooleanUtils.isTrue(existLoginFlag)){
                Object existAccessTokenObj = redisUtil.hget(redisKey, "accessToken");
                if(Objects.nonNull(existAccessTokenObj)){
                    accessToken = String.valueOf(existAccessTokenObj);
                    redisHashMap.put("accessToken",existAccessTokenObj);
                    existAccessTokenFlag = false;
                }
            }
        }
        if(existAccessTokenFlag){
            if(StringUtils.isBlank(accessToken)) accessToken =UuidUtil.generateAccessToken();
            redisHashMap.put("accessToken",accessToken);
        }
        redisHashMap.put("loginTime", DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        redisHashMap.put("userInfo",JSONUtil.toJsonStr(authLoginRespVo));
        redisUtil.hmset(redisKey, redisHashMap, SignLoginConstant.MEMBER_EXPIRES_SECOND);//redis有效期10小时
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(20);
        //是否是第三方（第三方的前端人员对接）调取登录
        Boolean thirdLoginFlag = zzyUser.getThirdLoginFlag();
        paramMap.clear();
        String clientTypeToken = formatClientTypeToken(authLoginRespVo,String.valueOf(redisHashMap.get("loginClientType")),accessToken,"clientType",from);
        if(BooleanUtils.isTrue(thirdLoginFlag)){
            paramMap.put(CommonConstant.TOKEN_NAME, AESSecretUtil.encryptToStr(clientTypeToken, CommonConstant.TOKEN_SECRET));
        }else{
            String projectIdStr = zzyUser.getF_project_id() != null ? zzyUser.getF_project_id().toString() : "";
            paramMap.put(CommonConstant.TOKEN_NAME,clientTypeToken+"_"+zzyUser.getFocusOrgNum()+"_"+projectIdStr);
            paramMap.put("zzyUserInfo",authLoginRespVo);
        }
        //是否是企业登录
        Boolean enterpriseLoginFlag = zzyUser.getEnterpriseLoginFlag();
        //品牌项目ID  在方法查询中暂时注释
        Integer brandProjectId = zzyUser.getBrandProjectId();
        //企业会员中心登录时 判断会员是否存在企业
        if(BooleanUtils.isTrue(enterpriseLoginFlag)){
            Boolean existEnterpriseInfo = queryExistEnterprise(authLoginRespVo.getZzyUserId(), zzyUser.getFocusOrgNum(), brandProjectId);
            authLoginRespVo.setExistEnterpriseInfo(existEnterpriseInfo);
        }
        if(Objects.nonNull(authLoginReqDto)) {
            //插入登录日志
            String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN) + "登录成功";
            ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                    .loginId(authLoginRespVo.getLoginId())
                    .zzyuserId(zzyUser.getF_zzyuser_id())
                    .memo(loginMemo)
                    .loginState(1)
                    .logoutState(0)
                    .sessionId(String.valueOf(paramMap.get(CommonConstant.TOKEN_NAME)))
                    .operatorType(authLoginReqDto.getLoginOperatorType())
                    .build();
            insertZzyUserLog(authLoginReqDto, zzyuserLoginLog);
        }
        return AjaxResponse.success("",paramMap);
    }


    /**
     * 查询是否存在企业
     * @return
     */
    private Boolean queryExistEnterprise(Integer zzyUserId,Integer orgNum,Integer brandProjectId){
        if(zzyUserId == null || orgNum == null) return false;
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(10);
        //paramMap.put("brandId",brandProjectId);
        paramMap.put("orgNum",orgNum);
        paramMap.put("zzyUserId",zzyUserId);
        paramMap.put("enterpriseFocusState",1);
        List<Enterprise> enterprises = enterpriseMapper.selectByMap(paramMap);
        if(CollectionUtils.isNotEmpty(enterprises)) return true;
        return false;
    }


    private String formatClientTypeToken(AuthLoginRespVo authLoginRespVo,String clientType,String accessToken,String typeFlag,Object from) {
        Integer orgNum = authLoginRespVo.getFocusOrgNum();
        Integer zzyUserId = authLoginRespVo.getZzyUserId();
//        String encodeSign = Base64.encode(orgNum + ":" + zzyUserId);
        Integer sponsorOperatorId = authLoginRespVo.getSponsorOperatorId();
        Integer clientId = authLoginRespVo.getClientId();
        Integer projectId = authLoginRespVo.getProjectId();
        String encodeSign = String.valueOf(orgNum);
        if(zzyUserId!=null && zzyUserId > 0){
            if(sponsorOperatorId!=null && sponsorOperatorId > 0){
                encodeSign = encodeSign+"-"+projectId+"-"+clientId+"-"+sponsorOperatorId;
            }else{
                encodeSign = encodeSign + "-" + zzyUserId;
            }
        }else{
            if(sponsorOperatorId!=null && sponsorOperatorId > 0){
                encodeSign = encodeSign +"-"+projectId+"-"+clientId+"-"+sponsorOperatorId;
            }
        }
        if(Objects.nonNull(from)){//展商自助中心
            encodeSign = encodeSign+"-OPER";
        }
        String accessTokenRFormat = null;
        if(StringUtils.isNotBlank(typeFlag) && "clientType".equals(typeFlag)){
            accessTokenRFormat = String.format(SignLoginConstant.MEMBER_ACCESS_TOKEN_CLIENT_TYPE,clientType,encodeSign,accessToken);
        }else{
            accessTokenRFormat = String.format(SignLoginConstant.MEMBER_ACCESS_TOKEN_REDIS_KEY,clientType,encodeSign);
        }
        return accessTokenRFormat;
    }


    private AuthLoginRespVo buildAuthLoginRespVo(ZzyUser zzyUser){
        AuthLoginRespVo authLoginRespVo = AuthLoginRespVo.builder().build();
        authLoginRespVo.setZzyUserId(zzyUser.getF_zzyuser_id());
        authLoginRespVo.setZzorgClientId(zzyUser.getZzorgClientId());
        authLoginRespVo.setClientId(zzyUser.getF_client_id());
        authLoginRespVo.setFocusOrgNum(zzyUser.getFocusOrgNum());
        authLoginRespVo.setUserName(zzyUser.getF_user_name());
        authLoginRespVo.setMobile(zzyUser.getF_mobile());
        authLoginRespVo.setEmail(zzyUser.getF_email());
        authLoginRespVo.setNickName(zzyUser.getF_name());
        authLoginRespVo.setPasswordUpdateTime(zzyUser.getPasswordUpdateTime());
        authLoginRespVo.setVipLevel(zzyUser.getVipLevel());
        authLoginRespVo.setVersion(zzyUser.getVersion());
        authLoginRespVo.setIsForeign(zzyUser.getIsForeign());
        authLoginRespVo.setMemberGradeId(zzyUser.getMemberGradeId());
        authLoginRespVo.setProjectId(zzyUser.getF_project_id());
        authLoginRespVo.setExhibitCode(zzyUser.getF_exhibit_code());
        authLoginRespVo.setRegTime(zzyUser.getF_reg_time());
        authLoginRespVo.setUpdateTime(zzyUser.getF_update_time());
        authLoginRespVo.setChildOrgNum(zzyUser.getF_org_num());
        authLoginRespVo.setLoginModeType(zzyUser.getLoginModeType());
        authLoginRespVo.setMobileCertified(zzyUser.getMobileCertified());
        authLoginRespVo.setEmailCertified(zzyUser.getEmailCertified());
        authLoginRespVo.setSupplyAndDemand(zzyUser.getSupplyAndDemand());
        authLoginRespVo.setPwdGrade(zzyUser.getPwdGrade());
        authLoginRespVo.setHeadImage(zzyUser.getHeadImage());
        authLoginRespVo.setOperatorId(zzyUser.getOperatorId());
        authLoginRespVo.setSponsorOperatorId(zzyUser.getSponsorOperatorId());
        authLoginRespVo.setMemberInfoId(zzyUser.getMemberInfoId());
        authLoginRespVo.setZzyUserType(zzyUser.getZzyuserType());
        return authLoginRespVo;
    }

//    /**
//     * 构建jwt加密参数
//     * @param authLoginRespVo
//     * @param signToken
//     * @return
//     */
//    @NotNull
//    private Map<String,Object> buildClaims(@NotNull AuthLoginRespVo authLoginRespVo, String signToken){
//        Map<String,Object> claimsMap = Maps.newHashMap();
//        claimsMap.put("orgNum",authLoginRespVo.getFocusOrgNum());
//        claimsMap.put("zzyUserId",authLoginRespVo.getZzyUserId());
//        claimsMap.put("loginType",SignUserTypeEnum.MEMBER.getCode());
//        claimsMap.put("signToken",signToken);
//        return claimsMap;
//    }

    @Override
    public void logout(String authorization) {
        /**
         * 请求头的token：MEMBER-PC-1002-3809-11fec888684e45e2912aa50853d29983_1002_1837
         */
//        String decodeStr = Base64.decodeStr(accessToken);
//        String sourceAccessToken = AESSecretUtil.decryptToStr(decodeStr, SignLoginConstant.DATAKEY);
//        String redisKey = StringUtils.substring(sourceAccessToken, 0,sourceAccessToken.lastIndexOf(":"));
        authorization = StringUtils.trim(authorization);
        String[] tokenGroups = StringUtils.split(authorization, "_");
        String redisKey = StringUtils.substringBeforeLast(tokenGroups[0], "-");
        Boolean existFlag = redisUtil.hasKey(redisKey);
        if(BooleanUtils.isFalse(existFlag)) return;
        String uuidToken = StringUtils.substringAfterLast(StringUtils.trim(tokenGroups[0]), "-");
        Object redisValue = redisUtil.hHasKey(redisKey,CommonConstant.ACCESS_TOKEN_REDIS_HAS_KEY_ITEM);
        if(Objects.isNull(redisValue)) return;
        Object redisTokenValue = redisUtil.hget(redisKey, CommonConstant.ACCESS_TOKEN_REDIS_HAS_KEY_ITEM);
        if(Objects.isNull(redisTokenValue)) return;
        if(!uuidToken.equals(String.valueOf(redisTokenValue))) return;
        redisUtil.del(redisKey);
    }

    /**
     * 注册
     * @param authLoginReqDto
     * @return
     */
    @Override
    public AjaxResponse register(AuthLoginReqDto authLoginReqDto) {
        ZzyUser zzyUser = null;
        Project project = null;
        Integer socialType = authLoginReqDto.getSocialType();//授权登录 排除小程序（34）
        String code = StringUtils.trim(authLoginReqDto.getCode());
        Boolean refZzyUserRegFlag = authLoginReqDto.getRefZzyUserRegFlag();//注册请求来源存在关联账号  该参数为true直接注册会员 false先查询
        Integer projectId = authLoginReqDto.getProjectId();
        Integer orgNum = authLoginReqDto.getOrgNum();
        String sources = StringUtils.trim(authLoginReqDto.getSources());
        Boolean thirdLoginFlag = authLoginReqDto.getThirdLoginFlag();//第三方授权获取
        if(projectId!=null && projectId > 0){
            project = projectService.selectByProjectId(projectId,orgNum);
        }
        //后端进行判断是否是邮箱或手机号
        String userName = StringUtils.trim(authLoginReqDto.getUsername());
        if(authLoginReqDto.getSendType() == null){
            if(Validator.isEmail(userName)){
                authLoginReqDto.setSendType(2);
            }else{
                authLoginReqDto.setSendType(1);
            }
        }
        Integer sendType = authLoginReqDto.getSendType();
        ZzyUser registerUser = new ZzyUser();
        Map<String,Object> paramMap = Maps.newHashMap();
        registerUser.setF_create_org_num(authLoginReqDto.getOrgNum());
        if(RegisterSourceEnum.BUYER_REG.getCode().equals(sources)
                || RegisterSourceEnum.WX_MINI_REG.getCode().equals(sources)
                || RegisterSourceEnum.TRADER_REG.getCode().equals(sources)){//观众登记、展商参展申请、微信小程序 buyerId,projectId,orgNum
            registerUser.setF_reg_source(sources);
            if(!(socialType!=null && 34 == socialType)){//注册时排除微信小程序校验验证码
                CheckLog checkLog = new CheckLog();
                checkLog.setOrgNum(orgNum);
                checkLog.setWayNumber(userName);
                if(RegisterSourceEnum.TRADER_REG.getCode().equals(sources)){
                    checkLog.setTarget(authLoginReqDto.getTarget());
                }
                if(Objects.nonNull(project)){
                    checkLog.setProjectId(projectId);
                }else{
                    checkLog.setProjectId(projectId);
                }
                String msg = checkLogService.checkCode(checkLog,code);
                if (StringUtils.isNotBlank(msg)){
                    String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，验证码错误";
                    ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                            .memo(loginMemo)
                            .operatorType(ZzyuserLoginOperatorTypeEnum.REGISTER.getCode())
                            .loginState(0)
                            .build();
                    if(BooleanUtils.isTrue(thirdLoginFlag)){
                        insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                        return AjaxResponse.create(SystemAuthExceptionEnum.CAPTCHA_CODE_ERROR.getCode(),msg);
                    }else{
                        insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                        return AjaxResponse.create(-2,msg);
                    }
                }
            }
            paramMap.clear();
            if(!refZzyUserRegFlag) {
                if (authLoginReqDto.getBuyerId() != null && authLoginReqDto.getBuyerId() > 0) {
                    paramMap.put("buyerId", authLoginReqDto.getBuyerId());
                    paramMap.put("orgNum", authLoginReqDto.getOrgNum());
                    paramMap.put("projectId", authLoginReqDto.getProjectId());
                    BuyerReg buyerReg = buyerRegMapper.getWithType(paramMap);
                    if (Objects.isNull(buyerReg)){
                        return AjaxResponse.failure("观众信息不存在");
                    }
                    String mobile = StringUtils.trim(buyerReg.getMobile());
                    String email = StringUtils.trim(buyerReg.getEmail());
                    if (StringUtils.isBlank(mobile) && StringUtils.isBlank(email)){
                        return AjaxResponse.failure("观众手机邮箱为空");
                    }
                    paramMap.clear();
                    paramMap.put("buyerRegister", true);//特殊 观众登记
                    paramMap.put("buyerMobile", mobile);
                    paramMap.put("buyerEmail", email);
                    if(StringUtils.isNotBlank(email) && email.indexOf("@") > 0){
                        paramMap.put("buyerEmailIgnoreCase", StringUtils.lowerCase(email));
                    }
                } else {
                    paramMap.clear();
                    if(Validator.isEmail(userName)){//邮箱
                        paramMap.put("queryMemberEmail",StringUtils.lowerCase(userName));
                    }else{
                        paramMap.put("queryMemberMobile",userName);
                    }
                }
                paramMap.put("certifiedMobileOrEmail", true);//已认证的
                paramMap.put("orderBy", "a.f_reg_time desc");//已认证的
                List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
                if (CollectionUtils.isEmpty(zzyUserList)) {
                    paramMap.put("certifiedMobileOrEmail", false);//已认证的
                    zzyUserList = zzyuserService.selectZzyUser(paramMap);
                    if (CollectionUtils.isNotEmpty(zzyUserList)) {
                        List<Map<String, Object>> zzyUserVoList = buildRefZzyUser(zzyUserList, authLoginReqDto.getOrgNum());
                        if(BooleanUtils.isTrue(thirdLoginFlag)){
                            return AjaxResponse.create(SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getCode(), SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getMessage(),zzyUserVoList);
                        }else{
                            return AjaxResponse.create(-9, "存在未认证的会员关联账号", zzyUserVoList);
                        }
                    }
                } else {
                    ZzyUser zzy_user = zzyUserList.get(0);
                    zzy_user = updateZzyUserCer(zzy_user, userName);
                    paramMap.clear();
                    paramMap.put("zzyUserId", zzy_user.getF_zzyuser_id());
                    zzyUser = selectZzyUserInfo(paramMap, zzy_user, authLoginReqDto);
                    if (2 == sendType) {//邮箱
                        zzyUser.setLoginModeType(3);
                    } else {
                        zzyUser.setLoginModeType(2);
                    }
                    //社交授权绑定会员ID
                    AjaxResponse resp = buildZzyUserSocial(authLoginReqDto, zzyUser);
                    if (resp.getState() != 1) return resp;
                    //如果项目ID不为空，为该会员创建会员项目关联
                    if (projectId != null && projectId > 0) {
                        paramMap.clear();
                        paramMap.put("projectId", projectId);
                        paramMap.put("zzyUserId", zzyUser.getF_zzyuser_id());
                        paramMap.put("sources", sources);
                        zzyuserService.inserOrUpdateUserFocusProj(paramMap);
                    }
                    zzyUser.setThirdLoginFlag(thirdLoginFlag);
                    return buildAccessTokenToRedis(authLoginReqDto, zzyUser, authLoginReqDto.getRequestHeadMap());
                }
            }
            if(2 == sendType){//邮箱
                registerUser.setEmailCertified(true);
                registerUser.setF_email(userName);
            }else{
                registerUser.setMobileCertified(true);
                registerUser.setF_mobile(userName);
            }
        }else if(RegisterSourceEnum.BUSINESS.getCode().equals(sources)
                || RegisterSourceEnum.VIP.getCode().equals(sources)
                || RegisterSourceEnum.BOOTH_LAYOUT.getCode().equals(sources)
                || RegisterSourceEnum.TICKET_CERTIFICATE.getCode().equals(sources)
                || RegisterSourceEnum.SITE_TRADE.getCode().equals(sources)){//展商自助 行业网站 会员中心
            registerUser.setF_reg_source(sources);
            CheckLog checkLog = new CheckLog();
            checkLog.setOrgNum(orgNum);
            checkLog.setWayNumber(userName);
            if(Objects.nonNull(project)){
                checkLog.setProjectId(projectId);
                checkLog.setExhibitCode(StringUtils.trim(project.getF_exhibit_code()));
            }
            String msg = checkLogService.checkCode(checkLog,code);
            if (StringUtils.isNotBlank(msg)){
                String loginMemo = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN)+"登录失败，验证码错误";
                ZzyuserLoginLog zzyuserLoginLog = ZzyuserLoginLog.builder()
                        .memo(loginMemo)
                        .operatorType(ZzyuserLoginOperatorTypeEnum.REGISTER.getCode())
                        .loginState(0)
                        .build();
                insertZzyUserLog(authLoginReqDto,zzyuserLoginLog);
                return AjaxResponse.create(0,msg);
            }
            if(!refZzyUserRegFlag){
                paramMap.clear();
//                paramMap.put("userName",userName);
//                if(userName.indexOf("@") > 0){
//                    paramMap.put("userNameIgnoreCase", StringUtils.lowerCase(userName));
//                }
                if(Validator.isEmail(userName)){//邮箱
                    paramMap.put("queryMemberEmail",StringUtils.lowerCase(userName));
                }else{
                    paramMap.put("queryMemberMobile",userName);
                }
                paramMap.put("certifiedMobileOrEmail",true);
                List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
                if(CollectionUtils.isEmpty(zzyUserList)){//不存在已认证的账号
//                    if(!Validator.isEmail(userName) && !RegexUtils.validateAllMobile(userName)){
//                        return AjaxResponse.failure("注册账号不符号注册条件（手机或邮箱）");
//                    }
                    paramMap.put("certifiedMobileOrEmail",false);
                    zzyUserList = zzyuserService.selectZzyUser(paramMap);
                    if(CollectionUtils.isNotEmpty(zzyUserList)){
                        List<Map<String,Object>> zzyUserVoList = buildRefZzyUser(zzyUserList,authLoginReqDto.getOrgNum());
                        if(BooleanUtils.isTrue(thirdLoginFlag)){
                            return AjaxResponse.create(SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getCode(), SystemAuthExceptionEnum.AN_UNAUTHEN_MEMBER_EXISTS.getMessage(),zzyUserVoList);
                        }else{
                            return AjaxResponse.create(-9,"存在未认证的会员关联账号",zzyUserVoList);
                        }
                    }
                }else{
                    ZzyUser zzy_user = zzyUserList.get(0);
                    zzy_user = updateZzyUserCer(zzy_user,userName);
                    paramMap.clear();
                    paramMap.put("zzyUserId",zzy_user.getF_zzyuser_id());
                    zzyUser = selectZzyUserInfo(paramMap,zzy_user, authLoginReqDto);
                    if(2 == sendType){
                        zzyUser.setLoginModeType(3);
                    }else{
                        zzyUser.setLoginModeType(2);
                    }
                    //社交授权绑定会员ID
                    AjaxResponse resp = buildZzyUserSocial(authLoginReqDto,zzyUser);
                    if(resp.getState()!=1) return resp;
                    //如果项目ID不为空，为该会员创建会员项目关联
                    if(projectId !=null && projectId > 0){
                        paramMap.clear();
                        paramMap.put("projectId",authLoginReqDto.getProjectId());
                        paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
                        paramMap.put("sources",sources);
                        zzyuserService.inserOrUpdateUserFocusProj(paramMap);
                    }
                    zzyUser.setThirdLoginFlag(thirdLoginFlag);
                    return buildAccessTokenToRedis(authLoginReqDto, zzyUser, authLoginReqDto.getRequestHeadMap());
                }
            }
            if(2 == sendType){
                registerUser.setEmailCertified(true);
                registerUser.setF_email(userName);
            }else{
                registerUser.setMobileCertified(true);
                registerUser.setF_mobile(userName);
            }
        }
        //注册新会员
        Map<String, Object> resultMap = zzyuserService.insertRegister(authLoginReqDto, registerUser);
        if(MapUtils.isNotEmpty(resultMap)){
            Integer zzyUserId = (Integer) resultMap.get("zzyUserId");
            Integer focusOrgNum = (Integer) resultMap.get("focusOrgNum");
            zzyUser = zzyuserService.selectByPrimaryKey(zzyUserId, focusOrgNum);
//            if(refZzyUserRegFlag){//清空跟注册新会员账号一致且未认证的其它会员中的手机号邮箱
//                authLoginReqDto.setZzyUserId(zzyUserId);
//                zzyuserService.resetAndUpdateZzyUserInfo(authLoginReqDto);
//            }
        }
        //社交授权绑定会员ID
        AjaxResponse resp = buildZzyUserSocial(authLoginReqDto, zzyUser);
        if(resp.getState()!=1) return resp;
        if(2 == sendType){
            zzyUser.setLoginModeType(3);
        }else{
            zzyUser.setLoginModeType(2);
        }
        zzyUser.setThirdLoginFlag(thirdLoginFlag);
        return buildAccessTokenToRedis(authLoginReqDto, zzyUser, authLoginReqDto.getRequestHeadMap());
    }


    /**
     * 社交授权绑定会员ID
     */
    public AjaxResponse buildZzyUserSocial(AuthLoginReqDto authLoginReqDto,ZzyUser zzyUser){
        zzyUser.setF_project_id(authLoginReqDto.getProjectId());
        Map<String,Object> paramMap = Maps.newHashMap();
        Integer socialType = authLoginReqDto.getSocialType();
        String openId = authLoginReqDto.getOpenId();
        if(StringUtils.isNotBlank(authLoginReqDto.getLoginType()) && authLoginReqDto.getLoginType().equals(SignTypeEnum.SOCIAL_LOGIN.getCode())
                && socialType!=null
                && StringUtils.isNotBlank(authLoginReqDto.getOpenId())
        ){
            List<Integer> socialTypeList = Arrays.asList(31,32);
            if(socialTypeList.contains(socialType)){//微信公众号、微信开放平台
                paramMap.clear();
                paramMap.put("userType",4);
                paramMap.put("orgNum",zzyUser.getFocusOrgNum());
                paramMap.put("socialType",socialType);
                paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id().longValue());
                List<ZzyUserSocialBind> socialBindList = socialBindService.selectZzyUserSocialBind(paramMap);
                if(CollectionUtils.isNotEmpty(socialBindList)){
                    List<String> openIdList = socialBindList.stream().filter(Objects::nonNull).filter(zzyUserSocialBind -> StringUtils.isNotBlank(zzyUserSocialBind.getOpenId()))
                            .map(ZzyUserSocialBind::getOpenId).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(openIdList) && !openIdList.contains(authLoginReqDto.getOpenId())){
                        return AjaxResponse.failure("该会员已绑定其他微信号，请重新确认");
                    }
                }
            }
            //通过openId查询已存在的授权信息
            paramMap.clear();
            paramMap.put("socialType",socialType);
            paramMap.put("openId",openId);
            List<ZzyUserSocial> socialUserList = zzyUserSocialMapper.selectZzyUserSocial(paramMap);
            if(CollectionUtils.isNotEmpty(socialUserList)){
                ZzyUserSocial zzyUserSocial = socialUserList.get(0);
                paramMap.clear();
                paramMap.put("userType",4);
                paramMap.put("orgNum",zzyUser.getFocusOrgNum());
                paramMap.put("socialType",socialType);
                paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id().longValue());
                paramMap.put("socialUserId",zzyUserSocial.getId());
                List<ZzyUserSocialBind> socialBindList = socialBindService.selectZzyUserSocialBind(paramMap);
                if(CollectionUtils.isEmpty(socialBindList)){
                    DateTime date = DateUtil.date();
                    // 绑定当前登录的社交用户
                    ZzyUserSocialBind socialUserBind = ZzyUserSocialBind.builder()
                            .id(newIntegerIdService.snowNextId())
                            .userId(zzyUser.getF_zzyuser_id().longValue())
                            .userType(4)
                            .orgNum(zzyUser.getFocusOrgNum())
                            .socialUserId(zzyUserSocial.getId())
                            .socialType(socialType)
                            .createTime(date)
                            .updateTime(date)
                            .build();
                    socialBindService.insertZzyUserSocialBind(socialUserBind);
                }
            }
        }
        return AjaxResponse.success();
    }

    @Override
    public AjaxResponse confirmRefZzyUser(AuthLoginReqDto authLoginReqDto) {
        Integer refZzyUserId = authLoginReqDto.getRefZzyUserId();
        ZzyUser refZzyUser = zzyuserService.selectByPrimaryKey(refZzyUserId, null);
        if(Objects.isNull(refZzyUser)) return  AjaxResponse.failure("关联会员不存在");
        Map<String, Object> paramMap = Maps.newHashMap();
        String userName = StringUtils.trim(authLoginReqDto.getUsername());
        paramMap.put("userName",userName);
        if(userName.indexOf("@") > 0){
            paramMap.put("userNameIgnoreCase",StringUtils.lowerCase(userName));
        }
        paramMap.put("certifiedMobileOrEmail",false);//账号密码登录 只查询存在的未认证的账号
        List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
        if(CollectionUtils.isNotEmpty(zzyUserList)){
            String username = authLoginReqDto.getUsername();
            Boolean mobileFlag = false;
            Boolean emailFlag = false;
            if(Validator.isEmail(username)){
                emailFlag = true;
            }else if(Validator.isMobile(username)
                      || Validator.isMatchRegex(PatternPool.MOBILE_MO,username)
                      || Validator.isMatchRegex(PatternPool.MOBILE_HK,username)
                      || Validator.isMatchRegex(PatternPool.MOBILE_TW,username)){
                    mobileFlag = true;
            }else{
                return AjaxResponse.failure("当前登录账号不支持除手机、邮箱以外的关联");//不是手机号、不是邮箱 直接返回
            }
            for(ZzyUser zzyUser : zzyUserList){
                if(zzyUser.getF_zzyuser_id().equals(refZzyUserId)){//直接把认证
                    paramMap.clear();
                    paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
                    if(mobileFlag) paramMap.put("certifiedMobile",true);
                    if(emailFlag) paramMap.put("certifiedEmail",true);
                    zzyuserService.updateZzyUserCertifiedById(paramMap);
                }
//                else{
//                    if(mobileFlag) paramMap.put("resetMobile",true);
//                    if(emailFlag) paramMap.put("resetEmail",true);
//                    zzyuserService.updateResetZzyUserMobilrOrEmail(paramMap);
//                }
            }
            paramMap.clear();
            paramMap.put("zzyUserId",authLoginReqDto.getRefZzyUserId());
            paramMap.put("certifiedMobileOrEmail",true);//账号密码登录 只查询存在的已认证的账号
            paramMap.put("focusOrgNum",authLoginReqDto.getOrgNum());//账号密码登录 只查询存在的已认证的账号
            zzyUserList = zzyuserService.selectZzyUser(paramMap);
            if(CollectionUtils.isEmpty(zzyUserList)) return AjaxResponse.failure("会员不存在");
            ZzyUser zzyUser = zzyUserList.get(0);
            //社交授权绑定会员ID
            AjaxResponse resp = buildZzyUserSocial(authLoginReqDto,zzyUser);
            if(resp.getState()!=1) return resp;
            //如果项目ID不为空，为该会员创建会员项目关联
            if(authLoginReqDto.getProjectId() !=null && authLoginReqDto.getProjectId() > 0){
                paramMap.clear();
                paramMap.put("projectId",authLoginReqDto.getProjectId());
                paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
                zzyuserService.inserOrUpdateUserFocusProj(paramMap);
            }
            if(Validator.isEmail(userName)){
                zzyUser.setLoginModeType(3);
            }else{
                zzyUser.setLoginModeType(2);
            }
            return buildAccessTokenToRedis(null, zzyUser, authLoginReqDto.getRequestHeadMap());
        }
        return AjaxResponse.success();
    }

    @Override
    public AjaxResponse socialLogin(SocialUserBindDto socialUserBindDto) throws BusinessException {
        Integer socialType = socialUserBindDto.getSocialType();
        ZzyUserSocial zzyUserSocial = socialBindService.authSocialUser(socialUserBindDto);
        if(Objects.isNull(zzyUserSocial)) throw new BusinessException(0,"获取社交授权信息异常");
        Map<String,Object> resultMap = Maps.newHashMap();
        resultMap.put("openId",zzyUserSocial.getOpenId());
        resultMap.put("orgNum",zzyUserSocial.getOrgNum());
        resultMap.put("socialType",zzyUserSocial.getSocialType());
        return AjaxResponse.success(resultMap);
    }

    @Override
    public AjaxResponse confirmBindZzyUser(SocialUserBindDto socialUserBindDto) {
        // 使用 code 授权码进行登录,然后获得到绑定的用户编号
        AjaxResponse resp = socialBindService.getSocialBindZzyUser(socialUserBindDto);
        if(resp.getState()!=1){
            return resp;
        }
        ZzyUserSocialBind socialBindZzyUser = (ZzyUserSocialBind) resp.getData();
        //获得用户
        ZzyUser zzyUser = zzyuserService.selectByPrimaryKey(socialBindZzyUser.getUserId().intValue(), socialBindZzyUser.getOrgNum());
        if (Objects.isNull(zzyUser)) {
            return AjaxResponse.create(CommonResponseStateEnum.ZZYUSER_NOT_EXIST);
        }
        if(socialUserBindDto.getProjectId()!=null && socialUserBindDto.getProjectId() > 0){
            zzyUser.setF_project_id(socialUserBindDto.getProjectId());
            Map<String,Object> paramMap = Maps.newHashMap();
            paramMap.put("projectId",socialUserBindDto.getProjectId());
            paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
            zzyuserService.inserOrUpdateUserFocusProj(paramMap);
        }
        // 创建 Token
        return buildAccessTokenToRedis(null, zzyUser, socialUserBindDto.getRequestHeadMap());
    }

    @Override
    public ZzyUser checkZzyUserIfCerfified(ZzyUser zUser, String username) {
        return updateZzyUserCer(zUser,username);
    }

    /**
     * 更新会员认证数据
     * @param zzyUser
     * @param userName
     * @return
     */
    private ZzyUser updateZzyUserCer(ZzyUser zzyUser,String userName){
        if(Objects.isNull(zzyUser)) return zzyUser;
        Map<String, Object> paramMap = Maps.newHashMap();
        //观众登记过来的账号同会员表的会员账号一致  则检查会员手机号或邮箱是否存在 不存在填充并认证，存在则直接判断是否认证，未认真的则直接认证
        if(Validator.isMobile(userName)
           || Validator.isMatchRegex(PatternPool.MOBILE_TW,userName)
           || Validator.isMatchRegex(PatternPool.MOBILE_HK,userName)
           || Validator.isMatchRegex(PatternPool.MOBILE_MO,userName)){
            String f_mobile = zzyUser.getF_mobile();
            Boolean mobileCertified = zzyUser.getMobileCertified();
            if(StringUtils.isNotBlank(f_mobile) && f_mobile.equals(userName)){
                if(!BooleanUtils.isTrue(BooleanUtils.toBoolean(mobileCertified))){
                    paramMap.put("certifiedMobile",true);
                }
            }else{
                paramMap.put("fillMobile",userName);
                paramMap.put("certifiedMobile",true);
            }
        }
        if(Validator.isEmail(userName)){
            String f_email = zzyUser.getF_email();
            Boolean emailCertified = zzyUser.getEmailCertified();
            if(StringUtils.isNotBlank(f_email) && f_email.equalsIgnoreCase(userName)){
                if(!BooleanUtils.isTrue(BooleanUtils.toBoolean(emailCertified))){
                    paramMap.put("certifiedEmail",true);
                }
            }else{
                paramMap.put("fillEmail",StringUtils.lowerCase(userName));
                paramMap.put("certifiedEmail",true);
            }
        }
        paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
        zzyuserService.updateZzyUserCertifiedById(paramMap);
        paramMap.clear();
        paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
        List<ZzyUser> zzyUserList = zzyuserService.selectZzyUser(paramMap);
        return zzyUserList.get(0);
    }

    @Override
    public AjaxResponse updateBuyerRegBindZzyUser(AuthLoginReqDto authLoginReqDto) {
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(12);
        paramMap.put("zzyUserId",authLoginReqDto.getZzyUserId());
        ZzyUser zzyUser = zzyuserService.queryZzyUser(paramMap);
        if(Objects.isNull(zzyUser)) return AjaxResponse.failure("会员不存在");
        //允许会员修改公司名称
        boolean updateCompanyFlag = false;
        if(authLoginReqDto.getOrgNum()!=null){
            String updateCompanySet = queryUpdateCompanySet(authLoginReqDto.getOrgNum());
            if(StringUtils.isNotBlank(updateCompanySet) && Boolean.valueOf(updateCompanySet)){
                updateCompanyFlag = Boolean.valueOf(updateCompanySet);
            }
        }
        AjaxResponse resp = null;
        if(authLoginReqDto.getBuyerId()!=null){
            resp = updateBuyerRegUserInfoToZzyuser(paramMap,zzyUser,authLoginReqDto);
        }
        if(authLoginReqDto.getTraderTegId()!=null || BooleanUtils.isTrue(authLoginReqDto.getTraderFailFlag())){
            resp = updateTraderUserInfoToZzyuser(paramMap,zzyUser,authLoginReqDto,updateCompanyFlag);
        }
        return resp;
    }

    /**
     * 更新观众用户信息到会员
     */
    private AjaxResponse updateBuyerRegUserInfoToZzyuser(Map<String,Object> paramMap,ZzyUser zzyUser,AuthLoginReqDto authLoginReqDto){
        paramMap.clear();
        paramMap.put("buyerId",authLoginReqDto.getBuyerId());
        paramMap.put("orgNum",authLoginReqDto.getOrgNum());
        BuyerReg buyerReg = buyerRegMapper.getWithType(paramMap);
        if(Objects.isNull(buyerReg)) return AjaxResponse.failure("观众信息不存在");
        String tempMobile = buyerReg.getMobile();
        String tempEmail = buyerReg.getEmail();
        String tempCompanyName = buyerReg.getBuyerCompany();
        String tempLinkManName = buyerReg.getBuyerName();
        String f_mobile = zzyUser.getF_mobile();
        String f_email = zzyUser.getF_email();
        paramMap.clear();
        if(StringUtils.isBlank(f_mobile) && StringUtils.isNotBlank(tempMobile)){
            paramMap.put("fillMobile",tempMobile);
        }
        if(StringUtils.isBlank(f_email) && StringUtils.isNotBlank(tempEmail)){
            paramMap.put("fillEmail",StringUtils.lowerCase(tempEmail));
        }
        paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
        zzyuserService.updateZzyUserCertifiedById(paramMap);
//        UserCompany userCompany = new UserCompany();
//        userCompany.setF_zzyuser_id(zzyUser.getF_zzyuser_id());
//        List<UserCompany> user_companyList = userCompanyMapper.select(userCompany);
//        if(CollectionUtils.isEmpty(user_companyList)){
//            DateTime date = DateUtil.date();
//            int comID = gBiz.getSysIdFromZzysys("zzysys_f_user_com_id", true);
//            userCompany.setF_user_com_id(comID);
//            userCompany.setF_company_name(tempCompanyName);
//            userCompany.setF_contacts(tempLinkManName);
//            userCompany.setF_mobile(tempMobile);
//            if(StringUtils.isNotBlank(tempEmail)){
//                userCompany.setF_email(StringUtils.lowerCase(tempEmail));
//            }
//            userCompany.setCreateTime(date);
//            userCompany.setUpdateTime(date);
//            userCompanyMapper.insertSelective(userCompany);
//        }
        return AjaxResponse.success();
    }

    /**
     * 更新参展申请用户信息到会员
     * 如设置【不勾选】 允许用户修改公司名称，用户填写参展申请时，公司名称与会员历史公司名称不一致，则不更新会员信息
     * 如设置【不勾选】 允许用户修改公司名称，用户填写参展申请时，公司名称与会员历史公司名称一致，则更新会员个人姓名、电话、邮箱等信息
     * 如设置【勾选】允许用户修改公司名称，用户填写参展申请时，公司名称与会员历史公司名称一致，则更新会员个人姓名、电话、邮箱等信息
     * 如设置【勾选】允许用户修改公司名称，用户填写参展申请时，公司名称与会员历史公司名称不一致，则更新会员公司名称、个人姓名、电话、邮箱等信息
     */
    private AjaxResponse updateTraderUserInfoToZzyuser(Map<String,Object> paramMap,ZzyUser zzyUser,AuthLoginReqDto authLoginReqDto,Boolean updateCompanyFlag){
        //未勾选 修改公司名称按钮 且 参展申请失败调取接口则直接 return
        if(BooleanUtils.isNotTrue(updateCompanyFlag) && BooleanUtils.isTrue(authLoginReqDto.getTraderFailFlag())) return AjaxResponse.success();
        String tempMobile = null;
        String tempEmail = null;
        String tempCompanyName = null;
        String tempLinkManName = null;
        //参展申请登记失败 更新会员信息
        if(BooleanUtils.isTrue(authLoginReqDto.getTraderFailFlag())){
            tempMobile = authLoginReqDto.getTraderLinkmanMobile();
            tempEmail = authLoginReqDto.getTraderLinkmanEmail();
            tempCompanyName = authLoginReqDto.getTraderCompanyName();
            tempLinkManName = authLoginReqDto.getTraderLinkmanName();
        }else{
            TraderReg traderReg = traderRegMapper.selectById(authLoginReqDto.getTraderTegId());
            if(Objects.isNull(traderReg)) return AjaxResponse.failure("参展申请不存在");
            tempMobile = traderReg.getF_mobile();
            tempEmail = traderReg.getF_email();
            tempCompanyName = traderReg.getF_trader_company();
            tempLinkManName = traderReg.getF_linker();
        }
        String f_mobile = zzyUser.getF_mobile();
        String f_email = zzyUser.getF_email();
        paramMap.clear();
        if(StringUtils.isBlank(f_mobile) && StringUtils.isNotBlank(tempMobile)){
            paramMap.put("fillMobile",tempMobile);
        }
        if(StringUtils.isBlank(f_email) && StringUtils.isNotBlank(tempEmail)){
            paramMap.put("fillEmail",StringUtils.lowerCase(tempEmail));
        }
        paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
        zzyuserService.updateZzyUserCertifiedById(paramMap);
//        UserCompany userCompany = new UserCompany();
//        userCompany.setF_zzyuser_id(zzyUser.getF_zzyuser_id());
//        List<UserCompany> user_companyList = userCompanyMapper.select(userCompany);
//        DateTime date = DateUtil.date();
//        if(CollectionUtils.isEmpty(user_companyList)){
//            int comID = gBiz.getSysIdFromZzysys("zzysys_f_user_com_id", true);
//            userCompany.setF_user_com_id(comID);
//            userCompany.setF_company_name(tempCompanyName);
//            userCompany.setF_contacts(tempLinkManName);
//            userCompany.setF_mobile(tempMobile);
//            if(StringUtils.isNotBlank(tempEmail)){
//                userCompany.setF_email(StringUtils.lowerCase(tempEmail));
//            }
//            userCompany.setCreateTime(date);
//            userCompany.setUpdateTime(date);
//            userCompanyMapper.insertSelective(userCompany);
//        }else{
//            UserCompany historyCompany = user_companyList.get(0);
//            historyCompany.setF_company_name(tempCompanyName);
//            historyCompany.setF_contacts(tempLinkManName);
//            historyCompany.setF_mobile(tempMobile);
//            if(StringUtils.isNotBlank(tempEmail)){
//                historyCompany.setF_email(StringUtils.lowerCase(tempEmail));
//            }
//            historyCompany.setUpdateTime(date);
//            userCompanyMapper.updateByPrimaryKey(historyCompany);
//        }
        return AjaxResponse.success();
    }

    @Override
    public List<BuyerReg> selectWxUserBuyerInfo(Map<String, Object> paramMap) {
        List<BuyerReg> buyerRegList = buyerRegMapper.getList(paramMap);
        return buyerRegList;
    }

    @Override
    public AjaxResponse updateCertifiedMember(AuthLoginReqDto authLoginReqDto) {
        //校验会员是否存在
        Integer zzyUserId = authLoginReqDto.getZzyUserId();
        ZzyUser zzyUser = zzyuserService.selectByPrimaryKey(zzyUserId, null);
        if(Objects.isNull(zzyUser)) return AjaxResponse.failure("会员不存在");
        //校验验证码是否正确
        Integer projectId = authLoginReqDto.getProjectId();
        String username = authLoginReqDto.getUsername();
        Integer orgNum = authLoginReqDto.getOrgNum();
        String code = authLoginReqDto.getCode();
        Project project = null;
        if(projectId!=null && projectId > 0){
            project = projectService.selectByProjectId(projectId, null);
        }
        CheckLog checkLog = new CheckLog();
        checkLog.setOrgNum(orgNum);
        checkLog.setWayNumber(username);
        if(Objects.nonNull(project)){
            checkLog.setProjectId(projectId);
            checkLog.setExhibitCode(project.getF_exhibit_code());
        }
        String msg = checkLogService.checkCode(checkLog,code);
        if (StringUtils.isNotBlank(msg)) return AjaxResponse.create(-2,msg);
        //更新会员绑定手机邮箱信息
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(12);
        if(Validator.isEmail(username)){
            if(!username.equalsIgnoreCase(zzyUser.getF_email())){
                paramMap.put("fillEmail",StringUtils.lowerCase(username));
            }
            paramMap.put("certifiedEmail",true);
        }else{
            if(!username.equals(zzyUser.getF_mobile())){
                paramMap.put("fillMobile",username);
            }
            paramMap.put("certifiedMobile",true);
        }
        paramMap.put("zzyUserId",zzyUserId);
        zzyuserService.updateZzyUserCertifiedById(paramMap);
        return AjaxResponse.success();
    }

    @Override
    public AjaxResponse getMemberLoginUserInfo(AuthLoginRespVo userInfo,ProjectDto projectDto) {
        if(Objects.isNull(userInfo)) return AjaxResponse.success(userInfo);
        try {
            Boolean mobileCertified = userInfo.getMobileCertified();
            Boolean emailCertified = userInfo.getEmailCertified();
            userInfo = zzyuserService.checkZzyUserInfo(userInfo);
            //判断是否由企业会员进入
//            Boolean existEnterpriseInfo = userInfo.getExistEnterpriseInfo();
//            Integer brandProjectId = projectDto.getBrandProjectId();
            Boolean enterpriseLoginFlag = projectDto.getEnterpriseLoginFlag();
            Long currentEnterpriseId = userInfo.getCurrentEnterpriseId();

            Boolean mobileCertified1 = userInfo.getMobileCertified();
            Boolean emailCertified1 = userInfo.getEmailCertified();
            if(
                StringUtils.isNotBlank(projectDto.getAuthToken())
                &&
                (
                    (BooleanUtils.isFalse(mobileCertified) && BooleanUtils.isTrue(mobileCertified1))
                    ||
                    (BooleanUtils.isFalse(emailCertified) && BooleanUtils.isTrue(emailCertified1))
                )
            ){
                String memberRedisKey = StringUtils.substringBeforeLast(projectDto.getAuthToken(), "-");
                if(StringUtils.isNotBlank(memberRedisKey) && redisUtil.hasKey(memberRedisKey)){
                    redisUtil.hset(memberRedisKey,"userInfo", JSONUtil.toJsonStr(userInfo));
                }
            }

            Integer projectId = userInfo.getProjectId();
            if(projectDto.getProjectId()!=null && projectDto.getProjectId() > 0){
                projectId = projectDto.getProjectId();
            }
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(10);

//            if(BooleanUtils.isTrue(enterpriseLoginFlag)){
                if(ZzyUserTypeEnum.ENTERPRISE.getCode().equals(userInfo.getZzyUserType())){
                    paramMap.put("enterpriseZzyuserId",userInfo.getZzyUserId());
                }else{
                    paramMap.put("zzyUserId",userInfo.getZzyUserId());
                }
                paramMap.put("orgNum",userInfo.getFocusOrgNum());
                //paramMap.put("brandId",brandProjectId);
                List<Enterprise> enterpriseList = enterpriseMapper.selectByMap(paramMap);
                if(CollectionUtils.isNotEmpty(enterpriseList)){
                    userInfo.setExistEnterpriseInfo(true);
                }else{
                    userInfo.setExistEnterpriseInfo(false);
                }
                if(currentEnterpriseId!=null){
                    paramMap.put("currentEnterpriseId",currentEnterpriseId);
                    paramMap.put("enterpriseFocusState",1);
                    enterpriseList = enterpriseMapper.selectByMap(paramMap);
                    if(CollectionUtils.isEmpty(enterpriseList)){//说明当前会员变更了企业
                        paramMap.clear();
                        paramMap.put("zzyUserId",userInfo.getZzyUserId());
                        paramMap.put("orgNum",userInfo.getFocusOrgNum());
                        focusZzOrgMapper.updateResetEnterprise(paramMap);
                        userInfo.setCurrentEnterpriseId(null);//直接置空
                    }
                }
//            }


            if(projectId!=null && projectId > 0){
                //获取该公司客户Id
//                paramMap.put("zzyuserId",userInfo.getZzyUserId());
//                paramMap.put("orgNum",userInfo.getFocusOrgNum());
//                paramMap.put("contractProjectId",projectId);
////                        paramMap.put("projectId",projectId);
//                List<ServeBooth> serveBooths = serveBoothDao.queryServeBooth(paramMap);
//                if(CollectionUtils.isNotEmpty(serveBooths)){
//                    ServeBooth serveBooth = serveBooths.get(serveBooths.size()-1);
//                    if(Objects.nonNull(serveBooth)){
//                        userInfo.setZzorgClientId(serveBooth.getClientId());
//                        userInfo.setClientId(serveBooth.getClientId());
//                    }
//                }
                //通过项目Id查询是否存在展商或观众角色
                Integer loginProjectId = userInfo.getProjectId();
                if(projectDto.getProjectId()!=null && projectDto.getProjectId() > 0){
                    userInfo.setProjectId(projectDto.getProjectId());
                }
                userFocusProjService.queryExistZzyUserFocusProjOrInsert(userInfo);
                userInfo.setProjectId(loginProjectId);

                paramMap.clear();
                paramMap.put("queryProjectId",projectId);
                List<Integer> allProjectIdList = projectService.getExhibitionRefProjectId(paramMap);
                if(CollectionUtils.isNotEmpty(allProjectIdList)){
                    paramMap.clear();
                    paramMap.put("orgNum",userInfo.getFocusOrgNum());
                    paramMap.put("projectIdList",allProjectIdList);
                    List<Integer> projIdList = tradeProjectDao.getProjIdByProjectId(paramMap);
                    if(CollectionUtils.isNotEmpty(projIdList)){
                        paramMap.clear();
                        paramMap.put("zzyUserId",userInfo.getZzyUserId());
                        paramMap.put("fOrgNum",userInfo.getFocusOrgNum());
//                            paramMap.put("projId",projId);
//                            paramMap.put("projectId",projectId);
                        paramMap.put("projIdList",projIdList);
                        List<UserFocusProj> userProjRoleList = userFocusProjService.selectZzyUserProjRole(paramMap);
                        if(CollectionUtils.isNotEmpty(userProjRoleList)){
                            List<Integer> projRoleIdList = userProjRoleList
                                    .stream()
                                    .filter(Objects::nonNull)
                                    .map(UserFocusProj::getProjRoleId)
                                    .distinct()
                                    .collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(projRoleIdList)){
                                Collections.sort(projRoleIdList, new Comparator<Integer>() {
                                    @Override
                                    public int compare(Integer o1, Integer o2) {
                                        return o2 - o1;
                                    }
                                });
                                userInfo.setZzyUserRoleIds(projRoleIdList);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("获取会员其它信息异常：{}",e);
        }
        return AjaxResponse.success(userInfo);
    }


    @Override
    public AjaxResponse getMemberAuthToken(ApiMemberLoginTokenDto apiMemberLoginTokenDto) {
        AjaxResponse resp = apiUserService.checkByBrandId(apiMemberLoginTokenDto);
        if (resp.getState() != 1){
            addApiLog(apiMemberLoginTokenDto,resp,null,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),null);
            return resp;
        }
        ApiUser apiUser = (ApiUser) resp.getData();
        Integer zzyUserId = apiMemberLoginTokenDto.getZzyUserId();
        ZzyUser zzyUser = zzyuserService.selectByPrimaryKey(zzyUserId, null);
        if(Objects.isNull(zzyUser)){
            AjaxResponse response = AjaxResponse.create(ApiResponseStateEnum.MEMBER_NOT_EXIST.getState());
            addApiLog(apiMemberLoginTokenDto,response,apiUser,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),null);
            return response;
        }
        zzyUser.setFocusOrgNum(apiUser.getOrgNum());
        String sources = apiMemberLoginTokenDto.getSources();
        if(RegisterSourceEnum.TRADER_REG.getCode().equals(sources)){
            Integer projectId = apiMemberLoginTokenDto.getProjectId();
            Project project = projectService.getById(projectId);
            if(Objects.isNull(project)){
                AjaxResponse errResp = AjaxResponse.create(ApiResponseStateEnum.PROJECT_NOT_EXIST.getState());
                addApiLog(apiMemberLoginTokenDto,errResp,apiUser,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),zzyUser.getF_zzyuser_id());
                return errResp;
            }
            String f_exhibit_code = project.getF_exhibit_code();
            if(StringUtils.isBlank(f_exhibit_code)){
                AjaxResponse errResp = AjaxResponse.create(ApiResponseStateEnum.PROJECT_NOT_REF_EXHIBIT_CODE.getState());
                addApiLog(apiMemberLoginTokenDto,errResp,apiUser,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),zzyUser.getF_zzyuser_id());
                return errResp;
            }
            zzyUser.setF_project_id(project.getF_project_id());
            zzyUser.setF_exhibit_code(f_exhibit_code);
        }
        //构造登录token
        String clientType = StringUtils.isNotBlank(apiMemberLoginTokenDto.getLoginClientType()) ? apiMemberLoginTokenDto.getLoginClientType() : SignClientTypeEnum.PC.getCode();
        Map<String, Object> redisHashMap = Maps.newHashMapWithExpectedSize(4);
        redisHashMap.put("loginClientType",clientType);
        AjaxResponse responseResult = buildAccessTokenToRedis(null, zzyUser, redisHashMap);
        if(responseResult.getState()!=1){
            addApiLog(apiMemberLoginTokenDto,responseResult,apiUser,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),null);
            return responseResult;
        }
        ApiMemberLoginTokenInfo apiMemberLoginTokenInfo = new ApiMemberLoginTokenInfo();
        apiMemberLoginTokenInfo.setZzyUserId(zzyUser.getF_zzyuser_id());
        apiMemberLoginTokenInfo.setOrgNum(apiUser.getOrgNum());
        Map<String, Object> resultMap = (Map<String, Object>) responseResult.getData();
        Object tokenObj = resultMap.get(CommonConstant.TOKEN_NAME);
        if(RegisterSourceEnum.TRADER_REG.getCode().equals(sources)){
            String trade_reg_url = String.format(SystemBusinessUrlConstant.TRADE_REG, tokenObj,zzyUser.getF_exhibit_code(),apiUser.getOrgNum(),zzyUser.getF_project_id(),"");
            apiMemberLoginTokenInfo.setTradeRegUrl(trade_reg_url);
        }else if(RegisterSourceEnum.VIP.getCode().equals(sources)){
            String mobile_member_center_url = String.format(SystemBusinessUrlConstant.MOBILE_MEMBER_CENTER, apiUser.getOrgNum(),tokenObj);
            apiMemberLoginTokenInfo.setMobileMemberCenterUrl(mobile_member_center_url);
            String pc_member_center_url = String.format(SystemBusinessUrlConstant.PC_MEMBER_CENTER, apiUser.getOrgNum(),tokenObj);
            apiMemberLoginTokenInfo.setPcMemberCenterUrl(pc_member_center_url);
        }
        AjaxResponse successResp = AjaxResponse.success(apiMemberLoginTokenInfo);
        addApiLog(apiMemberLoginTokenDto,successResp,apiUser,ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),apiMemberLoginTokenDto.getZzyUserId());
        return successResp;
    }

    @Override
    public void addApiLog(Object objBean, AjaxResponse resp, ApiUser apiUser, String method,Integer zzyUserId) {
        try {
            String requestData = null;
            String requestContent = null;
            if(ApiMethodEnum.GET_MEMBER_LOGIN.getCode().equals(method)){
                ApiMemberLoginTokenDto apiMemberLoginTokenDto = (ApiMemberLoginTokenDto)objBean;
                requestData = JSONObject.toJSONString(apiMemberLoginTokenDto);
                if (zzyUserId != null) {
                    apiMemberLoginTokenDto.setZzyUserId(zzyUserId);
                    requestContent = JSONObject.toJSONString(apiMemberLoginTokenDto);
                }
            }
            int state = resp.getState() == 1 ? 1 : 0;
            String responseData = JSONObject.toJSONString(resp, SerializerFeature.WriteMapNullValue);
            Integer orgNum = null;
            Integer apiUserId = null;
            if (apiUser != null) {
                orgNum = apiUser.getOrgNum();
                apiUserId = apiUser.getId();
            }
            ApiLog build = ApiLog.builder()
                    .createTime(new Date())
                    .id(OnlyNumberUtil.nextId())
                    .requestData(requestData)
                    .requestContent(requestContent)
                    .state(state)
                    .responseData(responseData)
                    .orgNum(orgNum)
                    .apiUserId(apiUserId)
                    .apiType(ApiTypeEnum.MEMBER_LOGIN.getCode())
                    .method(method)
                    .build();
            apiLogService.add(build);
        } catch (Exception e) {
            logger.error("获取会员免登陆日志异常:{}",e);
        }
    }
}
