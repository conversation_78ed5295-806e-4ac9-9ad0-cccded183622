package com.eip.service.vip.biz;

import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.TaskDtlDto;
import com.eip.facade.vip.dto.api.ServeBoothDto;
import com.eip.facade.vip.entity.Task;
import com.eip.facade.vip.vo.ProjectTaskVo;

import java.util.List;
import java.util.Map;

public interface TaskBiz  {
	
	List<Task> geTask(TaskDtlDto taskDtlDto);

    List<ProjectTaskVo> selectProjectTaskById(Map<String, Object> map);

    AjaxResponse selectExhibitorProjectTask(ServeBoothDto serveBoothDto);

    Task getById(Integer taskId);

    List<Map<String, String>> getOrgNumSetTask(TaskDtlDto taskDtlDto);

}
