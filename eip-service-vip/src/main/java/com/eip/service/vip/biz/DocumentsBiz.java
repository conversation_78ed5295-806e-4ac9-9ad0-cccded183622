package com.eip.service.vip.biz;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.AccessoryDto;
import com.eip.facade.vip.entity.Documents;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface DocumentsBiz {
	
	int insert(Documents e);
	
	int update(Documents e);

	AjaxResponse saveDocumentByUploadAttach(Map<String, String> resultMap, AccessoryDto accessoryDto);

	int delete(Integer docCodeId);

	int deleteBatch(List<Documents> list);

	int count(Documents e);

	List<Documents> select(PageParam pageParam, Documents e);

	List<Documents> getListByMap(Map<String, Object> map);

	Documents selectById(Integer docCodeId);

	PageInfo<Documents> getExhibitorNoticePage(PageParam pageParam, AccessoryDto accessoryDto);
}
