//package com.eip.web.crm.job;

/**
 * 
 * 定时任务
 * 判断预定保留时间修改展位和订单状态
 *
 */
//@Slf4j
//@Component
//public class ExhibitBoothStateJob {
//
//	@Autowired
//	private BoothPreorderService boothPreorderService;

//	public void updateSaleState(){
//		try {
//			System.out.println(new Date()+"定时任务执行了···");
//			boothPreorderService.updateSaleState();
//			System.out.println(new Date()+"定时任务执行结束了···");
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
	/**
	 * 每10分钟更新展位状态
	 * //	@Scheduled(fixedDelay = 30000)
	 */
//	@Scheduled(cron = "0 */10 * * * ?")
//	public void updateBoothPreOrderAndContractState(){
//		try {
//			Date now = DateUtil.getCurrentNow();
////			log.info("---updateBoothPreOrderAndContractState---start---"+ DateUtil.format(DateUtil.YYYY_MM_DD_HH_MM_SS,now));
//			boothPreorderService.updateBoothPreOrderAndContractState();
////			log.info("---updateBoothPreOrderAndContractState---end---"+DateUtil.format(DateUtil.YYYY_MM_DD_HH_MM_SS,now));
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//}
