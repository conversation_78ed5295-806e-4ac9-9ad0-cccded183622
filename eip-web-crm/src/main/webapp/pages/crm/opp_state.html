<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商机设置</title>
    <link href="../../vue/element-ui/index.css" rel="stylesheet" type="text/css"/>
    <link href="./css/note.css" rel="stylesheet" type="text/css"/>
    <link href="./css/common.css" rel="stylesheet" type="text/css"/>
    <script src="../../vue/vue.js"></script>
    <!-- 开发环境版本，包含了有帮助的命令行警告 -->
<!--    <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>-->
    <script src="../../vue/axions.js"></script>
    <script src="../../vue/element-ui/index.js"></script>
    <script src="js/jquery.min.js" type="text/javascript" charset="utf-8"></script><!-- 下面Common.js需要-->
    <script type="text/javascript" src="common/variable.js"></script>
    <script type="text/javascript" src="./CRM_files/Common.js.下载"></script>
    <link href="../../components/dialog_mould.css" rel="stylesheet" type="text/css"/>
    <script src="../../components/dialog.js"></script>
    <style type="text/css">
        .gray {
            color: #666
        }
    </style>
<body>
<div id="app" style="height: 100%" v-cloak>
    <el-container style="height: 100%;">
        <el-aside width="240px" class="leftmemu">
            <ul>
                <li :class="{active: remind}" @click="getNoteList(1)">商机主题设置</li>
                <li :class="{active: !remind}" @click="getNoteList(2)">商机阶段设置</li>
            </ul>
        </el-aside>

        <el-container>
            <el-header id="serachKey" :style="{height: remind ? '56px' : '80px'}">
                <div class="gray">
                    <span class="key" v-if="remind">当前商机主题数量： {{total}}</span>
                    <div v-else>
                        <span style="line-height: 36px">当前商机阶段数量： {{total}}</span>
                        <div class="flex a-i-c" v-if="teamId != -1">
                            <el-radio-group v-model="enable" @change="updateState">
                                <el-radio :label="1">默认公司统一设置商机阶段</el-radio>
                                <el-radio :label="0">启用团队自定义商机阶段</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                </div>
                <el-link :underline="false" type="primary" icon="el-icon-plus" @click="add($event, 1)"
                         v-if="enable!=1 && !remind">新增商机阶段
                </el-link>
                <el-link :underline="false" type="primary" icon="el-icon-plus" @click="add($event, 1)" v-if="remind">
                    新增商机主题
                </el-link>
            </el-header>

            <el-main class="keypage">
                <div v-if="remind" style="margin-top: 20px">
                    <el-table

                            :data="tableData"
                            id="tableData"
                            ref="clientTb"
                            max-height="100%"
                            stripe
                            border
                            @selection-change="rowchange"
                            style="width: 100%;"
                    >
                        <el-table-column
                                type="selection"
                                width="55">
                        </el-table-column>
<!--						<el-table-column-->
<!--								type="index"-->
<!--								lable="序号"-->
<!--								width="60"-->
<!--						>-->
<!--							<template slot="header">-->
<!--								序号-->
<!--							</template>-->
<!--							<template  slot-scope="scope">-->
<!--								<el-link type="primary"  :underline="false" @click="check(scope.row, 1)">0{{scope.$index + 1}}</el-link>-->
<!--							</template>-->
<!--						</el-table-column>-->
                        <el-table-column
                                prop="text"
                                label="商机主题"
                                key="1"

                                resizable
                                show-overflow-tooltip
                                width="480">
                        </el-table-column>
                        <el-table-column
                                prop="order"
                                label="排序号">
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-link
                                        :underline="false"
                                        type="primary"
                                        icon="el-icon-edit"
                                        @click="edit(scope.row)"
                                        style="margin-right: 20px"
                                >修改
                                </el-link>
                                <el-link
                                        :underline="false"
                                        type="primary"
                                        icon="el-icon-delete"
                                        @click="del(scope.$index, scope.row)"
                                >删除
                                </el-link>

                            </template>
                        </el-table-column>
                    </el-table>

                </div>
                <div v-else style="margin-top: 20px">
                    <el-table

                            :data="tableData2"
                            class="tableData"
                            ref="clientTb"
                            max-height="100%"
                            stripe
                            border
                            @selection-change="rowchange"
                            style="width: 100%;"
                    >
                        <el-table-column
                                type="selection"
                                key="selection"
                                width="55">
                        </el-table-column>
<!--						<el-table-column-->
<!--								type="index"-->
<!--								lable="序号"-->
<!--								key="index"-->
<!--								width="60"-->
<!--						>-->
<!--							<template slot="header">-->
<!--								序号-->
<!--							</template>-->
<!--							<template  slot-scope="scope">-->
<!--								<el-link type="primary"  :underline="false" @click="check(scope.row, 1)">0{{scope.$index + 1}}</el-link>-->
<!--							</template>-->
<!--						</el-table-column>-->
                        <el-table-column
                                prop="f_opp_state_name"
                                key="2"
                                label="商机阶段"
                                resizable
                                show-overflow-tooltip
                                width="480">
                        </el-table-column>
                        <el-table-column
                                prop="f_key_point"
                                key="3"
                                label="关键点标识"
                                resizable
                                show-overflow-tooltip
                                width="480">
                            <template slot-scope="{row}">
                                {{row.f_key_point | keypoint}}
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="f_state_order"
                                key="4"
                                label="排序号">
                        </el-table-column>
                        <el-table-column label="操作" key="5" v-if="enable != 1">
                            <template slot-scope="scope">
                                <el-link
                                        :underline="false"
                                        type="primary"
                                        icon="el-icon-edit"
                                        @click="stedit(scope.row)"
                                        style="margin-right: 20px"
                                >修改
                                </el-link>
                                <el-link
                                        :underline="false"
                                        type="primary"
                                        icon="el-icon-delete"
                                        @click="stdel(scope.row)"
                                >删除
                                </el-link>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

            </el-main>
            <el-footer>
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :page-sizes="[10, 20, 30, 40, 50]"
                        :page-size="pagesize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total">
                </el-pagination>
            </el-footer>
        </el-container>
    </el-container>
<!--提示框-->
    <dialog-cust :title="title"
                 :visible="showHide"
                 width="424px"
                 :showHeader="true"
                 jc="h_end"
                 @open="initform"
                 :before-close="close"
                 v-on:update:visible="showHide=$event">
        <template v-slot:sloth>
            <el-button @click="saveopp()" class="saveClose"><i></i>保存</el-button>
        </template>

<!--        商机 主题 表单-->
        <el-form :model="tForm" id="mainfrom"
                 label-width="80px"
                 label-position="left"
                 size="mini"
                 ref="form"
                 v-if="remind"
                 :rules="rules"
        >
            <el-form-item label="商机主题:" prop="text">
                <el-input v-model="tForm.text" placeholder="请输入名称"></el-input>
            </el-form-item>
            <el-form-item label="排序号:">
                <el-input v-model="tForm.order"></el-input>
            </el-form-item>
        </el-form>

<!--        商机 阶段  表单-->
        <el-form :model="oppform" class="mainfrom"
                 label-width="90px"
                 label-position="left"
                 size="mini"
                 ref="oppform"
                 v-if="!remind"
                 :rules="rules"
        >
            <el-form-item label="商机阶段:" prop="f_opp_state_name"
            >
                <el-input v-model="oppform.f_opp_state_name" placeholder="请输入名称"></el-input>
            </el-form-item>
            <el-form-item label="序号:">
                <el-input v-model.number="oppform.f_state_order"></el-input>
            </el-form-item>
<!--            <el-form-item label="商机阶段id:" v-if="stagetype == 2">-->
<!--                <el-input v-model.number="oppform.f_opp_state_id" readonly></el-input>-->
<!--            </el-form-item>-->
            <el-form-item label="关键点标识">
                <el-select v-model="oppform.f_key_point" placeholder="请选择">
                    <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
    </dialog-cust>
</div>
</body>
<script type="text/javascript">
    var Axios = axios.create({
        baseURL: variableSponsor
    });
    new Vue({
        el: '#app',
        data() {
            return {
                // 新增或修改
                mode:'',
                teamId: '', // 团队ID,
                rows: [],
                enable: 1,
                options: [
                    {label: '开始保护', value: 'start_protect'},
                    {label: '退出保护', value: 'quit_protect'},
                    {label: '客户成交', value: 'make_bargain'},
                    {label: '无', value: ''}
                ],
                showHide: false,
                title: '新增商机主题',
                pickerOptions: {
                    shortcuts: [{
                        text: '今天',
                        onClick(picker) {
                            picker.$emit('pick', new Date());
                        }
                    }, {
                        text: '昨天',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', date);
                        }
                    }, {
                        text: '一周前',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', date);
                        }
                    }]
                },
                dateoption: {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                },
                tableData: [],
                tableData2: [],
                checked: false,
                remind: true,
                tForm: {
                    text: '',
                    order: ''
                },
                oppform: {
                    f_work_team_id: '',
                    f_opp_state_name: '',
                    f_opp_state_id: '', // 不传值代表新增，传值代表修改
                    f_state_order: '',
                    f_key_point: '无'
                },


                restform: {
                    text: '',
                    order: ''
                },
                restoppform: {
                    f_work_team_id: '',
                    f_opp_state_name: '',
                    f_opp_state_id: '', // 不传值代表新增，传值代表修改
                    f_state_order: '',
                    f_key_point: '无'
                },
                rules: {
                    text: [{required: true, message: '请输入商机主题名称', trigger: 'blur'}],
                    f_opp_state_name: [{required: true, message: '请输入商机阶段名称', trigger: 'blur'}]
                },
                total: '', // 总页数
                currentPage: 1, // 当前页面
                pageIndex: 1, // 默认第一页
                pagesize: 20,  // 默认每页显示数目
                type: 1, // 1 新增，2 编辑 商机主题,
                stagetype: 1, // 1.新增， 2 编辑 商机阶段
                id: 0 // 当前行ID
            }

        },
        components: {
            dialogCust: dialog_mon
        },
        methods: {
            rowchange(rows) {
                this.rows = Object.assign({}, this.rows, rows)
            },
            getNoteList(type) {
                this.remind = type == 1
                this.total = 0
                this.getData()
            },
            async updateState(v) { // 不启用到启用需要清空数据重新调用接口
                let params = new FormData()
                let params2 = new FormData()
                params2.set('f_work_team_id', this.teamId)
                params.set('f_work_team_id', this.teamId)
                params.set('defaultOppState', String(!!v))
                //下面的代码都是100%报错的没有任何效果
                /*if (v == 1) {
                    try {
                        let {data: {data}} = Axios.post('/OppState/queryIsUseByTeamId', params2)
                        if (data && data.length > 0) {
                            this.$message.info('团队下的商机有被使用过')
                        } else if (data && data.length == 0) {
                            this.$message.info('团队下的商机没有被使用过')
                        }
                    } catch (e) {
                        console.dir(e)
                    }
                }*/
                try {
                    v && await this.$confirm(
                        '是否确认切换商机阶段设置模式？ 切换后，商机的商机阶段将清空，需要对商机重新设置商机阶段！',
                        '提醒',
                        {type: 'warning'})
                }catch (e) {
                    if(e === 'cancel' || e === 'close'){
                        return this.getEnable()
                    }
                }
                Axios.post('/OppState/updateDefaultOppState', params).then(res => {
                    let {data: {state, msg}} = res
                    if (state == 1) {
                        this.$message.success('设置成功')
                        this.getData()
                        this.getEnable()
                    } else if (state == 2) {
                        this.$message.warning(msg)
                    } else {
                        this.$message.error('数据出错')
                    }
                })
            },
            add() {
                this.mode = 'add'
                this.remind ? this.type = 1 : this.stagetype = 1
                this.remind ? this.title = '新增商机主题' : this.title = '新增商机阶段'
                this.$nextTick(() => {
                    this.showHide = true
                })
            },
            // 商机主题的保存
            savetheme() {
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        let params = new FormData()
                        params.set('text', this.tForm.text)
                        // if(this.tForm.order != null && this.tForm.order != ''){
                            params.set('order', this.tForm.order)
                        // }
                        this.type === 2 ? params.set('id', this.id) : ''
                        let msg = this.type === 1 ? '新增成功' :
                            this.type === 2 ? '修改成功' : ''
                        Axios.post('/oppTheme/save', params).then(res => {
                            let {data: {state}} = res
                            if (state === 1) {
                                this.$message.success(msg)
                                this.getData()
                                this.close()
                            } else {
                                this.$message.error('数据出错')
                            }
                        }).catch(e => {
                            console.dir(e)
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });


                // let params = new FormData()
                // params.set('text', this.form.text)
                // params.set('order', this.form.order)
                // this.type === 2 ? params.set('id', this.id) : ''
                // let msg = this.type === 1 ? '新增成功' :
                //     this.type === 2 ? '修改成功' : ''
                // Axios.post('/oppTheme/save', params).then(res => {
                //     let {data: {state}} = res
                //     if (state === 1) {
                //         this.$message.success(msg)
                //         this.getData()
                //         this.close()
                //     } else {
                //         this.$message.error('数据出错')
                //     }
                // }).catch(e => {
                //     console.dir(e)
                // })
            },
            // 商机阶段的保存
            savestage() {
                this.$refs['oppform'].validate((valid) => {
                    if (valid) {
                        let params = new FormData()
                        for (let [k, v] of Object.entries(this.oppform)) {
                            if(!v)continue
                            params.set(k, v)
                        }
                        Axios.post('/OppState/saveE', params).then(res => {
                            let {data: {state, msg}} = res
                            if (state == 1) {
                                let mesg = this.stagetype == 1 ? '商机阶段新增成功' : '商机阶段修改成功'
                                this.$message.success(mesg)
                                this.getData()
                                this.showHide = false
                            } else if (state == 2) {
                                this.$message.warning(msg)
                                return
                            } else {
                                this.$message.error('数据出错')
                            }
                        }).catch(e => {
                            console.dir(e)
                        })
                    } else {
                        return false;
                    }
                });
            },
            saveopp() { //新增、修改 商机主题, 新增、修改商机阶段。数据总览下 团队ID必须为-1
                this.remind ? this.savetheme() : this.savestage()
            },
            stedit(row) {
                this.mode = 'edit'
                this.title = '修改商机阶段'
                this.stagetype = 2
                for (let k of Object.keys(this.oppform)) {
                    this.$set(this.oppform, k, row[k])
                }
                this.$nextTick(() => {
                    this.showHide = true
                })
            },
            edit(row) {
                this.mode = 'edit'
                Object.keys(this.tForm).forEach(key=>{
                    this.$set(this.tForm, key, row[key])
                })
                console.log({...(this.tForm)})
                this.title = '修改商机主题'
                this.type = 2
                this.showHide = true
                this.id = row.id
            },
            // edit({text, order, id}) {
            //     this.title = '修改商机主题'
            //     this.type = 2
            //     this.id = id
            //     this.$set(this.form, 'text', text)
            //     this.$set(this.form, 'order', order)
            //     this.$nextTick(() => {
            //         this.showHide = true
            //     })
            // },
            stdel({f_opp_state_id}) { // 商机阶段删除
                this.$confirm('此操作将删除该阶段, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let params = new FormData()
                    params.set('f_opp_state_id', f_opp_state_id)
                    Axios.post('/OppState/del', params).then(res => {
                        let {data: {state, msg}} = res
                        if (state == 1) {
                            this.$message.success('删除成功')
                            this.getData()
                        } else if (state == 2) {
                            this.$message.warning(msg)
                        } else {
                            this.$message.error('数据出错')
                        }
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            del(index, row) { // 商机主题删除
                this.$confirm('此操作将删除该主题, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let params = [{id: row.id}]
                    Axios.post('/oppTheme/deleteBatch', params).then(res => {
                        let {data: {state}} = res
                        if (state == 1) {
                            this.$message.success('删除成功')
                        } else {
                            this.$message.error('数据出错')
                        }
                        this.showHide = false
                        this.getData()
                    }).catch(e => {
                        console.dir(e)
                    })
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            addRowClass({row, rowIndex}) {
                if (row.clientId === this.clientId) {
                    return 'success-row'
                }
            },
            handleCurrentChange(page) {
                this.pageIndex = page
                this.getData()
            },
            handleSizeChange(pages) {
                this.pagesize = pages
                this.getData()
            },
            initform() {
            if(this.mode == 'add'){
              if (this.$refs['form']) {
                  this.tForm = Object.assign({}, this.tForm, this.restform)
                  this.$refs['form'].resetFields()
                  this.$refs['form'].clearValidate()
              }
              if (this.$refs['oppform']) {
                  this.oppform = Object.assign({}, this.oppform, this.restoppform)
                  this.$set(this.oppform, 'f_work_team_id', this.teamId)
                  this.$refs['oppform'].resetFields()
                  this.$refs['oppform'].clearValidate()
              }
             }
            },
            close() {
                this.$nextTick(function () {
                    this.showHide = false
                })
            },
            autoindex(index) {
                return '0' + (index + 1)
            },

            async getEnable() {
                let params = new FormData()
                params.set('f_work_team_id', this.teamId)
                let {data: {data}} = await Axios.post('/OppState/queryDefaultOppState', params);
                this.enable = Number(data)
            },
            getData() {
                let params = new FormData()
                params.set('page', this.pageIndex);
                params.set('rows', this.pagesize)
                this.remind ? '' : params.set('f_work_team_id', this.teamId)
                console.dir(this.remind)
                if (this.remind) {
                    Axios.post('/oppTheme/getPage', params).then(res => {
                        let {data: {rows, total}} = res
                        this.total = total
                        this.tableData = rows
                        console.log(this.total, this.tableData)
                    }).catch(e => {
                        console.dir(e)
                    })
                } else {
                    Axios.post('/OppState/getList', params).then(res => {
                        let {data: {rows, total}} = res
                        this.total = total
                        this.tableData2 = rows
                    }).catch(e => {
                        console.dir(e)
                    })
                }
            }
        },
        created() {
            let url = (new URL(window.location.href)).searchParams
            this.teamId = url.get('teamId')
            this.menuId = url.get('menuId')
            this.$set(this.oppform, 'f_work_team_id', this.teamId)
            this.getEnable()
            this.getData()

        },
        filters: {
            keypoint(data) {
                switch (data) {
                    case 'start_protect':
                        return '开始保护'
                    case 'make_bargain':
                        return '客户成交'
                    case 'quit_protect':
                        return '退出保护'
                    default:
                        return ''
                }
            },
            dateFormat(date, date2) {
                console.dir(date)
                return date.toLocaleDateString('zh-Hans-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                }).replace(/\//g, '-') == date2
            }
        },
    })
</script>
</html>
