;(function (Vue) {
  const template = `<custom-dialog
      :show="show"
      @before-close="close"
      append2body
      title="产品类别"
      width="800px">
    <el-main slot="body" class="dialog-body">
      <el-header class="dialog-header" height="51px">
        <div class="dialog-header-left">
          <span>关键字查询</span>
          <el-input size="mini" v-model="searchForm.f_type_name" clearable style="flex-basis: 200px"
                    v-enter="requestTableData"></el-input>
          <el-button size="mini" @click="searchTable">查询</el-button>
        </div>
        <div class="dialog-header-right">
          <el-button
              size="mini"
              icon="el-icon-circle-check"
              type="primary"
              @click="confirmProduct()">
            确定
          </el-button>
        </div>
      </el-header>
      <el-main class="product-category-selector-content">
        <div class="left">
          <el-tree
              style="background: none;"
              default-expand-all
              :data="productTree"
              :expand-on-click-node="false"
              :props="defaultProps"
              @node-click="handleNodeClick">
            <span slot-scope="{ node, data }">
              <span :class="node.isLeaf ? 'el-icon-document' : 'el-icon-folder-opened'">{{node.label}}</span>
            </span>
          </el-tree>
        </div>
        <div class="right">
          <el-table
              class="prod-details"
              border
              max-height="320px"
              @row-click="rowClick"
              @selection-change="onSelectionChange"
              size="small"
              ref="table"
              :data="tableData"
              style="width: 100%">
            <el-table-column
                width="40"
                type="index">
            </el-table-column>
            <el-table-column
                width="45"
                type="selection">
            </el-table-column>
            <el-table-column
                prop="f_type_name"
                label="产品类别">
            </el-table-column>
          </el-table>
          <el-footer height="40px" class="page-footer">
            <el-pagination
                @size-change="pageSizeChange"
                @current-change="pageChange"
                :current-page="searchForm.page"
                :page-sizes="[20, 50, 100, 200, 1000]"
                :page-size="searchForm.rows"
                layout="total, sizes, prev, pager, next"
                :total="total">
            </el-pagination>
          </el-footer>
        </div>
      </el-main>
    </el-main>
  </custom-dialog>`

  const ProductCategorySelector = {
    template,
    name: "ProductCategorySelector",
    props: {
      allowEmpty: Boolean
    },
    data() {
      return {
        show: false,
        productTree: [],
        tableData: [],
        defaultProps: {
          children: 'children',
          label: 'text'
        },
        searchForm: {
          f_type_name: '',
          parentTradeId: '',
          workTeamId: window.teamId || window.top.teamId,
          page: 1,
          rows: 20,
        },
        total: 0,
        selected: [],
      }
    },
    methods: {
      //支持多选和双击
      confirmProduct() {
        const selected = this.selected.map(item => ({...item}))
        this.tableData.forEach(item => {
          const index = selected.findIndex(_item => _item['f_product_type_id'] === item['f_product_type_id'])
          if (index !== -1) selected.splice(index, 1)
        })
        this.$refs['table'].selection.forEach(item => selected.push({...item}))
        // if (!selected.length)
        //   return this.$errorMsg('最少选择一条明细！', '提示', 'warning')
        this.$emit('selected', selected)
        this.show = false
      },
      open(selected) {
        this.requestTableData()
        this.show = true
        this.selected = selected || []
      },
      close() {
        this.show = false
        this.$emit('cancel')
      },
      requestTreeData() {
        const workTeamId = this.searchForm.workTeamId
        Axios.post('/tradeClass/getTree', JSON2FormData({workTeamId})).then(({data}) => {
          this.productTree = data
        }).catch(e => {
          console.warn(e)
          this.$errorMsg('产品类别加载失败！')
        })
      },
      requestTableData() {
        const queryData = {}
        Object.keys(this.searchForm).forEach(key => {
          if (!this.searchForm[key]) return
          queryData[key] = this.searchForm[key]
        })
        Axios.post('/productTypeBase/getList', JSON2FormData(queryData)).then(({data}) => {
          if (this.allowEmpty) {
            data.rows.unshift({f_trade_name: '', f_type_name: '-为空-', f_product_type_id: -1})
          }
          this.tableData = data.rows
          this.total = data.total
          this.updateSelected()
        }).catch(e => {
          console.warn(e)
          this.$errorMsg('产品类别加载失败！')
        })
      },
      searchTable() {
        this.$set(this.searchForm, 'parentTradeId', '')
        this.requestTableData()
      },
      handleNodeClick(data) {
        this.$set(this.searchForm, 'parentTradeId', data.tradeId)
        this.$set(this.searchForm, 'f_type_name', '')
        this.requestTableData()
      },
      pageSizeChange(val) {
        this.searchForm.page = 1
        this.searchForm.rows = val
        this.requestTableData()
      },
      pageChange(val) {
        this.searchForm.page = val
        this.requestTableData()
      },
      rowClick(row) {
        this.$refs['table'].toggleRowSelection(row)
      },
      updateSelected() {
        this.$refs['table'].clearSelection()
        this.selected.forEach(item => {
          const index = this.tableData.findIndex(_item => {
            return _item['f_product_type_id'] === item['f_product_type_id']
          })
          if (index === -1) return
          this.$nextTick(() => {
            this.$refs['table'].toggleRowSelection(this.tableData[index], true)
          })
        })
      },
      onSelectionChange(selection) {
        if (!this.allowEmpty) return
        if (!selection.length) return
        const [first] = selection
        const [last] = selection.slice(-1)
        if (first.f_product_type_id === -1) {
          if (selection.length > 1) {
            this.$refs['table'].toggleRowSelection(first, false)
          }
        } else if (last.f_product_type_id === -1) {
          this.$refs['table'].clearSelection()
          this.$refs['table'].toggleRowSelection(last, true)
        }
      }
    },
    mounted() {
      this.requestTreeData()
    },
    components: {
      CustomDialog
    }
  }
  Vue.component('product-category-selector', ProductCategorySelector)
})(Vue)