.dialog-body {
    padding: 0;
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #fff;
}

.dialog-header-left {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 360px;
}

.product-category-selector-content {
    width: 100%;
    display: flex;
    height: 360px;
    padding: 0;
    overflow: hidden;
}

.product-category-selector-content .left {
    flex-basis: 200px;
    flex-shrink: 0;
    background-color: #fafcff;
    overflow: auto;
}

.product-category-selector-content .right {
    flex-basis: calc(100% - 210px);
}

.prod-details .el-table__header th {
    background-color: #f5f5f5;
    color: #858585 !important;
}

.prod-details .el-table__row tr {
    padding: 5px 0;
}

.page-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    border: 1px solid #eee;
    border-top: none;
}