;!(function () {
  const template = `
    <custom-dialog :show="show" append2body @before-close="close" :title="title" width="800px" class="select-dialog">
      <el-main slot="body">
        <el-header class="select-header" height="33px">
          <el-alert
            class="header-msg"
            :closable="false"
            :title="currentKey in dataMap && dataMap[currentKey].msg"
            type="warning"
            show-icon>
          </el-alert>
          <el-button size="mini"
                     v-if="currentKey !== 'self'"
                     :icon="currentKey in dataMap && dataMap[currentKey].btnIcon"
                     plain
                     type="info"
                     @click="headerBtnClick">
            {{currentKey in dataMap && dataMap[currentKey].btnText}}
          </el-button>
        </el-header>
        <el-container>
          <el-table
            row-key="clientId"
            ref="clientTable"
            :data="tableData"
            highlight-current-row
            @select="checkboxSelect"
            @select-all="selectAll"
            @row-click="rowClick"
            @row-dblclick="rowDbClick"
            height="220"
            stripe
            border
            style="width: 100%">
            <el-table-column
              :key="'selection' + Math.random()"
              v-if="currentKey!=='self'"
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              label="序号"
              :key="'序号' + Math.random()"
              type="index"
              width="60"
              :index="i=>i + 1">
            </el-table-column>
            <el-table-column
              prop="clientId"
              label="客户ID"
              :key="'客户ID' + Math.random()"
              width="80">
              <template  slot-scope="{row}">
                <span v-if="!dataMap[currentKey].tableRowClick">{{row.clientId}}</span>
                <el-link v-else type="primary" @click="openLink(row)" :underline="false" >{{row.clientId}}</el-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="companyName"
              label="客户名称"
              :key="'客户名称' + Math.random()"
              :column-key="String(Math.random())"
              :width="['other', 'search'].includes(currentKey) ? 484 : 220"
            >
              <template slot-scope="{row}">
                <span v-if="!dataMap[currentKey].tableRowClick">{{row.companyName}}</span>
                <el-link v-else type="primary" @click="openLink(row)" :underline="false" >{{row.companyName}}</el-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="isClientAlias"
              prop="clientAlias"
              label="公司别名"
              :key="'公司别名' + Math.random()"
              :column-key="String(Math.random())"
              :width="['other', 'search'].includes(currentKey) ? 484 : 220"
            ></el-table-column>
            <el-table-column
              label="审核状态"
              :key="'审核状态' + Math.random()"
              width="80">
              <template  slot-scope="{row}">
                <span>{{row.isChecked ? '已审核' : '未审核'}}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!['other', 'search'].includes(currentKey)"
              :key="'业务员' + Math.random()"
              prop="salesmanName"
              label="业务员"
              width="80">
            </el-table-column>
            <el-table-column
              v-if="!['other', 'search'].includes(currentKey)"
              :key="'授权状态' + Math.random()"
              label="授权状态">
              <template  slot-scope="{row}">
                <span v-html="AuthStatus(row)" class="auth-state"></span>
              </template>
            </el-table-column>
          </el-table>
        </el-container>
        <el-footer height="40">
          <el-pagination
            style="text-align: center"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[20, 50, 100, 200, 1000]"
            :page-size="pageSize"
            layout="total, prev, pager, next, jumper"
            :total="tableData.length">
          </el-pagination>
        </el-footer>
      </el-main>
    </custom-dialog>`;
  const getAuthSate = ()=>{
    if(getAuthority('fu_company_add_mon')) return {state: 3, text:'独占'}; //独占权限
    if(getAuthority('fu_company_add_rec')) return {state: 1, text:'领用'}; //领用权限
    return {state: 0, text:'公共客户'};
  };
  const operationSuccess = (row, text)=>{
    loadHtmlPage('client_add_hint');
    $("#client_add_hint_id").val(row.clientId);
    $("#client_add_hint_name").val(row.companyName);
    $("#client_add_hint_msg_type").text(text + '成功');
    $('#client_add_hint').window('open');
    const postData = {};
    postData['isResultFind'] = 1;
    postData['last'] = true;
    loadTable(postData);
  }
  const funNone = _=>_;
  window.ClientRepeatCheck = {
    template,
    data(){
      return{
        show:false,
        currentKey:'',
        tableData:'',
        dataMap:{},
        server_current_time:'',
        pageSize: 20,
        currentPage: 1,
        cltName: '',
        isClientAlias: false,
        title: ''
      }
    },
    created(){
      try {
        this.dataMap = {
          self:{
            msg: '当前新增的客户在自己名下',      //消息
            btnText: '关闭',                  //按钮文字
            btnIcon: 'el-icon-circle-close',  //按钮图标
            btnClick(rows){                      //按钮点击事件
              this.$message({message:'当前新增的客户已经在自己名下！', showClose: true, type:'warning'});
              //关闭弹窗
              this.hide();
              // close_add()
            },
            tableRowClick: true,             //数据表格时候可以点击
          },
          team:{
            msg: '当前新增的客户被团队其他成员领用或独占，请选择要申请共享的一条',      //消息
            btnText: '申请共享',                  //按钮文字
            btnIcon: 'el-icon-chat-line-round',  //按钮图标
            btnClick(rows){                      //按钮点击事件
              if(rows.length !== 1){
                this.$error('请选择要申请共享的一条',funNone,'警告','warning');
                return null;
              }
              let row = rows[0];
              //申请共享请求接口
              const postData = {
                fClientId: row.clientId,
                fWorkteamId: teamId,
                salesmanId: row.salesmanId,
                cltName: row.cltName
              };
              Axios.post('/awoke/addShareAwoke', JSON2FormData(postData)).then(({data:res})=>{
                if(res.state !== 1){
                  this.$error('数据加载失败！',()=>this.hide());
                  return
                }
                this.$message({message:`向${row.salesmanName}发送申请共享成功！`, showClose: true, type:'success'});
                this.hide();
              }).catch(e=>this.$error('数据加载失败', ()=>this.hide()));
            },
            tableRowClick: getAuthority('fu_team_data_kywy'),             //数据表格时候可以点击
          },
          public:{
            msg: '当前新增的客户已在公共客户中，请选择一条至自己名下',      //消息
            btnText: '确定',                  //按钮文字
            btnIcon: 'el-icon-circle-check',  //按钮图标
            btnClick(rows){                      //按钮点击事件
              if(rows.length !== 1){
                this.$error('请选择要引用的一条', funNone, '提示', "warning");
                return null;
              }
              let row = rows[0];
              //请求接口
              let auth = getAuthSate();
              const postData = {
                fClientId: row.clientId,
                fWorkteamId: teamId,
                fClientTypeCode:row.clientTypeCode,
                authStatus: auth.state,
                operEntrance: '公司客户列表处理重复客户'
              };
              Axios.post('/client/repeatClientAuth', JSON2FormData(postData)).then(({data:res})=>{
                if(res.state === 1){ //正常的保存
                  this.hide();
                  // this.$message.success(auth.text + '成功！');
                  operationSuccess(row, auth.text)
                  return
                }
                this.$error(res.msg, ()=>this.hide());
              }).catch(e=>this.$error('数据加载失败', ()=>this.hide()));
            },
            tableRowClick: getAuthority('fu_team_data_kywy'),             //数据表格时候可以点击
          },
          other:{
            msg: '当前新增的客户在公司数据总览中已存在，请选择要引用的一条',      //消息
            btnText: '确定',                  //按钮文字
            btnIcon: 'el-icon-circle-check',  //按钮图标
            btnClick(rows,authState){                      //按钮点击事件
              if(rows.length !== 1){
                this.$error('请选择要引用的一条', undefined, undefined, "warning");
                return null;
              }
              let row = rows[0];
              let auth = getAuthSate();
              //请求接口
              const postData = {
                fClientId: row.clientId,
                fWorkteamId: teamId,
                fClientTypeCode:row.clientTypeCode,
                authStatus:authState === undefined ? auth.state : authState,
                operEntrance: '公司客户列表处理重复客户'
              };
              Axios.post('/client/repeatClientAuth', JSON2FormData(postData)).then(({data:res})=>{
                if(res.state === 1){ //正常的保存
                  this.hide();
                  if(this.currentKey === 'other'){
                    authState === undefined ?
                      operationSuccess(row, auth.text) :
                      this.$message({message:'已成功添加到公共客户！', showClose: true, type:'success'});
                  }else if(this.currentKey === 'search'){ // 和下面的功能一样，只是这里有一点区别，直接就调用这个了
                    this.$message({message:'引用成功！', showClose: true, type:'success'})
                    const postData = {};
                    postData['isResultFind'] = 1;
                    postData['last'] = true;
                    postData['callback'] = this.openLink.bind(this, row)
                    loadTable(postData);
                  }
                }
                else if(res.state === 2){ //领用（或者独占）客户数量已达到最大值
                  this.changVModalZIndex(22000,()=>{
                    this.$confirm(auth.text + '的客户数量已达到最大值,是否放到公共客户?', '提示', {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'warning'
                    }).then(() => {
                      arguments.callee.call(this,rows, 0); //再调一次自己
                    }).catch(() => {
                      this.$message({message:'已取消！', showClose: true});
                      //关闭弹窗
                      this.hide();
                    });
                  })
                }
                else this.$error('错误信息:' + res.msg, ()=>this.hide());
              }).catch(e=>this.$error('数据加载失败', ()=>this.hide()));
            },
            tableRowClick: getAuthority('fu_team_data_kywy'),             //数据表格时候可以点击
          }
        };
        this.$set(
          this.dataMap, 'search',
          Object.assign(
            {},
            this.dataMap.other,
            { msg: '当前检索的客户在数据总览中匹配到的结果如下，请选择要引用的一条' })
        )
      }
      catch (e) {}
    },
    methods:{
      changVModalZIndex(zIndex, cb=funNone){
        cb();
        setTimeout(()=>$('.v-modal').css('z-index', zIndex),200);
      },
      $error(message, alertCallBack=funNone,title='错误', type='error'){
        this.changVModalZIndex(22000,()=>{
          this.$alert(message, title, {type}).then(c=>alertCallBack(true)).catch(e=>alertCallBack(false))
        })
      },
      $checkRepeat(cltName,callback, isClientAlias=false){
        this.cltName = cltName;
        this.isClientAlias = isClientAlias;
        this.requestTableData(null, callback)
      },
      headerBtnClick(){
        this.dataMap[this.currentKey].btnClick.call(this, this.$refs['clientTable'].selection)
      },
      handleCurrentChange(currentPage) {
        this.currentPage = currentPage;
        this.requestTableData()
      },
      handleSizeChange(val) {
        this.pagesize = val;
        this.requestTableData()
      },
      requestTableData(_t='pageChange',cb=funNone){
        const postData = {
          fWorkteamId:teamId,
          cltName: this.cltName,
          page: this.currentPage,
          rows: this.pageSize
        }
        $.messager.progress()
        Axios.post('/client/queryIsExistCltNameWithTeam', JSON2FormData(postData)).then(({data:res})=>{
          if(res.state !== 1){
            this.$error(res.msg);
            return
          }
          cb(res);
          if(!res.rows.length || !res.data)return;//先判断有没有重复
          this.tableData  = res.rows;
          this.currentKey = Object.keys(this.dataMap)[res.data - 1];
          this.server_current_time = formatterDate(getSysTime());
          if(_t === 'pageChange')return; //如果是分页操作到此为止
          this.display();
        }).catch(()=>this.$error('数据加载失败！')).finally(() => $.messager.progress('close'));
      },
      selectAll(){
        this.$refs['clientTable'].clearSelection();
      },
      rowClick(row){
        this.$refs['clientTable'].clearSelection();
        this.$refs['clientTable'].toggleRowSelection(row, true);
        //this.$refs['clientTable'].selection 获取表格选中行;
      },
      rowDbClick(row){
        this.dataMap[this.currentKey].btnClick.call(this, [row]) //[row] 为了数据类型统一
      },
      checkboxSelect(selection,row){
        this.$refs['clientTable'].clearSelection();
        this.$refs['clientTable'].toggleRowSelection(row, true);
      },
      hide(){
        this.changVModalZIndex(2000,()=>{
          this.show=false;
          $('#saveBtn').attr('disabled', false);
          close_add()
        });
      },
      close(){
        this.show=false;
        //初始化清除值
        // for (let i = 1; i <= 5; i++) setEditInfoClear(`#client-add-panel-${i} ul`);
        // setEditInfoClear(`#client-add-panel-1 ul`); //清楚基本信息就可以了
        $('#saveBtn').attr('disabled', false);
        // $('body').autoFocus('#companyNameMial', 'easyUI');
      },
      display(isSearch){
        this.show=true;
        this.title = isSearch ? '快速检索' : '客户重复'
        this.changVModalZIndex(12000,()=>{
          this.$refs['clientTable'] && this.$refs['clientTable'].doLayout()
        })
      },
      openLink(row){
        openNewTab(true,row.clientId,row.companyName, this.$error)
      },
      async openQuickSearchQuote(companyName){
        const loading = this.$loading({
          fullscreen: !0,
          text: '检索中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        try {
          const postData = { workTeamId: teamId,companyName }
          const { data: {rows, state} } = await Axios.post('/client/queryByCompanyNameNotTeam', JSON2FormData(postData))
          if(state !== 1 || !rows.length) await Promise.reject('cancel')
          this.tableData  = rows
          this.currentKey = 'search'
          this.display(!0)
          this.server_current_time = formatterDate(getSysTime())
        }catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }finally {
          loading && loading.close()
        }
      }
    },
    computed:{
      AuthStatus(){
        return row => {
          let html = '';
          let imgSrc = '';
          let icon_src = ['img/bench(4).png','img/bench(5).png','img/bench(3).png'];
          if(row.authStatus === 1) //表示领用
            imgSrc = icon_src[0]
          else if(row.authStatus === 3) //表示独占
            imgSrc = icon_src[1]

          switch (this.currentKey) {
            case "self":
              if(Number(getCookie('operatorId')) !== row.salesmanId)
                return `<img src="${icon_src[2]}" alt=""/><span>共享</span>`
              else{
                html = `<img src="${imgSrc}" alt=""/>`
                if (row.protectionTime==null||row.protectionDay==null)return html;
                let number = dateDays(this.server_current_time, getNewDay(row.protectionTime,row.protectionDay));
                html += `<span>${isNaN(number) ? 'Error': number}天</span>`
                return html;
              }
            case "team":
              html = `<img src="${imgSrc}" alt=""/>`
              if (row.protectionTime==null||row.protectionDay==null)return html;
              let number = dateDays(this.server_current_time, getNewDay(row.protectionTime,row.protectionDay));
              html += `<span>${isNaN(number) ? 'Error': number}天</span>`
              return html;
            case "public":
              return '<span>公共客户</span>'
            //case "other":break; 不显示
          }
        }
      }
    },
    components:{
      customDialog
    }
  }
})();