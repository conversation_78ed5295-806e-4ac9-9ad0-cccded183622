;(function (Vue) {
  const TAG_TYPE_LIST = [
    {id: 1, text: '个人标签'},
    {id: 2, text: '团队标签'},
    {id: 3, text: '总览标签'}
  ]
  // 标签管理
  const template = `
  <custom-dialog
    :show="show"
    title="标签管理"
    width="1000px"
    @before-close="close"
    append2body
    id="TagsManager"
  >
    <el-main slot="body" class="dialog-body">
      <el-form inline class="search-form" :model="searchForm" label-width="70px" size="mini">
        <el-form-item label="标签类型" prop="type">
          <el-select v-model="searchForm.type" clearable filterable placeholder="标签类型">
            <el-option v-for="item in TAG_TYPE_LIST" :label="item.text" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签名称" prop="keyWord">
          <el-input @keyup.enter.native="getManagerTags" clearable v-model="searchForm.keyWord" placeholder="标签名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="getManagerTags">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="table-batch">
        <el-button type="primary" size="small" @click="openEditor('add')">新增标签</el-button>
      </div>
      <el-table
        class="tags-table"
        border
        ref="table"
        size="small"
        :data="manageTable"
        style="width: 100%">
      <el-table-column
          width="40"
          type="index">
      </el-table-column>
      <el-table-column
          width="45"
          type="selection">
      </el-table-column>
      <el-table-column
          prop="favoriteName"
          label="标签名称">
      </el-table-column>
      <el-table-column
          prop="tagType"
          label="标签类型">
        <template slot-scope="{row, $index}">
          <span>{{(TAG_TYPE_LIST.find(item => item.id === row.tagType) || {}).text || ''}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="expire"
          label="标签清除时间">
        <template slot-scope="{row, $index}">
          {{row.expire === -1 ? '不清除' : ((row.expire || 0) + '日后清除')}}
        </template>
      </el-table-column>
      <el-table-column
          width="150"
          prop="sharedName"
          label="共享给">
          <template slot-scope="{row, $index}">
            {{Object.values(row.shareTo||{}).toString()}}
          </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          label="创建时间">
      </el-table-column>
      <el-table-column
          prop="empName"
          label="创建人">
      </el-table-column>
      <el-table-column
          prop="clientNum"
          label="数据数量">
        <template slot-scope="{row}" v-if="row.clientNum">
          <el-button type="text" @click="openTagData(row)">{{row.clientNum || 0}}</el-button>
        </template>
      </el-table-column>
       <el-table-column label="操作" width="210">
        <template slot-scope="{row, $index}">
          <el-button v-if="isAllow(row)" type="text" size="mini" @click="openEditor('edit', row)">编辑</el-button>
          <el-button v-if="isAllow(row)" type="text" size="mini" @click="deleteTag(row.id)">删除</el-button>
          <el-button v-if="isAllow(row)" type="text" size="mini" @click="clearTagData(row.id)">清除数据</el-button>
          <el-button v-if="row.tagType === 1" @click="shareTag(row)" type="text" size="mini">分享</el-button>
        </template>
      </el-table-column>
    </el-table>
      <custom-dialog 
        append2body 
        :show="showEditor"
        :title="editorTitle"
        width="480px"
        @before-close="closeEditor">
        <div slot="body" class="dialog-body">
          <el-header class="dialog-header" height="auto">
            <div class="dialog-header-left">
              <span>基本信息</span>
            </div>
            <div class="dialog-header-right">
              <el-button
                size="mini"
                icon="el-icon-circle-check"
                type="primary"
                :loading="saving"
                @click="saveTag">
                保存{{saving ? '中' : ''}}
              </el-button>
            </div>
          </el-header>
          <el-form 
            class="tForm" 
            style="padding: 20px 40px" 
            :rules="rules" 
            ref="tForm" 
            :model="tForm" 
            label-width="100px" 
            size="mini">
            <el-form-item label="标签名称" prop="favoriteName">
              <el-input clearable v-model="tForm.favoriteName" placeholder="标签名称"></el-input>
            </el-form-item>
            <el-form-item label="标签类型" prop="tagType">
              <el-radio-group v-model="tForm.tagType" @change="tagTypeChange">
                <el-radio :key="item.id" :label="item.id" v-for="item in editorTagType">{{item.text}}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="标签清除时间" prop="expire">
              <el-radio-group v-model="tForm.expire">
                <el-radio :label="-1">永不清除</el-radio>
                <el-radio :label="-2">定时清除</el-radio>
              </el-radio-group>
              <div class="expire-days-wrapper" v-if="tForm.expire === -2">
                <el-autocomplete
                  v-model="tForm.expireDays"
                  @blur="blurExpireDays"
                  :fetch-suggestions="getSuggestionsExpireDays"
                  placeholder="标签清除时间"></el-autocomplete>
                <span>天内清除</span>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </custom-dialog>
      <custom-dialog 
        v-if="false"
        append2body 
        :show="showShareEditor"
        title="分享标签"
        width="480px"
        @before-close="showShareEditor=false">
        <div slot="body" class="dialog-body">
          <el-header class="dialog-header" height="auto">
            <div class="dialog-header-left">基本信息</div>
            <div class="dialog-header-right"></div>
          </el-header>
          <el-form 
            style="padding: 20px 40px" 
            label-width="80px" 
            size="mini">
            <el-form-item label="分享给">
              <el-select @remove-tag="" multiple v-model="shareTags" placeholder="分享给" class="hide-popper-select" popper-class="hide-popper">
                <el-option :key="item.id" :label="item.text" :value="item.id" v-for="item in shareOptionsTags"></el-option>
              </el-select>
              <el-button size="normal" type="text" @click="addShareOperator" :underline="false" icon="el-icon-circle-plus-outline">添加分享业务员</el-button>
            </el-form-item>
          </el-form>
        </div>
      </custom-dialog>
      <custom-dialog
        append2body
        :show="showOperator"
        @before-close="showOperator=false"
        title="选择要分享标签的职员"
        width="550px">
      <div slot="body" style="max-height: 600px">
        <el-header height="45px" class="top-header">
          <el-form inline size="mini" @submit.native.prevent style="display: flex;align-items: center;justify-content: space-between;width: 100%;margin-top: 12px;">
            <div>
              <el-form-item label="关键字检索">
                <el-input
                  v-focus
                  v-model="operatorKeyword"
                  clearable
                  @keyup.native.stop.enter="getOperatorList()"
                  placeholder="职员姓名或登录名"
                  style="width: 220px;"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="getOperatorList()">检索</el-button>
              </el-form-item>
            </div>
            <el-form-item style="margin-right: 0">
              <el-button
                  type="primary"
                  icon="el-icon-circle-check"
                  @click="confirmShare"
                  :loading="saving">{{ saving ? '保存中...' : '确定' }}</el-button>
            </el-form-item>
          </el-form>
        </el-header>
        <el-table
            size="mini"
            height="500px"
            :data="operatorList"
            ref="transferTable"
            element-loading-text="数据加载中"
            element-loading-spinner="el-icon-loading"
            v-loading="operatorLoading"
            stripe
            border
            @select-all="$refs.transferTable.clearSelection()"
            @select="(_, r) => {
              $refs.transferTable.clearSelection();
              !shareTags.includes(r.operatorId) && $refs.transferTable.toggleRowSelection(r, true);
            }"
            @row-click="r => {
              $refs.transferTable.clearSelection();
              !shareTags.includes(r.operatorId) && $refs.transferTable.toggleRowSelection(r, true);
            }"
        >
          <el-table-column
              type="selection"
              fixed="left"
              :selectable="selectableHandler"
              width="49">
          </el-table-column>
          <el-table-column
              prop="operatorId"
              label="操作员编号"
              width="165"
              resizable
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="employeeName"
              label="职员姓名"
              width="165"
              resizable
              show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
              prop="operName"
              label="登录名"
              width="165"
              resizable
              show-overflow-tooltip
          >
          </el-table-column>
        </el-table>
      </div>
    </custom-dialog>
    </el-main>
  </custom-dialog>
  `
  const TagsManager = {
    template,
    name: 'TagsManager',
    props: {
      clientTypeCode: {
        type: String,
        default: 'B_CLIENT'
      }
    },
    mixins: [Mixins.clearAndInitForm()],
    data() {
      return {
        show: false,
        TAG_TYPE_LIST,
        searchForm: {
          type: '',
          keyWord: ''
        },
        manageTable: [],
        showEditor: false,
        editorMode: 'add',
        saving: false,
        tForm: {
          favoriteName: '',
          id: '',
          tagType: '',
          expire: '',
          expireDays: '',
        },
        rules: {
          favoriteName: [{required: true, message: '请输入标签名称', trigger: 'blur'}],
        },
        teamId: window.teamId || top.teamId,
        showShareEditor: false,
        currentTagId: '',
        showOperator: false,
        operatorLoading: false,
        operatorKeyword: '',
        operatorList: [],
        shareTags: [],
        shareOptionsTags: [],
      }
    },
    async mounted() {
      await this.getOperatorList(true)
    },
    methods: {
      /**
       * @param options {{}|string=}
       * */
      loading(options) {
        options = options || '拼命加载中...'
        const baseOptions = {
          fullscreen: !0,
          text: '',
          lock: !0,
          body: !0,
          background: 'rgba(0, 0, 0, 0.3)'
        }
        return this.$loading(
          typeof options === 'string' ?
            Object.assign(baseOptions, {text: options}) :
            Object.assign(baseOptions, options)
        )
      },
      async getManagerTags() {
        const loading = this.loading()
        try {
          const post = {
            clientTypeCode: this.clientTypeCode,
            teamId: this.teamId,
            ...this.searchForm
          }
          if (post.type === 2) post.workTeamId = post.teamId
          const {data: {state, data}} = await Axios.post('/tags/selectMana', JSON2FormData(post))
          if (state !== 1) await Promise.reject('获取标签列表失败')
          this.manageTable = data
        }catch (e) {
          this.$errorMsg(e || '获取标签列表失败')
        } finally {
          loading && loading.close()
        }
      },
      async open() {
        this.show = true
        await this.getManagerTags()
      },
      close() {
        this.show = false
        this.$emit('refresh')
      },
      openEditor(mode, row) {
        this.editorMode = mode
        this.showEditor = true
        this.clearAll()
        if (row) {
          const _row = {}
          Object.keys(this.tForm).forEach(item => _row[item] = row[item])
          if (_row.expire !== -1) {
            _row.expireDays = _row.expire
            _row.expire = -2
          }
          row = _row
        }
        this._initForm(row || {
          tagType: 1,
          expire: -2,
          expireDays: 1
        })
      },
      closeEditor() {
        this.showEditor = false
      },
      async saveTag() {
        try {
          const post = {...this.tForm}
          post.expire = post.expire === -2 ? post.expireDays : post.expire
          post.clientTypeCode = this.clientTypeCode
          post.workTeamId = this.teamId
          this.saving = true
          await this.validateForm()
          if (post.expire !== -2 && post.expireDays <= 0) await Promise.reject('标签清除时间必须大于0')
          delete post.expireDays
          const {data: {state, msg}} = await Axios.post('/tags/saveTag', JSON2FormData(post))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('保存成功')
          await this.getManagerTags()
          this.closeEditor()
        }catch (e) {
          if (this.handleErrors(e)) {
            this.$errorMsg(e || '保存失败')
          }
        } finally {
          this.saving = false
        }
      },
      async deleteTag(tagId) {
        const loading = this.loading()
        try {
          await this.$confirm('确定删除此标签吗？', '提醒', {type: 'warning'})
          const {data: {state, msg}} = await Axios.post('/tags/deleteTag', JSON2FormData({tagId, workTeamId: this.teamId}))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('删除成功')
          await this.getManagerTags()
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '删除失败')
        } finally {
          loading.close()
        }
      },
      async clearTagData(favoritesId) {
        const loading = this.loading()
        try {
          await this.$confirm('确定清空此标签全部数据吗？', '提醒', {type: 'warning'})
          const {data: {state, msg}} = await Axios.post('/tags/clearClient', JSON2FormData({favoritesId}))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('清除成功')
          await this.getManagerTags()
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '清除失败')
        } finally {
          loading.close()
        }
      },
      getSuggestionsExpireDays(queryString, cb) {
        const {expire} = this.tForm
        if (expire !== -2) return cb([])
        cb([7, 14, 30, 180, 365].map(value => ({value})))
      },
      blurExpireDays() {
        const {expire,expireDays} = this.tForm
        if (expire !== -2) return
        const expireDaysNumber = parseInt(expireDays)
        this.tForm.expireDays = isNaN(expireDaysNumber) ? '': expireDaysNumber
      },
      tagTypeChange(val) {
        if (val === 1){
          return this.setFormFromObject({
            expire: -2,
            expireDays: 1,
          })
        }
        this.setForm('expire', -1)
      },
      async shareTag(row) {
        let _callback = Boolean
        try {
          const favoriteId = row.id
          const selfOperatorId = +getCookie('operatorId')
          const {operatorIds, callback} = await this.$openOperatorSelector({
            title: '分享标签',
            label: '分享给',
            operatorIds: Object.keys(row.shareTo || {}),
            operatorListFilter: list => list.filter(item => item.id !== selfOperatorId)
          })
          _callback = callback
          const post = {
            favoriteId,
            operatorIds: operatorIds.toString()
          }
          const {data} = await Axios.post('/tags/shareSave', JSON2FormData(post))
          if (data.state !== 1) await Promise.reject(data.msg)
          this.$message.success('分享成功!')
          _callback(true)
          await this.getManagerTags()
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '分享失败')
          _callback(false)
        }
        // this.currentTagId = row.id
        // console.log(row)
        // this.showShareEditor = true
        // this.shareTags = Object.keys(row.shareTo || {}).map(Number)
      },
      async addShareOperator() {
        await this.getOperatorList()
        this.showOperator = true
      },
      async getOperatorList(exp2Tags) {
        try {
          this.operatorLoading = true
          const post = {
            page: 1,
            rows: 1e4,
            keyword: this.operatorKeyword
          }
          if (exp2Tags) delete post.keyword
          const {data: {state, rows, msg}} = await Axios.post('/operator/getPage', JSON2FormData(post))
          if (state !== 1) await Promise.reject(msg)
          if (exp2Tags) this.shareOptionsTags = rows.map(item => ({id: item.operatorId, text: item.employeeName}))
          else this.operatorList = rows
        }catch (e) {
          this.$errorMsg(e || '获取业务员列表失败')
        }finally {
          this.operatorLoading = false
        }
      },
      async confirmShare() {
        try {
          this.saving = true
          const rows = this.$refs.transferTable.selection
          if (rows.length < 1) return this.$message.error('选择需要分享的职员')
          if (!this.currentTagId) return this.$message.error('数据异常, 请刷新重试!')
          const {operatorId} = rows[0]
          const post = {
            operatorId,
            favoriteId: this.currentTagId
          }
          const {data: {state, msg}} = await Axios.post('/tags/shareOne', JSON2FormData(post))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('分享成功!')
          this.showOperator = false
          this.shareTags.push(operatorId)
          await this.getManagerTags()
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$message.error('数据加载失败!')
        } finally {
          this.saving = false
        }
      },
      selectableHandler(row) {
        return !this.shareTags.includes(row.operatorId)
      },
      openTagData(row) {
        const {clientTypeCode} = row
        const page = {
          B_CLIENT: 'client-list.html',
          C_LINKMAN: 'linkman-list.html',
        }[clientTypeCode] || 'client-list.html'
        const tabTitle = {
          B_CLIENT: '公司客户',
          C_LINKMAN: '联系人',
        }[clientTypeCode] || '公司客户'
        const search = new URLSearchParams({
          teamId,
          tempTagId: row.id,
          origin: 'other',
          retrieveName: '标签检索：' + row.favoriteName
        })
        FinalLinkOpen(`${page}?${search}`, null, true, false, tabTitle)
      }
    },
    computed: {
      editorTitle() {
        switch (this.editorMode) {
          case 'add':
            return '新增标签'
          case 'edit':
            return '编辑标签'
          default:
            return '标签管理'
        }
      },
      editorTagType() {
        const list = [...this.TAG_TYPE_LIST]
        if (+this.teamId === -1 || (!this.permission.fu_team_data_kywy && !this.permission.fu_client_data_overview)) {
          list.splice(list.findIndex(item => item.id === 2), 1)
        }
        if (!this.permission.fu_client_data_overview)
          list.splice(list.findIndex(item => item.id === 3), 1)
        return list
      },
      /**
       * 跨团队业务员权限 可新增/编辑【全部内容】
       * 团队内跨业务员权限 可新增/编辑【该业务员团队的团队标签】【个人标签】---->编辑列表页不显示总览标签、其他团队标签
       * 无上方管理权限仅可新增/编辑【个人标签】---->编辑列表页不显示团队、总览标签
       * */
      permission() {
        return {
          fu_all_team_manage: getAuthority('fu_all_team_manage'), // 跨团队管理权限
          fu_team_data_kywy: getAuthority('fu_team_data_kywy'),
          fu_client_data_overview: getAuthority('fu_client_data_overview'),
        }
      },
      isAllow() {
        return row => {
          const {tagType} = row
          if (tagType === 2) return this.permission.fu_team_data_kywy
          if (tagType === 3) return this.permission.fu_client_data_overview || this.permission.fu_all_team_manage
          return true
        }
      }
    },
    components: {
      CustomDialog
    }
  }

  Vue.component('tags-manager', TagsManager)
})(Vue)