.hide-popper {
  display: none !important;
}

.hide-popper-select.el-select {
  .el-select__caret {
    transform: rotateZ(180deg) !important;
  }
}

#TagsManager {
  .dialog-body {
    height: 70vh;
    overflow: auto;
    padding: 20px;

    .table-batch {
      margin-bottom: 20px;
    }
  }

  [class^=el-icon-] {
    margin: 0 !important;
  }
}

.expire-days-wrapper {
  display: flex;
  align-items: center;
  margin-top: 18px;

  .el-autocomplete {
    width: 205px;
  }

  > span {
    margin-left: 20px;
  }
}


#TagsSelector {
  .dialog-body {
    height: 65vh;
    overflow: auto;

    .search-form {
      margin: 0;
      padding: 15px 20px 0;
      border-bottom: 1px solid #eee;
    }

    .tags-container {
      height: calc(65vh - 63px);
      display: flex;

      &.multiple {
        height: calc(65vh - 123px);
      }

      .tag-type-wrapper {
        flex-basis: 200px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #eee;

        .tag-type-item {
          padding-left: 20px;
          box-sizing: border-box;
          height: 40px;
          display: inline-flex;
          align-items: center;
          color: #434A5A;
          cursor: pointer;

          &:hover, &.active {
            background-color: #f0f4fc;
          }

        }
      }

      .tags-content-wrapper {
        max-height: 100%;
        overflow: auto;
        padding: 20px 0 20px 20px;

        .tags-content {
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          width: 100%;


          .shared-title {
            flex-basis: 100%;
            margin-bottom: 10px;
            color: #434a5a;
          }

          .tag-item {
            padding: 7px 10px;
            box-sizing: border-box;
            min-width: 100px;
            width: fit-content;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 34px;
            border: 1px solid #eee;
            margin-right: 20px;
            margin-bottom: 20px;
            position: relative;
            cursor: pointer;

            &:hover {
              border-color: #909399;
              //overflow: unset;
            }

            &.active {
              border-color: #479afb;
              color: #479afb;
            }

            > span {
              max-width: 580px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .delete-tag {
              color: #479afb;
              position: absolute;
              font-size: 15px;
              right: -10px;
              top: -8px;
              cursor: pointer;
              transition: transform 0.3s;

              &:hover {
                transform: scale(1.2);
              }
            }
          }
        }
      }
    }

    .tags-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fafafa;
      .el-button {
        width: 90px;
        border-radius: 5px;
      }
    }

  }
}