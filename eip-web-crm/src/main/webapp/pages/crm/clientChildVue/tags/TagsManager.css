.hide-popper {
  display: none !important;
}

.hide-popper-select.el-select .el-select__caret {
  transform: rotateZ(180deg) !important;
}

#TagsManager .dialog-body {
  height: 70vh;
  overflow: auto;
  padding: 20px;
}
#TagsManager .dialog-body .table-batch {
  margin-bottom: 20px;
}
#TagsManager [class^=el-icon-] {
  margin: 0 !important;
}

.expire-days-wrapper {
  display: flex;
  align-items: center;
  margin-top: 18px;
}
.expire-days-wrapper .el-autocomplete {
  width: 205px;
}
.expire-days-wrapper > span {
  margin-left: 20px;
}

#TagsSelector .dialog-body {
  height: 65vh;
  overflow: auto;
}
#TagsSelector .dialog-body .search-form {
  margin: 0;
  padding: 15px 20px 0;
  border-bottom: 1px solid #eee;
}
#TagsSelector .dialog-body .tags-container {
  height: calc(65vh - 63px);
  display: flex;
}
#TagsSelector .dialog-body .tags-container.multiple {
  height: calc(65vh - 123px);
}
#TagsSelector .dialog-body .tags-container .tag-type-wrapper {
  flex-basis: 200px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #eee;
}
#TagsSelector .dialog-body .tags-container .tag-type-wrapper .tag-type-item {
  padding-left: 20px;
  box-sizing: border-box;
  height: 40px;
  display: inline-flex;
  align-items: center;
  color: #434A5A;
  cursor: pointer;
}
#TagsSelector .dialog-body .tags-container .tag-type-wrapper .tag-type-item:hover, #TagsSelector .dialog-body .tags-container .tag-type-wrapper .tag-type-item.active {
  background-color: #f0f4fc;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper {
  max-height: 100%;
  overflow: auto;
  padding: 20px 0 20px 20px;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  width: 100%;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .shared-title {
  flex-basis: 100%;
  margin-bottom: 10px;
  color: #434a5a;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item {
  padding: 7px 10px;
  box-sizing: border-box;
  min-width: 100px;
  width: fit-content;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 34px;
  border: 1px solid #eee;
  margin-right: 20px;
  margin-bottom: 20px;
  position: relative;
  cursor: pointer;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item:hover {
  border-color: #909399;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item.active {
  border-color: #479afb;
  color: #479afb;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item > span {
  max-width: 580px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item .delete-tag {
  color: #479afb;
  position: absolute;
  font-size: 15px;
  right: -10px;
  top: -8px;
  cursor: pointer;
  transition: transform 0.3s;
}
#TagsSelector .dialog-body .tags-container .tags-content-wrapper .tags-content .tag-item .delete-tag:hover {
  transform: scale(1.2);
}
#TagsSelector .dialog-body .tags-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}
#TagsSelector .dialog-body .tags-footer .el-button {
  width: 90px;
  border-radius: 5px;
}

/*# sourceMappingURL=TagsManager.css.map */
