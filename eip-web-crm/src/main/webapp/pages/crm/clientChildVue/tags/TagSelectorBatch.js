;!(function (w) {
  const template = `
    <custom-dialog 
      id="TagSelectorBatch"
      append2body 
      :show="show" 
      :title="openOptions.title"
      @closed="closed"
      @before-close="close" 
      width="400px">
      <el-main slot="body" class="dialog-body" style="padding: 0;height: 180px;">
        <el-header class="dialog-header" height="40px">
          <div class="dialog-header-left">
            <span v-show="showHeaderLeft">当前选择的{{clientTypeCode === 'C_LINKMAN' ? '联系人' : '公司客户'}}的数量为：{{ openOptions.total }}</span>
          </div>
          <div class="dialog-header-right">
            <el-button 
              size="mini" 
              :loading="saving" 
              icon="el-icon-circle-check" 
              type="primary" 
              @click="startSaving">
              {{saving ? '保存中...' : '确定'}}
            </el-button>
          </div>
        </el-header>
        <el-form label-width="120px" size="mini" style="padding: 30px 30px 0 0;">
          <el-form-item label="标签名称">
            <el-input v-model="favoritesName" readonly @click.native="openTagsSelector" placeholder="请选择标签名称">
              <template #suffix>
                <i class="el-icon-arrow-down"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <tags-selector ref="TagsSelector" :client-type-code="clientTypeCode" @tags-confirm="tagsConfirm"></tags-selector>
      </el-main>
    </custom-dialog>
  `

  w.TagSelectorBatch = {
    template,
    name: 'TagSelectorBatch',
    props:{
      clientTypeCode: String,
      delMemoList:{
        type: Array,
        default(){
          return[]
        }
      },
      showHeaderLeft: {
        type: Boolean,
        default: true
      }
    },
    data(){
      return {
        show: false,
        saving: false,
        selectedTags: [],
        favoritesId: '',
        favoritesName: '',
        /**
         * @type {{
         *   total: number,
         *   api: string,
         *   postData: object,
         *   isBatchAll: boolean,
         *   title: string,
         *   tips: string,
         * }}
         * */
        openOptions: {}
      }
    },
    computed: {
      isLinkman() {
        return this.clientTypeCode === 'C_LINKMAN'
      },
      permission() {
        return {
          team_data_show: this.isLinkman ?
            getAuthority('fu_linkman_team_data_show') :                      // 联系人团队客户显示
            getAuthority('fu_team_data_show'),                               // 公司客户团队客户显示
          team_data_info_show: this.isLinkman ?
            getAuthority('fu_linkman_team_data_info_show') :                 // 联系人团队客户详情显示
            getAuthority('fu_team_data_info_show')                           // 公司客户团队客户详情显示
        }
      }
    },
    methods:{
      open(openOptions){
        this.show = true
        this.openOptions = openOptions
        this.favoritesId = ''
        this.favoritesName = ''
      },
      close(e){
        //如果是点的XX关闭的弹窗e会是一个函数
        //手动关闭调用close不传参数
        //保证正在删除的时候不能关掉弹窗
        if(this.saving && e !== undefined)
          return this.$message.warning('正在处理数据，稍后自动关闭弹窗！')
        this.show = false
      },
      closed(){
        this.saving = false
      },
      async startSaving(){
        try {
          this.saving = true
          const favoritesId = this.favoritesId
          if (!favoritesId) await Promise.reject('请选择标签名称！')
          const {api, postData, isBatchAll, tips} = this.openOptions
          let post = {}
          if (isBatchAll) {
            Object.assign(post, postData, {add_tagsId: favoritesId})
          } else {
            post = postData.map(({clientId}) => {
              return favoritesId.split(',').map(id => {
                return {
                  clientId,
                  favoritesId: id
                }
              })
            })
          }
          const params = isBatchAll ? JSON2FormData(post) : post.flat()
          const {data: {state}} = await Axios.post(api, params)
          if (state !== 1) await Promise.reject('操作标签失败！')
          this.$message({
            type: 'success',
            message: tips || '操作标签成功！',
            showClose: true
          })
          this.close()
          this.$emit('reload')
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '操作标签失败！')
        } finally {
          this.saving = false
        }
      },
      openTagsSelector() {
        const clientType = this.openOptions.clientType
        const isIncludesTeamData =  clientType === '' || clientType === 2
        const isSelfDataScope = !isIncludesTeamData || !this.permission.team_data_show || (
          this.permission.team_data_show &&
          !this.permission.team_data_info_show
        )
        this.$refs.TagsSelector.setShowAllTags(false)
        this.$refs.TagsSelector.open({
          multiple: true,
          showSelected: true,
          title: this.openOptions.title,
          isSelfDataScope
        })
      },
      tagsConfirm({searchList}) {
        this.favoritesId = searchList.map(({id}) => id).join(',')
        this.favoritesName = searchList.map(({text}) => text).join(',')
      }
    },
    components:{
      CustomDialog
    }
  }

  Vue.component(TagSelectorBatch.name, TagSelectorBatch)
})(window)