;(function (Vue) {
  const TAG_TYPE_LIST = [
    {id: 1, text: '个人标签'},
    {id: 2, text: '团队标签'},
    {id: 3, text: '总览标签'}
  ]
  // 标签管理
  const template = `
  <custom-dialog
    :show="show"
    :title="config.title"
    width="850px"
    @before-close="close"
    append2body
    id="TagsSelector"
  >
    <el-main slot="body" class="dialog-body">
      <el-form inline class="search-form" :model="searchForm" label-width="70px" size="mini">
        <el-form-item label="标签名称" prop="keyWord" style="margin-bottom: 18px">
          <el-input @keyup.enter.native="getManagerTags" clearable v-model="searchForm.keyWord" placeholder="标签名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="getTags">查询</el-button>
        </el-form-item>
        <el-form-item style="float: right">
          <el-link :underline="false" type="primary" @click="openTagManager" icon="el-icon-edit">标签管理</el-link>
        </el-form-item>
      </el-form>
      <div class="tags-container" :class="{multiple}">
        <ul class="tag-type-wrapper">
          <li v-if="isAllow(3)" class="tag-type-item" :class="{active: searchForm.type===3}" @click="setTagType(3)"><img src="/eip-web-crm/img/tag-overview.svg" alt="overview">总览标签 ({{overviewCnt}})</li>
          <li v-if="isAllow(2) && +teamId !== -1" class="tag-type-item" :class="{active: searchForm.type===2}" @click="setTagType(2)"><img src="/eip-web-crm/img/tag-team.svg" alt="team">团队标签 ({{teamCnt}})</li>
          <li class="tag-type-item" :class="{active: searchForm.type===1}" @click="setTagType(1)">个人标签 ({{personalCnt}})</li>
        </ul>
        <div class="tags-content-wrapper">
          <div class="tags-content">
            <div class="tag-item" :class="{active: itemActivated(item.id)}" @click="onTagClick(item)" :key="item.id" v-for="item in myTags">
              <span>{{item.favoriteName}}</span>
            </div>
            <template :key="group.id" v-for="group in sharedTags">
              <div class="shared-title">{{group.title}}</div>
              <div class="tag-item" :class="{active: itemActivated(item.id)}" @click="onTagClick(item)" :key="item.id" v-for="item in group.list">
                <span>{{item.favoriteName}}</span>
                <i v-if="multiple" class="delete-tag el-icon-error" @click.stop="onTagRemove(item)"></i>
              </div>
            </template>
            <span v-if="!sharedTags.length && !myTags.length" style="color: #aaa;font-size: 18px;">暂无标签</span>
        </div>
      </div>
      </div>
      <el-footer class="tags-footer" v-if="multiple">
        <div v-if="config.showSelected">
           <span>已选择{{selected.length}}个标签</span>    
        </div>
        <el-radio-group v-model="logic" v-else-if="showLogic">
          <el-radio label="and">标签使用"与"关系</el-radio>
          <el-radio label="or">标签使用"或"关系</el-radio>
        </el-radio-group>
        <div v-else></div>
        <div>
          <el-button plain size="small" @click="close">取消</el-button>
          <el-button type="primary" size="small" @click="confirmSelected">选择</el-button>
        </div>
      </el-footer>
      <tags-manager v-if="show" ref="TagsManager" @refresh="getTags" :client-type-code="clientTypeCode"></tags-manager>
    </el-main>
  </custom-dialog>
  `
  const TagsSelector = {
    template,
    name: 'TagsSelector',
    props: {
      clientTypeCode: {
        type: String,
        default: 'B_CLIENT'
      },
      showLogic: {
        default: true
      },
    },
    data() {
      return {
        show: false,
        TAG_TYPE_LIST,
        searchForm: {
          type: '',
          keyWord: ''
        },
        teamId: window.teamId || top.teamId,
        sharedTags: [],
        myTags: [],
        overviewCnt: 0,
        personalCnt: 0,
        teamCnt: 0,
        multiple: false,
        selected: [],
        logic: 'or',
        groupIndex: 0,
        showAllTags: true,
        isSelfDataScope: false,
        config: {}
      }
    },
    methods: {
      /**
       * @param options {{}|string=}
       * */
      loading(options) {
        options = options || '拼命加载中...'
        const baseOptions = {
          fullscreen: !0,
          text: '',
          lock: !0,
          body: !0,
          background: 'rgba(0, 0, 0, 0.3)'
        }
        return this.$loading(
          typeof options === 'string' ?
            Object.assign(baseOptions, {text: options}) :
            Object.assign(baseOptions, options)
        )
      },
      async getTags() {
        const loading = this.loading()
        try {
          const post = {
            clientTypeCode: this.clientTypeCode,
            ...this.searchForm
          }
          post.workTeamId = this.teamId
          const {data: {state, data}} = await Axios.post('/tags/selectAll', JSON2FormData(post))
          if (state !== 1) await Promise.reject('获取标签列表失败')
          const {myTags, sharedTags,overviewCnt,teamCnt,personalCnt} = data
          this.myTags = myTags || []
          this.overviewCnt = overviewCnt
          this.teamCnt = teamCnt
          this.personalCnt = personalCnt
          const shareTagsMap = {}
          ;(sharedTags || []).forEach(item => {
            const group = shareTagsMap[item.operatorId]
            if (!group) shareTagsMap[item.operatorId] = {
              id: item.operatorId,
              title: `由${item['sharedName']}分享的标签`,
              list: []
            }
            shareTagsMap[item.operatorId].list.push(item)
          })
          this.sharedTags = Object.values(shareTagsMap)
        }catch (e) {
          this.$errorMsg(e || '获取标签列表失败')
        } finally {
          loading && loading.close()
        }
      },
      async setTagType(type) {
        if (this.searchForm.type === type) return
        this.searchForm.type = type
        await this.getTags()
      },
      async open(multiple, selected, logic, index) {
        // 重载，可以直接传入一个对象
        const config = typeof multiple === 'object' ? multiple : {multiple, selected, logic, index}
        this.show = true
        this.multiple = !!config.multiple
        this.isSelfDataScope = !!config.isSelfDataScope // 操作的数据是否是当前用户的数据范围
        this.selected = config.selected || []
        this.groupIndex = config.index || 0
        this.logic = config.logic || 'or' // 第一次点击时，默认为【或】
        config.title = config.title || '检索标签数据'
        this.config = config
        await this.setTagType(1)
      },
      close() {
        this.show = false
        if (this.multiple) {
          this.multiple = false
          this.selected = []
          this.logic = 'or'
          this.groupIndex =  0
          this.$emit('tags-close', this.config)
        }
        this.showAllTags = true
        this.isSelfDataScope = false
      },
      setShowAllTags(showAllTags) {
        this.showAllTags = showAllTags
      },
      async deleteTag(tagId) {
        const loading = this.loading()
        try {
          await this.$confirm('确定删除此标签吗？', '提醒', {type: 'warning'})
          const {data: {state, msg}} = await Axios.post('/tags/deleteTag', JSON2FormData({tagId, workTeamId: this.teamId}))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('删除成功')
          await this.getManagerTags()
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '删除失败')
        } finally {
          loading.close()
        }
      },
      onTagClick(tag) {
        if (!this.multiple) {
          this.$emit('tag-selected', tag, this.config)
          this.close()
          return
        }
        const existsIndex = this.selected.findIndex(item => item.id && tag.id === item.id)
        if (existsIndex !== -1) this.selected.splice(existsIndex, 1)
        else this.selected.push({id: tag.id, text: tag.favoriteName})
      },
      confirmSelected() {
        this.$emit('tags-confirm', {
          searchList: this.selected.map(item => ({...item})),
          searchSplit: this.logic,
          index: this.groupIndex
        }, this.config)
        this.close()
      },
      async onTagRemove(row) {
        try {
          const {sharedId} = row
          if (!sharedId) await Promise.reject('数据异常，无法删除')
          await this.$confirm('确定删除此标签吗？', '提醒', {type: 'warning'})
          const {data: {state, msg}} = await Axios.post('/tags/delShare', JSON2FormData({sharedId}))
          if (state !== 1) await Promise.reject(msg)
          this.$message.success('删除成功')
          await this.getTags()
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        }
      },
      openTagManager() {
        this.$refs.TagsManager && this.$refs.TagsManager.open()
      }
    },
    computed: {
      /**
       * 跨团队业务员权限 可新增/编辑【全部内容】
       * 团队内跨业务员权限 可新增/编辑【该业务员团队的团队标签】【个人标签】---->编辑列表页不显示总览标签、其他团队标签
       * 无上方管理权限仅可新增/编辑【个人标签】---->编辑列表页不显示团队、总览标签
       * */
      permission() {
        return {
          fu_all_team_manage: getAuthority('fu_all_team_manage'),           // 跨团队管理权限
          fu_team_data_kywy: getAuthority('fu_team_data_kywy'),             // 团队内跨业务员权限
          fu_client_data_overview: getAuthority('fu_client_data_overview'), // 数据总览权限
          batch_set_overview_tag: this.isLinkman ?
            getAuthority('fu_linkman_batch_set_overview_tag'):              // 联系人批量设置总览标签
            getAuthority('fu_company_batch_set_overview_tag'),              // 公司批量设置总览标签
        }
      },
      itemActivated() {
        return id => Boolean(this.selected.find(item => item.id === id))
      },
      isAllow() {
        return tagType => {
          if (this.showAllTags) return true
          const admin = this.permission.fu_client_data_overview || this.permission.fu_all_team_manage
          if (tagType === 2) return this.permission.fu_team_data_kywy || admin || this.isSelfDataScope
          // 是否可设置总览标签
          if (tagType === 3) return admin || this.permission.batch_set_overview_tag
          return true
        }
      },
      isLinkman() {
        return this.clientTypeCode === 'C_LINKMAN'
      }
    },
    components: {
      CustomDialog
    }
  }

  Vue.component('tags-selector', TagsSelector)
})(Vue)