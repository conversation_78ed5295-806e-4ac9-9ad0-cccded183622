;(function (Vue) {
  Vue.prototype.$errorMsg = function (msg = '数据加载失败！', title = '错误', type = 'error') {
    return this.$alert(msg, title, {type})
  }
  Vue.prototype.$focusError = () => {
    setTimeout(() => {
      let isError = document.getElementsByClassName("is-error")
      isError.length && isError[0].querySelector('input,textarea').focus();
    }, 100)
  }
  Vue.prototype.$typeof = n => Object.prototype.toString.call(n).slice(-8, -1)

  //  查看修改日志
  const changeLogTemplate = `
    <custom-dialog 
      title="查看修改日志"
      id="ChangeLogDialog"
      :show="showChangLog" 
      width="900px"
      v-if="showChangLog"
      style="width:0;display:table;table-layout:fixed;margin-left:10px"
      @opened="loggerOpened"
      @submit.native.prevent
      @before-close="close">
      <div slot="body" style="padding: 20px">
        <el-form ref="form" :model="searchForm" inline label-width="80px" size="mini" style="padding-top: 20px">
          <el-form-item label="操作内容">
            <el-input v-model="searchForm.operateMemo" clearable placeholder="操作内容" v-focus v-enter="reloadTable"></el-input>
          </el-form-item>
          <el-form-item label="显示范围">
            <el-radio v-model="searchForm.afterTheLastCheck" :label="false">全部</el-radio>
            <el-radio v-model="searchForm.afterTheLastCheck" :label="true">显示上一次审核后的记录</el-radio>
          </el-form-item>
          <el-button 
            size="mini" 
            style="margin-left: 30px" 
            @click="reloadTable" 
            type="info" 
            icon="el-icon-search">查询</el-button>
        </el-form>
        <el-table
          v-el-table-infinite-scroll="onLoadNext"
          v-loading="loadingChangeLog"
          :infinite-scroll-disabled="isLoadFinished"
          :data="changeLogList"
          ref="changeLog"
          class="change-log-table"
          height="400"
          size="mini"
          border
          style="width: 100%">
          <el-table-column prop="operateTime" width="260" label="日志">
            <template slot-scope="{row}">
              {{[row.operateTime, row.operName,row.operateType].filter(Boolean).join(' ')}}
            </template>
          </el-table-column>
          <el-table-column prop="operateMemo" label="操作内容"></el-table-column>
        </el-table>
      </div>
    </custom-dialog>
    `
  const ChangeLogDialog = {
    template: changeLogTemplate,
    name: 'ChangeLogDialog',
    data() {
      return {
        searchForm: {
          operObjCode: '',
          eventKind: '',
          operateMemo: '',
          afterTheLastCheck: true
        },
        showChangLog: false,
        changeLogList: [],
        pageIndex: 1,
        pageSize: 10,
        isLoadFinished: false,
        loadingChangeLog: false
      }
    },
    methods: {
      async getChangeLogger() {
        if (this.loadingChangeLog || !this.searchForm.operObjCode) return
        this.loadingChangeLog = true
        const post = {...this.searchForm, page: this.pageIndex, rows: this.pageSize}
        try {
          let {data: {rows, total}} = await Axios.post('/log/event/getClientModifyAndCheckLog', JSON2FormData(post))
          if (rows.length === 0 || this.changeLogList.length >= total) {
            this.isLoadFinished = true
          }
          this.changeLogList = post.page === 1 ? rows : this.changeLogList.concat(rows)
        } catch (e) {
          console.log(e.message)
          this.$message.error('日志加载失败')
          this.isLoadFinished = true
        } finally {
          this.loadingChangeLog = false
        }
      },
      open(initSearch) {
        if (this.showChangLog) return
        this.showChangLog = true
        // 内部弹框宽度 - (屏幕宽度 / 2 - 外部弹框宽度 / 2 - 间隙填充)
        const offset = 850 - window.innerWidth / 2 + 400 + 20
        $('#ClientEditor .custom-dialog').css({left: offset + 'px'})
        $('.v-modal').addClass('nesting')
        Object.assign(this.searchForm, initSearch)
      },
      reloadTable() {
        this.pageIndex = 1
        this.isLoadFinished = false
        this.changeLogList = []
        this.getChangeLogger().then()
      },
      loggerOpened() {
        this.reloadTable()
        window.__LOG_OLD_INDEX__ = this.$el.style.zIndex
        window.__LOG_EVENT_HANDLER__ = window.__LOG_EVENT_HANDLER__ || (event => {
          if (event.which !== 1) return
          event.stopPropagation()
          this.$el.style.zIndex = window.__LOG_OLD_INDEX__
        })
        this.$el.addEventListener('mousedown', window.__LOG_EVENT_HANDLER__)
      },
      close() {
        this.showChangLog = false
        $('.v-modal').removeClass('nesting')
        this.$el.removeEventListener('mousedown', window.__LOG_EVENT_HANDLER__)
        delete window.__LOG_EVENT_HANDLER__
      },
      update(initSearch) {
        if (!this.showChangLog) return
        Object.assign(this.searchForm, initSearch)
        this.getChangeLogger().then()
      },
      onLoadNext() {
        this.pageIndex++
        this.getChangeLogger().then()
      }
    },
    components: {
      CustomDialog
    }
  }

  // 客户编辑加审核
  const template = `
  <custom-dialog
    :show="show"
    :title="title"
    width="800px"
    @before-close="close"
    @opened="openedChangeLogger()"
    append2body
    v-loading="innerLoading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.3)"
    id="ClientEditor"
  >
    <el-main slot="body" class="dialog-body" v-autoscroll ref="wrapper">
      <el-header class="dialog-header" height="auto">
        <div class="dialog-header-left">
           <span class="page-info" v-if="!single">第{{currentIndex}}/{{clientTotal}}条</span>
        </div>
        <div class="dialog-header-right" style="white-space: nowrap">
          <el-link 
            v-if="isAllowEdit && (!single || isCheckMode)" 
            type="primary" 
            style="margin-right: 10px" 
            :underline="false" 
            @click="openedChangeLogger(true)">查看修改日志</el-link>
          <el-button v-if="isCheckMode" @click="saveForm(true)" size="mini" plain>
              <img src="img/audit_icon.png" style="width: 12px;height: 12px;vertical-align: bottom;margin-right: 4px;">审核
            </el-button>
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            v-if="isAllowEdit"
            @click="saveForm()">
            保存{{saving ? '中' : ''}}
          </el-button>
          <span class="un-permission" v-if="!isAllow">
            <i class="el-icon-warning"></i> 暂无权限查看
          </span>
          <span class="disabled-msg" v-if="!isAllowEdit">
            <template v-if="!isAllowUpdateCheckedClient">已审核</template>（不可编辑）
          </span>
          <template v-if="!single">
            <el-button size="mini" icon="el-icon-arrow-left" type="text" @click="around('prev')">上一条</el-button>
            <el-button size="mini" type="text" @click="around('next')">下一条<i class="el-icon-arrow-right"></i></el-button>
          </template>
        </div>
      </el-header>
      <el-form class="tForm" style="padding: 0" :rules="rules" ref="tForm" :model="tForm" label-width="90px" size="mini" :disabled="!isAllowEdit">
        <el-collapse v-model="activeNames" ref="tFormWrapper">
          <el-collapse-item title="基本信息" :name="1">
          <!--C_LINKMAN --- BEGIN{-->
            <el-form-item label="姓名" prop="personName" v-if="companyInfo.indexOf('personName')!== -1">
              <el-input v-model="tForm.personName" placeholder="姓名"></el-input>
            </el-form-item>
            <el-form-item label="英文名称" prop="englishName" v-if="companyInfo.indexOf('englishName')!== -1">
              <el-input v-model="tForm.englishName"></el-input>
            </el-form-item>
            <el-form-item label="主联系人" prop="isMain" v-if="companyInfo.indexOf('isMain')!== -1">
              <el-select v-model="tForm.isMain" filterable default-first-option>
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="部门" prop="department" v-if="companyInfo.indexOf('department')!== -1">
              <el-select v-model="tForm.department" clearable filterable default-first-option allow-create>
                <el-option v-for="v in department" :label="v.text" :value="v.text" :key="v.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职务" prop="position" v-if="companyInfo.indexOf('position')!== -1">
              <el-select v-model="tForm.position" clearable filterable default-first-option allow-create>
                <el-option v-for="v in f_position_id" :label="v.text" :value="v.text" :key="v.id"></el-option>
              </el-select>
            </el-form-item>
            <!--C_LINKMAN --- }END-->
            <el-form-item :label="'公司名称,工作单位'.split(',')[+isLinkMan]" prop="companyName" v-if="companyInfo.indexOf('companyName')!== -1">
              <el-input :disabled="deepControl.companyName" ref="companyName" v-model="tForm.companyName" placeholder="公司名称">
                <template #append v-if="isEnableCompanySearch">
                  <span @click.stop="searchCompany" title="需输入4字及以上才可以检索公司名称" style="margin: 0 -5px;cursor: pointer">
                    <img src="/eip-web-crm/pages/crm/img/search-company.svg" alt="icon">
                  </span>
                </template>
            </el-input>
            </el-form-item>
            <el-form-item label="公司简称" prop="companyAbbr" v-if="companyInfo.indexOf('companyAbbr')!== -1">
              <el-input :disabled="deepControl.companyAbbr" v-model="tForm.companyAbbr" placeholder="公司简称"></el-input>
            </el-form-item>
            <el-form-item label="公司别名" prop="clientAlias" v-if="companyInfo.indexOf('clientAlias')!== -1">
              <el-input :disabled="deepControl.clientAlias" v-model="tForm.clientAlias" placeholder="公司别名"></el-input>
            </el-form-item>
            <el-form-item label="英文名称" prop="companyEnglish" v-if="companyInfo.indexOf('companyEnglish')!== -1">
              <el-input :disabled="deepControl.companyEnglish" v-model="tForm.companyEnglish" placeholder="英文名称"></el-input>
            </el-form-item>
            <el-form-item label="网址" prop="website" v-if="companyInfo.indexOf('website')!==-1">
              <el-input v-model="tForm.website" placeholder="网址"></el-input>
            </el-form-item>
            <el-form-item label="国家/地区" prop="country" v-if="companyInfo.indexOf('country')!==-1">
              <el-select v-model="tForm.country" @change="countryChange()" placeholder="请选择国家/地区" clearable filterable default-first-option>
                <el-option v-for="v in countries" :key="v.f_area_code" :label="v.name" :value="v.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="省份" prop="province" v-if="companyInfo.indexOf('province')!==-1">
              <el-select v-model="tForm.province" placeholder="请选择省份" @change="loadArea(-1, $event),getCurrentArea()" clearable filterable default-first-option>
                <el-option v-for="v in provinces" :key="v.f_id" :label="v.f_province" :value="v.f_province"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="城市" prop="city" v-if="companyInfo.indexOf('city')!==-1" >
              <el-select v-model="tForm.city" placeholder="请选择城市" @change="loadArea($event, '')" clearable filterable default-first-option>
                <el-option v-for="v in city" :key="v.f_city_id" :label="v.f_city_name" :value="v.f_city_name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="区县" prop="district" v-if="companyInfo.indexOf('district')!==-1">
              <el-select v-model="tForm.district" placeholder="请选择区县" clearable filterable default-first-option>
                <el-option v-for="item in district" :key="item.f_city_id" :label="item.f_city_name" :value="item.f_city_name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="详细地址" prop="address" v-if="companyInfo.indexOf('address')!==-1">
              <el-input v-model="tForm.address" placeholder="详细地址">
                <template #append v-if="isEnableMapSearch">
                  <span ref="address-stub" @click.stop="searchAddress" style="margin: 0 -5px;cursor: pointer">
                    <img src="/eip-web-crm/pages/crm/img/address.svg" alt="icon">
                  </span>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="英文地址" prop="addressEnglish" v-if="companyInfo.indexOf('addressEnglish')!==-1">
              <el-input v-model="tForm.addressEnglish" placeholder="英文地址"></el-input>
            </el-form-item>
            <el-form-item :label="'电话,办公电话'.split(',')[+isLinkMan]" prop="tel" v-if="companyInfo.indexOf('tel')!==-1">
              <el-input v-model="tForm.tel" placeholder="电话"></el-input>
            </el-form-item>
            <el-form-item label="传真" prop="fax" v-if="companyInfo.indexOf('fax')!==-1">
              <el-input v-model="tForm.fax" placeholder="传真"></el-input>
            </el-form-item>
            <!--C_LINKMAN --- BEGIN{-->
            <el-form-item label="手机" prop="mobile" v-if="companyInfo.indexOf('mobile')!==-1">
              <el-input v-model="tForm.mobile"></el-input>
            </el-form-item>
            <el-form-item label="手机2" prop="mobile2" v-if="companyInfo.indexOf('mobile2')!==-1">
              <el-input v-model="tForm.mobile2"></el-input>
            </el-form-item>
            <el-form-item label="qq" prop="qq" v-if="companyInfo.indexOf('qq')!==-1">
              <el-input v-model.trim="tForm.qq"></el-input>
            </el-form-item>
            <el-form-item label="微信" prop="wechat" v-if="companyInfo.indexOf('wechat')!==-1">
              <el-input v-model="tForm.wechat"></el-input>
            </el-form-item>
            <el-form-item label="家庭电话" prop="homeTel" v-if="companyInfo.indexOf('homeTel')!==-1">
              <el-input v-model="tForm.homeTel"></el-input>
            </el-form-item>
            <el-form-item label="身份证号" prop="idcardNo" v-if="companyInfo.indexOf('idcardNo')!==-1">
              <el-input v-model="tForm.idcardNo"></el-input>
            </el-form-item>
            <el-form-item style="flex-basis: 100%;" label="生日" prop="birthday" v-if="companyInfo.indexOf('birthday')!==-1">
             <div style="display:flex;">
                <div style="width: 240px;margin-right: 20px;" >
                  <el-date-picker value-format="yyyy-MM-dd" v-model="tForm.birthday" align="right" type="date" placeholder="选择日期"></el-date-picker>
                </div>
                <el-checkbox v-model="tForm.birthdayRemind">生日时发送提醒</el-checkbox>
              </div>
            </el-form-item>
            <el-form-item label="性别" prop="sex" v-if="companyInfo.indexOf('sex')!==-1">
              <el-select v-model="tForm.sex" clearable filterable default-first-option>
                <el-option label="男" value="男"></el-option>
                <el-option label="女" value="女"></el-option>
              </el-select>
            </el-form-item>
            <!--C_LINKMAN --- }END-->
            <el-form-item label="邮编" prop="postcode" v-if="companyInfo.indexOf('postcode')!==-1">
              <el-input v-model="tForm.postcode" placeholder="邮编"></el-input>
            </el-form-item>
            <el-form-item label="电子信箱" prop="email" v-if="companyInfo.indexOf('email')!==-1">
              <el-input v-model="tForm.email"></el-input>
            </el-form-item>
            <!--C_LINKMAN --- BEGIN{-->
            <el-form-item label="个人简介" v-if="companyInfo.indexOf('cltBrief')!==-1" style="flex-basis: 100%">
              <el-input type="textarea" :rows="2" v-model="tForm.cltBrief"></el-input>
            </el-form-item>
            <!--C_LINKMAN --- }END-->
            <el-form-item label="备注" prop="memo" v-if="companyInfo.indexOf('memo')!==-1" style="flex-basis: 100%">
              <el-input type="textarea" :rows="2" v-model="tForm.memo"></el-input>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item title="分类和关联" :name="2">
            <!--C_LINKMAN --- BEGIN{-->
            <el-form-item label="区域" prop="area" v-if="companyClassIf.indexOf('area')!==-1">
              <el-select v-model="tForm.area" clearable filterable default-first-option>
                <el-option v-for="item in area" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="职业" prop="occupation" v-if="companyClassIf.indexOf('occupation')!==-1">
              <el-input v-model="tForm.occupation"></el-input>
            </el-form-item>
            <el-form-item label="兴趣爱好" prop="hobby" v-if="companyClassIf.indexOf('hobby')!==-1">
              <el-select v-model="tForm.hobby" multiple clearable filterable default-first-option>
                <el-option v-for="item in hobby" :key="item.id" :label="item.text" :value="item.text"></el-option>
              </el-select>
            </el-form-item>
            <!--C_LINKMAN --- }END--> 
            <el-form-item label="行业分类" prop="tradeName" v-if="companyClassIf.indexOf('tradeId')!==-1">
              <el-select :title="tForm.tradeName" v-model="tForm.tradeName">
                <el-option value="" style="background-color:transparent;font-weight: normal">
                  <el-tree
                      :data="tradeClass"
                      :props="defaultProps"
                      highlight-current
                      node-key="tradeId"
                      :current-node-key="tForm.tradeId"
                      :expand-on-click-node="false"
                      @node-click="onTradeSelect"
                      ref="tree">
                  </el-tree>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="客户来源" prop="source" v-if="companyClassIf.indexOf('source')!==-1">
              <el-select v-model="tForm.source" placeholder="请选择客户来源" multiple clearable filterable default-first-option>
                <el-option v-for="item in client_source" 
                         :key="item.id" 
                         :label="item.text"
                         :value="item.text"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="客户等级" prop="grade" v-if="companyClassIf.indexOf('grade')!==-1">
              <el-select v-model="tForm.grade" placeholder="请选择资源类型" clearable filterable default-first-option>
                <el-option v-for="item in client_grade" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品类别" prop="cptList" v-if="companyClassIf.indexOf('productType')!==-1">
              <div style="display:flex;align-items: center">
                <el-select
                  :value="cptListNames"
                  multiple
                  filterable
                  remote
                  @remove-tag="removeCptTag">
                </el-select>
                <el-button style="flex-basis: 28px" type="primary" circle icon="el-icon-circle-plus-outline" @click="showTradSelect=true"></el-button>
              </div>
            </el-form-item>
            <el-form-item label="主营产品" prop="mainProduct" v-if="companyClassIf.indexOf('mainProduct')!==-1">
               <el-input v-model="tForm.mainProduct" placeholder="主营产品"></el-input>
             </el-form-item>
            <el-form-item label="重点市场" prop="keyMarket" v-if="companyClassIf.indexOf('keyMarket')!==-1">
              <el-select v-model="tForm.keyMarket" multiple placeholder="重点市场" clearable filterable default-first-option>
                <el-option v-for="item in key_market" :key="item.id" :label="item.text" :value="item.text"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="企业规模" prop="companySize" v-if="companyClassIf.indexOf('companySize')!==-1">
              <el-select v-model="tForm.companySize" placeholder="企业规模" clearable filterable default-first-option>
                <el-option v-for="item in CompanySize" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="企业性质" prop="nature" v-if="companyClassIf.indexOf('nature')!==-1">
              <el-select v-model="tForm.nature" clearable filterable default-first-option>
                 <el-option v-for="item in nature" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="品牌名称" prop="brandName" v-if="companyClassIf.indexOf('brandName')!==-1">
              <el-input v-model="tForm.brandName" placeholder="品牌名称"></el-input>
            </el-form-item>
            <el-form-item label="资源类型" prop="sourceType" v-if="companyClassIf.indexOf('sourceType')!==-1">
              <el-select v-model="tForm.sourceType" clearable filterable default-first-option>
                <el-option v-for="item in NumberIdWrapper(SourceType)" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item 
              :label="item.f_field_label" 
              v-for="item in custom" 
              :key="item.f_field_id"
              :title="item.f_field_label"
              :style="{flexBasis: item.f_type === 'textarea' ? '100%': '340px'}"
              :prop="'customizedJson.' + item.f_field_name">
              <el-input 
                :label="item.f_field_label" 
                v-if="item.f_type === 'text' || item.f_type === 'textarea'"
                :type="item.f_type"
                :placeholder="item.f_show_setting['placeholder']"
                :autosize="{ minRows: 2, maxRows: 6 }"
                v-model="tForm.customizedJson[item.f_field_name]"></el-input>
              <el-autocomplete
                v-else-if="item.autocomplete"
                :fetch-suggestions="item.querySearch"
                v-model="tForm.customizedJson[item.f_field_name]"
                :name="item.f_field_label" 
                clearable
                style="width: 100%;"
              ></el-autocomplete>
              <el-select 
                clearable filterable default-first-option
                :name="item.f_field_label" 
                v-else-if="item.f_type.startsWith('select')"
                :multiple="item.multiple"
                v-model="tForm.customizedJson[item.f_field_name]">
                <el-option v-for="(v, k) in item.options" :key="k" :label="v" :value="v"></el-option>
              </el-select>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item title="其他信息" :name="3" v-if="!isLinkMan">
            <el-tabs v-model="activeTab" style="flex-basis: 100%">
              <el-tab-pane label="公司简介">
                <div class="form-item-wrapper">
                  <el-form-item label="公司关键词">
                    <el-input v-model="tForm.keyword" placeholder="公司关键词"></el-input>
                  </el-form-item>
                  <el-form-item label="公司简介">
                    <el-input v-model="tForm.cltBrief" placeholder="公司简介"></el-input>
                  </el-form-item>
                  <el-form-item label="产品简介" style="flex-basis: 100%">
                    <el-input type="textarea" placeholder="产品简介" :rows="2" v-model="tForm.productBrief"></el-input>
                  </el-form-item>
                </div>
              </el-tab-pane>
              <el-tab-pane label="银行账户及开票信息">
                <div class="form-item-wrapper">
                  <el-form-item label="开户银行">
                    <el-input v-model="tForm.clientBank.fBankName" placeholder="开户银行"></el-input>
                  </el-form-item>
                  <el-form-item label="纳税人识别号">
                    <el-input v-model="tForm.clientBank.fDutyNum" placeholder="纳税人识别号"></el-input>
                  </el-form-item>
                  <el-form-item label="开户账号">
                    <el-input v-model="tForm.clientBank.fAccountNum" placeholder="开户账号"></el-input>
                  </el-form-item>
                  <el-form-item label="户名">
                    <el-input v-model="tForm.clientBank.fAccountName" placeholder="户名"></el-input>
                  </el-form-item>
                  <el-form-item label="地址">
                    <el-input v-model="tForm.clientBank.fAddress" placeholder="地址"></el-input>
                  </el-form-item>
                  <el-form-item label="电话">
                    <el-input v-model="tForm.clientBank.fTel" placeholder="电话"></el-input>
                  </el-form-item>
                  <el-form-item label="收款人">
                    <el-input v-model="tForm.clientBank.fPayee" placeholder="收款人"></el-input>
                  </el-form-item>
                  <el-form-item label="开票类别">
                    <el-select v-model="tForm.clientBank.fInvoiceType" placeholder="请选择开票类型" clearable filterable default-first-option>
                      <el-option v-for="v in fInvoiceType" :key="v.val" :label="v.lab" :value="v.key"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="邮寄地址">
                    <el-input v-model="tForm.clientBank.fPostAddress" placeholder="邮寄地址"></el-input>
                  </el-form-item>
                  <el-form-item label="开票名称">
                    <el-input v-model="tForm.clientBank.fInvoiceName" placeholder="开票名称"></el-input>
                  </el-form-item>
                  <el-form-item label="收件人">
                    <el-input v-model="tForm.clientBank.fPostPerson" placeholder="收件人"></el-input>
                  </el-form-item>
                  <el-form-item label="联系方式">
                    <el-input v-model="tForm.clientBank.fPostTel" placeholder="联系方式"></el-input>
                  </el-form-item>
                  <el-form-item label="备注" style="flex-basis: 100%">
                    <el-input type="textarea" placeholder="备注" :rows="2" v-model="tForm.clientBank.fMemo"></el-input>
                  </el-form-item>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <custom-dialog title="选取产品类别" :show="showTradSelect" width="740px" @before-close="showTradSelect=false" append2body>
        <div style="height: 450px;display: flex;" slot="body">
          <div style="flex: 0 0 195px; overflow-y: auto">
            <el-tree
              :data="tradeClass"
              :props="defaultProps"
              highlight-current
              node-key="tradeId"
              icon-class="el-icon-arrow-right"
              @node-click="({tradeId}) => getTradeList(tradeId)"
              style="padding: 10px;box-sizing: border-box">
            </el-tree>
          </div>
          <div style="overflow-y: auto; flex: 1; display: flex; flex-direction: column">
            <div style="justify-content: flex-end;padding: 3px 15px;display: flex;flex: 0 0 32px;align-items: center;">
              <el-button size="mini" @click="getTradeValue" type="primary">确定</el-button>
              <el-button size="mini" @click="showTradSelect=false">返回</el-button>
            </div>
            <el-table
              :data="tradeList"
              ref="trade"
              class="trade-table"
              height="385"
              stripe
              border
              @row-click="row => $refs['trade'] && $refs['trade'].toggleRowSelection(row)"
              style="flex: 1; width: 100%">
              <el-table-column type="index" width="55">
                <template slot="header">序号</template>
                <template slot-scope="{$index}">{{$index + 1}}</template>
              </el-table-column>
              <el-table-column type="selection" width="50"></el-table-column>
              <el-table-column prop="f_type_name" label="产品类别"></el-table-column>
              <el-table-column prop="f_trade_name" label="行业分类"></el-table-column>
            </el-table>
            <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next"
            :total="total">
          </el-pagination>
        </div>
        </div>
      </custom-dialog>
      <change-log-dialog ref="changeLogger"></change-log-dialog>
    </el-main>
  </custom-dialog>
  `
  const ClientEditor = {
    template,
    name: 'ClientEditor',
    mixins: [Mixins.clearAndInitForm(), Mixins.listenChange()],
    props: {
      clientTypeCode: {
        type: String,
        default: 'B_CLIENT'
      },
      single: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        saving: false,
        innerLoading: false,
        show: false,
        hasBeenSaved: false,
        tForm: {
          companyName: '',
          companyAbbr: '',
          clientAlias: '',
          companyEnglish: '',
          website: '',
          country: '',
          province: '',
          city: '',
          district: '',
          address: '',
          addressEnglish: '',
          tel: '',
          fax: '',
          postcode: '',
          email: '',
          memo: '',
          tradeId: '',
          tradeName: '', // named
          source: '',
          grade: '',
          gradeName: '', // named
          cptList: [],
          mainProduct: '',
          keyMarket: [],
          companySize: '',
          nature: '',
          brandName: '',
          sourceType: '',
          customizedJson: {},
          keyword: '',
          cltBrief: '',
          productBrief: '',
          area: '',
          clientBank: {
            fBankName: '',  // 开户银行
            fAccountName: '', // 户名
            fAccountNum: '', // 开户账号
            fDutyNum: '', // 纳税人识别号
            fAddress: '', // 地址
            fTel: '',  // 电话
            fPayee: '',  // 收款人
            fInvoiceType: '', // 开票类别id
            fInvoiceName: '', // 开票名称
            fPostAddress: '', // 邮寄地址
            fPostPerson: '', // 收件人
            fPostTel: '', // 联系方式
            fMemo: '' // 备注
          },
          // ---- C_LINKMAN ---BEGIN{
          personName: '',
          englishName: '',
          isMain: '',
          department: '',
          position: '',
          mobile: '',
          mobile2: '',
          qq: '',
          wechat: '',
          homeTel: '',
          idcardNo: '',
          birthday: '',
          birthdayRemind: '',
          sex: '',
          occupation: '',
          hobby: '',
          // ---- C_LINKMAN ---}END

          cltName: ''
        },
        rules: {},
        activeNames: 1,
        activeTab: '',
        // -----item code list BEGIN{
        SourceType: [],
        client_grade: [],
        nature: [],
        CompanySize: [],
        key_market: [],
        client_source: [],
        area: [],
        department: [],
        f_position_id: [],
        hobby: [],
        // -----item code list }END
        tradeClass: [], // 行业分类
        defaultProps: {
          children: 'children',
          label: 'tradeName'
        },
        companyInfo: [],
        companyClassIf: [],

        custom: [],
        fInvoiceType: [ //开票类型
          {lab: '普通发票', key: 1},
          {lab: '增值税专用发票', key: 2},
          {lab: '其他', key: 3}
        ],
        countries: [],
        provinces: [],
        city: [],
        district: [],

        showTradSelect: false,
        pageIndex: 1,
        pageSize: 20,
        tradeList: [],
        total: 0,
        tradeHistory: {},

        clientId: '',
        configLoaded: false,
        clientTotal: 0,
        currentIndex: 0,
        disabledForm: false,
        isAllow: true,
        isChecked: false,
        isCheckMode: false,
        openMode: 'edit', // or check
        fieldControls: [],
        deepControl: {
          companyEnglish: false,
          companyAbbr: false,
          clientAlias: false,
          companyName: false,
        },
      }
    },
    methods: {
      /**
       * @param items {string[]|string}
       * */
      async getBasicDataByCodes(items) {
        items = typeof items === 'string' ? items.split(',') : items
        const queryList = items.map(itemKindCode => Axios.post('/itemCode/selectByItemKindCodeElse', JSON2FormData({itemKindCode})))
        const resultList = await Promise.all(queryList)
        items.forEach((itemKindCode, index) => this.$set(this, itemKindCode, resultList[index]['data']))
      },
      async getTradeClassTree() {
        try {
          const {data} = await Axios.post('/tradeClass/getTree', JSON2FormData({workTeamId: teamId}))
          this.tradeClass = data
        } catch (e) {
          console.log(e.message)
        }
      },
      syncFieldControl(data) {
        data = data || this.fieldControls
        this.companyInfo = []
        this.companyClassIf = []
        const requiredFieldMap = {}
        const customizedField = {}
        const filterFn = code => item => +item.value === 1 && item.taskKindCode === code
        const basic = 'companyInfo,person'.split(',')[+this.isLinkMan]
        const classIf = 'companyClassIf,personClassif'.split(',')[+this.isLinkMan]
        const customized = 'fo_client_com,fo_client_per'.split(',')[+this.isLinkMan]
        const filterField = {basic, classIf, customized}
        data.forEach(item => {
          if (item.param === 'group') return
          if (filterFn(filterField.basic)(item)) {
            requiredFieldMap[item.param] = item.isNull === 1
            this.companyInfo.push(item.param)
          } else if (filterFn(filterField.classIf)(item)) {
            requiredFieldMap[item.param] = item.isNull === 1
            this.companyClassIf.push(item.param)
          } else if (filterFn(filterField.customized)(item)) {
            requiredFieldMap['customizedJson.' + item.param] = item.isNull === 1
            customizedField[item.param] = true
          }
        })
        const polyfillObject = {
          "companyInfo": [
            "companyName",
            "companyAbbr",
            "companyEnglish",
            "clientAlias",
            "website",
            "country",
            "province",
            "city",
            "district",
            "address",
            "addressEnglish",
            "tel",
            "fax",
            "postcode",
            "email",
            "cltBrief",
            "memo"
          ],
          "person": [
            "personName",
            "englishName",
            "companyName",
            "country",
            "province",
            "city",
            "district",
            "isMain",
            "department",
            "position",
            "birthday",
            "sex",
            "address",
            "mobile",
            "mobile2",
            "homeTel",
            "tel",
            "qq",
            "wechat",
            "email",
            "fax",
            "postcode",
            "skype",
            "idcardNo",
            "cltBrief",
            "memo"
          ],
          "companyClassIf": [
            "sourceType",
            "tradeId",
            "source",
            "grade",
            "productType",
            "mainProduct",
            "keyMarket",
            "companySize",
            "nature",
            "brandName",
            "area",
            "parentClient",
            // "group"
          ],
          "personClassif": [
            "sourceType",
            "occupation",
            "hobby",
            "grade",
            "source",
            "area",
            "clientName",
            // "group"
          ],
          "fo_client_per": [],
          "fo_client_com": []
        }
        if (!this.companyInfo.length) {
          this.companyInfo = polyfillObject[basic]
          requiredFieldMap['companyName,personName'.split(',')[+this.isLinkMan]] = true
        }
        if (!this.companyClassIf.length)
          this.companyClassIf = polyfillObject[classIf]
        // 隐藏不显示的自定义字段 [只有有数据的时候才过滤]
        Object.keys(customizedField).length && this.custom.filter(({f_field_name}) => customizedField[f_field_name])
        const deepFormItem = node => {
          if (Array.isArray(node.$children) && node.$children.length > 0) {
            node.$children.forEach(child => {
              const staticClass = child._vnode.data.staticClass
              if (staticClass === 'el-form-item' && child.prop) {
                const {prop, label} = child
                requiredFieldMap[prop] &&
                this.$set(this.rules, prop, [{
                  required: true,
                  message: '请填写' + label,
                  trigger: ['change', 'blur']
                }])
                if (prop === 'companyName' || prop === 'companyAbbr') {
                  const rule = {max: 100, message: label + '不能超过100个字符', trigger: ['change', 'blur']}
                  Array.isArray(this.rules[prop]) ? this.rules[prop].push(rule) : this.$set(this.rules, prop, [rule])
                } else if (prop === 'email') {
                  const rule = {
                    pattern: new RegExp(top.$GlobalDefine.PATTERN_EMAIL_LOOSE),
                    message: '请输入正确的邮箱地址',
                    trigger: ['change', 'blur']
                  }
                  Array.isArray(this.rules[prop]) ? this.rules[prop].push(rule) : this.$set(this.rules, prop, [rule])
                }
              } else {
                deepFormItem(child)
              }
            })
          }
        }
        this.$nextTick(() => {
          const $form = this.$refs.tForm
          const $formCollapse = this.$refs.tFormWrapper
          if (!$form || !$formCollapse) return
          this.rules = {}
          deepFormItem($form)
          deepFormItem($formCollapse)
          this.$nextTick(() => $form.clearValidate())
        })
      },
      async getFieldRequire() { // 查询是否显示及必须输入
        try {
          const {data} = await Axios.post('/exhibition/selectExhibitSetting', JSON2FormData({teamId}))
          this.fieldControls = data
          this.syncFieldControl(data)
        } catch (e) {
          console.warn(e)
          this.$errorMsg('字段数据加载失败！')
        }
      },
      async getCustomField(f_form_name) {
        const {data} = await Axios.post('/customizedField/getAll', JSON2FormData({f_form_name}))
        const parseSettings = settingStr => {
          try {
            return JSON.parse(settingStr)
          } catch {
            return {}
          }
        }
        const parseSelectOptions = optionStr => {
          const select = document.createElement('SELECT')
          select.innerHTML = optionStr
          return Array.from(select.options).map(option => option.text)
        }
        this.custom = data.map(item => {
          const type = item.f_type || 'text'
          item.settings = parseSettings(item.f_show_setting)
          if (type === 'text' || type === 'textarea') return item
          if (type.startsWith('select')) {
            item.options = parseSelectOptions(item.settings.option)
          }
          if (type.endsWith('multiple')) {
            item.multiple = true
          }
          if (type.endsWith('autocomplete')) {
            item.autocomplete = true
            const options = item.options.map(v => ({value: v}))
            item.querySearch = (qs, cb) => {
              if (!qs || !qs.trim()) return cb(options)
              cb(options.filter(v => v.value.includes(qs)))
            }
          }
          return item
        }).filter(Boolean)
      },
      // _sleep(delay) {
      //   return new Promise(resolve => setTimeout(() => resolve(), delay))
      // },
      async loadConfig() {
        // 真离谱 哈哈哈哈哈，竟然没删掉
        // await this._sleep(1e3)
        this.innerLoading = true
        try {
          if (this.configLoaded) return
          const queryItems = this.isLinkMan ?
            'SourceType,client_grade,client_source,area,department,f_position_id,hobby' :
            'SourceType,client_grade,CompanySize,area,nature,key_market,client_source'
          await this.getBasicDataByCodes(queryItems)
          !this.isLinkMan && await this.getTradeClassTree()
          await this.loadProvinces()
          await this.loadCountry()
          await this.getCustomField(this.isLinkMan ? 'fo_client_per' : 'fo_client_com')
          await this.getFieldRequire()
          this.configLoaded = true
        } finally {
          this.innerLoading = false
        }
      },
      async open(clientId, ClientDTO, checkMode, openMode) {
        this.isChecked = Boolean(ClientDTO.isChecked)
        this.isCheckMode = Boolean(checkMode)
        this.openMode = openMode || 'edit'
        if (!this.isAllowUpdateCheckedClient)
          return $.messager.alert(
            '提示', '该' + (this.isLinkMan ? '联系人' : '公司客户') + '已审核，暂无权限编辑',
            'warning'
          )
        this.show = true
        if (!this.single && window.ClientPagerNav) {
          this.clientTotal = ClientPagerNav.pagerOptions().total
        }
        await this.loadConfig()
        await this.loadClient(clientId)
      },
      openedChangeLogger(force) {
        if (force){} // bypass
        else if (!this.isCheckMode) return
        this.$refs['changeLogger'].open({
          operObjCode: this.clientId,
          eventKind: this.isLinkMan ? 3 : 2
        })
        window.__CLIENT_EDITOR_HANDLER__ = window.__CLIENT_EDITOR_HANDLER__ || (event => {
          if (event.which !== 1) return
          const logger = this.$el.querySelector('#ChangeLogDialog')
          logger && (logger.style.zIndex = -1)
        })
        this.$el.addEventListener('mousedown', window.__CLIENT_EDITOR_HANDLER__)
      },
      async isAllowOpenClient(clientId) {
        const param = {clientId, workTeamId: teamId}
        const {data: {data: {isAllow}}} = await Axios.post('/client/isAllowOpen', JSON2FormData(param))
        this.isAllow = isAllow
        return isAllow
      },
      // 是否是当前登录操作员的同部门或下级部门
      isAllowDepartment(operatorId) {
        return new Promise(resolve => {
          $.ajax({
            url: variableSponsor + '/operator/sameOrChildBranchByNowOperator',
            dataType: "json", //返回数据类型
            type: "post",
            data: {operatorId},
            success({data}) {
              resolve(Boolean(data))
            }
          })
        })
      },
      async loadClient(clientId) {
        if (!this.single)
          this.currentIndex = ClientPagerNav.getCurrentIndexById(+clientId)
        this.clientId = clientId
        try {
          // 重置表单
          this._initForm({
            cptList: [],
            keyMarket: [],
            customizedJson: {},
            clientBank: {
              fBankName: '',  // 开户银行
              fAccountName: '', // 户名
              fAccountNum: '', // 开户账号
              fDutyNum: '', // 纳税人识别号
              fAddress: '', // 地址
              fTel: '',  // 电话
              fPayee: '',  // 收款人
              fInvoiceType: '', // 开票类别id
              fInvoiceName: '', // 开票名称
              fPostAddress: '', // 邮寄地址
              fPostPerson: '', // 收件人
              fPostTel: '', // 联系方式
              fMemo: '' // 备注
            },
          })
          Object.keys(this.deepControl).forEach(key => this.$set(this.deepControl, key, false))
          const isAllow = await this.isAllowOpenClient(clientId)
          if (!isAllow) {
            this.disabledForm = true
            return await this.getNameById(clientId)
          }
          const {data} = await Axios.post('/client/getByClientId', JSON2FormData({clientId, fWorkteamId: teamId}))
          if (!(Array.isArray(data) && data.length > 0)) return this.$errorMsg('客户信息加载失败！')
          const ClientDTO = data[0]
          Object.keys(this.deepControl).forEach(key => this.$set(this.deepControl, key, Boolean(ClientDTO[key] && !this.isAllowUpdateCheckedClientName)))
          this.disabledForm = !isAllow || (ClientDTO.salesmanId && +getCookie('operatorId') !== ClientDTO.salesmanId && !getAuthority('fu_team_data_kywy') && !(getAuthority('fu_team_manage_subordinate') && (await this.isAllowDepartment(ClientDTO.salesmanId))))
          this.isChecked = Boolean(ClientDTO.isChecked)
          this.isCheckMode = !this.isChecked && getAuthority(this.isLinkMan ? 'fu_linkman_check' : 'fu_company_check')
          const changeLogger = this.$refs['changeLogger']
          if (changeLogger) {
            this.isCheckMode ? changeLogger.update({
              operObjCode: this.clientId,
              eventKind: this.isLinkMan ? 3 : 2
            }) : changeLogger.close()
          }
          for (let key of Object.keys(this.tForm)) {
            let v = ClientDTO[key]
            if (v === undefined) continue
            if (key === 'keyMarket' || key === 'source' || key === 'hobby') v = v ? v.split(',') : []
            else if (key === 'province' && v) await this.loadArea(-1, v, !0)
            else if (key === 'city' && v) await this.loadArea(v, '', !0)
            else if (key === 'customizedJson') {
              if (v) {
                this.custom.forEach(({f_field_name, f_type}) => {
                  if (!f_type.endsWith('multiple')) return
                  v[f_field_name] = v[f_field_name] ? v[f_field_name].split(',') : []
                })
              }
              v = v || (() => {
                const result = {}
                this.custom.forEach(({f_type, f_field_name}) => {
                  result[f_field_name] = f_type.endsWith('multiple') ? [] : ''
                })
                return result
              })()
            }
            this.$set(this.tForm, key, v)
          }
          await this.countryChange(true)
          this.$nextTick(() => this.$refs.tForm && this.$refs.tForm.clearValidate())
          this.startChange()
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      async loadProvinces() {
        const {data} = await Axios.post('/city/selectProvince')
        this.provinces = Object.freeze(data)
      },
      async loadCountry() {
        const {data} = await Axios.post('/city/selectNation', JSON2FormData({type: 3}))
        this.countries = data.data.map(item => {
          const _item = {...item}
          _item['f_area_code'] = Math.random().toString(32).slice(2)
          return _item
        })
      },
      async loadArea(f_parent_id, f_province, ignoreClear) {
        const key = f_parent_id === -1 ? 'city' : 'district'
        !ignoreClear && this.clear(...new Set([key, 'district']))
        if (typeof f_parent_id === 'string')
          f_parent_id = (this.city.find(({f_city_name}) => f_city_name === f_parent_id) || {})['f_city_id']
        if (!f_parent_id) return
        const post = JSON2FormData({f_parent_id, f_province})
        const {data} = await Axios.post('/city/select', post)
        this[key] = Object.freeze(data)
      },
      /**
       * @param options {{}|string=}
       * */
      loading(options) {
        options = options || '拼命加载中...'
        const baseOptions = {
          fullscreen: !0,
          text: '',
          lock: !0,
          body: !0,
          background: 'rgba(0, 0, 0, 0.3)'
        }
        return this.$loading(
          typeof options === 'string' ?
            Object.assign(baseOptions, {text: options}) :
            Object.assign(baseOptions, options)
        )
      },
      close() {
        this.$refs['changeLogger'].close()
        this._close(() => {
          this.show = false
          this.isChange = false
          this.hasBeenSaved && this.$emit('reload')
          this.hasBeenSaved = false // 重置保存状态
          this.$el.removeEventListener('mousedown', window.__CLIENT_EDITOR_HANDLER__)
          delete window.__CLIENT_EDITOR_HANDLER__
        })
      },
      async saveForm(isCheck) {
        this.saving = true
        try {
          await this.validateForm()
          await this.checkCompanyNameRepeat()
          await this.checkLinkmanRepeat()
          const oldCompanyName = await top.$GlobalDefine.getNameByClientId(this.clientId)
          const post = Object.assign({
            clientId: this.clientId,
            clientTypeCode: this.clientTypeCode,
            operEntrance: this.title
          }, this.tForm)
          if (isCheck) post.isChecked = 1
          if (post.customizedJson) {
            Object.keys(post.customizedJson).forEach(key => {
              if (!Array.isArray(post.customizedJson[key])) return
              post.customizedJson[key] = post.customizedJson[key].toString()
            })
          }
          const toStringKeys = 'source,keyMarket,hobby'.split(',')
          toStringKeys.forEach(key => {
            const v = post[key]
            if (!v) return
            post[key] = v.toString()
          })
          const {data: {state, msg}} = await Axios.post('/client/updateAll', post)
          if (state !== 1) await Promise.reject(`保存失败${msg ? (', ' + msg) : '!'}`)
          this.hasBeenSaved = true // 更新标记为保存过数据
          this.isChange = false // 更新标记未修改
          Object.keys(this.deepControl).forEach(key => this.$set(this.deepControl, key, Boolean(this.tForm[key] && !this.isAllowUpdateCheckedClientName)))
          this.$message.success({
            message: '保存成功！',
            showClose: true
          })
          if (!this.isLinkMan) {
            await top.$GlobalDefine.checkCompanyChangedNoRet(oldCompanyName, this.tForm.companyName, this.clientId, this)
          }
          if (isCheck) this.isCheckMode = false
          this.single && this.close()
        } catch (e) {
          if (!e || e === 'cancel' || e === 'close') return
          if (this.handleErrors(e)) this.$errorMsg(e)
          this.activeNames = [1, 2]
          await Promise.reject('close')
        } finally {
          this.saving = false
        }
      },
      /**
       * @param direction {'prev','next'}
       * @return {Promise<any>}
       * */
      async around(direction) {
        this.innerLoading = true
        try {
          try {
            // 数据没改变相当于取消保存
            if (!this.isChange) await Promise.reject('cancel')
            await this.$confirm('当前数据存在变动，是否保存？', '提示', {
              confirmButtonText: '保存',
              cancelButtonText: '不保存',
              type: 'warning',
              distinguishCancelAndClose: true
            })
            await this.saveForm()
          } catch (e) { // cancel 取消按钮也需要继续往下执行
            if (e === 'close') return
            if (e !== 'cancel') e && console.warn(e)
          }
          // 获取前后的 clientId
          const result = ClientPagerNav.getAroundIdById(+this.clientId, direction)
          const cid = this.$typeof(result) === 'Promise' ? (await result) : result
          // 获取到了无效的数据直接报错
          if (cid <= 1) return this.$errorMsg(cid === -1 ? '前面没有数据了' : '没有数据了')
          await this.loadClient(cid)
        } finally {
          this.innerLoading = false
        }
      },
      onTradeSelect({tradeName, tradeId, treePathName}) {
        this.$set(this.tForm, 'tradeName', treePathName || tradeName)
        this.$set(this.tForm, 'tradeId', tradeId)
      },
      async getTradeList(parentTradeId) {
        const post = {
          ...this.tradeHistory,
          parentTradeId,
          page: this.pageIndex,
          rows: this.pageSize,
          workTeamId: teamId
        }
        this.tradeHistory = post
        try {
          let {data: {rows, total}} = await Axios.post('/productTypeBase/getList', JSON2FormData(post))
          this.tradeList = rows
          this.total = total
        } catch (e) {
          console.log(e.message)
        }
      },
      handleCurrentChange(page) {
        this.pageIndex = page
        this.getTradeList().then()
      },
      handleSizeChange(pages) {
        this.pageSize = pages
        this.getTradeList().then()
      },
      getTradeValue() {
        const rows = this.$refs.trade.selection
        if (rows.length > 0 && !this.tForm.tradeId) {
          const {f_trade_name: tradeName, f_trade_id: tradeId, tradeTreePathName} = rows[0]
          this.onTradeSelect({tradeName, tradeId, treePathName: tradeTreePathName})
        }
        this.$set(this.tForm, 'cptList', rows.map(item => ({
          typeName: item.f_type_name,
          productTypeId: item.f_product_type_id
        })))
        this.showTradSelect = false
      },
      removeCptTag(tagName) {
        if (!tagName) return
        const cptIndex = this.tForm.cptList.findIndex(({typeName}) => typeName === tagName)
        if (cptIndex < 0) return
        this.tForm.cptList.splice(cptIndex, 1)
      },
      async checkCompanyNameRepeat() {
        if (this.isLinkMan || !this.tForm.companyName) return
        const post = {
          clientId: this.clientId,
          fWorkteamId: teamId,
          cltName: this.tForm.companyName
        }
        const {
          data: {
            cltName,
            companyKeyName,
            state
          }
        } = await Axios.post('/client/queryIsExistCltName', JSON2FormData(post))
        // bug #13346
        // if (!this.tForm.companyAbbr) {
        //   this.$set(this.tForm, 'companyAbbr', companyKeyName)
        // }
        this.$set(this.tForm, 'cltName', cltName)
        const rejectMessage = '当前公司名称已存在同名客户，请重新输入'
        if (state === -1) await Promise.reject(rejectMessage)
        if (state === 1)
          await (
            fu_forcibly_save_client ?
              this.$confirm('当前公司名称已存在同名客户，是否强制保存?', '提示', {type: 'info'}) :
              Promise.reject(rejectMessage)
          )
      },
      async checkLinkmanRepeat() {
        const {
          personName,
          mobile,
          mobile2,
          email,
          wechat,
          qq,
          tel,
        } = this.tForm
        if (!this.isLinkMan || !personName) return
        let cltName = personName
        if (mobile) cltName += mobile
        else if (mobile2) cltName += mobile2
        else if (qq) cltName += qq
        else if (wechat) cltName += wechat
        else if (tel) cltName += tel
        else if (email) cltName += email
        this.$set(this.tForm, 'cltName', cltName)
        const post = {
          clientId: this.clientId,
          fWorkteamId: teamId,
          cltName,
          personName
        }
        const {data: {state, data}} = await Axios.post('/client/checkLinkmanRepeat', JSON2FormData(post))
        if (state <= 0) return
        if (+data['personNameCount'] <= 0) return
        if (personName === cltName) await Promise.reject('联系人姓名重复，请至少输入手机、QQ、邮箱、微信、办公电话其中的一个!')
        if (+data['cltNameCount'] <= 0) return
        await (
          fu_forcibly_save_linkman ?
            this.$confirm('当前团队下已重复联系人，是否强制保存？', '提示', {type: 'info'}) :
            Promise.reject('当前团队下已重复联系人，不允许保存！')
        )
      },
      async getNameById(clientId) {
        const {data: {msg}} = await Axios.post('/client/getNameById', JSON2FormData({clientId}))
        this.$set(this.tForm, this.isLinkMan ? 'personName' : 'companyName', msg)
        this.$nextTick(() => this.$refs.tForm && this.$refs.tForm.clearValidate())
        this.startChange()
      },
      async getCurrentArea() {
        let {country, province} = this.tForm
        const foreign = country && country !== '中国'
        province = foreign ? 'foreign' : province
        if (!province) return
        const {data: {data}} = await Axios.post('/provinceArea/getByProvince', JSON2FormData({province}))
        const area = String((data || {})['areaId'] || '')
        area && this.setFormFromObject({area})
      },
      async countryChange(ignoreArea) {
        const country = this.tForm.country
        if (country ==='中国' || !country) {
          this.syncFieldControl()
        }else {
          const controlFields = 'province,city,district'.split(',')
          this.clear(...controlFields)
          this.syncFieldControl(this.fieldControls.map(item => {
            const _temp = {...item}
            if (controlFields.indexOf(_temp.param) !== -1) {
              _temp.isNull = 0
              _temp.value = '0'
            }
            return _temp
          }))
        }
        if (ignoreArea) return
        await this.getCurrentArea()
      },
      searchAddress() {
        const {province, city, district, address} = this.tForm
        const ref = this.$refs['address-stub']
        if (!ref) return this.$errorMsg('数据异常，请刷新重试')
        if (!address) return this.$errorMsg('请先输入地址关键字')
        const _address = window.getSelection().toString() || address
        this.$addressHelper(ref, _address, [province, city, district], (address, item) => {
          this.tForm.address = address
          this.tForm.province = item.province
          this.tForm.city = item.city
          this.tForm.district = item.district
        })
      },
      searchCompany() {
        top.searchCompanyByQichacha(
          this.$refs.companyName.$el,
          value => this.tForm.companyName = value,
          () => this.tForm.companyName,
          document
        )
      }
    },
    computed: {
      cptListNames() {
        return this.tForm.cptList.map(item => (item || {}).typeName) || ''
      },
      title() {
        return (this.isCheckMode && this.openMode === 'check' ? '审核' : '编辑') + (this.isLinkMan ? '联系人' : '公司')
      },
      NumberIdWrapper() {
        return array => array.map(item => ({...item, id: +item.id}))
      },
      isLinkMan() {
        return this.clientTypeCode === 'C_LINKMAN'
      },
      isAllowEdit() {
        return this.isAllow && !this.disabledForm && this.isAllowUpdateCheckedClient
      },
      isAllowUpdateCheckedClient() {
        if (!this.isChecked) return true
        return getAuthority({
          B_CLIENT: 'fu_update_audit_client',
          C_LINKMAN: 'fu_update_audit_linkman'
        }[this.clientTypeCode])
      },
      isAllowUpdateCheckedClientName() {
        if (this.isLinkMan || !this.isChecked) return true
        return getAuthority('fu_update_audit_client_all')
      },
      isEnableMapSearch() {
        return this.$getEnabledAddressHelper()
      },
      isEnableCompanySearch() {
        return top.__IS_ENABLE_QICHACHA__ && !this.isLinkMan
      }
    },
    components: {
      CustomDialog,
      ChangeLogDialog
    }
  }

  Vue.component('client-editor', ClientEditor)
})(Vue)