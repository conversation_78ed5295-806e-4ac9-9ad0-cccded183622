#ClientEditor .dialog-body {
  height: 70vh;
  overflow: auto;
  padding: 0;
}
#ClientEditor .dialog-body .dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #fff;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-left {
  color: #4696f9;
  line-height: 25px;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .el-button [class*=el-icon-] + span {
  margin-left: 0;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .el-button--text {
  color: #909399;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .el-button--text:hover {
  color: #3089ff;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .disabled-msg {
  color: #888;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .un-permission {
  color: #888;
}
#ClientEditor .dialog-body .dialog-header .dialog-header-right .un-permission i[class^=el-icon-] {
  color: #faad14;
  font-size: 16px;
}
#ClientEditor .dialog-body .el-form .el-collapse-item__header {
  height: 30px;
  line-height: 30px;
  color: #4696f9;
  background-color: #f5f5f5;
  padding-left: 20px;
}
#ClientEditor .dialog-body .el-form .el-collapse-item__content {
  padding: 20px 20px 2px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
#ClientEditor .dialog-body .el-form .el-collapse-item__content .form-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
#ClientEditor .dialog-body .el-form .el-collapse-item__content .el-form-item.el-form-item--mini {
  flex: 0 0 340px;
}
#ClientEditor .dialog-body .el-form .el-collapse-item__content .el-form-item.el-form-item--mini .el-form-item__label {
  white-space: nowrap;
  margin-bottom: 0;
  font-weight: normal;
  text-overflow: ellipsis;
  overflow: hidden;
}
#ClientEditor .el-input, #ClientEditor .el-select {
  width: 100%;
}
#ClientEditor [class^=el-icon-] {
  margin: 0 !important;
}

.trade-table .el-table-column--selection .cell {
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
}

#ChangeLogDialog .el-form-item__label {
  white-space: nowrap;
  margin-bottom: 0;
  font-weight: normal;
  text-overflow: ellipsis;
  overflow: hidden;
}
#ChangeLogDialog .el-select {
  width: 100px;
}

.v-modal.nesting {
  z-index: 2000 !important;
}

/*# sourceMappingURL=index.css.map */
