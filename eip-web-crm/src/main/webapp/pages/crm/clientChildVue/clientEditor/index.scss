#ClientEditor{
  .dialog-body {
    .dialog-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 20px;
      box-shadow: 0 0 4px #ccc;
      position: sticky;
      top: 0;
      z-index: 2;
      background-color: #fff;
      .dialog-header-left {
        color: #4696f9;
        line-height: 25px;
      }
      .dialog-header-right{
        .el-button [class*=el-icon-]+span{
          margin-left: 0;
        }
        .el-button--text{
          color: #909399;
        }
        .el-button--text:hover{
          color: #3089ff;
        }
        .disabled-msg{
          color: #888;
        }
        .un-permission {
          color: #888;
          i[class^=el-icon-]{
            color: #faad14;
            font-size: 16px;
          }
        }
      }
    }
    height: 70vh;
    overflow: auto;
    padding: 0;
    .el-form{
      .el-collapse-item__header {
        height: 30px;
        line-height: 30px;
        color: #4696f9;
        background-color: #f5f5f5;
        padding-left: 20px;
      }
      .el-collapse-item__content {
        padding: 20px 20px 2px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .form-item-wrapper{
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .el-form-item.el-form-item--mini {
          flex: 0 0 340px;
          .el-form-item__label{
            white-space: nowrap;
            margin-bottom: 0;
            font-weight: normal;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }
  }
  .el-input, .el-select {
    width: 100%;
  }
  [class^=el-icon-]{
    margin: 0!important;
  }
}
.trade-table{
  .el-table-column--selection .cell{
    padding-left: 10px;
    padding-right: 10px;
    text-align: center;
  }
}

#ChangeLogDialog {
  .el-form-item__label {
    white-space: nowrap;
    margin-bottom: 0;
    font-weight: normal;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .el-select{
    width: 100px;
  }
}

.v-modal.nesting{
  z-index: 2000!important;
}