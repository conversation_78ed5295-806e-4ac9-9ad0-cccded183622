#exportDialog .dialog-body {
    padding: 0;
    /* height: 350px; */
}

#exportDialog .dialog-body .el-input__icon:hover {
    color: #2e82ff;
}

#exportDialog .dialog-body .el-tabs__header.is-top {
    display: none;
}

#exportDialog .dialog-body .operate-button {
    display: flex;
    align-items: center;
    justify-content: center;
}

#exportDialog .dialog-body .operate-button .btn + .btn {
    margin-left: 20px;
}

#exportDialog .dialog-body .text-over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#exportDialog .check-tip {
    height: 33px;
    font-size: 24px;
    font-weight: 400;
    color: rgba(0,0,0,0.85);
    line-height: 32px;
    padding: 25px 0 30px;
}
#exportDialog .btn-radio2 {
    border-radius: 2px !important;
}
#exportDialog .dialog-body .btn {
    width: 140px;
    height: 34px;
    padding-top: 8px;
    padding-bottom: 8px;
}

#exportDialog .dialog-body .prompt {
    position: absolute;
    top: 32px;
    left: 5px;
    font-size: 12px;
    line-height: 12px;
    width: 350px;
    color: #f74444;
}

#exportDialog .dialog-body .fourth .container {
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#exportDialog .dialog-body .fourth .container .el-progress {
    width: 100%;
}

#exportDialog .dialog-body .fourth {
    padding: 40px;
}

#exportDialog .dialog-body .fourth .form-input {
    margin-bottom: 55px;
}

#exportDialog .dialog-body .fourth .form-input .el-form-item {
    width: 420px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 14px;
}

#exportDialog .dialog-body .fourth .form-input .el-input, #exportDialog .dialog-body .fourth .form-input .el-select {
    width: 200px;
}

#exportDialog .dialog-body .fourth .form-input .prompt {
    left: 0;
}