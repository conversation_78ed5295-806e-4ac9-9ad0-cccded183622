;!(function () {
  // 组件模板
  const ExportDialogTemplate = `
  <custom-dialog :title="title" :show="showExportClient" width="630px" id="exportDialog" @before-close="close()">
    <el-main slot="body" class="dialog-body">
      <div class="fourth">
        <center >
          <img v-if="ok" src="./img/check.svg" alt="" width="72" height="72">
          <el-progress class="export-loading" v-else type="circle" :percentage="25" width="72" :show-text="false" :style="{animation: 'loading-rotate 2s linear infinite'}"></el-progress>
          <div class="check-tip">
            {{ ok ? '导出成功' : '正在导出 ...' }}
          </div>
        </center>
        <!--div class="operate-button" v-if="ok">
           <el-button class="btn btn-radio2" type="primary" @click="close">继续导出</el-button>
           <el-button class="btn btn-radio2" @click="downFile" plain>下载导出文件</el-button>
        </div-->
        <div class="container">
          <el-progress :text-inside="true" :stroke-width="8" :show-text="false" :percentage="progress"></el-progress>
        </div>
      </div>
    </el-main>
  </custom-dialog>
`;
  // 组件对象
  window.ExportDialog = {
    template: ExportDialogTemplate,
    data() {
      return {
        showExportClient: false,
        exporting: false,
        progress: 0,
        timer: null,
        ok: false,
        err: '',
      };
    },
    methods: {
      open(serialNum) {
        this.showExportClient = true;
        this.start(serialNum);
      },
      downFile(){
        this.$emit('down-file')
      },
      close(noRefresh) {
        if (this.exporting) return this.$message.warning("正在导出，请勿关闭");
        this.showExportClient = false;
        this.exporting = false
        this.progress = 0
        this.ok = false
        this.err = '';
      },
      // clear(...keys) {
      //   keys.forEach(key => this.$set(this.importForm, key, ''))
      // },
      start(serialNum) {
        this.exporting = true;
        const that = this;
        const requestData = JSON2FormData({ serialNum });
        this.timer = setInterval(() => {
          Axios.post("/client/getProgress", requestData).then(
            ({ data: res }) => {
              if (res.state === 1) {
                if(res.data){
                  const { progress, msg } = res.data;
                  this.progress = progress;
                  // this.exportMsg = msg;
                }else{
                  that.done()
                }
              }
            }
          );
        }, 2000);
      },
      done(resp) {
        console.log('done' , resp);
        // const $this = this
        // if (resp.state !== 1)
        //   return this.$alert(resp.msg, '错误', {
        //     type: 'error', callback() {
        //       $this.resetExportData(file)
        //     }
        //   })
        this.exporting = false
        this.progress  = 100
        clearInterval(this.timer)
        setTimeout(() => {
          this.ok = true
        }, 600);
        // this.timer     = null
        // this.exportResult = resp.data;
        //这是为了先执行fileChange，所以让他到下一事件循环在执行
        //this.$nextTick(this.resetImportData.bind(this, file, true))
      },
      error(err) {
        clearInterval(this.timer);
        // this.timer    = null
        this.$message.error("导出失败！");
        this.exporting = false;
        this.err = err || '导出失败';
      },
    },
    computed: {
      exportButtonText() {
        return this.exporting ? "正在导出中..." : this.ok ? "导出成功" : this.err ? "导出失败" : "开始导出";
      },
      title() {
        return this.exporting ? "正在导出..." : this.ok ? "导出成功" :  this.err ? "导出失败" : "开始导出";
      },
      exportMsg() {
        return this.exporting ? "正在导出" : this.ok ? "导出成功" : this.err ? this.err : "开始导出";
      }
    },
    components: {
      customDialog
    },
  };
})()