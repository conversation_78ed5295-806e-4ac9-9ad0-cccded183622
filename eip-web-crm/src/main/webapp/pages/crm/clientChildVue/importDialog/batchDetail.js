;!(function () {
  const template = `
    <custom-dialog title="批次详情" :show="show" width="505px" top="58px" :modal="false" append2body id="BatchDetail" @before-close="close">
      <el-main 
        slot="body" 
        class="dialog-body" 
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
        v-loading="loading">
        <el-radio-group v-model="import_dataType" @change="$emit('change', $event, batchId)">
          <el-radio :label="1" v-if="count.import_addCount">新增公司客户<span class="el-button--text"> {{count.import_addCount}} </span>条</el-radio>
          <el-radio :label="2" v-if="count.import_megreCount">团队内合并公司客户<span class="el-button--text"> {{count.import_megreCount}} </span>条</el-radio>
          <el-radio :label="3" v-if="count.import_mergeFromOverviewCount">总览合并引用公司客户<span class="el-button--text"> {{count.import_mergeFromOverviewCount}} </span>条</el-radio>
          <el-radio :label="4" v-if="repeatAuth===1 && count.import_repeatAddCount">冲突客户<span class="el-button--text"> {{count.import_repeatAddCount}} </span>条</el-radio>
          <el-radio :label="null" v-if="count.import_count">导入总数<span class="el-button--text"> {{count.import_count}} </span>条<span v-if="count.import_delCount">（已进回收站 <span class="el-button--text"> {{count.import_delCount}} </span> 条）</span></el-radio>
        </el-radio-group>
      </el-main>
    </custom-dialog>
  `
  window.BatchDetail = {
    template,
    data() {
      return {
        show: false,
        import_dataType: null,
        repeatAuth: 2,
        loading: false,
        type: '',
        batchId: '',
        count:{
          import_count: 0,  //导入总数
          import_delCount: 0,  //已进回收站数量
          import_addCount: 0,  //新增数量
          import_repeatAddCount: 0, //重复强制新增数量
          import_megreCount: 0, //团队内合并数量
          import_mergeFromOverviewCount: 0, //团队内合并数量
        }
      }
    },
    methods: {
      open({type, repeatAuth, batchId}, count) {
        this.show = true
        this.type = type
        this.repeatAuth = repeatAuth
        this.batchId = batchId
        this.setCount(count)
        console.log(this.show)
      },
      close() {
        this.show = false
      },
      startLoading(){
        this.loading = true
      },
      closeLoading(){
        this.loading = false
      },
      setCount(count){
        Object.keys(this.count).forEach(key=>{
          this.$set(this.count, key, count[key] || 0)
        })
      }
    },
    components: {
      customDialog
    }
  }
})()