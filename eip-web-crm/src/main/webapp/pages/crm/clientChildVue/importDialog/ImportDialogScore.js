;!(function () {
  // 积分导入模板
  // require : dialogDrag.js element-ui xlsx.js
  const ImportDialogTemplate = `
  <custom-dialog :title="importing ? '正在导入...' : title" :show="show" width="600px" id="importDialog" @before-close="close()">
    <el-main slot="body" class="dialog-body">
      <el-form size="mini" label-width="120px">
        <el-tabs v-model="currentStepName">
          <el-tab-pane name="1"
                       v-loading="reading"
                       element-loading-text="正在读取文件..."
                       element-loading-spinner="el-icon-loading">
            <el-main class="first">
              <div>
                <el-button class="btn" type="info" @click="downloadTemplate">下载导入模板</el-button>
              </div>
              <div class="select-file">
                <el-upload ref="upload"
                           :action="api"
                           accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                           :data="uploaderData"
                           :show-file-list="false"
                           :file-list="fileList"
                           :auto-upload="false"
                           :on-success="importSuccess"
                           :on-error="importError"
                           :on-change="fileChange">
                  <el-button slot="trigger" class="btn" @click="$refs.upload.clearFiles()" type="warning">选择文件
                  </el-button>
                  <div slot="tip" class="el-upload__tip text-over">{{tips}}</div>
                </el-upload>
                <div class="dropdown">
                  <el-dropdown @command="selectSheet">
                    <el-button type="warning" class="btn">{{sheetName}}<i
                        class="el-icon-arrow-down el-icon--right"></i></el-button>
                    <el-dropdown-menu slot="dropdown" :class="{'import-hide':!sheetNames.length}">
                      <el-dropdown-item
                        v-for="(s, index) in sheetNames"
                        class="right-dropdown"
                        :key="index"
                        :command="index + 1">
                        {{s}}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <span class="el-upload__tip text-over">当前选中：共{{sheetLength}}条数据</span>
                </div>
              </div>
              <div class="operate-button">
                <el-button @click="next" class="btn" type="primary">下一步</el-button>
              </div>
            </el-main>
          </el-tab-pane>
          <el-tab-pane name="2">
            <div class="second">
              <div class="form-input" style="
                height: 220px;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <el-checkbox v-model="uploaderData.autoCreateZzyUser">导入数据存在未生成会员信息时，自动生成会员账号</el-checkbox>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn" type="primary">上一步</el-button>
                <el-button @click="next" class="btn" type="primary">开始导入</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="3">
            <div class="fifth">
              <div class="container">
                <div><i class="el-icon-success"></i></div>
                <div class="import-result">
                  <p class="total">
                    <span>导入成功{{importResult.successNum}}条</span> &nbsp;&nbsp;&nbsp;
                    <span v-if="importResult.failedNum">导入失败{{importResult.failedNum}}条</span>
                    <br ><br >
                    <span>已生成{{importResult.zzyUserSuccessNum || 0}}个新会员账号</span>
                  </p>
                </div>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn">继续导入</el-button>
                <el-button @click="next" class="btn" type="primary">完成导入</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-main>
  </custom-dialog>
`
  /***
   *  向导式流程
   *  [title] 每个步骤的标题
   *  [prev，next]没有就会调默认的
   *  这里面写也就相当于重写了
   */
  const flow = {
    step1: {
      title: '选择导入文件',
      next() {
        const $upload = this.$refs.upload
        if (!$upload.uploadFiles.length)
          return this.$message.error('请先选择文件')
        if (this.sheetLength < 1)
          return this.$message.error('您选择的sheet没有数据！')
        this.currentStep++
      }
    },
    step2: {
      title: '导入生成会员',
      next() {
        this.currentStep++;
        this.startImport()
      }
    },
    step3: {
      title: '导入结果',
      //这是继续导入
      prev() {
        this.currentStep = 1
      },
      next() {
        this.close();
        this.$emit('finish');
      },
    }
  }
  // 组件对象
  window.ImportDialogScore = {
    template: ImportDialogTemplate,
    components: {
      customDialog,
    },
    props: {
      api: '',
      tpl: '',
    },
    data() {
      return {
        show: false,
        // overview: false,
        reading: false,
        importing: false,
        // groupShow: false,
        // addGroupShow: false,
        importMsg: '正在导入',
        importForm: {
        },
        uploaderData: {
          autoCreateZzyUser: false, //  是否自动生成会员 true\false
          sheetNo: '',
        },
        progress: 0,
        currentFileName: '',
        currentStep: 1,
        tips: '请选择需要导入的excel文件',
        sheetName: '选择sheet',
        sheetNames: [],
        sheets: {},
        sheetLength: 0,
        fileList: [],
        timer: null,
        // fu_forcibly_save_client: getAuthority('fu_forcibly_save_client'),
        importResult: {
          successNum: 0,   // 成功记录数
          failedNum: 0,    // 失败记录数
          zzyUserSuccessNum: 0, // 成功生成的会员数量
        },
        message: null,
        debounceTimer:null, // 防抖器
        importFile: null,   // 当前导入成功的文件
        isAllowGetProgress: false,
      }
    },
    computed: {
      currentStepName: {
        set(value) {
          this.currentStep = Number(value)
        },
        get() {
          return this.currentStep + ''
        }
      },
      importButtonText() {
        return this.importing ? '正在导入中...' : '开始导入'
      },
      title() {
        return flow['step' + this.currentStepName].title
      }
    },
    methods: {
      debounceMethods(func,...args){
        let context = this;
        let callNow = !this.debounceTimer;    //是否立即执行
        if (this.debounceTimer) clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          this.debounceTimer = null;
        },500)
        if(callNow){
          func.apply(context,args)
        }else{
          console.log('操作过快 ! 已阻止 !')
        }
      },
      next() {
        const that = this
        const _next = flow['step' + this.currentStepName].next
        if (Number(this.currentStepName) === 2) { // 导入
          _next ? this.debounceMethods(_next, that) : this.currentStep++
        } else {
          _next ? _next.call(that, ...arguments) : this.currentStep++
        }
      },
      prev() {
        const _prev = flow['step' + this.currentStepName].prev
        _prev ? _prev.call(this) : this.currentStep--
      },
      open() {
        this.show = true;
        // reset in close already
      },
      close() {
        if (this.importing)
          return this.$message.warning('正在导入，请勿关闭')
        this.show = false
        setTimeout(_=>{
          //深度重置数据
          this.resetImportData(false, true)
        },0)
      },
      clear(...keys) {
        keys.forEach(key => this.$set(this.importForm, key, ''))
      },
      fileChange(file, fileList) {
        if (file.status === 'ready' && !this.importing) {
          this.reading = true
          this.currentFileName = file.name
          this.tips = `当前选择：${file.name}`
          this.readExcel([file.raw]).then(data => {
            const _data = data[0]
            this.sheetNames = _data.SheetNames
            this.sheets = _data.Sheets
            this.selectSheet(1) //默认选择第一条
            this.reading = false
          })
        }
      },
      /***
       * e.g.:
       * document.getElementById('file').onchange = function(){
       *    readExcel(this.files).then(data=>{
       *      console.log(data)
       *    })
       *  }
       * @param files
       * @param rABS
       * @returns {Promise}
       */
      readExcel(files, rABS=false) {
        function bin2str(data) {
          let o = "",
            l = 0,
            w = 10240;
          for(; l < data.byteLength / w; ++l) o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)));
          o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)));
          return o;
        }
        return new Promise((resolve, reject)=>{
          if(!window.XLSX)reject(new Error('Require Import SheetJS! More Information:http://sheetjs.com'))
          const LOADER = window.XLSXX ? XLSXX: XLSX
          const filesLength = files.length
          const resultList = []
          let cursor = 0
          const handleLoader = e=>{
            try {
              const result = e.target.result
              resultList.push(rABS ? LOADER.read(btoa(bin2str(result)), {type: 'base64'}) :
                LOADER.read(result, {type: 'binary'}))
              if(++cursor === filesLength)
                resolve(resultList)
            }catch (e) {reject(e)}
          }
          [...files].forEach(file=>{
            const reader = new FileReader()
            reader[rABS ? 'readAsArrayBuffer' : 'readAsBinaryString'](file)
            reader.onload = handleLoader
          })
        })
      },
      startImport() {
        this.importing = true;
        this.importMsg = '导入中 ... ';
        this.$refs.upload.submit()
      },
      importSuccess(resp, file, fileList) {
        this.importing = false;
        this.importFile = file;
        const $this = this
        // this.isAllowGetProgress = true
        if (resp.state !== 1){
          return $this.$alert(resp.msg, '错误', {
            type: 'error', callback() {
              $this.resetImportData()
            }
          })
        } else {
          setTimeout(() => {
            this.importResult.successNum = +(resp.data.successNum || '')
            this.importResult.failedNum = +(resp.data.failNum || '')
            this.importResult.zzyUserSuccessNum = +(resp.data.zzyUserSuccessNum || '')
            this.importMsg = '导入成功';
            $this.resetImportData(true);
          })
        }
      },
      importError(err, file, fileList) {
        this.importing = false
        this.importFile = file;
        this.importMsg = '导入失败';
        this.$message.error('导入失败！')
        this.resetImportData()
      },
      // requestTeamList() {
      //   Axios.post('/workTeam/getList').then(({data: res}) => {
      //     this.teamList = res
      //   }).catch(() => this.$message.error('团队列表获取失败！'))
      // },
      // requestOperators(workTeamId) {
      //   Axios.post('/operator/selectByMap', JSON2FormData({workTeamId})).then(({data: res}) => {
      //     this.operatorList = res
      //   }).catch(() => this.$message.error('操作员列表获取失败！'))
      // },
      // requestGroupData(key, fWorkTeamId) {
      //   const queryData = {
      //     fClientTypeCode: 'B_CLIENT',
      //     fWorkTeamId
      //   }
      //   Axios.post('/workteamMenber/getGroupByTeamAndOper', JSON2FormData(queryData)).then(({data: res}) => {
      //     this[key] = res
      //   }).catch(() => this.$message.error('分组列表获取失败！'))
      // },
      groupNodeClick(keyPrefix, args) {
        const data = args[0]
        const node = args[1]
        if (node.level === 1) return
        this.$set(this.importForm, keyPrefix + 'Id', data['id'])
        this.$set(this.importForm, keyPrefix + 'Name', data['text'])
        if (node.level > 1) this[keyPrefix + 'Show'] = false
      },
      selectSheet(val) {
        this.uploaderData.sheetNo = val
        this.sheetName = this.sheetNames[val - 1]
        // this.importForm.batchName = this.createBatchName()
        try {
          //解析单元格范围
          const range = XLSX.utils.decode_range(this.sheets[this.sheetName]['!ref'])
          //获取行数（数据条数不包含表头）
          this.sheetLength = range.e.r - range.s.r
        } catch (e) {
          console.warn(e.message)
          this.sheetLength = 0
        }
        if (this.sheetLength < 1) this.sheetLength = 0
      },
      resetImportData(success = false, deep = false) {
        //上传后的简单的重置几个数据
        if (this.importFile) {
          let file = this.importFile
          file.status = 'ready'
          this.fileList = [file]
        }
        this.timer && clearInterval(this.timer)
        this.importing = false
        this.importMsg = '正在导入'
        this.progress = 0
        this.currentStep = success ? 3 : 1
        this.message && this.message.close()

        //深度重置数据
        if (deep) {
          this.clear(...Object.keys(this.importForm))
          // const initData = {
          //   merge_tel_auth: true,
          //   merge_source_auth: true,
          //   merge_keyMark_auth: true,
          //   merge_productType_auth: true,
          //   repeat_auth: '2', //重复规则
          //   merge_auth: '1',  //合并规则
          // }
          // Object.keys(initData).forEach(key => this.$set(this.importForm, key, initData[key]))
          this.uploadData = {
            autoCreateZzyUser: false, //  是否自动生成会员 true\false
            sheetNo: '',
          }
          this.tips = '请选择需要导入的excel文件'
          this.sheetName = '选择sheet'
          this.sheetNames = []
          this.sheets = {}
          this.sheetLength = 0
          this.$refs.upload.clearFiles() //这句清除文件的
        }
      },
      // loadGroup(teamId) {
      //   if (!teamId)
      //     return this.clear('addGroupId', 'authState', 'salesmanId', 'addGroupName')
      //   this.clear('addGroupId', 'addGroupName')
      //   this.requestGroupData('teamGroupData', teamId)
      //   this.queryAuthRuleByPerson().then(()=>{})
      // },
      downloadTemplate() {
        window.open(this.tpl, '_blank')
      },
      // createBatchName() {
      //   const time = (new Date()).format('yyyy-MM-dd hh:mm:ss')
      //   const filename = this.currentFileName.replace('.xls', '').replace('.xlsx', '')
      //   return `${time}${filename}${this.sheetName}`
      // },
      // getBatchId() {
      //   return new Promise((resolve, reject) => {
      //     Axios.post('/client/getBatchId').then(({data: res}) => {
      //       if (res.state !== 1) return this.$message.error('数据加载失败！')
      //       resolve(res.data)
      //     }).catch(e => {
      //       reject(e)
      //     })
      //   })
      // },
      // batchWeighting() {
      //   this.$emit('batch-weighting', this.uploaderData.serialNum)
      //   this.close(true)
      // },
      // async queryAuthRuleByPerson() {
      //   try {
      //     const {addWorkTeamId: workTeamId, salesmanId, authState: authType} = this.importForm
      //     if(!workTeamId || !salesmanId || !authType || this.message)return // 跳过检测
      //     const postData = {
      //       workTeamId,
      //       salesmanId,
      //       authType,
      //       clientTypeCode: 'B_CLIENT'
      //     }
      //     const authStateName = (this.authStateList.find(({id}) => authType === id) || {})['text'] || '独占'
      //     const {data} = await Axios.post('/client/queryAuthRuleByPerson', JSON2FormData(postData))
      //     if (data && data.allowAmount > 0) {
      //       this.message = this.$message({
      //         message: `还可${authStateName}数量${data.allowAmount}，本次导入如超出，将全部保存为公共客户！`,
      //         duration: 30 * 1e3, // 30s
      //         showClose: !0,
      //         offset: 50,
      //         type: 'info',
      //         onClose:() => this.message = null
      //       })
      //     }
      //   } catch (e) {
      //     e && e !== 'cancel' && console.warn(e)
      //   }
      // },
      // readImportDetail(){
      //   const batchId = this.uploaderData.serialNum
      //   const queryString = `?queryType=import&teamId=${teamId}&extend=${btoa(encodeURIComponent(JSON.stringify({batchId})))}`
      //   FinalLinkOpen(`InfoLogger/index.html${queryString}`, null, true, false, '数据导入日志')
      // },
      // readLinkmanImportDetail(batchId) {
      //   FinalLinkOpen('linkman-list.html?teamId=' + teamId + '&origin=clientList&import_batchId=' + batchId, null, true, false, '联系人')
      // }
    }
  }
})()