;!(function () {
  // 组件模板
  const ImportDialogTemplate = `
  <custom-dialog :title="importing ? '正在导入...' : title" :show="showImportClient" width="600px" id="importDialog" @before-close="close()">
    <el-main slot="body" class="dialog-body">
      <el-form size="mini" label-width="120px">
        <el-tabs v-model="currentStepName">
          <el-tab-pane 
            name="1"
            v-loading="reading"
            element-loading-text="正在读取文件..."
            element-loading-spinner="el-icon-loading">
            <el-main class="first">
              <div 
                style="position:relative" 
                :style="{marginBottom: showImportSelector ? '10px': void 0,marginTop: showImportSelector ? '35px': void 0}">
                <el-radio-group v-if="showImportSelector" style="position: absolute;top: -60px" v-model="importForm.updateById" size="small">
                  <el-radio-button label="false">普通导入</el-radio-button>
                  <el-radio-button label="true">ID关联更新导入</el-radio-button>
                </el-radio-group>
                <el-button class="btn" type="info" @click="downloadTemplate">下载导入模板</el-button>
                <div v-if="isUpdateById" style="position: absolute;" class="el-upload__tip">
                  ID关联更新导入时，导入模板中的<template v-if="!isLinkman">客户ID或</template>联系人ID必须进行填写后，<br>才会对对应ID的数据进行导入更新
                </div>
              </div>
              <div class="select-file" :style="{marginBottom: showImportSelector ? '60px': void 0,paddingTop: showImportSelector ? '40px': void 0}">
                <el-upload 
                  ref="upload"
                  action="/eip-web-crm/client/importClientNew"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                  :data="uploaderData"
                  :show-file-list="false"
                  :file-list="fileList"
                  :auto-upload="false"
                  :on-success="importSuccess"
                  :on-error="importError"
                  :on-change="fileChange">
                  <el-button slot="trigger" class="btn" @click="$refs.upload.clearFiles()" type="warning">选择文件
                  </el-button>
                  <div slot="tip" class="el-upload__tip text-over" style="max-width: 280px" :title="tips">{{tips}}</div>
                </el-upload>
                <div class="dropdown">
                  <el-dropdown @command="selectSheet">
                    <el-button type="warning" class="btn">{{sheetName}}<i
                        class="el-icon-arrow-down el-icon--right"></i></el-button>
                    <el-dropdown-menu slot="dropdown" :class="{'import-hide':!sheetNames.length}">
                      <el-dropdown-item
                        v-for="(s, index) in sheetNames"
                        class="right-dropdown"
                        :key="index"
                        :command="index + 1">
                        {{s}}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <span class="el-upload__tip text-over">当前选中：共{{sheetLength}}条数据</span>
                </div>
              </div>
              <div class="operate-button">
                <el-checkbox class="pretreatment" v-model="importForm.pretreatment">导入之前预览数据</el-checkbox>
                <el-button @click="next" class="btn" type="primary">下一步</el-button>
              </div>
            </el-main>
          </el-tab-pane>
          <el-tab-pane name="2">
            <div class="second">
              <div class="form-input">
                <el-form-item exculde label="批次名称">
                  <el-input v-model="importForm.batchName"></el-input>
                </el-form-item>
                <el-form-item exculde label="合并规则" style="position: relative;">
                  <el-select v-model="importForm.merge_auth">
                    <el-option value="1" label="以第一次录入数据为准"></el-option>
                    <el-option value="2" label="以最后一次录入数据为准"></el-option>
                  </el-select>
                  <span class="prompt" v-if="isLinkman">联系人姓名、地址等字段的合并规则</span>
                  <span class="prompt" v-else>公司名称、地址等字段的合并规则</span>
                </el-form-item>
                <el-form-item label="电话、电子邮箱">
                  <el-checkbox v-model="importForm.merge_tel_auth">附加</el-checkbox>
                </el-form-item>
                <el-form-item label="客户来源">
                  <el-checkbox v-model="importForm.merge_source_auth">附加</el-checkbox>
                </el-form-item>
                <template v-if="!isLinkman">
                  <el-form-item label="重点市场">
                    <el-checkbox v-model="importForm.merge_keyMark_auth">附加</el-checkbox>
                  </el-form-item>
                  <el-form-item label="产品类别">
                      <el-checkbox v-model="importForm.merge_productType_auth">附加</el-checkbox>
                  </el-form-item>
                </template>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn" type="primary">上一步</el-button>
                <el-button @click="next" class="btn" type="primary">下一步</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="3">
            <div class="third">
              <div class="container">
                <el-form-item label="总览客户分组">
                  <el-popover v-model="groupShow" popper-class="input-popper" placement="bottom-start" width="175"
                              trigger="click">
                    <el-input slot="reference" v-model="importForm.groupName" placeholder="总览客户分组" readonly clearable>
                      <i slot="suffix" class="el-input__icon el-icon-delete"
                         @click.stop="clear('groupId', groupName)"></i>
                    </el-input>
                    <custom-tree nodename="总览客户分组" :expand-on-click-node="false" :datalist="overviewGroupData"
                                 @search-table="groupNodeClick('group', arguments)">
                    </custom-tree>
                  </el-popover>
                </el-form-item>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn" type="primary">上一步</el-button>
                <el-button @click="next" class="btn" type="primary">下一步</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="4">
            <div class="fourth">
              <div class="container" v-if="importing">
                <el-progress :text-inside="true" :stroke-width="16" :percentage="progress"></el-progress>
                <p style="text-align: center;margin: 5px 0;">
                <i class="el-icon-loading el-button--text"></i>
                {{importMsg}}</p>
              </div>
              <div class="form-input" v-else>
                <el-form-item label-width="140px" label="指派到团队" v-if="overview">
                  <el-select v-model="importForm.addWorkTeamId" placeholder="请选择团队" filterable default-first-option clearable  @change="loadGroup">
                    <el-option v-for="team in teamList" :label="team.text" :value="team.id" :key="team.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <div class="el-form-item" style="height:29px;" v-else></div>
                <el-form-item label-width="140px" label="团队客户分组">
                  <el-popover v-model="addGroupShow"
                              :disabled="!importForm.addWorkTeamId"
                              popper-class="input-popper"
                              placement="bottom-start"
                              width="175"
                              trigger="click">
                    <el-input slot="reference"
                      v-model="importForm.addGroupName"
                      :disabled="!importForm.addWorkTeamId"
                      placeholder="团队客户分组"
                      readonly
                      clearable>
                      <i slot="suffix"
                         class="el-input__icon el-icon-delete"
                         @click.stop="importForm.addWorkTeamId && clear('addGroupId', 'addGroupName')"></i>
                    </el-input>
                    <custom-tree nodename="团队客户分组" :expand-on-click-node="false" :datalist="teamGroupData"
                                 @search-table="groupNodeClick('addGroup', arguments)">
                    </custom-tree>
                  </el-popover>
                </el-form-item>
                <el-form-item label-width="140px" label="授权状态">
                  <el-select v-model="importForm.authState" @change="v=>{
                    v <= 0 && clear('salesmanId');
                    queryAuthRuleByPerson()
                  }" clearable placeholder="请选择授权状态">
                    <el-option v-for="item in authStateList" :key="item.id" :label="item.text" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label-width="140px" label="业务员">
                  <el-select v-model="importForm.salesmanId" @change="queryAuthRuleByPerson" filterable default-first-option clearable :disabled="importForm.authState <= 0" placeholder="请选择业务员">
                    <el-option v-for="oper in operatorList" :label="oper.text" :key="oper.id" :value="oper.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item exculde label-width="140px" label="授权冲突时合并规则" style="position: relative;">
                  <el-select v-model="importForm.repeat_auth">
                    <el-option value="2" label="严格排重合并"></el-option>
                    <el-option value="1" v-if="fu_forcibly_save_client" label="先重复后排重合并"></el-option>
                  </el-select>
                  <span class="prompt">{{repeat_auth_text}}</span>
                </el-form-item>
                <div class="el-form-item" style="height:29px;" v-else></div>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn" :disabled="importing" type="primary">上一步</el-button>
                <el-button @click="next" class="btn" :loading="importing" type="primary">{{importButtonText}}</el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="5">
            <div class="fifth">
              <div class="container">
                <div><i class="el-icon-success"></i></div>
                <div class="import-result">
                  <p class="total">
                    <span>导入成功{{importResult.successNum}}条</span>
                    <span v-if="importResult.failedNum">导入失败{{importResult.failedNum}}条</span>
                  </p>
                  <template v-if="!isLinkman">
                    <p>公司信息</p>
                    <p class="details">
                      <span>新增 <span class="el-button--text">{{importResult.addNum}}</span></span>
                      <span>团队内合并 <span class="el-button--text">{{importResult.mergeNum}}</span></span>
                      <span>总览合并引用 <span class="el-button--text">{{importResult.mergeNumFromOverview}}</span></span>
                    </p>
                    </template>
                  <p>联系人信息</p>
                  <p class="details">
                    <span>新增 <span class="el-button--text">{{importResult.linkman_addNum}}</span></span>
                    <span>团队内合并 <span class="el-button--text">{{importResult.linkman_mergeNum}}</span></span>
                    <span>总览合并引用 <span class="el-button--text">{{importResult.linkman_mergeNumFromOverview}}</span></span>
                  </p>
                </div>
              </div>
              <div class="operate-button">
                <el-button @click="prev" class="btn">继续导入</el-button>
                <el-button @click="readImportDetail" class="btn">查看导入日志</el-button>
                <el-button @click="batchWeighting" class="btn" v-if="importForm.repeat_auth === '1'">批量排重</el-button>
                <el-dropdown v-if="!isLinkman" @command="next">
                  <el-button class="btn" type="primary" style="margin-left: 20px">
                    查看导入数据<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="B_CLIENT">公司客户</el-dropdown-item>
                    <el-dropdown-item command="C_LINKMAN">联系人</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-button v-else  @click="next" class="btn" type="primary">查看导入数据</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <custom-dialog
        class="resizable-dialog"
        @opened="getClientImportPretreatment"
        title="导入前预览" append2body
        width="1000px"
        :show="showPretreatment"
        @before-close="closePretreatment">
        <el-main slot="body" style="padding: 30px 20px;max-height: 75vh" class="dialog-body" v-if="showPretreatment">
          <el-header>
            <el-button style="width:135px;border-radius:3px" size="small" @click="exportPretreatment">导出预处理数据</el-button>
            <span v-if="pretreatmentRepeat.companyCount" style="margin-left: 10px;">
              <template v-if="+teamId !== -1">与总览</template>已有公司重复
              <span @click="readPretreatmentRepeatDetail('B_CLIENT', true)" style="color: #409EFF;padding: 0 10px;cursor: pointer">{{pretreatmentRepeat.companyCount}}</span>
            </span>
            <span v-if="pretreatmentRepeat.teamCompanyCount" style="margin-left: 10px;">
              当前团队公司重复
              <span @click="readPretreatmentRepeatDetail('B_CLIENT')" style="color: #409EFF;padding: 0 10px;cursor: pointer">{{pretreatmentRepeat.teamCompanyCount}}</span>
            </span>
            <span v-if="pretreatmentRepeat.linkmanCount" style="margin-left: 10px;">
              <template v-if="+teamId !== -1">与总览</template>已有联系人重复
              <span @click="readPretreatmentRepeatDetail('C_LINKMAN', true)" style="color: #409EFF;padding: 0 10px;cursor: pointer">{{pretreatmentRepeat.linkmanCount}}</span>
            </span>
            <span v-if="pretreatmentRepeat.teamLinkmanCount" style="margin-left: 10px;">
              当前团队联系人重复
              <span @click="readPretreatmentRepeatDetail('C_LINKMAN')" style="color: #409EFF;padding: 0 10px;cursor: pointer">{{pretreatmentRepeat.teamLinkmanCount}}</span>
            </span>
          </el-header>
          <div style="padding: 15px 0 25px; height: 400px">
            <div id="pretreatment-table"></div>
          </div>
          <div style="display: flex;width: 280px;justify-content: space-between;margin: auto">
            <el-button style="width:125px;border-radius:3px" size="small" @click="stopImport">中止导入</el-button>
            <el-button style="width:125px;border-radius:3px" size="small" type="primary" @click="confirmImport">确认导入</el-button>
          </div>
        </el-main>
      </custom-dialog>
    </el-main>
  </custom-dialog>
`
  /***
   *  向导式流程
   *  [title] 每个步骤的标题
   *  [prev，next]没有就会调默认的
   *  这里面写也就相当于重写了
   */
  const flow = {
    step1: {
      title: '选择导入模式及文件',
      next() {
        const $upload = this.$refs.upload
        if (!$upload.uploadFiles.length)
          return this.$message.error('请先选择文件')
        if (this.sheetLength < 1)
          return this.$message.error('您选择的sheet没有数据！')
        // 如果是更新，就直接开始导入
        if (this.isUpdateById) {
          this.$set(this.importForm, 'batchName', this.createBatchName())
          this.currentStep = 4
          //  这里不是递归调用，因为此时的this指向的是组件实例而不是flow
          return this.next()
        }
        this.currentStep++
      }
    },
    step2: {
      title: '设置批次名称和合并规则',
      next() {
        if (!this.importForm.batchName)
          return this.$message.error('请输入批次名称')
        this.currentStep = this.currentStep + (this.overview ? 1 : 2)
      }
    },
    step3: {
      title: '选择总览分组',
    },
    step4: {
      title: '指派团队及授权',
      prev() {
        this.currentStep = this.currentStep - (this.overview ? 1 : 2)
      },
      //这里是开始导入的按钮了
      next() {
        //如果授权状态是指派或领用，那么操作员必填
        if (this.importForm.authState > 0 && !this.importForm.salesmanId)
          return this.$message.error('授权状态为领用或独占时，必须选择操作员！')
        this.importForm.nowWorkTeamId = teamId
        this.uploaderData = {...this.importForm, importLinkman: this.isLinkman, operEntrance: '公司客户列表'}
        //这里转换合并规则相关参数
        Object.keys(this.uploaderData).forEach(key => {
          if (/merge_(.*?)_auth/.test(key)) {
            this.uploaderData[key] = this.uploaderData[key] ? 1 : 2
          }
        })
        this.queryAuthRuleByPerson().then(()=>{})
        this.getBatchId().then(serialNum => {
          this.uploaderData.serialNum = serialNum
          this.startImport(serialNum)
        }).catch(e => e)
      }
    },
    step5: {
      title: '导入结果',
      //这是继续导入
      prev() {
        this.currentStep = 1
      },
      next(type) {
        const serialNum = this.uploaderData.serialNum
        if (type === 'C_LINKMAN') return this.readLinkmanImportDetail(serialNum)
        this.$emit('view-data', serialNum)
        this.close(true)
      },
    }
  }

  // 由ChatGPT所写
  function debounce(func, wait, immediate) {
    let timeout;
    return function () {
      const context = this, args = arguments;
      const later = function () {
        timeout = null;
        if (!immediate) func.apply(context, args)
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args)
    }
  }

  // 组件对象
  window.ImportDialog = {
    template: ImportDialogTemplate,
    props: {
      isLinkman: Boolean
    },
    data() {
      return {
        showImportClient: false,
        overview: false,
        reading: false,
        importing: false,
        groupShow: false,
        addGroupShow: false,
        importMsg: '正在导入',
        importForm: {
          merge_tel_auth: true,
          merge_source_auth: true,
          merge_keyMark_auth: true,
          merge_productType_auth: true,
          repeat_auth: '2', //重复规则
          merge_auth: '1',  //合并规则
          nowWorkTeamId: '', //当前团队ID
          batchName: '',    // 批次名称
          sheetNo: '',
          groupId: '',
          addWorkTeamId: '',
          addGroupId: '',
          authState: '',
          salesmanId: '',
          pretreatment: false,
          updateById: 'false',
          //多余的用作展示的数据
          groupName: '',
          addGroupName: '',
        },
        uploaderData: {},
        progress: 0,
        currentFileName: '',
        currentStep: 1,
        tips: '请选择需要导入的excel文件',
        sheetName: '选择sheet',
        sheetNames: [],
        sheets: {},
        sheetLength: 0,
        authStateList: [
          {id: 0, text: '公共客户'},
          {id: 1, text: '领用'},
          {id: 3, text: '独占'},
        ],
        operatorList: [],
        teamList: [],
        overviewGroupData: [],
        teamGroupData: [],
        fileList: [],
        timer: null,
        fu_forcibly_save_client: getAuthority('fu_forcibly_save_client'),
        importResult: {
          successNum: 0,   //成功记录数
          failedNum: 0,    //失败记录数
          addNum: 0,       //公司 新增记录数
          mergeNum: 0,     //公司 团队内合并记录数
          mergeNumFromOverview: 0, //公司 数据总览引用合并记录数
          linkman_addNum: 0,       // 联系人 新增记录数
          linkman_mergeNum: 0,     //联系人 团队内合并记录数
          linkman_mergeNumFromOverview: 0 //联系人 数据总览引用合并记录数
        },
        message: null,
        debounceTimer:null, // 防抖器
        importFile: null,   // 当前导入成功的文件
        isAllowGetProgress: false,
        showPretreatment: false,
        pretreatmentTableColumns: [],
        pretreatmentRepeat: {
          companyCount: 0,
          linkmanCount: 0,
          teamCompanyCount: 0,
          teamLinkmanCount: 0
        }
      }
    },
    methods: {
      debounceMethods(func,...args){
        let context = this;
        let callNow = !this.debounceTimer;    //是否立即执行
        if (this.debounceTimer) clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          this.debounceTimer = null;
        },500)
        if(callNow){
          func.apply(context,args)
        }else{
          console.log('操作过快 ! 已阻止 !')
        }
      },
      next() {
        const that = this
        //如果没有写next方法就直接执行this.currentStep++（默认的下一步）
        const _next = flow['step' + this.currentStepName].next
        if (Number(this.currentStepName) === 4) { // 导入
          _next ? this.debounceMethods(_next, that) : this.currentStep++
        } else {
          _next ? _next.call(that, ...arguments) : this.currentStep++
        }
      },
      prev() {
        //如果没有写prev方法就直接执行this.currentStep++（默认的上一步）
        const _prev = flow['step' + this.currentStepName].prev
        _prev ? _prev.call(this) : this.currentStep--
      },
      open(overview) {
        this.overview = overview
        this.showImportClient = true
        if (overview) {
          this.requestTeamList()
          this.requestGroupData('overviewGroupData', teamId)
        } else {
          this.requestGroupData('teamGroupData', teamId)
          this.$set(this.importForm, 'addWorkTeamId', teamId)
        }
        this.requestOperators(teamId)
      },
      close(noRefresh) {
        if (this.importing)
          return this.$message.warning('正在导入，请勿关闭')
        this.showImportClient = false
        //深度重置数据
        this.resetImportData(false, true)
        !noRefresh && loadTable(upParam, 0) // 关闭弹窗，导入完成，刷新列表！
      },
      clear(...keys) {
        keys.forEach(key => this.$set(this.importForm, key, ''))
      },
      fileChange(file, fileList) {
        if (file.status === 'ready' && !this.importing) {
          this.reading = true
          this.currentFileName = file.name
          this.tips = `当前选择：${file.name}`
          readExcel([file.raw]).then(data => {
            const _data = data[0]
            this.sheetNames = _data.SheetNames
            this.sheets = _data.Sheets
            this.selectSheet(1) //默认选择第一条
            this.reading = false
          })
        }
      },
      startImport(serialNum) {
        this.importing = true
        const $upload = this.$refs.upload
        $upload.submit()
        const requestData = JSON2FormData({serialNum})
        this.isAllowGetProgress = false
        this.timer = setInterval(() => {
          if (!this.isAllowGetProgress) return
          Axios.post('/client/getProgress', requestData).then(({data: res}) => {
            if (res.state === 1) {
              if(res.data){ // 导入中
                const {progress, msg} = res.data
                this.progress = progress
                this.importMsg = msg
              }else{ // 导入已完成
                this.progress = 100;
                this.importMsg = '导入成功';
                clearInterval(this.timer);
                setTimeout(() => {
                  this.getImportResult()
                }, 300);
              }
            }else{
              this.importMsg = msg
              clearInterval(this.timer);
            }
          })
        }, 2000)
      },
      getImportResult() {
        if (this.importForm.pretreatment) {
          this.getPretreatmentMergeClientCount(this.uploaderData.serialNum).finally(Boolean)
          this.showPretreatment = true
          return
        }
        const requestData = JSON2FormData({batchId: this.uploaderData.serialNum})
        Axios.post('/batch/getImportClientResult', requestData).then( ({data:res}) => {
          if (res.state === 1) {
            const {
              successNum,   //成功记录数
              failedNum,    //失败记录数
              addNum,       //公司 新增记录数
              mergeNum,     //公司 团队内合并记录数
              mergeNumFromOverview, //公司 数据总览引用合并记录数
              linkman_addNum,       // 联系人 新增记录数
              linkman_mergeNum,     //联系人 团队内合并记录数
              linkman_mergeNumFromOverview //联系人 数据总览引用合并记录数
            } = res.data
            this.importResult = {
              successNum,   //成功记录数
              failedNum,    //失败记录数
              addNum,       //公司 新增记录数
              mergeNum,     //公司 团队内合并记录数
              mergeNumFromOverview, //公司 数据总览引用合并记录数
              linkman_addNum,       // 联系人 新增记录数
              linkman_mergeNum,     //联系人 团队内合并记录数
              linkman_mergeNumFromOverview //联系人 数据总览引用合并记录数
            }
            // this.importResult = resp.data
            //这是为了先执行fileChange，所以让他到下一事件循环在执行
          }else{
            this.$message.error('上传成功,但获取上传结果失败！')
          }
          this.$nextTick(this.resetImportData.bind(this, true))
        }).catch(() => {
          this.$message.error('上传成功,但获取上传结果失败！')
          this.$nextTick(this.resetImportData.bind(this, true))
        })
      },
      async exportPretreatment() {
        const loading = this.$loading({
          fullscreen: !0,
          text: '正在导出...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.3)',
          lock: !0,
          body: !0
        })
        const sleep = delay => new Promise((resolve) => {setTimeout(resolve, delay)})
        const batchId = this.uploaderData.serialNum
        const progress = async () => {
          const {data} = await Axios.post('/batchDtl/getExportPretreatmentProgress', JSON2FormData({batchId}))
          if (!data.data) return
          loading.setText(data.data.msg)
          await sleep(1000)
          await progress()
        }
        const timer = setTimeout(() => progress(batchId), 1000)
        try {
          const {data} = await axios.post('/eip-web-crm/batchDtl/exportClientImportPretreatment', JSON2FormData({batchId}), {responseType: 'blob'})
          const href = URL.createObjectURL(data)
          const link = document.createElement('a')
          link.href = href
          link.setAttribute('download','导出与处理数据-导出日期' + top.$GlobalDefine.getSystemTime('yyyy_MM_dd hh_mm_ss') + '.xlsx')
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(href)
          document.body.removeChild(link)
        } catch (e) {
          this.$alert('导出失败', '错误', {type: 'error'})
        } finally {
          clearTimeout(timer)
          loading && loading.close()
        }
      },
      async closePretreatment() {
        try {
          await this.$confirm('关闭弹窗将中止导入，确定关闭吗？', '提示', {type: 'info'})
          await this.stopImport()
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      async getPretreatmentMergeClientCount(batchId) {
        try {
          const {data} = await Axios.post('/batchDtl/getPretreatmentMergeClientCount', JSON2FormData({batchId, workTeamId: teamId}))
          this.pretreatmentRepeat = data.data || {}
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          console.warn(e)
        }
      },
      async getClientImportPretreatment() {
        const loading = this.$loading({
          fullscreen: !0,
          text: '加载中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        try {
          const $table = $('#pretreatment-table')
          const vm = this
          const batchId = this.uploaderData.serialNum
          const {data} = await Axios.post('/batchDtl/getClientImportPretreatment', JSON2FormData({
            batchId,
            rows: 1,
            page: 1
          }))
          const columns = (data.data || []).map(field => ({field, title: field, width: 100}))
          this.pretreatmentTableColumns = [columns]
          const fn = debounce(() => $table.datagrid('resize'), 300)
          new ResizeObserver(fn).observe(document.querySelector('.resizable-dialog .custom-dialog'))
          $table.datagrid({
            url: '/eip-web-crm/batchDtl/getClientImportPretreatment',
            method: 'post',
            rownumbers: true,
            singleSelect: true,
            fit: true,
            selectOnCheck: true,
            pageSize: 20,
            pagination: true,
            columns: this.pretreatmentTableColumns,
            pageList: [20, 50, 100, 500, 1000],
            queryParams: {batchId},
            onLoadSuccess(data) {
              if (data.state !== 1)
                return vm.$alert('查询预览数据失败', '错误', {type: 'error'})
            },
            onLoadError() {
              return vm.$alert('查询预览数据失败', '错误', {type: 'error'})
            }
          })
        } catch (e) {
          this.$alert('查询预览数据失败', '错误', {type: 'error'})
        } finally {
          loading && loading.close()
        }
      },
      async stopImport(tips) {
        try {
          if (tips) {
            await this.$confirm('确定中止导入吗？', '提示', {type: 'info'})
          }
          this.showPretreatment = false
          this.resetImportData()
        }catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      confirmImport() {
        this.showPretreatment = false
        this.uploaderData.pretreatment = this.importForm.pretreatment = false
        this.resetImportData()
        this.currentStep = 4
        this.startImport(this.uploaderData.serialNum)
      },
      importSuccess(resp, file, fileList) {
        const $this = this
        this.importFile = file;
        this.isAllowGetProgress = true
        if (resp.state !== 1){
          return $this.$alert(resp.msg, '错误', {
            type: 'error', callback() {
              $this.resetImportData()
            }
          })
        }
      },
      importError(err, file, fileList) {
        this.$message.error('导入失败！')
        this.importFile = file;
        this.resetImportData()
        this.importing = false
      },
      requestTeamList() {
        Axios.post('/workTeam/getList').then(({data: res}) => {
          this.teamList = res
        }).catch(() => this.$message.error('团队列表获取失败！'))
      },
      requestOperators(workTeamId) {
        Axios.post('/operator/selectByMap', JSON2FormData({workTeamId})).then(({data: res}) => {
          this.operatorList = res
        }).catch(() => this.$message.error('操作员列表获取失败！'))
      },
      requestGroupData(key, fWorkTeamId) {
        const queryData = {
          fClientTypeCode: 'B_CLIENT',
          fWorkTeamId
        }
        Axios.post('/workteamMenber/getGroupByTeamAndOper', JSON2FormData(queryData)).then(({data: res}) => {
          this[key] = res
        }).catch(() => this.$message.error('分组列表获取失败！'))
      },
      groupNodeClick(keyPrefix, args) {
        const data = args[0]
        const node = args[1]
        if (node.level === 1) return
        this.$set(this.importForm, keyPrefix + 'Id', data['id'])
        this.$set(this.importForm, keyPrefix + 'Name', data['text'])
        if (node.level > 1) this[keyPrefix + 'Show'] = false
      },
      selectSheet(val) {
        this.importForm.sheetNo = val
        this.sheetName = this.sheetNames[val - 1]
        this.importForm.batchName = this.createBatchName()
        try {
          //解析单元格范围
          const range = XLSX.utils.decode_range(this.sheets[this.sheetName]['!ref'])
          //获取行数（数据条数不包含表头）
          this.sheetLength = range.e.r - range.s.r
        } catch (e) {
          console.warn(e.message)
          this.sheetLength = 0
        }
        if (this.sheetLength < 1) this.sheetLength = 0
      },
      resetImportData(success = false, deep = false) {
        //上传后的简单的重置几个数据
        if (this.importFile) {
          let file = this.importFile
          file.status = 'ready'
          this.fileList = [file]
        }
        this.timer && clearInterval(this.timer)
        this.importing = false
        this.importMsg = '正在导入'
        this.progress = 0
        this.currentStep = success ? 5 : 1
        this.message && this.message.close()

        //深度重置数据
        if (deep) {
          this.clear(...Object.keys(this.importForm))
          const initData = {
            merge_tel_auth: true,
            merge_source_auth: true,
            merge_keyMark_auth: true,
            merge_productType_auth: true,
            repeat_auth: '2', //重复规则
            merge_auth: '1',  //合并规则
          }
          Object.keys(initData).forEach(key => this.$set(this.importForm, key, initData[key]))
          this.uploadData = {}
          this.tips = '请选择需要导入的excel文件'
          this.sheetName = '选择sheet'
          this.sheetNames = []
          this.sheets = {}
          this.sheetLength = 0
          this.$refs.upload.clearFiles() //这句清除文件的
        }
      },
      loadGroup(teamId) {
        if (!teamId)
          return this.clear('addGroupId', 'authState', 'salesmanId', 'addGroupName')
        this.clear('addGroupId', 'addGroupName')
        this.requestGroupData('teamGroupData', teamId)
        this.queryAuthRuleByPerson().then(()=>{})
      },
      downloadTemplate() {
        let templateFilename = this.isUpdateById ? '客户ID更新导入模板.xlsx' : '客户导入模板.xlsx'
        if (this.isLinkman) templateFilename = templateFilename.replace('客户', '联系人')
        window.open('../../excel/' + templateFilename, '_blank')
      },
      createBatchName() {
        const time = (new Date()).format('yyyy-MM-dd hh:mm:ss')
        const filename = this.currentFileName.replace('.xls', '').replace('.xlsx', '')
        return `${time}${filename}${this.sheetName}`
      },
      getBatchId() {
        return new Promise((resolve, reject) => {
          Axios.post('/client/getBatchId').then(({data: res}) => {
            if (res.state !== 1) return this.$message.error('数据加载失败！')
            resolve(res.data)
          }).catch(e => {
            reject(e)
          })
        })
      },
      batchWeighting() {
        this.$emit('batch-weighting', this.uploaderData.serialNum)
        this.close(true)
      },
      async queryAuthRuleByPerson() {
        try {
          const {addWorkTeamId: workTeamId, salesmanId, authState: authType} = this.importForm
          if(!workTeamId || !salesmanId || !authType || this.message)return // 跳过检测
          const postData = {
            workTeamId,
            salesmanId,
            authType,
            clientTypeCode: 'B_CLIENT'
          }
          const authStateName = (this.authStateList.find(({id}) => authType === id) || {})['text'] || '独占'
          const {data} = await Axios.post('/client/queryAuthRuleByPerson', JSON2FormData(postData))
          if (data && data.allowAmount > 0) {
            this.message = this.$message({
              message: `还可${authStateName}数量${data.allowAmount}，本次导入如超出，将全部保存为公共客户！`,
              duration: 30 * 1e3, // 30s
              showClose: !0,
              offset: 50,
              type: 'info',
              onClose:() => this.message = null
            })
          }
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      readImportDetail(){
        const batchId = this.uploaderData.serialNum
        const queryString = `?queryType=import&teamId=${teamId}&extend=${btoa(encodeURIComponent(JSON.stringify({batchId})))}`
        FinalLinkOpen(`InfoLogger/index.html${queryString}`, null, true, false, '数据导入日志')
      },
      readLinkmanImportDetail(batchId) {
        FinalLinkOpen('linkman-list.html?teamId=' + teamId + '&origin=clientList&import_batchId=' + batchId, null, true, false, '联系人')
      },
      readPretreatmentRepeatDetail(type, onlyAllowOverview = false) {
        const conf = {
          B_CLIENT: {
            title: '公司客户',
            page: 'client-list.html',
          },
          C_LINKMAN: {
            title: '联系人',
            page: 'linkman-list.html',
          }
        }
        const {page, title} = conf[type]
        const batchId = this.uploaderData.serialNum
        if (onlyAllowOverview && +teamId !== -1) {
          if (!getAuthority('fu_client_data_overview')  && !getAuthority('fu_team_data_kywy'))
            return this.$errorMsg('与总览重复数据请至公司总览环境下进行查看。', '提醒', 'warning')
          const url = page + '?teamId=-1&origin=import_pretreatment&batchId=' + batchId
          localStorage.setItem('NEXT_TASK', JSON.stringify({taskName: 'FinalLinkOpen', args: [url, null, true, false, title]}))
          const target = new URL(top.location.href)
          target.searchParams.set('teamId', '-1')
          return top.open(target, '_blank')
        }
        FinalLinkOpen(page + '?teamId=' + teamId + '&origin=import_pretreatment&batchId=' + batchId, null, true, false, title)
      }
    },
    computed: {
      currentStepName: {
        set(value) {
          this.currentStep = Number(value)
        },
        get() {
          return this.currentStep + ''
        }
      },
      repeat_auth_text() {
        const text = {
          '1': '已被他人领用或独占的数据将强制重复，不合并基本信息',
          '2': '已被他人领用或独占的数据将放弃领用或独占，但合并基本信息'
        }
        return text[this.importForm.repeat_auth]
      },
      importButtonText() {
        return this.importing ? '正在导入中...' : '开始导入'
      },
      title() {
        return flow['step' + this.currentStepName].title
      },
      isUpdateById() {
        return this.importForm.updateById === 'true'
      },
      showImportSelector() {
        return +top.teamId === -1
      }
    },
    components: {
      customDialog,
      CustomTree
    }
  }
})()

