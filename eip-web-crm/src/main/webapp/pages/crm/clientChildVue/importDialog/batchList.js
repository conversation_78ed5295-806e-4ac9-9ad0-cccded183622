;!(function (w) {
  const template = `
   <custom-dialog @closed="confirming=false" title="导入批次查询" append2body :show="show" width="573px" id="importBatch" @before-close="close">
      <el-main slot="body" class="dialog-body">
        <el-header class="dialog-header" height="40px">
          <div class="dialog-header-right">
            <el-button size="mini" icon="el-icon-circle-check" type="primary" :loading="confirming" @click="confirmBatch">
              {{confirming ? '加载中...' : '选定'}}
            </el-button>
          </div>
        </el-header>
        <el-main class="main">
          <el-table :data="tableData" v-loading="loading" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.1)"
            highlight-current-row @current-change="handleCurrentRowChange" @row-dblclick="confirmBatch" ref="batchTable"
            :show-header="false" border size="small" height="201px">
            <el-table-column width="40" type="index" :index="i=>(currentPage - 1) * 5 + i + 1"></el-table-column>
            <el-table-column width="40" type="selection"></el-table-column>
            <el-table-column prop="batchName"></el-table-column>
          </el-table>
          <el-pagination @current-change="handleCurrentChange" :current-page="currentPage" :page-size="5"
            hide-on-single-page layout="prev, pager, next" :total="total"></el-pagination>
        </el-main>
      </el-main>
    </custom-dialog>
  `
  w.BatchList = {
    template,
    data() {
      return {
        show: false,
        confirming: false,
        loading: false,
        currentPage: 1,
        total: 100,
        requestHistory: {
          types: '',
          workTeamId: '',
          operatorId: '',
          page: 1,
          rows: 5,
        },
        tableData: []
      }
    },
    methods: {
      confirmBatch() {
        this.confirming = true
        const selection = this.$refs.batchTable.selection
        if (selection.length < 1){
          this.confirming = false
          return this.$message.error('请先选择导入批次！')
        }
        this.$emit('finished', selection[0])
        this.close()
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.requestTableData()
      },
      handleCurrentRowChange(currentRow) {
        this.$refs.batchTable.clearSelection()
        this.$refs.batchTable.toggleRowSelection(currentRow, true)
      },
      requestTableData() {
        this.loading = true
        this.requestHistory.page = this.currentPage
        Axios.post('/batch/getPage', JSON2FormData(this.requestHistory)).then(({data: res}) => {
          if (res.state !== 1){
            this.loading = false
            return this.$message.error('批次数据加载失败！')
          }
          this.tableData = res.rows
          this.total = res.total
          this.loading = false
        }).catch(e => {
          this.$message.error('批次数据加载失败！')
          this.loading = false
          console.log(e)
        })
      },
      open(types) {
        this.show = true
        this.requestHistory.types = types
        this.requestHistory.workTeamId = teamId
        this.requestHistory.operatorId = operatorId
        this.requestTableData()
      },
      close() {
        this.show = false
      }
    },
    components: {
      customDialog
    }
  }
})(window)