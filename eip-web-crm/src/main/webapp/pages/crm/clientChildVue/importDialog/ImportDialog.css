/***
 本文件由 Easy-scss 自动生成
 */

.resizable-dialog .custom-dialog {
    overflow: hidden;
    resize: horizontal;
    min-width: 1000px;
}
#importDialog .dialog-body {
    padding: 0;
    height: 350px;
}

#importDialog .dialog-body .el-input__icon:hover {
    color: #2e82ff;
}

#importDialog .dialog-body .el-tabs__header.is-top {
    display: none;
}

#importDialog .dialog-body .operate-button {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#importDialog .dialog-body .operate-button .pretreatment {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
}

#importDialog .dialog-body .operate-button .btn + .btn {
    margin-left: 20px;
}

#importDialog .dialog-body .text-over {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#importDialog .dialog-body .btn {
    width: 140px;
    height: 34px;
    padding-top: 8px;
    padding-bottom: 8px;
}

#importDialog .dialog-body .prompt {
    position: absolute;
    top: 32px;
    left: 5px;
    font-size: 12px;
    line-height: 12px;
    width: 350px;
    color: #f74444;
}

#importDialog .dialog-body .first {
    padding: 50px 73px 30px;
    display: flex;
    flex-direction: column;
}

#importDialog .dialog-body .first > :nth-child(1) {
    margin-bottom: 45px;
}

#importDialog .dialog-body .first > :nth-child(2) {
    margin-bottom: 100px;
}

#importDialog .dialog-body .first .select-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#importDialog .dialog-body .first .select-file .el-upload.el-upload--text {
    text-align: left;
}

#importDialog .dialog-body .first .select-file .dropdown {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-basis: 160px;
}

#importDialog .dialog-body .second {
    padding: 25px 30px 30px;
}

#importDialog .dialog-body .second .form-input {
    margin-bottom: 40px;
}

#importDialog .dialog-body .second .form-input p.batch-num {
    margin: 0;
    font-size: 12px;
    line-height: 24px;
}

#importDialog .dialog-body .second .form-input .el-form-item:nth-of-type(1) {
    margin-bottom: 14px;
}

#importDialog .dialog-body .second .form-input .el-form-item:nth-of-type(2) {
    margin-bottom: 25px;
}

#importDialog .dialog-body .second .form-input .el-form-item:not([exculde]) {
    margin-bottom: 4px;
}

#importDialog .dialog-body .third {
    padding: 24px 30px 30px;
}

#importDialog .dialog-body .third .container,
#importDialog .dialog-body .fourth .container {
    height: 262px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#importDialog .dialog-body .fourth .container {
    flex-direction: column;
    height: 254px;
}

#importDialog .dialog-body .fourth .container .el-progress {
    width: 100%;
}

#importDialog .dialog-body .fourth {
    padding: 30px 40px 30px;
}

#importDialog .dialog-body .fourth .form-input {
    margin-bottom: 55px;
}

#importDialog .dialog-body .fourth .form-input .el-form-item {
    width: 420px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 14px;
}

#importDialog .dialog-body .fourth .form-input .el-input, #importDialog .dialog-body .fourth .form-input .el-select {
    width: 200px;
}

#importDialog .dialog-body .fourth .form-input .prompt {
    left: 0;
}

#importDialog .dialog-body .fifth {
    padding: 24px 30px 30px;
}

#importDialog .dialog-body .fifth .container {
    height: 262px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    font-size: 16px;
    flex: 1;
}

#importDialog .dialog-body .fifth .container div:first-child {
    width: 100px;
    text-align: center;
}

#importDialog .dialog-body .fifth .container div:last-child p {
    margin: 10px 0;
}

#importDialog .dialog-body .fifth .container div:first-child i {
    font-size: 70px;
    color: #67C23A;
}

#importDialog .dialog-body .fifth .container .import-result {
    font-size: 15px;
    color: #333;
}

#importDialog .dialog-body .fifth .container .import-result .total {
    font-size: 16px;
    margin-bottom: 16px;
}

#importDialog .dialog-body .fifth .container .import-result .details {
    color: #8D939D;
}

#importDialog .dialog-body .fifth .container .import-result .details .el-button--text {
    margin-right: 12px;
}

#importDialog .dialog-body .fifth .container .import-result .details > span:last-child .el-button--text {
    margin-right: 0;
}

.right-dropdown {
    width: 97px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.el-dropdown-menu.el-popper.import-hide {
    opacity: 0;
    z-index: -1;
}

[id^="el-popover-"] .el-tree {
    max-height: 200px;
    overflow: auto;
}