function generateBasicData(projectDetail, projectNumMapper) {
  // 变量信息
  const {
    projectId,
    projectName,
    exhibitCode,
    mainProjectId,
    projKindCode
  } = projectDetail || {}
  // 兼容encode
  const encode = encodeURIComponent || encodeURI || escape

  //生成链接地址
  function genLinkAddress(type, ...args) {
    const factory = {
      // 对象转成QueryString
      Object2QueryString(o) {
        o = o || {}
        return JSON.stringify(o).replace(/:/g, '=').replace(/,/g, '&').replace(/{/g, '?').replace(/}/g, '').replace(/"/g, '')
      },
      // 销售合同 提成计算等相关页面
      ProjectMarket(page, extend) {
        extend = extend || {}
        const searchObject = {
          teamId,
          mainProjectId,
          exhibitCode,
          page,
          extend: btoa(encode(JSON.stringify({projectId, projectName, ...extend})))
        }
        return 'ProjectMarket/sales-contract.html' + this.Object2QueryString(searchObject)
      },
      // 销售政策相关
      ExhibitEdit(_type,extend) {
        extend = extend || {}
        const searchObject = {
          teamId,
          mainProjectId,
          projectId,
          exhibitCode,
          type: _type,
          extend: btoa(encode(JSON.stringify({projectId, projectName, ...extend})))
        }
        return 'exhibit_edit.html' + this.Object2QueryString(searchObject)
      },
      // 销售定价
      SalesPricing() {
        const searchObject = {
          teamId,
          projectId,
          projectName: encode(projectName),
        }
        return 'sales-pricing.html' + this.Object2QueryString(searchObject)
      },
      // 展位图
      ExhibitionCenter(param) {
        return () => loginSponsor(param)
      },
      // 登记记录
      RegisterRecord(page, extend) {
        extend = extend || {}
        const searchObject = {
          teamId,
          queryType: page,
          extend: btoa(encode(JSON.stringify({projectId, projectName, ...extend})))
        }
        return 'VisitorRegistration/visitor-registration.html' + this.Object2QueryString(searchObject)
      },
      // 打开展之客
      OpenZZK(param) {
        return () => OpenZzk(param)
      },
      // 奖项设置
      Awards() {
        return 'project-awards.html' + this.Object2QueryString({projectId})
      },
      // 展位类型
      BoothType(){
        return 'booth/index.html' + this.Object2QueryString({projectId})
      },
      // 商旅信息
      Travel(){
        return 'travel/index.html' + this.Object2QueryString({projectId})
      },
      // 项目详情页
      ProjectDetail(id){
        return 'project-details.html' + this.Object2QueryString({projectId: id, teamId})
      },
      // 会员访问日志
      MemberVisitLog(param){
        const extend = btoa(encode(JSON.stringify({projectId, projectName, ...param})))
        return 'MemberVisitLog/index.html' + this.Object2QueryString({teamId, extend})
      },
    }
    if (!factory.hasOwnProperty(type) && typeof factory[type] !== 'function')
      return console.warn('type：' + type + '不支持！') || ''
    return factory[type].apply(factory, args)
  }

  // 显示的字段，可能后面会添加字段控制
  const fields = [
    {label: "项目名称", key: "projectName"},
    {label: "项目英文名称", key: "projectNameEn"},
    {label: "项目简称", key: "projectAbbr"},
    {label: "项目类型", key: "projKindName"},
    {label: "行业分类", key: "tradeName"},
    // {label: "举办地点", key: "venue"},
    {label: "举办地点", key: "f_address"},
    {label: "举办地点英文", key: "f_address_en"},
    {label: "主办方", key: "sponsor"},
    {label: "承办方", key: "organizer"},
    {label: "举办年份", key: "f_year_month"},
    {label: "开始时间", key: "f_start_time"},
    {label: "结束时间", key: "f_end_time"},
    {label: "所属项目", key: "parentName"},
    {label: "所属展会", key: "exihibitName"},
    {label: "商机保护上限", key: "oppProtectNum"},
  ]
  // 权限过滤
  const filterFn = ({permission}) => {
    if(typeof permission === 'boolean') return permission;
    if(!permission) return true;
    permission = permission || '';
    if(permission.lastIndexOf('|') > -1) return !!String(permission).split('|').filter(it => getAuthority(it)).length
    else if(permission.lastIndexOf(',') > -1) return !String(permission).split(',').filter(it => !getAuthority(it)).length
    else return getAuthority(permission)
  }
  // 销售政策信息菜单 - 1014
  const salesPolicyMenu = [
    {
      id: 1001,
      text: '销售资料',
      tabName: `销售资料-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 2),
      icon: 'img/projectInfo/detail(1).png',
      permission: 'fu_crm_project_sales_data'
    },
    {
      id: 1002,
      text: '项目定价',
      tabName: `项目定价-${projectName}`,
      href: genLinkAddress('SalesPricing'),
      icon: 'img/projectInfo/detail(2).png',
      permission: 'fu_crm_project_sales_pricing'
    },
    {
      id: 1013,
      text: '展位类型',
      tabName: `展位类型-${projectName}`,
      href: genLinkAddress('BoothType'),
      icon: 'img/projectInfo/boothType.png',
      permission: 'fu_crm_project_booth_type'
    },
    {
      id: 1014,
      text: '商旅信息',
      tabName: `商旅信息-${projectName}`,
      href: genLinkAddress('Travel'),
      icon: 'img/projectInfo/travel.png',
      permission: 'fu_crm_project_business_travel_info'
    },
    {
      id: 1003,
      text: '动态展位图',
      tabName: `动态展位图-${projectName}`,
      href: genLinkAddress('ExhibitionCenter', 2),
      icon: 'img/projectInfo/detail(3).png',
      permission: 'fu_crm_project_booth_map'
    },
    {
      id: 1004,
      text: '展会行程',
      tabName: `展会行程-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 3),
      icon: 'img/projectInfo/detail(4).png',
      permission: 'fu_crm_project_exhibition_tour'
    },
    {
      id: 1005,
      text: '销售政策',
      tabName: `销售政策-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 1),
      icon: 'img/projectInfo/market3.png',
      permission: 'fu_crm_project_sales_policy'
    },
    {
      id: 1006,
      text: '销售计划',
      tabName: `销售计划-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'plan'),
      icon: 'img/projectInfo/market2.png',
      permission: 'fu_crm_sales_plan_menu'
    },
    {
      id: 1007,
      text: '项目财务汇总',
      tabName: `项目财务汇总-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'summary'),
      icon: 'img/projectInfo/market4.png',
      permission: 'fu_crm_project_sales_collect'
    },
    {
      id: 1008,
      text: '展会测评',
      tabName: `展会测评-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 4),
      icon: 'img/projectInfo/market5.png',
      permission: 'fu_crm_project_exhibition_evaluating'
    },
    {
      id: 1009,
      text: '提成计算',
      tabName: `提成标准-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 10),
      // href: genLinkAddress('ProjectMarket', 'commission'),
      icon: 'img/projectInfo/market5.png',
      permission: 'fu_crm_commission_data'
    },
    {
      id: 1010,
      text: '项目积分设置',
      tabName: `项目积分设置-${projectName}`,
      href: 'projectScoreSetting', // 单独判断 单独组件
      icon: 'img/projectInfo/score.png',
      permission: 'fu_crm_project_integral_set,fu_crm_integral_management',
    },
    {
      id: 1011,
      text: '提成规则设置',
      tabName: `提成规则设置-${projectName}`,
      href: 'projectCommissionRule', // 单独判断 单独组件
      icon: 'img/projectInfo/rule.png',
      permission: true,
    },
    {
      id: 1012,
      text: '会员访问日志',
      tabName: `会员访问日志-${projectName}`,
      href: genLinkAddress('MemberVisitLog'),
      icon: 'img/projectInfo/member-visits.png',
      permission: 'fu_crm_user_visit_log',
    }
  ]
  // 展商服务管理菜单
  const exhibitionTask = [
    {
      id: 1001,
      text: '市场任务',
      tabName: `市场任务-${projectName}`,
      href: '',
      countList: [],
      icon: 'img/projectInfo/task1.png',
      permission: 'fu_crm_project_market_task'
    },
    {
      id: 1002,
      text: '销售任务',
      tabName: `销售任务-${projectName}`,
      href: '',
      countList: [],
      icon: 'img/projectInfo/task2.png',
      permission: 'fu_crm_project_sales_task'
    },
    {
      id: 1003,
      text: '售后任务',
      tabName: `售后任务-${projectName}`,
      href: '',
      countList: [],
      icon: 'img/projectInfo/task3.png',
      permission: 'fu_crm_project_sales_after_task'
    },
    {
      id: 1004,
      text: '展商参展申请',
      tabName: `展商参展申请-${projectName}`,
      href: genLinkAddress('RegisterRecord', 'exhibitor'),
      countList: [
        {
          label: '已审：',
          value: projectNumMapper['checkTraderRegNum'],
          href: genLinkAddress('RegisterRecord', 'exhibitor', {f_is_check: true}),
        },
        {
          label: '未审：',
          value: projectNumMapper['unCheckTraderRegNum'],
          href: genLinkAddress('RegisterRecord', 'exhibitor', {f_is_check: false}),
        },
      ],
      icon: 'img/projectInfo/task4.png',
      permission: 'fn_trader_menu'
    },
    {
      id: 1005,
      text: '展商参展管理',
      tabName: `展商参展管理-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'exManage'),
      countList: [
        {
          label: '展商数量：',
          value: projectNumMapper['confirmServeBoothNum'] + projectNumMapper['unConfirmServeBoothNum'],
          href: genLinkAddress('ProjectMarket', 'exManage'),
        },
        // {
        //   label: '已确认：',
        //   value: projectNumMapper['confirmServeBoothNum'],
        //   href: genLinkAddress('ProjectMarket', 'exManage', {boothConfirmed: true}),
        // },
        // {
        //   label: '未确认：',
        //   value: projectNumMapper['unConfirmServeBoothNum'],
        //   href: genLinkAddress('ProjectMarket', 'exManage', {boothConfirmed: false}),
        // },
      ],
      icon: 'img/projectInfo/task5.png',
      permission: 'fu_crm_exhibitor_servebooth_manager_head_menu'
      // permission: 'fu_crm_project_exhibitor_manage'
    },
  ]
  // 项目销售管理菜单
  const contractReceipt = [
    {
      id: 1001,
      text: '销售合同单',
      tabName: `销售合同单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'contract'),
      countList: [
        {
          label: '已审：',
          value: projectNumMapper['checkContractNum'],
          href: genLinkAddress('ProjectMarket', 'contract', {isChecked: true}),
        },
        {
          label: '未审：',
          value: projectNumMapper['unCheckContractNum'],
          href: genLinkAddress('ProjectMarket', 'contract', {isChecked: false}),
        },
      ],
      icon: 'img/projectInfo/contract1.png',
      permission: 'fu_crm_project_contract'
    },
    {
      id: 1002,
      text: '销售订单',
      tabName: `销售订单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'order'),
      countList: [
        {
          label: '已审：',
          value: projectNumMapper['checkOrderNum'],
          href: genLinkAddress('ProjectMarket', 'order', {isChecked: true}),
        },
        {
          label: '未审：',
          value: projectNumMapper['unCheckOrderNum'],
          href: genLinkAddress('ProjectMarket', 'order', {isChecked: false}),
        },
      ],
      icon: 'img/projectInfo/contract2.png',
      permission: 'fu_crm_project_sales_order'
    },
    {
      id: 1003,
      text: '应收款',
      tabName: `应收款-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'receivables'),
      countList: [
        {
          label: '笔数：',
          value: projectNumMapper['checkReceivablestNum'] + projectNumMapper['unCheckReceivablestNum']
        },
      ],
      icon: 'img/projectInfo/contract3.png',
      permission: 'fu_crm_project_receivables'
    },
    {
      id: 1004,
      text: '收款单',
      tabName: `收款单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'receipt'),
      countList: [
        {
          label: '笔数：',
          value: projectNumMapper['checkReceiptNum'] + projectNumMapper['unCheckReceiptNum']
        },
      ],
      icon: 'img/projectInfo/contract4.png',
      permission: 'fu_crm_project_receipt'
    },
    {
      id: 1005,
      text: '发票申请单',
      tabName: `发票申请单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'invoice'),
      countList: [
        {
          label: '已开：',
          value: projectNumMapper['checkFnInvoiceNum'],
          href: genLinkAddress('ProjectMarket', 'invoice', {invoicedOut: true}),
        },
        {
          label: '未开：',
          value: projectNumMapper['unCheckFnInvoiceNum'],
          href: genLinkAddress('ProjectMarket', 'invoice', {invoicedOut: false}),
        },
      ],
      icon: 'img/projectInfo/contract5.png',
      permission: 'fu_crm_project_invoice'
    },
  ]
  // 采购付款信息菜单
  const buyPayment = [
    {
      id: 1001,
      text: '采购订单',
      tabName: `采购订单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'CGOrder'),
      countList: [
        {
          label: '已审：',
          value: projectNumMapper['checkCGOrderNum'],
          href: genLinkAddress('ProjectMarket', 'CGOrder', {isChecked: true}),
        },
        {
          label: '未审：',
          value: projectNumMapper['unCheckCGOrderNum'],
          href: genLinkAddress('ProjectMarket', 'CGOrder', {isChecked: false}),
        },
      ],
      icon: 'img/projectInfo/purchase1.png',
      permission: 'fu_crm_project_buy_order'
    },
    {
      id: 1002,
      text: '应付款',
      tabName: `应付款-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'Payables'),
      countList: [
        {
          label: '笔数：',
          value: projectNumMapper['unCheckFnPayablesNum']
        },
      ],
      icon: 'img/projectInfo/purchase2.png',
      permission: 'fu_crm_project_payables'
    },
    {
      id: 1003,
      text: '付款单',
      tabName: `付款单-${projectName}`,
      href: genLinkAddress('ProjectMarket', 'payment'),
      countList: [
        {
          label: '笔数：',
          value: projectNumMapper['unCheckPaymentNum']
        },
      ],
      icon: 'img/projectInfo/purchase3.png',
      permission: 'fu_crm_project_payment'
    },
  ]
  // 导出的菜单列表
  const listCardMenu = [
    {
      id: 1,
      type: LINK,
      title: '项目资料信息',
      permission: 'fu_crm_project_sales_policy_data',
      list: salesPolicyMenu.filter(filterFn),
    },
    {
      id: 2,
      type: CARD_LINK,
      title: '展商服务管理',
      permission: 'fu_crm_exhibitor_servebooth_manager_head_menu|fu_crm_project_exhibition_task_data|fu_crm_project_market_task|fu_crm_project_sales_task|fu_crm_project_sales_after_task',
      // permission: 'fu_crm_project_exhibition_task_data',
      list: exhibitionTask.filter(filterFn),
    },
    {
      id: 3,
      type: CARD_LINK,
      title: '销售收款管理',
      permission: 'fu_crm_project_contract_receipt_data',
      list: contractReceipt.filter(filterFn),
    },
    {
      id: 4,
      type: CARD_LINK,
      title: '采购付款管理',
      permission: 'fu_crm_project_buy_payment_data',
      list: buyPayment.filter(filterFn)
    },
  ]
  // 其他跳转菜单
  const otherMenu = {
    Awards: {
      tabName: `销售资料-${projectName}`,
      href: genLinkAddress('ExhibitEdit', 2),
      show: 'AWD' === projKindCode && getAuthority('fu_crm_project_awards')
    },
    ExhibitionCenter:{
      href: genLinkAddress('ExhibitionCenter', 1),
      show: getAuthority('fu_crm_project_exhibition_center')
    }
  }

  return {
    fields,
    otherMenu,
    genLinkAddress,
    permissionMenu: listCardMenu.filter(filterFn),
  }
}