// 项目的服务工单
const ProjectTaskShow = {
  template: `<div class="project-service-show"><custom-dialog
        :show="show"
        @before-close="close"
        title="展商参展信息填报"
        width="550px">
      <el-main slot="body" class="dialog-body" ref="body">
        <div>
          <div class="all_address" style="display: flex;align-items: center;">
            <h6 style="margin-top: 8px;margin-right: 15px;">填报内容</h6>
            <div style="width: 200px;">
              <el-select v-model="taskId" size="mini">
                <el-option label="填报中心" value=""></el-option>
                <el-option v-for="it in tasks" :key="it.taskId" :label="it.taskTopic || it.taskKindName" :value="it.taskId"></el-option>
              </el-select>
            </div>
          </div>
          <div class="all_address" v-if="enEnable" style="padding-bottom: 0;">
            <span :class="'webSetStyle '+ (isZh ? 'webSetStyleActive': '')" @click="showEn = false">中文版设置</span>
            <span :class="'webSetStyle '+ (isEn ? 'webSetStyleActive': '')" @click="showEn = true">英文版设置</span>
          </div>
          <div class="all_address" v-if="isZh && (!taskKindCode || h5Services.includes(taskKindCode))">
            <h6 style="margin-top: 15px;">手机填报地址</h6>
            <textarea cols="50" rows="3" readonly id="service-show-mobile">{{ link.mobile }}</textarea>
            <div style="margin: 5px 0">
              <el-button class="btn-hover" size="small" plain @click="copyLink('mobile')">复制链接</el-button>
              <el-button size="small" plain @click="openLink('mobile')">直接打开</el-button>
            </div>
          </div>
          <div class="all_address" v-if="isEn && (!taskKindCode || h5Services.includes(taskKindCode))">
            <h6 style="margin-top: 15px;">手机填报地址(英文)</h6>
            <textarea cols="50" rows="3" readonly id="service-show-mobileEn">{{ link.mobileEn }}</textarea>
            <div style="margin: 5px 0">
              <el-button class="btn-hover" size="small" plain @click="copyLink('mobileEn')">复制链接</el-button>
              <el-button size="small" plain @click="openLink('mobileEn')">直接打开</el-button>
            </div>
          </div>
          <div class="all_address" v-if="isZh && (!taskKindCode || pcServices.includes(taskKindCode))">
            <h6 style="margin-top: 15px;">电脑填报地址</h6>
            <textarea cols="50" rows="3" id="service-show-pc" readonly>{{ link.pc }}</textarea>
            <div style="margin: 5px 0">
              <el-button  size="small" class="btn-hover"  plain @click="copyLink('pc')">复制链接</el-button>
              <el-button  size="small" plain @click="openLink('pc')">直接打开</el-button>
            </div>
          </div>
          <div class="all_address" v-if="isEn && (!taskKindCode || pcServices.includes(taskKindCode))">
            <h6 style="margin-top: 15px;">电脑填报地址(英文)</h6>
            <textarea cols="50" rows="3" id="service-show-pcEn" readonly>{{ link.pcEn }}</textarea>
            <div style="margin: 5px 0">
              <el-button  size="small" class="btn-hover"  plain @click="copyLink('pcEn')">复制链接</el-button>
              <el-button  size="small" plain @click="openLink('pcEn')">直接打开</el-button>
            </div>
          </div>
          <div class="address_download" v-show="isZh && link.mobile && (!taskKindCode || h5Services.includes(taskKindCode))">
            <ul style="display: flex;">
              <li>
                <div class="qr_code"></div>
                <div class="address_download_button">
                  <a download="qrcode.jpg"></a>
                </div>
              </li>
              <li>
                <div class="prompt_text">
                  <!--<p>左侧手机二维码点击下载即可手机扫码填报</p>-->
                  <el-button  size="small" class="btn-hover" plain @click="downPic()">下载二维码</el-button>
                </div>
              </li>
            </ul>
          </div>
          <div class="address_download" v-show="isEn && link.mobileEn && (!taskKindCode || h5Services.includes(taskKindCode))">
            <ul>
              <li>
                <div class="qr_code_en"></div>
                <div class="address_download_button">
                  <a download="qrcode.jpg"></a>
                </div>
              </li>
              <li>
                <div class="prompt_text">
                  <!--<p>左侧手机二维码点击下载即可手机扫码填报</p>-->
                  <el-button  size="small" class="btn-hover" plain @click="downPic(true)">下载二维码</el-button>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </el-main>
      </custom-dialog>
    </div>`,
  data(){
    return {
      show: false,
      linkPre: window.origin,
      cid: getCookie('operatorId') || '',// 无用
      operName: decodeURI(getCookie('OperName') || ''),// 无用
      qrcodeLogo: '',
      targetType: 1, // 无用
      pcServices: ['PERSON','BOOTH','CATA','VISA','INVITE','CERT','TRANS','FILE','WRITE_INSTRUCTIONS','GUEST', 'OVERSEAS_GUEST','VIP_BADGE','EVENT'],
      h5Services: ['PERSON','BOOTH','CATA','VISA','INVITE','CERT','TRANS','FILE','WRITE_INSTRUCTIONS','GUEST', 'OVERSEAS_GUEST','VIP_BADGE','EVENT'],
      // h5Services: ['BOOTH','CATA','CERT','FILE'],
      taskId: '',
      enEnable: false,
      showEn: false,
    }
  },
  computed: {
    // title() {
    //   return (this.operName ? (this.operName +'专属') : '') + '观众参展链接'
    // },
    // showLink(){
    //   return this.cid && this.projectData.releaseReg && this.projectData.exhibitCode && this.projectData.f_org_num && this.projectData.projectId;
    // },
    isEn() {
      return this.showEn && this.enEnable;
    },
    isZh() {
      return !this.showEn;
    },
    linkTail(){
      // console.log(this.projectData)
      return  `?p=${this.projectData.projectId}&role=2&f=1&taskId=${this.taskId}`;
    },
    taskKindCode() {
      return (this.tasks.find(it=> it.taskId == this.taskId) || {}).taskKindCode || ''
    },
    tasks() {
      return this.projectData.taskDetailVoList || [];
    },
    link(){
      return {
        pc: this.linkPre + '/eip-app-business/pages/boothinfo/exhibitor_member_fast.html' + this.linkTail + '&l=zh',
        pcEn: this.linkPre + '/eip-app-business/pages/boothinfo/exhibitor_member_fast.html' + this.linkTail + '&l=en',
        mobile: this.linkPre + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/vip-subpages/task-filling-up/task-directory.html' + this.linkTail + '&l=zh',
        mobileEn: this.linkPre + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/vip-subpages/task-filling-up/task-directory.html' + this.linkTail + '&l=en',
      };
    },
    // qrcodeLogoURL() {
    //   return this.queryData2image(this.qrcodeLogo)
    // }
  },
  props: ['projectData'],
  methods:{
    close(){
      this.show = false
    },
    async getEnEnable() {
      const {data: {state,data}} = await Axios.post('/project/information/selectMemberCenterSet');
      if(state !== 1) await Promise.reject('数据加载失败');
      this.enEnable = ((data || []).find(it=> it.type == 999) || {}).content == 'true';
    },
    open(task, pid = ''){
      this.show = true
      this.showEn   = false;
      this.enEnable = false;
      this.getEnEnable();
      // this.task = task || ''
      this.qrcodeLogo = this.getQrLogo(pid,'', 'exhibitorSelfHelpCenterLogo|memberCenterLogo');
      this.$nextTick(() => {
        $('.project-service-show .qr_code').html("").qrcode({
          src: this.qrcodeLogo,
          text: this.link.mobile,
          correctLevel: 0,
          width: '1000',  //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,	//图片宽
          imgHeight : 200,//图片高
        });
        $('.project-service-show .qr_code_en').html("").qrcode({
          src: this.qrcodeLogo,
          text: this.link.mobileEn,
          correctLevel: 0,
          width: '1000',  //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,	//图片宽
          imgHeight : 200,//图片高
        })
        // this.getQRCodeLogo().then(() => {
        //   this.$nextTick(() => {
        //     $('.project-service-show .qr_code').html("").qrcode({
        //       src: this.qrcodeLogoURL,
        //       text: this.link.mobile,
        //       correctLevel: 0,
        //       width: '1000', //二维码的宽度
        //       height: '1000', //二维码的高度
        //       imgWidth : 200,			 //图片宽
        //       imgHeight : 200,			 //图片高
        //     })
        //   })
        // })
      })
    },
    copyLink(type){
      if(type){
        document.getElementById('service-show-'+type).select();
        document.execCommand("Copy");
      }
    },
    openLink(type){
      type && window.open(this.link[type])
    },
    downPic(isEn = false){
      var type = 'png';
      var c = $('.project-service-show .qr_code' + (isEn ? '_en' : '')).find('canvas')[0];
      var imgdata = c.toDataURL("image/png");
      var fixtype = function(type) {
        type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
        var r = type.match(/png|jpeg|bmp|gif/)[0];
        return 'image/' + r;
      };
      imgdata = imgdata.replace(fixtype(type), 'image/octet-stream');
      var savaFile = function(data, filename) {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
      };
      savaFile(imgdata, '展商参展信息填报二维码'+(isEn ? '(英文)' : '')+'.png');
    },
    //兼容之前已经上传的图片
    // queryData2image(src) {
    //   const inner = src => {
    //     if (!src) return '/RegSever/RegPage/images/top-lucency.png'
    //     const prefix = location.origin
    //     if (/(http|https)/.test(src)) return src
    //     else if (/(^\\+|^\/)/.test(src)) return prefix + src
    //     return prefix + '/image/' + src
    //   }
    //   const url = inner(src)
    //   if (!url) return ''
    //   try {
    //     return new URL(url).host.indexOf('aliyuncs.com') !== -1
    //       ? `/eip-web-sponsor/upload/ossOut?ossUrl=${encodeURIComponent(url)}`
    //       : url
    //   } catch (e) {
    //     return url
    //   }
    // },
    // async getQRCodeLogo() {
    //   try {
    //     const {data: {state, data}} = await Axios.post(location.origin + '/RegSever/projRegTemp/queryAppointTemp', JSON2FormData({
    //       f_project_id: this.projectData.projectId,
    //       f_version: 1,
    //       f_target: this.targetType || 1,
    //       appointTemp: 'f_2code_img'
    //     }))
    //     if (state === 1 && data.f_2code_img)
    //       this.qrcodeLogo = data.f_2code_img
    //   }catch (e) {
    //     e && e !== 'cancel' && console.warn(e)
    //   }
    // }
    getQrLogo(pid, targetType='',prior = 'exhibitionLogo') {
      let tmp = {},logo = '';
      $.ajax({
        type: "post",
        url: '/eip-web-sponsor/api/project/getProjectLogo',
        data: {
          projectId: pid,
          targetType: '',
        },
        async: false,
        dataType: "json",
        success (rsp) {
          if(rsp && rsp.state == 1) tmp = rsp.data || {};
        },
      })
      function fixLogo(url, pre= '') {
        if(!url) return '';
        if(url.startsWith('//') || url.startsWith('http')) return url;
        return window.origin + pre + url;
      }
      tmp.exhibitionLogo = fixLogo(tmp.exhibitionLogo,'/image/');
      tmp.projectLogo = fixLogo(tmp.projectLogo,'');
      const logoDf = tmp.exhibitionLogo || '';
      if(!prior) logoDf;
      prior.split('|').map(k=> {
        if(!logo) logo = tmp[k] || ''
      })
      return (logo || logoDf).replace('http:', '');
    },
  },
  watch: {
    'link.mobile'(newName) {
      if(newName){
        $('.project-service-show .qr_code').html("").qrcode({
          text: newName,
          src: this.qrcodeLogo,
          correctLevel: 0,
          width: '1000', //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,			 //图片宽
          imgHeight : 200,			 //图片高
        });
      }
    },
    'link.mobileEn'(newName) {
      if(newName){
        $('.project-service-show .qr_code_en').html("").qrcode({
          text: newName,
          src: this.qrcodeLogo,
          correctLevel: 0,
          width: '1000', //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,			 //图片宽
          imgHeight : 200,			 //图片高
        });
      }
    }
  },
  components:{
    customDialog
  }
}