// 用于兼容 css3中的max函数
;(function (window,css) {
  if (!css) return console.warn('What rubbish browser, this all have no, How dare you use it!')
  const isSupportMaxFn = css.supports('width', 'max(1px, 2px)')
  if(isSupportMaxFn) return console.log('That\'s nice！Seems to be the new browser！')
  console.warn('The browser is a little old!')
  window.onresize = function () {
    const $root = document.documentElement
    const viewWidth = $root.clientWidth
    const coefficient = (viewWidth - 1440) * 0.003625
    const column = Math.max(coefficient, 1) * 27
    $root.style.setProperty('--column', column + 'px')
  }
  window.dispatchEvent(new Event('resize'))
})(window, CSS)