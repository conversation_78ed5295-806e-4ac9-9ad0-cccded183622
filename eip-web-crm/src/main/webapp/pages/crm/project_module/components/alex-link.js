;(function (window,mixins) {
  const template = `
    <div class="alex-link" @click="$emit('link-click', $event)">
      <el-image :src="iconSrc" @click="handleImageClick"></el-image>
      <span class="alex-link__text"><slot>{{text}}</slot></span>
    </div>
  `
  window.AlexLink = {
    template,
    name: 'AlexLink',
    mixins,
    props: {
      iconSrc: String,
      text: String,
    }
  }
})(window, [Mixins.fixImageOverFlow()])
