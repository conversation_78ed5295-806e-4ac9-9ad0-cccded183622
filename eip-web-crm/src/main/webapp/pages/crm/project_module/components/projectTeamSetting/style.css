.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
}

.dialog-header .dialog-header-left {
    color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
    padding: 0 30px 0 5px;
    line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}

.dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
}


.team-table .el-table__header th {
    background-color: #63A9FB;
    color: #fff !important;
}

.team-table .el-table__row tr {
    padding: 5px 0;
}

.dialog-body i {
    color: inherit !important;
}
.mm-form{
    display: flex;
    width: 100%;
}
.mm-form .el-form-item {
    margin-bottom: 0;
}