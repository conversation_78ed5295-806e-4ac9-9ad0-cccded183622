const ProjectCommissionRule = {
  template: `<div class="project-commission-rule">
  <custom-dialog
    :show="showDate"
    @before-close="_ => showDate = false"
    title="项目账期设置"
    width="400px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left" style="display: inline-flex;flex: 1;">
        </div>
        <div class="dialog-header-right">
          <el-button type="primary" size="mini" icon="el-icon-circle-check" @click="save()" @disabled="!form.accountPeriod">保存</el-button>
        </div>
      </el-header>
      <el-container>
        <center style="width: 100%;margin-top: 20px;">
        <el-form inline>
          <el-form-item label="项目账期" required>
            <el-date-picker
              size="mini"
              v-model="form.accountPeriod"
              type="month"
              :clearable="false"
              format="yyyy年M月"
              value-format="yyyy-MM"
              placeholder="选择年月">
            </el-date-picker>
          </el-form-item>
        </el-form>
        </center>
      </el-container>
    </el-main>
  </custom-dialog>
  <custom-dialog
    :show="set.show"
    @before-close="_ => set.show = false"
    :title="(!set.form.projectCommissionRuleId ? '新增' : '编辑' ) + '团队提成规则'"
    width="400px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left" style="display: inline-flex;flex: 1;">
        </div>
        <div class="dialog-header-right">
          <el-button type="primary"
           :disabled="(!set.form.objId && set.form.objId!==0) || !set.form.commissionRuleId"
           size="mini" icon="el-icon-circle-check"
           @click="setSave()">保存</el-button>
        </div>
      </el-header>
      <el-container>
        <center style="width: 100%;margin-top: 20px;">
        <el-form inline label-width="80px">
          <el-form-item v-if="dataType == 1" label="团队" required :style="{marginBottom: '10px'}">
            <el-select size="mini" v-model="set.form.objId">
              <el-option v-for="it in dataTeams" :disabled="setIds.includes(it.fWorkTeamId)" :key="it.fWorkTeamId" :label="it.fWorkTeamName" :value="it.fWorkTeamId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="dataType == 2" label="部门" required :style="{marginBottom: '10px'}">
            <!-- <el-option v-for="it in dataDeparts" :disabled="setIds.includes(it.f_branch_id)" :key="it.f_branch_id" :label="it.f_branch_name" :value="it.f_branch_id"></el-option>
            <el-cascader size="mini" v-model="set.form.objId"
               :options="dataDeparts"
              :props="{
                label: 'f_branch_name',
                value: 'f_branch_id',
              }">
            </el-cascader> -->
            <el-select size="mini" v-model="set.form.objId"  ref="el-select-tree" clearable  popper-class="el-select-tree">
              <el-option :value="branchSelect.id" :label="branchSelect.name">
                <el-tree :data="dataDeparts" ref="tree" label="f_branch_name"  node-key="f_branch_id"
                  highlight-current default-expand-all
                  @node-click="clickBranchSelect">
                  <span class="custom-tree-node" slot-scope="{ data }">
                    {{ data.f_branch_name || '部门'+data.f_branch_id }}
                  </span>
                </el-tree>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提成规则" required >
            <el-select  size="mini" v-model="set.form.commissionRuleId">
              <el-option v-for="it in dataRules"  :key="it.commissionRuleId" :label="it.commissionRuleName" :value="it.commissionRuleId"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        </center>
      </el-container>
    </el-main>
  </custom-dialog>
  <custom-dialog
    :show="show"
    @before-close="close"
    title="设置提成规则"
    width="800px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left" style="display: inline-flex;flex: 1;">
          <span>项目财务账期</span>
          <span v-if="accountPeriod" style="display: flex;align-items: center;">
            <span class="dot dot1"></span>
            <span style="color: #606266;">已设置</span>&nbsp;&nbsp;
            <el-button size="mini" plain :style="{
              padding: '4px 7px',
              height: '20px',
            }" @click="dateClear">取消账期</el-button>&nbsp;&nbsp;
            <span style="color: #999;"> 当前项目账期 {{ accountPeriodDesc }}</span>&nbsp;&nbsp;
            <el-link :underline="false" @click="openDate" type="primary">修改</el-link>
          </span>
          <span v-else  style="display: flex;align-items: center;">
            <span class="dot"></span>
            <span  style="color: #606266;">未设置</span> &nbsp;&nbsp;
            <el-button size="mini"  type="primary"
              :style="{
                height: '25px',
              }" @click="openDate">设置</el-button>
          </span>
        </div>
        <div class="dialog-header-right">
          <!-- <el-button type="primary" size="mini" icon="el-icon-circle-check" @click="save()">保存</el-button> -->
        </div>
      </el-header>
      <el-container>
        <div style="padding: 10px 20px;width: 100%;box-sizing: border-box;">
          <div>
            <el-button type="text" icon="el-icon-plus" @click="add">新增</el-button>
            <el-button type="text" @click="$emit('select-project')">复制其他项目提成规则</el-button>
          </div>
          <el-table
            border
            pager
            class="team-table"
            max-height="415px"
            size="mini"
            ref="table"
            :data="data"
            :total="total"
            style="width: 100%;margin-bottom: 20px;">
            <el-table-column
              width="50"
              label="序号"
              type="index">
            </el-table-column>
            <el-table-column
              prop="objName"
              :label="['','团队','部门'][dataType] ||'团队/部门'"
            ></el-table-column>
            <el-table-column
              prop="commissionRuleName"
              label="提成规则"
              >
            </el-table-column>
            <el-table-column
              width="120"
              label="操作">
              <template slot-scope="{row}">
                <div style="padding: 0 10px;">
                  <el-link :underline="false" type="primary" @click.stop="edit(row)">编辑</el-link>
                  &nbsp;
                  <el-popconfirm
                    icon="el-icon-warning"
                    icon-color="red"
                    title="确定删除吗？"
                    @onconfirm="del(row)"
                  >
                    <el-link :style="{marginTop: '-1px'}" :underline="false" type="primary" slot="reference" @click.stop>删除</el-link>
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination align='center'
            @size-change="sizeChange"
            @current-change="pageChange"
            :current-page="page"
            :page-sizes="[5,10,20,50]"
            :page-size="rows"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </div>
      </el-container>
    </el-main>
  </custom-dialog>
</div>`,
  data(){
    return{
      show: false,
      showDate: false,
      page: 1,
      rows: 20,
      total: 0,
      data: [],       // 项目提成规则列表
      dataTeams: [],
      dataDeparts: [],
      dataType: '',
      dataRules: [],
      pinfo: {},
      accountPeriod: '',  // 项目现在的账期
      accountPeriodDf: '',// 项目现在的默认账期
      form: {
        accountPeriod: '',
        copyProjectId: '',
        _copyProjectName: '',
      },
      set: {
        form: {
          projectCommissionRuleId: '',
          objId: '',
          commissionRuleId: '',
        },
        formInit: '',
        show: false,
      },
      formInit: '',
      branchSelect: {
        id: '',
        name: '',
      }
    }
  },
  computed: {
    accountPeriodDesc() {
      if(!this.accountPeriod) return ''
      return new Date(this.accountPeriod).format('yyyy年M月')
    },
    setIds() {
      return (this.data ||[]).map(it=> it.objId) || []
    },
    // dataDepartsFlat() {
    //   return this.treeFlat(this.dataDeparts);
    // },
  },
  components:{
    customDialog
  },
  mounted(){
    this.formInit = JSON.stringify(this.form);
    this.set.formInit = JSON.stringify(this.set.form);
  },
  methods:{
    // treeFlat(treeData) {
    //   const result = [];
    //   function traverse(node) {
    //     if (node.children && node.children.length > 0) {
    //       for (const child of node.children) {
    //         traverse(child);
    //       }
    //     }
    //     delete node.children
    //     result.push(node);
    //   }
    //   traverse(treeData);
    //   return result;
    // },
    error(msg='', throws = true) {
      msg = msg || '请求失败！';
      const proms = this.$message.error(msg);// this.$alert(msg, '错误', {type:'error'})
      if(throws) throw new Error(msg)
      return proms
    },
    ok(message='操作成功!') {
      return this.$message({
        message,
        type: 'success'
      });
    },
    checkRet(obj, errMsg='') {
      if(obj && obj.state !== 1) this.error(errMsg ||obj.msg || obj.message || '')
    },
    close(){
      this.show = false
      // this.requestProjectTeam()
    },
    async setSave() {
      const {data} = await Axios.post('/projectCommissionRule/save', JSON2FormData({
        projectCommissionRuleId: this.set.form.projectCommissionRuleId,
        projectId: this.pinfo.projectId,
        objType: this.dataType,
        objId: this.set.form.objId,
        commissionRuleId: this.set.form.commissionRuleId,
      })).catch(_ => this.error());
      this.checkRet(data)
      await this.getPageData();
      this.set.show = false;
    },
    sizeChange(size) {
      this.page = 1;
      this.rows = size;
      this.getPageData();
    },
    pageChange(page) {
      this.page = page
      this.getPageData()
    },
    openDate() {
      this.form.accountPeriod = this.accountPeriod || this.accountPeriodDf
      this.showDate = true
    },
    async dateClear() {
      await this.$confirm('是否确认取消项目账期? ', '提醒', {
        type: 'warning',
      })
      await this.save(true);
    },
    async getProjectTeam(){
      const {data} = await Axios.post('/workteamMenber/getTeamByOperator', JSON2FormData({projectId})).catch(_ => this.error());
      this.checkRet(data)
      this.dataTeams = data.result || [];
    },
    async getRules(){
      const {data} = await Axios.post('/commissionRule/getList').catch(_ => this.error());
      this.checkRet(data)
      this.dataRules = data.data || [];
    },
    async getDeparts(){
      const {data} = await Axios.post('/branch/getTree').catch(_ => this.error());
      // this.checkRet(data)
      this.dataDeparts = data || [];
    },
    async selectedProject(pInfo = {}) {
      this.form.copyProjectId = pInfo.projectId || ''
      this.form._copyProjectName = pInfo.projectName || ''
      if(this.data && this.data.length) {
        await this.$confirm('复制前将删除本项目下已有提成设置，是否确认? ', '提醒', {
          type: 'warning',
        })
      }
      const {data} = await Axios.post('/projectCommissionRule/copy', JSON2FormData({
        projectId: this.pinfo.projectId,
        copyProjectId: this.form.copyProjectId,
        objType: this.dataType,
      })).catch(_ => this.error());
      this.checkRet(data);
      this.$message.success('复制项目规则成功！')
      this.getPageData();
    },
    clickBranchSelect(data,node,objDom) {
      this.set.form.objId = +data.f_branch_id || 0;
      this.branchSelect = {
        id: +data.f_branch_id || 0,
        name: data.f_branch_name || '',
      }
      this.$refs['el-select-tree'].blur();
    },
    // 保存账期
    async save(clear = false) {
      if(clear) this.form.accountPeriod = '';
      const {data} = await Axios.post('/project/saveAccountPeriod', JSON2FormData({
        projectId: this.pinfo.projectId,
        accountPeriod: this.form.accountPeriod,
      })).catch(_ => this.error());
      this.checkRet(data);
      this.$message.success(clear ? '账期取消成功！' :'账期设置成功！')
      this.accountPeriod = this.form.accountPeriod;
      this.showDate = false;
      this.$emit('finish')
    },
    async open(pinfo = {}){
      this.page = 1;
      // this.rows = 20;
      this.form = JSON.parse(this.formInit);
      this.pinfo = pinfo || {};
      // this.form.projectId  = this.pinfo.projectId;
      this.accountPeriod   = this.pinfo.accountPeriod || ''
      this.accountPeriodDf = this.buildNextMonth(this.pinfo.f_end_time);
      await this.getPageData();
      this.show = true
    },
    buildNextMonth(endTime = '') {
      endTime = endTime || top.$GlobalDefine.getSystemTime('yyyy-MM-dd')
      let time = endTime;
      time = time.substr(0,7);
      let month = +time.substr(5,2);
      let year = +time.substr(0,4);
      if(month < 12) {
        time = year + '-' + (month+1)
      } else {
        time = (year + 1) + '-01'
      }
      return time;
    },
    async getPageData() {
      this.getRules();
      const {data} = await Axios.post('/projectCommissionRule/getByProject', JSON2FormData({
        projectId: this.pinfo.projectId,
        page: this.page,
        rows: this.rows,
      })).catch(_ => this.error());
      this.checkRet(data);
      this.data = data.rows || [];
      this.total = +data.total || 0;
      this.dataType = +data.data || 0;
      await Promise.all([
        this.dataType == 1 ?  this.getProjectTeam() : (this.dataType == 2 ? this.getDeparts() : '')
      ])
    },
    async del({projectCommissionRuleId}){
      const {data} = await Axios.post('/projectCommissionRule/delete', JSON2FormData({
        projectCommissionRuleIds: projectCommissionRuleId
      })).catch(_ => this.error())
      this.checkRet(data);
      this.$message.success('删除成功！')
      this.getPageData()
    },
    add() {
      this.set.form = JSON.parse(this.set.formInit);
      this.set.show = true
    },
    edit(row) {
      this.set.form = JSON.parse(this.set.formInit);
      Object.keys(this.set.form).map(it=> {
        this.set.form[it] = row[it] === 0 ? 0 : (row[it]|| '');
      })
      this.branchSelect = {
        id: row.objId,
        name: row.objName,
      }
      this.set.show = true
    },
    //
    // rowClick(row, ref){
    //   ref.toggleRowSelection(row)
    // },
    // async requestTeamRole(){ // 查询项目角色
    //   const {data:res} = await Axios.post('/projRole/get', null).catch(e=>console.warn(e));
    //   console.log('requestTeamRole', res);
    //   if(res.state !== 1)return this.$message.error('获取角色失败！'+ (res.msg || ''))
    //   this.teamRoles = res.data
    // },
    // requestProjectTeam(){
    //   Axios.post('/projTeam/getByProjectId', JSON2FormData({projectId})).then(({data: res})=>{
    //     if(res.state !== 1)return this.$message.error('数据加载失败！')
    //     this.teamTableData = res.data
    //     this.$emit('operation-complete', res.data)
    //   }).catch(e=>console.warn(e))
    // },
    // requestSelectOperator(workTeamId){
    //   Axios.post('/projMember/getByPidAndTeamId', JSON2FormData({workTeamId, projectId})).then(({data: res})=>{
    //     if(res.state !== 1)return this.$message.error('数据加载失败！')
    //     this.selectOperatorData = res.data
    //   }).catch(e=>console.warn(e))
    // },
    // requestAddTeam(notIds, callback){
    //   Axios.post('/workTeam/getPage', JSON2FormData({notIds})).then(({data: res})=>{
    //     if(res.state !== 1)return this.$message.error('数据加载失败！')
    //     this.addTeamData = res.rows
    //     this.$nextTick(()=>callback(res))
    //   }).catch(e=>console.warn(e))
    // },
  },
}