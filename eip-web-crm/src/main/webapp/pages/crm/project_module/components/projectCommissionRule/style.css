.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
}

.dialog-header .dialog-header-left {
    color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
    padding: 0 30px 0 5px;
    line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}

.dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
}


.team-table .el-table__header th {
    background-color: #63A9FB;
    color: #fff !important;
}

.team-table .el-table__row tr {
    padding: 5px 0;
}

.dialog-body i {
    color: inherit !important;
}
.mm-form{
    display: flex;
    width: 100%;
}
.mm-form .el-form-item {
    margin-bottom: 0;
}
.dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin: 0px 8px 0px 0px;
    border: 4px solid rgb(242, 242, 253);
    background: #aaa;
    border-radius: 50%;
}
.dot1 {
    background-color: rgb(82, 196, 26);
}

.el-select-tree .el-scrollbar>.el-scrollbar__bar {
    opacity: 0 !important;
  }
.el-select-tree .el-scrollbar .el-scrollbar__view .el-select-dropdown__item{
    height: auto;
    max-height: 274px;
     padding: 5px 0;
    overflow: hidden;
    overflow-y: auto;
  }
  .el-select-tree .el-select-dropdown__item.selected{
    font-weight: normal;
  }
  .el-select-tree ul li >>>.el-tree .el-tree-node__content{
    height:auto;
    padding: 0 20px;
  }
  .el-select-tree .el-tree-node__label{
    font-weight: normal;
  }
  .el-select-tree .el-tree >>>.is-current .el-tree-node__label{
    color: #409EFF;
    font-weight: 700;
  }
  .el-select-tree .el-tree >>>.is-current .el-tree-node__children .el-tree-node__label{
    color:#606266;
    font-weight: normal;
  }
