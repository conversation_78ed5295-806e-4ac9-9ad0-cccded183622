;(function (window,mixins) {
  // <el-image :src="iconSrc" @click="handleImageClick"></el-image>
  const template = `
    <div class="link-card no-pointer">
      <div class="pointer" @click="$emit('card-click', $event)">
        <el-image :src="iconSrc" @click="handleImageClick"></el-image>
      </div>
      <div class="right">
        <span class="title pointer" @click="$emit('card-click', $event)">
          <slot name="title">{{title}}</slot>
        </span>
        <span class="count">
          <span v-for="count in countList" class="pointer" :key="JSON.stringify(count)" @click.stop="$emit('count-click', count ,$event)">
            <slot :count="count">
              {{count['label']}}<span>{{count['value']}}</span>
            </slot>
          </span>
        </span>
      </div>
    </div>
  `
  window.AlexLinkCard = {
    template,
    name: 'AlexLinkCard',
    mixins,
    props: {
      iconSrc: String,
      title: String,
      countList: {
        type: Array,
        default() {
          return []
        }
      }
    }
  }
})(window, [Mixins.fixImageOverFlow()])
