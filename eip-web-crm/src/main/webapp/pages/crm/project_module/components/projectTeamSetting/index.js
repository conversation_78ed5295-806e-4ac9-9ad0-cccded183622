const ProjectTeamSetting = {
  template: `<div class="project-team-setting"><custom-dialog
        :show="show"
        @before-close="close"
        title="项目参与团队设置"
        width="800px">
        <el-main slot="body" class="dialog-body" ref="body">
          <el-header class="dialog-header" height="40px">
            <div class="dialog-header-left"><span>基本信息</span></div>
            <div class="dialog-header-right">
              <el-button type="text" size="mini" icon="el-icon-plus" @click="addItemClick">新增参与团队</el-button>
            </div>
          </el-header>
          <el-container style="padding: 20px;">
            <el-table
              border
              class="team-table"
              max-height="415px"
              size="mini"
              ref="teamTable"
              :data="teamTableData"
              style="width: 100%">
              <el-table-column
                width="50"
                label="序号"
                type="index">
              </el-table-column>
              <el-table-column
                prop="f_work_team_name"
                label="团队名称"
                width="180"
              ></el-table-column>
              <el-table-column
                prop="operatorList"
                label="团队管理人员"
                width="365">
                <template slot-scope="{row}">
                  {{row.operatorList.map(it=>it.employeeName + (it.projRoleName ? '('+it.projRoleName + ')' : '')).join(',')}}
                </template>
              </el-table-column>
              <el-table-column
                label="操作">
                <template slot-scope="{row}">
                  <div style="display:flex;justify-content: space-between;padding: 0 10px;">
                    <el-link :underline="false" type="primary" @click.stop="setOperator(row)">设置管理人员</el-link>
                    <el-popconfirm
                      icon="el-icon-warning"
                      icon-color="red"
                      title="确定删除吗？"
                      @onconfirm="deleteRow(row)"
                    >
                      <el-link :underline="false" type="primary" slot="reference" @click.stop>删除</el-link>
                    </el-popconfirm>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-container>
          <custom-dialog
            :show="showSelectOperator"
            title="设置管理人员"
            width="650px"
            append2body
            @before-close="showSelectOperator=false"
          >
            <el-main slot="body" class="dialog-body">
              <el-header class="dialog-header" height="40px">
                <div class="dialog-header-left"><span v-if="currentTeamName">当前团队：{{currentTeamName}}</span></div>
                <div class="dialog-header-right" style="flex: none">
                  <el-button type="text" size="mini" icon="el-icon-plus" @click="addManageMember">新增管理人员</el-button>
                  <!--<el-button-->
                  <!--  size="mini"-->
                  <!--  icon="el-icon-circle-check"-->
                  <!--  type="primary"-->
                  <!--  @click="saveSelectOperator"-->
                  <!--  plain>-->
                  <!--  保存-->
                  <!--</el-button>-->
                </div>
              </el-header>
              <el-container style="padding: 20px;">
                <el-table
                  border
                  class="team-table"
                  max-height="300px"
                  size="mini"
                  @row-click="rowClick($event, $refs['selectOperator'])"
                  ref="selectOperator"
                  :data="selectOperatorData"
                  style="width: 100%">
                  <el-table-column
                    width="50"
                    label="序号"
                    type="index">
                  </el-table-column>
                  <!--<el-table-column-->
                  <!--  width="50"-->
                  <!--  type="selection">-->
                  <!--</el-table-column>-->
                  <!--<el-table-column-->
                  <!--  prop="workTeamName"-->
                  <!--  label="团队名称"-->
                  <!--  width="180"-->
                  <!--&gt;</el-table-column>-->
                  <el-table-column
                    prop="employeeName"
                    label="职员姓名">
                  </el-table-column>
                  <el-table-column prop="projRoleName" label="项目管理权限"></el-table-column>
                  <el-table-column label="操作" width="120px">
                    <template slot-scope="{row}">
                      <el-link 
                        style="margin-right: 10px" 
                        :underline="false" 
                        type="primary"
                         @click.stop="editManageMember([row])">编辑</el-link>
                        <el-popconfirm
                          icon="el-icon-warning"
                          icon-color="red"
                          title="确定删除吗？"
                          @onconfirm="deleteManageMember(row)"
                        >
                          <el-link :underline="false" type="primary" slot="reference" @click.stop>删除</el-link>
                        </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-container>
            </el-main>
          </custom-dialog>
          <custom-dialog
            :show="showAddTeam"
            title="新增参与团队"
            width="450px"
            append2body
            @before-close="showAddTeam=false">
            <el-main slot="body" class="dialog-body">
              <el-header class="dialog-header" height="40px">
                <div></div>
                <div class="dialog-header-right" style="flex: none">
                  <el-button
                    size="mini"
                    icon="el-icon-circle-check"
                    type="primary"
                    @click="saveAddTeam"
                    plain>
                    保存
                  </el-button>
                </div>
              </el-header>
              <el-container style="padding: 20px;">
                <el-table
                  border
                  class="team-table"
                  max-height="300px"
                  size="mini"
                  @row-click="rowClick($event, $refs['addTeam'])"
                  ref="addTeam"
                  :data="addTeamData"
                  style="width: 100%">
                  <el-table-column
                    width="50"
                    label="序号"
                    type="index">
                  </el-table-column>
                  <el-table-column
                    width="50"
                    type="selection">
                  </el-table-column>
                  <el-table-column
                    prop="fWorkTeamName"
                    label="团队名称">
                  </el-table-column>
                </el-table>
              </el-container>
            </el-main>
          </custom-dialog>
          <!--添加成员-->
          <custom-dialog
            append2body
            :show="showManageMember"
            title="添加成员"
            width="548px"
            @before-close="showManageMember=false">
            <el-main slot="body" class="dialog-body">
              <el-header class="dialog-header" height="40px">
                <el-form inline @submit.native.prevent class="mm-form">
                  <el-form-item label="关键字查询">
                    <el-input 
                      size="mini" 
                      v-focus="showManageMember" 
                      @clear="getAllowAddableMemberList"
                      v-enter="getAllowAddableMemberList"
                      v-model="searchOperatorKeyword" 
                      clearable 
                      placeholder="职员姓名或登录名"></el-input>
                  </el-form-item>
                  <el-form-item>
                  </el-form-item>
                  <el-form-item style="margin-left: auto">
                    <el-button size="mini" @click="getAllowAddableMemberList">查询</el-button>
                    <el-button :disabled="!selectedManageMemberList.length" size="mini" type="primary"  @click="confirmManageMember">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-header>
              <el-table
                border
                style="padding: 10px;"
                class="team-table"
                max-height="450px"
                min-height="200px"
                size="mini"
                :data="allowAddableMemberList"
                element-loading-text="数据加载中"
                element-loading-spinner="el-icon-loading"
                v-loading="turnLoading"
                ref="allowAddableMemberList"
                @row-click="rowClick($event, $refs['allowAddableMemberList'])"
                @selection-change="selectedManageMemberList=$event">
                <el-table-column
                  type="selection"
                  width="40">
                </el-table-column>
                <el-table-column
                  prop="fOperatorId"
                  label="操作员编号"
                  resizable
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="employeeName"
                  label="职员姓名"
                  resizable
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="operName"
                  label="登录名"
                  resizable
                  show-overflow-tooltip
                >
               </el-table-column>
            </el-table>
            </el-main>   
          </custom-dialog>
          <!--设置管理角色-->
          <custom-dialog
            :show="showRoleSet"
            title="设置管理角色"
            append2body
            @before-close="showRoleSet=false"
            width="600px">
            <el-main slot="body" class="dialog-body">
              <el-header class="dialog-header" height="40px">
                <el-button
                 style="margin-left: auto" 
                 size="mini" 
                 icon="el-icon-circle-check"
                 type="primary" 
                 plain
                 @click="saveRoleSet">保存</el-button>
              </el-header>
              <el-table
                style="padding: 10px;"
                border
                class="team-table"
                max-height="450px"
                min-height="200px"
                size="mini"
                :data="roleSetTableData"
              >
                <el-table-column
                  width="50"
                  label="序号"
                  type="index">
                </el-table-column>
                <el-table-column
                  prop="operatorId"
                  label="操作员编号"
                  resizable
                  width="90px"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="employeeName"
                  label="职员姓名"
                  resizable
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="operName"
                  label="登录名"
                  resizable
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="projRoleCode"
                  label="项目管理权限"
                  resizable
                  width="200px"
                >
                  <template slot-scope="{row}">
                    <el-select style="width: 160px" v-model="row.projRoleCode" placeholder="请选择角色" filterable default-first-option  size="mini">
                      <el-option 
                        v-for="item in teamRoles"
                        :key="item.projRoleCode"
                        :label="item.projRoleName"
                         :value="item.projRoleCode"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </el-main>
          </custom-dialog>
          
        </el-main>
      </custom-dialog>
    </div>`,
  data(){
    return{
      show: false,
      teamTableData: [],
      showSelectOperator: false,
      showAddTeam: false,
      selectOperatorData:[],
      teamRoles: [],
      addTeamData:[],
      currentTeamName: '',
      currentTeamId: '',
      allowAddableMemberList: [],
      showManageMember: false,
      searchOperatorKeyword: '',
      selectedManageMemberList: [],
      turnLoading: false,
      roleSetTableData: [],
      showRoleSet: false
    }
  },
  methods:{
    close(){
      this.show = false
      this.requestProjectTeam()
    },
    deleteRow({f_project_id, f_work_team_id}){
      Axios.post('/projTeam/del', JSON2FormData({f_project_id, f_work_team_id, operEntrance: '项目详情删除参与团队'})).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('删除失败！')
        this.$message.success('删除成功！')
        this.requestProjectTeam()
      }).catch(e=>console.warn(e))
    },
    setOperator(row){
      this.showSelectOperator = true
      this.currentTeamName = row.f_work_team_name || ''
      this.currentTeamId = row.f_work_team_id || ''
      this.requestSelectOperator(row.f_work_team_id)
    },
    saveSelectOperator(){
      const selectOperatorSelection = this.$refs['selectOperator'].selection
      if(selectOperatorSelection.length < 1)
        return this.$message.warning('最少选择选择一条数据！')
      if(selectOperatorSelection.some(it =>!it.projRoleCode))
        return this.$message.warning('存在未设置的成员角色,保存失败！')
      const queryData =
        selectOperatorSelection.map(row=>({
        projectId,
        workTeamId: row.fWorkTeamId,
        operatorId: row.fOperatorId,
        projRoleCode: row.projRoleCode,
        operEntrance: '项目详情设置管理人员'
      }))
      Axios.post('/projMember/batchSave', queryData).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('数据加载失败！0x01')
        this.$message.success('保存成功！')
        this.requestProjectTeam()
        this.showSelectOperator = false
      }).catch(e=>console.warn(e))
    },
    addItemClick(){
      const notIds = this.teamTableData.map(row=>row.f_work_team_id)
      this.requestAddTeam(notIds, res=>this.showAddTeam = true)
    },
    saveAddTeam(){
      const addTeamSelection = this.$refs['addTeam'].selection
      if(addTeamSelection.length < 1)
        return this.$message.warning('最少选择选择一条数据！')
      const queryData =
        addTeamSelection.map(row=>({
          f_project_id: projectId,
          f_work_team_id: row.fWorkTeamId,
          operEntrance: '新增参与团队'
        }))
      Axios.post('/projTeam/batchAdd', queryData).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('保存失败！')
        this.$message.success('保存成功！')
        this.requestProjectTeam()
        this.showAddTeam = false
      }).catch(e=>console.warn(e))
    },
    async open(){
      try{
        await this.requestTeamRole()
      }catch(e){
        console.warn(e);
        this.$message.error('获取角色失败！')
      }
      this.requestProjectTeam()
      this.show = true
    },
    rowClick(row, ref){
      ref.toggleRowSelection(row)
    },
    async requestTeamRole(){ // 查询项目角色
      const {data:res} = await Axios.post('/projRole/get', null).catch(e=>console.warn(e));
      console.log('requestTeamRole', res);
      if(res.state !== 1)return this.$message.error('获取角色失败！'+ (res.msg || ''))
      this.teamRoles = res.data
    },
    requestProjectTeam(){
      Axios.post('/projTeam/getByProjectId', JSON2FormData({projectId})).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('数据加载失败！')
        this.teamTableData = res.data
        this.$emit('operation-complete', res.data)
      }).catch(e=>console.warn(e))
    },
    requestSelectOperator(workTeamId){
      Axios.post('/projMember/getByPidAndTeamId', JSON2FormData({workTeamId, projectId})).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('数据加载失败！')
        this.selectOperatorData = res.data
      }).catch(e=>console.warn(e))
    },
    requestAddTeam(notIds, callback){
      Axios.post('/workTeam/getPage', JSON2FormData({notIds})).then(({data: res})=>{
        if(res.state !== 1)return this.$message.error('数据加载失败！')
        this.addTeamData = res.rows
        this.$nextTick(()=>callback(res))
      }).catch(e=>console.warn(e))
    },
    async addManageMember() {
      await this.getAllowAddableMemberList()
      this.showManageMember = true
    },
    confirmManageMember() {
      const rows = this.selectedManageMemberList.map(row => {
        const {employeeName, fOperatorId, fWorkTeamId, operName} = row
        return {
          id: '',
          projRoleCode: '',
          employeeName,
          workTeamId: fWorkTeamId,
          operatorId: fOperatorId,
          projectId,
          operName,
        }
      })
      this.editManageMember(rows)
    },
    editManageMember(rows) {
      /**
       * employeeName
       * projRoleCode
       * workTeamId
       * operatorId
       * projectId
       * id
       * */
      this.roleSetTableData = rows.map(row => ({...row}))
      this.showRoleSet = true
    },
    async deleteManageMember({id}) {
      try {
        if (!id) return this.$errorMsg('数据异常')
        const post = {id, operEntrance: '项目详情删除管理人员'}
        const {data} = await Axios.post('/projMember/delete', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject()
        this.$message.success('删除成功')
        this.requestSelectOperator(this.currentTeamId)
        this.requestProjectTeam()
      }catch (e) {
        e && e !== 'cancel' && console.warn(e)
        this.$errorMsg('删除失败')
      }
    },
    async getAllowAddableMemberList() {
      try {
        const params = new FormData()
        params.set('fWorkTeamId', this.currentTeamId)
        params.set('notProjMemberProjectId', projectId)
        params.set('keyword', this.searchOperatorKeyword || '')
        this.turnLoading = true
        const {data} = await Axios.post('/workteamMenber/getPage', params)
        const {rows, state} = data
        if (state !== 1) await Promise.reject()
        this.allowAddableMemberList = rows
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
        this.$message.error('获取数据出错')
      } finally {
        this.turnLoading = false
      }
    },
    async saveRoleSet() {
      try {
        let isEdit = false
        const post = this.roleSetTableData.map(row => {
          if (!row.projRoleCode) throw Error('required')
          if (!row.id) return {...row, operEntrance: '项目详情设置管理人员'}
          const {id, projRoleCode} = row
          // 存在Id表示编辑
          isEdit = true
          return {id, projRoleCode, operEntrance: '项目详情设置管理人员'}
        })
        const {data} = await Axios.post('/projMember/saveList', post)
        if (data.state !== 1) await Promise.reject()
        this.$message.success('保存成功')
        this.showRoleSet = false
        this.showManageMember = false
        this.requestSelectOperator(this.currentTeamId)
        this.requestProjectTeam()
        !isEdit && (this.showSelectOperator = false)
      }catch (e) {
        if (e && e.message === 'required')
          return this.$errorMsg('项目管理权限不能为空')
        e && e !== 'cancel' && console.warn(e)
        this.$errorMsg('保存失败')
      }
    }
  },
  mounted(){
    this.requestProjectTeam()
  },
  components:{
    customDialog
  }
}