;(function (window) {
  const template = `
    <div class="card">
        <div class="card__header">
          <slot name="header">
            <slot name="title">
              <span class="card__title">{{title}}</span>
            </slot>
            <slot name="link">
              <el-link 
                v-if="showLink" 
                @click="$emit('link-click', $event)" 
                :icon="linkIcon" 
                class="card__link" 
                :underline="false">
              </el-link>
            </slot>
          </slot>
        </div>
        <div class="card__body">
          <slot></slot>
        </div>
    </div>
  `
  window.AlexCard = {
    template,
    name: '<PERSON><PERSON><PERSON>',
    props: {
      title: {
        type: String,
        default: ''
      },
      linkIcon: {
        type: String,
        default: 'el-icon-edit'
      },
      showLink: {
        type: Boolean,
        default: false
      }
    }
  }
})(window)
