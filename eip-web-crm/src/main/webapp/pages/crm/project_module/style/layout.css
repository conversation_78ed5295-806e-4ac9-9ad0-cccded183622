:root {
    --base-width: 1440px;
    --const-multiple: 0.003625;
    --coefficient: calc((100vw - var(--base-width)) * var(--const-multiple));
    --gutter: 24px;
    /*兼容老版本*/
    --column: 30px;
    --column: max(27px, calc(calc(100vw - 824px) / 24));
    --item: calc(var(--gutter) + var(--column));
    --full-width: calc(23 * var(--item) + var(--column));
}
.alex-container{
    width: var(--full-width);
    box-sizing: content-box;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
}
