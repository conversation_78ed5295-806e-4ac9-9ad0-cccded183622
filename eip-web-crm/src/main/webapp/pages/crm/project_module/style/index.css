@charset "UTF-8";
html, body {
  margin: 0;
  padding: 0;
}

.text-over-hidden, #app .main .layout-aside .project-attachment .attachment__list .attachment__item .filename {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

[v-cloak] {
  display: none;
}

::-webkit-scrollbar-thumb {
  display: block;
  width: 6px;
  margin: 0 auto;
  background: #c0c2c4;
}
::-webkit-scrollbar-thumb:hover {
  background: #78797b;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.card {
  width: 100%;
  background-color: #fff;
  margin-bottom: calc(var(--column) * 0 + var(--gutter) * 0 + 20px);
}
.card .card__header {
  padding-left: calc(var(--column) * 0 + var(--gutter) * 0 + 19px);
  padding-right: calc(var(--column) * 0 + var(--gutter) * 0 + 16px);
  height: calc(var(--column) * 1 + var(--gutter) * 0 + 8px);
  line-height: calc(var(--column) * 1 + var(--gutter) * 0 + 8px);
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card .card__header .card__title {
  color: rgba(0, 0, 0, 0.85);
}
.card .card__header .card__link {
  color: #1890FF;
  font-size: 16px;
}
.card .card__body {
  padding: var(--gutter);
}

.link-card {
  cursor: pointer;
  width: calc(var(--column) * 5 + var(--gutter) * 4 + 0px);
  height: 60px;
  display: flex;
  margin-right: var(--gutter);
  margin-bottom: var(--gutter);
}
.link-card .el-image {
  max-width: 60px;
  max-height: 60px;
  width: var(--item);
  height: var(--item);
  flex-shrink: 0;
  margin-right: 14px;
}
.link-card .right {
  display: flex;
  flex-direction: column;
  /*兼容老版本*/
  line-height: calc(60px / 2);
  line-height: min(var(--column) * 0.5 + var(--gutter) * 0.5 + 0px, 30px);
}
.link-card .right .title {
  color: #666;
}
.link-card .right .title:hover {
  color: #1890FF;
}
.link-card .right .count > span {
  font-size: 14px;
  color: #666;
}
.link-card .right .count > span:first-child {
  margin-right: 5px;
}
.link-card .right .count > span:hover span {
  color: #1890FF;
}
.link-card .right .count > span span {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bolder;
  vertical-align: bottom;
}

.alex-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 7rem;
  padding-bottom: var(--gutter);
  flex-direction: column;
}
.alex-link:hover .alex-link__text {
  color: #1890FF;
}
.alex-link .el-image {
  width: var(--item);
  height: var(--item);
}
.alex-link .alex-link__text {
  color: #666;
  font-size: 15px;
  /*兼容老版本*/
  margin-top: 10px;
  margin-top: max(var(--column) * 1 + var(--gutter) * 0 + -29px, 5px);
}
@media screen and (min-width: 1680px) {
  .alex-link .alex-link__text {
    font-size: 16px;
  }
}

#app {
  background-color: #f0f2f5;
  min-height: 100vh;
}
#app .header {
  background-color: #fff;
}
#app .header .project-primary {
  padding-top: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#app .header .project-primary .header__left {
  display: flex;
  align-items: center;
}
#app .header .project-primary .header__left .el-image {
  width: calc(var(--column) * 2 + var(--gutter) * 1 + 10px);
  height: var(--item);
  margin-right: var(--gutter);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
}
#app .header .project-primary .header__left .el-image img {
  width: 100%;
}
#app .header .project-primary .header__left .el-image .image-error {
  font-size: 20px;
}
#app .header .project-primary .header__left .project-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#app .header .project-primary .header__left .project-info > p {
  margin: 0;
  height: calc(var(--column) * 0.5 + var(--gutter) * 0.5 + 0px);
  line-height: calc(var(--column) * 0.5 + var(--gutter) * 0.5 + 0px);
}
#app .header .project-primary .header__left .project-info > p .info__title {
  font-weight: bolder;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  margin-right: var(--gutter);
}
#app .header .project-primary .header__left .project-info > p .info__link {
  margin-right: var(--gutter);
  font-size: 15px;
  vertical-align: unset;
  color: #1890FF;
}
#app .header .project-primary .header__left .project-info > p .info__id, #app .header .project-primary .header__left .project-info > p .info__state, #app .header .project-primary .header__left .project-info > p .info__charge {
  color: #999;
  font-size: 14px;
}
#app .header .project-primary .header__left .project-info > p .info__id.info__id, #app .header .project-primary .header__left .project-info > p .info__state.info__id, #app .header .project-primary .header__left .project-info > p .info__charge.info__id {
  margin-right: var(--gutter);
}
#app .header .project-primary .header__right .project-info {
  height: var(--item);
}
#app .header .project-primary .header__right .project-info .el-dropdown-link {
  color: #1890FF;
  cursor: pointer;
}
#app .header .project-primary .header__right .project-info > div:not(:last-child) {
  margin-right: 10px;
}
#app .header .project-field {
  padding-top: var(--gutter);
  padding-bottom: calc(var(--column) * 0 + var(--gutter) * 0.5 + 0px);
  display: flex;
  flex-wrap: wrap;
}
#app .header .project-field .field-item {
  display: flex;
  margin-right: calc(var(--column) * 1 + var(--gutter) * 1.5 + 0px);
  margin-bottom: calc(var(--column) * 0 + var(--gutter) * 0.5 + 0px);
  font-size: 14px;
}
#app .header .project-field .field-item .field-item__label {
  width: calc(var(--column) * 2 + var(--gutter) * 1 + 0px);
  word-break: keep-all;
  color: #999;
  margin-right: var(--gutter);
}
#app .header .project-field .field-item .field-item__value {
  width: calc(var(--column) * 5 + var(--gutter) * 4 + 0px);
  color: #666;
}
#app .main {
  display: flex;
  margin-top: calc(var(--column) * 0 + var(--gutter) * 0 + 20px);
}
#app .main .layout-aside {
  flex-basis: calc(var(--column) * 6 + var(--gutter) * 5 + 0px);
  margin-right: var(--gutter);
  width: 0;
  flex-shrink: 0;
}
#app .main .layout-aside .part-team__list {
  display: flex;
  flex-direction: column;
  max-height: 300px;
  overflow-y: auto;
}
#app .main .layout-aside .part-team__list .part-team__item {
  color: #666;
}
#app .main .layout-aside .part-team__list .part-team__item:not(:last-child) {
  margin-bottom: var(--gutter);
}
#app .main .layout-aside .part-team__list .part-team__item p {
  margin: 0;
}
#app .main .layout-aside .part-team__list .part-team__item p.part-team__title {
  font-size: 12px;
}
@media screen and (min-width: 1680px) {
  #app .main .layout-aside .part-team__list .part-team__item p.part-team__title {
    font-size: 14px;
  }
}
#app .main .layout-aside .part-team__list .part-team__item p.part-team__info {
  padding-top: 5px;
  font-size: 14px;
  line-height: 20px;
  color: #333;
}
@media screen and (min-width: 1680px) {
  #app .main .layout-aside .part-team__list .part-team__item p.part-team__info {
    font-size: 16px;
  }
}
#app .main .layout-aside .projclient-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  max-height: 300px;
  overflow-y: auto;
}
#app .main .layout-aside .projclient-info .projclient-info__item {
  display: flex;
  flex-direction: column;
  text-align: center;
}
#app .main .layout-aside .projclient-info .projclient-info__item span {
  font-size: 14px;
  color: #666;
}
#app .main .layout-aside .projclient-info .projclient-info__item span:first-child {
  margin-bottom: 8px;
}
#app .main .layout-aside .projclient-info .projclient-info__item span.item__count {
  color: #1890FF;
  font-size: 20px;
}
#app .main .layout-aside .project-related {
  max-height: 300px;
  overflow-y: auto;
}
#app .main .layout-aside .project-related .el-tree-node__label:hover {
  color: #1890FF;
}
#app .main .layout-aside .card-icon--upload {
  background: url("../../img/upfile.png") no-repeat;
  background-size: 16px;
  display: inline-block;
  width: 16px;
  height: 16px;
}
#app .main .layout-aside .project-attachment .attachment__list {
  display: flex;
  flex-direction: column;
  max-height: 300px;
  overflow-y: auto;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item {
  display: flex;
  height: 40px;
  align-items: center;
  margin-bottom: 10px;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item:hover .del-icon {
  display: block;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item:hover .filename {
  color: #1890FF;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item .file-icon {
  padding: 5px;
  width: 38px;
  height: 40px;
  box-sizing: border-box;
  cursor: pointer;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item .filename {
  color: #666;
  flex-basis: calc(100% - 56px);
  flex-shrink: 0;
  width: 0;
  font-size: 15px;
  cursor: pointer;
}
#app .main .layout-aside .project-attachment .attachment__list .attachment__item .del-icon {
  display: none;
  font-size: 18px;
  margin-left: auto;
  cursor: pointer;
}
#app .main .layout-aside .project-attachment .attachment__placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 30px;
}
#app .main .layout-aside .project-attachment .attachment__placeholder span {
  font-size: 15px;
  color: #666;
}
#app .main .layout-content {
  flex-basis: 100%;
}
#app .main .layout-content .card__body {
  padding-left: calc(var(--column) * 1 + var(--gutter) * 0 + 0px);
  padding-bottom: 0;
}
#app .main .layout-content .info-link__list,
#app .main .layout-content .link-card__list {
  display: flex;
  flex-wrap: wrap;
}

.project-related__dialog .parent-project {
  width: 280px;
}
.project-related__dialog .tag-wrapper {
  max-height: 200px;
  overflow: hidden auto;
}
.project-related__dialog .tag-wrapper .el-tag.el-tag--info {
  margin: 5px;
}
.project-related__dialog .append-children {
  padding-top: 10px;
}
.project-related__dialog .append-children .el-link.el-link--primary {
  font-size: 16px;
}
.project-related__dialog .append-children .el-link.el-link--primary i::before {
  font-size: 18px;
}

/*# sourceMappingURL=index.css.map */
