html, body {
  margin: 0;
  padding: 0;
}

.text-over-hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

[v-cloak] {
  display: none;
}


$--gutter: var(--gutter) !default; // 24
$--column: var(--column) !default; // 47
$--item: var(--item) !default; // 71
$--full-width: var(--full-width) !default; // 1680 = computed(24, 23)
$--color-title: rgba(0, 0, 0, 0.85) !default;
$--color-label: #999 !default;
$--color-text: #666 !default;
$--color-b-text: #333 !default;
$--color-primary: #1890FF !default;
$--background-color: #f0f2f5 !default;
$--break-point: 1680px !default;


::-webkit-scrollbar-thumb {
  display: block;
  width: 6px;
  margin: 0 auto;
  background: mix(#000, $--background-color, 20%);

  &:hover {
    background: mix(#000, $--background-color, 50%);
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}


@function computed($column: 1, $getter: 0, $other: 0px) {
  @return calc(#{$--column} * #{$column} + #{$--gutter} * #{$getter} + #{$other});
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin gutter($dir: right, $prop: margin, $value: $--gutter) {
  #{$prop}-#{$dir}: $value;
}

// 卡片样式
.card {
  width: 100%;
  background-color: #fff;
  @include gutter(bottom, margin, computed(0, 0, 20px));

  .card__header {
    $--line-height: computed(1, 0, 8px);
    @include gutter(left, padding, computed(0, 0, 19px));
    @include gutter(right, padding, computed(0, 0, 16px));
    height: $--line-height;
    line-height: $--line-height;
    border-bottom: 1px solid #e8e8e8;
    @include flex-between;

    .card__title {
      color: $--color-title;
      //font-weight: bolder;
    }

    .card__link {
      color: $--color-primary;
      font-size: 16px;
    }
  }

  .card__body {
    padding: $--gutter;
  }
}

// 卡片链接
.link-card {
  cursor: pointer;
  width: computed(5, 4);
  $--height: 60px;
  height: $--height;
  display: flex;
  @include gutter(right);
  @include gutter(bottom);

  .el-image {
    max-width: $--height;
    max-height: $--height;
    width: $--item;
    height: $--item;
    flex-shrink: 0;
    @include gutter(right, margin, 14px);
  }

  .right {
    display: flex;
    flex-direction: column;
    /*兼容老版本*/
    line-height: calc(#{$--height} / 2);
    line-height: min(computed(0.5, 0.5), $--height / 2);

    .title {
      color: $--color-text;

      &:hover {
        color: $--color-primary;
      }
    }

    .count {
      > span {
        font-size: 14px;
        color: $--color-text;

        &:first-child {
          margin-right: 5px;
        }

        &:hover span {
          color: $--color-primary;
        }

        span {
          font-size: 16px;
          color: $--color-title;
          font-weight: bolder;
          vertical-align: bottom;
        }
      }
    }
  }
}

// 链接
.alex-link {
  cursor: pointer;
  @include flex-center;
  width: 7rem;
  //@include gutter(right, margin, computed(1, 1, -10px));
  @include gutter(bottom, padding);
  flex-direction: column;

  &:hover .alex-link__text {
    color: $--color-primary;
  }

  .el-image {
    width: $--item;
    height: $--item;
  }

  .alex-link__text {
    color: $--color-text;
    font-size: 15px;

    @media screen and (min-width: $--break-point) {
      font-size: 16px;
    }
    /*兼容老版本*/
    margin-top: 10px;
    margin-top: max(computed(1, 0, -29px), 5px);
  }
}

#app {
  background-color: $--background-color;
  min-height: 100vh;

  .header {
    background-color: #fff;

    .project-primary {
      padding-top: 32px;
      @include flex-between;
      $--line-height: computed(0.5, 0.5);

      .header__left {
        display: flex;
        align-items: center;

        .el-image {
          width: computed(2, 1, 10px);
          height: $--item;
          @include gutter;
          @include flex-center;
          background-color: $--background-color;

          img {
            width: 100%;
          }

          .image-error {
            font-size: 20px;
          }
        }

        .project-info {
          display: flex;
          flex-direction: column;
          justify-content: center;

          > p {

            margin: 0;
            height: $--line-height;
            line-height: $--line-height;

            .info__title {
              font-weight: bolder;
              color: $--color-title;
              font-size: 16px;
              @include gutter;
            }

            .info__link {
              @include gutter;
              font-size: 15px;
              vertical-align: unset;
              color: $--color-primary;
            }

            .info__id, .info__state, .info__charge {
              color: $--color-label;
              font-size: 14px;

              &.info__id {
                @include gutter;
              }
            }
          }
        }
      }

      .header__right {
        .project-info {
          height: $--item;

          .el-dropdown-link {
            color: $--color-primary;
            cursor: pointer;
          }

          > div:not(:last-child) {
            margin-right: 10px;
          }
        }
      }
    }

    .project-field {
      @include gutter(top, padding);
      @include gutter(bottom, padding, computed(0, 0.5));
      display: flex;
      flex-wrap: wrap;

      .field-item {
        display: flex;
        @include gutter(right, margin, computed(1, 1.5));
        @include gutter(bottom, margin, computed(0, 0.5));
        font-size: 14px;

        .field-item__label {
          width: computed(2, 1);
          word-break: keep-all;
          color: $--color-label;
          @include gutter(right);
        }

        .field-item__value {
          width: computed(5, 4);
          color: $--color-text;
        }
      }
    }
  }

  .main {
    display: flex;
    @include gutter(top, margin, computed(0, 0, 20px));

    .layout-aside {
      flex-basis: computed(6, 5);
      @include gutter;
      // 防止子元素超出
      width: 0;
      flex-shrink: 0;

      @mixin max-height($mh:300px) {
        max-height: $mh;
        overflow-y: auto;
      }

      .part-team__list {
        display: flex;
        flex-direction: column;
        @include max-height;

        .part-team__item {
          color: $--color-text;

          &:not(:last-child) {
            @include gutter(bottom);
          }

          p {
            margin: 0;

            &.part-team__title {
              font-size: 12px;
              @media screen and (min-width: $--break-point) {
                font-size: 14px;
              }
            }

            &.part-team__info {
              padding-top: 5px;
              font-size: 14px;
              line-height: 20px;
              color: $--color-b-text;
              @media screen and (min-width: $--break-point) {
                font-size: 16px;
              }
            }
          }
        }
      }

      .projclient-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        @include max-height;

        .projclient-info__item {
          display: flex;
          flex-direction: column;
          text-align: center;

          span {
            font-size: 14px;
            color: $--color-text;

            &:first-child {
              margin-bottom: 8px;
            }

            &.item__count {
              color: $--color-primary;
              font-size: 20px;
            }
          }
        }
      }

      .project-related {
        @include max-height;

        .el-tree-node__label {
          &:hover {
            color: $--color-primary;
          }
        }
      }

      .card-icon--upload {
        background: url("../../img/upfile.png") no-repeat;
        background-size: 16px;
        display: inline-block;
        width: 16px;
        height: 16px;
      }

      .project-attachment {
        .attachment__list {
          display: flex;
          flex-direction: column;
          @include max-height;

          .attachment__item {
            $--height: 40px;
            $--file-icon-width: 38px;
            $--del-icon-size: 18px;
            display: flex;
            height: $--height;
            align-items: center;
            margin-bottom: 10px;

            &:hover {
              .del-icon {
                display: block;
              }

              .filename {
                color: $--color-primary;
              }
            }

            .file-icon {
              padding: 5px;
              width: $--file-icon-width;
              height: $--height;
              box-sizing: border-box;
              cursor: pointer;
            }

            .filename {
              color: $--color-text;
              @extend .text-over-hidden;
              flex-basis: calc(100% - #{$--del-icon-size + $--file-icon-width});
              flex-shrink: 0;
              width: 0;
              font-size: 15px;
              cursor: pointer;
            }

            .del-icon {
              display: none;
              font-size: $--del-icon-size;
              margin-left: auto;
              cursor: pointer;
            }
          }
        }

        .attachment__placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          padding: 30px;

          span {
            font-size: 15px;
            color: $--color-text;
          }
        }
      }

    }

    .layout-content {
      flex-basis: 100%;

      .card__body {
        @include gutter(left, padding, computed(1, 0));
        padding-bottom: 0;
      }

      .info-link__list,
      .link-card__list {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}

// append to body element
.project-related__dialog {
  .parent-project {
    width: 280px;
  }

  .tag-wrapper {
    max-height: 200px;
    overflow: hidden auto;

    .el-tag.el-tag--info {
      margin: 5px;
    }
  }

  .append-children {
    padding-top: 10px;

    .el-link.el-link--primary {
      font-size: 16px;

      i::before {
        font-size: 18px;
      }
    }
  }
}
