const ProjectLinkShow = {
  template: `<div class="project-link-show"><custom-dialog
        :show="show"
        @before-close="close"
        :title="title"
        width="500px">
      <el-main slot="body" class="dialog-body" ref="body">
        <div>
          <div class="all_address" style="display: flex;align-items: center;">
            <h6 style="margin-top: 8px;margin-right: 70px;">版本</h6>
            <div style="width: 200px;">
              <el-radio v-if="releaseReg" v-model="version" label="1">中文版</el-radio>
              <el-radio v-if="releaseRegEn" v-model="version" label="2">英文版</el-radio>
            </div>
          </div>
          <div class="all_address">
            <h6 style="margin-top: 15px;">手机登记地址</h6>
            <textarea cols="50" rows="3" readonly id="link-show-mobile">{{ link.mobile }}</textarea>
            <div style="margin: 5px 0">
              <el-button class="btn-hover" size="small" plain @click="copyLink('mobile')">复制链接</el-button>
              <el-button size="small" plain @click="openLink('mobile')">直接打开</el-button>
            </div>
          </div>
          <div class="all_address">
            <h6 style="margin-top: 15px;">电脑登记地址</h6>
            <textarea cols="50" rows="3" id="link-show-pc" readonly>{{ link.pc }}</textarea>
            <div style="margin: 5px 0">
              <el-button  size="small" class="btn-hover"  plain @click="copyLink('pc')">复制链接</el-button>
              <el-button  size="small" plain @click="openLink('pc')">直接打开</el-button>
            </div>
          </div>
          <div class="address_download" v-if="link.mobile">
            <ul>
              <li>
                <div class="qr_code"></div>
                <div class="address_download_button">
                  <a download="qrcode.jpg"></a>
                </div>
              </li>
              <li>
                <div class="prompt_text">
                  <p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
                  <el-button  size="small" class="btn-hover" plain @click="downPic()">下载二维码</el-button>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </el-main>
      </custom-dialog>
    </div>`,
  data(){
    return {
      show: false,
      linkPre: window.origin + '/web-reg-server',
      cid: getCookie('operatorId') || '',
      operName: decodeURI(getCookie('OperName') || ''),
      qrcodeLogo: '',
      targetType: 1,
      version: '1',
      releaseReg: false,
      releaseRegEn: false,
    }
  },
  computed: {
    title() {
      return (this.operName ? (this.operName +'专属') : '') + '观众参展链接'
    },
    showLink(){
      return this.cid && this.projectData.releaseReg && this.projectData.exhibitCode && this.projectData.f_org_num && this.projectData.projectId;
    },
    linkTail(){
      return  `?eid=${this.projectData.exhibitCode}&orgnum=${this.projectData.f_org_num}&target=${this.targetType}&pid=${this.projectData.projectId}&version=${this.version}&ctid=1&cid=${this.cid}`;
    },
    link(){
      return {
        mobile: this.linkPre + '/mobile/vistor-register-m.html' + this.linkTail,
        pc: this.linkPre + '/pc/vistor-register.html' + this.linkTail
      };
    },
    qrcodeLogoURL() {
      return this.qrcodeLogo
    }
  },
  props: ['projectData'],
  methods:{
    close(){
      this.show = false
    },
    open(targetType, settings) {
      console.log(settings)
      const {releaseReg, releaseRegEn} = settings
      this.releaseReg = releaseReg
      this.releaseRegEn = releaseRegEn
      this.version = !releaseReg ? '2' : '1'
      this.show = true
      this.targetType = targetType || 1
      this.$nextTick(() => {
        this.getQRCodeLogo().then(() => {
          this.$nextTick(() => {
            $('.project-link-show .qr_code').html("").qrcode({
              src: this.qrcodeLogoURL,
              text: this.link.mobile,
              correctLevel: 0,
              width: '1000', //二维码的宽度
              height: '1000', //二维码的高度
              imgWidth : 200,			 //图片宽
              imgHeight : 200,			 //图片高
            })
          })
        })
      })
    },
    copyLink(type){
      if(type){
        document.getElementById('link-show-'+type).select();
        document.execCommand("Copy");
      }
    },
    openLink(type){
      type && window.open(this.link[type])
    },
    downPic(){
      var type = 'png';
      var c = $('.project-link-show .qr_code').find('canvas')[0];
      var imgdata = c.toDataURL("image/png");
      var fixtype = function(type) {
        type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
        var r = type.match(/png|jpeg|bmp|gif/)[0];
        return 'image/' + r;
      };
      imgdata = imgdata.replace(fixtype(type), 'image/octet-stream');
      var savaFile = function(data, filename) {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
      };
      savaFile(imgdata, '观众登记中文版二维码.png');
    },
    async getQRCodeLogo() {
      //兼容之前已经上传的图片
      function queryData2image(src) {
        const inner = src => {
          if (!src) return '/RegSever/RegPage/images/top-lucency.png'
          const prefix = location.origin
          src = src.replace(/\\/g, '/')
          if (/^(https?:)?\/\//.test(src)) return src
          if (src.startsWith('/')) return prefix + src
          return prefix + '/image/' + src
        }
        const url = inner(src)
        if (!url) return ''
        try {
          return new URL(url).host.indexOf('aliyuncs.com') !== -1
            ? `/eip-web-sponsor/upload/ossOut?ossUrl=${encodeURIComponent(url)}`
            : url
        } catch (e) {
          return url
        }
      }

      function getLogoImgs(projectId, version) {
        let QRCodeLogo = ''
        $.ajax({
          url: '/eip-web-sponsor/api/project/getProjectLogo',
          data: {projectId},
          type: "post",
          async: false,
          success(data) {
            if (data.state !== 1) return
            const buyerLogoKey = version === 2 ? 'buyerRegEnLogo' : 'buyerRegCnLogo'
            const result = data.data || {}
            QRCodeLogo = result[buyerLogoKey] || result['exhibitionLogo']
          },
          error(err) {
            console.warn(err)
          }
        });
        return queryData2image(QRCodeLogo)
      }

      this.qrcodeLogo = getLogoImgs(this.projectData.projectId)
    }
  },
  watch: {
    'link.mobile'(newName) {
      if(newName){
        $('.project-link-show .qr_code').html("").qrcode({
          text: newName,
          src: this.qrcodeLogoURL,
          correctLevel: 0,
          width: '1000', //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,			 //图片宽
          imgHeight : 200,			 //图片高
        });
      }
    }
  },
  components:{
    customDialog
  }
}