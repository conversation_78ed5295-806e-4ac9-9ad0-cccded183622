const ProjectApplyShow = {
  template: `<div class="project-apply-show"><custom-dialog
        :show="show"
        @before-close="close"
        title="参展申请"
        width="550px">
      <el-main slot="body" class="dialog-body" ref="body">
        <div>
          <div class="all_address" style="display: flex;align-items: center;">
            <h6 style="margin-top: 8px;margin-right: 70px;">版本</h6>
            <div style="width: 200px;">
              <!-- <el-select v-model="version" size="mini">
                 <el-option label="中文版" value="1"></el-option>
                 <el-option label="英文版" value="2"></el-option>
              </el-select> -->
              <el-radio v-model="version" label="1">中文版</el-radio>
              <el-radio v-model="version" label="2">英文版</el-radio>
            </div>
          </div>
          <div class="all_address">
            <h6 style="margin-top: 15px;">手机申请地址</h6>
            <textarea cols="50" rows="3" readonly id="apply-show-mobile">{{ link.mobile }}</textarea>
            <div style="margin: 5px 0">
              <el-button class="btn-hover" size="small" plain @click="copyLink('mobile')">复制链接</el-button>
              <el-button size="small" plain @click="openLink('mobile')">直接打开</el-button>
            </div>
          </div>
          <div class="all_address">
            <h6 style="margin-top: 15px;">电脑申请地址</h6>
            <textarea cols="50" rows="3" id="apply-show-pc" readonly>{{ link.pc }}</textarea>
            <div style="margin: 5px 0">
              <el-button  size="small" class="btn-hover"  plain @click="copyLink('pc')">复制链接</el-button>
              <el-button  size="small" plain @click="openLink('pc')">直接打开</el-button>
            </div>
          </div>
          <div class="address_download">
            <ul>
              <li>
                <div class="qr_code"></div>
                <div class="address_download_button">
                  <a download="qrcode.jpg"></a>
                </div>
              </li>
              <li>
                <div class="prompt_text">
                  <!--<p>左侧手机二维码点击下载即可手机扫码申请</p>-->
                  <el-button  size="small" class="btn-hover" plain @click="downPic()">下载二维码</el-button>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </el-main>
      </custom-dialog>
    </div>`,
  data(){
    return {
      show: false,
      linkPre: window.origin,
      cid: getCookie('operatorId') || '',// 无用
      operName: decodeURI(getCookie('OperName') || ''),// 无用
      qrcodeLogo: '',
      targetType: '4',
      version: '1',
    }
  },
  computed: {
    // title() {
    //   return (this.operName ? (this.operName +'专属') : '') + '观众参展链接'
    // },
    linkTail() {
      const operatorId = getCookie('operatorId') || ''
      return `?EID=${this.projectData.exhibitCode}&target=${this.targetType}&orgnum=${this.projectData.f_org_num || ''}&pid=${this.projectData.projectId}&version=${this.version}&cid=${operatorId}&ctid=1`
    },
    link(){
      return {
        pc: this.linkPre + '/web-reg-server/pc/exhibit-apply.html' + this.linkTail,
        mobile: this.linkPre + '/web-reg-server/mobile/exhibit-apply-m.html' + this.linkTail
      };
    },
    qrcodeLogoURL() {
      return this.qrcodeLogo
    }
  },
  props: ['projectData'],
  methods:{
    close(){
      this.show = false
    },
    open(targetType){
      this.show = true
      this.targetType = targetType || 4;
      this.$nextTick(() => {
        this.getQRCodeLogo().then(() => {
          this.$nextTick(() => {
            $('.project-apply-show .qr_code').html("").qrcode({
              src: this.qrcodeLogoURL,
              text: this.link.mobile,
              correctLevel: 0,
              width: '1000', //二维码的宽度
              height: '1000', //二维码的高度
              imgWidth : 200,			 //图片宽
              imgHeight : 200,			 //图片高
            })
          })
        })
      })
    },
    copyLink(type){
      if(type){
        document.getElementById('apply-show-'+type).select();
        document.execCommand("Copy");
      }
    },
    openLink(type){
      type && window.open(this.link[type])
    },
    downPic(){
      var type = 'png';
      var c = $('.project-apply-show .qr_code').find('canvas')[0];
      var imgdata = c.toDataURL("image/png");
      var fixtype = function(type) {
        type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
        var r = type.match(/png|jpeg|bmp|gif/)[0];
        return 'image/' + r;
      };
      imgdata = imgdata.replace(fixtype(type), 'image/octet-stream');
      var savaFile = function(data, filename) {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
      };
      savaFile(imgdata, `参展申请${+this.version === 2 ? '英文版' : '中文版'}二维码.png`);
    },
    //兼容之前已经上传的图片
    queryData2image(src) {
      const inner = src => {
        if (!src) return '/RegSever/RegPage/images/top-lucency.png'
        const prefix = location.origin
        if (/(http|https)/.test(src)) return src
        else if (/(^\\+|^\/)/.test(src)) return prefix + src
        return prefix + '/image/' + src
      }
      const url = inner(src)
      if (!url) return ''
      try {
        return new URL(url).host.indexOf('aliyuncs.com') !== -1
          ? `/eip-web-sponsor/upload/ossOut?ossUrl=${encodeURIComponent(url)}`
          : url
      } catch (e) {
        return url
      }
    },
    async getQRCodeLogo() {
      //兼容之前已经上传的图片
      function queryData2image(src) {
        const inner = src => {
          if (!src) return '/RegSever/RegPage/images/top-lucency.png'
          const prefix = location.origin
          src = src.replace(/\\/g, '/')
          if (/^(https?:)?\/\//.test(src)) return src
          if (src.startsWith('/')) return prefix + src
          return prefix + '/image/' + src
        }
        const url = inner(src)
        if (!url) return ''
        try {
          return new URL(url).host.indexOf('aliyuncs.com') !== -1
            ? `/eip-web-sponsor/upload/ossOut?ossUrl=${encodeURIComponent(url)}`
            : url
        } catch (e) {
          return url
        }
      }

      function getLogoImgs(projectId, version) {
        let QRCodeLogo = ''
        $.ajax({
          url: '/eip-web-sponsor/api/project/getProjectLogo',
          data: {projectId},
          type: "post",
          async: false,
          success(data) {
            if (data.state !== 1) return
            const buyerLogoKey = version === 2 ? 'tradeEnLogo' : 'tradeCnLogo'
            const result = data.data || {}
            QRCodeLogo = result[buyerLogoKey] || result['exhibitionLogo']
          },
          error(err) {
            console.warn(err)
          }
        });
        return queryData2image(QRCodeLogo)
      }

      this.qrcodeLogo = getLogoImgs(this.projectData.projectId, +this.version)
    }
  },
  watch: {
    'link.mobile'(newName) {
      if(newName){
        $('.project-apply-show .qr_code').html("").qrcode({
          text: newName,
          src: this.qrcodeLogoURL,
          correctLevel: 0,
          width: '1000', //二维码的宽度
          height: '1000', //二维码的高度
          imgWidth : 200,			 //图片宽
          imgHeight : 200,			 //图片高
        });
      }
    }
  },
  components:{
    customDialog
  }
}