<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="85px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
            class="project"
            ref="project"
            v-model="projectIds"
            placeholder="选择项目名称"
            multiple
            collapse-tags
            popper-class="disallow-popper"
            clearable
            @hook:mounted="projectMultipleMounted"
            @change="selectChange"
            @clear="projectList = []"
            @click.native.stop="openProjectSelect"
          >
            <el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="举办年份" prop="yearMonth">
          <el-date-picker
            v-model="searchForm.yearMonth"
            format="yyyy"
            type="year"
            value-format="yyyy"
            placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="举办地点" prop="city">
          <el-input v-model="searchForm.city" placeholder="地点" v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="是否缴清" prop="balanceState">
          <el-select
            v-model="searchForm.balanceState"
            placeholder="是否缴清"
            clearable
            @clear="clear('balanceState')"
          >
            <el-option label="已缴清" :value="1"></el-option>
            <el-option label="未缴清" :value="2"></el-option>
            <el-option label="未缴" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-bottom: 0;" prop="exhibitDate" label="开展时间">
          <el-date-picker
            v-model="searchForm.startTime"
            type="date"
            placeholder="开始时间"
            value-format="yyyy-MM-dd 00:00:00"
            clearable
            @clear="clear('startTime')">
          </el-date-picker>
          <el-date-picker
            v-model="searchForm.endTime"
            type="date"
            placeholder="结束时间"
            value-format="yyyy-MM-dd 23:59:59"
            clearable
            @clear="clear('endTime')">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-top: 5px;" prop="orderSignDateStart">
          <template #label>
            采购时段
            <el-tooltip placement="top">
              <i class="el-icon-info" style="color: #1890ff;"></i>
              <template #content>
                <div style="padding: 10px;font-size: 14px;line-height: 20px;">
                  采购统计时段 费用算法对应来源<br>
                  订单金额 ― 签订时间为该时段内的采购订单<br>
                  应付金额 ― 记账时间为该时段内的应付款<br>
                  采购订单数 ― 签订时间为该时段内的采购订单数量
                </div>
              </template>
            </el-tooltip>
          </template>
          <el-date-picker
            v-model="searchForm.orderSignDateStart"
            type="date"
            placeholder="开始时间"
            value-format="yyyy-MM-dd 00:00:00"
            clearable
            @clear="clear('orderSignDateStart')">
          </el-date-picker>
          <el-date-picker
            v-model="searchForm.orderSignDateEnd"
            type="date"
            placeholder="结束时间"
            value-format="yyyy-MM-dd 23:59:59"
            clearable
            @clear="clear('orderSignDateEnd')">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-top: 5px;" prop="paymentBookDateStart">
          <template #label>
            付款时段
            <el-tooltip placement="top">
              <i class="el-icon-info" style="color: #1890ff;"></i>
              <template #content>
                <div style="padding: 10px;font-size: 14px;line-height: 20px;">
                  付款时段 费用算法对应来源<br>
                  实付金额 ― 记账时间为该时段内的付款单<br>
                  尾款 ― 该时段最后时点时剩余未付尾款<br>
                  付款单数 ― 记账时间为该时段内的付款单数量
                </div>
              </template>
            </el-tooltip>
          </template>
          <el-date-picker
            v-model="searchForm.paymentBookDateStart"
            type="date"
            placeholder="开始时间"
            value-format="yyyy-MM-dd 00:00:00"
            clearable
            @clear="clear('paymentBookDateStart')">
          </el-date-picker>
          <el-date-picker
            v-model="searchForm.paymentBookDateEnd"
            type="date"
            placeholder="结束时间"
            value-format="yyyy-MM-dd 23:59:59"
            clearable
            @clear="clear('paymentBookDateEnd')">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-top: 5px;" prop="showProductKind">
          <el-checkbox v-model="searchForm.showProductKind">是否显示产品大类</el-checkbox>
        </el-form-item>
      </el-form>
      <div class="right-btn-box">
        <el-button size="mini" type="primary" plain @click="search" style="margin-right: 12px">检索</el-button>
        <el-button v-if="getAuthority('fu_report_export')" size="mini" class="btn" icon="el-icon-upload2"
                   @click="$emit('export')">导出
        </el-button>
      </div>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{ pro.projectName }}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false"
                    @finish="selectFinish"/>
  </el-main>
</template>

<script>
const ProjectSelect = importC('ProjectSelect')
export default {
  name: "PurchaseSummaryHeader",
  mixins: [Mixins.requestOperator(), Mixins.ProjectMultipleSelect()],
  data() {
    return {
      pickerOptions,
      searchForm: {
        projectIds: '',
        projectName: '',
        yearMonth: '',
        city: '',
        balanceState: '',
        startTime: '',
        endTime: '',
        orderSignDateStart: '',
        orderSignDateEnd: '',
        paymentBookDateStart: '',
        paymentBookDateEnd: '',
        showProductKind: '',
      },
      showProjectSelect: false,
      dataMap: {
        projectName: '项目名称',
        yearMonth: '举办年份',
        city: '举办地点',
        balanceState: '是否缴清',
        startTime: '开展时间起',
        endTime: '开展时间止',
        orderSignDateStart: '采购时段起',
        orderSignDateEnd: '采购时段止',
        paymentBookDateStart: '付款时段起',
        paymentBookDateEnd: '付款时段止',
        showProductKind: '是否显示产品大类',
      },
    }
  },
  mounted() {
    this.requestOperator()
    this.$set(this.searchForm, 'yearMonth', top.$GlobalDefine.getSystemTime(null).getFullYear().toString())
    this.search(true)
  },
  methods: {
    search(isFirst) {
      Object.keys(this.searchForm).forEach(key => this.searchForm[key] === null && this.clear(key))
      let balanceStateLabel, searchValues = {
          projectIds: this.projectIds.toString(),
          // projectName:this.searchForm.projectName,
          yearMonth: this.searchForm.yearMonth,
          city: this.searchForm.city,
          balanceState: this.searchForm.balanceState,
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime,
          orderSignDateStart: this.searchForm.orderSignDateStart,
          orderSignDateEnd: this.searchForm.orderSignDateEnd,
          paymentBookDateStart: this.searchForm.paymentBookDateStart,
          paymentBookDateEnd: this.searchForm.paymentBookDateEnd,
          showProductKind: this.searchForm.showProductKind,
        },
        breadcrumbValues = {
          projectName: this.searchForm.projectName,
          yearMonth: this.searchForm.yearMonth,
          city: this.searchForm.city,
          balanceState: balanceStateLabel,
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime,
          orderSignDateStart: this.searchForm.orderSignDateStart,
          orderSignDateEnd: this.searchForm.orderSignDateEnd,
          paymentBookDateStart: this.searchForm.paymentBookDateStart,
          paymentBookDateEnd: this.searchForm.paymentBookDateEnd,
          showProductKind: this.searchForm.showProductKind ? '是' : void 0,
        },
        result = {
          requestData: {},
          breadcrumbList: []
        }
      switch (searchValues.balanceState) {
        case 1:
          breadcrumbValues.balanceState = '已缴清'
          break
        case 2:
          breadcrumbValues.balanceState = '未缴清'
          break
        case 3:
          breadcrumbValues.balanceState = '未缴'
          break
        default:
          breadcrumbValues.balanceState = ''
      }
      let breadcrumbKeys = Object.keys(breadcrumbValues)
      Object.keys(searchValues).forEach((k, index) => {
        let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
        if (bk === 'projectName') {
          bv = this.projectIds.map(projectId => {
            const p = this.projectList.find(item => projectId === item.projectId)
            if (!p) return ''
            return p.projectName
          }).join('、')
        } else if (!val && bk === 'showProductKind') return
        if (val !== false && !val) return
        result.requestData[k] = val
        result.breadcrumbList.push(`${this.dataMap[bk]}: ${typeof bv === 'string' ? bv.replace(/\s\d{2}:\d{2}:\d{2}$/, '') : bv}`)
      })
      this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
    },
    clear(...key) {
      key.forEach(k => this.$set(this.searchForm, k, ''))
    },
    clearAll() {
      this.clear(...Object.keys(this.searchForm))
      this.projectList = []
      this.projectIds = []
      this.searchForm.projectIds = Object.create(null)
      this.searchForm.projectName = Object.create(null)
    },
  },
  components: {
    ProjectSelect
  }
}
</script>

<style scoped>
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  min-width: 1290px;
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  max-width: 1400px;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.el-input {
  width: 140px;
}


.right-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-btn-box .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right-btn-box .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}
</style>