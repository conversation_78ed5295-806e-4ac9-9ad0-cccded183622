<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
              class="clear-search"
              @click.native="clearSearch"
              title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
              v-for="item in breadcrumbList"
              class="text-over-hidden"
              style="max-width: 240px"
              :title="item"
              :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button
          size="mini"
          v-if="AuthorityMapper('add')"
          class="btn"
          @click="$emit('add')">{{isPurchaseOrder ? '生成其他应付': '新增其他应收'}}</el-button>
        <el-dropdown size="mini" type="primary" placement="bottom-start" @command="$emit($event)">
          <el-button type="primary" plain size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="export">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>
    <alex-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 240px)"
        border
        size="small"
        class="alex-table"
        mode="multiple"
        :fields="fields"
        :operation-width="100"
        @sort-change="sort"
        @selection-change="$emit('table-select', $event)"
        :handle-pagination="handlePagination"
        @row-dblclick="readRow"
        :total="total"
        :loading="loading">
      <template slot="alex-operation" slot-scope="{row}">
        <div>
          <el-link
            :underline="false"
            v-if="row.businessTypeCode === Y_CODE || !AuthorityMapper('mod')"
            type="success"
            @click.stop="readRow(row)">查看</el-link>
          <el-link :underline="false" v-if="row.businessTypeCode === OTHER_CODE && AuthorityMapper('mod')"
                   type="primary" @click.stop="editRow(row)">修改</el-link>
          <el-link
            v-if="row.businessTypeCode === OTHER_CODE && AuthorityMapper('del')"
            :underline="false"
            @click.stop="deleteRow(row)">删除</el-link>
        </div>
      </template>
      <template slot="businessTypeCode" slot-scope="{row}">{{row.businessTypeCode | businessTypeCode2text}}</template>
      <template slot="amount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.amount | toFixed2}}</span>
      </template>
      <template slot="settlementState" slot-scope="{row}">
        <span>{{ row.settlementState ? '已结算' : '未结算' }}</span>
      </template>
      <template slot="unSettlementAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.unSettlementAmount | toFixed2 }}</span>
      </template>
      <template slot="settlementAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.settlementAmount | toFixed2 }}</span>
      </template>
    </alex-table>
    <el-footer height="30px" style="margin-top: -15px;" class="footer">
      <p style="font-size: 14px;">人民币总额：<span style="color: #409EFF">{{amountRmb | toFixed2}}元</span></p>
      <p v-if="amountForeign && amountForeign.length" style="font-size: 14px;">外币总额：
        <span v-for="it in amountForeign" :key="it.currencyCode" style="color: #409EFF">
          {{ (it.amount || 0) + (it.currencyName || '')}}
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        </span>
      </p>
    </el-footer>
  </el-container>
</template>

<script>
  const AlexTable = importC('AlexTable')
  export default {
    name: "ReceivablesMainBody",
    inject:{
      businessTypeCode: {
        from: 'businessTypeCode',
        default: 'YS_XSDD'
      },
      isPurchaseOrder: {
        from: 'isPurchaseOrder',
        default: false
      },
      apiPrefix: {
        from: 'apiPrefix',
        default: 'fnReceivables'
      },
      primaryKey: {
        from: 'primaryKey',
        default: 'receivablesId'
      }
    },
    data() {
      return {
        breadcrumbList: [],
        fields: [],
        total: 0,
        tableData: [],
        loading: false,
        requestHistory: {rows: 20, page: 1},
        leftData: [],
        rightData: [],
        amountForeign: [],
        amountRmb: 0,
        enableReceiptSettlement: false
      }
    },
    mounted() {
      this.getSysOrgSetting()
    },
    methods: {
      handlePagination({page, rows}) {
        this.requestHistory.page = page
        this.requestHistory.rows = rows
        this.requestTableData(this.requestHistory)
      },
      sort({column, prop, order}) {
        let data = this.requestHistory;
        if (order) {
          data.order = order.replace('ending', '');
          data.sort = prop;
        } else {
          delete (data.order)
          delete (data.sort)
        }
        this.requestTableData(data)
      },
      clearSearch() {
        this.breadcrumbList = [];
        this.requestTableData({page: 1, rows: 20});
        this.$emit('clear-search')
      },
      createFields(callback) {
        this.fields = [
          {label: this.isPurchaseOrder ? '应付款序号': '应收款序号' , prop: this.primaryKey},
          {label: '订单序号', prop: 'orderId'},
          {label: '记账日期', prop: 'bookDate'},
          {label: '项目名称', prop: 'projectName'},
          {label: '手工单号', prop: this.isPurchaseOrder ? 'payablesCode': 'receivablesCode'},
          {label: '公司名称', prop: 'cltName'},
          !this.isPurchaseOrder ? {label: '产品名称', prop: 'prodName'}: null,
          {label: '业务类型', prop: 'businessTypeCode'},
          {label: this.isPurchaseOrder ? '应付金额': '应收金额', prop: 'amount'},
          this.IS_ENABLE_FOREIGN_CURRENCY ? {label: '币种', prop: 'currencyName'}: null,
          {label: '备注', prop: 'memo'},
        ].filter(Boolean)
        if (!this.isPurchaseOrder && this.enableReceiptSettlement) {
          this.fields.push(...[
            {label: '结算状态', prop: 'settlementState'},
            {label: '已结算金额', prop: 'settlementAmount'},
            {label: '未结算金额', prop: 'unSettlementAmount'},
          ])
        }
        callback && callback.call(this)
      },
      requestTableData(queryData={}, callback = ()=>this.loading = false) {
        this.loading = true
        this.requestHistory = queryData
        if(!this.requestHistory.workTeamId)
          this.requestHistory.workTeamId = teamId
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        Axios.post(`/${this.apiPrefix}/getPage`, JSON2FormData(queryData)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败')
          this.total = res.total
          this.tableData = res.rows
          this.amountRmb = res.data.amountRmb
          this.amountForeign = res.data.amountForeign || []
          callback(this.$refs.table, res.rows)
        }).catch(e => {
          this.$errorMsg('数据加载失败')
          this.loading = false
        })
      },
      editRow(row) {
        this.$emit('edit-row', row[this.primaryKey])
      },
      readRow(row){
        this.$emit('read-row', row[this.primaryKey])
      },
      async deleteRow(row) {
        try {
          await this.$confirm('确定要删除这1条数据吗？ 此操作不可逆！', '提醒', {type: 'warning'})
          const postData = JSON2FormData({
            [this.primaryKey]: row[this.primaryKey],
            operEntrance: (this.isPurchaseOrder ? '应付款': '应收单') + '删除'
          })
          const {data: {state}} = await Axios.post(`/${this.apiPrefix}/deleteOther`, postData)
          let message = ''
          switch (state) {
            case 0:
              message = '删除失败！'
              break
            case 1:
              this.$message.success('删除成功！')
              this.requestTableData(this.requestHistory)
              break
            case 2:
              message = `'只能删除业务类型为：“${this.isPurchaseOrder ? '其他应付': '其他应收'}” 的数据！'`
              break
            case 3:
              message = `因项目封存而无法删除`
              break
          }
          message && this.$errorMsg(message)
        }catch (e) {
          if(!e || e === 'cancel' || e === 'close') return
          this.$errorMsg('删除失败！')
          console.warn(e)
        }
      },
      search(data, isFirst) {
        let postData = data.requestData
        let breadcrumbList = data.breadcrumbList
        postData.page = 1
        postData.rows = 20
        //breadcrumbList 面包屑导航
        this.breadcrumbList = breadcrumbList
        if(isFirst){
          this.requestHistory = postData
          return this.updateColumnDisplay()
        }
        this.requestTableData(postData)
      },
      updateColumnDisplay(){
        this.createFields($=>{
          this.requestTableData(this.requestHistory, table=>{
            table.computedWidth().then(()=>{
              this.loading=false
            })
          })
        })
      },
      async getSysOrgSetting() {
        try {
          const {data: {state, data}} = await Axios.post('/sysSettingOrg/queryReceiptSet')
          if (state !== 1) await Promise.reject('获取设置失败！')
          let tmp = (data || []).find(it => it.settingCode === 'enable_receipt_settlement') || {}
          this.enableReceiptSettlement = tmp.settingValue === 'true'
          this.enableReceiptSettlement && this.updateColumnDisplay()
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$message.error(e)
        }
      },
    },
    components: {AlexTable},
    filters:{
      bool2text(bool){
        return bool ? '是' : '否'
      },
      number2money(num){
        return '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      businessTypeCode2text(code){
        const businessTypeList = [
          {id: 'YF_CGDD', text: '采购订单应付'},
          {id: 'YS_XSDD', text: '销售订单应收'},
          {id: 'YF_OTHER', text: '其他应付'},
          {id: 'YS_OTHER', text: '其他应收'},
        ]
        const curType = businessTypeList.find(({id}) => id === code )
        return curType ? curType.text: ''
      }
    },
    computed:{
      AuthorityMapper(){
        const Authority = {
          add: ['fu_crm_receivables_add', 'fu_crm_payables_add'],
          mod: ['fu_crm_receivables_mod', 'fu_crm_payables_mod'],
          del: ['fu_crm_receivables_del', 'fu_crm_payables_del'],
        }
        return type => getAuthority((Authority[type] || [])[Number(this.isPurchaseOrder)])
      },
      OTHER_CODE(){
        return this.isPurchaseOrder ? 'YF_OTHER': 'YS_OTHER'
      },
      Y_CODE(){
        return this.isPurchaseOrder ? 'YF_CGDD': 'YS_XSDD'
      },
    }
  }
</script>

<style scoped>
  .main-body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3px;
  }
  .right {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right .el-button.btn {
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 0;
  }

  .right .el-button.btn:hover {
    background-color: rgb(99, 169, 251);
  }
  .alex-table {
    width: 100%;
    padding-top: 10px;
  }
  .footer{
    display: flex;
  }
  .footer p{
    margin-right: 50px;
  }
  .el-table--small td, .el-table--small th {
    padding: 4px 0;
  }
</style>