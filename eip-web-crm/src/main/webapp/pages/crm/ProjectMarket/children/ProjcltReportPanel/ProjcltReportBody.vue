<template>
  <el-container style="padding: 0px 20px;flex-direction: column;height: calc(100vh - 70px);">
    <div v-if="showPlaceholder" class="placeholder">
      <el-image src="../img/empty.svg"></el-image>
      <span class="message">{{loading ? '加载中...': '请选择项目和公司后进行统计'}}</span>
    </div>
    <template v-else>
      <el-header class="main-body-header" height="30px">
        <div class="left">
          <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item
              class="clear-search"
              @click.native="clearSearch"
              title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item
              v-for="item in breadcrumbList"
              class="text-over-hidden"
              style="max-width: 240px"
              :title="item"
              :key="String(Math.random())">{{ item }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="right">
          <el-button
            v-if="getAuthority('fu_report_export')"
            size="mini" class="btn" icon="el-icon-upload2"
            @click="$emit('export')">导出
          </el-button>
        </div>
      </el-header>
      <el-descriptions style="margin-top: 12px;" :column="4" size="small" border>
        <el-descriptions-item v-for="field in descFields" :label="field.label" :key="field.prop">
          {{ descData[field.prop] }}
        </el-descriptions-item>
      </el-descriptions>
      <alex-table
        ref="table"
        :data="tableData"
        show-summary
        :summary-method="getTableDataSum"
        height="100%"
        border
        size="small"
        class="alex-table"
        mode="none"
        :fields="fields"
        @header-dragend="onTableHeaderDragend"
        :show-pagination="false"
        :show-operation="false"
        :loading="loading">
        <template slot="productList" slot-scope="{row}">
          <alex-table
            :key="row.prodKindCode"
            ref="productTable"
            :data="row.productList"
            border
            size="small"
            class="alex-table alex-table--prod"
            mode="none"
            :fields="prodFields"
            :show-pagination="false"
            :show-operation="false"
            :loading="loading">
            <template slot="salesAmountList" slot-scope="{row}">
              <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.salesAmountList)}}</span>
            </template>
            <template slot="purchaseAmountList" slot-scope="{row}">
              <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.purchaseAmountList)}}</span>
            </template>
            <template slot="surplusAmountList" slot-scope="{row}">
              <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.surplusAmountList)}}</span>
            </template>
          </alex-table>
        </template>
        <template slot="salesAmountList" slot-scope="{row}">
          <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.salesAmountList)}}</span>
        </template>
        <template slot="purchaseAmountList" slot-scope="{row}">
          <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.purchaseAmountList)}}</span>
        </template>
        <template slot="surplusAmountList" slot-scope="{row}">
          <span style="display:block;text-align:right;white-space: pre-wrap;">{{formatAmountList(row.surplusAmountList)}}</span>
        </template>
      </alex-table>
    </template>
  </el-container>
</template>

<script>
const AlexTable = importC('AlexTable')
export default {
  name: "ProjcltReportBody",
  inject: ['formatAmountList'],
  data() {
    return {
      breadcrumbList: [],
      fields: [
        {type: 'expand', prop: 'productList', width: 40, fixedWidth: true},
        {label: '产品大类', prop: 'prodKindName', sortable: false, minWidth: 200},
        {label: '总采购金额', prop: 'purchaseAmountList', minWidth: 300, sortable: false},
        {label: '总销售金额', prop: 'salesAmountList', minWidth: 300, sortable: false},
        {label: '总盈余', prop: 'surplusAmountList', minWidth: 300, sortable: false},
      ],
      prodFields: [
        {label: '产品名称', prop: 'prodName', sortable: false, minWidth: 200},
        {label: '总采购金额', prop: 'purchaseAmountList', minWidth: 300, sortable: false},
        {label: '总销售金额', prop: 'salesAmountList', minWidth: 300, sortable: false},
        {label: '总盈余', prop: 'surplusAmountList', minWidth: 300, sortable: false},
      ],
      descFields: [
        {label: '项目名称', prop: 'projectName'},
        {label: '项目国家', prop: 'country'},
        {label: '公司客户', prop: 'companyName'},
        {label: '展位类型', prop: 'boothTypeName'},
        {label: '展位面积(平米)', prop: 'boothArea'},
        {label: '展馆展区', prop: 'sectionName'},
        {label: '展位号', prop: 'boothCode'},
        {label: '折算标摊数', prop: 'qtyConverted'},
        {label: '设计师', prop: 'designer'},
        {label: '供应商', prop: 'supplier'},
      ],
      tableData: [],
      tableDataAll: {},
      descData: {},
      loading: false,
      showPlaceholder: true
    }
  },
  methods: {
    clearSearch() {
      this.breadcrumbList = [];
      this.$emit('clear-search')
    },
    createFields(callback) {
      this.fields = []
      callback && callback.call(this)
    },
    number2money(num){
      return '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    async startStatistics(payload) {
      payload = payload || {}
      try {
        this.loading = true
        const {data} = await Axios.post('/statistics/projClientOrderProduct', JSON2FormData(payload))
        if (data.state !== 1) await Promise.reject(data.msg)
        const result = data.data
        if (!result) await Promise.reject('当前项目客户无数据，请重新选择')
        this.showPlaceholder = false
        this.tableData = result.productTypeList
        this.descFields.forEach(item => {
          this.$set(this.descData, item.prop, result[item.prop] || '')
        })
        this.fields.forEach(item => {
          const key = item.prop
          let value = result[item.prop]
          if (key === 'prodKindName') value = value || '合计'
          this.$set(this.tableDataAll, key, value || '')
          if (key === 'salesAmountList' && result.subtractAmount) {
            // 合并抹零金额的显示
            const reCalcValue = (value) => {
              if (!Array.isArray(value)) return ''
              return value.map(item => {
                let ret = item.amountStr
                if (item.currencyCode === 'RMB') ret += ` (含抹零金额${this.number2money(result.subtractAmount)})`
                return ret
              }).join('\n')
            }
            this.$set(this.tableDataAll, key + 'Str', reCalcValue(value))
          } else if (String(key).endsWith('List')) {
            this.$set(this.tableDataAll, key + 'Str', this.formatAmountList(value))
          }
        })
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
        this.$errorMsg(e || '数据加载失败')
      } finally {
        this.loading = false
      }
    },
    search(data) {
      this.breadcrumbList = data.breadcrumbList
      this.startStatistics(data.requestData)
    },
    getTableDataSum(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = this.tableDataAll[column.property + 'Str'] || this.tableDataAll[column.property];
      });
      return sums;
    },
    onTableHeaderDragend() {
      setTimeout(() => {
        this.$refs.table.doLayout2()
      }, 100)
    }
  },
  components: {AlexTable},
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.right {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}

.el-container .alex-table {
  width: 100%;
  padding: 0;
  margin-top: 20px;
  min-height: 0;
  flex: 1;
  margin-bottom: 40px;
}
.el-container .alex-table.alex-table--prod {
  margin: 0;
  padding: 0 0 0 40px;
}

.el-container .alex-table.alex-table--prod .el-table {
  border-width: 0;
  border-left-width: 1px;
  width: 100%;
}

.alex-table .el-table__header th.el-table__cell .cell {
  padding: 10px;
}

.alex-table .el-table .el-table__expand-column .el-table__expand-icon {
  background: none !important;
  transform: rotate(0) !important;
}

.alex-table .el-table .el-table__expand-column .el-table__expand-icon .el-icon::before {
  content: "\e6d9";
  border: 1px solid #c0c4cc;
}

.alex-table .el-table .el-table__expand-column .el-table__expand-icon.el-table__expand-icon--expanded .el-icon::before {
  content: "\e6d8";
}

.alex-table .el-table table[class^="el-table__"] tr > th[class^="el-table_"] .cell {
  text-align: left;
}

.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(2) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(3) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(4) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(5) .cell {
  text-align: right;
  white-space: pre-wrap;
}

.alex-table .el-table--small .el-table__cell.el-table__expanded-cell {
  padding: 0;
}

.placeholder {
  width: 100%;
  height: calc(100vh - 200px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder .el-image {
  width: 94px;
  height: 94px;
}

.placeholder .message {
  font-size: 32px;
  font-family: Microsoft YaHei, Microsoft YaHei-Bold sans-serif;
  color: #585046;
  margin-top: 25px;
}
</style>