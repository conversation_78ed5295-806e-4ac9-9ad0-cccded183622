<template>
  <custom-dialog
      :show="show"
      append2body
      @opened.once="opened"
      @before-close="close"
      top="3vh"
      title="组合查询"
      width="1000px"
      class="combined-query"
  >
    <el-main slot="body" class="cq-body">
      <el-tabs
          class="panel-box"
          v-model="activeName"
          tab-position="left"
          type="card"
      >
        <cq-header :tag-data="tagData" @tag-close="tagClose"></cq-header>
        <el-tab-pane label="参展信息" name="basicInfo" key="basicInfo">
          <el-form class="search-form" ref="basicInfo" :model="basicInfo" label-width="110px" size="mini">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="basicInfo.companyName" clearable placeholder="公司名称"></el-input>
            </el-form-item>
            <el-form-item label="联系人" prop="linkman">
              <el-input v-model="basicInfo.linkman" clearable placeholder="联系人"></el-input>
            </el-form-item>
            <el-form-item label="手机" prop="linkmanMobile">
              <el-input v-model="basicInfo.linkmanMobile" clearable placeholder="手机"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="linkmanEmail">
              <el-input v-model="basicInfo.linkmanEmail" clearable placeholder="邮箱"></el-input>
            </el-form-item>
            <el-form-item label="微信" prop="linkmanWechat">
              <el-input v-model="basicInfo.linkmanWechat" clearable placeholder="微信"></el-input>
            </el-form-item>
            <el-form-item label="QQ" prop="linkmanQQ">
              <el-input v-model="basicInfo.linkmanQQ" clearable placeholder="QQ"></el-input>
            </el-form-item>
            <el-form-item label="职务" prop="position">
              <el-input v-model="basicInfo.position" clearable placeholder="职务"></el-input>
            </el-form-item>
            <el-form-item label="性别" prop="sex">
              <el-select v-model="basicInfo.sex" clearable>
                <el-option label="男" value="男"></el-option>
                <el-option label="女" value="女"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公司网址" style="flex-basis: 100%" prop="linkmanWebsite">
              <el-input v-model="basicInfo.linkmanWebsite" clearable placeholder="公司网址"></el-input>
            </el-form-item>
            <el-form-item label="邮寄地址" style="flex-basis: 100%" prop="deliveryAddress">
              <el-input type="textarea"
                        resize="none"
                        :autosize="{minRows: 1, maxRows: 6}"
                        v-model="basicInfo.deliveryAddress"></el-input>
            </el-form-item>
            <el-form-item label="备注" style="flex-basis: 100%" prop="memo">
              <el-input type="textarea"
                        resize="none"
                        :autosize="{minRows: 2, maxRows: 6}"
                        v-model="basicInfo.memo"></el-input>
            </el-form-item>
            <el-form-item label="创建人" prop="inputterId">
              <el-select v-model="basicInfo.inputterId"
                         @clear="clear('basicInfo', 'inputterName', 'inputterId')"
                         @change="bothWayStore($event, operators, formProp('inputterName'), basicInfo)"
                         clearable placeholder="创建人">
                <el-option
                    v-for="item in operators"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间起" prop="inputTimeStart">
              <el-date-picker
                  placeholder="创建时间起"
                  type="date"
                  value-format="yyyy-MM-dd 00:00:00"
                  clearable
                  v-model="basicInfo.inputTimeStart">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="创建时间止" prop="inputTimeEnd">
              <el-date-picker
                  placeholder="创建时间止"
                  type="date"
                  value-format="yyyy-MM-dd 23:59:59"
                  clearable
                  v-model="basicInfo.inputTimeEnd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="业绩归属人" prop="ownerId">
              <el-select v-model="basicInfo.ownerId"
                         @clear="clear('basicInfo', 'ownerName', 'ownerId')"
                         @change="bothWayStore($event, operators, formProp('ownerName'), basicInfo)"
                         filterable
                         default-first-option
                         clearable
                         placeholder="业绩归属人">
                <el-option
                    v-for="operator in operators"
                    :key="operator.id"
                    :label="operator.text"
                    :value="operator.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="需求检索" name="boothInfo" key="boothInfo" v-if="isEnableBoothDemand">
          <el-form class="search-form" ref="boothInfo" :model="boothInfo" label-width="110px" size="mini">
            <el-form-item label="存在展位需求" prop="existBoothDemandFlag">
              <el-select v-model="boothInfo.existBoothDemandFlag"
                         filterable
                         default-first-option
                         clearable
                         placeholder="存在展位需求">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位需求状态" prop="boothDemandConfirm">
              <el-select v-model="boothInfo.boothDemandConfirm"
                         filterable
                         default-first-option
                         clearable
                         placeholder="展位需求状态">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="确认人" prop="confirmId">
              <el-select v-model="boothInfo.confirmId"
                         filterable
                         default-first-option
                         clearable
                         @clear="clear('boothInfo', 'confirmName', 'confirmId')"
                         @change="bothWayStore($event, operators, formProp('confirmName'), boothInfo)"
                         placeholder="确认人">
                <el-option
                    v-for="operator in operators"
                    :key="operator.id"
                    :label="operator.text"
                    :value="operator.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="确认时间起" prop="confirmTimeStart">
              <el-date-picker
                  placeholder="确认时间起,回车确认"
                  type="date"
                  value-format="yyyy-MM-dd 00:00:00"
                  clearable
                  v-model="boothInfo.confirmTimeStart">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="确认时间止" prop="confirmTimeEnd">
              <el-date-picker
                  placeholder="确认时间止,回车确认"
                  type="date"
                  value-format="yyyy-MM-dd 23:59:59"
                  clearable
                  v-model="boothInfo.confirmTimeEnd">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="展馆展区" prop="sectionCode">
              <el-select v-model="boothInfo.sectionCode"
                         filterable
                         @clear="clear('boothInfo', 'sectionName', 'sectionCode')"
                         @change="bothWayStore($event, exhibitSectionList, formProp('sectionName'), boothInfo, {id: 'code', text: 'text'})"
                         default-first-option
                         clearable
                         placeholder="展馆展区">
                <el-option
                    v-for="{code, text} in exhibitSectionList"
                    :label="text"
                    :key="code"
                    :value="code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位" prop="boothNum">
              <el-input v-model="boothInfo.boothNum" placeholder="展位" clearable></el-input>
            </el-form-item>
            <el-form-item label="展位类型" prop="boothType">
              <el-select v-model="boothInfo.boothType"
                         filterable
                         default-first-option
                         clearable
                         @clear="clear('boothInfo', 'boothTypeName', 'boothType')"
                         @change="bothWayStore($event, boothTypeList, formProp('boothTypeName'), boothInfo)"
                         placeholder="展位类型">
                <el-option
                    v-for="{id, text} in boothTypeList"
                    :label="text"
                    :key="id"
                    :value="id"></el-option>
              </el-select>
            </el-form-item><el-form-item></el-form-item>
            <el-form-item label="开口情况" prop="openInfoId">
              <el-select v-model="boothInfo.openInfoId"
                         filterable
                         default-first-option
                         @clear="clear('boothInfo', 'openInfoName', 'openInfoId')"
                         @change="bothWayStore($event, openInfoList, formProp('openInfoName'), boothInfo)"
                         clearable
                         placeholder="开口情况">
                <el-option
                    v-for="{id, text} in openInfoList"
                    :label="text"
                    :key="id"
                    :value="id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位面积"
                          class="boothArea">
              <el-input v-model="boothInfo.boothAreaStart" placeholder="单位(㎡)" clearable></el-input>
              <span style="padding: 0 6px;">至</span>
              <el-input v-model="boothInfo.boothAreaEnd" placeholder="单位(㎡)" clearable></el-input>
              <span style="padding: 0 6px;">㎡</span>
            </el-form-item>
            <el-form-item label="展品范围" prop="exhibitsRange" style="flex-basis: 100%;">
              <el-select
                  v-model="boothInfo.exhibitsRange"
                  multiple
                  clearable
                  filterable
                  @clear="clear('boothInfo', 'exhibitsRangeText', 'exhibitsRange')"
                  @change="exhibitsRangeChange"
                  default-first-option
                  placeholder="展品范围">
                <el-option v-for="item in productTypeList"
                           :label="item.text"
                           :value="item.id"
                           :key="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="会员检索" name="memberInfo" key="memberInfo">
          <el-form
            class="search-form"
            ref="memberInfo"
            :model="memberInfo"
            label-width="110px"
            size="mini">
            <el-form-item label="会员账号" prop="userName">
              <el-input v-model="memberInfo.userName" clearable placeholder="会员账号"></el-input>
            </el-form-item>
            <el-form-item label="会员等级" prop="memberGradeId">
              <el-select
                  v-model="memberInfo.memberGradeId"
                  clearable
                  filterable
                  default-first-option
                  @clear="clear('memberInfo', 'memberGradeName', 'memberGradeId')"
                  @change="bothWayStore($event, memberGrades, formProp('memberGradeName'), memberInfo)"
                  placeholder="会员等级">
                <el-option
                    v-for="item in memberGrades"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否外商" prop="isForeign">
              <el-select v-model="memberInfo.isForeign" clearable>
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态检索" name="preOrderInfo" key="preOrderInfo">
          <el-form
              class="search-form"
              ref="preOrderInfo"
              :model="preOrderInfo"
              label-width="110px"
              size="mini">
            <el-form-item label="存在展位订单" prop="existBoothPreOrderFlag">
              <el-select v-model="preOrderInfo.existBoothPreOrderFlag"
                         filterable
                         clearable
                         placeholder="存在展位订单">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="订单状态" prop="saleState" v-if="!isFullProcess">
              <el-select
                  v-model="preOrderInfo.saleState"
                  clearable
                  @clear="clear('preOrderInfo', 'saleStateName', 'saleState')"
                  @change="bothWayStore($event, saleStateList, formProp('saleStateName'), preOrderInfo)"
                  filterable
                  default-first-option
                  placeholder="订单状态">
                <el-option
                    v-for="item in saleStateList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位状态" prop="preorderAndContractState" v-else >
              <el-select
                  v-model="preOrderInfo.preorderAndContractState"
                  clearable
                  @clear="clear('preOrderInfo', 'preorderAndContractStateName', 'preorderAndContractState')"
                  @change="bothWayStore($event, preorderAndContractStateList, formProp('preorderAndContractStateName'), preOrderInfo)"
                  filterable
                  default-first-option
                  placeholder="展位状态">
                <el-option
                  v-for="item in preorderAndContractStateList"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="存在合同" prop="existContractFlag">
              <el-select v-model="preOrderInfo.existContractFlag"
                         filterable
                         clearable
                         placeholder="存在合同">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="存在收款单" prop="existFnReceiptFlag">
              <el-select v-model="preOrderInfo.existFnReceiptFlag"
                         filterable
                         clearable
                         placeholder="存在收款单">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="移交客服状态" prop="serveBoothSaleState">
              <el-select
                  v-model="preOrderInfo.serveBoothSaleState"
                  clearable
                  @clear="clear('preOrderInfo', 'serveBoothSaleStateName', 'serveBoothSaleState')"
                  @change="bothWayStore($event, serveBoothSaleStateList, formProp('serveBoothSaleStateName'), preOrderInfo)"
                  filterable
                  default-first-option
                  placeholder="移交客服状态">
                <el-option
                    v-for="item in serveBoothSaleStateList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="展位确认" prop="boothConfirmed">
              <el-select v-model="preOrderInfo.boothConfirmed"
                         filterable
                         clearable
                         placeholder="展位确认">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位" prop="boothNum">
              <el-input v-model="preOrderInfo.boothNum" placeholder="展位" clearable></el-input>
            </el-form-item>
            <el-form-item label="服务状态" prop="f_is_serve">
              <el-select v-model="preOrderInfo.f_is_serve"
                         filterable
                         clearable
                         placeholder="服务状态">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="进馆证" prop="task_cert">
              <el-select v-model="preOrderInfo.task_cert"
                         filterable
                         clearable
                         placeholder="进馆证">
                <el-option v-for="it in taskStates" :key="it.id" :label="it.text" :value="it.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="展位配置" prop="task_booth">
              <el-select v-model="preOrderInfo.task_booth"
                         filterable
                         clearable
                         placeholder="展位配置">
                <el-option v-for="it in taskStates" :key="it.id" :label="it.text" :value="it.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="会刊" prop="task_cata">
              <el-select v-model="preOrderInfo.task_cata"
                         filterable
                         clearable
                         placeholder="会刊">
                <el-option v-for="it in taskStates" :key="it.id" :label="it.text" :value="it.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="人员及签证" prop="task_person">
              <el-select v-model="preOrderInfo.task_person"
                         filterable
                         clearable
                         placeholder="人员及签证">
                <el-option v-for="it in taskStates" :key="it.id" :label="it.text" :value="it.id"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-main>
    <el-footer slot="footer" height="50px">
      <div>
        <el-button
            type="primary"
            size="mini"
            style="width: 60px"
            @click="formSubmit"
        >检索
        </el-button>
        <el-button
            plain
            size="mini"
            style="width: 60px"
            @click="formReset"
        >清空
        </el-button>
      </div>
    </el-footer>
  </custom-dialog>
</template>

<script>
  const CqHeader = importC('CqHeader')
  const CustomDialog = importC('CustomDialog')
  const ProjectSelect = importC('ProjectSelect')
  export default {
    name: "ExhibitorManageCombinedQuery",
    inject: [
      'mainProjectId',
      'isFullProcessComputed',
      'saleStateList',
      'preorderAndContractStateList',
      'serveBoothSaleStateList'
    ],
    data() {
      // only display
      return {
        show: false,
        activeName: 'basicInfo',
        showProjectSelect: false,
        // form
        onlyDisplayMapper: {
          // basicInfo
          inputterName: 'inputterId',
          ownerName: 'ownerId',
          // boothInfo
          confirmName: 'confirmId',
          sectionName: 'sectionCode',
          boothTypeName: 'boothType',
          openInfoName: 'openInfoId',
          exhibitsRangeText: 'exhibitsRange',
          // memberInfo
          memberGradeName: 'memberGradeId',
          // preOrderInfo
          saleStateName: 'saleState',
          preorderAndContractStateName: 'preorderAndContractState',
          serveBoothSaleStateName: 'serveBoothSaleState',
        },
        basicInfo: {
          //only display
          inputterName: '',
          ownerName: '',
          // search
          companyName: '',
          linkman: '',
          linkmanMobile: '',
          linkmanEmail: '',
          linkmanWechat: '',
          linkmanQQ: '',
          position: '',
          sex: '',
          linkmanWebsite: '',
          deliveryAddress: '',
          memo: '',
          inputterId: '',
          inputTimeStart: '',
          inputTimeEnd: '',
          ownerId: '',
        },
        boothInfo: { // 需求信息
          //only display
          confirmName: '',
          sectionName: '',
          boothTypeName: '',
          openInfoName: '',
          exhibitsRangeText: '',
          // search
          confirmId: '',
          confirmTimeStart: '',
          confirmTimeEnd: '',
          boothDemandConfirm:'',//参展需求确认标记
          // search: boothNeed.
          existBoothDemandFlag: '', // 是否存在展位需求
          sectionCode: '',
          boothNum: '',
          boothType: '',
          openInfoId: '',
          boothAreaStart: '',
          boothAreaEnd: '',
          exhibitsRange: []
        },
        memberInfo: {
          // only display
          memberGradeName: '',
          // search
          memberGradeId: '',
          userName: '',
          isForeign: '',
        },
        preOrderInfo: { // 状态信息
          saleStateName: '', //  订单状态 : 简化流程
          preorderAndContractStateName: '', // //  订单状态 : 全流程
          serveBoothSaleStateName: '',
          // search preOrderInfo.
          saleState: '',
          preorderAndContractState: '',
          // search
          existBoothPreOrderFlag: '',
          existContractFlag: '',
          existFnReceiptFlag: '',
          serveBoothSaleState: '',
          boothConfirmed: '',//参展展位确认标记
          boothNum: '',
          f_is_serve: '',
          task_booth: '',
          task_cata: '',
          task_cert: '',
          task_person: '',
        },
        formKeys: ['basicInfo', 'boothInfo', 'preOrderInfo', 'memberInfo'],
        // header ang label
        dataMap: {},
        tagData: {},
        // select data
        memberGrades: [],
        productTypeList: [],
        openInfoList: [],
        exhibitSectionList: [],
        operators: [],
        boothTypeList: [
          {id: 1, text: '标摊'},
          {id: 2, text: '光地'},
        ],
        taskStates: [
          {id: 1, text: '启动'},
          {id: 2, text: '草稿'},
          {id: 3, text: '已提交'},
          {id: 4, text: '审核'},
          {id: 9, text: '驳回'},
        ],
        fieldSetting: [], // 参展记录 设置
      }
    },
    methods: {
      open() {
        this.getServerBoothSetting()
        this.show = true
      },
      close() {
        this.show = false
      },
      //获取tag字典和下拉框信息并且只执行一次
      async opened() {
        await this.getSelectList();
        this.dataMap = {}
        this.$nextTick(() => {
          this.formKeys.forEach(ref => {
            if(!this.$refs[ref] || !this.$refs[ref].$children) return;
            this.$refs[ref].$children.forEach(child => {
              this.dataMap[child.prop] = child.label
              Object.entries(this.onlyDisplayMapper).forEach(([name, prop]) => {
                if (child.prop === prop) this.dataMap[name] = child.label
              })
            })
          })
          this.dataMap['boothAreaStart'] = '展位面积起'
          this.dataMap['boothAreaEnd'] = '展位面积止'
          // this.getSelectList()
        })
      },
      async getServerBoothSetting() { // 字段设置
        const { data } = await Axios
        // .post('/exhibition/queryExhibitSetting', JSON2FormData({
        .post('/exhibition/selectExhibitSetting', JSON2FormData({
          teamId,
          // queryParam: 'enableBoothDemand',
          // taskKindCode: 'fo_serve_booth_field_set',
        }))
        .catch(_ => this.error())
        // if(data.state !==1) this.error('查询参展记录设置失败 !');
        this.fieldSetting = data || []
        // this.isEnableBoothDemand = !!(data && data.data && data.data[0].value == '1');
      },
      formSubmit() {
        const ids = Object.values(this.onlyDisplayMapper)
        const texts = Object.keys(this.onlyDisplayMapper)
        let requestData = {
          projectFlow: this.isFullProcess ? 'fullProcess' : 'simpleProcess',
        }, breadcrumbList = [], val, breadcrumb;
        this.formKeys.forEach(formKey => {
          const form = this[formKey]
          Object.keys(form).forEach(k => {
            val = form[k]
            if (val === '' || val === null) return
            breadcrumb = `${this.dataMap[k]}: ${(function(v, tss){
              if(k.startsWith('task_')) {
                v = (tss.find(tmp => +tmp.id == +v) || {}).text || ''
              } else if (v === true || v === 1) v = '是'
              else if (v === false || v === 0) v = '否'
              return v
            })(val,this.taskStates)}`
            // 只要不是那些Id就添加面包屑导航
            !ids.includes(k) && breadcrumbList.push(breadcrumb)
            // 只要不是那些只用于显示的就添加到检索中
            if(!texts.includes(k)){
              if(k === 'exhibitsRange' && Array.isArray(val)){
                val.forEach((v,i)=>this.$set(requestData, `boothNeed.bptList[${i}].productTypeId`, v))
              } else if(formKey === 'boothInfo' && Object.keys(form).slice(9, -1).includes(k)){
                // 第9个后为 二级
                this.$set(requestData, 'boothNeed.' + k, val)
              // }else if(formKey === 'preOrderInfo' && !(['serveBoothSaleState','preorderAndContractState'].includes(k))){
              }else if(formKey === 'preOrderInfo' && ['boothNum','saleState'].includes(k)){
                // 只有这2个是二级
                this.$set(requestData, 'boothPreorderInfo.' + k, val)
              }
              else this.$set(requestData, k, val)
            }
          })
        })
        this.$emit('combined-query', {requestData, breadcrumbList})
        this.close()
      },
      formReset() {
        let forms = this.formKeys
        forms.forEach(name => {
          this.clear(name, ...Object.keys(this[name]))
        })
      },
      clear(targetKey, ...key) {
        key.forEach(k => this.$set(this[targetKey], k, ''))
      },
      //tag生成器
      tagGenerator(data, name, ...exclude) {
        this.$set(this.tagData, name, '')
        let list = [], val
        Object.keys(data).forEach(k => {
          for (let ex of exclude) if (ex === k) return
          if (k in this.dataMap && (data[k] === false || data[k] === 0 || data[k])) {
            val = data[k]
            if (Array.isArray(val) && !val.length) return
            else if (k.startsWith('task_')) {
              val = (this.taskStates.find(tmp => +tmp.id == +data[k]) || {}).text || ''
            } else if (data[k] === true || data[k] === 1) val = '是'
            else if (data[k] === false || data[k] === 0) val = '否'
            list.push(`${this.dataMap[k]} "${val}"`)
          }
        })
        if (list.length < 1) return
        let tagText = `${this.$refs[name].$parent.label}: ${list.join('、')}`
        this.$set(this.tagData, name, tagText)
      },
      //清除单个表单
      tagClose(name) {
        this.clear(name, ...Object.keys(this[name]))
      },
      //name与Id的绑定 setIdKey的值需要与列表和表单保持一次
      bothWayStore(val, list, formProps, form, listProps) {
        val = val !== false && val !== 0 && !val ? '' : val
        if (!Array.isArray(list)) return console.warn('list type should be an Array, got', Object.prototype.toString.call(list).slice(8, -1))
        form = form || this.tForm || this
        listProps = listProps || {id: 'id', text: 'text'}
        if (!formProps || !formProps.text)
          return console.warn(`formProps: object {id?: string?, text: string}`)
        if (!listProps || !listProps.id || !listProps.text)
          return console.warn(`listProps: object {id: string, text: string}`)
        const item = list.find(item => item[listProps.id] === val)
        if (!item) return console.warn('The corresponding data is not found. Please check whether the `formProps` and `listProps` parameters are correct!')
        this.$set(form, formProps.text, item[listProps.text])
        return item
      },
      async getSelectList() {
        try {
          const projectId = this.mainProjectId
          const projectArg = JSON2FormData({projectId})
          const requests = [
            Axios.post('/memberGrade/getAll'),
            Axios.post('/productType/getByMap', projectArg),
            Axios.post('/itemCode/selectByItemKindCodeElse', JSON2FormData({itemKindCode: 'open_info'})),
            Axios.post('/exhibitSection/getByProjectId', projectArg),
            Axios.post('/operator/selectByMap', JSON2FormData({workTeamId: teamId})),
          ]
          const [
            {data: {data: memberGrades}},
            {data: {data: productTypeList}},
            {data: openInfoList},
            {data: {data: exhibitSectionList}},
            {data: operators},
          ] = await Promise.all(requests)
          this.memberGrades = memberGrades.map(({memberGradeId, memberGradeName}) => ({
            id: memberGradeId,
            text: memberGradeName
          }))
          this.productTypeList = productTypeList.map(({productTypeId, typeName}) => ({
            id: productTypeId,
            text: typeName
          }))
          this.openInfoList = openInfoList.map(({id, text}) => ({id: +id, text}))
          this.exhibitSectionList = exhibitSectionList
          this.operators = operators.map(({id, text}) => ({id: +id, text}))
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      formProp(key) {
        return {id: this.onlyDisplayMapper[key], text: key}
      },
      exhibitsRangeChange(val){
        if(!Array.isArray(val))return
        const temp = []
        val.forEach(_val=>{
          const current = this.productTypeList.find(({id})=>id===_val)
          if(current){
            temp.push(current.text)
          }
        })
        this.$set(this.boothInfo, 'exhibitsRangeText', temp.join())
      }
    },
    watch: {
      basicInfo: {
        handler: function (newVal) {
          this.tagGenerator(newVal, 'basicInfo', ...Object.values(this.onlyDisplayMapper).slice(0, 2))
        },
        deep: true //深度监听
      },
      boothInfo: {
        handler: function (newVal) {
          this.tagGenerator(newVal, 'boothInfo', ...Object.values(this.onlyDisplayMapper).slice(2, 7))
        },
        deep: true //深度监听
      },
      memberInfo: {
        handler: function (newVal) {
          this.tagGenerator(newVal, 'memberInfo', ...Object.values(this.onlyDisplayMapper).slice(7,8))
        },
        deep: true //深度监听
      },
      preOrderInfo: {
        handler: function (newVal) {
          this.tagGenerator(newVal, 'preOrderInfo', ...Object.values(this.onlyDisplayMapper).slice(8))
        },
        deep: true //深度监听
      },
    },
    computed: {
      isEnableBoothDemand() {
        const tmp = this.fieldSetting.find(it=> it.taskKindCode == 'fo_serve_booth_field_set' && it.param == 'enableBoothDemand') || {};
        return tmp.value === '1';
        // return this.fieldSettingEx.enableBoothDemand ? this.fieldSettingEx.enableBoothDemand.show : false;
      },
      isFullProcess() {
        return this.isFullProcessComputed()
      }
    },
    components: {
      CqHeader,
      CustomDialog,
    },
  }
</script>

<style scoped>
  .cq-body {
    padding: 0;
    height: 600px;
  }

  .el-tabs__header.is-left, .el-tabs__header.is-left * {
    border: none !important;
  }

  .el-tabs--left .el-tabs__header.is-left {
    width: 195px;
    height: 600px;
    background-color: #F5F7FA;
  }

  .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
    padding-left: 0;
    text-align: center;
    line-height: 40px;
    user-select: none;
    transition: all .3s;
  }

  .el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
  .el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
    background-color: #fff;
    color: #2e82e4;
  }

  .combined-query .el-footer,
  .combined-query .el-dialog__footer {
    padding: 0;
  }

  .combined-query .el-dialog__footer {
    display: flex;
    justify-content: flex-end;
    background-color: #f0f0f0;
  }

  .combined-query .el-footer {
    width: calc(100% - 180px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .combined-query .search-form {
    display: flex;
    justify-content: space-between;
    height: 524px;
    flex-wrap: wrap;
    overflow: auto;
    box-sizing: border-box;
    padding: 20px;
    align-items: flex-start;
    align-content: flex-start;
  }

  .combined-query .search-form .el-input, .el-select {
    width: 100%;
  }

  .combined-query .search-form .el-form-item.el-form-item--mini {
    flex: 0 0 49%;
  }

  .combined-query .boothArea .el-form-item__content {
    display: flex;
  }

  .combined-query .placeholder {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #e7afaf;
    font-size: 12px;
    font-weight: bold;
  }
</style>