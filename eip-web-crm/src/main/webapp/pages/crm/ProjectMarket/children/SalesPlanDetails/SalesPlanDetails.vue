<template>
  <div class="sp-detail" :style="{'--table-min-height': tableMinHeight + 'px'}">
    <div class="sp-detail__header">
      <div class="spdh__basic">
        <div class="left">
          <h2>{{planEntity.planName}}</h2>
          <el-link
              v-if="allowEdit"
              icon="el-icon-edit"
              :underline="false"
              type="primary"
              @click="editPlan">编辑
          </el-link>
          <span class="grey-text">计划状态：{{finishedState}}</span>
        </div>
        <div class="right">
          <el-button size="small" @click="reload(true)" icon="el-icon-refresh">刷新</el-button>
          <el-button type="primary" size="small" @click="allocatePlan()" v-if="fu_crm_sales_plan_add">生成子计划</el-button>
        </div>
      </div>
      <p class="spdh__info">
        <span class="grey-text">上级计划: {{planEntity.parentName || '无'}}</span>
        <span class="grey-text">创建时间：{{planEntity.createTime}}</span>
        <span class="grey-text">创建人：{{planEntity.createPersonName}}</span>
      </p>
      <div class="spdh__progress">
        <div class="progress">
          <div class="progress__icon">
            <img src="../img/plan.svg" alt="plan" style="vertical-align: middle" draggable="false">
          </div>
          <div class="progress__info">
            <div class="progress__text">
              <span class="grey-text" style="font-size: 15px;flex-basis: 65px;flex-shrink: 0;">计划数量</span>
              <span style="flex-grow: 1;"
                    class="fz18-c333-fwb">{{planEntity.finishedQuantity}}/{{planEntity.quantity || 0}}</span>
              <span style="flex-basis: 43px;" class="fz18-c333-fwb">{{quantityPercentage(planEntity)}}%</span>
            </div>
            <el-progress :stroke-width="8" :percentage="quantityPercentage(planEntity)" :format="()=>''"></el-progress>
          </div>
        </div>
        <div class="progress">
          <div class="progress__icon">
            <img src="../img/plan.svg" alt="plan" style="vertical-align: middle" draggable="false">
          </div>
          <div class="progress__info">
            <div>
              <span class="grey-text" style="font-size: 15px;flex-basis: 65px;flex-shrink: 0;">计划金额</span>
              <span style="flex-grow: 1;"
                    class="fz18-c333-fwb">{{planEntity.finishedAmount | number2money}}/{{planEntity.amount | number2money}}</span>
              <span style="flex-basis: 43px;" class="fz18-c333-fwb">{{amountPercentage(planEntity)}}%</span>
            </div>
            <el-progress :stroke-width="8" :percentage="amountPercentage(planEntity)" :format="()=>''"></el-progress>
          </div>
        </div>
        <div class="timer">
          <div class="timer__icon">
            <img src="../img/timer.svg" alt="plan" style="vertical-align: middle" draggable="false">
          </div>
          <div class="timer__info">
            <span class="fz18-c333-fwb"
                  style="display:block;padding: 0;line-height: 20px;">{{planEntity.startDate}}</span>
            <span class="grey-text" style="font-size: 15px;">开始时间</span>
          </div>
        </div>
        <div class="timer">
          <div class="timer__icon">
            <img src="../img/timer.svg" alt="plan" style="vertical-align: middle" draggable="false">
          </div>
          <div class="timer__info">
            <span class="fz18-c333-fwb"
                  style="display:block;padding: 0;line-height: 20px;">{{planEntity.endDate}}</span>
            <span class="grey-text" style="font-size: 15px;">结束时间</span>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="spdh__entity">
        <span class="spdhe__item">
          <span class="grey-text">关联项目</span>
          <span class="grey-text__59" :title="planEntity.projectName">{{planEntity.projectName}}</span>
        </span>
        <span class="spdhe__item">
          <span class="grey-text">计划金额来源</span>
          <span class="grey-text__59">{{amountSource}}</span>
        </span>
        <span class="spdhe__item">
          <span class="grey-text">计划类型</span>
          <span class="grey-text__59">主计划</span>
        </span>
        <span class="spdhe__item">
          <span class="grey-text">归属团队</span>
          <span class="grey-text__59">{{planEntity.workTeamName}}</span>
        </span>
        <span class="spdhe__item">
          <span class="grey-text">归属部门</span>
          <span class="grey-text__59">{{planEntity.branchName}}</span>
        </span>
        <span class="spdhe__item">
          <span class="grey-text">归属人员</span>
          <span class="grey-text__59">{{planEntity.ownerName}}</span>
        </span>
      </div>
    </div>
    <div class="sp-detail__table">
      <el-tabs v-model="currentKindId" @tab-click="tabChange">
        <el-tab-pane v-for="item in finalPlanKindList" :label="item.text" :key="item.id"
                     :name="String(item.id)"></el-tab-pane>
      </el-tabs>
      <template v-if="showSubPlanTable">
        <div class="buttons-small">
          <template v-if="fu_crm_sales_plan_add">
            <el-button size="small" @click="deletePlan">删除计划</el-button>
            <el-button size="small" @click="editPlanQA">调整计划数</el-button>
          </template>
          <div v-else style="height: 10px;/*placeholder*/"></div>
        </div>
        <alex-table
          border
          ref="table"
          style="margin: 0"
          class="alex-table-ant sp-detail__table"
          v-bind="tableHeight"
          size="small"
          mode="multiple"
          row-key="planId"
          :data="tableData"
          :fields="fields"
          :loading="loading"
          :index="false"
          :show-operation="false"
          :show-pagination="false"
          :cell-class-name="cellClassNameFn"
          :header-cell-class-name="cellClassNameFn"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column
            label=""
            type="index"
            width="47"
            slot="selectBefore">
            <template slot-scope="{row}">
              <el-tooltip effect="dark" :open-delay="100" :content="planStatusText(row['finishedState'])" placement="top">
                <i v-bind="planStatus(row['finishedState'])"></i>
              </el-tooltip>
            </template>
          </el-table-column>
          <template slot="planName" slot-scope="{row}">
            <el-link
              :underline="false"
              type="primary"
              v-if="row.planId"
              @click="openDetails(row.planId, row.planName)">{{row.planName}}
            </el-link>
            <span v-else>{{row.planName}}</span>
          </template>
          <template slot-scope="{row}" slot="quantityPercentage">
            <span v-if="row.planId">{{quantityPercentage(row)}}%</span>
            <span v-else></span>
          </template>
          <template slot-scope="{row}" slot="amountPercentage">
            <span v-if="row.planId">{{amountPercentage(row)}}%</span>
            <span v-else></span>
          </template>
          <template slot="quantity" slot-scope="{row}">
            <span v-if="row.planId">{{row.finishedQuantity}}/{{row.quantity}}</span>
            <span v-else>{{row.quantity}}</span>
          </template>
          <template slot="amount" slot-scope="{row}">
            <span v-if="row.planId">{{row.finishedAmount}}/{{row.amount | number2money}}</span>
            <span v-else>{{row.amount | number2money}}</span>
          </template>
          <template slot="startDate" slot-scope="{row}">{{timerFormat(row)}}</template>
        </alex-table>
      </template>
      <template v-else-if="showSubPlanPlaceholder">
        <div class="sp-detail__table empty">
          <div class="placeholder">
            <img src="../img/plan-empty.png" alt="plan-empty">
            <span class="grey-text" style="margin-bottom: 12px;">暂无子计划</span>
            <el-button type="primary" size="small" @click="allocatePlan()" v-if="fu_crm_sales_plan_add">点击添加</el-button>
          </div>
        </div>
      </template>
      <template v-else-if="showPlanCompare">
        <template v-if="!selectedComparePlan">
          <div class="sp-detail__table empty">
            <div class="placeholder">
              <img src="../img/empty.svg" alt="plan-empty">
              <span class="grey-text" style="margin: 12px;">请选择销售计划进行比对</span>
              <el-button type="primary" size="small" @click="pickComparePlan()">点击添加</el-button>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="buttons-small" style="justify-content:flex-start;">
            <el-button icon="el-icon-zoom-in" type="primary" plain size="small" @click="pickComparePlan">选择比对计划</el-button>
            <el-form inline size="small" :model="compareForm" label-width="170px" class="compare-form">
              <el-form-item label="当前计划统计截止时间" prop="endDate">
                <el-date-picker
                  :picker-options="getPlanCutoffDatePickerOption(planEntity)"
                  v-model="compareForm.endDate"
                  type="date"
                  placeholder="统计截止时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
              </el-form-item>
              <el-form-item label="比对计划统计截止时间" prop="comparePlanEndDate">
                <el-date-picker
                  :picker-options="getPlanCutoffDatePickerOption(selectedComparePlan)"
                  v-model="compareForm.comparePlanEndDate"
                  type="date"
                  placeholder="统计截止时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
              </el-form-item>
            </el-form>
            <el-button type="primary" size="small" @click="comparePlan">比对</el-button>
            <el-button size="small" @click="exportComparePlan">导出</el-button>
          </div>
          <alex-table
            border
            style="margin: 0"
            class="alex-table-ant sp-detail__table"
            mode="none"
            row-key="planId"
            ref="compareTable"
            :data="compareTableData"
            :fields="compareFields"
            :loading="loading"
            :index="false"
            :show-operation="false"
            :show-pagination="false"
          >
            <template slot-scope="{row}" slot="quantityPercentage">
              <span v-if="row.planId">{{quantityPercentage(row)}}%</span>
              <span v-else></span>
            </template>
            <template slot-scope="{row}" slot="amountPercentage">
              <span v-if="row.planId">{{amountPercentage(row)}}%</span>
              <span v-else></span>
            </template>
            <template slot="quantity" slot-scope="{row}">
              <span v-if="row.planId">{{row.finishedQuantity}}/{{row.quantity}}</span>
              <span v-else>{{row.quantity}}</span>
            </template>
            <template slot="amount" slot-scope="{row}">
              <span v-if="row.planId">{{row.finishedAmount  | number2money }}/{{row.amount | number2money}}</span>
              <span v-else>{{row.amount | number2money}}</span>
            </template>
            <template slot="startDate" slot-scope="{row}">{{timerFormat(row)}}</template>
          </alex-table>
          <div style="width: 100%;height: 420px;margin: 20px 0;position: relative;">
            <div ref="chart"></div>
            <el-radio-group
              v-model="compareChartIsAmount"
              @change="renderChart"
              size="small"
              style="position: absolute;right: 5%;top: 0;" >
              <el-radio-button :label="false">计划数量柱状图</el-radio-button>
              <el-radio-button :label="true">计划金额柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </template>
      </template>
    </div>
    <sales-plan-editor
        ref="editor"
        @read-alg="openALG"
        @operation-complete="reload()"></sales-plan-editor>
    <sales-plan-alg ref="alg"></sales-plan-alg>
    <sales-plan-allocation ref="allocate" @operation-complete="reload()"></sales-plan-allocation>
    <sale-plan-selector ref="selector"></sale-plan-selector>
  </div>
</template>

<script>
  const AlexTable = importC('AlexTable')
  const SalesPlanEditor = importC('SalesPlanEditor')
  const SalesPlanAlg = importC('SalesPlanAlg')
  const SalesPlanAllocation = importC('SalesPlanAllocation')
  const SalePlanSelector = importC('SalePlanSelector')

  const number2money = num => '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  export default {
    name: 'SalesPlanDetails',
    data() {
      return {
        planId: '',
        planEntity: {
          amount: 0,
          quantity: 0,
          finishedQuantity: 0,
          finishedAmount: 0,
          salesPlanEditorList: [],
        },
        planKindList: [{id: -1, text: 'loading...'}],
        currentKindId: '-1',
        tableHeight: {},
        tableMinHeight: 100,
        tableData: [],
        loading: false,
        fields: Object.freeze([
          {label: '名称', prop: 'planName', minWidth: 100, align: 'left', sortable: false},
          {label: '计划时间', prop: 'startDate', width: 205, align: 'left', sortable: false},
          {label: '计划数量', prop: 'quantity', maxWidth: 300, align: 'right', sortable: false},
          {label: '目标完成率', prop: 'quantityPercentage', width: 90, align: 'right', sortable: false},
          {label: '计划金额', prop: 'amount', minWidth: 150, maxWidth: 300, align: 'right', sortable: false},
          {label: '营业额完成率', prop: 'amountPercentage', width: 120, align: 'right', sortable: false},
          {label: '关联项目', prop: 'projectName', align: 'left', sortable: false},
          {label: '关联团队', prop: 'workTeamName', align: 'left', sortable: false},
          {label: '关联部门', prop: 'branchName', align: 'left', sortable: false},
          {label: '关联人员', prop: 'ownerName', align: 'left', sortable: false, width: 120},
        ]),
        selectedComparePlan: null,
        compareForm: {
          endDate: '',
          comparePlanEndDate: '',
        },
        compareFields: Object.freeze([
          {label: '计划类型', prop: 'planTypeName', align: 'left', sortable: false},
          {label: '名称', prop: 'planName', minWidth: 205, align: 'left', sortable: false},
          {label: '计划时间', prop: 'startDate', width: 205, align: 'left', sortable: false},
          {label: '统计截止时间', prop: 'cutoffDate', width: 120, align: 'left', sortable: false},
          {label: '计划数量', prop: 'quantity', maxWidth: 300, align: 'right', sortable: false},
          {label: '目标完成率', prop: 'quantityPercentage', width: 90, align: 'right', sortable: false},
          {label: '计划金额', prop: 'amount', minWidth: 150, maxWidth: 300, align: 'right', sortable: false},
          {label: '营业额完成率', prop: 'amountPercentage', width: 120, align: 'right', sortable: false},
          {label: '关联项目', prop: 'projectName', align: 'left', sortable: false},
          {label: '关联团队', prop: 'workTeamName', align: 'left', sortable: false},
          {label: '关联部门', prop: 'branchName', align: 'left', sortable: false},
          {label: '关联人员', prop: 'ownerName', align: 'left', sortable: false, width: 120},
        ]),
        compareTableData: [],
        compareChart: null,
        compareChartIsAmount: true,
      }
    },
    async created() {
      this.maxHeight = window.innerHeight - 195
      this.planId = Number(getQueryString('planId'))
      await this.reload()
      window.addEventListener('resize', () => {
        this.compareChart && this.compareChart.resize()
      })
    },
    methods: {
      async requestById(planId) {
        const {data: {state, data}} = await Axios.post('/salesPlan/getMoreById', JSON2FormData({planId}))
        if (state !== 1) await Promise.reject()
        this.planEntity = data
      },
      async requestPlanKindById(planId) {
        const scope = 2
        const {data: {state, data}} = await Axios.post('/salesPlan/getPlanKindById', JSON2FormData({planId, scope}))
        if (state !== 1) await Promise.reject()
        this.planKindList = data
        if (this.planKindList.length) {
          this.tabChange({name: this.planKindList[0]['id']})
        }
      },
      async requestChildByParentId(parentId, planKind) {
        this.loading = true
        try {
          const post = JSON2FormData({parentId, planKind})
          const {data: {state, data}} = await Axios.post('/salesPlan/selectChildByParentId', post)
          if (state !== 1) await Promise.reject()
          this.tableData = data
          const maxHeight = window.innerHeight - 195
          const height = window.innerHeight - 480
          this.tableHeight = data.length < 10 ? {height} : {maxHeight}
          const alexTable = this.$refs.table.$refs['alexTable']
          alexTable.$el.style.height = ''
          alexTable.$el.style.maxHeight = ''
          alexTable.doLayout()
          // http://deve.smtcrm.com:81/zentao/bug-view-14882.html
          // 自己计算body的最小高度
          this.$nextTick(() => {
            const header = alexTable.$el.querySelector('.el-table__header-wrapper')
            const footer = alexTable.$el.querySelector('.el-table__footer-wrapper')
            const headerHeight = header ? header.offsetHeight : 0
            const footerHeight = footer ? footer.offsetHeight : 0
            this.tableMinHeight = height - headerHeight - footerHeight
          })
        } catch (e) {
          this.$errorMsg('数据加载失败！')
          e && console.warn(e)
        } finally {
          this.loading = false
        }

      },
      async openDetails(planId, name) {
        try {
          const {data: {data: {state, isAllow}}} = await Axios.post('/salesPlan/isAllowOpen', JSON2FormData({
            planId,
            nowWorkTeamId: teamId
          }))
          if (!isAllow) await Promise.reject(state > 0 ? '权限不足！' : '数据不存在！')
          const target = `ProjectMarket/sales-contract.html?page=planDetail&planId=${planId}&teamId=${teamId}`
          FinalLinkOpen(target, null, true, false, name)
        } catch (e) {
          this.$errorMsg(e)
          console.warn(e)
        }
      },
      async deletePlan() {
        try {
          const selection = this.$refs.table.selection()
          if (selection.length === 0)
            return this.$errorMsg('没有可以删除的数据', '提醒', 'warning')
          await this.$confirm(`确认要删除这${selection.length}条计划及其子计划数据么？此操作不可逆，请谨慎操作。`, '提醒', {type: 'warning'})
          const post = {
            delChild: true,
            operEntrance: '详情页删除销售计划',
            list: selection.map(({planId, planName}) => ({planId, planName}))
          }
          const {data: {state}} = await Axios.post('/salesPlan/batchDel', post)
          if (state !== 1)
            await Promise.reject(('删除失败,,删除失败：不是授权编辑人员 '.split(','))[state])
          this.$message.success('删除成功！')
          await this.reload()
        } catch (e) {
          if (e === 'cancel') return
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg(e)
        }
      },
      async reload(force) {
        if (force) return location.reload()
        const loading = this.$loading()
        try {
          await this.requestById(this.planId)
          await this.requestPlanKindById(this.planId)
          await this.comparePlan()
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg('数据加载失败！')
        } finally {
          loading.close()
        }
      },
      tabChange({name}) {
        this.currentKindId = String(name)
        if (this.currentKindId <= 0) return
        this.requestChildByParentId(this.planId, this.currentKindId)
      },
      getSummaries({columns, data}) {
        let quantity = 0, finishedQuantity = 0
        let amount = 0, finishedAmount = 0
        // 这里不用 reduce
        data.forEach(item => {
          quantity += +item.quantity
          finishedQuantity += +item.finishedQuantity
          amount += +item.amount
          finishedAmount += +item.finishedAmount
        })
        const number2money = this.$options.filters.number2money
        return columns.map((col, index) => {
          switch (index) {
            case 2:
              return '子计划合计数'
            case 4:
              return `${finishedQuantity}/${quantity}`
            case 5:
              return `${this.quantityPercentage({quantity, finishedQuantity})}%`
            case 6:
              return `${number2money(finishedAmount.toFixed(2))}/${number2money(amount.toFixed(2))}`
            case 7:
              return `${this.amountPercentage({amount, finishedAmount})}%`
            default:
              return ''
          }
        })
      },
      editPlan() {
        this.$refs.editor &&
        this.$refs.editor.open('edit', this.planId)
      },
      openALG() {
        this.$refs.alg &&
        this.$refs.alg.open('read')
      },
      allocatePlan(mode, ext) {
        mode = mode || 'add'
        ext = ext || {}
        const planEntity = this.planEntity
        this.$refs.allocate &&
        this.$refs.allocate.open(this.planId, {mode, planEntity, ...ext})
      },
      editPlanQA() {
        const selection = this.$refs.table.selection()
        if (!selection.length)
          return this.$errorMsg('请先选择要调整的数据！')
        const tableData = selection.map(item => {
          const {
            planId,
            planName,
            planKind,
            startDate,
            endDate,
            amount,
            quantity,
            workTeamId,
            workTeamName,
            ownerId,
            ownerName
          } = item
          const result = {planId, planName, planKind, amount, quantity}
          switch (+this.currentKindId) {
            case 2:
            case 3:
              result.startDate = startDate
              result.endDate = endDate
              break
            case 4:
              result.workTeamId = workTeamId
              result.workTeamName = workTeamName
              break
            case 5:
              result.ownerId = ownerId
              result.ownerName = ownerName
              break
          }
          return result
        })
        this.allocatePlan('edit', {tableData})
      },
      cellClassNameFn({column: {type}}) {
        if (type === 'index')
          return 'no-border-color'
        return ''
      },
      async pickComparePlan() {
        const result = await this.$refs.selector.open()
        if (!result) return
        this.selectedComparePlan = result
        this.$set(this.compareForm, 'endDate', this.getPlanStatisticsCutoffDate(this.planEntity))
        this.$set(this.compareForm, 'comparePlanEndDate', this.getPlanStatisticsCutoffDate(this.selectedComparePlan))
        await this.comparePlan()
      },
      async comparePlan() {
        if (!this.selectedComparePlan) return
        const {planId: comparePlanId} = this.selectedComparePlan
        try {
          this.loading = true
          const {data} = await Axios.post('/salesPlan/compare', JSON2FormData({
            planId: this.planId,
            comparePlanId,
            ...this.compareForm
          }))
          if (data.state !== 1) await Promise.reject(data.msg)
          this.compareTableData = data.data.map((item, index) => {
            return {
              ...item,
              planTypeName: index === 0 ? '当前计划' : '对比计划',
              cutoffDate: index === 0 ? this.compareForm.endDate : this.compareForm.comparePlanEndDate
            }
          })
          this.$nextTick(() => this.renderChart())
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '数据加载失败')
        } finally {
          this.loading = false
        }
      },
      initChart() {
        if (!this.$refs.chart) {
          this.compareChart = null
          return
        }
        if (this.compareChart) {
          this.compareChart.resize()
          return
        }
        this.compareChart = echarts.init(this.$refs.chart)
        this.compareChart.resize({height: '420px'})
      },
      renderChart() {
        this.initChart()
        const isAmount = this.compareChartIsAmount
        const data = this.compareTableData.map(item => {
          return {
            name: `${item.planName} (完成率${isAmount ? this.amountPercentage(item) : this.quantityPercentage(item)}%)`,
            value: isAmount ? item.finishedAmount : item.finishedQuantity,
            value2: isAmount ? item.amount : item.quantity,
            ...item
          }
        })
        const numberFormatter = this.numberFormatter
        const option = {
          color: [
            'rgb(58,161,255)',
            'rgb(54,203,203)',
            'rgb(78,203,115)',
            'rgb(251,212,56)',
            'rgb(242,99,123)',
            'rgb(151,95,229)',
          ],
          backgroundColor: '#fff',
          tooltip: {
            trigger: 'item',
            formatter({data, value}) {
              const {name} = data
              return `<span>${name}&nbsp;&nbsp;&nbsp;<small style="font-weight: bold">${numberFormatter(value)}</small></span>`
            }
          },
          legend: {
            orient: 'horizontal',
            left: '5%'
          },
          xAxis: [
            {
              type: 'category',
              data: data.map(item => item.name),
            }
          ],
          yAxis: [{
            type: 'value',
            axisLabel: {
              formatter(value) {
                return numberFormatter(value)
              }
            }
          }],
          series: [
            {
              name: this.compareChartIsAmount ? '计划完成金额' : '计划完成数量',
              type: 'bar',
              data,
              label: {
                show: true,
                position: 'top',
                formatter(params) {
                  return params.value === 0 ? '' : numberFormatter(params.value)
                }
              }
            },
            {
              name: this.compareChartIsAmount ? '总计划金额' : '总计划数量',
              type: 'bar',
              data: data.map(item => ({...item, value: item.value2})),
              label: {
                show: true,
                position: 'top',
                formatter(params) {
                  return params.value === 0 ? '' : numberFormatter(params.value)
                }
              }
            }
          ]
        }
        this.compareChart.setOption(option)
      },
      getPlanStatisticsCutoffDate(plan) {
        const {startDate, endDate} = plan
        const systemTime = top.$GlobalDefine.getSystemTime('yyyy-MM-dd')
        if (!startDate || !endDate) return systemTime
        /**
         * 存在默认值，如果当前服务器时间在两个计划范围内，则默认服务器时间
         * 如果当前服务器时间不在计划时间范围内，则默认【计划最后一天】
         * */
        // 1. 把字符串转成日期对象
        const systemTimeDate = new Date(systemTime)
        const startDateDate = new Date(startDate)
        const endDateDate = new Date(endDate)
        // 2. 在计划时间内，返回当前服务器时间
        if (systemTimeDate >= startDateDate && systemTimeDate <= endDateDate) {
          return systemTime
        }
        // 3. 不在计划时间范围内，返回计划最后一天
        return endDate
      },
      getPlanCutoffDatePickerOption(plan) {
        const {startDate, endDate} = plan
        if (!startDate || !endDate) return {}
        // 转成Date对象
        const startDateDate = new Date(startDate)
        const endDateDate = new Date(endDate)
        return {
          disabledDate(date) {
            return date.getTime() < (startDateDate.getTime() - 86400000) || date.getTime() > endDateDate.getTime()
          }
        }
      },
      makeTableJSON(ref, data, mapFn) {
        if (!ref || !Array.isArray(data)) return []
        mapFn = mapFn || (row => row)
        const columns = ref.columns
        const result = [columns.map(({label}) => label || '')]
        data.forEach(row => result.push(columns.map(({property}) => {
          const val = mapFn(row[property], row, property)
          return val === null || val === void 0 ? '' : val
        })))
        return result
      },
      sleep(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay))
      },
      async exportComparePlan() {
        const loading = this.$loading({lock: true, text: '正在导出报表...', spinner: 'el-icon-loading'})
        try {
          if (!this.compareTableData.length) await Promise.reject('暂无需要导出的数据')
          const name = this.compareTableData.map(item => item.planName).join('-').replace(/[\s%*/\\:"<>|?]/g, '') + `比对报表`
          const workbook = new ExcelJS.Workbook();
          const worksheet = workbook.addWorksheet(name)
          const rows = this.makeTableJSON(
            this.$refs.compareTable.$refs['alexTable'],
            this.compareTableData,
            (value, row, property) => {
              switch (property) {
                case 'startDate':
                  return this.timerFormat(row)
                case 'quantityPercentage':
                  return this.quantityPercentage(row) + '%'
                case 'amountPercentage':
                  return this.amountPercentage(row) + '%'
                case 'quantity':
                  return `${row.finishedQuantity}/${row.quantity}`
                case 'amount':
                  return `${number2money(row.finishedAmount)}/${number2money(row.amount)}`
                default:
                  return value
              }
            }
          )
          let lastRow, offset, lastCol = 0, gutter = 3
          lastRow = offset = rows.length
          const addGutter = () => {
            worksheet.addRows(Array(gutter).fill([]))
            offset += gutter
            lastRow += gutter
          }
          // 添加基本数据
          worksheet.addRows(rows)
          // 添加间隙
          addGutter()
          lastCol = rows[0].length
          const writeChart = async chart => {
            await this.sleep(2000)
            // 添加间隙
            addGutter()
            const imgData = chart.getDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
            const imgId = workbook.addImage({
              base64: imgData,
              extension: 'png',
            })
            const col = 24 / 2
            const row = 30 / 2
            worksheet.mergeCells(
              lastRow,
              0,
              lastRow + row,
              col
            )
            worksheet.addImage(imgId, {
              tl: {col: 0, row: lastRow},
              br: {col: col, row: lastRow + row},
              editAs: 'oneCell'
            })
            lastRow += row
          }
          // 写入图形
          await writeChart(this.compareChart)
          this.compareChartIsAmount = !this.compareChartIsAmount
          this.renderChart()
          await writeChart(this.compareChart)
          const buffer = await workbook.xlsx.writeBuffer()
          saveAs(new Blob([buffer], {type: "application/octet-stream"}), `${name}-导出日期-${top.$GlobalDefine.getSystemTime('yyyy_MM_dd hh_mm_ss')}.xlsx`)
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          console.warn(e)
          this.$errorMsg(typeof e === 'string' ? e : '导出失败')
        } finally {
          loading.close()
        }
      },
    },
    computed: {
      amountSource() {
        return Object.freeze(',销售订单金额,到款金额'.split(','))[this.planEntity.amountSource]
      },
      finishedState() {
        return Object.freeze(',进行中,已超期,已完成'.split(','))[this.planEntity.finishedState]
      },
      quantityPercentage() {
        return row => row.quantity ? Math.round(row.finishedQuantity / row.quantity * 100) || 0 : 0
      },
      amountPercentage() {
        return row => row.amount ? Math.round(row.finishedAmount / row.amount * 100) || 0 : 0
      },
      showPlanCompare() {
        return +this.currentKindId === -2
      },
      showSubPlanPlaceholder() {
        return +this.currentKindId === -1
      },
      showSubPlanTable() {
        return +this.currentKindId > 0
      },
      timerFormat() {
        return row => {
          let {startDate, endDate} = row
          startDate = startDate || ''
          endDate = endDate || ''
          return [startDate, endDate].filter(T => T).map(t => t.replace(/-/g, '/')).join(' - ')
        }
      },
      planStatus() {
        return finishedState => {
          if ([2, 3].indexOf(finishedState) === -1)
            return {}
          return {
            class: 'el-icon-info,el-icon-success'.split(',')[finishedState - 2],
            style: {
              fontSize: '16px',
              color: '#d94d4d,#74c041'.split(',')[finishedState - 2],
            }
          }
        }
      },
      planStatusText() {
        return finishedState => {
          if ([2, 3].indexOf(finishedState) === -1) return ''
          return '未完成,已完成'.split(',')[finishedState - 2]
        }
      },
      allowEdit() {
        const operatorId = Number(getCookie('operatorId'))
        return this.planEntity.salesPlanEditorList.some(({editorId}) => operatorId === editorId) && this.fu_crm_sales_plan_add
      },
      // 权限点意义更改为管理权限: 控制：删除、导出、设置算法、编辑
      fu_crm_sales_plan_add() {
        return getAuthority('fu_crm_sales_plan_add')
      },
      // @Referer: http://deve.smtcrm.com:81/zentao/task-view-3270.html > 4.去掉计划外统计
      // outerOfPlanData() {
      //   let {finishedQuantity, finishedAmount} = this.planEntity
      //   this.tableData.forEach(item => {
      //     finishedQuantity -= item.finishedQuantity
      //     finishedAmount -= item.finishedAmount
      //   })
      //   return {
      //     planName: '子计划外实际业绩',
      //     startDate: null,
      //     quantity: finishedQuantity,
      //     quantityPercentage: null,
      //     amount: finishedAmount,
      //     amountPercentage: null,
      //     projectName: null,
      //     workTeamName: null,
      //     ownerName: null,
      //   }
      // },
      // planList() {
      //   return [...this.tableData, this.outerOfPlanData]
      // }
      finalPlanKindList() {
        const planKindList = [...this.planKindList]
        if (planKindList.length === 0) {
          planKindList.push({id: -1, text: '子计划'})
        }
        planKindList.push({id: -2, text: '比对计划'})
        return planKindList
      },
      numberFormatter() {
        return this.compareChartIsAmount ? number2money : (num) => num
      }
    },
    components: {
      SalePlanSelector,
      AlexTable,
      SalesPlanEditor,
      SalesPlanAlg,
      SalesPlanAllocation
    }
  }
</script>

<style>
  body {
    overflow-y: auto
  }
</style>
<style scoped>
  .sp-detail {
    --table-min-height: 0;
    background-color: #eff3fc;
    padding-bottom: 20px;
  }

  .sp-detail__header {
    background-color: #fff;
    padding: 30px 6.25vw 14px;
  }

  .sp-detail__header > * {
    display: flex;
  }

  .grey-text, .grey-text__59 {
    color: #999;
    font-size: 14px;
  }

  .grey-text__59 {
    color: #595959;
  }

  .fz18-c333-fwb {
    font-size: 18px;
    color: #333;
    font-weight: bold;
    text-align: center;
    padding: 0 4px;
  }

  .spdh__basic {
    align-items: center;
    justify-content: space-between;
  }

  .spdh__basic .left {
    display: flex;
    align-items: center;
  }

  .spdh__basic .left > * {
    margin-right: 30px;
  }

  .spdh__basic .left h2 {
    font-size: 20px;
    margin-top: 0;
    margin-bottom: 0;
  }

  .spdh__info {
    margin-top: 12px;
    margin-bottom: 16px;
  }

  .spdh__info > span {
    margin-right: 12px;
  }

  .spdh__progress .progress, .spdh__progress .timer {
    display: flex;
    margin-right: 2.1vw;
  }

  .progress__icon, .timer__icon {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: #409EFF;
    text-align: center;
    line-height: 40px;
    user-select: none;
    margin-right: 14px;
    flex-shrink: 0;
  }

  .timer__icon {
    background: #e8e8e8;
  }

  .progress__info {
    flex-grow: 1;
  }

  .progress__text {
    display: flex;
    align-items: center;
  }

  .progress__text span:nth-child(2) {
    flex-grow: 1;
  }

  .el-progress-bar {
    padding: 0;
  }

  .spdh__entity {
    display: flex;
    flex-wrap: wrap;
  }

  .spdhe__item {
    flex-basis: 33.3333%;
    display: flex;
    margin-bottom: 10px;
  }

  .spdhe__item span {
    max-height: 60px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical
  }

  .spdhe__item > span:first-child {
    width: 6.25vw;
    min-width: 120px;
    margin-right: 24px;
    text-align: right;
  }

  .sp-detail__table {
    margin: 20px 6.25vw 0;
    padding: 10px 20px;
    background-color: #fff;
  }

  .sp-detail__table .el-tabs__item {
    font-size: 16px;
  }

  .sp-detail__table .el-tabs__nav {
    padding-bottom: 5px;
  }

  .sp-detail__table .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #eaeaea;
  }

  .sp-detail__table.empty {
    height: calc(100vh - 334px);
  }

  .sp-detail__table.empty .placeholder {
    margin-top: 9vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .buttons-small {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 5px;
    padding-left: 3px;
    padding-right: 3px;
  }

  .el-button {
    border-radius: 0;
  }
  .no-border-color{
    border-right-color: transparent!important;
  }

  .sp-detail__table .el-table__body-wrapper {
    min-height: var(--table-min-height, 100px);
  }

  .compare-form {
    margin-left: auto;
  }
  .compare-form .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
  .compare-form .el-date-editor.el-input,
  .compare-form .el-date-editor .el-input__inner {
    width: 150px;
    border-radius: 0;
  }
</style>