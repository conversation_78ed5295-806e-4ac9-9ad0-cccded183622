<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            class="text-over-hidden"
            style="max-width: 240px"
            :title="item"
            :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" v-if="getAuthority('fu_report_export')" style="margin: 0 0 0 10px;" class="btn"
                   icon="el-icon-upload2" @click="$emit('export')">导出
        </el-button>
        <el-button size="mini" @click="showColumnDisplayDialog = true">列显示设置</el-button>
        <el-button size="mini" @click="saveColumnWidth()">保存列宽</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 188px)"
      operation-width="120px"
      border
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      @sort-change="sort"
      :padding="10"
      :handle-pagination="handlePagination"
      @selection-change="$emit('table-select', $event)"
      show-summary
      :summary-method="getTableDataSum"
      :total="total"
      :loading="loading">
      <template slot="companyName" slot-scope="{row}">
        <el-link :underline="false" type="primary" @click="openNewTab(row.clientId, row.companyName)">
          {{ row.companyName }}
        </el-link>
      </template>
      <template :slot="prop" slot-scope="{row}" v-for="({prop, page}) in slots">
        <div style="text-align: right" v-if="page" :key="prop">
          <el-link
            :underline="false"
            type="primary"
            @click.stop="gotoPage(row, page)">
            {{ row[prop] || '0.00' }}
          </el-link>
        </div>
      </template>
      <template slot="orderCount" slot-scope="{row}">
        <el-link
          v-if="row.orderCount"
          :underline="false"
          type="primary"
          @click.stop="gotoPage(row, 'CGOrder')">
          {{ row.orderCount }}
        </el-link>
      </template>
      <template slot="paymentCount" slot-scope="{row}">
        <el-link
          v-if="row.paymentCount"
          :underline="false"
          type="primary"
          @click.stop="gotoPage(row, 'payment')">
          {{ row.paymentCount }}
        </el-link>
      </template>
      <template slot="alex-operation" slot-scope="{row}">
        <el-link type="primary" :underline="false" @click.stop="gotoPage(row,'purchaseDetail')">
          查看台账明细
        </el-link>
      </template>
    </alex-table>
    <column-display-dialog
      :show="showColumnDisplayDialog"
      :left-data="leftData"
      :right-data="rightData"
      :exclude="['projectName', 'companyName']"
      @reset="handleReset"
      @save="handleSave"
      @close="showColumnDisplayDialog=false">
    </column-display-dialog>
  </el-container>
</template>

<script>
const AlexTable = importC('AlexTable')
export default {
  name: "SupplierSummaryMainBody",
  data() {
    return {
      breadcrumbList: [],
      fields: [],
      total: 0,
      tableData: [],
      tableDataAll: {},
      loading: false,
      requestHistory: {rows: 20, page: 1},
      summationData: [],
      dynamicPropMap: {},
      prop2page: {
        orderAmount: 'CGOrder',
        paymentAmount: 'payment',
        payablesAmount: 'payablesAmount',
      },
      slots: []
    }
  },
  mixins: [Mixins.columnDisplay('supplierSummary')],
  methods: {
    handlePagination({page, rows}) {
      this.requestHistory.page = page
      this.requestHistory.rows = rows
      this.requestTableData(this.requestHistory)
    },
    sort({column, prop, order}) {
      let data = this.requestHistory;
      if (order) {
        data.order = order.replace('ending', '');
        data.sort = prop;
      } else {
        delete (data.order)
        delete (data.sort)
      }
      this.requestTableData(data)
    },
    clearSearch() {
      this.breadcrumbList = [];
      this.requestTableData({page: 1, rows: 20});
      this.$emit('clear-search')
    },
    createFieldsEx(callback) {
      this.createFields(() => {
        this.fields.forEach(item => {
          switch (item.prop) {
            case 'companyName':
            case 'projectName':
              Object.assign(item, {fixed: 'left', minWidth: '180'})
              break
          }
        })
        callback && callback.call(this)
      })
    },
    getTableDataSum(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = this.tableDataAll[column.property];
      });
      return sums;
    },
    requestTableData(queryData = {}, callback = () => this.loading = false) {
      this.loading = true
      this.requestHistory = queryData
      if (!this.requestHistory.workTeamId)
        this.requestHistory.workTeamId = teamId
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      Axios.post('/statistics/supplierSummary', JSON2FormData(queryData))
        .then(({data}) => {
          const {state, total, rows, data: {all}} = data
          if (state !== 1) return Promise.reject()
          this.total = total
          const rowBuilder = this.buildDynamicFields(all)
          this.tableDataAll = rowBuilder(all)
          this.tableData = rows.map(row => rowBuilder(row))
          callback(this.$refs.table, rows)
        })
        .catch(e => {
          console.warn(e)
          this.$errorMsg('数据加载失败')
          this.loading = false
        })
    },
    search(data, isFirst) {
      let postData = data.requestData
      let breadcrumbList = data.breadcrumbList
      postData.page = 1
      postData.rows = 20
      //breadcrumbList 面包屑导航
      this.breadcrumbList = breadcrumbList
      if (isFirst) {
        this.requestHistory = postData
        return this.updateColumnDisplay()
      }
      this.requestTableData(postData)
    },
    buildGotoPageParams(page, mixedParams) {
      const {
        projCltId,
        projectId,
        projectName,
        orderSignDateStart,
        orderSignDateEnd,
        paymentBookDateStart,
        paymentBookDateEnd,
      } = mixedParams
      const reqHistory = {...this.requestHistory}
      delete reqHistory.page
      delete reqHistory.rows

      const cleanObject = obj => {
        const ret = {}
        Object.keys(obj).forEach(key => {
          const val = obj[key]
          if (val === null || val === '' || val === void 0 || val === []) return
          ret[key] = val
        })
        return ret
      }
      const mapObject = (obj, keyFn) => {
        if (!keyFn) keyFn = k => `supplierStatistics.${k}`
        const ret = {}
        Object.keys(obj).forEach(key => {
          const val = obj[key]
          ret[keyFn(key, val, obj)] = val
        })
        return ret
      }
      switch (page) {
        case 'CGOrder':
          return {
            isAllow: this.getAuthority('fu_crm_project_buy_order'),
            titlePrefix: '采购订单-',
            queryParams: cleanObject(mapObject({
              projCltId,
              ...reqHistory,
            }))
          }
        case 'payment':
          return {
            isAllow: this.getAuthority('fu_crm_project_payment'),
            titlePrefix: '付款单-',
            queryParams: cleanObject(mapObject({
              projCltId,
              ...reqHistory,
            }))
          }
        case 'purchaseDetail':
          return {
            isAllow: this.getAuthority('fu_crm_project_purchase_ledger'),
            titlePrefix: '项目采购台账明细-',
            queryParams: cleanObject({
              projCltId,
              projectId,
              projectName,
              orderSignDateStart,
              orderSignDateEnd,
              paymentBookDateStart,
              paymentBookDateEnd
            })
          }
        default:
          return {isAllow: false}
      }
    },
    gotoPage(row, page) {
      const query = '?teamId=' + teamId + '&page=' + String(page).replace('!', '') + '&extend='
      const result = this.buildGotoPageParams(page, Object.assign({}, row, this.requestHistory))
      const {isAllow, titlePrefix, queryParams} = result
      if (!isAllow) return this.$errorMsg('权限不足！')
      const searchObject = {...queryParams, details: true, ref: '项目供应商采购汇总'}
      const extend = btoa(encodeURIComponent(JSON.stringify(searchObject)))
      FinalLinkOpen('ProjectMarket/sales-contract.html' + query + extend, null, true, false, titlePrefix + row.projectName)
    },
    copyText(text) {
      copyText2Clipboard(text, this)
    },
    updateColumnDisplay() {
      this.createFieldsEx(() => {
        this.tableData = []
        this.requestTableData(this.requestHistory, table => {
          table.computedWidth().then(() => this.loading = false)
        })
      })
    },
    buildDynamicFields(all) {
      const dynamicFields = [
        'balance',
        'orderAmount',
        'paymentAmount',
        'payablesAmount'
      ]
      const baseConfig = {
        minWidth: '120',
        headerAlign: 'center',
        align: 'right',
        sortable: 'custom',
        showOverflowTooltip: true,
      }
      this.dynamicPropMap = {}
      // clean old children columns
      this.fields.forEach(item => {
        if (dynamicFields.includes(item.prop)) {
          this.$set(item, 'children', [])
        }
      })
      dynamicFields.forEach(key => {
        const list = all[key]
        if (!Array.isArray(list) || !list.length) return
        const baseProp = key.replace('List', '')
        const setChildren = (_prop, list) => {
          _prop = _prop || baseProp
          const children = list.map((item) => {
            const {currencyName, currencyCode, fDatagridSetId, fShowWidth} = item
            const prop = [_prop, currencyCode].join('_')
            this.dynamicPropMap[prop] = fDatagridSetId
            const fixedWidth = fShowWidth && fShowWidth > 30
            // this.slots.push({
            //   page: this.prop2page[baseProp],
            //   prop,
            // })
            return {
              ...baseConfig,
              label: currencyName,
              fixedWidth: true,
              width: fixedWidth ? fShowWidth : 110,
              prop
            }
          })
          const fieldIndex = this.fields.findIndex(item => item.prop === _prop)
          if (fieldIndex !== -1) {
            this.$set(this.fields[fieldIndex], 'sortable', false)
            this.$set(this.fields[fieldIndex], 'children', children)
          }
        }
        setChildren(baseProp, list)
      })
      return row => {
        const newRow = {...row}
        dynamicFields.forEach(key => {
          const list = newRow[key]
          if (!Array.isArray(list) || !list.length) return
          const baseProp = key.replace('List', '')
          const getChildren = (_prop, list) => {
            _prop = _prop || baseProp
            list.forEach((item) => {
              const {currencyCode, amountStr} = item
              const prop = [_prop, currencyCode].join('_')
              newRow[prop] = amountStr || ''
            })
            return newRow
          }
          getChildren(baseProp, list)
        })
        return newRow
      }
    },
  },
  components: {
    AlexTable
  },
  filters: {
    number2money(num) {
      if (!num) return '￥0'
      return '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
  }
}
</script>

<style scoped>
.el-main {
  padding-bottom: 0 !important;
}

.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.table-footer {
  /*margin-top: -5px;*/
  padding: 5px 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  /*text-align: center;*/
  font-size: 14px;

}

.table-footer > div {
  margin: 0 40px 0 0;
}

.table-footer > div > p {
  min-width: 200px;
  margin: 0;
}

.table-footer > p:nth-of-type(1) {
  text-align: left;
}

.el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}

.el-table__footer .cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alex-table .el-table th:not(.is-sortable) .cell {
  line-height: 30px;
}
</style>