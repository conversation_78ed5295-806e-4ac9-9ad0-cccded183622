<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    title="结算明细"
    width="800px">
    <el-main slot="body" class="dialog-body" ref="body" v-autoscroll>
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left">
          <i class="dot" :style="{backgroundColor: getCheckStatusDotColor(tForm.checkState)}"></i>
          <template v-if="tForm.checkState > 1">{{ tForm.checkTime }}</template>
          {{ getCheckStatusLabel(tForm.checkState) }}
          <template v-if="tForm.checkState === 1 && tForm.settlementFileName"> 缺：
            {{tForm.settlementFileName}}
          </template>
          <span v-if="tForm.checkState > 1 && tForm.accountPeriod" style="color: #999; margin-left: 10px;">
            提成账期 {{ new Date(tForm.accountPeriod).format('yyyy年M月') }}
          </span>
        </div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-arrow-left"
            type="text"
            @click="prev">上一条
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="next">
            下一条
            <i class="el-icon-arrow-right"></i>
          </el-button>
          <template v-if="getAuthority('fu_crm_receipt_settlement_detail_check')">
            <el-button
              v-if="tForm.checkState === 1"
              size="mini"
              type="custom"
              class="is"
              @click="checkSettlement(null)">
              弃审
            </el-button>
            <el-button
              size="mini"
              type="custom"
              :class="{is: tForm.checkState > 1}"
              @click="checkSettlement(tForm.checkState > 1 ? null : tForm.checkState || 0)">
              {{ tForm.checkState > 1 ? '弃审' : '审核' }}
            </el-button>
          </template>
        </div>
      </el-header>
      <el-form label-width="100px" size="mini">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="结算明细信息" :name="1">
            <el-form-item label="项目"><span>{{ tForm.projectName }}</span></el-form-item>
            <el-form-item label="业绩归属人"><span>{{ tForm.ownerName }}</span></el-form-item>
            <el-form-item label="公司客户" class="full"><span>{{ tForm.cltName }}</span></el-form-item>
            <el-form-item label="结算操作人"><span>{{ tForm['creatorName'] }}</span></el-form-item>
            <el-form-item label="结算时间"><span>{{ tForm.createTime }}</span></el-form-item>
            <el-form-item label="结算金额" class="full">{{ tForm | settlementAmountFilter }}</el-form-item>
          </el-collapse-item>
          <el-collapse-item title="关联信息" :name="2">
            <el-tabs class="link-info-tabs" v-model="currentTab" type="card">
              <el-tab-pane label="合同信息" name="contract">
                <div class="link-info-item">
                  <span class="li-item__title"><template v-if="!linkInfo.contract">暂无</template>合同信息</span>
                  <div class="li-item__cell" v-if="linkInfo.contract">
                    <div>
                      <el-image src="../img/settlement/contract.svg"></el-image>
                    </div>
                    <div>
                      <span>
                        <span style="color: #000; margin-right: 20px">
                          合同序号
                          <el-link
                            @click="$emit('open-market', tForm.projectName, 'contract', tForm.contractId)"
                            type="primary" :underline="false">
                          {{ linkInfo.contract.contractId }}
                        </el-link>
                        </span>
                        <span>{{ linkInfo.contract.signDate || '暂无' }}</span>
                      </span>
                      <span>人民币总额 ￥{{ linkInfo.contract.amount || 0 }}</span>
                    </div>
                    <div>
                      <span>业绩归属人</span>
                      <span>{{ linkInfo.contract.ownerName || '暂无' }}</span>
                    </div>
                    <div>
                      <span>展位号</span>
                      <span>{{ linkInfo.contract.boothNum || '暂无' }}</span>
                    </div>
                    <div v-if="isShowPaperContractState">
                      <span>纸质合同</span>
                      <span v-if="linkInfo.contract.paperContractState">
                        <i style="color: #67c23a;" class="el-icon-success"></i>
                        已归档
                      </span>
                      <span v-else>
                        <i style="display: inline-block;width: .8em;height: .8em;border-radius: 50%;background-color: #eee;"></i>
                        未归档
                      </span>
                    </div>
                  </div>
                </div>
                <div class="link-info-attachment"
                     v-if="linkInfo.contract && linkDocument.contract && linkDocument.contract.length">
                  <span class="li-attachment__title">合同附件</span>
                  <ul class="li-attachment__list">
                    <li class="li-attachment__item" :key="item.url" v-for="item in linkDocument.contract">
                      <el-link @click="previewDocument(item)" type="primary" :underline="false" icon="el-icon-link">
                        {{ item.name }}
                      </el-link>
                    </li>
                  </ul>
                </div>
              </el-tab-pane>
              <el-tab-pane label="收款单" name="receipt">
                <div class="link-info-item">
                  <span class="li-item__title"><template v-if="!linkInfo.receipt">暂无</template>关联收款单</span>
                  <div class="li-item__cell" v-if="linkInfo.receipt">
                    <div>
                      <el-image src="../img/settlement/receipt.svg"></el-image>
                    </div>
                    <div>
                      <span>
                        <span style="color: #000; margin-right: 20px">
                          收款单序号
                          <el-link
                            @click="$emit('open-market', tForm.projectName, 'receipt', tForm.receiptId)"
                            type="primary"
                            :underline="false">{{ linkInfo.receipt.receiptId }}</el-link>
                        </span>
                        <span>{{ linkInfo.receipt.bookDate || '暂无' }}</span>
                      </span>
                      <span>收款单金额
                        {{ linkInfo.receipt.currencySymbol }}
                        {{ linkInfo.receipt.amount || 0 }}
                        <template
                          v-if="!linkInfo.receipt.currencySymbol">
                          {{ linkInfo.receipt.currencyName }}
                        </template>
                      </span>
                    </div>
                    <div>
                      <span>业绩归属人</span>
                      <span>{{ linkInfo.receipt.ownerName || '暂无' }}</span>
                    </div>
                    <div>
                      <span>账户</span>
                      <span>{{ linkInfo.receipt.accountName || '暂无' }}</span>
                    </div>
                  </div>
                </div>
                <div class="link-info-attachment"
                     v-if="linkInfo.receipt && linkDocument.receipt && linkDocument.receipt.length">
                  <span class="li-attachment__title">收款单附件</span>
                  <ul class="li-attachment__list">
                    <li class="li-attachment__item" :key="item.url" v-for="item in linkDocument.receipt">
                      <el-link @click="previewDocument(item)" type="primary" :underline="false" icon="el-icon-link">
                        {{ item.name }}
                      </el-link>
                    </li>
                  </ul>
                </div>
              </el-tab-pane>
              <el-tab-pane label="应收款" name="receivables">
                <div class="link-info-item">
                  <span class="li-item__title"><template v-if="!linkInfo.receivables">暂无</template>关联应收款</span>
                  <div class="li-item__cell" v-if="linkInfo.receivables">
                    <div>
                      <el-image src="../img/settlement/receivables.svg"></el-image>
                    </div>
                    <div>
                      <span>
                          <span style="color: #000; margin-right: 20px">
                          应收款序号
                          <el-link
                            @click="$emit('open-market', tForm.projectName, 'receivables', tForm.receivablesId)"
                            type="primary"
                            :underline="false">{{ linkInfo.receivables.receivablesId }}</el-link>
                        </span>
                        <span>{{ linkInfo.receivables.bookDate || '暂无' }}</span>
                      </span>
                      <span>应收款金额
                        {{ linkInfo.receivables.currencySymbol }}
                        {{ linkInfo.receivables.amount || 0 }}
                        <template
                          v-if="!linkInfo.receivables.currencySymbol">
                          {{ linkInfo.receivables.currencyName }}
                        </template>
                      </span>
                    </div>
                    <div>
                      <span>产品名称</span>
                      <span>{{ linkInfo.receivables.prodName || '暂无' }}</span>
                    </div>
                    <div>
                      <span>备注</span>
                      <span>{{ linkInfo.receivables.memo || '暂无' }}</span>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <file-preview ref="filePreview"></file-preview>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
const FilePreview = importC('FilePreview')
export default {
  name: "ReceiptSettlementDetails",
  components: {CustomDialog, FilePreview},
  inject: ['checkStatus', 'getCheckStatusLabel', 'getCheckStatusDotColor'],
  data() {
    return {
      activeNames: [1, 2],
      currentTab: 'contract',
      tForm: {},
      show: false,
      linkInfo: {
        contract: null,
        receipt: null,
        receivables: null,
      },
      linkDocument: {
        contract: null,
        receipt: null,
        receivables: null,
      },
      isShowPaperContractState: false
    }
  },
  methods: {
    async getShowPaperContractState() {
      try {
        const {data} = await Axios.post('/exhibition/selectExhibitSetting', JSON2FormData({
          teamId,
          taskKindCode: 'fo_contract_field_set',
          param: 'paperContractState'
        }))
        if (!data || !Array.isArray(data)) return
        //获取合同编号自动生成
        const paperContractState = data.find(item => 'paperContractState' === item.param)
        this.isShowPaperContractState = Boolean(paperContractState && paperContractState.isNull)
      }catch (e) {
        console.warn(e)
      }
    },
    async requestTFormById(settlementId) {
      try {
        if (!settlementId) await Promise.reject('数据异常，请刷新重试！')
        const {data} = await Axios.post('/fnReceiptSettlement/getById', JSON2FormData({settlementId}))
        if (data.state !== 1) await Promise.reject(data.msg || data.message)
        if (!data.data) await Promise.reject('数据可能已被删除, 请刷新重试')
        this.tForm = data.data
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '数据加载失败').finally(this.close)
      }
    },
    async getLinkInfo() {
      Object.keys(this.linkInfo).forEach(key => this.linkInfo[key] = null)
      try {
        const {contractId, receiptId, receivablesId} = this.tForm
        if (contractId) {
          const {data} = await Axios.post('/contract/getMoreById', JSON2FormData({contractId, workTeamId: teamId}))
          if (data.state === 1) this.linkInfo.contract = data.data
        }
        if (receiptId) {
          const {data} = await Axios.post('/fnReceipt/getById', JSON2FormData({receiptId}))
          if (data.state === 1) this.linkInfo.receipt = data.data
        }
        if (receivablesId) {
          const {data} = await Axios.post('/fnReceivables/getById', JSON2FormData({receivablesId}))
          if (data.state === 1) this.linkInfo.receivables = data.data
        }
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg('关联信息加载失败')
      }
    },
    async getDocuments(id, table) {
      const request = async (id, table) => {
        const {data} = await Axios.get(`/client/getDocumentsByfOrgNum?recordCode=${id}&tableName=${table}`)
        if (data.state === 1) return data.result.map(item => {
          return {
            accId: item.accId,
            f_doc_code: item.f_doc_code,
            name: item.f_doc_name + item.f_doc_suffix,
            url: attachUrl + item.f_doc_file.replace('\\', '/')
          }
        })
        return null
      }
      try {
        if (id && table) return request(id, table)
        Object.keys(this.linkDocument).forEach(key => this.linkDocument[key] = null)
        const {contractId, receiptId/*, receivablesId*/} = this.tForm
        this.linkDocument.contract = await request(contractId, 't_contract')
        this.linkDocument.receipt = await request(receiptId, 't_receipt')
        // this.linkDocument.receivables = await request(receivablesId, 't_receivables')
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '附件加载失败！')
      }
    },
    async checkSettlement(checkState) {
      try {
        if (checkState === null) await this.$confirm('确定要弃审这条数据吗？', '提示', {type: 'info'})
        this.$emit('check', checkState, [this.tForm], true, (this.tForm.settlementFileList || []).map(item => item.settlementFileId))
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        throw e
      }
    },
    previewDocument({url, name}, download = false) {
      this.$refs['filePreview'].open({url, name}, !!download)
    },
    async open(id) {
      this.show = true
      await this.getShowPaperContractState()
      await this.read(id)
    },
    async refresh() {
      await this.requestTFormById(this.tForm.settlementId)
    },
    async read(id) {
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.3)'
      })
      try {
        await this.requestTFormById(id)
        await this.getLinkInfo()
        await this.getDocuments()
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        throw e
      } finally {
        loading && loading.close()
      }
    },
    close() {
      this.show = false
      this.$emit('refresh-body')
    },
    next() {
      $bus.$emit('ReceiptSettlement:next', this, this.tForm.settlementId)
    },
    prev() {
      $bus.$emit('ReceiptSettlement:prev', this, this.tForm.settlementId)
    },
  },
  filters: {
    settlementAmountFilter(row) {
      const {
        settlementAmount,
        settlementCurrencyCode,
        settlementCurrencyName,
        settlementCurrencySymbol,
        receivablesAmount,
        receivablesCurrencyCode,
        receivablesCurrencyName,
        receivablesCurrencySymbol,
        settlementRate
      } = row

      let settlement = settlementAmount
      if (receivablesCurrencySymbol)
        settlement = `${settlementCurrencySymbol}${settlement}`
      else if (receivablesCurrencyName)
        settlement = `${settlement}${settlementCurrencyName}`
      else
        settlement = `${settlement}${settlementCurrencyCode || 'RMB'}`


      let ret = receivablesAmount
      if (receivablesCurrencySymbol)
        ret = `${receivablesCurrencySymbol}${ret}`
      else if (receivablesCurrencyName)
        ret = `${ret}${receivablesCurrencyName}`
      else
        ret = `${ret}${receivablesCurrencyCode || 'RMB'}`
      return `${settlement} （已结算应收款${ret}  汇率${settlementRate || 1}）`
    }
  }
}
</script>

<style scoped>
.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.dialog-header .dialog-header-right > .el-button.el-button--custom {
  border-radius: 0;
  background-color: #63A9FB;
  color: #fff !important;
}

.dialog-header .dialog-header-right > .el-button.is.el-button--custom {
  background-color: #9FA9B3;
}

.el-collapse-item__header {
  height: 30px;
  line-height: 30px;
  color: #4696f9;
  background-color: #f5f5f5;
  padding-left: 20px;
}

.el-collapse-item__content {
  padding: 10px 20px 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.el-form .el-form-item.el-form-item--mini {
  flex: 0 0 342px;
  margin-bottom: 10px;
}

.el-form .el-form-item.el-form-item--mini.full {
  flex-basis: 100%
}

.link-info-tabs .el-tabs__item {
  height: 35px;
  line-height: 35px;
  border: 1px solid #ebeef5;
}

.link-info-tabs .el-tabs__item + .el-tabs__item {
  border-left: none;
}

.link-info-tabs .el-tabs__item:first-child {
  border-left: 1px solid #ebeef5;
}


.link-info-tabs .el-tabs__item.is-active {
  border-left: 1px solid;
  border-color: #409EFF;
}

.link-info-tabs .el-tabs__item + .el-tabs__item.is-active {
  margin-left: -1px;
}

.link-info-tabs.el-tabs--card > .el-tabs__header {
  border-bottom: none;
}

.link-info-tabs.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: none;
}


.link-info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.li-item__title,
.li-attachment__title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
}

.li-item__cell {
  display: flex;
  align-items: center;
}

.li-item__cell > div:first-child {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.li-item__cell > div + div {
  display: flex;
  flex-direction: column;
  margin-right: 40px;
  color: #aaa;
}

.link-info-attachment {
  display: flex;
  flex-direction: column;
}

.li-attachment__list {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding-left: 1rem;
  margin: 0;
}

.li-attachment__item {
  margin-bottom: 3px;
}

.li-attachment__item .el-link {
  padding: 3px 10px;
  width: 100%;
  justify-content: flex-start;
}

.li-attachment__item .el-link:hover {
  background-color: #f5f7fa;
}
</style>