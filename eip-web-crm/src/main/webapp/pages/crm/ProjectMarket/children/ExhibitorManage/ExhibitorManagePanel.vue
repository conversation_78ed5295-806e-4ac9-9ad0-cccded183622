<template>
  <div style="min-height: calc(100vh - 33px);">
    <exhibitor-manage-header ref="header"
      @select-projects="selectedProjects($event)"
      @init="init($event)" @search="search" @combined-query="combinedQuery"></exhibitor-manage-header>
    <div style="width: 100%;height: 24px;background-color: #F0F2F5;"></div>
    <!-- @contract-full="$refs.contractFull && $refs.contractFull.open(...$event)" -->
    <exhibitor-manage-main-body
      ref="body"
      :is-full-process="isFullProcess"
      @add="openDetails('add')"
      @booth-sure="boothSure"
      @booth-sure-batch="boothSureBatch"
      @change-pass="$refs.cPass && $refs.cPass.open($event)"
      @change-vip="changeVip"
      @clear-search="clearSearch"
      @combined-query="combinedQuery($event)"
      @contract="$refs.contract && $refs.contract.open($event)"
      @contract-full="openContractDetails(...$event)"
      @contract-tip="$refs.contractTip && $refs.contractTip.open(...$event)"
      @create-vip="createVip"
      @create-vip-corp="createVipCorp"
      @del-batch="delBatch"
      @edit-row="openDetails('edit', $event)"
      @enable-work-flow="enableWorkFlow"
      @export="export_"
      @hand-over="handOver"
      @hand-cancel="handCancel"
      @import="import_"
      @init-pass="initPass"
      @import-batch="$refs['batchList'] && $refs['batchList'].open('import_serveBooth', void 0, void 0, associatedProjectList.map(({id}) => id).toString())"
      @merge="merge"
      @order-tip="$refs.orderTip && $refs.orderTip.open($event)"
      @read-row="openDetails('read', $event)"
      @receipt="$refs.receipt && $refs.receipt.open($event)"
      @set-owner="$refs.setOwner && $refs.setOwner.open($event)"
      @sms-send-batch="$refs.body && $refs.body.smsSendBatch()"
      @special-rule="$refs.spRule && $refs.spRule.open($event)"
      @special-rule-batch="specialRule"
      @table-select="tableSelect"
    >
    </exhibitor-manage-main-body>
    <exhibitor-manage-contract ref="contract" @operation-complete="operationComplete"></exhibitor-manage-contract>
    <!-- <exhibitor-manage-contract-full ref="contractFull" @operation-complete="operationComplete" @remind="onRemind"></exhibitor-manage-contract-full> -->
    <!-- <contract-details ref="contractDetails" @operation-complete="operationComplete" @remind="onRemind"></contract-details> -->
    <exhibitor-manage-receipt ref="receipt" @operation-complete="operationComplete"></exhibitor-manage-receipt>
    <exhibitor-manage-change-pass ref="cPass" @operation-complete="operationComplete"></exhibitor-manage-change-pass>
    <exhibitor-manage-create-vip ref="cVip" @operation-complete="operationComplete"></exhibitor-manage-create-vip>
    <exhibitor-manage-create-vip-corp ref="cVipCorp" @operation-complete="operationComplete"></exhibitor-manage-create-vip-corp>
    <exhibitor-manage-change-vip ref="chVip" @operation-complete="operationComplete"></exhibitor-manage-change-vip>
    <exhibitor-manage-special-rule ref="spRule" @operation-complete="operationComplete"
      @select-complete="selectComplete"></exhibitor-manage-special-rule>
    <exhibitor-manage-set-owner ref="setOwner" @operation-complete="operationComplete"></exhibitor-manage-set-owner>
    <exhibitor-manage-details ref="details" @operation-complete="operationComplete"
        @special-rule="openSpecialRuleWithIds"
    ></exhibitor-manage-details>
    <exhibitor-manage-combined-query ref="cQuery" @combined-query="searchCq($event, false)"></exhibitor-manage-combined-query>
    <remind-box ref="remind"></remind-box>
    <contract-import
      ref="import"
      import-type="import_serveBooth"
      template-src="/eip-web-crm/excel/参展记录导入模板.xlsx?v=240621"
      result-api="/batchDtl/getImportResult"
      upload-api="/eip-web-crm/serveBooth/importData"
      @operation-complete="operationComplete"></contract-import>
    <import-batch-list ref="batchList" @operation-complete="operationComplete"></import-batch-list>
    <exhibitor-manage-contract-tip ref="contractTip"
      @go-contract="openContractDetails(...$event)"
      ></exhibitor-manage-contract-tip>
    <exhibitor-manage-order-tip ref="orderTip"
      @go-order="$refs.body && $refs.body.goBoothList($event)"
      ></exhibitor-manage-order-tip>
    <booth-sure ref="boothSure"></booth-sure>
    <exhibitor-manage-merge ref="merge"
      @done="location.reload()"
    ></exhibitor-manage-merge>
    <task-selector-slim ref="selectTask" @done="enableWorkFlowSure($event)"></task-selector-slim>
  </div>
</template>

<script>
  const ExhibitorManageHeader = importC('ExhibitorManageHeader')
  const ExhibitorManageMainBody = importC('ExhibitorManageMainBody')
  const ExhibitorManageContract = importC('ExhibitorManageContract')
  // const ExhibitorManageContractFull = importC('ExhibitorManageContractFull')
  // const ContractDetails = importC('ContractDetails')
  const ExhibitorManageReceipt = importC('ExhibitorManageReceipt')
  const ExhibitorManageChangePass = importC('ExhibitorManageChangePass')
  const ExhibitorManageCreateVip = importC('ExhibitorManageCreateVip')
  const ExhibitorManageCreateVipCorp = importC('ExhibitorManageCreateVipCorp')
  const ExhibitorManageChangeVip = importC('ExhibitorManageChangeVip')
  const ExhibitorManageSpecialRule = importC('ExhibitorManageSpecialRule')
  const ExhibitorManageSetOwner = importC('ExhibitorManageSetOwner')
  const ExhibitorManageDetails = importC('ExhibitorManageDetails')
  const BoothSure = importC('BoothSure')
  const ExhibitorManageCombinedQuery = importC('ExhibitorManageCombinedQuery')
  const RemindBox = importC('RemindBox')
  const ContractImport = importC('ContractImport')
  const ImportBatchList = importC('ImportBatchList')
  const ExhibitorManageOrderTip = importC('ExhibitorManageOrderTip')
  const ExhibitorManageContractTip = importC('ExhibitorManageContractTip')
  const ExhibitorManageMerge = importC('ExhibitorManageMerge')
  const TaskSelectorSlim = importC('TaskSelectorSlim')
  const url = (new URL(window.location.href)).searchParams;
  export default {
    name: "ExhibitorManagePanel",
    data() {
      return {
        tableSelection: [],
        isFullProcess: true,
        isEnableBoothDemand: false,
        isUnionEnable: false,
        taskDescs: ['未启动','已启动','草稿','已提交','已审核','','','','','已驳回'],
        toFixedFields: [],// 'amount oriAmount firstPayment amountForeign'.split(' '),
        exporting:false,
        saleStates: [],
        projectId: '',
        projectName: '',
        contractProjectId: '',
        mainProjectId: '',
        // all: false,
        // page:1,
        associatedProjectList: []
      }
    },
    created(){
      this.getServerBoothSetting();
      this.getServerBoothSettingUnion();
      this.getColors();
      // await Promise.all([
      //   this.getGroupFlowSetting(),
      //   // this.getParentAndChildTree(),
      //   this.getServerBoothSetting(),
      // ]);
    },
    // mounted () {
    //   // window.app = this;
    //   if(extend.data) {
    //     setTimeout(() => {
    //       this.openDetails('edit', extend.data)
    //     }, 0);
    //   }
    // },
    methods: {
      error(msg='', title='', throws = true) {
        msg = msg || '请求失败';
        const p = this.$alert(msg, title || '提示', {
          type: 'warning',
        })
        window.loading && window.loading.close();
        if(throws) throw new Error(msg);
        return p;
      },
      selectedProjects(rows) {
        if(rows && rows.length) {
          this.selectedProject({projectId: rows[0].id,projectName: rows[0].text});
        }
      },
      selectedProject({projectId,projectName}) {
        this.projectId = projectId;
        this.projectName = projectName;
        this.mainProjectId = projectId
        this.contractProjectId = projectId
        this.associatedProjectList = [{id: projectId,text: projectName}];
        this.$refs['body'].init()
      },
      async init({projectId,projectName}) { // 等待回调再初始化
        if(projectId) {
          this.selectedProject({projectId,projectName});
        }
        if(extend.data) {
          setTimeout(() => {
            this.openDetails('edit', extend.data)
          }, 0);
        }
        // // 自身init
        // await Promise.all([
        //   this.getGroupFlowSetting(),
        //   // this.getParentAndChildTree(),
        //   this.getServerBoothSetting(),
        // ]);
      },
      fmtSaleState(value) {
        return (this.saleStates.find(it => it.saleState == value) || {}).stateName || ''
      },
      async getColors() {
        const {data} = await Axios.post('/salestateColor/getList',)
        .catch(e => this.$errorMsg('查询展位状态信息失败！'))
        if (data.state !== 1) return this.$errorMsg(data.msg || data.message || '查询展位状态信息失败！')
        this.saleStates = data.data || [];
      },
      // async getFieldSetting() { // 字段设置
      //   const {data} = await Axios
      //   .post('/exhibition/selectExhibitSetting', JSON2FormData({teamId: top.teamId || -1}))
      //   .catch(_ => this.error())
      //   if (data.state !== 1) this.error(data.msg);
      //   this.fieldSetting = data.data || []
      // },
      async getServerBoothSettingUnion() { // 字段设置
        const { data } = await Axios
          .post('/sysSettingOrg/queryShareBoothSet', JSON2FormData({
            queryType: 'serveBooth',
          })).catch(_ => this.error())
        if(data.state !==1) this.error('查询参展记录设置失败 !');
         this.isUnionEnable = !!(data || {}).data;
      },
      async getServerBoothSetting() { // 字段设置
        const { data } = await Axios

        .post('/exhibition/queryExhibitSetting', JSON2FormData({
        // .post('/exhibition/selectExhibitSetting', JSON2FormData({
          // teamId: url.get('teamId') || top.teamId,

          teamId: url.get('teamId') || top.teamId,
          queryParam: 'enableBoothDemand',
          taskKindCode: 'fo_serve_booth_field_set',
        }))
        .catch(_ => this.error())

        // this.fieldSetting = data || []
        if(data.state !==1) this.error('查询参展记录设置失败 !');
        this.isEnableBoothDemand = !!(data && data.data && data.data[0].value == '1');
      },
      async getGroupFlowSetting(){ // 已废弃
        this.isFullProcess = true;//
        // const { data: projectFlowData } = await Axios.post('/sysSettingOrg/selectProjectFlow')
        // this.isFullProcess = 'fullProcess' === projectFlowData.data
      },
      searchCq(data, isFirst) {
        // 组合查询无项目 : 清空普通查询的表单
        if(!data.requestData || !data.requestData.moreProjectIds) {
          this.$refs['header'] && this.$refs['header'].clearAll()
        }
        return this.$refs['body'].search(data, isFirst)
      },
      search(data, isFirst) {
        return this.$refs['body'].search(data, isFirst)
      },
      async initPass() { // 初始化密码
        // const ids = this.tableSelection.map(v => v.zzyUserId).filter(it=> it)
        const ids = this.tableSelection.map(v => v.serveBoothId).filter(it=> it)
        let msg = '您未选择数据，是否需要操作全部数据？'
        let params = {
          // zzyUserIds: ids.join(),
          serveBoothIds: ids.join(),
          operEntrance:'展商参展管理'
        };
        if(this.tableSelection && this.tableSelection.length) {
          if(ids.length < 1) this.error('请先生成会员账号，在进行初始化密码!');
          msg = '即将初始化' + ids.length + '个会员账号的密码, 是否确认 ?'
        } else {
          params = this.$refs['body'].requestHistory;
          delete params.page
          delete params.rows
          params.operEntrance = '展商参展管理'
        }
        await this.$confirm(msg,'提醒',{
          type: 'warning',
        })
        window.loading = this.$loading({text: '请稍后...'})
        const {data} = await Axios
          .post('/serveBooth/batchInitZzyUserPassWord', JSON2FormData(params))
          .catch(_ => this.error())
        if(data.state !==1) this.error(data.msg || data.message || '操作失败!');
        window.loading && window.loading.close();
        this.$message({
          message: data.msg || data.message || '操作成功!',
          type: 'success'
        });
        return this.$refs['body'].refresh()
      },
      async boothSureBatch() { // 批量确认展位
        let params = {
          serveBoothIds: this.tableSelection.map(it => it.serveBoothId).join()
        };
        let msg = '您未选择数据，是否需要操作全部数据？'
        if(this.tableSelection && this.tableSelection.length) {
          msg = '当前选择的记录数为: ' + this.tableSelection.length + ', 是否对参展记录中展位进行展位确认 ?'
        } else {
          params = this.$refs['body'].requestHistory;
          delete params.page
          delete params.rows
        }
        await this.$confirm(msg,'提醒',{
          type: 'warning',
        })
        const {data} = await Axios
          .post('/serveBooth/batchConfirmBooth', JSON2FormData(params))
          .catch(_ => this.error())
        if(data.state !==1) this.error(data.msg || data.message || '操作失败!','提示');
        const okMsg = '确认展位成功: ' + data.data.successNum + (data.data.failNum ?  (', 失败: '+ data.data.failNum) : '');
        this.$message({
          message: okMsg || data.msg || data.message || '操作成功!',
          type: 'success'
        });
        return this.$refs['body'].refresh()
      },
      boothSure(ops) {
        ops.isList = true;
        this.$refs.boothSure && this.$refs.boothSure.open(ops)
      },
      exportFmtRow(item, prop) {
        let v = item[prop]
        if(prop == 'boothConfirmed'){
          v = v ? '确认参展' : ''
        }else if(prop == 'fisServe'){
          v = v ? '已启动服务' : '未启动服务'
        }else if(prop === 'boothDemandConfirm'){
          v = v===null ? '': (v ? '已确认展位需求' : '存在展位需求')
        }else if(prop === 'serveBoothSaleState'){
          v = v==9 ? '已移交客服' : ''
        }else if(prop == 'boothPreOrderFlag'){
          v = v? (item.boothPreOrderList || []).map(it=> {
            return `${ (it.preOrderTime && it.preOrderTime.length>10)  ? it.preOrderTime.substr(0,10) : (it.preOrderTime || '') } 订单生效
            `
          }).join('<br />'): '';
        }else if(prop == 'saleOrderFlag'){       // 销售订单
          v = v ? (item.saleOrderList || []).map(it=> {
            return `${ (it.signDate && it.signDate.length>10)  ? it.signDate.substr(0,10) : (it.signDate || '') }
             ${ it.fIsCheck ? '' : '未'}审核`;
          }) : '';
        }else if(prop == 'boothInfoDtlFlag'){    // 展位信息
          v = v ? (item.boothInfoDtlList || []).map(it=> {
            return `${it.boothCode } 规格${it.boothSpec } ${this.fmtSaleState(it.saleState)}`;
          }).join('<br />') : '';
        }else if(prop == 'traderRegFlag'){       // 参展申请
          v= v ? (item.traderRegBaseVos || []).map(it=> {
            return it.boothArea ? ('展位需求面积' + it.boothArea + '㎡') : ' '
          }).join('<br />') : '';
        }else if(prop == 'boothReleaseTimeFlag'){ // 释放时间
          v = v? (item.boothReleaseTimeList || []).map(it=> {
            return it.boothReleaseTime ? (it.boothReleaseTime + '释放') : ' '
          }).join('<br />'): '';
        }else if(prop == 'contractFlag'){
          v = v? (item.contractList || []).map(it=> {
            return `${ (it.paymentDate && it.paymentDate.length>10)  ? it.paymentDate.substr(0,10) : (it.paymentDate || '') } ${ it.fIsChief ? '(主) ' : '' }合同${ it.paymentFlag ? '已' : '未' }生效
            `
          }).join('<br />'): '';
        }else if(prop == 'serveBoothSaleState'){
          v = (v && v==9) ? '已移交客服' : ''
        }else if(prop == 'fnReceiptFlag'){
          v = v? (item.fnReceiptList || []).map(it=> {
            return `${(it.bookDate && it.bookDate.length>10)  ? it.bookDate.substr(0,10) : (it.bookDate || '')} 到款
            `
          }).join('<br />'): '';
        }else if(('cataTaskState,certTaskState,fileTaskState,boothTaskState,personTaskState'
          +',writeInstructionsTaskState,transTaskState,inviteTaskState,visaTaskState').includes(prop)){
          // v = this.taskDescs[item[v.substr(0, v.length-5) + 'List']] || ''
          v = v ? (item[prop.substr(0, prop.length-5) + 'List'] || []).map(it=> {
            return it.state ===0 ? '已中止' : (this.taskDescs[it.confirm] || '')
          }).join('<br />') : '';
        }else if(this.toFixedFields.includes(prop)){
          v = this.$toFixed2(v)
        }else if(typeof v === 'boolean'){
          v = v ? '是' : '否'
        }
        return v;
      },
      //导出
      async export_() {
        const $body = this.$refs['body']
        const $mainFields = $body.fields
        const propList = $mainFields.map(it => it.prop)
        const labelList = $mainFields.map(it => it.label)
        const tHead = ['序号', ...labelList]
        const exportList = this.tableSelection.map((item, index) => {
          let temp = {index: index + 1}
          propList.forEach(prop => {
            temp[prop] = this.exportFmtRow(item,prop)
          })
          return temp
        })
        if (exportList.length < 1) {
          await this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          this.exportAll()
          return;
        }
        exportObject2Excel({
          header: tHead,
          body: exportList,
          title: '展商参展记录',
          fileName: '展商参展记录',
          // bodyFormat:{
          //   amount: '0\\.00',
          //   oriAmount:'0\\.00',
          //   firstPayment: '0\\.00',
          //   amountForeign:'0\\.00',
          // }
        })
        this.$message.success('导出成功！')
      },
      exportAll() {
        this.exporting = true
        const $body = this.$refs['body']
        const $mainFields = $body.fields
        // const amounts = $body.amountRmb
        // const sumBoothArea = $body.sumBoothArea
        // const sumBoothCount = $body.sumBoothCount
        const propList = $mainFields.map(it => it.prop)
        const labelList = $mainFields.map(it => it.label)
        const tHead = ['序号', ...labelList]
        const data = {...$body.requestHistory, importList: true}
        delete data.rows
        delete data.page
        Axios.post('/serveBooth/getPage', JSON2FormData(data)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('导出数据失败！')
          if (!Array.isArray(res.rows) || res.rows.length === 0)
            return this.$errorMsg('没有可导出的数据！')
          const exportData = res.rows.map((item, _index) => {
            const res = {index: _index + 1}
            $mainFields.forEach((it, index) =>{
              res[it.prop] = this.exportFmtRow(item,it.prop);
            })
            return res
          })
          let lastSum = ['总记',  (document.getElementsByClassName('footer')[0].innerText || '').replaceAll('\n', '&nbsp;&nbsp;&nbsp;')]
          exportObject2Excel({
            header: tHead,
            body: exportData,
            lastSum,
            title: '展商参展记录',
            fileName: '展商参展记录-导出全部',
            // bodyFormat:{
            //   amount: '0\\.00',
            //   oriAmount:'0\\.00',
            //   firstPayment: '0\\.00',
            //   amountForeign:'0\\.00',
            // }
          })
          this.$message.success('导出成功！')
          this.exporting = false
        }).catch(e => {
          this.exporting = false
          console.error(e);
          this.$errorMsg('导出数据失败！')
        }).finally(() => this.exporting = false)
      },
      async merge() {
        // 合并参展记录
        const ids = this.tableSelection.map(v => (''+(v.serveBoothId || '')).trim()).join()
        if(!this.tableSelection || this.tableSelection.length < 1) this.error('请先选择要操作的数据 !');
        const cid = this.tableSelection[0].clientId || 0;
        const pid = this.tableSelection[0].projectId || '';
        if(this.tableSelection.find(it=> it.projectId!= pid || it.clientId!=cid)) this.error('请选择一条以上 同项目 同公司 参展记录进行合并 。');
        // await this.$confirm('是否确认移交客服？','提醒',{
        //   type: 'success',
        //   dangerouslyUseHTMLString: true,
        // })
        console.log(this.tableSelection);
        this.$refs['merge'].open(this.tableSelection);
      },
      async handCancel() {
        const ids = this.tableSelection.map(v => (''+(v.serveBoothId || '')).trim()).join()
        if(!this.tableSelection || this.tableSelection.length < 1) this.error('请先选择要操作的数据 !');
        await this.$confirm('是否确认取消移交客服？','提醒',{
          type: 'success',
          dangerouslyUseHTMLString: true,
        })
        const {data} = await Axios
        .post('/serveBooth/batchCancelCustomerService', JSON2FormData({
          handleType: 2,
          serveBoothIds: ids,
          operEntrance:"展商参展管理",
        }))
        .catch(_ => this.error())
        if(data.state !==1) this.error(data.msg || data.message || '取消移交失败!');
        this.$message({
          message: data.msg || data.message || '',
          // message: '移交成功' + (+data.data.successNum ||0) + '条数据' + (data.data.failNum ? (',移交失败'  + data.data.failNum + '条数据') : ''),
          type: 'success'
        });
        return this.$refs['body'].refresh()
      },
      async handOver() {
        const ids = this.tableSelection.map(v => (''+(v.serveBoothId || '')).trim()).join()
        if(!this.tableSelection || this.tableSelection.length < 1) this.error('请先选择要操作的数据 !');
        await this.$confirm('是否确认移交客服？','提醒',{
          type: 'success',
          dangerouslyUseHTMLString: true,
        })
        // if(this.tableSelection.find( v => v.serveBoothSaleState === 9)){
        //     this.$alert('不允许删除`已移交客服`状态的参展记录 !','提示')
        // } else {
          const {data} = await Axios
          .post('/serveBooth/batchHandOver', JSON2FormData({
            handleType: 2,
            serveBoothIds: ids,
            workTeamId:url.get('teamId') || top.teamId,
            operEntrance:'展商参展管理'
          }))
          .catch(_ => this.error());
          if(data.state !==1) this.error(data.msg || data.message || '移交失败!');
          this.$message({
            message: data.msg || data.message || '',
            // message: '移交成功' + (+data.data.successNum ||0) + '条数据' + (data.data.failNum ? (',移交失败'  + data.data.failNum + '条数据') : ''),
            type: 'success'
          });
          return this.$refs['body'].refresh()
        // }
      },
      delBatch() {
        const list = this.tableSelection.map(v => ({serveBoothId: v.serveBoothId,operEntrance:'展商参展管理'}))
        // if(this.tableSelection.find( v => v.serveBoothSaleState === 9)){
        //     this.$alert('不允许删除`已移交客服`状态的参展记录 !','提示')
        // } else {
          const url = '/serveBooth/deleteBatch'
          return this.$refs['body'].delBatch(list, url)
        // }
      },
      tableSelect(rows) {
        this.$set(this, 'tableSelection', rows)
      },
      //组合查询
      combinedQuery(para){
        this.$refs['cQuery'].open(JSON.parse(JSON.stringify(para ||{})))
      },
      //清空检索条件
      clearSearch() {
        this.$refs['header'].clearAll()
        this.$refs['cQuery'].formReset()
      },
      openContractDetails(mode, row){
        const {contractId,companyName,projectName,projectId,contractProjectId,
          clientId,linkman,linkmanId,linkmanPosition,linkmanEmail,linkmanMobile,
        } = row
        // 前往合同详情
        const extend = btoa(encodeURIComponent(JSON.stringify({
          mode,
          contractId,
          clientName: companyName || '',
          projectId: contractProjectId,projectName,
          clientId,
          linkmanClientId: linkmanId,
          linkmanPersonName: linkman || '',
          linkmanPosition: linkmanPosition || '',
          linkmanEmail: linkmanEmail || '',
          mobile: linkmanMobile || '',
        })))
        this.openTab('ProjectMarket/sales-contract.html?teamId=' + top.teamId +
          '&page=contract' +
          '&extend=' + extend,'销售合同单-' + projectName + '-'+ companyName)
        // this.$refs['contractDetails'] && this.$refs['contractDetails'].open(mode, contractId)
      },
      openTab(url,title) {
        const tab = top.tabs.getTabByUrl(url)
        if(tab) {
          tab.activate();tab.refresh();
        } else {
          FinalLinkOpen(url, null, true, false, title)
        }
      },
      openDetails(mode, row) {
        this.$refs['details'] && this.$refs['details'].open(mode, row || {})
      },
      selectComplete(ids) {
        this.$refs['details'].setRuleIds(ids)
      },
      operationComplete(event){
        const $body = this.$refs['body']
        if (event && typeof event === 'object') {
          $body.search(event)
        } else {
          $body.requestTableData($body.requestHistory)
        }
      },
      changeVip(e) {
        if(e) {
          this.$refs.chVip && this.$refs.chVip.open(e);
        } else {
          const ids = this.tableSelection.map(v => (''+(v.serveBoothId || '')).trim());// .join()
          if(!this.tableSelection || this.tableSelection.length < 1) this.error('请先选择要操作的数据 !');
          this.$refs.chVip && this.$refs.chVip.open2(ids);
        }
      },
      async getProjectTasks() {
        // eip-web-sponsor/task/getTaskList
        // projectId: 1837
        // exhibitCode: E0000000294
        // taskKindCode:
        return [];
      },
      async enableWorkFlowSure({serveBoothIds,taskDtlDtoList}) {
        // 选择完了任务
        // "taskDtlDtoList":[//启动的任务服务集合
        //   {  -- 正常的启动任务
        //     "taskId":8676,
        //     "boothFocusIds":null,
        //     "operatorType":null,
        //     "taskDtlId":null,
        //     "taskDtlIds":null
        //   }
        // ]
        let para = {
          taskDtlDtoList
        };
        let paraSearch = JSON.parse(JSON.stringify(this.$refs['body'].requestHistory ||{}));
        let tmpPara = {};
        Object.keys(paraSearch).map(it => {
          let tmp = it.split('.');
          if(tmp.length == 2) {
            if(!tmpPara[tmp[0]]) tmpPara[tmp[0]] = {}
            tmpPara[tmp[0]][tmp[1]] = paraSearch[it];
            delete paraSearch[it]
          }
        })
        if(serveBoothIds) {
          para.serveBoothIds = serveBoothIds ||'',
          para.operEntrance = '展商参展管理'
        } else {
          delete paraSearch.page;
          delete paraSearch.rows;

          para = {
            ...para,
            ...paraSearch,
            ...tmpPara,
            operEntrance:'展商参展管理'
          }
        }
        const {data} = await Axios
        .post('/serveBooth/generateTaskDetailBatch', para)
        .catch(_ => this.error())
        if(data.state !==1) this.error(data.msg || data.message || '启动服务流程失败!');
        window.loading && window.loading.close();
        this.$refs['selectTask'].close();
        if(data.data) {
          const tmp = data.data || [];
          const tmpK = Object.keys(tmp);
          if(tmpK && tmpK.length) {
            this.$message({
              message: '<div>以下公司未移交客服。 参展记录未能启动服务流程 。 <div>' +  tmpK.map(k => '<div style="padding: 8px 0;line-height: 1.2;">' + k + ' ：' + tmp[k].replaceAll(',',' , ') + '<div>').join(''),
              dangerouslyUseHTMLString: true,
              duration: 10000,
              showClose: true,
              type: 'error'
            });
          } else {
            this.$message.success('操作成功');
          }
        } else {
          this.$message.success('操作成功');
        }
        return this.$refs['body'].refresh()
      },
      async enableWorkFlow() {
        const ids = (this.tableSelection || []).map(v => v.serveBoothId);
        // if (ids.length < 1) this.error('请先选择要操作的数据!');
        // const pidTmp = this.tableSelection[0].projectId;
        // const pids = this.tableSelection.filter(v => v.projectId !== pidTmp);
        // if (pids.length > 0) this.error('仅允许单项目批量启用服务流程!');
        let para = JSON.parse(JSON.stringify(this.$refs['body'].requestHistory));
        if(ids.length <1) {
          delete para.page;
          delete para.rows;
        } else {
          para = {};
        }
        this.$refs['selectTask'].open({
          serveBoothIds: ids,
          ...para
        }, true);
        // await this.$confirm('是否确认启动服务流程 (不含未确认参展) ？', '提示', {type: 'info'});
        // await this.enableWorkFlowSure(ids);
        // // TODO 查询项目启动的任务
        // // this.error('TODO 请先选择要启动的任务!')
      },
      async createVip() {
        try {
          const list = this.tableSelection.map(v => v.serveBoothId)
          if (list.length > 0) return this.$refs['cVip'].open(list.join())
          await this.$confirm('您未选择数据，是否需要操作全部数据？', '提示', {type: 'info'})
          this.$refs['cVip'].open(this.$refs['body'].requestHistory)
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      async createVipCorp() {
        try {
          const list = this.tableSelection.map(v => v.serveBoothId)
          if (list.length > 0) return this.$refs['cVipCorp'].open(list.join())
          await this.$confirm('您未选择数据，是否需要操作全部数据？', '提示', {type: 'info'})
          this.$refs['cVipCorp'].open(this.$refs['body'].requestHistory)
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      openSpecialRuleWithIds(ids) {
        // console.log('openSpR',ids)
        // const list = this.tableSelection.map(v => v.serveBoothId)
        return this.$refs['spRule'].openIds(ids)
      },
      async specialRule() {
        try {
          const boothIds = this.tableSelection.map(v => v.serveBoothId).join(",")
          if (boothIds) return this.$refs['spRule'].open(boothIds)
          this.$alert('请先选择要操作的数据 !','提示')
          // await this.$confirm('您未选择数据，是否需要操作全部数据？', '提示', {type: 'info'})
          // this.$refs['spRule'].open(this.$refs['body'].requestHistory)
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      },
      onRemind(){
        this.$refs.remind && this.$refs.remind.open(...arguments)
      },
      async getParentAndChildTree() {
        const {data: {state, data}} = await Axios.post('/project/getParentAndChildTree', JSON2FormData({projectId: extend.projectId}))
        if (state !== 1) await Promise.reject()
        const deep = value => {
          value.forEach(item => {
            const {id, text} = item
            this.associatedProjectList.push({id, text})
            if ((item.children || []).length === 0) return
            deep(item.children)
          })
        }
        deep([data])
      },
      import_(){
        // const $body = this.$refs['body']
        // let projectId = +getQueryString('mainProjectId')
        // if ($body && $body.requestHistory.contractProjectId)
        //   projectId = $body.requestHistory.contractProjectId
        // const initValue = {projectId}
        // this.$refs.import && this.$refs.import.open(initValue, this.associatedProjectList)
        const pinfo = (this.$refs['header'].projectSelect.data || [])[0] || '';
        if(pinfo) {
          this.$refs.import && this.$refs.import.open({projectId: pinfo.projectId}, [{
            id: pinfo.projectId,
            text: pinfo.projectName,
          }])
        } else {
          this.$refs.import && this.$refs.import.open()
        }
      }
    },
    provide() {
      return {
        panel: this,
        exhibitCode: getQueryString('exhibitCode'),
        orgnum: getCookie("orgNum"),
        projectIdComputed: () => this.projectId,
        // projectIdComputed: () => extend.projectId,
        projectNameComputed: () => this.projectName,
        //
        // contractProjectId: extend.projectId,
        contractProjectIdComputed: () => this.contractProjectId,
        // projectId: extend.projectId,
        // mainProjectId: getQueryString('mainProjectId'),
        // isMainProject: Number(extend.projectId) === Number(getQueryString('mainProjectId')), // 已废弃
        projectList: () => this.associatedProjectList,
        mainProjectIdComputed: () => this.mainProjectId,
        isFullProcessComputed: () => this.isFullProcess,         // 已废弃
        //
        exhibitCodeComputed: () => this.exhibitCode,
        isEnableBoothDemandComputed: () => this.isEnableBoothDemand,
        saleStateList: [ // 简化流程的订单状态
          {id: -1, text: '未预定'},
          {id: 1, text: '已预定'},
          {id: 2, text: '已签合同'},
          {id: 3, text: '订单完成'},
          {id: 0, text: '订单取消'},
        ],
        preorderAndContractStateList: [ // 全流程的订单状态 (销售状态, 展位状态)
          // {id: 1, text: '未预定'},
          // {id: 2, text: '订单生效'},
          // {id: 3, text: '已签合同'},
          // {id: 4, text: '合同生效'},
          {id: 1, text: '已预订'},
          // {id: 2, text: '已签合同'},
          {id: 2, text: '合同未生效'},
          {id: 3, text: '合同已生效'},
          {id: 4, text: '已到款'},
        ],
        serveBoothSaleStateList: [
          {id: 9, text: '已移交客服'},
          {id: 0, text: '未移交客服'},
        ]
      }
    },
    components: {
      ExhibitorManageHeader,
      ExhibitorManageMainBody,
      ExhibitorManageContract,
      // ExhibitorManageContractFull,
      // ContractDetails,
      ExhibitorManageReceipt,
      ExhibitorManageChangePass,
      ExhibitorManageCreateVip,
      ExhibitorManageCreateVipCorp,
      ExhibitorManageChangeVip,
      ExhibitorManageSpecialRule,
      ExhibitorManageSetOwner,
      ExhibitorManageDetails,
      ExhibitorManageCombinedQuery,
      RemindBox,
      ContractImport,
      ImportBatchList,
      ExhibitorManageOrderTip,
      ExhibitorManageMerge,
      ExhibitorManageContractTip,
      BoothSure,
      TaskSelectorSlim,
    }
  }
</script>
