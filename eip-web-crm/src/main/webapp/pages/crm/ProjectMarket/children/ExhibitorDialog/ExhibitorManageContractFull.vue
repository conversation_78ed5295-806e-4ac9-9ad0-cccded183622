<!--此文件已作废-->
<template>
  <custom-dialog
      :show="show"
      @before-close="close"
      @opened="autoFullClient() && setFieldRules()"
      @closed="dialogClosed"
      :title="title"
      width="1035px"
      top="8vh"
      v-loading.fullscreen.lock="innerLoading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)"
  >
    <el-main slot="body" class="dialog-body" ref="body" v-autoscroll>
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left" v-if="selfAuthority.showHeaderLeft">
          <el-button @click="contractCheck()" size="mini" v-if="selfAuthority.contractCheck"
                     :class="{is: isChecked}">{{checkedText}}
          </el-button>
          <span :class="{is: isChecked}">{{isChecked ? '已' : '未'}}审核</span>
          <el-button @click="contractPayment" size="mini" v-if="selfAuthority.contractEffect"
                     :class="{is: isPayment}">{{paymentText}}
          </el-button>
          <span :class="{is: isPayment}">{{isPayment ? '已' : '未'}}生效</span>
          <template v-if="selfAuthority.showInitialPayment">
            <!--<el-button @click="initialPayment()" size="mini" v-if="selfAuthority.contractEffect"
                       :class="{is: paymentFlag}">{{initialPaymentText}}
            </el-button>-->
            <i class="el-icon-success" v-if="paymentFlag" style="color: #67C23A"></i>
            <i class="el-icon-warning" v-else style="color: #909399"></i>
            <span style="color: #333;padding-left: 0;">{{paymentFlag ? '已' : '未'}}到款</span>
          </template>
          <el-dropdown
              v-if="selfAuthority.contractInvalid && selfAuthority.allowInvalid"
              placement="bottom-start"
              trigger="click"
              @command="evalContractCmd">
            <i class="el-icon-more-outline" style="font-size: 20px;vertical-align: middle" title="合同作废"></i>
            <el-dropdown-menu slot="dropdown" class="contract-invalid">
              <el-dropdown-item command="invalid">合同作废</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div v-else class="dialog-header-left">
          <template v-if="selfAuthority.isInvalid">
            <el-button size="mini">合同已作废</el-button>
            <span class="is">{{contractForm.stateTime || ''}} 已作废</span>
            <el-dropdown
                v-if="selfAuthority.contractInvalid"
                placement="bottom-start"
                trigger="click"
                @command="evalContractCmd">
              <i class="el-icon-more-outline" style="font-size: 20px;vertical-align: middle" title="取消作废"></i>
              <el-dropdown-menu slot="dropdown" class="contract-invalid">
                <el-dropdown-item command="unInvalid">取消作废</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </div>
        <div class="dialog-header-right" v-if="!selfAuthority.isInvalid">
          <el-button
              v-if="selfAuthority.noSave && selfAuthority.exportPdf"
              size="mini"
              icon="el-icon-download"
              type="primary"
              plain
              @click="export_pdf()">
            导出PDF
          </el-button>
          <el-button
              :disabled="selfAuthority.noSave"
              size="mini"
              icon="el-icon-circle-check"
              type="primary"
              :loading="saveLoading"
              @click="saveContract()">
            保存{{saveLoading ? '中...': ''}}
          </el-button>
        </div>
        <div v-else></div>
      </el-header>
      <el-form class="contractForm"
               :rules="rules"
               ref="contractForm"
               :disabled="selfAuthority.noSave"
               :model="contractForm"
               v-loading="loadingField"
               element-loading-text="正在加载字段..."
               element-loading-spinner="el-icon-loading"
               element-loading-background="rgba(255, 255, 255, 0.5)"
               label-width="110px"
               size="mini">
        <el-collapse v-model="activeNames" ref="tFormWrapper">
          <el-collapse-item title="基本信息" :name="1">
            <el-form-item label="合同序号" prop="contractId">
              <el-input v-model="contractForm.contractId" disabled placeholder="合同序号由系统自动生成"></el-input>
            </el-form-item>
            <el-form-item label="合同编号" prop="contractCode">
              <el-input
                  v-model="contractForm.contractCode"
                  :disabled="isAutoGenContractCode"
                  clearable
                  :placeholder="'合同编号' + (isAutoGenContractCode ?　'由系统自动生成' : '')"></el-input>
            </el-form-item>
            <el-form-item label="是主合同" prop="isChief">
              <el-select
                  v-model="contractForm.isChief"
                  placeholder="请选择是否是主合同"
                  clearable
              >
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item></el-form-item><!--占位的-->
            <el-form-item label="签订日期" prop="signDate">
              <el-date-picker
                  placeholder="请选择签订日期"
                  type="date"
                  value-format="yyyy-MM-dd"
                  clearable
                  v-model="contractForm.signDate">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="合同生效时间" prop="paymentData">
              <el-date-picker
                  ref="paymentData"
                  :disabled="selfAuthority.disabledPayment"
                  placeholder="合同生效时间"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  v-model="contractForm.paymentData">
              </el-date-picker>
            </el-form-item>
            <el-form-item ref="company" label="公司名称" prop="clientName">
              <el-input
                  class="project"
                  v-model="contractForm.clientName"
                  readonly
                  :disabled="selfAuthority.readonly || !selfAuthority.updateCidAndPid"
                  @click.native="selfAuthority.allowSelection && (showCompanySelect = true)"
                  placeholder="选择公司名称"
              >
                <i slot="suffix"
                   class="el-input__icon el-icon-delete"
                   @click.stop="selfAuthority.allowSelection && clear('clientId','clientName')"></i>
              </el-input>
              <input type="hidden" v-model="contractForm.clientId">
            </el-form-item>
            <el-form-item ref="project" label="项目名称" prop="projectName">
              <el-input
                  class="project"
                  v-model="contractForm.projectName"
                  readonly
                  :disabled="selfAuthority.readonly || !selfAuthority.updateCidAndPid"
                  @click.native="selfAuthority.allowSelection && (showProjectSelect = true)"
                  placeholder="选择项目名称"
              >
                <i slot="suffix"
                   class="el-input__icon el-icon-delete"
                   @click.stop="selfAuthority.allowSelection && clear('projectId','projectName')">
                </i>
              </el-input>
              <input type="hidden" v-model="contractForm.projectId">
            </el-form-item>
            <el-form-item label="招展类型" prop="attractType">
              <el-select
                  v-model="contractForm.attractType"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  placeholder="招展类型"
              >
                <el-option v-for="at in attractType" :label="at.text" :value="at.id" :key="at.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="客户关系" prop="clientRelations">
              <el-select
                  v-model="contractForm.clientRelations"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  placeholder="客户关系"
              >
                <el-option v-for="cr in clientRelations" :label="cr.text" :value="cr.id" :key="cr.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="采购区域分布" prop="purchaserArea" style="flex-basis: 100%">
              <el-input v-model="contractForm.purchaserArea" clearable placeholder="采购区域分布"></el-input>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :name="2">
            <template slot="title">
              联系人信息　<span class="title-info">
              联系人：{{contractForm.linkmanPersonName}}　　
              手机：{{contractForm.mobile}}　　
              电话：{{contractForm.linkmanTel}}　　
            </span>
            </template>
            <el-form-item label="联系人" prop="linkmanPersonName">
              <el-select
                  v-model="contractForm.linkmanClientId"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  @change="linkmanFullData"
                  placeholder="联系人"
              >
                <el-option
                    v-for="linkman in linkmanList"
                    :label="linkman.personName"
                    :value="linkman.clientId"
                    :key="linkman.clientId">
                </el-option>
              </el-select>
              <input type="hidden" v-model="contractForm.linkmanPersonName">
            </el-form-item>
            <el-form-item label="职务" prop="linkmanPosition">
              <el-input v-model="contractForm.linkmanPosition" maxlength="50" clearable placeholder="职务"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="linkmanEmail">
              <el-input v-model="contractForm.linkmanEmail" maxlength="200" clearable placeholder="邮箱"></el-input>
            </el-form-item>
            <el-form-item label="电话" prop="linkmanTel">
              <el-input v-model="contractForm.linkmanTel" maxlength="100" clearable placeholder="电话"></el-input>
            </el-form-item>
            <el-form-item label="网址" prop="linkmanWebsite">
              <el-input v-model="contractForm.linkmanWebsite" maxlength="255" clearable placeholder="网址"></el-input>
            </el-form-item>
            <el-form-item label="地址" prop="linkmanAddress">
              <el-input v-model="contractForm.linkmanAddress" maxlength="200" clearable placeholder="地址"></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="contractForm.mobile" maxlength="100" clearable placeholder="手机号"></el-input>
            </el-form-item>
            <el-form-item label="传真" prop="linkmanFax">
              <el-input v-model="contractForm.linkmanFax" maxlength="50" clearable placeholder="传真"></el-input>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item :name="3">
            <template slot="title">
              展位信息　　<span class="title-info">
              展位号：{{contractForm.boothNum}}
            </span>
            </template>
            <el-form-item label="展馆展区" prop="section">
              <el-input v-model="contractForm.section" clearable placeholder="展馆展区"></el-input>
            </el-form-item>
            <el-form-item label="展位号" prop="boothNum">
              <el-input v-model="contractForm.boothNum" clearable placeholder="展位号"></el-input>
            </el-form-item>
            <el-form-item label="展品名称" prop="exhibitsName">
              <el-input v-model="contractForm.exhibitsName" clearable placeholder="展品名称"></el-input>
            </el-form-item>
            <el-form-item label="展位类型" prop="boothType">
              <el-input v-model="contractForm.boothType" clearable placeholder="展位类型"></el-input>
            </el-form-item>
            <el-form-item label="展位面积" prop="boothArea">
              <el-input v-model="contractForm.boothArea" clearable placeholder="展位面积（单位: m²）"></el-input>
            </el-form-item>
            <el-form-item label="展位数量" prop="boothCount">
              <el-input v-model="contractForm.boothCount" clearable placeholder="展位数量"></el-input>
            </el-form-item>
            <el-form-item label="展位规格" prop="boothSpec">
              <el-input v-model="contractForm.boothSpec" clearable placeholder="展位规格"></el-input>
            </el-form-item>
            <el-form-item label="开口情况" prop="openInfo">
              <el-input v-model="contractForm.openInfo" clearable placeholder="开口情况"></el-input>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item title="产品明细" :name="4" class="p10">
            <div class="top-button">
              <el-link
                  :underline="false"
                  :disabled="selfAuthority.noSave"
                  icon="el-icon-circle-plus-outline"
                  size="mini"
                  @click="addProductDetails">新增
              </el-link>
              <el-link
                  :underline="false"
                  :disabled="selfAuthority.noSave"
                  icon="el-icon-delete"
                  size="mini"
                  @click="deleteProductDetails">删除
              </el-link>
            </div>
            <el-table
                class="contract-details"
                border
                @row-click="rowClick(...arguments)"
                ref="contractDetails"
                size="small"
                :data="contractForm.contractDtlList"
                style="width: 100%">
              <el-table-column
                  width="40"
                  type="index">
              </el-table-column>
              <el-table-column
                  width="40"
                  type="selection">
              </el-table-column>
              <el-table-column
                  prop="prodCode"
                  label="产品编号">
              </el-table-column>
              <el-table-column
                  prop="prodName"
                  label="产品名称">
              </el-table-column>
              <el-table-column
                  prop="qty"
                  label="数量">
                <template slot-scope="{row, $index}">
                  <el-input-number :controls="false" v-model="row.qty" @change="amountModify($index)" size="mini"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                  prop="prodSpec"
                  label="产品规格">
                <template slot-scope="{row, $index}">
                  <el-input v-model="row.prodSpec" size="mini"></el-input>
                </template>
              </el-table-column>
              <el-table-column
                  prop="prodPrice"
                  label="标准报价">
              </el-table-column>
              <el-table-column
                  prop="discount"
                  label="折扣%">
                <template slot-scope="{row, $index}">
                  <el-input-number
                      :controls="false"
                      :disabled="!row['prodPrice']"
                      v-model="row.discount"
                      @change="amountModify($index, 'discount')"
                      size="mini">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                  prop="price"
                  label="单价">
                <template slot-scope="{row, $index}">
                  <el-input-number :controls="false" class="money-style" :precision="2" v-model="row.price" @change="amountModify($index, 'price')" size="mini"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                  prop="curencyCode"
                  label="币种">
                <template slot-scope="{row, $index}">
                  <el-select v-model="row.curencyCode" filterable default-first-option @change="amountModify($index, 'null')" size="mini">
                    <el-option
                        v-for="currencyForeign in currencyForeignList"
                        :key="currencyForeign.currencyForeign"
                        :label="currencyForeign.currencyForeignName"
                        :value="currencyForeign.currencyForeign"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                  prop="amount"
                  label="金额">
                <template slot-scope="{row, $index}">
                  <el-input-number :controls="false" class="money-style" :precision="2" v-model="row.amount" @change="amountModify($index, 'amount')" size="mini"></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item title="其他信息" :name="5">
            <el-form-item label="人民币总额" prop="amount">
              <el-input-number disabled :controls="false" class="money-style" :precision="2" v-model="contractForm.amount" clearable placeholder="人民币总额"></el-input-number>
            </el-form-item>
            <el-form-item label="原价总额" prop="oriAmount">
              <el-input-number disabled :controls="false" class="money-style" :precision="2" v-model="contractForm.oriAmount" clearable placeholder="原价总额"></el-input-number>
            </el-form-item>
            <el-form-item label="外币总额" prop="amountForeign">
              <el-input readonly type="number" v-model.number="contractForm.amountForeign" clearable placeholder="外币总额"></el-input>
            </el-form-item>
            <el-form-item label="外币币种" prop="currencyForeign">
              <el-select disabled v-model="contractForm.currencyForeign" clearable placeholder="外币币种">
                <el-option
                    v-for="currencyForeign in currencyForeignList"
                    :key="currencyForeign.currencyForeign"
                    :label="currencyForeign.currencyForeignName"
                    :value="currencyForeign.currencyForeign"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="总折扣%" prop="totalDiscount">
              <el-input readonly type="number" v-model.number="contractForm.totalDiscount" clearable placeholder="总折扣"></el-input>
            </el-form-item>
          <!--  <el-form-item label="首付款金额" prop="firstPayment">
              <el-input-number :controls="false" :precision="2" v-model="contractForm.firstPayment" clearable placeholder="首付款金额"></el-input-number>
            </el-form-item>
            <el-form-item label="付款期限" prop="payDateLimit">
              <el-date-picker
                  placeholder="请选择付款期限"
                  type="date"
                  value-format="yyyy-MM-dd"
                  clearable
                  v-model="contractForm.payDateLimit">
              </el-date-picker>
            </el-form-item>-->
            <el-form-item label="输入员" prop="inputterId">
              <el-select v-model="contractForm.inputterId" filterable default-first-option clearable placeholder="输入员" disabled>
                <el-option
                    v-for="operator in operatorList"
                    :key="operator.id"
                    :label="operator.text"
                    :value="operator.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="输入时间" prop="inputTime">
              <el-date-picker
                  :disabled="true"
                  placeholder="请选择输入时间"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  v-model="contractForm.inputTime">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="业绩归属人" prop="ownerId">
              <el-select v-model="contractForm.ownerId" filterable default-first-option clearable placeholder="业绩归属人">
                <el-option
                    v-for="operator in operatorList"
                    :key="operator.id"
                    :label="operator.text"
                    :value="operator.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="业绩归属团队" prop="ownTeamName">
              <el-input
                  class="project"
                  v-model="contractForm.ownTeamName"
                  readonly
                  :disabled="selfAuthority.disableOwnTeamName"
                  @click.native="openSelectTeam"
                  placeholder="选择业绩归属团队"
              >
                <i slot="suffix"
                   class="el-input__icon el-icon-delete"
                   @click.stop="!selfAuthority.disableOwnTeamName && clear('ownTeamId','ownTeamName')"></i>
              </el-input>
              <input type="hidden" v-model="contractForm.ownTeamId">
            </el-form-item>
            <el-form-item label="备注" style="flex-basis: 100%">
              <el-input type="textarea"
                        resize="none"
                        maxlength="500"
                        show-word-limit
                        :autosize="{minRows: 2, maxRows: 6}"
                        v-model="contractForm.memo"></el-input>
            </el-form-item>
            <el-form style="flex-basis: 100%" label-width="110px">
              <el-form-item label="附件上传" >
                <el-upload
                    :disabled="!(selfAuthority.contractModify || (mode === 'add' && selfAuthority.contractAdd))"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :on-success="uploadSuccess"
                    :before-remove="beforeRemove"
                    :file-list="fileList"
                    :action="uploadInfo.uploadAction"
                    :data="uploadInfo.uploadData"
                    ref="uploader"
                    list-type="text"
                    drag>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="分期付款计划" :name="6" class="p10">
            <div class="top-button">
              <el-link
                :underline="false"
                :disabled="selfAuthority.noSave"
                icon="el-icon-circle-plus-outline"
                size="mini"
                @click="addPayPlanDetails">新增
              </el-link>
              <el-link
                :underline="false"
                :disabled="selfAuthority.noSave"
                icon="el-icon-delete"
                size="mini"
                @click="deletePayPlanDetails">删除
              </el-link>
            </div>
            <el-table
              class="contract-details"
              border
              @row-click="rowClick(...arguments, 'payPlanDetails')"
              ref="payPlanDetails"
              size="small"
              :data="contractForm.contractPayPlanList"
              style="width: 100%">
              <el-table-column width="40" type="index"> </el-table-column>
              <el-table-column width="40" type="selection"></el-table-column>
              <el-table-column prop="planName" label="名称" width="85"></el-table-column>
              <el-table-column prop="periods" label="期次" width="70"></el-table-column>
              <el-table-column prop="planDate" label="计划时间" width="105">
                <template slot-scope="{row}">
                  <el-date-picker
                    type="date"
                    value-format="yyyy-MM-dd"
                    size="mini"
                    :clearable="false"
                    v-model="row.planDate">
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="付款金额" width="110">
                <template slot-scope="{row}">
                  <el-input-number :controls="false" class="money-style" :precision="2" v-model="row.amount" size="mini"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column prop="currencyCode" label="币种" width="80">
                <template slot-scope="{row}">
                  <el-select v-model="row.currencyCode" filterable default-first-option size="mini">
                    <el-option
                      v-for="currencyForeign in currencyForeignList"
                      :key="currencyForeign.currencyForeign"
                      :label="currencyForeign.currencyForeignName"
                      :value="currencyForeign.currencyForeign"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="合同生效后提醒">
                <template slot-scope="{row}">
                  <div class="effect-remind-wrap">
                    <el-checkbox v-model="row.needReminder">
                      <span style="margin-left: -4px">提前</span>
                      <el-input-number :min="0" :controls="false" style="width: 40px" v-model="row.aheadDays" size="mini"></el-input-number>
                      <span>天</span>
                    </el-checkbox>
                    <el-time-picker
                      v-model="row.remindTimeStr"
                      value-format="HH:mm:ss"
                      size="mini"
                      :clearable="false"
                      style="width: 90px;margin-left: 5px">
                    </el-time-picker>
                    <div class="effect-remind">
                      <span style="font-size: 14px;">提醒给</span>
                      <el-select
                        size="mini"
                        style="width: 190px"
                        v-model="row.remindeds"
                        filterable
                        :placeholder="' '"
                        multiple
                        clearable
                        collapse-tags
                        default-first-option>
                        <el-option
                          v-for="operator in operatorList"
                          :key="operator.id"
                          :label="operator.text"
                          :value="operator.id"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <project-select
          :show="showProjectSelect"
          @close="showProjectSelect=false"
          @finish="selectFinish('project', $event)"></project-select>
      <company-select
          :show="showCompanySelect"
          @close="showCompanySelect=false"
          @finish="selectFinish('company', $event)"></company-select>
      <product-details ref="productDetails" @finish="productDetailsFinish"></product-details>
      <contract-payment ref="ConPayDialog" @finished="contractPaymentFinish"></contract-payment>
      <contract-tpl ref="ContractTpl"></contract-tpl>
      <team-select ref="ts" title="选择业绩归属团队"></team-select>
    </el-main>
  </custom-dialog>
</template>

<script>
  const ContractPayment = importC('ContractPayment')
  const ContractTpl = importC('ContractTpl')
  const TeamSelect = importC('TeamSelect')
  export default {
    name: "ExhibitorManageContractFull",
    data() {
      const validateBoothArea = (rule, value, callback)=>{
        if(!value)return callback()
        if(isNaN(Number(value)))
          return callback(new Error('展位面积只能为数字!'))
        callback()
      }
      return {
        activeNames: [1, /*2, 3,*/ 4, 5],
        contractForm: {
          contractId: '',  //合同id
          projectId: '',  //项目id
          projectName: '', //项目名称
          clientId: '',  //客户id
          clientName: '',  //公司名称
          amount: '',  // 人民币总额
          inputTime: top.$GlobalDefine.getSystemTime(),//录入时间
          inputterId: Number(getCookie('operatorId')),  //录入人id
          ownerId: Number(getCookie('operatorId')),  // 业绩归属人id
          contractDtlList: [],
          contractPayPlanList: [],
          isChief: '',  //是否主合同  true/false
          signDate: '', //签订日期
          memo: '',  //备注
          oriAmount: '',  //原价总额
          totalDiscount: '', //总折扣
          firstPayment: '', // 首付款金额
          payDateLimit: '', //付款期限
          paymentData: '',  //合同生效时间
          amountForeign: '', //外币总额
          currencyForeign: '', // 外币币种代号
          boothNum: '',  //展位号
          linkmanClientId: '',
          linkmanPersonName: '', // 联系人姓名
          linkmanTel: '',  //  联系人电话
          linkmanFax: '',  // 联系人传真
          linkmanAddress: '', // 联系人地址
          linkmanEmail: '',  // 联系人电子邮箱
          linkmanWebsite: '',  // 联系人网址
          linkmanPosition: '', // 联系人职务
          mobile: '', // 手机
          exhibitsName: '',  // 展品名称
          section: '',  // 展馆
          boothSpec: '',  // 展位规格
          boothArea: '', //展位面积
          boothCount: '',  // 展位数量
          boothType: '', //展位类型名
          openInfo: '', // 开口情况
          contractCode: '',  //合同编号
          clientRelations: '', //客户关系 id
          attractType: '', //招展类型 id
          purchaserArea: '', //采购商区域分布
          ownTeamId: '', // 业绩归属团队 ID
          ownTeamName: '',// 业绩归属团队名称
          stateTime: '', // 合同作废时间
        },
        linkmanList: [],
        currencyForeignList: [],
        operatorList: [],
        isChecked: false,  //是否审核   true/false
        isPayment: false,  // 是否生效  true/false
        paymentFlag: false, // 是否到款  true/false
        isInvalid: false, // 是否作废  true/false
        fu_team_data_kywy: getAuthority('fu_team_data_kywy'),
        rules: {
          projectName: [{required: true, message: '必须选择项目名称', trigger: 'blur'}],
          clientName: [{required: true, message: '必须选择公司名称', trigger: 'blur'}],
          boothArea: [{ validator: validateBoothArea, trigger: 'blur' }]
        },
        fieldsLock: false,
        loadingField: false,
        innerLoading:false,
        isRequiredContractDtlList: false,
        isAutoGenContractCode: false,
        companyData:{
          website:'',
          fax:'',
          address:'',
        },
        attractType: [],
        clientRelations: [],
        boothTypes: [
          {value: '标摊',lable: '标摊'},
          {value: '光地',lable: '光地'},
        ],
        boothList: [],
        orgNum: Number(getCookie('orgNum')),
        preOrderIds: '',
        effectiveSetting: '',
        isFullProcess: '',
        allowInvalid: false,
        updateCidAndPid: false,
      }
    },
    mixins: [
      createMixin('contractForm', 'contractId', 't_contract'),
      Mixins.itemKindCode(),
      contractPayPlanMixin(),
    ],
    methods: {
      selectBoothType(e){
        this.contractForm.boothType= e.target.value
      },
      requestCurrencyForeign() {
        Axios.post('/fnCurrency/getAll').then(({data: res}) => {
          this.currencyForeignList = res.map(item => ({currencyForeign: item.id, currencyForeignName: item.text}))
        })
      },
      requestLinkman(parentClientId) {
        const data = {
          parentClientId,
          fWorkteamId: teamId,
          clientTypeCode: 'C_LINKMAN',
          relationCode: 'BtoC1',
          stopped: false,
        }
        if (this.fu_team_data_kywy) {   //权限
          data['fuTeamDataShow'] = 1; //暂时用fuTeamDataShow这个参数
        }
        Axios.post('/client/getLinkman', JSON2FormData(data)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('联系人加载失败！')
          this.linkmanList = res.rows
        })
      },
      selectFinish(type, data) {
        if (type === 'project') {
          this.$set(this.contractForm, 'projectName', data.projectName)
          this.$set(this.contractForm, 'projectId', data.projectId)
          // if(this.mode === 'add' && this.autoGenId){
          //   this.$set(this.contractForm, 'contractCode', this.generateContractCode(data.projectId))
          // }
        } else if (type === 'company') {
          this.$set(this.contractForm, 'clientId', data.clientId)
          this.$set(this.contractForm, 'clientName', data.companyName)

          this.$set(this.companyData, 'website', data.website)
          this.$set(this.companyData, 'fax', data.fax)
          this.$set(this.companyData, 'address', data.address)
          this.requestLinkman(data.clientId)
        }
        this.$refs[type] && this.$refs[type].clearValidate()
      },
      requestBoothInfo(clientId, havePreOrderFlag){
        if (!havePreOrderFlag) return
        Axios.post(variableSponsorTrue + '/api/boothOrderContract/selectPreOrderInfo',JSON2FormData({
            clientId ,
            projectId: this.contractForm.projectId,
            withServeBooth: true,
            orgNum: this.orgNum
          }))
          .then( ({ data: res }) => {
            let {state, data} = res;
            if (state !== 1) return this.$errorMsg('展位数据加载失败！')
            if(!data) return console.warn('data is ' + data)
            this.preOrderIds = data.preOrderIds
            // 产品明细
            this.contractForm.contractDtlList = data.contractDtlList;
            this.amountModify(0, 'null')
            // 展位信息
            this.boothList = data['exhibitBoothOrderVoList']
            this.buildBoothInfo()
          }).catch(err => {
            if(err instanceof Error && err.message === 'Request failed with status code 404'){
              return this.$errorMsg('信息门户未启用，展位数据加载失败！')
            }
            this.$message.error('展位数据加载失败！')
          })
      },
      buildBoothInfo() { // 重新生成展位信息
        if(this.boothList && this.boothList.length) {
          var tmp = {
            boothNum: '',     //展位号
            exhibitsName: '', // 展品名称
            section: '',      // 展馆
            boothSpec: '',    // 展位规格
            boothArea: 0,     //展位面积
            boothCount: 0,    // 展位数量
            boothType: '',    //展位类型名
            openInfo: '',     // 开口情况
          }
          this.boothList.forEach(it => {
            tmp.boothNum += (tmp.boothNum ? ',' : '' ) + it.boothNum;
            tmp.exhibitsName += (tmp.exhibitsName ? ',' : '' ) + it.exhibitsName;
            tmp.section += (tmp.section ? ',' : '' ) + it.sectionName;
            tmp.boothSpec += (tmp.boothSpec ? ',' : '' ) + it.boothSpec;
            tmp.boothArea += Number(it.boothArea);
            tmp.boothCount += Number(it.boothCount);
            tmp.boothType += (tmp.boothType ? ',' : '' ) + it.boothTypeName;
            tmp.openInfo += (tmp.openInfo ? ',' : '' ) + this.getOpenInfoText(it.openInfo);
          })
          Object.keys(tmp).forEach(k => {
            this.$set(this.contractForm, k, tmp[k])
          })
        }
      },
      getOpenInfoText(id){
        const results = '单开口,双开口,三开口,四开口'.split(',')
        return id.split(',').filter(id => !isNaN(id)).map(_info => results[_info - 1])
      },
      autoFullClient() {
        const clientId = this.contractForm.clientId
        if(clientId){
          this.requestLinkman(clientId)
          Axios.post('/client/getByClientId',JSON2FormData({clientId}))
            .then(res=>{
              let data = res.data[0] || {}
              this.$set(this.contractForm, 'clientName', data.companyName || '')
              this.$set(this.companyData, 'website', data.website || '')
              this.$set(this.companyData, 'fax', data.fax || '')
              this.$set(this.companyData, 'address', data.address || '')
            })
        }
        return true
      },
      open(mode, row) {
        const {
          contractId,
          clientId,
          projectId,
          companyName: clientName,
          contractReasonType,
          contractProjectId,
          projectName,
          projectFlow,
          havePreOrderFlag
        } = row
        this.mode = mode
        this.effectiveSetting = contractReasonType || ''
        this.isFullProcess = 'fullProcess' === projectFlow
        if (mode === 'add'){
          this.initForm({clientId, clientName})
          this.selectFinish('project', {projectId: contractProjectId, projectName})
          this.requestBoothInfo(clientId, havePreOrderFlag)
          this.show = true
          this.startChange()
          return
        }
        if (mode === 'edit' || mode === 'read') {
          const handlerContractCallback = data => {
            const {inputterId, ownerId} = data
            this.requestOperator(inputterId, ownerId)
            this.getDocuments(contractId, ()=>this.startChange())
          }
          this.show = true
          contractId ?
            this.requestContractById(contractId, handlerContractCallback):
            this.requestContractByClient(clientId, projectId).then(handlerContractCallback)
        }
      },
      initForm(initValEx) {
        this.clear(...Object.keys(this.contractForm))
        const initVal = {
          inputTime: top.$GlobalDefine.getSystemTime(),//录入时间
          inputterId: Number(getCookie('operatorId')),  //录入人id
          ownerId: Number(getCookie('operatorId')),  // 业绩归属人id
          contractDtlList: [],
          contractPayPlanList: [],
          signDate: top.$GlobalDefine.getSystemTime('yyyy-MM-dd'),
          isChief: true,
          ...initValEx,
          ...extend
        }
        if (Number(teamId) !== -1) {
          const teamList = window.top.vm1.teamList
          const data = teamList.find(({id}) => id === Number(teamId))
          if (data) {
            initVal['ownTeamName'] = data.text
            initVal['ownTeamId'] = data.id
          }
        }
        this.isChecked = false  //是否审核   true/false
        this.isPayment = false  // 是否生效  true/false
        Object.keys(initVal).forEach(key => Object.keys(this.contractForm).includes(key) && (this.contractForm[key] = initVal[key]))
        this.$nextTick(()=>{
          this.$refs.contractForm && this.$refs.contractForm.clearValidate()
        })
      },
      close() {
        if (this.isChange) {
          return this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.isChange = false
            this.fileList = []
            this.contractForm.contractId = ''
            this.show = false
            this.activeNames = [1, /*2, 3,*/ 4, 5]
            this.$refs.contractForm && this.$refs.contractForm.clearValidate()
          }).catch(e => e)
        }
        this.show = false
        this.fileList = []
        this.contractForm.contractId = ''
        this.activeNames = [1, /*2, 3,*/ 4, 5]
        this.$refs.contractForm && this.$refs.contractForm.clearValidate()
      },
      export_pdf(){
        this.$refs.ContractTpl.open(this.contractForm.contractId,this.contractForm.clientName);
      },
      getDownAddress() {
        let param = ['PathZZKDownload', 'PathZZTPhoneDownload', 'PathZZTClientDownload'];
        let downUrl = param.map(settingCode => {
          return Axios.post('/sysSetting/get', JSON2FormData({settingCode}))
        })
        Promise.all(downUrl).then(([{data: {data: PathZZKDownload}}, {data: {data: PathZZTPhoneDownload}}, {data: {data: PathZZTClientDownload}}]) => {
          window.PathZZTPhoneDownload = PathZZTPhoneDownload
          window.PathZZKDownload = PathZZKDownload
          window.PathZZTClientDownload = PathZZTClientDownload
        }).catch(e => e)
      },
      saveContract(passive) {
        this.saveLoading = true
        const success = (noPop, noShowBack) => {
          !noPop && this.$message.success('保存成功')
          this.close()
          this.isChange = false
          this.$emit('operation-complete')
          !noShowBack && frameId && this.$postBackMessage(frameId, 'refresh')
          // this.updatePreOrderById(this.preOrderIds)
        }
        this.$refs['contractForm'].validate().then(() => {
          if (this.effectiveSetting && this.isPayment && !this.contractForm.paymentData) {
            return Promise.reject('intercept:paymentData|合同状态为已生效时合同生效时间不可为空')
          }
          const queryData = {...this.contractForm}
          const mapKeys = ['contractDtlId', 'prodCode', 'qty', 'prodSpec', 'prodName', 'prodPrice', 'discount', 'price', 'amount', 'curencyCode', 'projProductId']
          const contractDtlList = []
          this.contractForm.contractDtlList.forEach(item => {
            const temp = {}
            // 清除新增的时自建的Id
            if (String(item['contractDtlId']).startsWith('NEW_')) item['contractDtlId'] = null
            mapKeys.forEach(key => temp[key] = item[key])
            contractDtlList.push(temp)
          })
          if(this.isRequiredContractDtlList && contractDtlList.length < 1)
            return Promise.reject('RequiredContractDtlList')
          queryData['contractDtlList'] = contractDtlList
          queryData['contractPayPlanList'] = this.getPayPlanListParam()
          queryData['projectName'] && (delete queryData['projectName'])
          queryData['ownTeamName'] && (delete queryData['ownTeamName'])
          queryData['linkmanClientId'] && (delete queryData['linkmanClientId'])
          queryData['attrsCodes'] = Object.keys(this.attrsCodeMap).join(',')
          queryData['operEntrance'] = '定展管理合同修改'
          if(this.mode === 'add'){
            queryData['isAutoGenContractCode'] = this.isAutoGenContractCode
            queryData['operEntrance'] = '定展管理合同新增'
          }
          return Axios.post('/contract/save', queryData)
        }).then(({data: res}) => {
          // 0 失败 1成功  2 同一个机构号下 合同编号重复  3.同一个项目，同一个客户已经有主合同
          let msg = ''
          switch (res.state) {
            case 1:
              if(passive){ //如果是被动请求
                this.saveLoading = false
                this.isChange = false
                switch (passive) {
                  case 'check':  //修改数据后直接点击审核的被动请求
                    this.requestCheck() //得先保存完了再去请求审核接口要不然已经审核了得数据无法保存
                    this.$emit('operation-complete')
                    break
                  case 'upload-success': //这是在禁止主动保存的情况下上传了附件
                    //暂时不用其他操作
                    break
                }
                return //记得结束这边的函数要不完犊子了
              }
              //这里特别注意，[].every(()=>{}) 默认返回true，所以但没长度的时候直得到false（0）
              const autoCheck = this.contractForm.contractDtlList.length &&
                this.contractForm.contractDtlList.every(item => {
                const {discount, lowDiscount, prodPrice} = item
                if (!prodPrice) return true
                if (lowDiscount === null) return true
                return discount >= lowDiscount
              })
              const $inst = this
              const confirmParam = {
                type: 'info',
                callback(f) {
                  f === 'confirm' ?
                    $inst.contractCheck(success, $inst.mode === 'add' ? res.data : undefined) :
                    success()
                }
              }
              this.isChange = false //新增完了就完成了修改
              autoCheck ?
                this.mode !== 'read' ?
                  this.$confirm('保存成功，是否自动审核？', '提示', confirmParam) :
                  success() :
                this.$confirm('合同已提交审核，是否生成提醒？', '提示', {
                  type: 'info',
                  async callback(f){
                    if(f !== 'confirm') return success()
                    // 这里去生成提醒
                    $inst.$emit('remind', ...(await $inst.createRemindData('check', res.data)))
                    success(void 0, !0)
                  }
                })
              break
            case 2:
              msg = '合同编号重复!'
              break
            case 3:
              msg = '在同一个项目下，一个公司客户只能有一个主合同!'
              break
            case 4:
              msg = '数据已被审核!'
              break
            case 5:
              msg = '已生成订单的数据，不允许修改公司或项目!'
              break
            case 6:
              msg = '展位号重复!'
              break
            case 7:
              msg = '已作废的合同不能修改!'
              break
            case 9:
              msg = '有参展信息且合同已生效，不能修改客户和项目!'
              break
            case 10:
              msg = '合同已关联展位，不能修改客户和项目!'
              break
            default:
              msg = res.msg || '保存失败!'
          }
          msg && this.$errorMsg(msg).finally(() => this.saveLoading = false)
        }).catch(e => {
          if (typeof e === 'boolean')
            return this.$errorMsg('必填项未填写或填写的数据不合法!').finally(() => {
              this.saveLoading = false
              this.activeNames = [1, 2, 3]
              this.$focusError()
            })
          if(e === 'RequiredContractDtlList')
            return this.$errorMsg('必须添加产品明细!').finally(() => {
              this.saveLoading = false
              this.activeNames = [1, 4, 5]
            })
          if(typeof e === 'string' && e.startsWith('intercept:')){
            const [ref, message] = e.replace('intercept:', '').split('|')
            return this.$errorMsg(message, '提醒', 'warning').finally(() => {
              this.saveLoading = false
              this.activeNames = [1, 4, 5]
              this.$nextTick(()=>this.$refs[ref] && this.$refs[ref].focus())
            })
          }
          this.$errorMsg('保存失败！').finally(() => this.saveLoading = false)
        })
      },
      requestContractById(contractId, callback) {
        if (!contractId) return this.$errorMsg('数据异常，请刷新重试！')
        Axios.post('/contract/getMoreById', JSON2FormData({contractId})).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败！')
          if (res.data === null)
            return this.$errorMsg('数据可能已被删除, 请刷新重试！').finally(this.close)
          const data = res.data
          this.fullContractForm(data)
          callback && callback(data)
        }).catch(() => this.$errorMsg('数据加载失败！'))
      },
      linkmanFullData(data) {
        if (!data) return
        if (typeof data === 'string')
          return this.$set(this.contractForm, 'linkmanPersonName', data)
        const currentLinkman = this.linkmanList.find(item => item.clientId === data)
        if (!currentLinkman) return console.warn('currentLinkman数据异常！id>>>>>>', data)
        this.$set(this.contractForm, 'linkmanPersonName', currentLinkman.personName)
        const indexed = ['linkmanTel', 'linkmanFax', 'linkmanAddress', 'linkmanEmail', 'linkmanPosition']
        // 传真、地址、网址若联系人没有，则取公司信息
        indexed.forEach(index => {
          const clientKey = index.replace('linkman', '').toLowerCase()
          switch (index) {
            case 'linkmanAddress':
              this.$set(this.contractForm, 'linkmanAddress', currentLinkman[clientKey] || this.companyData[clientKey])
              break
            case 'linkmanFax':
              this.$set(this.contractForm, 'linkmanFax', currentLinkman[clientKey] || this.companyData[clientKey])
              break
            default:
              this.$set(this.contractForm, index, currentLinkman[clientKey])
          }
        })
        this.$set(this.contractForm, 'linkmanWebsite', this.companyData.website)
        this.$set(this.contractForm, 'mobile', currentLinkman['mobile'] || currentLinkman['mobile2'])
        //联系人没有网址这个字段，这边直接直接获取公司客户的网址
        /***
         * linkmanTel: "",  //  联系人电话
         linkmanFax: "",  // 联系人传真
         linkmanAddress: "", // 联系人地址
         linkmanEmail: "",  // 联系人电子邮箱
         linkmanWebsite: "",  // 联系人网址
         linkmanPosition
         mobile: '',
         */
      },
      addProductDetails() {
        if (!this.contractForm.projectId || !this.contractForm.projectName)
          return this.$errorMsg('请先选择项目!', '提示', 'warning')
        this.$refs['productDetails'].open(this.contractForm.projectId)
      },
      deleteProductDetails() {
        const selection = this.$refs['contractDetails'].selection
        if (!selection.length) return this.$errorMsg('请先选择一条要删除的数据', '提示', 'warning')
        this.$confirm(`确定删除选中的数据嘛？`, '警告', {type: 'warning'}).then(r => {
          const contractDtlIdList = selection.map(it => it.contractDtlId)
          this.$set(
            this.contractForm,
            'contractDtlList',
            this.contractForm.contractDtlList.filter(item => !contractDtlIdList.includes(item.contractDtlId))
          )
          this.amountModify(0, 'null')
        })
      },
      productDetailsFinish(rows) {
        rows.forEach(row => {
          const data = {...row}
          data['qty'] = 1
          // data['contractDtlId'] = null
          data['price'] = data['prodPrice']
          data['discount'] = data['prodPrice'] ? 100 : 0
          data['amount'] = data['price'] * data['discount'] / 100 * data['qty']
          this.contractForm.contractDtlList.push(data)
        })
        // console.log(this.contractForm.contractDtlList)
        this.amountModify(0, 'null')
      },
      contractCheck(success, contractId) {
        if (!this.contractForm.contractId && !contractId)
          return this.$errorMsg('新增的合同需要保存才能审核哦！')
        // 点击审核并且此时数据已经有改动需要先保存
        if(!this.isChecked && this.isChange)
          return this.saveContract('check')
        this.requestCheck(success, contractId) //分出来是为了做审核自动保存的操作
      },
      requestCheck(success, contractId){
        const msg = !this.isChecked ? '审核' : '撤销审核'
        const fd = JSON2FormData({
          contractId: this.contractForm.contractId || contractId,
          isChecked: !this.isChecked,
          operEntrance: !this.isChecked ? '定展管理合同审核' : '定展管理撤销审核'
        })
        Axios.post('/contract/check', fd).then(({data: res}) => {
          if (res.state === 0) return this.$errorMsg(`${msg}失败`)
          if (res.state === 2) return this.$errorMsg('重复操作！')
          if (res.state === 3) return this.$errorMsg(`合同已作废，不能${msg}！`)
          this.isChecked = !this.isChecked
          // 处理同意审核的提醒
          this.isChecked && this.processApplyCheck().then()
          //合同已生效，本次更改是否更新订单和应收款 (点击审核合同时)注意： 用 ‘this.isChecked‘ 是因为数据已经变了
          if (this.isPayment && this.isChecked)
            return this.$confirm('合同已生效，本次更改是否更新订单和应收款？', '提示', {type: 'info'})
          //取消审核的时候mode变成edit
          if (!this.isChecked) this.mode = this.selfAuthority.contractModify ? 'edit' : 'read'
          this.$message.success(`${msg}成功！`)
          this.$emit('operation-complete')
          success && success() //正常成功
          return Promise.reject('exit')
        }).then(() => {
          return Axios.post('/contract/updateOrderAndReceivable', JSON2FormData({contractId: this.contractForm.contractId}))
        }).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据更新失败！')
          this.$message.success('数据更新成功！')
          success && success(true) //确定更新订单和应收款后成功
          this.$emit('operation-complete')
        }).catch(e => {
          if (e === 'cancel') {
            success && success() //取消更新订单和应收款后成功
            // this.$message.success('数据更新成功！')
            this.$emit('operation-complete')
            return
          }
          if (e === 'exit') {
            return
          }
          console.log(e)
          this.$errorMsg('数据加载失败！')
        })
      },
      contractPayment(){
        if (this.effectiveSetting && !this.isPayment) {
          return this.$refs['ConPayDialog'].open(this.effectiveSetting, this.contractForm.paymentData)
        }
        // 如果是盖章生效，并且已经首付款了，就不能直接撤销合同生效
        // if (this.selfAuthority.contractEffective && this.paymentFlag) {
        //   return this.$errorMsg('首付款已到帐的合同不可撤销生效，请先撤销到帐后再进行撤销生效操作。', '提醒', 'warning')
        // }
        this.contractPaymentInner()
      },
      contractPaymentFinish({type, paymentData, isAddAwoke}){
        this.$refs['ConPayDialog'].close()
        if(type) this.contractPaymentInner(paymentData, isAddAwoke)
      },
      async contractPaymentInner(paymentData, isAddAwoke) {
        const contractId = this.contractForm.contractId
        if (!contractId) return this.$errorMsg('新增的合同需要先保存才能生效哦！')
        // let reserveBoothFlag = true
        // try {
        //   this.isFullProcess && this.isPayment && await this.$confirm(
        //     '取消合同生效可能会导致展位预定信息变更，是否保留当前展位预定信息？',
        //     '提醒',
        //     {
        //       type: 'warning',
        //       confirmButtonText: '保留',
        //       cancelButtonText: '不保留',
        //     }
        //   )
        // }catch (_) {
        //   reserveBoothFlag = false
        // }
        const fd = JSON2FormData({
          contractId,
          updateOrder: true,
          isPayment: !this.isPayment,
          paymentData: paymentData || '',
          payMentValue: this.effectiveSetting,
          // reserveBoothFlag,
          operEntrance: !this.isPayment ? '合同生效' : '取消生效'
        })
        const loading = this.$loading({background: 'rgba(0, 0, 0, 0.3)'})
        Axios.post('/contract/payment', fd)
          .then(({data: res}) => {
            if (res.state === 0) return this.$errorMsg('操作失败！')
            if (res.state === 2) return this.$errorMsg('当前数据已经是生效状态！')
            if (res.state === 3) return this.$errorMsg('当前数据已作废，无法操作！')
            this.isPayment = !this.isPayment
            // isPayment为最新状态
            !this.isPayment && this.$message.success('合同取消生效时, 会删除订单和应收单！')
            if (this.isPayment && isAddAwoke) {
              this.createRemindData('payment').then(params => this.$emit('remind', ...params))
            }
            /*if(this.isPayment){
              const _this = this
              this.$showMessage({
                title: '合同生效提醒',
                message: '合同已生效，是否生成提醒？',
                showCancel: !0,
                async confirm(next){
                  // 这里去生成提醒
                  _this.$emit('remind', ...(await _this.createRemindData('payment')))
                  next && next()
                },
              })
            }*/
            this.requestContractById(contractId, this.startChange)
            this.$emit('operation-complete')
          })
          .catch(() => this.$errorMsg('操作失败！'))
          .finally(() => loading && loading.close())
      },
      async initialPayment(){
        const {contractId, paymentData} = this.contractForm
        if (!contractId) return this.$errorMsg('数据异常！请刷新重试！')
        const fd = JSON2FormData({
          contractId,
          paymentFlag: !this.paymentFlag,
          paymentData: paymentData || '',
          operEntrance: !this.paymentFlag ? '定展管理首付款到账' : '定展管理撤销首付款到账'
        })
        try {
          const { data: { state } } = await Axios.post('/contract/initialPayment', fd)
          if(state === 0) return this.$errorMsg('操作失败！')
          if(state === 3) return this.$errorMsg('合同已经作废，不能进行到账操作！')
          this.requestContractById(contractId, this.startChange)
          this.$message.success('操作成功！')
          this.$emit('operation-complete')
        }catch (e) {
          console.warn(e)
          this.$errorMsg('操作失败！')
        }
      },
      rowClick(row, _, event, ref) {
        ref = ref || 'contractDetails'
        event.target.classList[0] !== 'el-input__inner' &&
        this.$refs[ref].toggleRowSelection(row)
      },
      amountModify($index, modifyCode = 'other') {
        let currency = 0, amount = 0, curencyCode = '', oriAmount = 0, RMBAmount = 0
        const item = this.contractForm.contractDtlList[$index]
        if(item){
          try {
            if(item['discount'] > 0 && !item['prodPrice'])
              item['discount'] = 0
            switch (modifyCode) {
              case 'amount':
                item['price'] = !item['qty'] ? 0 : item['amount'] / item['qty'] //Number().toFixed(2)
                item['discount'] = !item['prodPrice'] ? 0 : item['price'] / item['prodPrice'] * 100 // Number().toFixed(2)
                break
              case 'discount':
                item['price'] = item['prodPrice'] * item['discount'] / 100
                item['amount'] = item['price'] * item['qty'] //Number(().toFixed(2))
                break
              case 'price':
                item['discount'] = !item['prodPrice'] ? 0 : item['price'] / item['prodPrice'] * 100 //Number().toFixed(2)
                item['amount'] = item['price'] * item['qty'] //Number(().toFixed(2))
                break
              case 'other':
                item['amount'] = item['price'] * item['qty'] //Number(().toFixed(2))
                break
              case 'null':
                break
            }
          } catch (e) {
            return console.log('数据异常！')
          }
        }
        this.contractForm.contractDtlList.forEach(_item => {
          if (_item['curencyCode'] === 'RMB') {
            amount += _item['amount']
            RMBAmount += _item['price'] * _item['qty']
            oriAmount += _item['prodPrice'] * _item['qty'] || _item['amount']
          } else {
            currency += _item['amount']
            curencyCode = _item['curencyCode']
          }
        })
        this.$set(this.contractForm, 'amountForeign', currency)
        this.$set(this.contractForm, 'amount', RMBAmount)
        this.$set(this.contractForm, 'currencyForeign', curencyCode)
        this.$set(this.contractForm, 'oriAmount', oriAmount)
        const totalDiscount = RMBAmount === 0 ? 0 : Number((RMBAmount / oriAmount * 100).toFixed(2))
        this.$set(this.contractForm, 'totalDiscount', totalDiscount)
      },
      requestFieldSettings(taskKindCode) {
        this.loadingField = true
        return Axios.post('/exhibition/selectExhibitSetting',
          JSON2FormData({taskKindCode, teamId})).then(({data: res})=>{
          return Promise.resolve(res)
        }).catch(e=>{
          this.$errorMsg('字段设置加载失败！').finally(e=>this.loadingField = false)
          return Promise.resolve(false)
        })
      },
      setFieldRules(){
        if(!this.fieldsLock){
          this.requestFieldSettings('fo_contract_field_set').then(res=>{
            if(!res || !res.length)
              return this.$nextTick(()=>{
                this.loadingField = false
                this.fieldsLock = false
              })
            //获取产品明细必填
            const contractDtlListSetting = res.find(item=> 'contractDtlList'===item.param)
            this.isRequiredContractDtlList = contractDtlListSetting && contractDtlListSetting.isNull
            //获取合同编号自动生成
            const autoGenContractCode = res.find(item=> 'autoGen_contractCode'===item.param)
            this.isAutoGenContractCode = Boolean(autoGenContractCode && autoGenContractCode.isNull)
            const panelSettings = res.filter(item => ['linkManInfo', 'boothNumInfo'].includes(item.param))
            this.$refs['tFormWrapper'].$children.forEach(item=>{
              const panelId = item.name
              // 4和5还没做字段控制
              if([4, 5].includes(panelId)) return
              // 2 和 3 有一个控制整块的参数, 如果是隐藏就不用再设置内部的字段了
              if([2, 3].includes(panelId)){
                //表示不显示
                if(panelSettings[item.name - 2].value !== '1')
                  return item.$el.remove() //直接退出设置字段相关必填项
              }
              item.$children.forEach(formItem=>{
                const {prop, label} = formItem
                const currentSetting = res.find(__item=>__item.param === prop)
                if(currentSetting){
                  if(currentSetting.value !== '1')
                    return formItem.$el.remove()
                  if(currentSetting.isNull === 1){
                    const _rule = {required: true, message: label + '为必填项', trigger: ['blur', 'change']}
                    if(currentSetting.param === 'boothArea')
                      this.rules.boothArea.push(_rule)
                    else this.$set(this.rules, prop, [_rule])
                  }
                }
              })
            })
            this.$nextTick(()=>{
              this.loadingField = false
              this.$refs.contractForm && this.$refs.contractForm.clearValidate()
            })
          }).catch(e=>{
            console.warn(e)
            this.$nextTick(()=>{
              this.loadingField = false
              this.fieldsLock = false
            })
          })
          this.fieldsLock = true
        }
      },
      async createRemindData(type, _contractId){
        let title
        const {contractId, clientName, clientId, projectName} = this.contractForm
        const remindContractId = _contractId || contractId
        if(!remindContractId) return this.$errorMsg('生成提醒失败，请刷新重试！')
        const data = {
          tableName: 't_contract',
          recordCode: remindContractId,
          beforeMinutes: 0,
          remindeds: [],
          awokeTime: top.$GlobalDefine.getSystemTime(),
          awokeDetail: `项目名称：${projectName}<br/>公司名称：${clientName}<br/>客户ＩＤ：${clientId}<br/>合同序号：${remindContractId}`
        }
        switch (type) {
          case 'payment':
            title = '合同生效提醒'
            data.awokeType = '7'
            data.awokeSummary = data.subject = `合同${remindContractId}已经生效`
            break
          case 'check':
            title = '合同提交审核提醒'
            data.awokeType = '10'
            data.remindeds = await this.getSuperByOperId()
            data.awokeSummary = data.subject = `合同${remindContractId}审核申请`
            break
        }
        return [data, title, this.operatorList]
      },
      async getSuperByOperId(key){
        const { data: { state, data } } = await Axios.post('/operator/getSuperByOperId')
        if(state === 1 && data && data.length > 0){
          return [data[0]['operatorId']]
        }
        return null
      },
      createCheckApplySignature(){
        const timer = getCurrentIframeId().split('_')[1].slice(0, -4)
        return `AWOKE-${this.contractForm.contractId}-10-${getCookie('operatorId')}-${timer}`
      },
      async processApplyCheck(){
        const sign = this.createCheckApplySignature()
        const addAwokeParamsStr = sessionStorage.getItem(sign)
        sessionStorage.removeItem(sign) // 只能生效一次
        if(!addAwokeParamsStr){
          return console.warn('未找到签名为：' + sign + '的数据！' )
        }
        try {
          const addAwokeParams = JSON.parse(addAwokeParamsStr)
          const { __FUN_ADD_AWOKE__, __FUN_PROCESS_AWOKE_DEALRESULT__ } = window.top
          if(!__FUN_ADD_AWOKE__ ||  !__FUN_PROCESS_AWOKE_DEALRESULT__ ){
            return console.warn('`__FUN_ADD_AWOKE__` 或 `__FUN_PROCESS_AWOKE_DEALRESULT__`未找到!')
          }
          const awokeId = addAwokeParams['awokeId']
          // delete addAwokeParams['awokeId'] // 如果新增接口不能传ID的话就删掉
          await __FUN_ADD_AWOKE__(addAwokeParams, !0)
          await __FUN_PROCESS_AWOKE_DEALRESULT__(1, awokeId)
        }catch (e) {
          e && console.warn(e)
        }
      },
      async requestContractByClient(clientId, projectId){
        this.innerLoading = true
        try{
          const postData = JSON2FormData({clientId, projectId})
          const {data: {state, data}} = await Axios.post('/contract/selectMoreContractById', postData)
          if(state !== 1 || !data) await Promise.reject('查询主合同失败！')
          this.fullContractForm(data)
          return data
        }catch (e) {
          typeof e === 'string' && this.$errorMsg(e)
          await Promise.reject(e)
        }finally {
          this.innerLoading = false
        }
      },
      fullContractForm(data){
        if(!data) return console.warn(`[warn] fullContractForm.data is: ${data}`)
        Object.keys(this.contractForm).forEach(key => this.contractForm[key] = data[key])
        this.contractForm['linkmanClientId'] = data['linkmanPersonName']
        this.isChecked = data['isChecked']
        this.isPayment = data['isPayment']
        this.paymentFlag = data['paymentFlag']
        this.allowInvalid = data['allowInvalid']
        this.updateCidAndPid = data['updateCidAndPid']
        this.isInvalid = data['state'] === 0
        this.setPayPlanListParam(data['contractPayPlanList'])
      },
      async updatePreOrderById(preOrderIds){
        if(!preOrderIds) return console.warn(`[warn] updatePreOrderById.preOrderIds is Undefined!`)
        const apiURL = variableSponsorTrue + '/api/boothPreOrder/updatePreOrderById'
        try {
          const {data: {state}} = await Axios.post(apiURL, JSON2FormData({preOrderIds}))
          if (state !== 1) return this.$errorMsg('CRM订单更新失败！')
          this.$emit('operation-complete')
        }catch (e) {
         typeof e === 'string' && this.$errorMsg(e)
        }
      },
      evalContractCmd(command){
        if(!this.hasOwnProperty(command))
          return console.warn('[warn]Unsupport Command:' + command)
        this[command].call(this)
      },
      // 合同作废
      async invalid(isUnInvalid) {
        try {
          const state = isUnInvalid ? 1 : 0
          const extMsg = isUnInvalid ? '取消': ''
          const contractId = this.contractForm.contractId
          if(!contractId) await Promise.reject('数据异常，请刷新重试！')
          await this.$confirm(`确认${extMsg}作废此合同吗？`, '提醒', {type: 'warning'})
          const post = {
            contractId,
            state,
            operEntrance: `合同${extMsg}作废`
          }
          this.innerLoading = true
          const {data} = await Axios.post('/contract/invalid', JSON2FormData(post))
          let errorMessage
          switch (data.state) {
            case 0 :
              errorMessage = '操作失败！'
              break
            case 3 :
              errorMessage = '当前已经是作废状态，不能再操作！'
              break
            case 4 :
              errorMessage = '主合同没有参展信息不允许作废！'
              break
            case 5 :
              errorMessage = '当前项目客户已存在主合同，无法取消作废！'
              break
          }
          errorMessage && await Promise.reject(errorMessage)
          this.$message.success(data.data.payMentContractNum > 0 ? `作废成功，该客户在当前项目下还剩 ${data.data.payMentContractNum} 张已生效合同。`: `${extMsg}作废成功!`)
          this.isInvalid = !isUnInvalid
          if (!this.isInvalid) this.mode = 'edit'
          this.requestContractById(contractId, this.startChange)
          this.$emit('operation-complete')
        }catch (e) {
          if(e && e !== 'cancel') {
            typeof e === 'string' && this.$errorMsg(e)
            console.warn(e)
          }
        }finally {
          this.innerLoading = false
        }
      },
      unInvalid(){
        return this.invalid(!0)
      },
      dialogClosed(){
        this.saveLoading = false
        this.isInvalid = false
        this.effectiveSetting = ''
        this.isChecked = false
        this.isPayment = false
        this.paymentFlag = false
        this.removeAttrsCodes()
      },
      async openSelectTeam(){
        try {
          if (this.selfAuthority.disableOwnTeamName) return
          const [data] = await this.$refs.ts.open()
          this.$set(this.contractForm, 'ownTeamName', data.text)
          this.$set(this.contractForm, 'ownTeamId', data.id)
        }catch (e) {
          e && e !== 'cancel' && console.warn(e)
        }
      }
    },
    mounted() {
      this.requestCurrencyForeign()
      this.requestOperator()
      this.getItemKindCode('attract_type')
      this.getItemKindCode('client_relations')
      frameId && this.$handleOperation(frameId, 'contract', _data=>{
        const {data, mode, projectId, projectName, clientId, clientName} = _data
        if(this.show)return this.$message.info('弹窗被占用，请先处理当前弹窗！')
        mode && this.open(mode, data, {projectId, projectName, clientId, clientName})
      })
      if(extend.contractId){
        this.open('edit', extend.contractId)
      }
    },
    computed: {
      title() {
        switch (this.mode) {
          case 'add':
            return '新增销售合同'
          case 'read':
            return '销售合同'
          case 'edit':
            return '编辑销售合同'
        }
      },
      checkedText() {
        return this.isChecked ? '撤销审核' : '合同审核'
      },
      paymentText() {
        return this.isPayment ? '撤销生效' : '合同生效'
      },
      initialPaymentText() {
        return this.paymentFlag ? '撤销到账' : '首付款到账'
      },
      selfAuthority() {
        const mode = this.mode
        const isChecked = this.isChecked
        const isInvalid = this.isInvalid
        const allowInvalid = this.allowInvalid
        const updateCidAndPid = mode === 'add' || this.updateCidAndPid
        const readonly = mode === 'read'
        const contractModify = getAuthority('fu_contract_Mod_crm')
        const contractAdd = getAuthority('fu_contract_Add_crm')
        const contractCheck = getAuthority('fu_contract_check_crm')
        const contractEffect = getAuthority('fu_contract_effect_crm')
        const contractInvalid = getAuthority('fu_contract_invalid_crm')
        const exportPdf = getAuthority('fu_contract_export_pdf_crm')
        // 盖章生效
        const contractEffective = 'contractEffective' === this.effectiveSetting
        // 首付款生效
        const initialPayment = 'initialPayment' === this.effectiveSetting
        return {
          readonly, //只读
          contractCheck,  //合同审核权限
          contractModify, //合同修改权限
          contractAdd,    //合同新增权限
          contractEffect, //合同生效权限
          contractInvalid, //合同作废权限
          noSave: isInvalid || isChecked || readonly || (!contractModify && !contractAdd), //不允许保存
          showHeaderLeft: mode !== 'add' && !isInvalid, //是否显示头部左边元素
          allowSelection: !readonly && !isChecked && (contractModify || contractAdd) && updateCidAndPid,      //是否允许选择项目或公司
          disabledPayment: true, //!this.isPayment || mode === 'add', // 禁用合同生效时间控件
          showInitialPayment: contractEffective && this.isPayment, // 是否显示首付款到账按钮
          contractEffective,
          initialPayment,
          isInvalid,
          exportPdf,
          disableOwnTeamName: readonly || isChecked || (!contractModify &&  !contractAdd) || Number(teamId) !== -1,
          allowInvalid,
          updateCidAndPid
        }
      },
    },
    components:{
      ContractPayment,
      ContractTpl,
      TeamSelect
    }
  }
</script>

<style scoped>
  .el-input, .el-select {
    width: 100%;
  }

  .el-input .append {
    cursor: pointer;
  }

  .dialog-body {
    height: 75vh;
    max-height: 75vh;
    overflow: auto;
    padding: 0;
    position: relative;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
  }

  .dialog-header .dialog-header-left {
    color: #9FA9B3;
  }

  .dialog-header .dialog-header-left span.is {
    color: #F74444;
  }

  .dialog-header .dialog-header-left > span {
    padding: 0 30px 0 5px;
    line-height: 29px;
  }

  .dialog-header .dialog-header-left > .el-button {
    border-radius: 0;
    background-color: #63A9FB;
    color: #fff !important;
  }

  .dialog-header .dialog-header-left > .el-button.is {
    background-color: #9FA9B3;
  }

  .el-collapse-item__header {
    height: 30px;
    line-height: 30px;
    color: #4696f9;
    background-color: #f5f5f5;
    padding-left: 20px;
  }

  .p10 .el-collapse-item__content {
    padding: 10px 10px 20px;
  }

  .el-collapse-item__content {
    padding: 20px 20px 2px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .el-form .el-form-item.el-form-item--mini {
    flex: 0 0 400px;
  }

  .el-upload.el-upload--text {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
    height: 50px;
    line-height: 50px;
  }

  .contract-details .cell {
    text-align: center;
    cursor: default;
  }

  .contract-details .el-table__header th {
    background-color: #f5f5f5;
    color: #858585 !important;
  }

  .contract-details .el-input-number{
    width: 100%;
  }

  .contract-details .el-input__prefix{
    display: none;
  }

  .contract-details .el-input__inner {
    text-align: center;
    border-width: 0;
    border-radius: 0;
    padding: 0;
  }

  .contract-details .el-input__inner:focus {
    border-width: 1px;
  }

  .contract-details .effect-remind-wrap {
    display: flex;
    align-items: center;
  }
  .contract-details .effect-remind-wrap .effect-remind{
    margin-left: 10px
  }
  .contract-details .effect-remind-wrap .el-input__inner{
    border-width: 1px;
  }
  .contract-details .effect-remind-wrap .el-select .el-input__suffix i {
    opacity: initial;
  }

  .top-button {
    flex-basis: 100%;
    height: 35px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .top-button i {
    color: #63A9FB;
  }

  .top-button a {
    padding: 0 15px;
  }

  .top-button a > i {
    font-size: 1rem;
  }

  .top-button a:hover {
    background-color: #eee;
  }

  .title-info {
    margin-left: 20px;
    color: #999999;
  }

  .is-disabled .el-input__inner {
    color: #999999;
  }

  .cell .el-select .el-input__suffix i {
    opacity: 0;
  }
</style>
<style>
  .el-dropdown-menu.el-popper.contract-invalid{
    width: auto!important;
  }
</style>