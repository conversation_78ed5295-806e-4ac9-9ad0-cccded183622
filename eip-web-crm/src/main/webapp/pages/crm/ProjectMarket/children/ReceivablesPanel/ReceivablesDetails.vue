<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    @closed="saveLoading=false"
    :title="title"
    width="800px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left" v-if="!selfAuthority.projectArchiveState">
          <span v-if="tForm.orderId">订单序号： {{tForm.orderId}}</span>
        </div>
        <div v-else></div>
        <div class="dialog-header-right" v-if="!selfAuthority.projectArchiveState">
          <el-button
              :disabled="selfAuthority.disabled"
              size="mini"
              icon="el-icon-circle-check"
              type="primary"
              :loading="saveLoading"
              @click="saveForm">
            保存{{saveLoading ? '中...': ''}}
          </el-button>
        </div>
        <div v-else></div>
      </el-header>
      <el-form
          class="tForm"
          :rules="rules"
          ref="tForm"
          style="padding-right: 26px;"
          :model="tForm"
          :disabled="selfAuthority.disabled"
          label-width="90px"
          size="mini">
        <el-form-item label="应付款序号" prop="payablesId" v-if="isPurchaseOrder">
          <el-input v-model="tForm.payablesId" :disabled="true" placeholder="应付款序号由系统自动生成"></el-input>
        </el-form-item>
        <el-form-item label="应收款序号" prop="receivablesId" v-else>
          <el-input v-model="tForm.receivablesId" :disabled="true" placeholder="应收款序号由系统自动生成"></el-input>
        </el-form-item>
        <el-form-item label="手工单号" prop="payablesCode" v-if="isPurchaseOrder">
          <el-input v-model="tForm.payablesCode" clearable placeholder="手工单号"></el-input>
        </el-form-item>
        <el-form-item label="手工单号" prop="receivablesCode" v-else>
          <el-input v-model="tForm.receivablesCode" clearable placeholder="手工单号"></el-input>
        </el-form-item>
        <el-form-item ref="project" label="项目名称" prop="projectName">
          <el-input
              class="project"
              v-model="tForm.projectName"
              readonly
              :disabled="selfAuthority.readonly"
              @click.native="selfAuthority.allowSelection && (showProjectSelect = true)"
              placeholder="选择项目名称"
          >
            <i
                slot="suffix"
                class="el-input__icon el-icon-delete"
                @click.stop="selfAuthority.allowSelection && clear('projectId','projectName')"></i>
          </el-input>
          <input type="hidden" v-model="tForm.projectId">
        </el-form-item>
        <el-form-item ref="company" label="公司名称" prop="cltName">
          <el-popover
            ref="popover"
            placement="top"
            width="300"
            trigger="manual"
            @after-enter="$refs.popover && $refs.popover.popperJS.update()"
            :value="isCompanyNameDiff && !selfAuthority.readonly">
            <div v-if="nowClientInfo">
              <div style="display: flex;padding: 10px 5px 15px">
                <i class="el-icon-warning" style="color: rgb(250, 173, 20);font-size: 1.2rem;margin-right: 10px;"></i>
                <span>公司名称已更改为【{{ nowClientInfo.companyName }}】，是否更新？</span>
              </div>
              <div style="text-align: right">
                <el-button size="mini" plain @click="cancelUpdateDiffCompanyName">取消</el-button>
                <el-button size="mini" type="primary" @click="confirmUpdateDiffCompanyName">确定</el-button>
              </div>
            </div>
            <el-input
              slot="reference"
              class="project"
              v-model="tForm.cltName"
              readonly
              :disabled="selfAuthority.readonly"
              @click.native="selfAuthority.allowSelection && (showCompanySelect = true)"
              placeholder="选择公司名称"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-delete"
                @click.stop="selfAuthority.allowSelection && clear('clientId','cltName')"></i>
            </el-input>
          </el-popover>
          <input type="hidden" v-model="tForm.clientId">
        </el-form-item>
        <el-form-item :label="isPurchaseOrder ? '应付金额': '应收金额'" prop="amount">
          <el-input-number
            :controls="false"
            :precision="2"
            v-model="tForm.amount"
            clearable
            :placeholder="isPurchaseOrder ? '应付金额': '应收金额'"></el-input-number>
        </el-form-item>
        <el-form-item label="经手人" prop="handlerId">
          <el-select v-model="tForm.handlerId"  filterable default-first-option clearable placeholder="经手人">
            <el-option
                v-for="item in operatorList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否含税" prop="includeTax">
          <el-select
              @change="e=>!e && clear('taxRate')"
              v-model="tForm.includeTax"
              placeholder="请选择是否含税"
              clearable>
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税率" prop="taxRate">
          <el-input-number :controls="false" v-model="tForm.taxRate"  :disabled="!tForm.includeTax" clearable placeholder="税率"></el-input-number>
        </el-form-item>
        <el-form-item v-if="IS_ENABLE_FOREIGN_CURRENCY" label="币种" prop="currencyCode">
          <el-select v-model="tForm.currencyCode"  filterable default-first-option @change="currencyCodeChange" clearable placeholder="币种">
            <el-option
                v-for="item in currencyList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="IS_ENABLE_FOREIGN_CURRENCY" label="汇率" prop="rmbRate">
          <el-input-number :controls="false" v-model="tForm.rmbRate" clearable placeholder="汇率"></el-input-number>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessTypeCode">
          <el-select
              v-model="tForm.businessTypeCode"
              placeholder="请选择业务类型"
              disabled
              clearable>
            <el-option
                v-for="bType in businessTypeList"
                :key="bType.id + Math.random()"
                :value="bType.id"
                :label="bType.text">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="输入员" prop="inputterId">
          <el-select v-model="tForm.inputterId" clearable placeholder="输入员" :disabled="true">
            <el-option
                v-for="item in operatorList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="输入时间" prop="inputTime">
          <el-date-picker
              :disabled="true"
              placeholder="请选择输入时间"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
              v-model="tForm.inputTime">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="记账时间" prop="bookDate">
          <el-date-picker
              placeholder="请选择记账时间"
              type="date"
              value-format="yyyy-MM-dd"
              clearable
              v-model="tForm.bookDate">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" style="flex-basis: 100%">
          <el-input type="textarea"
                    maxlength="200"
                    show-word-limit
                    resize="none"
                    :autosize="{minRows: 2, maxRows: 6}"
                    v-model="tForm.memo"></el-input>
        </el-form-item>
      </el-form>
      <project-select
          :show="showProjectSelect"
          @close="showProjectSelect=false"
          @finish="selectFinish('project', $event)"></project-select>
      <company-select
          :show="showCompanySelect"
          @close="showCompanySelect=false"
          @finish="selectFinish('company', $event)"></company-select>
    </el-main>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  const ProjectSelect = importC('ProjectSelect')
  const CompanySelect = importC('CompanySelect')
  const isPayables = window.FormPrimaryKey === 'payablesId'

  export default {
    name: "ReceivablesDetails",
    mixins: [
      createCompanyChangedMixin(
        isPayables ? 'fnPayables' : 'fnReceivables',
        isPayables ? 'payablesId' : 'receivablesId',
        isPayables ? '应付款详情' : '应收款详情',
        'tForm',
        'cltName',
      )
    ],
    inject:{
      businessTypeCode: {
        from: 'businessTypeCode',
        default: 'YS_XSDD'
      },
      isPurchaseOrder: {
        from: 'isPurchaseOrder',
        default: false
      },
      apiPrefix: {
        from: 'apiPrefix',
        default: 'fnReceivables'
      },
      primaryKey: {
        from: 'primaryKey',
        default: 'receivablesId'
      }
    },
    data() {
      const form = {
        projectId: '',  //项目id
        projectName: '', //项目名称
        clientId: '',  //客户id
        cltName: '', //公司名称
        inputTime: top.$GlobalDefine.getSystemTime(),//录入时间
        inputterId: Number(getCookie('operatorId')),  //录入人id
        memo: '',  //备注
        bookDate: '',
        amount: undefined,
        handlerId: '',
        currencyCode: '',
        rmbRate: '',
        includeTax : '',
        taxRate : '',
        taxAmount : '',
        businessTypeCode: '',
        orderId: ''
      }
      // "payablesId": 133,   //应收单id
      // "payablesCode": "1757-1",  //手工单号
      const appendForm = this.isPurchaseOrder ?
        {payablesId: '', payablesCode: ''}:
        {receivablesId : '',receivablesCode : ''}
      Object.assign(form, appendForm)
      return {
        show: false,
        showProjectSelect: false,
        showCompanySelect: false,
        tForm: form,
        currencyList: [],
        operatorList: [],
        businessTypeList:[
          {id: 'YF_CGDD', text: '采购订单应付'},
          {id: 'YS_XSDD', text: '销售订单应收'},
          {id: 'YF_OTHER', text: '其他应付'},
          {id: 'YS_OTHER', text: '其他应收'},
        ],
        rules: {
          amount: [{required: true, message: '必须填写应收金额', trigger: 'blur'}],
          projectName: [{required: true, message: '必须选择项目名称', trigger: 'blur'}],
          cltName: [{required: true, message: '必须选择公司名称', trigger: 'blur'}]
        },
        mode: 'add',
        readonlyMarkHeight: 'auto',
        isChange: false,
        saveLoading: false,
        projectArchiveState: false
      }
    },
    methods: {
      requestCurrencyList() {
        Axios.post('/fnCurrency/getAll').then(({data: res}) => {
          this.currencyList = res
        }).catch(e => {
          this.$errorMsg('获取币种失败！');
        })
      },
      requestTFormById(id, callback) {
        if (!id) return this.$errorMsg('数据异常，请刷新重试！')
        Axios.post(`/${this.apiPrefix}/getById`, JSON2FormData({[this.primaryKey]: id})).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败！')
          if (res.data === null)
            return this.$errorMsg('数据可能已被删除, 请刷新重试！').finally(this.close)
          const data = res.data
          this.projectArchiveState = !!data.projectArchiveState
          Object.keys(this.tForm).forEach(key => this.tForm[key] = data[key])
          this.getNowClientInfo(data.clientId)
          callback && callback(res.data)
        }).catch(() => this.$errorMsg('数据加载失败！'))
      },
      open(mode, id, initValEx) {
        this.mode = mode
        this.show = true
        if (mode === 'edit' || mode === 'read') {
          this.requestTFormById(id, data=>{
            const {inputterId, handlerId} = data
            this.requestOperator(inputterId, handlerId)
            this.startChange()
            this.projectArchiveState && this.$message({
              message: '项目已处于封存状态，封存项目无法进行业务操作。',
              type: 'warning',
              showClose: true,
              duration: 0,
            })
            mode === 'read' && setTimeout(()=>{
              this.$refs['body'].$el.scrollTo(0, 0)
              this.readonlyMarkHeight = this.$refs['body'].$el.scrollHeight - 40  + 'px'
            }, 10)
          })
        }else{
          this.projectArchiveState = false
          this.initForm(initValEx)
          this.startChange()
        }
      },
      clear(...key) {
        key.forEach(k => this.$set(this.tForm, k, ''))
      },
      close() {
        this.$message.closeAll()
        if(this.isChange){
          return this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示',{
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(()=>{
            this.isChange = false
            this.readonlyMarkHeight = 'auto'
            this.show = false
            this.$refs.tForm && this.$refs.tForm.clearValidate()
          }).catch(e=>e)
        }
        this.show = false
        this.readonlyMarkHeight = 'auto'
        this.$refs.tForm && this.$refs.tForm.clearValidate()
        this.cancelUpdateDiffCompanyName()
      },
      initForm(initValEx){
        this.clear(...Object.keys(this.tForm))
        const initVal = {
          inputTime: top.$GlobalDefine.getSystemTime(),//录入时间
          bookDate: top.$GlobalDefine.getSystemTime('yyyy-MM-dd'),//录入时间
          inputterId: Number(getCookie('operatorId')),  //录入人id
          businessTypeCode: this.isPurchaseOrder ? 'YF_OTHER': 'YS_OTHER',
          currencyCode: 'RMB',
          amount: undefined,
          rmbRate: 1,
          ...initValEx,
          ...extend
        }
        Object.keys(initVal).forEach(key => Object.keys(this.tForm).includes(key) && (this.tForm[key] = initVal[key]))
      },
      saveForm() {
        this.saveLoading = true
        this.$refs['tForm'].validate().then(() => {
          const queryData = {...this.tForm}
          queryData['projectName'] && (delete queryData['projectName'])
          queryData['operEntrance'] = this.formName + '修改,新增'.split(',')[Number(this.mode === 'add')]
          return Axios.post(`/${this.apiPrefix}/saveOther`, queryData)
        }).then(({data: res}) => {
          // 0 失败 1成功  2 同一个机构号下 合同编号重复  3.同一个项目，同一个客户已经有主合同
          let msg = ''
          switch (res.state) {
            case 1:
              this.$message.success('保存成功')
              this.tForm[this.primaryKey] = ''
              this.isChange = false
              frameId && this.$postBackMessage(frameId, 'refresh', {type: 'refresh2'})
              this.close()
              this.$emit('operation-complete')
              break
            case 2:
              msg = `'只能修改业务类型为：“${this.isPurchaseOrder ? '其他应付': '其他应收'}” 的数据！'`
              break
            default:
              msg = '保存失败!'
          }
          msg && this.$errorMsg(msg).then(()=>this.saveLoading = false)
        }).catch(e => {
          if (typeof e === 'boolean')
            return this.$errorMsg('带 * 为必填项!').then(()=>{
              this.saveLoading = false
              this.$focusError()
            })
          this.$errorMsg('保存失败！').then(()=>this.saveLoading = false)
        })
      },
      selectFinish(type, data) {
        if (type === 'project') {
          this.$set(this.tForm, 'projectName', data.projectName)
          this.$set(this.tForm, 'projectId', data.projectId)
        } else if (type === 'company') {
          this.$set(this.tForm, 'clientId', data.clientId)
          this.$set(this.tForm, 'cltName', data.companyName)
        }
        this.$refs[type].clearValidate()
      },
      startChange(){
        //延迟开启检测
        setTimeout(()=>{
          this.isChange = false
        }, 10)
      },
      currencyCodeChange(id){
        const currentData = this.currencyList.find(item => item.id === id)
        currentData && this.$set(this.tForm, 'rmbRate', currentData.rmbRate)
      },
      requestOperator(...otherId) { // 获取业务员
        const otherIds = [...new Set(otherId)].filter(_=>!!_).toString()
        Axios.post('operator/selectByMap', JSON2FormData({workTeamId: teamId, otherIds})).then(({data: res}) => {
          this.operatorList = res.map(item => ({id: Number(item.id), text: item.text}))
        }).catch(e => {
          this.$errorMsg('获取业务员失败！');
        })
      },
    },
    mounted() {
      this.businessTypeList = this.businessTypeList.filter(({id})=>id === this.businessTypeCode || id === (this.isPurchaseOrder ?　'YF_OTHER' : 'YS_OTHER'))
      this.requestOperator()
      this.requestCurrencyList()
      if (extend.from === 'settlement') return this.open(extend.mode, extend.data)
      frameId && this.$handleOperation(frameId, this.isPurchaseOrder ? 'Payables': 'receivables', _data => {
        const {
          data,
          mode,
          projectId,
          projectName,
          clientId,
          clientName
        } = _data
        if(this.show)return this.$message.info('弹窗被占用，请先处理当前弹窗！')
        mode && this.open(mode, data, {projectId, projectName, clientId, cltName: clientName})
      })
    },
    computed: {
      formName(){
        return this.isPurchaseOrder ? '应付款': '应收单'
      },
      title() {
        const suffix = this.formName
        switch (this.mode) {
          case 'add':
            return '新增' + suffix
          case 'read':
            return suffix
          case 'edit':
            return '编辑' + suffix
        }
      },
      selfAuthority(){
        const mode = this.mode
        const readonly = mode === 'read'
        const projectArchiveState = this.projectArchiveState
        return {
          readonly,  // 只读模式
          disabled: readonly || projectArchiveState, //禁用普通表单控件
          projectArchiveState,
          allowSelection: !readonly && !projectArchiveState, //是否允许选择项目或公司
        }
      }
    },
    watch: {
      mode(newValue){
        newValue === 'read' && setTimeout(()=>{
          this.$refs['body'].$el.scrollTo(0, 0)
          this.readonlyMarkHeight = this.$refs['body'].$el.scrollHeight - 40  + 'px'
        })
      },
      tForm:{
        //监听他改变了数据没
        handler(){
          if(!this.isChange)
            this.isChange = true
        },
        deep: true
      }
    },
    components: {
      CompanySelect,
      ProjectSelect,
      CustomDialog,
    },
  }
</script>

<style scoped>
  .el-input, .el-select {
    width: 100%;
  }

  .el-input .append {
    cursor: pointer;
  }

  .dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
  }

  .readonly-mark {
    position: absolute;
    top: 40px;
    left: 0;
    z-index: 99;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
  }

  .dialog-header .dialog-header-left {
    color: #F74444;
  }

  .dialog-header .dialog-header-left > span {
    padding: 0 30px;
    line-height: 29px;
  }

  .dialog-header .dialog-header-left > .el-button {
    border-radius: 0;
    background-color: #63A9FB;
    color: #fff !important;
  }

  .dialog-header .dialog-header-left > .el-button.is {
    background-color: #9FA9B3;
  }

  .el-form.tForm{
    padding: 20px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .el-form .el-form-item.el-form-item--mini {
    flex: 0 0 342px;
  }

  .is-disabled .el-input__inner{
    color: #999999;
  }
</style>