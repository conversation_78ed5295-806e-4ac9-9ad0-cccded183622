<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="60px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <!--<el-form-item label="项目名称" prop="projectName">
          <el-tooltip
              :content="'项目名称:' + searchForm.projectName"
              placement="bottom"
              :disabled="!searchForm.projectName"
              :open-delay="300">
            <el-input v-model="searchForm.projectName" placeholder="项目名称"  v-enter="search" clearable></el-input>
          </el-tooltip>
        </el-form-item>-->
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
              class="project"
              ref="project"
              v-model="projectIds"
              placeholder="选择项目名称"
              multiple
              collapse-tags
              popper-class="disallow-popper"
              clearable
              @hook:mounted="projectMultipleMounted"
              @change="selectChange"
              @clear="projectList = []"
              @click.native.stop="openProjectSelect"
          ><el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="举办年份" prop="yearMonth">
          <el-date-picker
              v-model="searchForm.yearMonth"
              format="yyyy"
              type="year"
              value-format="yyyy"
              placeholder="选择年份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="举办地点" prop="city">
          <el-input v-model="searchForm.city" placeholder="地点" v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="是否缴清" prop="balanceState">
          <el-select
              v-model="searchForm.balanceState"
              placeholder="是否缴清"
              clearable
              @clear="clear('balanceState')"
          >
            <el-option label="已缴清" :value="1"></el-option>
            <el-option label="未缴清" :value="2"></el-option>
            <el-option label="未缴" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
        <!--<el-form-item style="margin-bottom: 0;" prop="exhibitDate" label="开展时间">
          <el-date-picker
              v-model="searchForm.startTime"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable
              @clear="clear('startTime')">
          </el-date-picker>
          <el-date-picker
              v-model="searchForm.endTime"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable
              @clear="clear('endTime')">
          </el-date-picker>
        </el-form-item>-->
        <el-form-item style="margin-top: 5px;" prop="paymentDate">
          <template #label>
            销售统计时段
            <el-tooltip content="统计对应时间段的合同金额、订单金额、应收金额、展位号" placement="top">
              <i class="el-icon-info" style="color: #1890ff;"></i>
            </el-tooltip>
          </template>
          <el-date-picker
              v-model="searchForm.paymentDataStart"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable
              @clear="clear('paymentDataStart')">
          </el-date-picker>
          <el-date-picker
              v-model="searchForm.paymentDataEnd"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable
              @clear="clear('paymentDataEnd')">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-top: 5px;" prop="receiptBookDate">
          <template #label>
            收款日期
            <el-tooltip content="统计对应时间段的实收金额、结束日期时剩余尾款" placement="top">
              <i class="el-icon-info" style="color: #1890ff;"></i>
            </el-tooltip>
          </template>
          <el-date-picker
              v-model="searchForm.receiptBookDateStart"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable
              @clear="clear('receiptBookDateStart')">
          </el-date-picker>
          <el-date-picker
              v-model="searchForm.receiptBookDateEnd"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable
              @clear="clear('receiptBookDateEnd')">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-top: 5px;" prop="showProductKind">
          <el-checkbox v-model="searchForm.showProductKind">是否显示产品大类</el-checkbox>
        </el-form-item>
      </el-form>
      <div class="right-btn-box">
        <el-button v-if="getAuthority('fu_report_export')" size="mini" class="btn" icon="el-icon-upload2" @click="$emit('export')">导出</el-button>
      </div>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{pro.projectName}}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false" @finish="selectFinish"/>
  </el-main>
</template>

<script>
  const ProjectSelect = importC('ProjectSelect')
  export default {
    name: "ReceivableSummaryPanel",
    data() {
      return {
        pickerOptions,
        searchForm: {
          projectIds: '',
          projectName: '',
          // companyName: '',
          // ownerId: '',
          // ownerName: '',
          yearMonth:'',
          city:'',
          balanceState: '',
          // startTime: '',
          // endTime: '',
          receiptBookDateStart: '',
          receiptBookDateEnd: '',
          paymentDataStart: '',
          paymentDataEnd: '',
          showProductKind: '',
        },
        showProjectSelect: false,
        dataMap: {
          projectName: '项目名称',
          // companyName: '公司名称',
          // ownerName: '业绩归属人'
          yearMonth: '举办年份',
          city: '举办地点',
          balanceState: '是否缴清',
          // startTime: '开展时间起',
          // endTime: '开展时间止',
          receiptBookDateStart: '到款时间起',
          receiptBookDateEnd: '到款时间止',
          paymentDataStart: '销售统计时段起',
          paymentDataEnd: '销售统计时段止',
          showProductKind: '是否显示产品大类',
        },
        operatorList: [],
        projectList: [],
        projectIds: [],
        fixedPos: {
          left: '-999px',
          top: '-999px',
        },
        showMoreInfo: false
      }
    },
    mounted() {
      this.requestOperatorList()
      this.$set(this.searchForm, 'yearMonth', top.$GlobalDefine.getSystemTime(null).getFullYear().toString())
      // this.searchForm.projectId = projectId
      // this.searchForm.projectName = projectName
      // this.searchForm.companyName = clientName
      this.search(true)
      // this.requestDefault()
    },
    methods: {
      search(isFirst) {
        Object.keys(this.searchForm).forEach(key=> this.searchForm[key] === null && this.clear(key))
        let balanceStateLabel,searchValues = {
              projectIds: this.projectIds.toString(),
              // projectName:this.searchForm.projectName,
              yearMonth:this.searchForm.yearMonth,
              city:this.searchForm.city,
              balanceState:this.searchForm.balanceState,
              // startTime:this.searchForm.startTime,
              // endTime:this.searchForm.endTime,
              receiptBookDateStart: this.searchForm.receiptBookDateStart,
              receiptBookDateEnd: this.searchForm.receiptBookDateEnd,
              paymentDataStart: this.searchForm.paymentDataStart,
              paymentDataEnd: this.searchForm.paymentDataEnd,
              showProductKind: this.searchForm.showProductKind,
            },
            breadcrumbValues = {
              projectName:this.searchForm.projectName,
              yearMonth:this.searchForm.yearMonth,
              city: this.searchForm.city,
              balanceState: balanceStateLabel,
              // startTime:this.searchForm.startTime,
              // endTime:this.searchForm.endTime,
              receiptBookDateStart: this.searchForm.receiptBookDateStart,
              receiptBookDateEnd: this.searchForm.receiptBookDateEnd,
              paymentDataStart: this.searchForm.paymentDataStart,
              paymentDataEnd: this.searchForm.paymentDataEnd,
              showProductKind: this.searchForm.showProductKind ? '是' : void 0,
            },
            result = {
              requestData: {},
              breadcrumbList: []
            }
        switch (searchValues.balanceState) {
          case 1:
            breadcrumbValues.balanceState = '已缴清'
            break
          case 2:
            breadcrumbValues.balanceState = '未缴清'
            break
          case 3:
            breadcrumbValues.balanceState = '未缴'
            break
          default:
            breadcrumbValues.balanceState = ''
        }
        let breadcrumbKeys = Object.keys(breadcrumbValues)
        Object.keys(searchValues).forEach((k, index) => {
          let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
          if(bk === 'projectName'){
            bv = this.projectIds.map(projectId => {
              const p = this.projectList.find(item => projectId === item.projectId)
              if(!p) return ''
              return p.projectName
            }).join('、')
          }
          else if (!val && bk === 'showProductKind') return
          if (val !== false && !val) return
          result.requestData[k] = val
          result.breadcrumbList.push(`${this.dataMap[bk]}: ${typeof bv === 'string' ? bv.replace(/\s\d{2}:\d{2}:\d{2}$/, '') : bv}`)
        })
        this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
      },
      clear(...key) {
        key.forEach(k => this.$set(this.searchForm, k, ''))
      },
      selectFinish(rows, cover) {
        cover = true
        if(cover){
          this.projectList = rows
          this.projectIds = rows.map(({projectId}) => projectId)
          this.$nextTick(this.updateElement)
        }
      },
      openProjectSelect(){
        this.showProjectSelect = true
        this.$refs['projectSelect'].setNeedTopProjectIds(this.projectList.map(({projectId}) => projectId).toString()).requestTableData()
      },
      selectChange(value){
        if(Array.isArray(value)){
          this.projectList = this.projectList.filter(item => value.indexOf(item.projectId) !== -1)
          this.$nextTick(this.updateElement)
        }
      },
      updateElement(){
        const ref = this.$refs['project']
        if(!ref) return console.warn('updateElement: GG~')
        const $SEL = $(ref.$el)
        const $tagsWidth = $SEL.find('.el-select__tags').width()
        $SEL.css({width: $tagsWidth + 62 + 'px'})
        function getPosition(element) {
          let actualLeft = element.offsetLeft,
            actualTop = element.offsetTop,
            current = element.offsetParent; // 取得元素的offsetParent
          // 一直循环直到根元素
          while (current !== null) {
            actualLeft += current.offsetLeft;
            actualTop += current.offsetTop;
            current = current.offsetParent;
          }
          // 返回包含left、top坐标的对象
          return {
            left: actualLeft,
            top: actualTop
          }
        }
        if(ref.$children.length === 4){
          const lastTagEl = ref.$children[3].$el
          const $$ = this
          lastTagEl.onmouseenter = function () {
            $$.showMoreInfo = true
          }
          lastTagEl.onmouseleave = function () {
            $$.showMoreInfo = false
          }
          if(/\+\s\d{0,3}/.test(lastTagEl.innerText)){
            const {left} = getPosition(lastTagEl)
            this.fixedPos.left = lastTagEl.offsetWidth + left + 10 + 'px'
            this.fixedPos.top = '10px'
          }
        }
      },
      clearAll() {
        this.clear(...Object.keys(this.searchForm))
        this.projectList = []
        this.projectIds = []
        this.searchForm.projectIds = Object.create(null)
        this.searchForm.projectName = Object.create(null)
      },
      requestOperatorList() { // 获取业务员
        Axios.post('operator/selectByMap', JSON2FormData({workTeamId: teamId, showAllState: true})).then(({data: res}) => {
          this.operatorList = res.map(item => ({id: Number(item.id), text: item.text}))
        }).catch(e => {
          this.$errorMsg('获取业务员失败！');
        })
      },
      ownerChange(val) {
        if (!val) return
        this.$set(
            this.searchForm,
            'ownerName',
            (this.operatorList.find(i => i.id === val) || {})['text'] || ''
        )
      },
      projectMultipleMounted(){
        const inputInner = this.$refs.project.$el.querySelector('.el-input')
        const spanSuffixIcon = document.createElement('span')
        spanSuffixIcon.classList.add('el-icon-arrow-down','pm__arrow-down')
        inputInner.appendChild(spanSuffixIcon)
      },
      /*requestDefault(){
        Axios.post('/project/selectByExhibitionEndDate', JSON2FormData({workTeamId: teamId})).then(({data:res})=>{
          if(res.state !== 1 || !res.data)
            return this.search(true)
          const {projectId, projectName} = res.data
          this.$set(this.searchForm, 'projectId', projectId)
          this.$set(this.searchForm, 'projectName', projectName)
          this.search(true)
        })
      },*/
    },
    components: {
      ProjectSelect
    }
  }
</script>

<style scoped>
  .top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    min-width: 1290px;
    padding-left: 3px;
    padding-right: 3px;
  }

  .search-form-inline {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    max-width: 1400px;
  }

  .search-form-inline .el-form-item {
    margin: 0 15px 0 0;
  }

  .el-divider {
    margin: 10px auto;
  }

  .el-input {
    width: 140px;
  }


  .right-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-btn-box .el-button.btn {
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 0;
  }

  .right-btn-box .el-button.btn:hover {
    background-color: rgb(99, 169, 251);
  }
  .project--multiple .el-select{
    min-width: 140px;
  }
  .project--multiple .el-input {
    width: 100%;
  }
  .project--multiple .el-select__tags{
    max-width: unset!important;
    width: max-content !important;
    flex-wrap: nowrap;
  }
  .project--multiple .el-select .el-select__caret.el-input__icon.el-icon-arrow-up{
    display: none!important;
  }
  .project--multiple .el-select .el-input__suffix{
    right: 30px;
  }
  .project--multiple .el-select .pm__arrow-down{
    position: absolute;
    top: 0;
    right: 10px;
    height: 100%;
    color: #c0c4cc;
    line-height: 30px;
    text-align: center;
  }
  .project--multiple .el-tag .el-select__tags-text{
    display: inline-block;
    line-height: 1;
    max-width: 184px;
    overflow:hidden;
    white-space:nowrap;
    text-overflow: ellipsis;
  }
  .project--multiple .el-tag .el-tag__close.el-icon-close{
    right: -5px;
    top: -1px;
  }
  .more-info-popper{
    position: fixed;
    background: rgba(0, 0, 0, 0.75);
    border-radius: 2px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    z-index: 999;
    color: #fff;
    padding: 15px;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    flex-direction: column;
  }
  .more-info-popper > span{
    padding-top: 3px;
    padding-bottom: 3px;
    line-height: 20px;
    max-width: 225px;
    overflow:hidden;
    white-space:nowrap;
    text-overflow: ellipsis;
  }
</style>