<template>
  <el-main class="header-layout">
    <el-header class="top-header" height="auto">
      <el-form inline size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
            class="project"
            ref="project"
            v-model="projectIds"
            placeholder="选择项目名称"
            multiple
            collapse-tags
            popper-class="disallow-popper"
            clearable
            @hook:mounted="projectMultipleMounted"
            @change="selectChange"
            @clear="projectList = []"
            @click.native.stop="openProjectSelect"
          ><el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="searchForm.companyName" placeholder="公司名称" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <!--<el-form-item label="展位号" prop="boothNum">
          <el-input v-model="searchForm.boothNum" placeholder="展位号" v-enter="search" clearable></el-input>
        </el-form-item>-->
        <el-form-item label="部门" prop="branchId">
          <el-select ref="bsel" clearable v-model="searchForm.branchName" @clear="clear('branchId', 'branchName')">
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="departmentList"
                :props="{ children: 'children', label: 'text'}"
                highlight-current
                :default-expanded-keys="departmentList[0] ? [(departmentList[0]||{})['f_branch_id']] : null"
                node-key="id"
                @node-click="departmentChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购负责人" prop="ownerId">
          <el-select
            v-model="searchForm.ownerId"
            clearable
            filterable
            default-first-option
            @clear="clear('ownerId','ownerName')"
            @change="ownerChange"
            placeholder="采购负责人">
            <el-option
              v-for="item in operatorList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="right">
        <el-button type="primary" size="mini" icon="el-icon-search" @click="search">检索</el-button>
        <el-button plain size="mini" @click="$emit('combined-query')">组合查询</el-button>
      </div>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{pro.projectName}}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false" @finish="selectFinish"/>
  </el-main>
</template>

<script>
  const ProjectSelect = importC('ProjectSelect')
  export default {
    name: "SupplierSummaryHeader",
    mixins: [Mixins.requestOperator(), Mixins.ProjectMultipleSelect()],
    data() {
      return {
        searchForm: {
          projectIds: Object.create(null),
          projectName: Object.create(null),
          companyName: '',
          ownerId: '',
          ownerName: '',
          contractState: '',
          contractStateName: '',
          signTime: '',
          boothNum: '',
          // balanceState: '',
          haveReceipt: '',
          noBalance: '',
          paymentBookDateStart: '',
          paymentBookDateEnd: '',
          orderSignDateStart: '',
          orderSignDateEnd: '',
          onlyContract: '',
          onlyReceipt: '',
          // startTime: '',
          // endTime: '',
          branchId: '',
          branchName: ''
        },
        dataMap: {
          projectName: '项目名称',
          companyName: '公司名称',
          ownerName: '采购负责人',
          contractStateName: '合同状态',
          paymentBookDateStart: '到款时间起',
          paymentBookDateEnd: '到款时间止',
          orderSignDateStart: '采购统计时段起',
          orderSignDateEnd: '采购统计时段止',
          boothNum:'展位号',
          // balanceState: '是否缴清',
          haveReceipt: '是否到款',
          noBalance: '尾款已结',
          // startTime: '展会开始时间',
          // endTime: '展会结束时间',
          onlyContract: '只查时段内主合同生效的展商',
          onlyReceipt: '只显示时段内到款的展商',
          branchName: '部门'
        },
        operatorList:[],
        departmentList: [],
        contractStateList: Object.freeze([
          {id: 1, text: '有生效合同'},
          {id: 2, text: ' 已到款但合同未生效'},
          {id: 3, text: '全部合同已作废'},
          {id: 4, text: '无合同'},
        ]),
      }
    },
    async mounted(){
      await this.requestOperator()
      await this.getDepartmentList()
      delete extend.details
      if (frameId) {
        this.$handleOperation(frameId, 'supplierSummary', data => {
          const {projectId, projectName, clientName} = data
          projectId && projectName && this.selectFinish([{projectId, projectName}], true)
          this.$set(this.searchForm, 'companyName', clientName)
          this.search(true)
        })
      } else {
        this.requestDefault(() => {
          const projectId = extend.projectId
          const projectName = extend.projectName
          delete extend.projectId
          delete extend.projectName
          Object.assign(this.searchForm, extend)
          projectId && projectName && this.selectFinish([{projectId, projectName}], true)
        })
      }
    },
    methods: {
      search(isFirst) {
        const result = {
          requestData: {},
          breadcrumbList: []
        }
        // 设置请求参数
        const setRequestData = (value, key) => {
          if (key !== 'projectIds' && key !== 'projectName' && (value === null || value === '')) return null
          const form = this.searchForm
          let retValue = value
          switch (key) {
            case 'onlyContract':
            case 'onlyReceipt':
              retValue = value || null
              break
            case 'ownerId':
              retValue = form.ownerName
              break
            case 'contractState':
              retValue = form.contractStateName
              break
            case 'ownerName':
            case 'contractStateName':
            case 'branchName':
              return form[key]
            case 'projectIds':
              value = this.projectIds.toString()
              break
            case 'branchId':
              retValue = form.branchName
              break
            // 终止赋值
            case 'projectName':
              return this.projectIds.map(projectId => {
                const p = this.projectList.find(item => projectId === item.projectId)
                if(!p) return ''
                return p.projectName
              }).join('、') || null
          }
          result.requestData[key] = value
          return retValue
        }
        // 设置面包屑
        const setBreadcrumb = (value, key) => {
          const labelMap = this.dataMap
          let bLabel = labelMap[key]
          let bValue = value
          switch (key) {
            case 'onlyContract':
            case 'onlyReceipt':
              bValue = '__NO_SET__'
              break
            case 'haveReceipt':
            case 'noBalance':
              bValue = value !== '' ? value ? '是' : '否' : ''
              break
            case 'paymentBookDateStart':
            case 'paymentBookDateEnd':
            case 'orderSignDateStart':
            case 'orderSignDateEnd':
              bValue = String(value).slice(0, 10)
              break
          }
          if (!bLabel) return null
          const breadcrumb = bValue === '__NO_SET__' ? bLabel: `${bLabel}: ${bValue}`
          result.breadcrumbList.push(breadcrumb)
          return breadcrumb
        }
        Object.keys(this.searchForm).forEach(key => {
          const searchValue = setRequestData(this.searchForm[key], key)
          if (searchValue === null) return
          setBreadcrumb(searchValue, key)
        })
        this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
      },
      clear(...key) {
        key.forEach(k => this.$set(this.searchForm, k, ''))
      },
      requestDefault(mergeFun){
        Axios.post('/project/selectByExhibitionEndDate', JSON2FormData({workTeamId: teamId})).then(({data:res})=>{
          if(res.state !== 1 || !res.data){
            mergeFun && mergeFun()
            return this.search(true)
          }
          this.selectFinish([res.data], true)
          mergeFun && mergeFun()
          this.search(true)
        })
      },
      clearAll() {
        this.clear(...Object.keys(this.searchForm))
        this.projectList = []
        this.projectIds = []
        this.searchForm.projectIds = Object.create(null)
        this.searchForm.projectName = Object.create(null)
        this.packUp()
      },
      ownerChange(val) {
        if (!val) return
        this.$set(
          this.searchForm,
          'ownerName',
          (this.operatorList.find(i => i.id === val) || {})['text'] || ''
        )
      },
      contractStateChange(val) {
        if (!val)return
        this.$set(
          this.searchForm,
          'contractStateName',
          (this.contractStateList.find(i=> i.id === val) || {})['text'] || ''
        )
      },
      packUp(){
        this.isPackUp = true
      },
      async getDepartmentList() {
        try {
          const {data} = await Axios.post('/branch/getTree')
          this.departmentList = data
        } catch (e) {
          this.$errorMsg('获取部门列表失败！')
        }
      },
      departmentChange({id, text}) {
        this.$set(this.searchForm, 'branchId', id)
        this.$set(this.searchForm, 'branchName', text)
        this.clear('ownerId', 'ownerName')
        this.requestOperatorBase({branchId: id})
        this.$refs.bsel.blur()
      }
    },
    components: {
      ProjectSelect
    }
  }
</script>

<style>@import "../../../VueCommonComponents/header.css?v=240306";</style>