<template>
  <custom-dialog
      :show="show"
      @before-close="show=false"
      @closed="saveLoading=false"
      title="特殊定展规则"
      height="500"
      width="1000px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
              size="mini"
              type="primary"
              icon="el-icon-circle-check"
              :disabled="!selectIds"
              :loading="saveLoading"
              @click="save()"> 确定
          </el-button>
        </div>
      </el-header>
      <el-main :style="{padding: '2px 20px 20px 30px'}">
      <div class="demo-block">
        <el-table
          size="mini"
          max-height="100%"
          class="table-noline"
          :data="specialRuleList"
          ref="table"
          element-loading-text="数据加载中"
          element-loading-spinner="el-icon-loading"
          @row-click="r=>$refs['table'].toggleRowSelection(r)"
          @selection-change="tableSelectChange"
          v-loading="loading"
        >
          <el-table-column
              type="selection" fixed="left" width="45" ></el-table-column>
          <el-table-column
              prop="preorderStartTime" width="140" label="开始时间" resizable ></el-table-column>
          <el-table-column
              prop="preorderEndTime" width="140" label="结束时间" resizable ></el-table-column>
          <el-table-column
              prop="boothStatus"  width="80" label="可看/可订" resizable >
              <template slot-scope="{row}">
                {{ row.boothStatus ===1 ? '可订' : (row.boothStatus ===2 ? '可看' : '') }}
              </template>
          </el-table-column>
          <el-table-column width="80"
              prop="memberType" label="会员类型" resizable >
              <template slot-scope="{row}">
                {{ getMemberType('memberType',row) }}
              </template>
          </el-table-column>
          <el-table-column width="90"
              prop="memberGradeNames" label="会员等级" resizable >
              <!-- <template slot-scope="{row}">
                <div v-html="saleFormat('chapterImgName',row)"></div>
              </template> -->
          </el-table-column>
          <el-table-column width="80"
            prop="bookLimitMax" label="最大数量" resizable ></el-table-column>
          <el-table-column width="100"
            prop="preorderReserveDays" label="预定保留时间" resizable >
            <template slot-scope="{row}">
              {{ getOrderReserve(row) }}
            </template>
          </el-table-column>
          <el-table-column width="100"
            prop="contractReserveDays" label="合同保留时间" resizable >
            <template slot-scope="{row}">
              {{ getContractReserve(row) }}
            </template>
          </el-table-column>
          <el-table-column width="120"
            prop="sponsorConfirmStatus" label="只可订主办方确认的展商需求范围" resizable >
            <template slot-scope="{row}">
              {{ row.sponsorConfirmStatus ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column  width="120"
            prop="reserveLockOpen" label="预留展位全开放" resizable >
            <template slot-scope="{row}">
              {{ row.reserveLockOpen ? '是' : '否' }}
            </template>
          </el-table-column>
          <!-- <el-table-column
              label="操作" width="160" fixed="right" >
            <template slot="header" slot-scope="scope">
              <div style="text-align:center">操作</div>
            </template>
            <template slot-scope="{row}">
              <el-link
                :underline="false" type="primary" @click.stop="salePreview(row)"
              >预览</el-link>
              <span style="color:#e9e9e9;padding:0 5px">|</span>
              <el-link
                :underline="false" type="primary" @click.stop="saleEdit(row)"
              >修改</el-link>
              <span style="color:#e9e9e9;padding:0 5px">|</span>
              <el-link
                  :underline="false" type="primary" @click.stop="saleDel(row)"
              >删除</el-link>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      </el-main>
    </el-main>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  export default {
    name: "ExhibitorManageSpecialRule",
    data() {
      return {
        saveLoading: false,
        loading: false,
        show: false,
        boothIds: '',
        selectIds: '', // 当前界面选择的规则ids , 单向
        specialRuleList: [],
        multi: false,
      }
    },
    inject: ['projectId','exhibitCode','orgnum', 'projectIdComputed','exhibitCodeComputed'],
    mounted() {
      // this.tForm.projectId = this.projectId
      // this.getSpecialRules()
    },
    computed: {
      projectIdC() { // 兼容动态 projectId
        return this.projectId || this.projectIdComputed();
      },
      exhibitCodeC() { // 兼容动态 exhibitCode
        return this.exhibitCode || this.exhibitCodeComputed();
      },
    },
    methods: {
      tableSelectChange(selection){
        this.selectIds = selection.map(i => i.boothReserveRuleId).join(",")
      },
      getTimeMsg (type, val1, val2) {
        if (type == "HOUR") {
          return val1 ? parseInt(val1) + "小时" : "";
        } else if (type == "DAY") {
          return val2 ? parseInt(val2) + "天 (工作日)" : "";
        }
        return "";
      },
      getOrderReserve (row) {
        return this.getTimeMsg(
          row.preorderReserveType,
          row.preorderReserveHours,
          row.preorderReserveDays
        );
      },
      getContractReserve (row) {
        return this.getTimeMsg(
          row.contractReserveType,
          row.contractReserveHours,
          row.contractReserveDays
        );
      },
      getMemberType(row) {
        let tmp = ''
        // 1，客户帐号, 2，本公司帐号 3，匿名游客
        switch (row.memberType) {
          case 1:
            tmp = '客户帐号';break;
          case 2:
            tmp = '本公司帐号';break;
          case 3:
            tmp = '匿名游客';break;
          default:
            break;
        }
      },
      async openIds(selectIds) { // 单个选择
        this.show = true
        this.selectIds = ''
        this.boothIds  = ''
        this.multi = false
        await this.getSpecialRules(selectIds)
      },
      async open(boothIds) {    // 批量操作
        if (!boothIds) return this.$errorMsg('数据异常！请刷新重试！')
        this.selectIds = ''
        this.boothIds  = boothIds
        this.show  = true
        this.multi = true
        await this.getSpecialRules()
      },
      async save() {
        this.saveLoading = true
        try {
          if(this.multi){
            const {data: {state, msg, data}} = await Axios.post('/serveBooth/batchEnableBookRule', JSON2FormData({
              serveBoothIds: this.selectIds,
              boothRuleIdStr: this.boothRuleIds,
            }))
            if (state === 1) {
              this.$message.success('启用成功');
              this.show = false
              return this.$emit('operation-complete')
            }
            throw new Error('操作失败: ' + msg)
          }else{
            this.show = false
            return this.$emit('select-complete',this.selectIds || '')
          }
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg(e.message || '操作失败')
        } finally {
          this.saveLoading = false
        }
      },
      initTableSelect(idsStr){
        const idStr = idsStr + ','
        this.specialRuleList.forEach((row ,idx) => {
          if(idStr.includes(row.boothReserveRuleId + ',') ){
            this.$refs['table'].toggleRowSelection(row, true)
          }
        })
      },
      async getSpecialRules(idsStr) {
        try {
          const {data: {state, data: specialRuleList}} = await Axios.post('/boothRule/getList', JSON2FormData({
              memberType: '',
              memberTypes: '1,3',
              exhibitCode: this.exhibitCodeC,
              projectId: this.projectIdC,
              orgNum: this.orgnum,
            }));
          if (state !== 1) await Promise.reject('预定规则加载失败！')
          if(idsStr) { // 排序
            let tmp = []
            idsStr.split(',').map(i => {
              tmp.push(specialRuleList.find(i2 => i2.boothReserveRuleId == i))
            })
            const idStr = idsStr + ','
            specialRuleList.map(i => {
              if(!(idStr.includes(i.boothReserveRuleId))){
                tmp.push(i)
              }
            })
            this.specialRuleList = tmp
            this.$nextTick( _ =>{
              this.initTableSelect(idsStr)
            })
          }else{
            this.specialRuleList = specialRuleList
          }
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg(e)
        }
      }
    },
    components: {
      CustomDialog
    }
  }
</script>

<style scoped>
  .demo-block .table-noline.el-table thead tr,
  .demo-block .table-noline.el-table thead th{
    background-color: #fafafa;
  }
  .demo-block {
    border: 1px solid #ebebeb;
    border-width: 1px 1px 0;
    border-radius: 2px;
  }
  .dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
    max-height: 65vh;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    /* box-shadow: 0 0 4px #ccc; */
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
  }
</style>