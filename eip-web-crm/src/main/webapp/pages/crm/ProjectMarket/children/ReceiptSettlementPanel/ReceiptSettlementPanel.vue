<template>
  <div v-loading.fullscreen.lock="exporting"
       element-loading-text="正在导出..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.1)">
    <receipt-settlement-header ref="header" @search="search"></receipt-settlement-header>
    <receipt-settlement-body
      ref="body"
      @check-batch="checkBatch"
      @import="import_"
      @export="export_"
      @table-select="tableSelect"
      @clear-search="clearSearch"
      @read-row="openDetails"
      @open-market="openProjectMarket"
    ></receipt-settlement-body>
    <receipt-settlement-check ref="check"></receipt-settlement-check>
    <receipt-settlement-details
      ref="details" @check="checkBatch"
      @refresh-body="refreshBody"
      @open-market="openProjectMarket"></receipt-settlement-details>
  </div>
</template>

<script>
const ReceiptSettlementDetails = importC('ReceiptSettlementDetails')
const ReceiptSettlementCheck = importC('ReceiptSettlementCheck')
const ReceiptSettlementHeader = importC('ReceiptSettlementHeader')
const ReceiptSettlementBody = importC('ReceiptSettlementBody')
export default {
  name: "ReceiptSettlementPanel",
  provide() {
    return {
      checkStatus: [
        {label: '未审核', value: 0},
        {label: '已审核', value: 3},
        {label: '已补审核', value: 2},
        {label: '材料待补', value: 1},
      ],
      getCheckStatusLabel(checkState) {
        return (this.checkStatus.find(i => i.value === +checkState) || {})['label']
      },
      getCheckStatusDotColor(checkState) {
        return ['#d9d9d9', '#f2b21c', '#52c41a', '#52c41a'][+checkState]
      },
      openCheckDialog: (...args) => this.$refs['check'].open(...args),
      getSettlementFileList: () => this.settlementFileList
    }
  },
  data() {
    return {
      exporting: false,
      tableSelection: [],
      show: false,
      toFixedFields: ['settlementAmount'],
      settlementFileList: [],
    }
  },
  mounted() {
    this.getSettlementFile()
  },
  methods: {
    fn() {
      return this.$developMessage()
    },
    async checkBatch(_checkState, _list, isDetails, oldSettlementFileIds) {
      try {
        const actionName = _checkState === 0 ? '弃审' : '审核'
        const list = _list || this.tableSelection || [];
        if (!_list) {
          if (list.length < 1) await Promise.reject(`请选择需要${actionName}的数据`)
          await this.$confirm(`确定要${actionName}这${list.length}条数据吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        }
        let checkState, settlementFileIds
        if (isDetails) {
          if (_checkState !== null) {
            const result = await this.$refs['check'].open(_checkState, oldSettlementFileIds)
            checkState = result.checkState
            settlementFileIds = result.settlementFileIds || ''
          } else {
            checkState = 0
            settlementFileIds = ''
          }
        } else {
          if (_checkState !== 0) {
            const result = await this.$refs['check'].open(_checkState)
            checkState = result.checkState
            settlementFileIds = result.settlementFileIds || ''
          }
        }
        const {data} = await Axios.post('/fnReceiptSettlement/check', JSON2FormData({
          checkState,
          settlementFileIds,
          settlementIds: list.map(it => it['settlementId']).join(),
          operEntrance: '收款单结算明细审核',
        }))
        if (data.state !== 1) await Promise.reject(data.msg || data.message)
        this.$message.success({
          message: '操作成功',
          showClose: true
        })
        if (isDetails) return this.$refs.details.refresh()
        this.$refs.body.refresh()
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '审核失败')
      }
    },
    import_() {
      this.$refs['import'].open()
    },
    //导出
    export_() {
      const $body = this.$refs['body']
      const $mainFields = $body.fields
      const propList = $mainFields.map(it => it.prop)
      const labelList = $mainFields.map(it => it.label)
      const tHead = ['序号', ...labelList]
      const exportList = this.tableSelection.map((item, index) => {
        let temp = {index: index + 1}
        propList.forEach(prop => {
          let v = item[prop]
          if (typeof v === 'boolean') {
            if (prop === 'isChecked') {
              v = v ? '已审核' : '未审核'
            } else {
              v = v ? '是' : '否'
            }
          } else if (this.toFixedFields.includes(prop)) {
            v = this.$toFixed2(v)
          }
          temp[prop] = v
        })
        return temp
      })
      if (exportList.length < 1)
        return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          .then(() => this.exportAll())
          .catch(e => e)
      // let amounts = 0
      // const exp = exportList.map(item => {
      //   const {amount} = item//对应表格金额字段
      //   amounts += Number(amount) || 0
      //   const res = {0: item.index}
      //   $mainFields.forEach((it, index) => res[index + 1] = item[it.prop] || '')
      //   return res
      // })
      // let arr = ['合计']
      // for(let i=0;i<propList.length;i++){arr.push('')}
      // propList.forEach((it,idx)=>{
      //   if(it.includes('amount')) arr.splice(idx+1,0,this.$toFixed2(amounts))
      // })
      // // exp.push(Object(['合计',this.$toFixed2(amounts)]))
      // exp.push(Object(arr))
      // export2Excel(tHead, exp, `收款单${Date.now()}`)
      const initSum = {amount: 0}
      const summation = exportList.reduce((prev, current) => {
        const result = {}
        Object.keys(prev).forEach(key => result[key] = prev[key] + (+current[key] || 0))
        return result
      }, initSum)
      const summationRow = {}
      Object.keys(exportList[0]).forEach(key => {
        if (key === 'index') return (summationRow[key] = '合计')
        summationRow[key] = summation[key] || ''
      })
      exportList.push(summationRow)
      exportObject2Excel({
        header: tHead,
        body: exportList,
        title: '销售订单',
        fileName: '收款单',
        bodyFormat: {
          amount: '0\\.00',
          amountBalanced: '0\\.00'
        }
      })
      this.$message.success('导出成功！')
    },
    exportAll() {
      this.exporting = true
      const $body = this.$refs['body']
      const $mainFields = $body.fields
      const amounts = $body.amountRmb
      const propList = $mainFields.map(it => it.prop)
      const labelList = $mainFields.map(it => it.label)
      const tHead = ['序号', ...labelList]
      const data = {...$body.requestHistory, importList: true}
      delete data.rows
      delete data.page
      Axios.post('/fnReceipt/getPage', JSON2FormData(data)).then(({data: res}) => {
        if (res.state !== 1) return this.$errorMsg('导出数据失败！')
        if (!Array.isArray(res.rows) || res.rows.length === 0)
          return this.$errorMsg('没有可导出的数据！')
        const exportData = res.rows.map((item, _index) => {
          const res = {index: _index + 1}
          $mainFields.forEach((it, index) => {
            let v = item[it.prop]
            if (typeof v === 'boolean') {
              if (it.prop === 'isChecked') {
                v = v ? '已审核' : '未审核'
              } else {
                v = v ? '是' : '否'
              }
            } else if (this.toFixedFields.includes(it.prop)) {
              v = this.$toFixed2(v)
            }
            res[it.prop] = v || ''
          })
          return res
        })
        // let arr = ['合计']
        // for(let i=0;i<propList.length;i++){arr.push('')}
        // propList.forEach((it,idx)=>{
        //   if(it.includes('amount')) arr.splice(idx+1,0,this.$toFixed2(amounts))
        // })
        // // exportData.push(Object(['合计', this.$toFixed2(amounts)]))
        // exportData.push(Object(arr))
        // export2Excel(tHead, exportData, `收款单_ALL_${Date.now()}`)
        // this.exporting = false
        // this.$message.success('导出成功！')
        const initSum = {amount: 0}
        const summation = exportData.reduce((prev, current) => {
          const result = {}
          Object.keys(prev).forEach(key => result[key] = prev[key] + (+current[key] || 0))
          return result
        }, initSum)
        //这里是为了对齐表格格式
        // const summationRow = {}
        // Object.keys(exportData[0]).forEach(key=>{
        //   // 这是给合计行添加一个序号
        //   if(key==='index')return (summationRow[key] = '合计')
        //   summationRow[key] = summation[key] || ''
        // })
        // exportData.push(summationRow)
        let lastSum = ['总记', (document.getElementsByClassName('footer')[0].innerText || '').replaceAll('\n', '&nbsp;&nbsp;&nbsp;')]
        exportObject2Excel({
          header: tHead,
          body: exportData,
          title: '收款单',
          fileName: `收款单-导出全部`,
          lastSum,
          bodyFormat: {
            amount: '0\\.00'
          }
        })
        this.$message.success('导出成功！')
        this.exporting = false
      }).catch(e => {
        this.exporting = false
        this.$errorMsg('导出数据失败！')
      }).finally(() => this.exporting = false)
    },
    search(data, isFirst) {
      return this.$refs['body'].search(data, isFirst)
    },
    tableSelect(rows) {
      this.$set(this, 'tableSelection', rows)
    },
    clearSearch() {
      this.$refs['header'].clearAll()
    },
    openDetails(settlementId) {
      this.$refs.details.open(settlementId)
    },
    openProjectMarket(projectName, page, id) {
      const fu_crm_project_contract = getAuthority('fu_crm_project_contract')
      const fu_crm_project_receipt = getAuthority('fu_crm_project_receipt')
      const fu_crm_project_receivables = getAuthority('fu_crm_project_receivables')
      const fu_contract_Mod_crm = getAuthority('fu_contract_Mod_crm')
      const fu_crm_receipt_mod = getAuthority('fu_crm_receipt_mod')

      let mode = 'read'
      let title = ''
      switch (page) {
        case 'contract':
          title = '销售合同-' + projectName
          if (!fu_crm_project_contract) return
          mode = fu_contract_Mod_crm ? 'edit' : 'read'
          break
        case 'receipt':
          if (!fu_crm_project_receipt) return
          title = '收款单-' + projectName
          mode = fu_crm_receipt_mod ? 'edit' : 'read'
          break
        case 'receivables':
          if (!fu_crm_project_receivables) return
          title = '应收单-' + projectName
          break
      }
      const extend = btoa(encodeURIComponent(JSON.stringify({mode, data: id, from: 'settlement'})))
      if (!projectName) title = title.slice(0, -1)
      FinalLinkOpen('ProjectMarket/sales-contract.html?teamId=' + teamId + '&page=' + page + '&extend=' + extend, null, true, false, title)
    },
    refreshBody() {
      this.$refs.body && this.$refs.body.refresh()
    },
    async getSettlementFile() {
      try {
        const {data} = await Axios.post('/settlementFile/getList')
        this.settlementFileList = data.rows || []
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
        this.settlementFileList = []
      }
    }
  },
  components: {
    ReceiptSettlementDetails,
    ReceiptSettlementCheck,
    ReceiptSettlementHeader,
    ReceiptSettlementBody,
  }
}
</script>
