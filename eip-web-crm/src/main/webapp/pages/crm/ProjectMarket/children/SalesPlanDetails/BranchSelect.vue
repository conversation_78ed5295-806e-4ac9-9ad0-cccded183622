<template>
  <custom-dialog
      :show="show"
      @before-close="close"
      :title="title"
      width="430px"
      append2body>
    <el-main slot="body" class="dialog-body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right" style="flex: none">
          <el-button size="mini" type="primary" @click="confirm()">确定</el-button>
        </div>
      </el-header>
      <el-container style="padding: 20px;">
        <el-table
            border
            class="branch-table"
            max-height="300px"
            size="mini"
            @select="checkboxSelect"
            @select-all="selectAll"
            @row-click="rowClick"
            @row-dblclick="confirm($event)"
            ref="branch-table"
            :data="departmentList"
            style="width: 100%">
          <el-table-column
              width="50"
              label="序号"
              type="index">
          </el-table-column>
          <el-table-column
              width="50"
              type="selection">
          </el-table-column>
          <el-table-column
              prop="f_branch_name"
              label="部门名称">
          </el-table-column>
        </el-table>
      </el-container>
    </el-main>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  export default {
    name: "BranchSelect",
    props: {
      title: {
        type: String,
        default: '选择部门'
      },
      multiple: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        show: false,
        promise: null,
        departmentList: []
      }
    },
    methods: {
      async open(args) {
        args = args || {}
        await this.getDepartmentList(args)
        this.show = true
        return new Promise((resolve, reject) => this.promise = {resolve, reject})
      },
      confirm(rows) {
        const result = rows ? [rows] : this.$refs['branch-table'].selection
        if (!result.length)
          return this.$errorMsg('请至少选择一条数据', '提醒', 'warning')
        this.$emit('result', result)
        this.promise && this.promise.resolve(result)
        this.close(!0)
      },
      close(noReject) {
        this.show = !1
        noReject && this.promise && this.promise.reject()
        this.promise = null
      },
      clearAll() {
        !this.multiple && this.$refs['branch-table'].clearSelection();
      },
      selectAll() {
        this.clearAll()
      },
      select(row) {
        this.$refs['branch-table'].toggleRowSelection(row, this.multiple ? undefined : !!1);
      },
      rowClick(row) {
        if ( this.multiple) {
          this.$refs['branch-table'].clearSelection()
          this.$refs['branch-table'].toggleRowSelection(row, true)
        } else {
          this.clearAll()
          this.select(row)
        }
      },
      checkboxSelect(selection, row) {
        this.clearAll()
        !this.multiple && this.select(row)
      },
      async getDepartmentList(args) {
        try {
          const {data} = await Axios.post('/branch/getList', JSON2FormData({...args}))
          if (data.state !== 1) await Promise.reject()
          this.departmentList = data.data
        } catch (e) {
          this.$errorMsg('获取部门列表失败！')
        }
      },
    },
    components: {
      CustomDialog
    }
  }
</script>

<style scoped>
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
  }

  .dialog-header .dialog-header-left {
    color: #63A9FB;
  }

  .dialog-header .dialog-header-left > span {
    padding: 0 30px 0 5px;
    line-height: 29px;
  }

  .dialog-header .dialog-header-right i.el-icon-plus {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
  }

  .dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
  }


  .branch-table .el-table__header th {
    background-color: #63A9FB;
    color: #fff !important;
    text-align: center;
  }

  .branch-table .el-table__row tr {
    padding: 5px 0;
  }

  .branch-table .el-table__row > *:not(:last-of-type) {
    text-align: center;
  }

  .dialog-body i {
    color: inherit !important;
  }
</style>