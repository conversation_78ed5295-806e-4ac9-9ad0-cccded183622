<template>
  <div
    v-loading.fullscreen.lock="exporting"
    element-loading-text="正在导出..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.1)"
  >
    <projclt-report-header
      ref="header"
      @search="search"
    ></projclt-report-header>
    <projclt-report-body
      ref="body"
      @clear-search="clearSearch"
      @export="exportAll"
    >
    </projclt-report-body>
  </div>
</template>

<script>
const ProjcltReportHeader = importC('ProjcltReportHeader')
const ProjcltReportBody = importC('ProjcltReportBody')
export default {
  name: "ProjcltReportPanel",
  data() {
    return {
      exporting: false,
    }
  },
  provide() {
    return {
      formatAmountList: this.formatAmountList,
    }
  },
  methods: {
    search(data, isFirst) {
      return this.$refs['body'].search(data, isFirst)
    },
    clearSearch() {
      this.$refs['header'].clearAll()
    },
    exportMapper(propList, item, index) {
      let temp = index !== void 0 ?{index: index + 1} : {}
      propList.forEach(prop => {
        let v = item[prop]
        if (String(prop).endsWith('List')) {
          v = this.formatAmountList(v, '<br/>')
        }
        temp[prop] = v
      })
      return temp
    },
    exportAll() {
      this.exporting = true

      const parseFields = fields => {
        fields = fields.filter(item => item.type !== 'expand')
        const propList = fields.map(it => it.prop)
        const labelList = fields.map(it => it.label)
        return {
          propList,
          labelList,
        }
      }

      try {
        const $body = this.$refs['body']
        const data = $body.tableData
        const mainTableFields = parseFields($body.fields)
        const childTableFields = parseFields($body.prodFields)
        const mainTableData = [...data, $body.tableDataAll].map((item, index) => {
          const result = this.exportMapper(mainTableFields.propList, item, index)
          if (Array.isArray(item.productList) && item.productList.length) {
            result.$$childTable = {
              header: childTableFields.labelList,
              body: item.productList.map(cItem => this.exportMapper(childTableFields.propList, cItem))
            }
          }
          return result
        })
        exportObject2Excel({
          header: ['序号',...mainTableFields.labelList],
          body: mainTableData,
          title: '项目客户产品收支明细',
          fileName: '项目客户产品收支明细'
        })
        this.$message.success('导出成功')
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
      }
      this.exporting = false

    },
    formatAmountList(list, joinChar = '\n') {
      if (!Array.isArray(list)) return ''
      return list.map(item => item.amountStr).join(joinChar)
    }
  },
  components: {
    ProjcltReportHeader,
    ProjcltReportBody,
  }
}
</script>
