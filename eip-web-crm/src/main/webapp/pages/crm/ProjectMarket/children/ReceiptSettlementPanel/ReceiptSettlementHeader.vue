<template>
  <el-main class="header-layout">
    <div v-show="!isPackUp" class="placeholder"></div>
    <el-header class="top-header" :class="{'pack-down': !isPackUp}" height="auto">
      <el-form inline size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item prop="gapDate" label="提成账期" >
          <el-date-picker
            size="mini"
            v-model="searchForm.accountPeriod"
            type="month"
            placeholder="提成账期"
            format="yyyy年M月"
            value-format="yyyy-MM">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
            class="project"
            ref="project"
            v-model="projectIds"
            placeholder="选择项目名称"
            multiple
            collapse-tags
            popper-class="disallow-popper"
            clearable
            @hook:mounted="projectMultipleMounted"
            @change="selectChange"
            @clear="projectList = []"
            @click.native.stop="openProjectSelect"
          >
            <el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称" prop="cltName">
          <el-input v-model="searchForm.cltName" placeholder="公司名称" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="业绩归属部门" prop="branchId">
          <el-select
            size="mini"
            v-model="searchForm.branchId"
            placeholder="全部部门"
            clearable ref="el-select-tree"
            popper-class="el-select-tree">
            <el-option :value="branchSelect.id" :label="branchSelect.text">
              <el-tree
                :data="departs"
                ref="tree"
                node-key="f_branch_id"
                :props="{ children: 'children', label: 'text'}"
                highlight-current
                :default-expanded-keys="departs[0] ? [(departs[0]||{})['f_branch_id']] : null"
                @node-click="branchSelectClick">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业绩归属人" prop="ownerId">
          <el-select
            v-model="searchForm.ownerId"
            clearable
            filterable
            default-first-option
            @clear="clear('ownerId','ownerName')"
            @change="ownerChange"
            placeholder="业绩归属人">
            <el-option
              v-for="item in operatorList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态" prop="isChecked">
          <el-select
            v-model="searchForm.checkState"
            placeholder="审核状态"
            size="mini"
            clearable>
            <el-option v-for="item in checkStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-collapse-transition>
          <el-form-item label="产品名称" prop="prodName" v-if="!isPackUp">
            <el-autocomplete
              clearable
              highlight-first-item
              v-model="searchForm.prodName"
              :fetch-suggestions="queryProductSearch"
              placeholder="产品名称"
            ></el-autocomplete>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="收款记账日期" prop="bookDateStart" v-if="!isPackUp">
            <el-date-picker
              v-model="searchForm.bookDateStart"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable>
            </el-date-picker>
            <el-date-picker
              v-model="searchForm.bookDateEnd"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="结算时间" prop="createTimeStart" v-if="!isPackUp">
            <el-date-picker
              v-model="searchForm.createTimeStart"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable>
            </el-date-picker>
            <el-date-picker
              v-model="searchForm.createTimeEnd"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="审核时间" prop="checkTimeStart" v-if="!isPackUp">
            <el-date-picker
              v-model="searchForm.checkTimeStart"
              type="date"
              placeholder="开始时间"
              value-format="yyyy-MM-dd 00:00:00"
              clearable>
            </el-date-picker>
            <el-date-picker
              v-model="searchForm.checkTimeEnd"
              type="date"
              placeholder="结束时间"
              value-format="yyyy-MM-dd 23:59:59"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-collapse-transition>
      </el-form>
      <div class="right">
        <el-button type="primary" size="mini" icon="el-icon-search" plain @click="search">检索</el-button>
        <el-link class="pack-button" :class="{rotate: isPackUp}" :underline="false" @click="isPackUp=!isPackUp">
          {{ isPackUp ? '展开' : '收起' }} <i class="el-icon-arrow-up"></i></el-link>
      </div>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{ pro.projectName }}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false"
                    @finish="selectFinish"/>
  </el-main>
</template>

<script>
const ProjectSelect = importC('ProjectSelect')
export default {
  name: "ReceiptSettlementHeader",
  mixins: [Mixins.requestOperator(), Mixins.ProjectMultipleSelect()],
  inject: ['checkStatus'],
  data() {
    return {
      searchForm: {
        accountPeriod: '',
        projectIds: 'PLACEHOLDER',
        projectName: 'PLACEHOLDER',
        cltName: '',
        ownerId: '',
        ownerName: '',
        prodName: '',
        checkState: '',
        bookDateStart: '',
        bookDateEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        checkTimeStart: '',
        checkTimeEnd: '',
        branchId: '',
      },
      showProjectSelect: false,
      accountList: [],
      departs: [],
      branchSelect: {id: '',text: ''},
      dataMap: {
        accountPeriod: '提成账期',
        projectName: '项目名称',
        cltName: '公司名称',
        ownerName: '业绩归属人',
        prodName: '产品名称',
        checkState: '审核状态',
        bookDateStart: '收款记账时间起',
        bookDateEnd: '收款记账时间止',
        createTimeStart: '创建时间起',
        createTimeEnd: '创建时间止',
        checkTimeStart: '审核时间起',
        checkTimeEnd: '审核时间止',
        branchId: '部门',
      },
      isPackUp: true,
    }
  },
  watch: {
    'searchForm.branchId' () {
      this.branchChange();
    },
  },
  async mounted() {
    // this.searchForm.accountPeriod = DateAdd('M', -1, new Date()).format('yyyy-MM')
    await this.requestOperator();
    this.getDeparts();
    const projectId = extend.projectId
    const projectName = extend.projectName
    delete extend.projectId
    delete extend.projectName
    Object.assign(this.searchForm, extend)
    if (projectId && projectName) this.selectFinish([{projectId, projectName}], true)
    this.search(true)
  },
  methods: {
    fresh() {
      this.search(false);
    },
    search(isFirst) {
      const result = {
        requestData: {},
        breadcrumbList: []
      }
      const searchForm = this.searchForm
      // 设置请求参数
      const setRequestData = (value, key) => {
        if (value === null || value === '') return null
        let retValue = value
        switch (key) {
          case 'ownerId':
            retValue = searchForm.ownerName
            break
          case 'projectId':
            retValue = searchForm.projectName
            break
          case 'ownerName':
            return searchForm[key]
          case 'projectIds':
            value = this.projectIds.toString()
            break
          case 'projectName':
            return this.projectIds.map(projectId => {
              const p = this.projectList.find(item => projectId === item.projectId)
              if (!p) return ''
              return p.projectName
            }).join('、') || null
        }
        result.requestData[key] = value
        return retValue
      }
      // 设置面包屑
      const setBreadcrumb = (value, key) => {
        const labelMap = this.dataMap
        let bLabel = labelMap[key]
        let bValue = value
        switch (key) {
          case 'checkState':
            bValue = value !== '' ? (this.checkStatus.find(item => item.value === value) || {}).label : ''
            break
          case 'bookDateStart':
          case 'bookDateEnd':
          case 'createTimeStart':
          case 'createTimeEnd':
          case 'checkTimeStart':
          case 'checkTimeEnd':
            bValue = String(value).slice(0, 10)
            break
          case 'branchId':
            bValue = this.branchSelect.text || '';
            break
          case 'accountPeriod':
            bValue = value ? new Date(value).format('yyyy年M月') : ''
            break
        }
        if (!bLabel) return null
        const breadcrumb = `${bLabel}: ${bValue}`
        result.breadcrumbList.push(breadcrumb)
        return breadcrumb
      }
      Object.keys(this.searchForm).forEach(key => {
        const searchValue = setRequestData(this.searchForm[key], key)
        if (searchValue === null) return
        setBreadcrumb(searchValue, key)
      })
      // this.packUp() //收起
      this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
    },
    clear(...key) {
      key.forEach(k => this.$set(this.searchForm, k, ''))
    },
    clearAll() {
      this.clear(...Object.keys(this.searchForm))
      this.projectList = []
      this.projectIds = []
      this.searchForm.projectIds = Object.create(null)
      this.searchForm.projectName = Object.create(null)
      this.packUp()
    },
    ownerChange(val, key) {
      key = key || 'ownerName'
      if (!val) return
      this.$set(
        this.searchForm,
        key,
        (this.operatorList.find(i => i.id === val) || {})['text'] || ''
      )
    },
    packUp() {
      this.isPackUp = true
    },
    branchChange() {
      // 查询业务员
      // this.searchForm.operatorId = '';
      // this.requestOperatorBase({
      //   branchId: this.searchForm.branchId
      // });
    },
    branchSelectClick(data,node,objDom) {
      this.searchForm.branchId = data.f_branch_id;
      this.branchSelect = {
        id: data.f_branch_id,
        text: data.f_branch_name || '',
      }
      this.$refs['el-select-tree'].blur();
    },
    async getDeparts() {  // 部门树
      try {
        const {data} = await Axios.post('/branch/getTree').catch(_ => this.error())
        this.departs = data || [];
        // this.app2.$refs['app2'].departs = this.departs;
      } catch (e) {
        this.$errorMsg('加载部门数据失败！')
      }
    },
    async queryProductSearch(prodNameLike, cb) {
      try {
        const post = {
          prodNameLike,
          rows: 50,
          page: 1,
        }
        const cacheKey = prodNameLike || '$empty'
        window.__$ProductSearchCache = window.__$ProductSearchCache || {}
        if (window.__$ProductSearchCache[cacheKey]) return cb(window.__$ProductSearchCache[cacheKey])
        const {data} = await Axios.post('/product/select', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject()
        const result = (data.rows || []).map(item => ({...item, value: item.prodName}))
        if (result.length) window.__$ProductSearchCache[cacheKey] = result
        cb(result)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        return cb([])
      }
    }
  },
  components: {
    ProjectSelect
  }
}
</script>

<style>@import "../../../VueCommonComponents/header.css?v=240306";</style>

<style scope>
  .el-select-tree .el-scrollbar>.el-scrollbar__bar {
    opacity: 0 !important;
  }
  .el-select-tree .el-scrollbar .el-scrollbar__view .el-select-dropdown__item{
    height: auto;
    max-height: 274px;
     padding: 5px 0;
    overflow: hidden;
    overflow-y: auto;
  }
  .el-select-tree .el-select-dropdown__item.selected{
    font-weight: normal;
  }
  .el-select-tree ul li .el-tree .el-tree-node__content{
    height:auto;
    padding: 0 20px;
  }
  .el-select-tree .el-tree-node__label{
    font-weight: normal;
  }
  .el-select-tree .el-tree .is-current .el-tree-node__label{
    color: #409EFF;
    font-weight: 700;
  }
  .el-select-tree .el-tree .is-current .el-tree-node__children .el-tree-node__label{
    color:#606266;
    font-weight: normal;
  }
</style>