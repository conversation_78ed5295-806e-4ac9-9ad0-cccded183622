<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            class="text-over-hidden"
            style="max-width: 240px"
            :title="item"
            :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button
          v-if="getAuthority('fu_report_export')"
          size="mini" class="btn" icon="el-icon-upload2"
          @click="$emit('export')">导出
        </el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      show-summary
      :summary-method="getTableDataSum"
      height="calc(100vh - 200px)"
      border
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      operation-width="120px"
      @sort-change="sort"
      @selection-change="$emit('table-select', $event)"
      @header-dragend="onTableHeaderDragend"
      :handle-pagination="handlePagination"
      :total="total"
      :loading="loading">
      <template slot="salesAmountList" slot-scope="{row}">
        <span style="display:block;text-align:right;white-space: pre-wrap;">{{ formatAmountList(row.salesAmountList) }}</span>
      </template>
      <template slot="purchaseAmountList" slot-scope="{row}">
        <span style="display:block;text-align:right;white-space: pre-wrap;">{{ formatAmountList(row.purchaseAmountList) }}</span>
      </template>
      <template slot="surplusAmountList" slot-scope="{row}">
        <span style="display:block;text-align:right;white-space: pre-wrap;">{{ formatAmountList(row.surplusAmountList) }}</span>
      </template>
      <template slot="alex-operation" slot-scope="{row}">
        <el-link type="primary" :underline="false" @click.stop="gotoDetailPage(row)">查看明细</el-link>
      </template>
    </alex-table>
  </el-container>
</template>

<script>
const AlexTable = importC('AlexTable')
export default {
  name: "ServeBoothReportHeader",
  inject: ['formatAmountList'],
  data() {
    return {
      breadcrumbList: [],
      fields: [],
      total: 0,
      tableData: [],
      loading: false,
      requestHistory: {rows: 20, page: 1},
      tableDataAll: {},
    }
  },
  methods: {
    handlePagination({page, rows}) {
      this.requestHistory.page = page
      this.requestHistory.rows = rows
      this.requestTableData(this.requestHistory)
    },
    sort({column, prop, order}) {
      let data = this.requestHistory;
      if (order) {
        data.order = order.replace('ending', '')
        data.sort = prop.replace('List', '')
      } else {
        delete (data.order)
        delete (data.sort)
      }
      this.requestTableData(data)
    },
    clearSearch() {
      this.breadcrumbList = [];
      this.requestTableData({page: 1, rows: 20});
      this.$emit('clear-search')
    },
    createFields(callback) {
      this.fields = [
        {label: '项目名称', prop: 'projectName', width: 180, fixed: 'left', sortable: true},
        {label: '项目国家', prop: 'country', sortable: true},
        {label: '公司客户', prop: 'companyName', width: 180, sortable: true},
        {label: '展位类型', prop: 'boothTypeName', width: 120, sortable: true},
        {label: '展位面积(平米)', prop: 'boothArea', width: 120, sortable: true},
        {label: '展馆展区', prop: 'sectionName', width: 120, sortable: true},
        {label: '展位号', prop: 'boothCode', width: 100, sortable: true},
        {label: '折算标摊数', prop: 'qtyConverted', width: 120, sortable: true},
        {label: '设计师', prop: 'designer', width: 100, sortable: true},
        {label: '供应商', prop: 'supplier', width: 150, sortable: true},
        {label: '总采购金额', prop: 'purchaseAmountList', width: 200, sortable: true},
        {label: '总销售金额', prop: 'salesAmountList', width: 200, sortable: true},
        {label: '总盈余', prop: 'surplusAmountList', width: 200, sortable: true},
      ]
      callback && callback.call(this)
    },
    requestTableData(queryData = {}, callback = () => this.loading = false) {
      this.loading = true
      this.requestHistory = queryData
      if (!this.requestHistory.workTeamId)
        this.requestHistory.workTeamId = teamId
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      Axios.post('/statistics/projClientServeBooth', JSON2FormData(queryData)).then(({data: res}) => {
        if (res.state !== 1) return this.$errorMsg('数据加载失败')
        const {total, rows, data} = res
        this.total = total
        this.tableData = rows
        this.tableDataAll = data
        this.tableDataAll.salesAmountListStr = this.formatAmountList(data.salesAmountList)
        this.tableDataAll.purchaseAmountListStr = this.formatAmountList(data.purchaseAmountList)
        this.tableDataAll.surplusAmountListStr = this.formatAmountList(data.surplusAmountList)
        callback(this.$refs.table, res.rows)
      }).catch(() => {
        this.$errorMsg('数据加载失败')
        this.loading = false
      })
    },
    search(data, isFirst) {
      let postData = data.requestData
      let breadcrumbList = data.breadcrumbList
      postData.page = 1
      postData.rows = 20
      //breadcrumbList 面包屑导航
      this.breadcrumbList = breadcrumbList
      if (isFirst) {
        this.requestHistory = postData
        return this.updateColumnDisplay()
      }
      this.requestTableData(postData)
    },
    updateColumnDisplay() {
      this.createFields(() => {
        this.requestTableData(this.requestHistory, table => {
          this.loading = false
        })
      })
    },
    getTableDataSum(param) {
      const {columns} = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = this.tableDataAll[column.property + 'Str'] || this.tableDataAll[column.property];
      });
      return sums;
    },
    onTableHeaderDragend() {
      setTimeout(() => {
        this.$refs.table.doLayout2()
      }, 100)
    },
    gotoDetailPage({clientId, companyName, projectId, projectName}) {
      const payload = {
        clientId, companyName,
        projectId, projectName
      }
      const extend = btoa(encodeURIComponent(JSON.stringify(payload)))
      FinalLinkOpen('ProjectMarket/sales-contract.html?page=projcltReport&teamId=' + teamId + '&extend=' + extend, null, true, false, '项目客户产品收支明细-' + companyName)
    }
  },
  components: {AlexTable},
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.right {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.footer {
  display: flex;
}

.footer p {
  margin-right: 50px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.alex-table .el-table table[class^="el-table__"] tr >th[class^="el-table_"]:nth-last-child(2) .cell {
  text-align: left;
}

.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(2) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(3) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(4) .cell,
.alex-table .el-table__footer td[class^="el-table_"]:nth-last-child(5) .cell {
  text-align: right;
  white-space: pre-wrap;
}

</style>