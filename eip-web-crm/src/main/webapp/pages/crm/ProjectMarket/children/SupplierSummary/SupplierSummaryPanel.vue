<template>
  <div v-loading.fullscreen.lock="exporting"
       element-loading-text="正在导出..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.1)">
    <supplier-summary-header
      ref="header"
      @search="search"
      @combined-query="caller($refs.cQuery.open)"
    ></supplier-summary-header>
    <supplier-summary-main-body
      ref="body"
      @clear-search="clearSearch"
      @table-select="tableSelect"
      @export="export_"
    >
    </supplier-summary-main-body>
    <supplier-summary-combined-query ref="cQuery" @search="search"></supplier-summary-combined-query>
  </div>
</template>

<script>
  const SupplierSummaryHeader = importC('SupplierSummaryHeader')
  const SupplierSummaryMainBody = importC('SupplierSummaryMainBody')
  const SupplierSummaryCombinedQuery = importC('SupplierSummaryCombinedQuery')
  export default {
    name: "SupplierSummaryPanel",
    mixins: [Mixins.caller()],
    components:{
      SupplierSummaryCombinedQuery,
      SupplierSummaryHeader,
      SupplierSummaryMainBody,
    },
    data(){
      return {
        exporting: false,
        tableSelection: [],
        // toFixedFields:'contractAmount orderAmount receivable receipt balance invoiceFinished invoiceUnFinished'.split(' '),
        toFixedFields: [],
      }
    },
    mounted () {
      const that = this;
      vbus.$on('export-dues', function(batchId) {
        that.exportAllDone(batchId);
      });
    },
    methods:{
      search(data, isFirst){
        return this.$refs['body'].search(data, isFirst)
      },
      tableSelect(rows){
        this.$set(this,'tableSelection', rows)
      },
      clearSearch(){
        this.$refs['header'].clearAll()
      },
      selectTplHis(){
        this.$refs.ContractTplBatch.open(2);
      },
      goBatchDetail(batchId) {
        this.$refs.ContractTplBatchDetail.open(batchId);
      },
      exportAllDone(batchId) {
        this.$refs.ContractTpl.close();
        this.goBatchDetail(batchId);
      },
      exportAll2(rows) { // 全部导出 + 选完模板
        this.$refs.ContractTpl.openDues(rows);
      },
      async selectTpl(){ // 导出点击 : 选择导出 + 全部导出 + 单个导出
        const rows = this.tableSelection || [];
        if(rows.length === 1){ // 单个导出
          const tmp = rows[0];
          this.$refs.ContractTpl.openDue(tmp.clientId,tmp.projectId, tmp.companyName);
        }else if(rows.length>1) { // 批量导出
          // this.$message('暂只支持单项导出 !')
          await this.$confirm('确定要操作当前选中的' + rows.length + '条数据吗？','提示',{
            type: 'warning',
            dangerouslyUseHTMLString: true,
          })
          this.$refs.ContractTpl.openDues(rows.map(it=> ({
            pcid: it.projCltId,
            cid: it.clientId,
            pid: it.projectId,
            cname: it.companyName || '',
          })));
        }else if(rows.length < 1){ // 全部导出
          // await this.$confirm('是否删除当前选中的' + rows.length + '</font>条提成信息？','提示',{
          await this.$confirm('确定要操作当前' + this.$refs['body'].total + '条数据吗？','提示',{
            type: 'warning',
            dangerouslyUseHTMLString: true,
          })
          this.$refs['body'].exportAll();
        }
      },
      //导出
      export_(){
        const $mainFields = this.$refs['body'].fields
        const propListEx = [[],[]];
        let tHead,exportList,propList = [];
        $mainFields.map(it=>{
          if(it.children && it.children.length) {
            propListEx[0].push({
              label: it.label,
              colspan: it.children.length,
              rowspan: 1,
            })
            it.children.map(itt => {
              propList.push(itt.prop)
              propListEx[1].push({
                label: itt.label,
                colspan: 1,
                rowspan: 1,
              })
            })
          } else {
            propList.push(it.prop)
            propListEx[0].push({
              label: it.label,
              colspan: 1,
              rowspan: 2,
            })
          }
        })
        if(propListEx[1].length) { // 二级表头
          tHead = [[{
            label: '序号',
            colspan: 1,
            rowspan: 2,
           }, ...propListEx[0]], propListEx[1]];
        } else { // 一级表头
          propList = $mainFields.map(it=>it.prop)
          const labelList = $mainFields.map(it=>it.label)
          tHead = ['序号', ...labelList]
        }
        if (this.tableSelection.length < 1)
          return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
            .then(() => this.exportAll(tHead, propList))
            .catch(e => e)
        const rows = this.transformTableData(this.tableSelection.map(it => ({...it})))
        console.log(rows)
        this.exportDataAddSum(tHead, propList, rows, '项目供应商采购汇总', '项目供应商采购汇总')
      },
      exportDataAddSum(header,propList,rows,title,fileName) {
        const data = rows.map((item, _index)=>{
          const temp = [_index + 1]
          propList.forEach(prop=>{
            let v = item[prop]
            // if(this.toFixedFields.includes(prop)){
            //   v = this.$toFixed2(v)
            // }
            temp.push(v)
          })
          return temp
        })
        // 添加统计
        const exp = [''];
        const all = this.transformTableData([{...this.$refs['body'].tableDataAll}])[0] || {};
        propList.forEach(prop=>{
          let v = all[prop]
          // if(this.toFixedFields.includes(prop)){
          //   v = this.$toFixed2(v)
          // }
          exp.push(v)
        })
        data.push(exp)
        exportObject2ExcelWith2Head({
          header,
          data,
          title,
          fileName,
        })
        this.$message.success('导出成功！')
      },
      makeSumRow(sumMapper, propList, sumSuffix){
        const cols = propList.map(prop => {
          const value = sumMapper[prop + sumSuffix]
          return value === undefined ? '': this.$toFixed2(value)
        })
        return ['合计', ...cols]
      },
      exportAll(tHead,propList){
        this.exporting = true
        const $body = this.$refs['body']
        const data = {...$body.requestHistory}
        delete data.rows
        delete data.page
        Axios.post('/statistics/supplierSummary', JSON2FormData(data)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('导出数据失败！')
          this.exportDataAddSum(tHead, propList, this.transformTableData(res.rows), '项目供应商采购汇总', '项目供应商采购汇总-导出全部')
          this.exporting = false
        }).catch(()=> {
          this.exporting = false
          this.$errorMsg('导出数据失败！')
        })
      },
      transformTableData(rows) {
        if (!(rows && Array.isArray(rows))) return
        const tmp = [];
        rows.map(row => {
          Object.keys(row).map(field => {
            if (Array.isArray(row[field]) && row[field].length > 0) {
              row[field].map(row2 => {
                row[field + '_' + (row2.currencyCode || '')] = row2.amount || '';
              })
            }
          })
          tmp.push(row);
        })
        return tmp;
      }
    },
  }
</script>

