<template>
  <custom-dialog
      :show="show"
      @before-close="show=false"
      @closed="saveLoading=false"
      title="创建企业会员"
      width="570px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
              size="mini"
              type="primary"
              :loading="saveLoading"
              @click="saveForm()">
            {{saveLoading ? '生成中...': '确定'}}
          </el-button>
        </div>
      </el-header>
      <el-form
          class="tForm"
          ref="tForm"
          :model="tForm"
          label-width="150px"
          size="mini">
        <el-form-item prop="createEnterpriseMemberMode" label="企业会员生成方式" required>
          <div style="">
            <el-radio v-model="tForm.createEnterpriseMemberMode" :label=1>生成随机登陆名</el-radio>
            <el-radio v-model="tForm.createEnterpriseMemberMode" :label=2>以公司名称作为登陆名</el-radio>
            <!-- <el-radio v-model="tForm.createEnterpriseMemberMode" :label=3>手动输入登陆名</el-radio> -->
          </div>
        </el-form-item>
        <el-form-item prop="enterpriseMemberGradeId" label="企业会员等级">
          <div style="width: 300px">
            <el-select
                v-model="tForm.enterpriseMemberGradeId"
                clearable
                filterable
                default-first-option
                placeholder="企业会员等级">
              <el-option
                  v-for="item in memberGrades"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  export default {
    name: "ExhibitorManageCreateVipCorp",
    data() {
      return {
        saveLoading: false,
        show: false,
        tForm: {
          projectId: '',
          createEnterpriseMemberMode: 1,
          //
          serveBoothIds: '',
          enterpriseMemberGradeId: '',
        },
        queryParam: {},
        memberGrades: []
      }
    },
    inject: ['projectId'],
    mounted() {
      this.tForm.projectId = this.projectId || ''
    },
    methods: {
      open(data, paras) {
        if (!data) return this.$errorMsg('数据异常！请刷新重试！')
        // 手动inject : projectId
        if(paras && paras.projectId) this.tForm.projectId = paras.projectId || ''
        this.show = true
        this.getMemberGrades()
        this.tForm.serveBoothIds = ''
        // this.tForm.numberBindLabel = false
        this.queryParam = {}
        if (typeof data === 'string') {
          this.tForm.serveBoothIds = data
        } else if (typeof data === 'object') {
          const tmp = { ...data };
          delete tmp.page
          delete tmp.rows
          this.queryParam = tmp
        }
      },
      async saveForm() {
        this.saveLoading = true
        try {
          const {data: {state, msg, data}} = await Axios.post('/serveBooth/batchCreateEnterpriseMember', JSON2FormData({
            ...this.tForm,
            ...this.queryParam,
            operEntrance:'展商参展管理'
          }))
          if (state === 1) {
            let hintDOM = ''
            if(data.success === 0){
              hintDOM = `<div style="font-size: 12px;color: rgba(0,0,0,0.4);padding-top: 5px;line-height: 20px;">
                  以下情况可能会导致生成账号失败： <br/>
                  1.待生成会员的手机号和邮箱为空时。<br/>
                  2.待生成会员的手机号和邮箱已被其他公司生成账号。</div>`
            }
            this.$alert(
              `<span>已生成${data.successNum}个会员账号！</span>${hintDOM}`,
              '提醒', {
              type: 'success',
              dangerouslyUseHTMLString: true
            })
            this.show = false
            return this.$emit('operation-complete')
          }
          if(this.tForm.serveBoothIds.split(',').length === 1){
            await Promise.reject(msg || undefined)
          }
          await Promise.reject('未查询到手机/邮箱，请先在需求确认中输入手机/邮箱！')
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg(e)
        } finally {
          this.saveLoading = false
        }
      },
      async getMemberGrades() {
        try {
          const {data: {state, data: memberGrades}} = await Axios.post('/memberGrade/queryEnterpriseMemberGrade')
          if (state !== 1) await Promise.reject('企业会员等级加载失败！')
          // let newMemberDefaultGradeId = ''
          // this.memberGrades = memberGrades.map(({memberGradeId, memberGradeName, newMemberDefaultGrade}) => {
          //   if(!newMemberDefaultGradeId && newMemberDefaultGrade){
          //     newMemberDefaultGradeId = memberGradeId
          //   }
          //   return {
          //     id: memberGradeId,
          //     text: memberGradeName
          //   }
          // })
          // this.memberGrades.length > 0 && this.$set(this.tForm, 'save_memberGradeId', newMemberDefaultGradeId || '')
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg(e)
        }
      }
    },
    components: {
      CustomDialog
    }
  }
</script>

<style scoped>
  .el-input, .el-select {
    width: 100%;
  }

  .tForm {
    padding: 20px;
  }

  .dialog-body {
    overflow: auto;
    padding: 0;
    position: relative;
    max-height: 65vh;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
  }

  .create-vip-wrapper {
    text-align: center;
  }
  .fix-el-tooltip {
    border-radius: 2px;
    line-height: 1.5em;
  }
</style>