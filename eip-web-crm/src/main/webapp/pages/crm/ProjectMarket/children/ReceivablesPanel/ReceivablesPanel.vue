<template>
  <div>
    <receivables-header
      ref="header"
      @search="search"
      @del-batch="delBatch"
      @import="fn"

    ></receivables-header>
    <receivables-main-body
      ref="body"
      @add="openDetails('add')"
      @table-select="tableSelect"
      @clear-search="clearSearch"
      @export="export_"
      @edit-row="openDetails('edit', $event)"
      @read-row="openDetails('read', $event)"
    >
    </receivables-main-body>
    <receivables-details ref="details" @operation-complete="operationComplete"></receivables-details>
  </div>
</template>

<script>
  const ReceivablesHeader = importC('ReceivablesHeader')
  const ReceivablesMainBody = importC('ReceivablesMainBody')
  const ReceivablesDetails = importC('ReceivablesDetails')
  export default {
    name: "ReceivablesPanel",
    data(){
      return {
        tableSelection: [],
        show: false,
        toFixedFields: 'unSettlementAmount settlementAmount amount'.split(' '),
      }
    },
    methods:{
      fn(){
        return this.$developMessage()
      },
      search(data, isFirst){
        return this.$refs['body'].search(data, isFirst)
      },
      delBatch(){
        return this.$refs['body'].delBatch(this.tableSelection.map(v=>({receivablesId:v.receivablesId})))
      },
      tableSelect(rows){
        this.$set(this,'tableSelection', rows)
      },
      clearSearch(){
        this.$refs['header'].clearAll()
      },
      openDetails(mode, receivablesId=''){
        const initValues = {}
        if (mode === 'add') {
          const projectList = this.$refs.header.projectList
          if (projectList && projectList.length === 1) {
            const [{projectId, projectName}] = projectList
            initValues.projectId = projectId
            initValues.projectName = projectName
          }
        }
        this.$refs['details'].open(mode, receivablesId, initValues)
      },
      operationComplete(){
        const $body = this.$refs['body']
        $body.requestTableData($body.requestHistory)
      },
      exportMapper(propList, item, index) {
        let temp = {index: index + 1}
        propList.forEach(prop => {
          let v = item[prop]
          if (typeof v === 'boolean') {
            if (prop === 'isChecked') {
              v = v ? '已审核' : '未审核'
            } else {
              v = v ? '是' : '否'
            }
          } else if (this.toFixedFields.includes(prop)) {
            v = this.$toFixed2(v)
          } else if (prop === 'settlementState') {
            v = v === 1 ? '已结算' : '未结算'
          } else if (prop === 'businessTypeCode') {
            v = this.businessTypeCode2text(v)
          }
          temp[prop] = v
        })
        return temp
      },
      export_() {
        const $body = this.$refs['body']
        const $mainFields = $body.fields
        const propList = $mainFields.map(it => it.prop)
        const labelList = $mainFields.map(it => it.label)
        const tHead = ['序号', ...labelList]
        if (this.tableSelection.length < 1)
          return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
            .then(() => this.exportAll())
            .catch(Boolean)
        const exportList = this.tableSelection.map((item, index) => this.exportMapper(propList, item, index))
        const initSum = {amount: 0}
        const summation = exportList.reduce((prev, current) => {
          const result = {}
          Object.keys(prev).forEach(key => result[key] = prev[key] + (+current[key] || 0))
          return result
        }, initSum)
        const summationRow = {}
        Object.keys(exportList[0]).forEach(key => {
          if (key === 'index') return (summationRow[key] = '合计')
          summationRow[key] = summation[key] || ''
        })
        exportList.push(summationRow)
        top.$GlobalDefine.addExportDataLog({
          eventKind: 13,
          document,
          operObjName: `应收款共${exportList.length}条`,
          operEntrance: '应收款列表'
        })
        exportObject2Excel({
          header: tHead,
          body: exportList,
          title: '应收款',
          fileName: '应收款',
          bodyFormat: {
            amount: '0\\.00',
            amountBalanced: '0\\.00'
          }
        })
        this.$message.success('导出成功！')
      },
      exportAll() {
        this.exporting = true
        const $body = this.$refs['body']
        const $mainFields = $body.fields
        const propList = $mainFields.map(it => it.prop)
        const labelList = $mainFields.map(it => it.label)
        const tHead = ['序号', ...labelList]
        const data = {...$body.requestHistory, importList: true}
        delete data.rows
        delete data.page
        Axios.post('/fnReceivables/getPage', JSON2FormData(data))
          .then(({data}) => {
            if (data.state !== 1) return this.$errorMsg('导出数据失败！')
            if (!Array.isArray(data.rows) || data.rows.length === 0)
              return this.$errorMsg('没有可导出的数据！')
            top.$GlobalDefine.addExportDataLog({
              eventKind: 13,
              document,
              operObjName: `应收款共${data.total}条`,
              isAll: true,
              operEntrance: '应收款列表'
            })
            const exportData = data.rows.map((item, index) => this.exportMapper(propList, item, index))
            let lastSum = ['总记', (document.getElementsByClassName('footer')[0].innerText || '').replaceAll('\n', '&nbsp;&nbsp;&nbsp;')]
            exportObject2Excel({
              header: tHead,
              body: exportData,
              title: '应收款',
              fileName: '应收款-导出全部',
              lastSum,
              bodyFormat: {
                amount: '0\\.00'
              }
            })
            this.$message.success('导出成功！')
            this.exporting = false
          })
          .catch(() => this.$errorMsg('导出数据失败！'))
          .finally(() => this.exporting = false)
      },
      businessTypeCode2text(code){
        const businessTypeList = [
          {id: 'YF_CGDD', text: '采购订单应付'},
          {id: 'YS_XSDD', text: '销售订单应收'},
          {id: 'YF_OTHER', text: '其他应付'},
          {id: 'YS_OTHER', text: '其他应收'},
        ]
        const curType = businessTypeList.find(({id}) => id === code )
        return curType ? curType.text: ''
      }
    },
    components:{
      ReceivablesHeader,
      ReceivablesMainBody,
      ReceivablesDetails
    }
  }
</script>

<style scoped>

</style>