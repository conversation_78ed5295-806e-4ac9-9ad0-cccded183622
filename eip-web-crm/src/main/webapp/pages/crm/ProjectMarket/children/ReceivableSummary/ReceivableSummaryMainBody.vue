<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
              class="clear-search"
              @click.native="clearSearch"
              title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
              v-for="item in breadcrumbList"
              class="text-over-hidden"
              style="max-width: 240px"
              :title="item"
              :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" @click="showColumnDisplayDialog = true">列显示设置</el-button>
        <el-button size="mini" @click="saveColumnWidth()">保存列宽</el-button>
      </div>
    </el-header>
    <alex-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 215px)"
        border
        size="small"
        class="alex-table"
        mode="multiple"
        :fields="fields"
        @sort-change="sort"
        :padding="10"
        operation-width="120px"
        :handle-pagination="handlePagination"
        @selection-change="$emit('table-select', $event)"
        show-summary
        :summary-method="getTableDataSum"
        :total="total"
        :loading="loading">
      <!--<template  slot="contractAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.contractAmount | toFixed2}}</span>
      </template>
      <template slot="orderAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.orderAmount | toFixed2}}</span>
      </template>
      <template slot="receivable" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.receivable | toFixed2}}</span>
      </template>
      <template slot="receipt" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.receipt | toFixed2}}</span>
      </template>
      <template slot="balance" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.balance | toFixed2}}</span>
      </template>
      <template slot="invoiceFinished" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.invoiceFinished | toFixed2}}</span>
      </template>
      <template slot="invoiceUnFinished" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{row.invoiceUnFinished | toFixed2}}</span>
      </template>-->
      <template slot="alex-operation" slot-scope="{row}">
        <el-link style="display: block; text-align: center" :underline="false" type="primary" @click.stop="gotoPage(row, 'summary')">查看详情</el-link>
      </template>
    </alex-table>
    <column-display-dialog
      :show="showColumnDisplayDialog"
      :left-data="leftData"
      :right-data="rightData"
      :exclude="['projectName', 'companyName']"
      @reset="handleReset"
      @save="handleSave"
      @close="showColumnDisplayDialog=false">
    </column-display-dialog>
    <!-- <el-footer class="table-footer">
      <div v-for="summation in summationData" :key="summation.prop">
        <p v-if="summation.prop == 'boothAreaAmount'">{{summation.label}}：<span style="color: #409EFF">{{summation.data}}平方米</span></p>
        <p v-else-if="summation.prop == 'boothCountAmount'">{{summation.label}}：<span style="color: #409EFF">{{summation.data}}个</span></p>
        <p v-else>{{summation.label}}：<span style="color: #409EFF">{{summation.data | toFixed2}}元</span></p>
      </div>
    </el-footer> -->
  </el-container>
</template>

<script>
  const AlexTable = importC('AlexTable')
  export default {
    name: "ReceivableSummaryPanel",
    mixins: [Mixins.columnDisplay('projectReceivableSummary')],
    data() {
      return {
        breadcrumbList: [],
        total: 0,
        tableData: [],
        tableDataAll: {},
        loading: false,
        requestHistory: {rows: 20, page: 1},
        currencyField: {},
        dynamicPropMap: {},
        summationData: []
      }
    },
    methods: {
      handlePagination({page, rows}) {
        this.requestHistory.page = page
        this.requestHistory.rows = rows
        this.requestTableData(this.requestHistory)
      },
      sort({column, prop, order}) {
        let data = this.requestHistory;
        if (order) {
          data.order = order.replace('ending', '');
          data.sort = prop;
        } else {
          delete (data.order)
          delete (data.sort)
        }
        this.requestTableData(data)
      },
      clearSearch() {
        this.breadcrumbList = [];
        this.requestTableData({page: 1, rows: 20});
        this.$emit('clear-search')
      },
      createFieldsEx(callback) {
        this.createFields(() => {
          this.fields.forEach(item => {
            switch (item.prop) {
              case 'projectName':
                Object.assign(item, {fixed: 'left', minWidth: '240'})
                break
              case 'boothArea':
              case 'boothCount':
              case 'invoiceFinished':
              case 'invoiceUnFinished':
                Object.assign(item, {extraWidth: 30})
                break
            }
          })
          callback && callback.call(this)
        })
      },
      updateColumnDisplay() {
        this.createFieldsEx(() => {
          this.requestTableData(this.requestHistory, table => {
            table.computedWidth().then(() => this.loading = false)
          })
        })
      },
      buildDynamicFields(all) {
        // 删除产品大类
        let tempIndex
        while ((tempIndex = this.fields.findIndex(field => field.prop && field.prop.startsWith('productKind_'))) > 0) {
          this.fields.splice(tempIndex, 1)
        }
        const dynamicFields = [
          'balanceList',
          'contractAmountList',
          'discountPriceList',
          'invalidContractAmountList',
          'orderAmountList',
          'receiptList',
          'receivableList',
          'productKindList'
        ]
        const baseConfig = {
          minWidth: '120',
          headerAlign: 'center',
          align: 'right',
          sortable: 'custom',
          showOverflowTooltip: true,
        }
        this.dynamicPropMap = {}
        // clean old children columns
        this.fields.forEach(item => {
          if (dynamicFields.includes(item.prop)) {
            this.$set(item, 'children', [])
          }
        })
        dynamicFields.forEach(key => {
          const list = all[key]
          if (!Array.isArray(list) || !list.length) return
          const baseProp = key.replace('List', '')
          const setChildren = (_prop, list) => {
            _prop = _prop || baseProp
            const children = list.map((item) => {
              const {currencyName, currencyCode, fDatagridSetId, fShowWidth} = item
              const prop =  [_prop, currencyCode].join('_')
              this.dynamicPropMap[prop] = fDatagridSetId
              const fixedWidth = fShowWidth && fShowWidth > 30
              return {
                ...baseConfig,
                label: currencyName,
                fixedWidth: true,
                width: fixedWidth ? fShowWidth : 110,
                prop
              }
            })
            const fieldIndex = this.fields.findIndex(item => item.prop === _prop)
            if (fieldIndex !== -1){
              this.$set(this.fields[fieldIndex], 'sortable', false)
              this.$set(this.fields[fieldIndex], 'children', children)
            }
          }
          if (key === 'productKindList') {
            const groupByCode = {}
            list.forEach(item => {
              const {kindName, kindCode,  fDatagridSetId, fShowWidth} = item
              const prop = [baseProp, kindCode].join('_')
              if (!groupByCode[prop]) {
                groupByCode[prop] = []
                this.dynamicPropMap[prop] = fDatagridSetId
                const fixedWidth = fShowWidth && fShowWidth > 30
                const conf = {
                  label: kindName,
                  prop,
                  fixedWidth: true,
                  width: fixedWidth ? fShowWidth : 150,
                  sortable: false
                }
                this.fields.splice(-3, 0, conf)
              }
              groupByCode[prop].push(item)
            })
            Object.entries(groupByCode).forEach(([prop, list]) => setChildren(prop, list))
            return
          }
          setChildren(baseProp, list)
        })
        return row => {
          const newRow = {...row}
          dynamicFields.forEach(key => {
            const list = newRow[key]
            if (!Array.isArray(list) || !list.length) return
            const baseProp = key.replace('List', '')
            const getChildren = (_prop, list) => {
              _prop = _prop || baseProp
              list.forEach((item) => {
                const {currencyCode, amountStr} = item
                const prop = [_prop, currencyCode].join('_')
                newRow[prop] = amountStr || ''
              })
              return newRow
            }
            if (key === 'productKindList') {
              const groupByCode = {}
              list.forEach(item => {
                const {kindCode} = item
                const prop = [baseProp, kindCode].join('_')
                if (!groupByCode[prop]) groupByCode[prop] = []
                groupByCode[prop].push(item)
              })
              Object.entries(groupByCode).map(([prop, list]) => getChildren(prop, list))
              return
            }
            getChildren(baseProp, list)
          })
          return newRow
        }
      },
      getTableDataSum(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          sums[index] = this.tableDataAll[column.property];
        });
        return sums;
      },
      requestTableData(queryData = {}, callback = () => this.loading = false) {
        this.loading = true
        this.requestHistory = queryData
        if (!this.requestHistory.workTeamId)
          this.requestHistory.workTeamId = teamId
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        Axios.post('/statistics/getProjectReceivableSummary', JSON2FormData(queryData))
          .then(({data}) => {
            const {state, total, rows, data: {all}} = data
            if (state !== 1) return Promise.reject()
            this.total = total
            // const {contractAmount, orderAmount, receivable, balance, receipt, invoiceFinished, invoiceUnFinished,boothAreaAmount,boothCountAmount} = res.data
            const rowBuilder = this.buildDynamicFields(all)
            this.tableDataAll = rowBuilder(all || {})
            this.tableData = rows.map(row => rowBuilder(row))
            // this.currencyField = res.data.currencyField || {}
            // this.summationData = [
            //   {label: '展位总面积', prop: 'boothAreaAmount', data: boothAreaAmount || 0},
            //   {label: '展位总数量', prop: 'boothCountAmount', data: boothCountAmount || 0},
            //   {label: '合同总额', prop: 'contractAmount', data: contractAmount || 0},
            //   {label: '订单总额', prop: 'orderAmount', data: orderAmount || 0},
            //   {label: '应收总额', prop: 'receivable', data: receivable || 0},
            //   {label: '实收总额', prop: 'receipt', data: receipt || 0},
            //   {label: '尾款总额', prop: 'balance', data: balance || 0},
            //   {label: '已开发票总额', prop: 'invoiceFinished', data: invoiceFinished || 0},
            //   {label: '未开发票总额', prop: 'invoiceUnFinished', data: invoiceUnFinished || 0},
            // ]
            callback(this.$refs.table, rows)
          })
          .catch(e => {
            console.warn(e)
            this.$errorMsg('数据加载失败')
            this.loading = false
          })
      },
      search(data, isFirst) {
        let postData = data.requestData
        let breadcrumbList = data.breadcrumbList
        postData.page = 1
        postData.rows = 20
        //breadcrumbList 面包屑导航
        this.breadcrumbList = breadcrumbList
        if (isFirst) {
          this.requestHistory = postData
          return this.updateColumnDisplay()
        }
        this.requestTableData(postData)
      },
      gotoPage(row, page) {
        const {projectId, projectName} = row
        const req = {...this.requestHistory}
        delete req.page
        delete req.rows
        delete req.projectIds
        const extend = btoa(encodeURIComponent(JSON.stringify({...req, projectId, projectName, details: true})))
        const query =
          '?teamId=' + teamId +
          '&page=' + page +
          '&extend=' + extend
        let title = ''
        switch (page) {
          case  'summary':
            title = '项目财务汇总-' + projectName
        }
        FinalLinkOpen('ProjectMarket/sales-contract.html' + query, null, true, false, title)
      }
    },
    components: {
      AlexTable,
    },
    filters: {
      number2money(num) {
        if (!num) return '￥0'
        return '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
    }
  }
</script>

<style scoped>
  .el-main{
    padding-bottom: 0 !important;
  }
  .main-body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3px;
  }

  .alex-table {
    width: 100%;
    padding-top: 10px;
  }

  .el-table--small td, .el-table--small th {
    padding: 4px 0;
  }

  .table-footer {
    /*margin-top: 15px;*/
    height: 80px !important;
    padding: 5px 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;

  }
  .table-footer >div{
    margin-right: 45px;
    /*min-width: 200px;*/
  }
  .table-footer >div p {
    /*flex-basis: 250px;*/
    margin: 0;
  }

  .table-footer > p:nth-of-type(1) {
    text-align: left;
  }

  .alex-table .el-table th:not(.is-sortable) .cell{
    line-height: 30px;
  }
</style>