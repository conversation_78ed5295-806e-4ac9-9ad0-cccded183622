<template>
  <custom-dialog
    append2body
    displaying-hide-bar
    :show="show"
    @opened.once="opened"
    @before-close="close"
    top="3vh"
    title="组合查询"
    width="1000px"
    class="combined-query"
  >
    <el-main slot="body" class="cq-body">
      <el-form
        ref="form"
        :model="form"
        label-width="120px"
        size="mini"
        :style="{padding: '0',height: '600px'}">
        <el-tabs
          class="panel-box fix-el-tabs-exm"
          v-model="activeName"
          tab-position="left"
          type="card"
          :style="{width: '100%'}"
        >
          <cq-header :tag-data="tagData" @tag-close="tagClose"></cq-header>
          <el-tab-pane label="基础信息查询" ref="basicInfo" name="basicInfo" key="basicInfo">
            <div class="search-form">
              <el-form-item label="项目" prop="basicInfo_projectIds" class="project--multiple">
                <el-select
                  class="project"
                  ref="project"
                  v-model="projectIds"
                  style="min-width: 100%"
                  placeholder="选择项目名称"
                  multiple
                  collapse-tags
                  popper-class="disallow-popper"
                  clearable
                  @clear="clear('basicInfo_projectIds')"
                  @hook:mounted="projectMultipleMounted"
                  @click.native.stop="openProjectSelect"
                >
                  <el-option
                    v-for="pro in projectList"
                    :key="pro.projectId" :value="pro.projectId"
                    :label="pro.projectName"/>
                </el-select>
              </el-form-item>
              <div class="el-form-item el-form-item--mini"></div>
              <el-form-item label="公司名称" prop="basicInfo_companyName">
                <el-input v-model="form.basicInfo_companyName" placeholder="公司名称" clearable></el-input>
              </el-form-item>
              <el-form-item label="部门" prop="basicInfo_branchName">
                <el-select
                  clearable
                  v-model="form.basicInfo_branchName"
                  @clear="clear('basicInfo_branchId', 'basicInfo_branchName')">
                  <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
                    <el-tree
                      :expand-on-click-node="false"
                      :data="departmentList"
                      :default-expanded-keys="departmentList[0] ? [(departmentList[0]||{})['f_branch_id']] : null"
                      :props="{ children: 'children', label: 'text'}"
                      highlight-current
                      node-key="id"
                      @node-click="departmentChange"
                      ref="tree">
                    </el-tree>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="采购负责人" prop="basicInfo_ownerId">
                <el-select
                  v-model="form.basicInfo_ownerId"
                  clearable
                  filterable
                  default-first-option
                  placeholder="采购负责人">
                  <el-option
                    v-for="item in operatorList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <div class="el-form-item el-form-item--mini"></div>
              <el-form-item label="是否付款" prop="basicInfo_havePayment">
                <el-select
                  v-model="form.basicInfo_havePayment"
                  placeholder="是否付款"
                  clearable
                >
                  <el-option label="是" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <div class="el-form-item el-form-item--mini"></div>
              <el-form-item label="尾款已结" prop="basicInfo_noBalance">
                <el-select
                  v-model="form.basicInfo_noBalance"
                  placeholder="尾款已结"
                  clearable
                >
                  <el-option label="是" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <div class="el-form-item el-form-item--mini"></div>
              <div style="height: 13px;width: 100%;border-top: 2px dotted #ddd;"></div>
              <el-form-item class="full" prop="basicInfo_onlyPaymentBookDateStart" label="付款单记账日期">
                <el-date-picker
                  v-model="form.basicInfo_onlyPaymentBookDateStart"
                  type="date"
                  placeholder="开始时间"
                  value-format="yyyy-MM-dd 00:00:00"
                  clearable>
                </el-date-picker>
                <el-date-picker
                  v-model="form.basicInfo_onlyPaymentBookDateEnd"
                  type="date"
                  placeholder="结束时间"
                  value-format="yyyy-MM-dd 23:59:59"
                  clearable>
                </el-date-picker>
              </el-form-item>
            </div>
          </el-tab-pane>
          <el-tab-pane label="费用算法及筛选" ref="feeInfo" name="feeInfo" key="feeInfo">
            <div class="search-form">
              <div class="field-explain">
                <span>费用统计时段</span>
                <span>在检索出的数据基础上，仅显示该时间段内产生的费用金额</span>
              </div>
              <el-form-item class="full" prop="feeInfo_orderSignDateStart">
                <template #label>
                  <div style="text-align: right">
                    采购统计时段
                    <el-tooltip placement="top">
                      <template #content>
                        <div style="padding: 10px;font-size: 14px;line-height: 20px;">
                          采购统计时段 费用算法对应来源<br>
                          订单金额 ― 签订时间为该时段内的采购订单<br>
                          应付金额 ― 记账时间为该时段内的应付款<br>
                          采购订单数 ― 签订时间为该时段内的采购订单数量
                        </div>
                      </template>
                      <i class="el-icon-info" style="color: #1890ff;"></i>
                    </el-tooltip>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.feeInfo_orderSignDateStart"
                  type="date"
                  placeholder="开始时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
                <el-date-picker
                  v-model="form.feeInfo_orderSignDateEnd"
                  type="date"
                  placeholder="结束时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
              </el-form-item>
              <el-form-item class="full" prop="feeInfo_paymentBookDateStart">
                <template #label>
                  <div style="text-align: right;">付款时段
                    <el-tooltip placement="top">
                      <i class="el-icon-info" style="color: #1890ff;"></i>
                      <template #content>
                        <div style="padding: 10px;font-size: 14px;line-height: 20px;">
                          付款时段 费用算法对应来源<br>
                          实付金额 ― 记账时间为该时段内的付款单<br>
                          尾款 ― 该时段最后时点时剩余未付尾款<br>
                          付款单数 ― 记账时间为该时段内的付款单数量
                        </div>
                      </template>
                    </el-tooltip>
                  </div>
                </template>
                <el-date-picker
                  v-model="form.feeInfo_paymentBookDateStart"
                  type="date"
                  placeholder="开始时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
                <el-date-picker
                  v-model="form.feeInfo_paymentBookDateEnd"
                  type="date"
                  placeholder="结束时间"
                  value-format="yyyy-MM-dd"
                  clearable>
                </el-date-picker>
              </el-form-item>
              <div style="height: 13px;width: 100%;border-top: 2px dotted #ddd;"></div>
              <div class="field-explain">
                <span>费用筛选</span>
                <span>仅检索出满足以下条件的数据</span>
              </div>
              <el-form-item class="full" label="费用类型">
                <el-select
                  v-model="form.feeInfo_costCode"
                  placeholder="费用类型"
                  clearable
                  @clear="clear('feeInfo_costCompare', 'feeInfo_costCurrency', 'feeInfo_costAmount')"
                  @change="$nextTick(() => !showCostCurrency && clear('feeInfo_costCurrency'))"
                >
                  <el-option v-for="item in costCodes" :key="item.id" :label="item.text" :value="item.id"></el-option>
                </el-select>
                <el-select
                  v-if="showCostCurrency"
                  v-model="form.feeInfo_costCurrency"
                  placeholder="币种"
                  clearable
                  filterable
                  default-first-option>
                  <el-option
                    v-for="item in currencyForeignList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="full" label="费用区段" v-if="form.feeInfo_costCode">
                <el-select
                  v-model="form.feeInfo_costCompare"
                  placeholder="费用区段"
                  filterable
                  default-first-option
                  clearable
                  @clear="clear('feeInfo_costAmount')"
                >
                  <el-option v-for="item in compares" :key="item.id" :label="item.text" :value="item.id"></el-option>
                </el-select>
                <el-input
                  v-if="form.feeInfo_costCompare"
                  v-model.number="form.feeInfo_costAmount"
                  placeholder="费用金额"
                  clearable></el-input>
              </el-form-item>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <transition name="el-fade-in-linear">
        <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
          <span v-for="pro in projectList" :key="pro.projectId">{{ pro.projectName }}</span>
        </div>
      </transition>
      <project-select
        :show="showProjectSelect"
        ref="projectSelect"
        multiple
        @close="showProjectSelect=false"
        @finish="selectFinishEx"></project-select>
    </el-main>
    <el-footer slot="footer" height="50px">
      <div>
        <el-button
          type="primary"
          size="mini"
          style="width: 60px"
          @click="formSubmit"
        >检索
        </el-button>
        <el-button
          plain
          size="mini"
          style="width: 60px"
          @click="formReset"
        >清空
        </el-button>
      </div>
    </el-footer>
  </custom-dialog>
</template>

<script>
const CqHeader = importC('CqHeader')
const CustomDialog = importC('CustomDialog')
const ProjectSelect = importC('ProjectSelect')
export default {
  name: "SupplierSummaryCombinedQuery",
  mixins: [Mixins.requestOperator(), Mixins.ProjectMultipleSelect(), Mixins.DataTransformUtils()],
  components: {
    CqHeader,
    CustomDialog,
    ProjectSelect,
  },
  data() {
    return {
      show: false,
      activeName: 'basicInfo',
      projectSelect: {
        show: false,
        data: [],
      },
      form: {
        // 数据筛选字段
        basicInfo_projectIds: [],
        basicInfo_companyName: '',
        basicInfo_branchId: '',
        basicInfo_branchName: '',
        basicInfo_ownerId: '',
        basicInfo_havePayment: '',
        basicInfo_noBalance: '',
        basicInfo_onlyPaymentBookDateStart: '',
        basicInfo_onlyPaymentBookDateEnd: '',


        // 费用筛选字段
        feeInfo_orderSignDateStart: '',
        feeInfo_orderSignDateEnd: '',
        feeInfo_paymentBookDateStart: '',
        feeInfo_paymentBookDateEnd: '',
        feeInfo_costCode: '',
        feeInfo_costCurrency: '',
        feeInfo_costCompare: '',
        feeInfo_costAmount: '',
      },
      formInit: '',
      formDesc: {},    // 字段内容描述
      formKeyDesc: {}, // 字段中文
      formKeys: {
        basicInfo: '基础信息',
        feeInfo: '费用算法信息',
      },
      tagData: {},
      currencyForeignList: [],
      departmentList: [],
      contractStateList: [
        {id: 1, text: '有生效合同'},
        {id: 2, text: '已到款但合同未生效'},
        {id: 3, text: '全部合同已作废'},
        {id: 4, text: '无合同'},
      ],
      costCodes: [
        {id: 'orderAmount', text: '订单金额'},
        {id: 'payablesAmount', text: '应付金额'},
        {id: 'paymentAmount', text: '实付金额'},
        {id: 'balance', text: '尾款'},
      ],
      compares: [
        {id: '<', text: '小于'},
        {id: '<=', text: '小于等于'},
        {id: '=', text: '等于'},
        {id: '>=', text: '大于等于'},
        {id: '>', text: '大于'},
      ]
    }
  },
  computed: {
    showCostCurrency() {
      return [
        'orderAmount',
        'payablesAmount',
        'paymentAmount',
        'balance',
      ].includes(this.form.feeInfo_costCode)
    },
  },
  watch: {
    form: {
      handler(n) {
        const desc = this.buildFormDesc(n)
        this.buildTagData(desc)
      },
      immediate: true,
      deep: true //深度监听
    },
  },
  mounted() {
    this.requestOperator()
    this.getDepartmentList()
    this.getCurrencyForeign()
    this.formInit = JSON.stringify(this.form)
  },
  methods: {
    open() {
      this.show = true
    },
    close() {
      this.show = false
    },
    selectFinishEx(rows) {
      this.selectFinish(rows)
      this.$set(this.form, 'basicInfo_projectIds', [...this.projectIds])
    },
    buildFormDesc(form) {
      const desc = {}
      Object.entries(form || {}).forEach(([k, v]) => {
        if (this.dataUtils.isNull(v)) return
        if (Array.isArray(v) && v.length === 0) return
        let value = v
        switch (k) {
          case 'basicInfo_projectIds':
            value = this.projectList.map(it => it.projectName).toString()
            break
          case 'basicInfo_ownerId':
            value = this.dataUtils.findText(this.operatorList, value)
            break
          case 'basicInfo_havePayment':
          case 'basicInfo_noBalance':
            value = this.dataUtils.bool2text(value)
            break
          case 'basicInfo_onlyPaymentBookDateStart':
          case 'basicInfo_onlyPaymentBookDateEnd':
          case 'feeInfo_orderSignDateStart':
          case 'feeInfo_orderSignDateEnd':
          case 'feeInfo_paymentBookDateStart':
          case 'feeInfo_paymentBookDateEnd':
            value = this.dataUtils.sliceDate(value)
            break
          case 'feeInfo_costAmount': {
            const {feeInfo_costCode, feeInfo_costCurrency, feeInfo_costCompare} = form
            const codeText = this.dataUtils.findText(this.costCodes, feeInfo_costCode)
            const currencyText = this.dataUtils.findText(this.currencyForeignList, feeInfo_costCurrency)
            const compareText = this.dataUtils.findText(this.compares, feeInfo_costCompare)
            value = `${codeText}${compareText}${value}${currencyText}`
          }
            break
        }
        if (value) desc[k] = value
      })
      return desc
    },
    buildTagData(desc) {
      this.tagData = {}
      const group = {}
      Object.keys(desc).forEach(k => {
        if (!this.formKeyDesc[k]) return
        const label = `${this.formKeyDesc[k]} "${desc[k]}"`
        const groupKey = Object.keys(this.formKeys).find(formKey => k.startsWith(formKey + '_'))
        if (!groupKey) return (group[k] = [label])
        group[groupKey] ? group[groupKey].push(label): (group[groupKey] = [`${this.formKeys[groupKey]}: ${label}`])
      })
      Object.keys(group).forEach(key => this.tagData[key] = group[key].join('、'))
    },
    //获取tag字典和下拉框信息并且只执行一次
    opened() {
      this.formKeyDesc = {}
      this.$nextTick(() => {
        Object.keys(this.formKeys).forEach(ref => {
          if (!this.$refs[ref] || !this.$refs[ref].$children) return;
          this.$refs[ref].$children.forEach(child => {
            if (child.prop) this.formKeyDesc[child.prop] = child.label;
          })
        })
        // 补充一些特殊的
        Object.entries({
          basicInfo_onlyPaymentBookDateStart: '付款单记账日期起',
          basicInfo_onlyPaymentBookDateEnd: '付款单记账日期止',

          feeInfo_orderSignDateStart: '销售统计时段起',
          feeInfo_orderSignDateEnd: '销售统计时段止',
          feeInfo_paymentBookDateStart: '收款时段起',
          feeInfo_paymentBookDateEnd: '收款时段止',
          feeInfo_costAmount: '费用',
        }).forEach(([k, v]) => this.$set(this.formKeyDesc, k, v))
      })
    },
    async getDepartmentList() {
      try {
        const {data} = await Axios.post('/branch/getTree')
        this.departmentList = data
      } catch (e) {
        this.$errorMsg('获取部门失败')
      }
    },
    async getCurrencyForeign() {
      try {
        const {data} = await Axios.post('/fnCurrency/getAll')
        this.currencyForeignList = data
      } catch (e) {
        this.$errorMsg('获取币种失败')
      }
    },
    departmentChange({id, text}) {
      this.$set(this.form, 'basicInfo_branchId', id)
      this.$set(this.form, 'basicInfo_branchName', text)
      this.clear('basicInfo_ownerId')
      this.requestOperatorBase({branchId: id})
    },
    formSubmit() {
      const requestData = {}
      const breadcrumbList = Object.entries(this.buildFormDesc(this.form)).map(([k, v]) => {
        if (this.formKeyDesc[k]) return `${this.formKeyDesc[k]}: ${v}`
      }).filter(Boolean)
      const formKeys = Object.keys(this.formKeys)
      Object.keys(this.form).forEach(k => {
        const value = this.form[k]
        if (this.dataUtils.isNull(value)) return
        if (Array.isArray(value) && value.length === 0) return
        const keyIndex = formKeys.findIndex(formKey => k.startsWith(formKey + '_'))
        requestData[keyIndex !== -1 ? k.slice(formKeys[keyIndex].length + 1) : k] = String(value)
      })
      this.$emit('search', {requestData, breadcrumbList})
      this.close()
    },
    formReset() {
      this.form = JSON.parse(this.formInit)
      this.projectList = []
      this.projectIds = []
    },
    clear(...key) {
      key.forEach(k => this.$set(this.form, k, ''))
    },
    tagClose(name) {
      if (name === 'basicInfo') {
        this.projectList = []
        this.projectIds = []
      }
      this.clear(...Object.keys(this.form).filter(it => it.startsWith(name + '_')))
    },
  },
}
</script>
<style scoped>
.cq-body {
  padding: 0;
  height: 600px;
}

.el-tabs .el-tab-pane {
  height: 524px;
  box-sizing: border-box;
  overflow: auto;
  padding: 20px;
}

.el-tabs__header.is-left, .el-tabs__header.is-left * {
  border: none !important;
}

.el-tabs .el-tabs__content {
  height: 600px;
}

.el-tabs--left .el-tabs__header.is-left {
  width: 195px;
  height: 600px;
  background-color: #F5F7FA;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  padding-left: 0;
  text-align: center;
  line-height: 40px;
  user-select: none;
  transition: all .3s;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
.el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
  background-color: #fff;
  color: #2e82e4;
}

.combined-query .el-footer,
.combined-query .el-dialog__footer {
  padding: 0;
}

.combined-query .el-dialog__footer {
  display: flex;
  justify-content: flex-end;
  background-color: #f0f0f0;
}

.combined-query .el-footer {
  width: calc(100% - 180px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.combined-query .search-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  overflow: auto;
  box-sizing: border-box;
  padding: 20px;
  align-items: flex-start;
  align-content: flex-start;
}

.combined-query .search-form .el-input, .el-select {
  width: 100%;
}

.combined-query .search-form .el-form-item.el-form-item--mini {
  flex: 0 0 49%;
}

.combined-query .search-form .el-form-item.el-form-item--mini.full {
  flex-basis: 100%;
}
.combined-query .search-form .el-form-item.el-form-item--mini.full .el-input,
.combined-query .search-form .el-form-item.el-form-item--mini.full .el-select{
  width: 200px;
}

.combined-query .search-form .field-explain {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.combined-query .search-form .field-explain span:last-child {
  margin-top: 5px;
  color: #999;
}
.combined-query .boothArea .el-form-item__content {
  display: flex;
}

.combined-query .placeholder {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #e7afaf;
  font-size: 12px;
  font-weight: bold;
}
</style>