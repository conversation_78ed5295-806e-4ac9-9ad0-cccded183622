<template>
  <div
    v-loading.fullscreen.lock="exporting"
    element-loading-text="正在导出..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.1)"
  >
    <serve-booth-report-header
      ref="header"
      @search="search"
    ></serve-booth-report-header>
    <serve-booth-report-body
      ref="body"
      @table-select="tableSelect"
      @clear-search="clearSearch"
      @export="export_"
    >
    </serve-booth-report-body>
  </div>
</template>

<script>
const ServeBoothReportHeader = importC('ServeBoothReportHeader')
const ServeBoothReportBody = importC('ServeBoothReportBody')
export default {
  name: "ServeBoothReportPanel",
  data() {
    return {
      tableSelection: [],
      exporting: false,
    }
  },
  provide() {
    return {
      formatAmountList: this.formatAmountList,
    }
  },
  methods: {
    fn() {
      return this.$developMessage()
    },
    search(data, isFirst) {
      return this.$refs['body'].search(data, isFirst)
    },
    tableSelect(rows) {
      this.$set(this, 'tableSelection', rows)
    },
    clearSearch() {
      this.$refs['header'].clearAll()
    },
    openDetails(mode, receivablesId = '') {
      const initValues = {}
      if (mode === 'add') {
        const projectList = this.$refs.header.projectList
        if (projectList && projectList.length === 1) {
          const [{projectId, projectName}] = projectList
          initValues.projectId = projectId
          initValues.projectName = projectName
        }
      }
      this.$refs['details'].open(mode, receivablesId, initValues)
    },
    operationComplete() {
      const $body = this.$refs['body']
      $body.requestTableData($body.requestHistory)
    },
    exportMapper(propList, item, index) {
      let temp = {index: index + 1}
      propList.forEach(prop => {
        let v = item[prop]
        if (String(prop).endsWith('List')) {
          v = this.formatAmountList(v, '<br/>')
        }
        temp[prop] = v
      })
      return temp
    },
    export_() {
      const $body = this.$refs['body']
      const $mainFields = $body.fields
      const propList = $mainFields.map(it => it.prop)
      const labelList = $mainFields.map(it => it.label)
      const tHead = ['序号', ...labelList]
      if (this.tableSelection.length < 1)
        return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          .then(() => this.exportAll())
          .catch(Boolean)
      const exportList = this.tableSelection.map((item, index) => this.exportMapper(propList, item, index))
      exportObject2Excel({
        header: tHead,
        body: exportList,
        title: '项目客户收支报表',
        fileName: '项目客户收支报表',
      })
      this.$message.success('导出成功！')
    },
    exportAll() {
      this.exporting = true
      const $body = this.$refs['body']
      const $mainFields = $body.fields
      const propList = $mainFields.map(it => it.prop)
      const labelList = $mainFields.map(it => it.label)
      const tHead = ['序号', ...labelList]
      const data = {...$body.requestHistory, importList: true}
      delete data.rows
      delete data.page
      Axios.post('/statistics/projClientServeBooth', JSON2FormData(data))
        .then(({data}) => {
          if (data.state !== 1) return this.$errorMsg('导出数据失败！')
          if (!Array.isArray(data.rows) || data.rows.length === 0)
            return this.$errorMsg('没有可导出的数据！')
          data.rows.push(data.data)
          const exportData = data.rows.map((item, index) => this.exportMapper(propList, item, index))
          exportObject2Excel({
            header: tHead,
            body: exportData,
            title: '项目客户收支报表',
            fileName: '项目客户收支报表-导出全部'
          })
          this.$message.success('导出成功')
          this.exporting = false
        })
        .catch(err => {
          console.log(err)
          this.$errorMsg('导出数据失败！')
        })
        .finally(() => this.exporting = false)
    },
    formatAmountList(list, joinChar = '\n') {
      if (!Array.isArray(list)) return ''
      return list.map(item => item.amountStr).join(joinChar)
    }
  },
  components: {
    ServeBoothReportHeader,
    ServeBoothReportBody,
  }
}
</script>
