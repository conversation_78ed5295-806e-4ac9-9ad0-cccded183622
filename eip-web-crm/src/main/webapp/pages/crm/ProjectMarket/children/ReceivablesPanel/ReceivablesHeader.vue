<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="30px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
              class="project"
              ref="project"
              v-model="projectIds"
              placeholder="选择项目名称"
              multiple
              collapse-tags
              popper-class="disallow-popper"
              clearable
              @hook:mounted="projectMultipleMounted"
              @change="selectChange"
              @clear="projectList = []"
              @click.native.stop="openProjectSelect"
          ><el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称" prop="cltName">
          <el-input v-model="searchForm.cltName" placeholder="公司名称" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="记账日期" prop="timeRange" class="hide-1300">
          <el-date-picker
              v-model="searchForm.timeRange"
              type="daterange"
              unlink-panels
              :picker-options="pickerOptions"
              range-separator="—"
              start-placeholder="记账开始时间"
              end-placeholder="记账结束时间"
              value-format="yyyy-MM-dd"
              clearable
              align="right">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessTypeCode">
          <el-select
              v-model="searchForm.businessTypeCode"
              placeholder="业务类型"
              clearable
              @clear="clear('businessTypeCode', 'businessTypeName')"
          >
            <el-option
              v-for="bType in businessTypeList"
              :key="bType.id + Math.random()"
              :value="bType.id"
              :label="bType.text"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算状态" prop="settlementState" v-if="enableReceiptSettlement">
          <el-select
            v-model="searchForm.settlementState"
            placeholder="结算状态"
            size="mini"
            clearable>
            <el-option label="未结清" :value="0"></el-option>
            <el-option label="已结清" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="searchForm.memo" placeholder="备注" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
      <div class="right-btn-box" v-if="false">
        <el-button
          size="mini"
          v-if="AuthorityMapper('add')"
          class="btn"
          @click="$emit('add')">{{isPurchaseOrder ? '生成其他应付': '新增其他应收'}}</el-button>
        <el-button size="mini" v-if="AuthorityMapper('del') && false" class="btn">删除</el-button>
        <el-button size="mini" v-if="false" class="btn" @click="$emit('combined-query')">组合查询</el-button>
        <el-dropdown size="mini" type="primary" placement="bottom-start" @command="$emit($event)">
          <el-button type="primary" plain size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <!--<el-dropdown-item v-if="AuthorityMapper('del')" command="del-batch">批量删除</el-dropdown-item>-->
            <!--<el-dropdown-item command="import">导入</el-dropdown-item>-->
            <el-dropdown-item command="export">导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{pro.projectName}}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false" @finish="selectFinish"/>
  </el-main>
</template>

<script>
  const ProjectSelect = importC('ProjectSelect')
  export default {
    name: "ReceivablesHeader",
    mixins: [Mixins.ProjectMultipleSelect()],
    inject:{
      businessTypeCode: {
        from: 'businessTypeCode',
        default: 'YS_XSDD'
      },
      isPurchaseOrder: {
        from: 'isPurchaseOrder',
        default: false
      }
    },
    data() {
      return {
        searchForm: {
          projectIds: Object.create(null),
          projectName: Object.create(null),
          timeRange: '',
          cltName: '',
          businessTypeCode: '',
          businessTypeName: '',
          settlementState: '',
          memo: '',
        },
        businessTypeList:[
          {id: 'YF_CGDD', text: '采购订单应付'},
          {id: 'YS_XSDD', text: '销售订单应收'},
          {id: 'YF_OTHER', text: '其他应付'},
          {id: 'YS_OTHER', text: '其他应收'},
        ],
        pickerOptions,
        showProjectSelect: false,
        dataMap: {
          projectName: '项目名称',
          cltName: '公司名称',
          bookDateStart: '记账时间起',
          bookDateEnd: '记账时间止',
          businessTypeName: '业务类型',
          settlementState: '结算状态',
          memo: '备注'
        },
        enableReceiptSettlement: false
      }
    },
    mounted() {
      this.getSysOrgSetting()
      this.businessTypeList = this.businessTypeList.filter(({id}) => id === this.businessTypeCode || id === (this.isPurchaseOrder ? 'YF_OTHER' : 'YS_OTHER'))
      if (frameId) {
        this.$handleOperation(frameId, this.isPurchaseOrder ? 'Payables' : 'receivables', data => {
          const {projectId, projectName, clientName} = data
          projectId && projectName && this.selectFinish([{projectId, projectName}], true)
          this.$set(this.searchForm, 'cltName', clientName)
          this.search(true)
        })
      } else {
        if (extend.from === 'settlement') {
          const result = {
            requestData: {receivablesId: extend.data},
            breadcrumbList: ['应收款序号: ' + extend.data]
          }
          return this.$emit('search', result, true)
        }
        delete extend.details
        const projectId = extend.projectId
        const projectName = extend.projectName
        delete extend.projectId
        delete extend.projectName
        Object.assign(this.searchForm, extend)
        projectId && projectName && this.selectFinish([{projectId, projectName}], true)
        this.search(true)
      }
    },
    methods: {
      search(isFirst) {
        this.searchForm.timeRange === null && this.clear('timeRange')
        const curBType =  this.businessTypeList.find(({id})=> id === this.searchForm.businessTypeCode)
        let searchValues = {
            projectIds: this.projectIds.toString(),
            cltName: this.searchForm.cltName,
            bookDateStart: this.searchForm.timeRange[0],
            bookDateEnd: this.searchForm.timeRange[1],
            businessTypeCode: this.searchForm.businessTypeCode,
            settlementState: this.searchForm.settlementState,
            memo: this.searchForm.memo,
          },
          breadcrumbValues = {
            projectName: this.projectIds.map(projectId => {
              const p = this.projectList.find(item => projectId === item.projectId)
              if (!p) return ''
              return p.projectName
            }).join('、'),
            cltName: searchValues.cltName,
            bookDateStart: searchValues.bookDateStart,
            bookDateEnd: searchValues.bookDateEnd,
            businessTypeName: curBType && curBType.text,
            settlementState: this.searchForm.settlementState ? '已结算' : '未结算' ,
            memo: searchValues.memo,
          },
          result = {
            requestData: {},
            breadcrumbList: []
          }
        let breadcrumbKeys = Object.keys(breadcrumbValues)
        Object.keys(searchValues).forEach((k, index) => {
          let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
          if (val !== false && val !== 0 && !val) return
          result.requestData[k] = val
          result.breadcrumbList.push(`${this.dataMap[bk]}: ${typeof bv === 'string' ? bv.replace(' 00:00:00', '') : bv}`)
        })
        this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
      },
      clear(...key) {
        key.forEach(k => this.$set(this.searchForm, k, ''))
      },
      clearAll() {
        this.clear(...Object.keys(this.searchForm))
        this.projectList = []
        this.projectIds = []
        this.searchForm.projectIds = Object.create(null)
        this.searchForm.projectName = Object.create(null)
      },
      async getSysOrgSetting() {
        try {
          const {data: {state, data}} = await Axios.post('/sysSettingOrg/queryReceiptSet')
          if (state !== 1) await Promise.reject('获取设置失败！')
          let tmp = (data || []).find(it => it.settingCode === 'enable_receipt_settlement') || {}
          this.enableReceiptSettlement = tmp.settingValue === 'true'
        } catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$message.error(e)
        }
      },
    },
    components: {
      ProjectSelect
    },
    computed:{
      AuthorityMapper(){
        const Authority = {
          add: ['fu_crm_receivables_add', 'fu_crm_payables_add'],
          mod: ['fu_crm_receivables_mod', 'fu_crm_payables_mod'],
          del: ['fu_crm_receivables_del', 'fu_crm_payables_del'],
        }
        return type => getAuthority((Authority[type] || [])[Number(this.isPurchaseOrder)])
      }
    }
  }
</script>

<style>
@media screen and (max-width: 1300px) {
  .hide-1300 {
    display: none!important;
  }
}
</style>

<style scoped>
  .top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    /* min-width: 1400px; */
    padding-left: 3px;
    padding-right: 3px;
  }

  .search-form-inline {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  .search-form-inline .el-form-item {
    margin: 0 15px 0 0;
  }

  .el-divider {
    margin: 10px auto;
  }

  .el-input {
    width: 120px;
  }

  .el-date-editor {
    width: 245px;
  }

  .right-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-btn-box .el-button.btn {
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 0;
  }

  .right-btn-box .el-button.btn:hover {
    background-color: rgb(99, 169, 251);
  }
</style>