<template>
  <div class="header-layout">
    <!-- <div v-show="!isPackUp" class="placeholder"></div> -->
    <el-header class="top-header" height="auto">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
       <!-- <el-form-item label="当前项目">
          <el-input v-model="projectName" :style="{width: (isMainProject ? 120 : 240) + 'px'}" placeholder="当前项目" disabled></el-input>
        </el-form-item>-->
        <!-- <el-form-item label="当前项目" prop="contractProjectId"  v-show="!isFromTop">
          <input type="hidden" name="contractProjectName" v-model="searchForm.contractProjectName">
          <el-select
            class="project-selector"
            v-model="searchForm.contractProjectId"
            filterable
            clearable
            default-first-option
            @clear="clear('contractProjectId','contractProjectName')"
            @change="contractProjectChange"
            placeholder="选择关联项目">
          <el-option label="全部关联项目" value=""></el-option>
          <el-option
            v-for="item in associatedProjectList"
            :key="item.id"
            :label="item.text"
            :value="item.id"
          >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item id="jsfMoreProjectIds" label="项目名称" prop="moreProjectIds" v-show="isFromTop" label-width="5em"> -->
        <el-form-item id="jsfMoreProjectIds" label="项目名称" prop="moreProjectIds" label-width="5em">
          <el-select popper-class="hide" @click.native="selectProject" readonly  multiple collapse-tags size="mini" placeholder="点击选择" clearable v-model="projectSelect.ids">
              <el-option v-for="it in projectSelect.data" :key="it.projectId" :label="it.projectName" :value="it.projectId"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item id="jsfCompanyName" label="公司名称" prop="companyName">
          <el-input v-model="searchForm.companyName" placeholder="公司名称" v-focus @change="clear('clientId')" v-enter="search"
                    clearable></el-input>
        </el-form-item>
        <!-- <el-form-item label="展位号" prop="boothNum" v-if="isMainProject"> -->
        <el-form-item label="展位号" prop="boothNum" >
          <el-input v-model="searchForm.boothNum" placeholder="展位号" v-enter="search" clearable></el-input>
        </el-form-item>
        <!-- <el-form-item label="部门" prop="branchId">
        </el-form-item> -->
        <el-form-item label="移交客服状态" prop="serveBoothSaleState" class="fix-el-radio-tab">
          <el-radio-group v-model="searchForm.serveBoothSaleState" @change="serveBoothSaleStateChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button :label="9">已移交</el-radio-button>
            <el-radio-button :label="0">未移交</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="业绩归属人" prop="ownerId">
          <el-select clearable v-model="searchForm.branchName"  placeholder="全部部门" @clear="clear('ownerId', 'ownerName')">
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="departmentList"
                :props="{ children: 'children', label: 'text'}"
                highlight-current
                node-key="id"
                @node-click="departmentChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
          <input type="hidden" name="ownerName" v-model="searchForm.ownerName">
          <el-select
            v-model="searchForm.ownerId"
            clearable
            filterable
            default-first-option
            @clear="clear('ownerId','ownerName')"
            @change="ownerChange"
            placeholder="全部业务员">
            <el-option
              v-for="item in operatorList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="展位状态" prop="preorderAndContractState" v-if="isFullProcess">
            <el-select
                v-model="searchForm.preorderAndContractState"
                clearable
                filterable
                default-first-option
                @clear="clear('preorderAndContractState','preorderAndContractStateName')"
                @change="preorderAndContractStateChange"
                placeholder="展位状态">
              <el-option
                  v-for="item in preorderAndContractStateList"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="到款状态" prop="serveBoothPayState">
          <!-- @change="search()" -- >
          <el-select
            v-model="searchForm.serveBoothPayState"
            clearable
            filterable
            default-first-option
            @clear="clear('serveBoothPayState')"
            placeholder="到款状态">
            <el-option
              v-for="item in serveBoothPayStateList"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-collapse-transition>
          <el-form-item label="部门" prop="branchId"  v-if="!isPackUp">
            <el-select clearable v-model="searchForm.branchName" @clear="clear('branchId', 'branchName')">
              <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
                <el-tree
                  :expand-on-click-node="false"
                  :data="departmentList"
                  :props="{ children: 'children', label: 'text'}"
                  highlight-current
                  node-key="id"
                  @node-click="departmentChange"
                  ref="tree">
                </el-tree>
              </el-option>
            </el-select>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="业绩归属人" prop="ownerId" v-if="!isPackUp">
            <input type="hidden" name="ownerName" v-model="searchForm.ownerName">
            <el-select
              v-model="searchForm.ownerId"
              clearable
              filterable
              default-first-option
              @clear="clear('ownerId','ownerName')"
              @change="ownerChange"
              placeholder="业绩归属人">
              <el-option
                v-for="item in operatorList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="订单状态" prop="saleState" v-if="!isFullProcess">
              <el-select
                  v-model="searchForm.saleState"
                  clearable
                  filterable
                  default-first-option
                  @clear="clear('saleState','saleStateName')"
                  @change="saleStateChange"
                  placeholder="订单状态">
                <el-option
                    v-for="item in saleStateList"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="展位需求状态" prop="boothConfirmed" v-if="!isPackUp">
            <el-select
                v-model="searchForm.boothDemandConfirm"
                placeholder="展位需求状态"
                clearable
                @clear="clear('boothDemandConfirm')"
            >
              <el-option label="已确认" :value="true"></el-option>
              <el-option label="未确认" :value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item label="移交客服状态" prop="serveBoothSaleState" v-if="!isPackUp">
            <el-select
                v-model="searchForm.serveBoothSaleState"
                clearable
                filterable
                default-first-option
                @clear="clear('serveBoothSaleState','serveBoothSaleStateName')"
                @change="serveBoothSaleStateChange"
                placeholder="移交客服状态">
              <el-option
                  v-for="item in serveBoothSaleStateList"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-collapse-transition> -->
        &nbsp;&nbsp;&nbsp;&nbsp;
        <el-button type="primary" size="mini" @click="search()">查询</el-button>
        <el-button plain size="mini" @click="$emit('combined-query', {
          ids:projectSelect.ids,data:projectSelect.data,
          serveBoothSaleState: searchForm.serveBoothSaleState,
        })">组合查询</el-button>
      </el-form>
      <!-- <div class="right">
        <el-button type="primary" size="mini" @click="search()">查询</el-button>
        <el-button plain size="mini" @click="$emit('combined-query')">组合查询</el-button>
       <el-link class="pack-button" :class="{rotate: isPackUp}" :underline="false" @click="isPackUp=!isPackUp">{{isPackUp? '展开': '收起'}} <i class="el-icon-arrow-up"></i></el-link> -- >
      </div> -->
    </el-header>
    <!-- <el-divider></el-divider> -->
    <project-select :show="projectSelect.show" ref="projectSelect"
      :multiple="['cq',''].includes(projectSelect.type)"
      @close="projectSelect.show = false" @finish="selectFinish" />
  </div>
</template>

<script>
  const ProjectSelect = importC('ProjectSelect')
   // 项目多选
  const MixinProjectSelect = {
    data() {
      return {
        projectSelect: {
          show: false,
          data: [],
          ids: [],
          type: '',
        }
      }
    },
    methods: {
      selectFinish(rows) {
        if(this.projectSelect.type == 'import' && vbus) {
          vbus.$emit('contract-import-selected-project', {id: rows.projectId,text: rows.projectName })
          return;
        } else  if(this.projectSelect.type == 'cq' && vbus) {
          vbus.$emit('exManage-cq-selected-projects', rows.map(it=> ({id: it.projectId,text: it.projectName })))
          return;
        }
        this.projectSelect.data = rows
        this.projectSelect.ids = rows.map(({projectId}) => projectId)
        this.$emit('select-projects', rows.map(it=> ({id: it.projectId,text: it.projectName })))
      },
      selectProject(){
        this.projectSelect.type = ''
        this.projectSelect.show = true
      },
      selectProjectByType(type=''){
        this.projectSelect.type = type
        this.projectSelect.show = true
      },
    }
  };
  export default {
    name: "ExhibitorManageHeader",
    components: {
      ProjectSelect,
    },
    mixins: [MixinProjectSelect],
    data() {
      return {
        searchForm: {
          companyName: '',
          clientId: '',
          ownerId: '',
          ownerName: '',
          boothNum: '',
          saleState: '',
          saleStateName: '',
          boothConfirmed: '',//确认参展标记
          boothDemandConfirm:'',//参展需求确认标记
          // contractProjectId: '',
          contractProjectName: '',
          preorderAndContractState: '',
          preorderAndContractStateName: '',
          serveBoothSaleState: '',
          serveBoothPayState: '',
          serveBoothSaleStateName: '',
          branchId: '',
          branchName: '',
          moreProjectIds: '',
        },
        dataMap: {
          // contractProjectId: '当前项目',
          companyName: '公司名称',
          ownerId: '业绩归属人',
          boothConfirmed: '展位确认',
          'boothPreorderInfo.boothNum': '展位号',
          'boothPreorderInfo.saleState': '订单状态',
          serveBoothPayState: '到款状态',
          boothDemandConfirm: '展位需求状态',
          preorderAndContractState: '销售状态',
          serveBoothSaleState: '移交客服状态',
          branchId: '部门',
          moreProjectIds: '项目名称',
        },
        operatorList: [],
        // {id: '',text: ''},
        serveBoothPayStateList: [
          {id: 1,text: '未到款'},
          {id: 2,text: '已到款'},
        ],
        projectId: extend.projectId || getQueryString('mainProjectId') || getQueryString('projectId') || '',
        projectName: extend.projectName || getQueryString('projectName') || '',
        isPackUp: true,
        isFromTop: false,
        departmentList: [],
        serveBoothSaleStateList2: [
          {id:0,text: '未移交'},
          {id:9,text: '已移交'}
        ],
      }
    },
    inject: [
      'projectId',
      'mainProjectId', // 已废弃
      'isMainProject', // 已废弃
      'projectList',   // 已废弃
      'saleStateList',
      'preorderAndContractStateList',
      'serveBoothSaleStateList',
      'isFullProcessComputed', // 已废弃
    ],
    async mounted() {
      const that = this;
      if(vbus) {
        vbus.$on('contract-import-select-project', function() {
          that.selectProjectByType('import')
        });
        vbus.$on('exManage-cq-select-projects', function() {
          that.selectProjectByType('cq')
        });
      }

      let url = (new URL(window.location.href)).searchParams;
      this.isFromTop = !!url.get('top');
      this.requestOperatorList()
      this.getDepartmentList()
      // this.getGroupFlowSetting()
      // this.$set(this.searchForm, 'companyName', clientName)
      // this.$set(this, 'projectName', projectName)
      this.$set(this.searchForm, 'projectName', extend.projectName)
      this.$set(this.searchForm, 'companyName', extend.companyName)
      this.$set(this.searchForm, 'clientId', extend.clientId)
      this.$set(this.searchForm, 'boothConfirmed', extend.boothConfirmed)
      if(this.isFromTop) {
        await this.requestDefaultProject();
      } else {
        if(extend.projectId) {
          this.projectSelect.ids = [extend.projectId];
          this.projectSelect.data = [{
            projectId: extend.projectId,
            projectName: extend.projectName || ('项目'+extend.projectId),
          }]
          this.$emit('init', {...this.projectSelect.data[0]})
        } else if(extend.projectList){
          let pList = extend.projectList || [];
          this.projectSelect.ids = pList.map(it=> it.projectId);
          this.projectSelect.data = pList;
          this.$emit('init', {...this.projectSelect.data[0]})
        } else {
          await this.requestDefaultProject();
        }
      }
      this.search(true)
    },
    methods: {
      packUp(){
        this.isPackUp = true
      },
      packDown(){
        this.isPackUp = false
      },
      error(msg , throws= true) {
        msg = msg || '请求失败';
        const p = this.$errorMsg(msg);
        if(throws) throw new Error(msg);
        return p;
      },
      async requestDefaultProject(){
        const {data:res} = await Axios.post('/project/selectByExhibitionEndDate', JSON2FormData({workTeamId: top.teamId || -1})).catch(_ => this.error())
        if(res.state !== 1) this.error(res.msg)
        if(res.data) {
          const {projectId, projectName} = res.data
          projectId && projectName && this.selectFinish([{projectId, projectName}], true);
          this.$emit('init', {projectId, projectName});
        } else {
          this.$emit('init', {});
        }
      },
      search(isFirst) {
        if(isFirst !== true) {
          this.clear('clientId');
        }
        let searchValues = {
            // contractProjectId: this.searchForm.contractProjectId,
            companyName: this.searchForm.companyName,
            ownerId: this.searchForm.ownerId,
            boothConfirmed: this.searchForm.boothConfirmed,
            'boothPreorderInfo.boothNum': this.searchForm.boothNum,
            'boothPreorderInfo.saleState': this.searchForm.saleState,
            preorderAndContractState: this.searchForm.preorderAndContractState,
            serveBoothSaleState: this.searchForm.serveBoothSaleState,
            serveBoothPayState: this.searchForm.serveBoothPayState,
            moreProjectIds: this.projectSelect.ids.join(),
            branchId: this.searchForm.branchId,
            clientId: this.searchForm.clientId,
            projectFlow: this.isFullProcess ? 'fullProcess' : 'simpleProcess',
          },
          breadcrumbValues = {
            // contractProjectId: this.searchForm.contractProjectName,
            companyName: this.searchForm.companyName,
            ownerId: this.searchForm.ownerName,
            boothConfirmed: this.searchForm.boothConfirmed !== '' ? this.searchForm.boothConfirmed ? '已确认' : '未确认' : '',
            'boothPreorderInfo.boothNum': this.searchForm.boothNum,
            'boothPreorderInfo.saleState': this.searchForm.saleStateName,
            preorderAndContractState: this.searchForm.preorderAndContractStateName,
            serveBoothSaleState: this.searchForm.serveBoothSaleStateName,
            serveBoothPayState: (this.serveBoothPayStateList.find(it => it.id == this.searchForm.serveBoothPayState) || {}).text || '',
            moreProjectIds: this.projectSelect.data.map(it=> it.projectName).join(),
            branchId: this.searchForm.branchName
          },
          result = {
            requestData: {},
            breadcrumbList: []
          };
        let breadcrumbKeys = Object.keys(breadcrumbValues)
        Object.keys(searchValues).forEach((k, index) => {
          let val = searchValues[k], bk = k, bv = breadcrumbValues[bk]
          // let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
          if (val !== 0 && val !== false && !val) return
          result.requestData[k] = val
          if(!bv && bv!==0) return;
          bk && result.breadcrumbList.push(`${this.dataMap[bk]}: ${typeof bv === 'string' ? bv.replace(' 00:00:00', '') : bv}`)
        })
        // this.packUp() //收起
        this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
      },
      clear(...key) {
        key.forEach(k => this.$set(this.searchForm, k, ''))
      },
      clearAll() {
        this.packUp()
        this.clear(...Object.keys(this.searchForm))
        this.projectSelect.data = []
        this.projectSelect.ids = []
      },
      requestOperatorList(branchId='') { // 获取业务员
        Axios.post('operator/selectByMap', JSON2FormData({workTeamId: teamId, branchId, showAllState: true})).then(({data: res}) => {
          this.operatorList = res.map(item => ({id: Number(item.id), text: item.text}))
        }).catch(e => {
          this.$errorMsg('获取业务员失败！');
        })
      },
      ownerChange(val) {
        if (!val) return
        this.$set(
          this.searchForm,
          'ownerName',
          (this.operatorList.find(i => i.id === val) || {})['text'] || ''
        )
      },
      // contractProjectChange(val) {
      //   if (!val) return
      //   this.$set(
      //     this.searchForm,
      //     'contractProjectName',
      //     (this.associatedProjectList.find(i => i.id === val) || {})['text'] || ''
      //   )
      // },
      saleStateChange(val) {
        if (!val) return
        this.$set(
          this.searchForm,
          'saleStateName',
          (this.saleStateList.find(i => i.id === val) || {})['text'] || ''
        )
      },
      preorderAndContractStateChange(val) {
        if (!val) return
        this.$set(
          this.searchForm,
          'preorderAndContractStateName',
          (this.preorderAndContractStateList.find(i => i.id === val) || {})['text'] || ''
        )
      },
      serveBoothSaleStateChange(val) {
        if (val !== 0 && !val) return
        this.$set(
          this.searchForm,
          'serveBoothSaleStateName',
          (this.serveBoothSaleStateList2.find(i => i.id === val) || {})['text'] || ''
        )
      },
      async getDepartmentList() {
        try {
          const {data} = await Axios.post('/branch/getTree')
          this.departmentList = data
        } catch (e) {
          this.$errorMsg('获取部门列表失败！')
        }
      },
      departmentChange({id, text}) {
        this.$set(this.searchForm, 'branchId', id)
        this.$set(this.searchForm, 'branchName', text)
        this.clear('ownerId', 'ownerName')
        this.requestOperatorList(id)
      }
    },
    computed: {
      // associatedProjectList(){
      //   return this.projectList()
      // },
      isFullProcess(){
        return true; // this.isFullProcessComputed()
      }
    },
    // watch: {
    //   associatedProjectList(list) {
    //     if (list.length && this.projectId){
    //       this.$set(this.searchForm, 'contractProjectId', this.projectId)
    //       this.contractProjectChange(this.projectId)
    //       this.search(true)
    //     }
    //   }
    // }
  }
</script>

<style>@import "../../../VueCommonComponents/header.css?v=240306";</style>
<style>
.el-select-dropdown.hide {
    display: none !important;
  }
</style>
<style scoped>
  #jsfMoreProjectIds .el-input{
    width: 270px !important;
  }
  #jsfCompanyName .el-input,
  .project-selector .el-input{
    width: 220px!important;
  }
  .top-header {
    padding-bottom: 15px;
    display: flex;
    flex-flow:  row nowrap;
    justify-content: space-between;
  }
  .pack-down {
    padding: 0 3px 15px !important;
  }
  .fix-el-radio-tab .el-radio-button__orig-radio:checked+.el-radio-button__inner{
    background-color: white;
    color: #409EFF;
  }
</style>