<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="30px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item ref="project" label="项目名称" prop="projectName">
          <el-input
            class="project"
            v-model="searchForm.projectName"
            readonly
            @click.native="showProjectSelect = true"
            placeholder="选择项目名称">
            <i slot="suffix"
               class="el-input__icon el-icon-delete"
               @click.stop="clear('projectId','projectName')"></i>
          </el-input>
          <input type="hidden" v-model="searchForm.projectId">
        </el-form-item>
        <el-form-item ref="company" label="公司名称" prop="companyName">
          <el-input
            class="project"
            v-model="searchForm.companyName"
            readonly
            @click.native="showCompanySelect = true"
            placeholder="选择公司名称">
            <i slot="suffix"
               class="el-input__icon el-icon-delete"
               @click.stop="clear('clientId','companyName')"></i>
          </el-input>
          <input type="hidden" v-model="searchForm.clientId">
        </el-form-item>
        <el-form-item>
          <el-button :disabled="!searchForm.projectId || !searchForm.clientId" type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
    </el-header>
    <el-divider></el-divider>
    <project-select
      :show="showProjectSelect"
      ref="projectSelect"
      @close="showProjectSelect=false"
      @finish="selectFinish('project', $event)"></project-select>
    <company-select
      :show="showCompanySelect"
      ref="companySelect"
      @close="showCompanySelect=false"
      @finish="selectFinish('company', $event)"></company-select>
  </el-main>
</template>

<script>
const ProjectSelect = importC('ProjectSelect')
const CompanySelect = importC('CompanySelect')
export default {
  name: "ProjcltReportHeader",
  mixins: [Mixins.clearForm()],
  data() {
    return {
      searchForm: {
        projectId: '',
        projectName: '',
        clientId: '',
        companyName: '',
      },
      showProjectSelect: false,
      showCompanySelect: false,
      dataMap: {
        projectName: '项目名称',
        companyName: '公司名称',
      },
    }
  },
  mounted() {
    delete extend.details
    Object.assign(this.searchForm, extend)
    if (this.searchForm.projectId && this.searchForm.clientId) {
      this.search()
    }
  },
  methods: {
    search() {
      const searchValues = {
        projectId: this.searchForm.projectId,
        clientId: this.searchForm.clientId,
      }
      const breadcrumbValues = {
        projectName: this.searchForm.projectName,
        companyName: this.searchForm.companyName,
      }
      const result = {
        requestData: {},
        breadcrumbList: []
      }
      const breadcrumbKeys = Object.keys(breadcrumbValues)
      Object.keys(searchValues).forEach((k, index) => {
        let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
        if (val !== false && val !== 0 && !val) return
        result.requestData[k] = val
        result.breadcrumbList.push(`${this.dataMap[bk]}: ${bv}`)
      })
      this.$emit('search', result)
    },
    /**
     * @param {'project'|'company'} type
     * @param {{}} payload
     * */
    selectFinish(type, payload) {
      switch (type) {
        case "project":
          this.$set(this.searchForm, 'projectName', payload.projectName)
          this.$set(this.searchForm, 'projectId', payload.projectId)
          break;
        case "company":
          this.$set(this.searchForm, 'companyName', payload.companyName)
          this.$set(this.searchForm, 'clientId', payload.clientId)
          break;
      }
      console.log(this.searchForm)
    }
  },
  components: {
    ProjectSelect,
    CompanySelect
  },
}
</script>
<style scoped>
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  /* min-width: 1400px; */
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.el-input {
  width: 160px;
}
</style>