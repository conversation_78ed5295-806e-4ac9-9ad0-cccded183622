<template>
  <div v-loading.fullscreen.lock="exporting"
       element-loading-text="正在导出..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.1)">
    <purchase-summary-header
        ref="header"
        @search="search"
        @export="export_"
    ></purchase-summary-header>
    <purchase-summary-main-body
        ref="body"
        @clear-search="clearSearch"
        @table-select="tableSelect"
    >
    </purchase-summary-main-body>
  </div>
</template>

<script>
  const PurchaseSummaryHeader = importC('PurchaseSummaryHeader')
  const PurchaseSummaryMainBody = importC('PurchaseSummaryMainBody')
  export default {
    name: "PurchaseSummaryPanel",
    data() {
      return {
        exporting: false,
        tableSelection: [],
        toFixedFields:'contractAmount orderAmount receivable receipt balance invoiceFinished invoiceUnFinished'.split(' ')
      }
    },
    methods: {
      search(data, isFirst) {
        return this.$refs['body'].search(data, isFirst)
      },
      tableSelect(rows) {
        this.$set(this, 'tableSelection', rows)
      },
      clearSearch() {
        this.$refs['header'].clearAll()
      },
      //导出
      export_() {
        const $body = this.$refs['body']
        const $mainFields = $body.fields.filter(item => item.prop !== 'opera' && item.label !== '操作')
        const propListEx = [[],[]];
        let tHead,exportList,propList = [];
        $mainFields.map(it=>{
          if(it.children && it.children.length) {
            propListEx[0].push({
              label: it.label,
              colspan: it.children.length,
              rowspan: 1,
            })
            it.children.map(itt => {
              propList.push(itt.prop)
              propListEx[1].push({
                label: itt.label,
                colspan: 1,
                rowspan: 1,
              })
            })
          } else {
            propList.push(it.prop)
            propListEx[0].push({
              label: it.label,
              colspan: 1,
              rowspan: 2,
            })
          }
        })
        if(propListEx[1].length) { // 二级表头
          tHead = [[{
            label: '序号',
            colspan: 1,
            rowspan: 2,
           }, ...propListEx[0]], propListEx[1]];
        } else { // 一级表头
          propList = $mainFields.map(it=>it.prop)
          const labelList = $mainFields.map(it=>it.label)
          tHead = ['序号', ...labelList]
        }
        if (this.tableSelection.length < 1)
          return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
              .then(() => this.exportAll(tHead,propList))
              .catch(e => e)
        this.exportDataAddSum(tHead,propList,this.tableSelection,'项目采购情况统计','项目采购情况统计')
      },
      exportDataAddSum(header,propList,rows,title,fileName) {
        const data = rows.map((item, _index)=>{
          const temp = [_index + 1]
          propList.forEach(prop=>{
            let v = item[prop]
            if(this.toFixedFields.includes(prop)){
              v = this.$toFixed2(v)
            }
            temp.push(v)
          })
          return temp
        })
        // 添加统计
        const exp = [''];
        const all = this.$refs['body'].tableDataAll || {};
        propList.forEach(prop=>{
          let v = all[prop]
          if(this.toFixedFields.includes(prop)){
            v = this.$toFixed2(v)
          }
          exp.push(v)
        })
        data.push(exp)
        exportObject2ExcelWith2Head({
          header,
          data,
          title,
          fileName,
        })
        this.$message.success('导出成功！')
      },
      exportAll(tHead,propList) {
        this.exporting = true
        const $body = this.$refs['body']
        const data = {...$body.requestHistory}
        delete data.rows
        delete data.page
        Axios.post('/statistics/projectPurchaseSummary', JSON2FormData(data)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('导出数据失败！')
          if (!Array.isArray(res.rows) || res.rows.length === 0)
            return this.$errorMsg('没有可导出的数据！')
          this.exportDataAddSum(tHead, propList, this.transformTableData(res.rows), '项目采购情况统计', '项目采购情况统计-导出全部')
        }).catch(e => {
          this.$errorMsg('导出数据失败！')
        }).finally(() => this.exporting = false)
      },
      transformTableData(rows) {
        if (!(rows && Array.isArray(rows))) return
        const tmp = [];
        rows.map(row => {
          Object.keys(row).map(field => {
            if (Array.isArray(row[field])) {
              row[field].map(row2 => {
                row[field + '_' + (row2.currencyCode || '')] = row2.amountStr || '';
              })
            }
          })
          tmp.push(row);
        })
        return tmp;
      }
    },
    components: {
      PurchaseSummaryHeader,
      PurchaseSummaryMainBody,
    }
  }
</script>
