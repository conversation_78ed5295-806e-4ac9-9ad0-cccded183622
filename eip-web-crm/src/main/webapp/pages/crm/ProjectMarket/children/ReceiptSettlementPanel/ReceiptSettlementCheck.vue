<template>
  <custom-dialog
    :show="show"
    @before-close="close('exit')"
    title="结算明细审核"
    append2body
    width="450px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            type="primary"
            @click="save()">确定
          </el-button>
        </div>
      </el-header>
      <el-form class="tForm" size="mini">
        <el-form-item label-width="110px" label="审核结果" required>
          <el-radio-group v-model="checkState">
            <el-radio v-for="item in currentCheckStatus" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="checkState === 1" label-width="110px" label="缺失材料">
          <el-checkbox-group style="width: 295px;" v-model="settlementFileIds">
            <el-checkbox v-for="item in settlementFileList" :key="item.settlementFileId" :label="item.settlementFileId">{{ item.settlementFileName }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ReceiptSettlementCheck",
  inject: ['checkStatus', 'getSettlementFileList'],
  data() {
    return {
      show: false,
      checkState: '',
      settlementFileIds: [],
      resolve: Boolean,
      reject: Boolean,
      oldCheckState: ''
    }
  },
  methods: {
    open(oldCheckState, oldSettlementFileIds) {
      this.oldCheckState = oldCheckState
      this.settlementFileIds = oldSettlementFileIds || []
      this.show = true
      this.$nextTick(() => {
        this.checkState = this.currentCheckStatus[0].value
      })
      return new Promise((resolve, reject) => {
        this.resolve = resolve
        this.reject = reject
      })
    },
    async save() {
      this.close()
    },
    close(isCancel) {
      this.show = false
      if (isCancel) return this.reject && this.reject(isCancel)
      this.resolve && this.resolve({
        checkState: this.checkState,
        settlementFileIds: this.checkState === 1 ? this.settlementFileIds.toString() : void 0
      })
    }
  },
  computed: {
    currentCheckStatus() {
      let keys
      if (this.oldCheckState === null || this.oldCheckState === void 0) keys = [1, 2, 3]
      else if (this.oldCheckState === 0) keys = [1, 3]
      else if (this.oldCheckState === 1) keys = [1, 2]
      else return []
      return this.checkStatus.filter(i => keys.includes(i.value))
    },
    settlementFileList() {
      return this.getSettlementFileList()
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.message-title {
  font-size: 14px;
  color: #2e82e4;
  padding-left: 10px;
}
</style>