<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            class="text-over-hidden"
            style="max-width: 240px"
            :title="item"
            :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <template v-if="getAuthority('fu_crm_receipt_settlement_detail_check')">
          <el-button size="mini" class="btn" @click="$emit('check-batch')">批量审核</el-button>
          <el-button size="mini" class="btn" @click="$emit('check-batch', 0)">批量弃审</el-button>
        </template>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 240px)"
      border
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      @sort-change="sort"
      @selection-change="$emit('table-select', $event)"
      :handle-pagination="handlePagination"
      @row-dblclick="readRow"
      :total="total"
      :loading="loading">
      <template slot="alex-operation" slot-scope="{row}">
        <el-link :underline="false" type="primary" @click.stop="readRow(row)">查看</el-link>
      </template>

      <template v-if="row.contractId"  slot="contractId" slot-scope="{row}">
        <el-link
          type="primary"
          :underline="false"
          @click.stop="$emit('open-market', row.projectName, 'contract', row.contractId)">{{row.contractId}}</el-link>
      </template>
      <template v-if="row.receiptId"  slot="receiptId" slot-scope="{row}">
        <el-link
          type="primary"
          :underline="false"
          @click.stop="$emit('open-market', row.projectName, 'receipt', row.receiptId)">{{row.receiptId}}</el-link>
      </template>
      <template v-if="row.receivablesId"  slot="receivablesId" slot-scope="{row}">
        <el-link
          type="primary"
          :underline="false"
          @click.stop="$emit('open-market', row.projectName, 'receivables', row.receivablesId)">{{row.receivablesId}}</el-link>
      </template>
      <template slot="balanced" slot-scope="{row}">{{ row.balanced | bool2text }}</template>
      <template slot="convert" slot-scope="{row}">{{ row.convert | bool2text }}</template>
      <template slot="amount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.amount | toFixed2 }}</span>
      </template>
      <template slot="unSettlementAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.unSettlementAmount | toFixed2 }}</span>
      </template>
      <template slot="convertAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.convertAmount | toFixed2 }}</span>
      </template>
      <template slot="checkState" slot-scope="{row}">
        <span>
          <i class="dot" :style="{backgroundColor: getCheckStatusDotColor(row.checkState)}"></i>
          {{getCheckStatusLabel(row.checkState)}}
        </span>
      </template>
      <template slot="settlementAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">{{ row.settlementAmount | toFixed2 }}</span>
      </template>
      <template slot="receivablesAmount" slot-scope="{row}">
        <span>{{ row | receivablesAmountFilter }}</span>
      </template>
      <template slot="accountPeriod" slot-scope="{row}">
        <span v-if="row.accountPeriod">{{ new Date(row.accountPeriod).format('yyyy年M月') }}</span>
      </template>
      <template slot="receiptAmount" slot-scope="{row}">
        <span style="display:block;text-align:right;">
          <template v-if="row.settlementCurrencySymbol">{{row.settlementCurrencySymbol}}</template>{{ row.settlementAmount | toFixed2 }}
          <template v-if="!row.settlementCurrencySymbol">
            {{row.settlementCurrencyName || row.settlementCurrencyCode}}
          </template>
        </span>
      </template>
    </alex-table>
  </el-container>
</template>

<script>
export default {
  name: "ReceiptSettlementBody",
  mixins: [Mixins.table()],
  inject: ['checkStatus', 'getCheckStatusLabel', 'getCheckStatusDotColor'],
  data() {
    return {
      breadcrumbList: [],
      total: 0,
      tableData: [],
      loading: false,
      fields: [],
    }
  },
  async created() {
    const enable_foreign_currency = await this.getSysOrgSetting('enable_foreign_currency')
    this.fields = [
      {label: '项目名称', prop: 'projectName', minWidth: 200},
      {label: '部门', prop: 'branchName', width: 120},
      {label: '业绩归属人', prop: 'ownerName', width: 120},
      {label: '合同ID', prop: 'contractId', width: 100},
      {label: '收款单ID', prop: 'receiptId', width: 100},
      {label: '应收款ID', prop: 'receivablesId', width: 100},
      {label: '客户名称', prop: 'cltName', minWidth: 200},
      {label: '结算金额', prop: 'settlementAmount', width: 120},
      enable_foreign_currency ? {
        label: '外币结算备注',
        prop: 'receivablesAmount',
        width: 200
      } : void 0,
      {label: '产品名称', prop: 'prodName', width: 120},
      {label: '审核状态', prop: 'checkState', width: 120},
      {label: '缺失材料', prop: 'settlementFileName', width: 200},
      {label: '结算操作人', prop: 'creatorName', width: 120},
      {label: '结算时间', prop: 'createTime', width: 180},
      {label: '收款账户', prop: 'receiptAccountName', width: 120},
      {label: '收款金额', prop: 'receiptAmount', width: 120},
      {label: '收款记账时间', prop: 'bookDate', width: 120},
      {label: '审核人', prop: 'checkerName', width: 120},
      {label: '审核时间', prop: 'checkTime', width: 180},
      {label: '提成账期', prop: 'accountPeriod', width: 180},
    ].filter(Boolean)

    const getIndex = (currentId) => this.tableData.findIndex(item => item.settlementId === currentId)
    $bus.$on('ReceiptSettlement:next', ($dialog, currentId) => {
      let index = getIndex(currentId); //获得当前对话框的Index
      //如果是当前页的最后一个就尝试翻页
      if (index >= this.tableData.length - 1) {
        //这里返回错误信息
        let res = this.$refs['table'].turnPage('next')
        //本次操作取消并显示错误信息
        if (res) {
          this.$errorMsg(res, '警告', 'warning')
          return
        }
        //如果翻页成功。加载下一页的数据，并通过回调获得新数据
        this.requestHistory.page++
        this.requestTableData(this.requestHistory, (_, data) => {
          this.loading = false
          if (data.length < 1) {
            return this.$errorMsg('没有数据可查看！', '警告', 'warning')
          }
          $dialog.read(data[0].settlementId)
        })
        return
      }
      let data = this.tableData[index + 1]
      if (!data) return this.$errorMsg('没有数据可查看！', '警告', 'warning')
      $dialog.read(data.settlementId)
    })
    $bus.$on('ReceiptSettlement:prev', ($dialog, currentId) => {
      let index = getIndex(currentId);
      //尝试翻页
      if (index < 1) {
        let res = this.$refs['table'].turnPage('prev')
        if (res) return this.$errorMsg(res, '警告', 'warning')
        this.requestHistory.page--
        this.requestTableData(this.requestHistory, (_, data) => {
          this.loading = false
          if (data.length < 1) return this.$errorMsg('没有数据可查看！')
          $dialog.read(data[0].settlementId)
        })
        return
      }
      let data = this.tableData[index - 1]
      if (!data) return this.$errorMsg('没有数据可查看！', '警告', 'warning')
      $dialog.read(data.settlementId)
    })
  },
  methods: {
    async requestTableData(queryData = {}, callback) {
      queryData = queryData || {}
      this.loading = true
      this.requestHistory = queryData
      if (!this.requestHistory.workTeamId)
        this.requestHistory.workTeamId = teamId
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      try {
        const {data} = await Axios.post(`/fnReceiptSettlement/getPage`, JSON2FormData(queryData))
        if (data.state !== 1) return this.$errorMsg('数据加载失败')
        this.total = data.total
        this.tableData = data.rows
        callback && callback(this.$refs.table, data.rows)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '数据加载失败')
      } finally {
        this.loading = false
      }
    },
    readRow(row) {
      this.$emit('read-row', row['settlementId'])
    },
    search(data, isFirst) {
      let postData = data.requestData
      let breadcrumbList = data.breadcrumbList
      postData.page = 1
      postData.rows = 20
      //breadcrumbList 面包屑导航
      this.breadcrumbList = breadcrumbList
      void isFirst
      // if (isFirst) {
      //   this.requestHistory = postData
      //   return this.updateColumnDisplay()
      // }
      this.requestTableData(postData)
    },
    async getSysOrgSetting(settingCode) {
      try {
        const {data: {state, data}} = await Axios.post('/sysSettingOrg/queryReceiptSet')
        if (state !== 1) await Promise.reject('获取设置失败！')
        const tmp = (data || []).find(it => it.settingCode ===  settingCode) || {}
        return tmp.settingValue === 'true'
      } catch (e) {
        console.warn(e)
      }
      return false
    },
  },
  filters: {
    bool2text(bool) {
      return bool ? '是' : '否'
    },
    number2money(num) {
      return '￥' + String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    receivablesAmountFilter(row) {
      const {
        receivablesAmount,
        receivablesCurrencyCode,
        receivablesCurrencyName,
        receivablesCurrencySymbol,
        settlementRate
      } = row
      if (!receivablesCurrencyCode || receivablesCurrencyCode === 'RMB') return ''
      let ret = receivablesAmount
      if (receivablesCurrencySymbol)
        ret = `${receivablesCurrencySymbol}${ret}`
      else if (receivablesCurrencyName)
        ret = `${ret}${receivablesCurrencyName}`
      else
        ret = `${ret}${receivablesCurrencyCode || 'RMB'}`
      return `已结算应收款${ret}  汇率${settlementRate || 1}`
    }
  },
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.footer {
  display: flex;
}

.footer p {
  margin-right: 50px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}
</style>