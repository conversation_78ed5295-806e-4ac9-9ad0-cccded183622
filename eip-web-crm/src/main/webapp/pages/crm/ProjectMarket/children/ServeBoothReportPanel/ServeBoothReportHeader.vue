<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="30px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="项目名称" prop="projectName" class="project--multiple">
          <el-select
            class="project"
            ref="project"
            v-model="projectIds"
            placeholder="选择项目名称"
            multiple
            collapse-tags
            popper-class="disallow-popper"
            clearable
            @hook:mounted="projectMultipleMounted"
            @change="selectChange"
            @clear="projectList = []"
            @click.native.stop="openProjectSelect"
          >
            <el-option v-for="pro in projectList" :key="pro.projectId" :value="pro.projectId" :label="pro.projectName"/>
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="searchForm.companyName" placeholder="公司名称" v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="展位号" prop="boothCode">
          <el-input v-model="searchForm.boothCode" placeholder="展位号" v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
    </el-header>
    <el-divider></el-divider>
    <transition name="el-fade-in-linear">
      <div class="more-info-popper" ref="more-project" v-show="showMoreInfo" :style="fixedPos">
        <span v-for="pro in projectList" :key="pro.projectId">{{ pro.projectName }}</span>
      </div>
    </transition>
    <project-select :show="showProjectSelect" ref="projectSelect" multiple @close="showProjectSelect=false"
                    @finish="selectFinish"/>
  </el-main>
</template>

<script>
const ProjectSelect = importC('ProjectSelect')
export default {
  name: "ServeBoothReportHeader",
  mixins: [Mixins.ProjectMultipleSelect()],
  data() {
    return {
      searchForm: {
        projectIds: Object.create(null),
        projectName: Object.create(null),
        companyName: '',
        boothCode: '',
      },
      showProjectSelect: false,
      dataMap: {
        projectName: '项目名称',
        companyName: '公司名称',
        boothCode: '展位号',
      },
    }
  },
  mounted() {
    delete extend.details
    const projectId = extend.projectId
    const projectName = extend.projectName
    delete extend.projectId
    delete extend.projectName
    Object.assign(this.searchForm, extend)
    projectId && projectName && this.selectFinish([{projectId, projectName}], true)
    this.search(true)
  },
  methods: {
    search(isFirst) {
      let searchValues = {
          projectIds: this.projectIds.toString(),
          companyName: this.searchForm.companyName,
          boothCode: this.searchForm.boothCode,
        },
        breadcrumbValues = {
          projectName: this.projectIds.map(projectId => {
            const p = this.projectList.find(item => projectId === item.projectId)
            if (!p) return ''
            return p.projectName
          }).join('、'),
          companyName: searchValues.companyName,
          boothCode: searchValues.boothCode,
        },
        result = {
          requestData: {},
          breadcrumbList: []
        }
      let breadcrumbKeys = Object.keys(breadcrumbValues)
      Object.keys(searchValues).forEach((k, index) => {
        let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
        if (val !== false && val !== 0 && !val) return
        result.requestData[k] = val
        result.breadcrumbList.push(`${this.dataMap[bk]}: ${bv}`)
      })
      this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
    },
    clear(...key) {
      key.forEach(k => this.$set(this.searchForm, k, ''))
    },
    clearAll() {
      this.clear(...Object.keys(this.searchForm))
      this.projectList = []
      this.projectIds = []
      this.searchForm.projectIds = Object.create(null)
      this.searchForm.projectName = Object.create(null)
    },
  },
  components: {
    ProjectSelect
  },
}
</script>
<style scoped>
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  /* min-width: 1400px; */
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.el-input {
  width: 140px;
}
</style>