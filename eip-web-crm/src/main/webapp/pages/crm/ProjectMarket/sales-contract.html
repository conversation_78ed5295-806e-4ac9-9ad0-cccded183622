<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title></title>
  <!--<script src="../../../js/vue2.dev.js"></script>-->
  <script src="../../../vue/vue.js"></script>
  <script src="../../../vue/httpVueLoader.js"></script>
  <script src="../../../vue/directive/dialogDrag.js?v=23.1"></script>
  <script src="../../../vue/axions.js"></script>
  <script src="../../../vue/element-ui@2.15.10/index.js"></script>
  <script src="../../../vue/Sortable.min.js"></script>
  <script src="../../../vue/vuedraggable.min.js"></script>
  <script src="../js/jquery.min.js"></script>
  <script src="../common/protocolcheck.js"></script>
  <script src="../common/utils.js?v=241025"></script>
  <link rel="stylesheet" href="../../../vue/element-ui@2.15.10/index.css">
  <link rel="stylesheet" href="../VueCommonComponents/base.css?v=2.0.0">
  <script src="./js/index.js?v=240624"></script>
  <script src="../VueCommonComponents/mixins.js?v=250122"></script>
  <script src="js/BoothNeed.js?v=230726"></script>

  <script src="/eip-web-crm/pages/test/GanttElastic.umd.js?v=250307"></script>
  <script src="../js/dayjs.min.js"></script>
  <script src="../js/exceljs.min.js"></script>
  <script src="../js/exceljs.polyfill.js"></script>
  <!--销售计划比对-->
  <script src="../js/echarts/echarts-v5.4.2.min.revision.js"></script>
  <!-- <script src="../js/FileSaver.min.js"></script> -->

  <script src="../VueCommonComponents/runtime.js"></script>
  <script src="../js/export2Excel.js?v=240621"></script>
  <script src="../../../js/login/md5.js"></script>

  <link rel="stylesheet" href="../../assets/vxe-table/style.css">
  <script src="../../assets/vxe-table/xe-utils.js"></script>
  <script src="../../assets/vxe-table/vxe-table.js"></script>

  <script src="../../../js/<EMAIL>"></script>
  <script src="../../../js/FileSaver.js"></script>
  <script src="../../../js/<EMAIL>"></script>
  <script src="../../../js/<EMAIL>"></script>
  <script src="../js/crm/export_docx.js?v=240124"></script>

  <!-- 发送短信,为了共用放到外层了,有问题的地方请改为外层调用 -->
  <!-- <script src="../components/common/custom_dialog.js"></script>
  <link rel="stylesheet" href="../components/style/custom_dialog.css?v=20221022">
  <script src="../components/common/sendMessage.js?v=22.11.08"></script>
  <link rel="stylesheet" href="../components/style/sendMessage.css"> -->
  <script src="../js/date-split.js?v=241220"></script>
  <link rel="stylesheet" href="../css/alex-table.css">
</head>
<body>
<div id="app"><App/></div>
<div id="load"
     v-loading.fullscreen.lock="loading"
     element-loading-text="拼命加载中"
     element-loading-spinner="el-icon-loading"
     element-loading-background="rgba(0, 0, 0, 0.1)">
</div>

<!--<script src="../js/image-compressor.js"></script>-->
<!--<script src="../js/uploadApp.js?v=240828"></script>-->
<script src="../js/compressor.min.js"></script>
<script src="../js/image-helper.js?v=250206"></script>

<script>
  const frameId = getQueryString('frameId')
  const teamId = getQueryString('teamId')
  const page = getQueryString('page')
  const extend = (_ex=>{
    try {
      return JSON.parse(decodeURIComponent(atob(_ex)))
    }catch (e) {
      return {}
    }
  })(getQueryString('extend'))
  const pickerOptions = {
    shortcuts: [{
      text: '最近一周',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit('pick', [start, end]);
      }
    }, {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        picker.$emit('pick', [start, end]);
      }
    }]
  }
  window.PathZZTPhoneDownload = ''; //  展之通电话
  window.PathZZTClientDownload = ''; //展之通短信邮件
  window.PathZZKDownload = ''; //展之客
  if(!top.queryArchiveState) top.queryArchiveState = function() {};
  window.vbus = new Vue(); //vue事件互通
  // export-dues(batchId)    : 批量导出收款单 : 整个批次完成了
  // export-docx({data,...}) : 批量导出(暂:收款单word) : 成功生成了某一个word
  // contract-import-select-project : 参展记录 批量导入 : 需要选择项目
  // contract-import-selected-project({id,text}) : 参展记录 批量导入 : 选择了某个项目
  // exManage-cq-select-projects : 参展记录 组合查询 : 需要选择项目
  // exManage-cq-selected-projects([{id,text}]) : 参展记录 组合查询 : 选择了项目
  // fix err
  top.$GlobalDefine = top.$GlobalDefine || {
    getSystemTime: fmt =>{ return fmt ? '2025-05-23 14:50:00' : new Date()},
    getNameByClientId : _ => { return ''},
  };
  top.tabs = top.tabs || {getTabByUrl: _ => {return null}};
  async function init()  {
    await window.run()
    // fix err 需要在 SetVueExtent 之后
    Vue.prototype.IS_ENABLE_FOREIGN_CURRENCY = top==window ? true : !!top.__IS_ENABLE_FOREIGN_CURRENCY__;
  }
  init()
</script>
</body>
</html>