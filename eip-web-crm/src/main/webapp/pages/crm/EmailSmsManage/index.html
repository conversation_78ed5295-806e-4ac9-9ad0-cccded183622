<!doctype html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>EMAIL-MANAGE</title>
  <!--  开发版-->
  <!--<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>-->
  <script src="../../../vue/vue.js"></script>
  <script src="../../../vue/httpVueLoader.js"></script>
  <script src="../../../vue/directive/dialogDrag.js?v=23.1"></script>
  <script src="../../../vue/axions.js"></script>
  <script src="../../../vue/element-ui/index.js"></script>
  <link rel="stylesheet" href="../VueCommonComponents/base.css">
  <link rel="stylesheet" href="../../../vue/element-ui/index.css">
  <script src="../js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="../../../vue/<EMAIL>"></script>
  <script src="../common/utils.js?v=241025"></script>
  <script src="./js/index.js?v=250618"></script>
  <script src="../VueCommonComponents/mixins.js?v=250122"></script>
  <script src="../components/common/custom_dialog.js"></script>
  <link rel="stylesheet" href="../components/style/custom_dialog.css?v=20221022">
  <script src="../components/common/customMessage.js"></script>
  <link rel="stylesheet" href="../components/style/customMessage.css">
  <script src="../VueCommonComponents/runtime.js"></script>
  <script src="../js/export2Excel.js"></script>
  <script src="js/testMessage.js"></script>
  <style>
    [v-cloak] {
      display: none
    }
  </style>
</head>
<body>
<div id="app">
  <App/>
</div>
<div id="load"
     v-loading.fullscreen.lock="loading"
     element-loading-text="拼命加载中"
     element-loading-spinner="el-icon-loading"
     element-loading-background="rgba(0, 0, 0, 0.1)">
</div>
<script>
  window.CURSOR_POSITION = {
    get(textarea) {
      let rangeData = {text: "", start: 0, end: 0};
      if (textarea.setSelectionRange) {
        // W3C
        textarea.focus();
        rangeData.start = textarea.selectionStart;
        rangeData.end = textarea.selectionEnd;
        rangeData.text =
          rangeData.start != rangeData.end
            ? textarea.value.substring(rangeData.start, rangeData.end)
            : "";
      } else if (document.selection) {
        // IE
        textarea.focus();
        var i,
          oS = document.selection.createRange(),
          // Don't: oR = textarea.createTextRange()
          oR = document.body.createTextRange();
        oR.moveToElementText(textarea);

        rangeData.text = oS.text;
        rangeData.bookmark = oS.getBookmark();

        // object.moveStart(sUnit [, iCount])
        // Return Value: Integer that returns the number of units moved.
        for (
          i = 0;
          oR.compareEndPoints("StartToStart", oS) < 0 &&
          oS.moveStart("character", -1) !== 0;
          i++
        ) {
          // Why? You can alert(textarea.value.length)
          if (textarea.value.charAt(i) == "\r") {
            i++;
          }
        }
        rangeData.start = i;
        rangeData.end = rangeData.text.length + rangeData.start;
      }

      return rangeData;
    },
    set(textarea, rangeData) {
      let oR, start, end;
      if (!rangeData) {
        alert("You must get cursor position first.");
      }
      textarea.focus();
      if (textarea.setSelectionRange) {
        // W3C
        textarea.setSelectionRange(rangeData.start, rangeData.end);
      } else if (textarea.createTextRange) {
        // IE
        oR = textarea.createTextRange();

        // Fixbug : ues moveToBookmark()
        // In IE, if cursor position at the end of textarea, the set function don't work
        if (textarea.value.length === rangeData.start) {
          //alert('hello')
          oR.collapse(false);
          oR.select();
        } else {
          oR.moveToBookmark(rangeData.bookmark);
          oR.select();
        }
      }
    },
    add(textarea, rangeData, text) {
      let oValue, nValue, oR, sR, nStart, nEnd, st;
      this.set(textarea, rangeData);

      if (textarea.setSelectionRange) {
        // W3C
        oValue = textarea.value;
        nValue =
          oValue.substring(0, rangeData.start) +
          text +
          oValue.substring(rangeData.end);
        nStart = nEnd = rangeData.start + text.length;
        st = textarea.scrollTop;
        textarea.value = nValue;
        // Fixbug:
        // After textarea.values = nValue, scrollTop value to 0
        if (textarea.scrollTop != st) {
          textarea.scrollTop = st;
        }
        textarea.setSelectionRange(nStart, nEnd);
      } else if (textarea.createTextRange) {
        // IE
        sR = document.selection.createRange();
        sR.text = text;
        sR.setEndPoint("StartToEnd", sR);
        sR.select();
      }
      return textarea.value;
    },
  }
  const teamId = getQueryString('teamId')
  const page = getQueryString('page') || sessionStorage.getItem('esm-page') || 'sms-template'
  const extend = (_ex => {
    try {
      return JSON.parse(decodeURIComponent(atob(_ex)))
    } catch (e) {
      return {}
    }
  })(getQueryString('extend'))
  window.run()
</script>
</body>
</html>