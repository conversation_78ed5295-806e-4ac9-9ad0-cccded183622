<template>
  <div class="SpecialSettings">
    <el-form class="SpecialSettingsForm" @submit.native.prevent :model="SpecialSettingsForm" size="medium">
      <el-form-item style="margin: 0">
        <el-checkbox v-model="SpecialSettingsForm.linkman_birthday_remind_sms"> 生日时给联系人发送短信</el-checkbox>
      </el-form-item>
      <template v-if="SpecialSettingsForm.linkman_birthday_remind_sms">
        <el-form-item
          label="提醒短信内容"
          style="width: var(--setting-width--std)">
          <embed-input
            v-model="SpecialSettingsForm.linkman_birthday_remind_sms_content"
            @reset="resetTemp(SpecialSettingsForm, 'linkman_birthday_remind_sms_content')"
            :autosize="{ minRows: 4, maxRows: 6 }"
          >
            <template v-slot="{ insert }">
              <el-button plain @click="insert('<姓名>')"> 嵌入姓名</el-button>
              <el-button plain @click="insert('<性别>')"> 嵌入女士/先生</el-button>
              <el-button plain @click="insert('<会员帐号>')"> 嵌入会员帐号</el-button>
            </template>
          </embed-input>
        </el-form-item>
      </template>

      <el-form-item>
        <el-checkbox @change="onRemindEmailChange" v-model="SpecialSettingsForm.linkman_birthday_remind_email">
          生日时给联系人发送邮件
        </el-checkbox>
      </el-form-item>
      <template v-if="SpecialSettingsForm.linkman_birthday_remind_email">
        <el-form-item>
          <span slot="label">
            提醒邮件标题
            <span class="label-end">建议：最大不超过50个字</span>
          </span>
          <el-input
            style="width: var(--setting-width--std)"
            placeholder="提醒邮件标题"
            v-model="SpecialSettingsForm.linkman_birthday_remind_email_subject"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="提醒邮件内容"
          style="width: var(--setting-width--std)">
          <div class="editor-wrapper" v-loading="uploading">
            <div ref="editor" style="padding: 10px 0;"></div>
            <ul class="button-list">
              <li>
                <button @click="editor.cmd.do('insertHTML', '<姓名>')">嵌入姓名</button>
              </li>
              <li>
                <button @click="editor.cmd.do('insertHTML', '<性别>')">嵌入女士/先生</button>
              </li>
              <li>
                <button @click="editor.cmd.do('insertHTML', '<会员账号>')">嵌入会员账号</button>
              </li>
              <li>
                <button @click="editor.txt.html(oldSpecialSettingsForm.linkman_birthday_remind_email_content)">还原
                </button>
              </li>
            </ul>
          </div>
        </el-form-item>
      </template>

      <el-form-item style="margin: 0">
        <el-checkbox v-model="SpecialSettingsForm.linkman_birthday_remind_operator"> 同时提醒给业务员</el-checkbox>
      </el-form-item>
      <template v-if="SpecialSettingsForm.linkman_birthday_remind_operator">
        <el-form-item label="提醒时间" style="width: var(--setting-width--std)">
          <div class="remind-operator">
            <span>提前</span>
            <div style="width: 80px;margin: 0 10px;">
              <el-input-number
                size="mini"
                :controls="false"
                :precision="0"
                :min="0"
                :max="99999"
                v-model="SpecialSettingsForm.linkman_birthday_remind_operator_before_days"></el-input-number>
            </div>
            <span>工作日，提醒给业务员</span>
          </div>
        </el-form-item>
      </template>
      <el-button style="width: 200px;margin-top: 20px;" type="primary" @click="saveOtherRemindForm">保存</el-button>
    </el-form>
    <custom-dialog append2body :show="showSourceEditor" @before-close="showSourceEditor=false" title="填入源代码"
                   width="800px">
      <el-main slot="body">
        <div style="display: flex;justify-content: flex-end">
          <el-button size="mini" icon="el-icon-circle-check" type="primary" @click="editSourceDone">确定</el-button>
        </div>
        <div style="padding: 10px 0">
          <el-input autofocus placeholder="填入源代码" resize="none" type="textarea"
                    :autosize="{minRows: 20, maxRows: 20}" v-model="tmpSource"></el-input>
        </div>
      </el-main>
    </custom-dialog>
  </div>
</template>

<script>
const EmbedInput = importC('EmbedInput')
const CustomDialog = importC('CustomDialog')
let openSourceEditor = /**@type {()=>void}*/null

class SourceMenu extends window.wangEditor.BtnMenu {
  constructor(editor) {
    // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
    const $elem = window.wangEditor.$(`<div class="w-e-menu" data-title="源代码"><i class="w-e-icon-terminal"></i></div>`)
    super($elem, editor)
  }

  clickHandler() {
    openSourceEditor && openSourceEditor()
  }

  tryChangeActive() {
    // pass
  }
}

export default {
  name: "SmsSpecialSettings",
  components: {
    EmbedInput,
    CustomDialog
  },
  data() {
    return {
      uploading: false,
      editor: null,
      tmpSource: '',
      showSourceEditor: false,
      SpecialSettingsForm: {
        linkman_birthday_remind_sms: false,
        linkman_birthday_remind_sms_content: '',
        linkman_birthday_remind_email: false,
        linkman_birthday_remind_email_subject: '',
        linkman_birthday_remind_email_content: '',
        linkman_birthday_remind_operator: false,
        linkman_birthday_remind_operator_before_days: void 0
      },
      oldSpecialSettingsForm: {
        linkman_birthday_remind_sms_content: '',
        linkman_birthday_remind_email_content: '',
      },
    }
  },
  mounted() {
    openSourceEditor = this.openSourceEditor.bind(this)
    this.syncBirthdayRemindSet().finally()
  },
  methods: {
    resetTemp(form, key) {
      this.$set(form, key, this.oldSpecialSettingsForm[key])
    },

    createWangEditor(el) {
      if (this.editor) return
      this.editor = new window.wangEditor(el)
      window.wangEditor.registerMenu('source', SourceMenu)
      this.editor.config.pasteIgnoreImg = true // 忽略粘贴内容中的图片
      this.editor.config.uploadImgMaxSize = 1024 * 1024 // 限制图片大小最大为 1MB
      this.editor.config.uploadImgMaxLength = 5 // 限制图片上传张数
      this.editor.config.focus = true
      this.editor.config.customAlert = (msg, type) => {
        this.uploading = false
        this.$errorMsg(msg, '提醒', type)
      }
      // this.editor.config.uploadImgShowBase64 = true
      this.editor.config.uploadImgServer = location.origin + '/eip-web-crm/upload/uploadFileOss'
      this.editor.config.uploadFileName = 'file'
      this.editor.config.uploadImgHooks = {
        before() {
          this.uploading = true
        },
        customInsert(insertImgFn, result) {
          this.uploading = false
          insertImgFn(result.result.path)
        },
      }
      this.editor.config.showFullScreen = false
      this.editor.config.height = 300
      this.editor.config.zIndex = 1e3
      this.editor.config.menus = [
        'head', // 标题
        'bold', // 粗体
        'fontSize', // 字号
        'fontName', // 字体
        'italic', // 斜体
        'underline', // 下划线
        'strikeThrough', // 删除线
        'foreColor', // 文字颜色
        'backColor', // 背景颜色
        'link', // 插入链接
        'list', // 列表
        'justify', // 对齐方式
        'quote', // 引用
        'image', // 插入图片
        'table', // 表格
        'source', // 源码模式
        'undo', // 撤销
        'redo' // 重复
      ]
      this.editor.create() // 创建富文本实例
    },
    openSourceEditor() {
      if (!this.editor) return console.warn('editor is null')
      this.tmpSource = this.editor.txt.html()
      this.showSourceEditor = true
    },
    editSourceDone() {
      this.editor.txt.html(this.tmpSource)
      this.showSourceEditor = false
    },
    async syncBirthdayRemindSet() {
      try {
        const {data} = await Axios.post('/sysSettingOrg/queryBirthdayRemindSet')
        if (data.state !== 1) await Promise.reject();
        (data.data || []).forEach(item => {
          const {settingCode, settingValue} = item
          let val = settingValue
          switch (settingCode) {
            case 'linkman_birthday_remind_sms':
            case 'linkman_birthday_remind_email':
            case 'linkman_birthday_remind_operator':
              val = !!val
              break
            case 'linkman_birthday_remind_operator_before_days':
              val = Number(val) || 0
              break
            case 'linkman_birthday_remind_email_content':
            case 'linkman_birthday_remind_sms_content':
              this.$set(this.oldSpecialSettingsForm, settingCode, val)
              break
          }
          this.$set(this.SpecialSettingsForm, settingCode, val)
        })
        this.onRemindEmailChange(this.SpecialSettingsForm.linkman_birthday_remind_email)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg('数据加载失败')
      }
    },
    async saveOtherRemindForm() {
      const loading = this.$loading({
        fullscreen: !0,
        text: '保存中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.1)',
        lock: !0,
        body: !0
      })
      try {
        const operEntrance = '特定短信邮件设置'
        this.SpecialSettingsForm.linkman_birthday_remind_email_content = this.editor.txt.html()
        const post = Object.keys(this.SpecialSettingsForm).map(settingCode => {
          const settingValue = this.SpecialSettingsForm[settingCode]
          return {
            settingCode,
            settingValue,
            operEntrance
          }
        })
        const {data} = await Axios.post('/sysSettingOrg/saveBirthdayRemindSet', post)
        if (data.state !== 1) await Promise.reject()
        this.$message.success({
          message: '保存成功',
          showClose: true
        })
        await this.syncBirthdayRemindSet()
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg('保存失败')
      } finally {
        loading.close()
      }
    },
    syncWangEditor() {
      this.$nextTick(() => {
        if (!this.$refs.editor) return
        this.createWangEditor(this.$refs.editor)
        this.editor.txt.html(this.SpecialSettingsForm.linkman_birthday_remind_email_content)
      })
    },
    onRemindEmailChange(val) {
      this.$nextTick(() => {
        if (!val) {
          this.editor = null
          return
        }
        this.syncWangEditor()
      })
    }
  }
}
</script>

<style scoped>
.SpecialSettings {
  --setting-width--std: 800px;
  height: 100vh;
  overflow: auto;
  padding-bottom: 40px;
  box-sizing: border-box;
}

.SpecialSettingsForm.el-form {
  padding: 20px;
}

.SpecialSettingsForm.el-form .form-flex {
  display: flex;
  align-items: center;
}

.SpecialSettingsForm.el-form .form-flex > div + div {
  margin-left: 30px;
}

.SpecialSettingsForm.el-form .form-flex .el-form-item.el-form-item--medium.ft-parent {
  position: relative;
  margin-bottom: 40px;
}

.SpecialSettingsForm.el-form .form-flex .el-form-item.el-form-item--medium.ft-parent .form-tips {
  position: absolute;
  bottom: -35px;
  left: 0;
  font-size: 13px;
  color: #999;
  width: 200%;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  width: 350px;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-form-item__label {
  padding: 0;
  text-align: left;
  color: #000;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-form-item__label .warn-icon {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #E6A23C;
  border-radius: 50%;
  margin-right: 3px;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-form-item__label .label-end {
  font-size: 13px;
  margin-left: 16px;
  color: #7c7a7a;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-form-item__label .label-end :not([class^=el-icon]) {
  color: rgb(244, 48, 48);
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-select,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-textarea,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-input {
  width: 100%;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-select .el-textarea__inner,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-select .el-input__inner,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-textarea .el-textarea__inner,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-textarea .el-input__inner,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-input .el-textarea__inner,
.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .el-input .el-input__inner {
  border-radius: 0;
  color: #5d5d5d;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .color-select {
  display: flex;
  align-items: center;
}

.SpecialSettingsForm.el-form .el-form-item.el-form-item--medium .color-select > span {
  margin-right: 15px;
}


.remind-operator {
  display: flex;
  align-items: center;
}

.editor-wrapper {
  position: relative;
}

.button-list {
  display: flex;
  padding: 0;
  margin: 0;
  list-style: none;
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  height: 40px;
  z-index: 2001;
}

.button-list li {
  flex: 1;
}

.button-list li button {
  width: 100%;
  height: 100%;
  background: none;
  border: 1px solid #eee;
  border-left-color: transparent;
}

.button-list li:first-child button {
  border-left-color: #eee;
}

.button-list li button:hover {
  color: #409EFF;
  border-color: #409EFF;
}
</style>