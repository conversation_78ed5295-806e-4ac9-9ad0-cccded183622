<!--
 * @Author: your name
 * @Date: 2021-10-29 13:39:47
 * @LastEditTime: 2021-10-29 14:33:35
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \webapp\pages\crm\EmailSmsManage\children\SmsLog\CombinedQuery\CqHeader.vue
-->
<template>
  <div>
    <div class="cq-header">
      <div class="left">
        筛选条件 <i class="el-icon-arrow-right"></i>
      </div>
      <div class="right tag-list">
        <el-tooltip
            v-if="v"
            v-for="(v,k,i) in tagData"
            :key="k"
            :content="v"
            placement="bottom"
            effect="light">
          <el-tag
              class="tag"
              effect="plain"
              type="primary"
              size="small"
              closable
              :disable-transitions="false"
              @close="handleClose(k)">
            {{ v.length >= 45 ? `${v.slice(0, 45)}...` : v }}
          </el-tag>
        </el-tooltip>
      </div>
    </div>
    <el-divider></el-divider>
  </div>
</template>

<script>
export default {
  name: "CqHeader",
  props: {
    tagData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    handleClose(k) {
      this.$emit('tag-close', k)
    }
  }
}
</script>

<style scoped>
.cq-header {
  display: flex;
  align-items: center;
  min-height: 50px;
  padding: 0 40px;
}

.cq-header .left {
  width: 100px;
  text-align: left;
  color: #409eff;
  min-width: 75px;
}

.cq-header .right {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  word-break: break-all;
  flex-wrap: wrap;
  height: 75px;
  overflow-y: auto;
  padding: 10px 0;
  box-sizing: border-box;
}

.el-divider {
  margin: 0;
}

.tag-list .tag {
  border-radius: 0;
  margin: .1rem;
}

.tag-list .tag.el-tag .el-icon-close {
  font-size: 13px;
}
</style>