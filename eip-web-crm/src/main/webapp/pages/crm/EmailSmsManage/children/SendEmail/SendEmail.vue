<template>
  <el-main class="send-email">
    <el-form
      @submit.native.prevent
      class="tForm"
      ref="tForm"
      :model="tForm"
      :rules="tFormRules"
      label-width="80px"
      size="mini">
      <div class="form-wrapper">
        <el-form-item prop="emailAccountId" label="发件邮箱" required class="flex">
          <el-select
              v-model="tForm.emailAccountId"
              filterable
              default-first-option
              clearable
              placeholder="发件邮箱">
            <el-option
                v-for="{sendEmail, emailAccountId, isDefault} in emailAccountList"
                :label="sendEmail"
                :value="emailAccountId"
                :key="emailAccountId"
            >
              <div class="option-box">
                <span v-text="sendEmail"></span>
                <el-tag v-if="isDefault" type="info" size="mini">默认</el-tag>
              </div>
            </el-option>
          </el-select>
          <el-button plain @click="dumpToSettings">设置</el-button>
        </el-form-item>
        <el-form-item prop="sender" label="发件人">
          <el-input v-model="tForm.sender" clearable placeholder="发件人"></el-input>
        </el-form-item>
        <el-form-item label="收件人" required style="flex-basis: 100%">
          <span>{{receiverCount ? `共${receiverCount}个`: '暂无'}}收件人</span>
          <el-button plain @click="openReceiverEditor">编辑</el-button>
        </el-form-item>
        <el-form-item prop="subject" label="邮件主题" style="flex-basis: 100%" required>
          <el-input v-model="tForm.subject" clearable placeholder="邮件主题"></el-input>
        </el-form-item>
        <el-form-item label="附件上传" style="flex-basis: 100%">
          <el-upload
              :on-preview="handlePreview"
              :on-remove="mHandleRemove"
              :on-success="mUploadSuccess"
              :before-remove="beforeRemove"
              :file-list="fileList"
              :action="uploadInfo.uploadAction"
              :data="uploadInfo.uploadData"
              ref="uploader"
              list-type="text"
              drag>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
      </div>
      <el-form-item class="flex-column">
        <template #label>
          <div class="content-label" @click.prevent>
            <span>邮件正文</span>
            <div class="buttons">
              <el-button plain @click="getLastByOperator">调上次发送内容</el-button>
              <el-button type="primary" @click="startSendEmail">发送</el-button>
            </div>
          </div>
        </template>
        <div class="editor-wrapper" v-loading="uploading">
          <div ref="editor" style="padding: 10px 0;"></div>
          <ul class="button-list" v-if="!isSingle">
            <li>
              <button @click="editor.cmd.do('insertHTML', '<公司名称>')">嵌入公司名称</button>
            </li>
            <li>
              <button @click="editor.cmd.do('insertHTML', '<姓名>')">嵌入姓名</button>
            </li>
            <li>
              <button @click="editor.cmd.do('insertHTML', '<性别>')">嵌入女士/先生</button>
            </li>
            <li>
              <button @click="editor.cmd.do('insertHTML', '<展会名称>')">嵌入展会名称</button>
            </li>
            <li>
              <button @click="editor.cmd.do('insertHTML', '<会员账号>')">嵌入会员账号</button>
            </li>
            <li>
              <button @click="editor.txt.html(lastContent)">还原</button>
            </li>
          </ul>
        </div>
      </el-form-item>
    </el-form>
    <recipient-dialog ref="recipient" :batch-id="batchId"></recipient-dialog>
    <custom-dialog append2body :show="showSourceEditor" @before-close="showSourceEditor=false" title="填入源代码" width="800px">
      <el-main slot="body">
        <div style="display: flex;justify-content: flex-end">
          <el-button size="mini" icon="el-icon-circle-check" type="primary" @click="editSourceDone">确定</el-button>
        </div>
        <div style="padding: 10px 0">
          <el-input autofocus placeholder="填入源代码" resize="none" type="textarea" :autosize="{minRows: 20, maxRows: 20}"  v-model="tmpSource"></el-input>
        </div>
      </el-main>
    </custom-dialog>
  </el-main>
</template>

<script>
class SourceMenu extends window.wangEditor.BtnMenu {
  constructor(editor) {
    // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
    const $elem = window.wangEditor.$(`<div class="w-e-menu" data-title="源代码"><i class="w-e-icon-terminal"></i></div>`)
    super($elem, editor)
  }
  clickHandler() {
    window.openSourceEditor()
  }

  tryChangeActive() {
    // pass
  }
}


  const CustomDialog = importC('CustomDialog')
  const RecipientDialog = importC('RecipientDialog')
  export default {
    name: "SendEmail",
    mixins: [upload('unset', 't_email_log'), clearAndInitForm()],
    inject: ['dumpToSettings'],
    data() {
      return {
        tForm: {
          emailAccountId: '',
          sender: '',
          subject: '',
        },
        tFormRules: {},
        emailAccountList: [],
        editor: null,
        emailLogId: '',
        docs: [],
        refId: '',
        batchId: '',
        receiverCount: 0,
        lastContent: '',
        isSingle: true,
        showSourceEditor: false,
        tmpSource: '',
        uploading: false
      }
    },
    mounted() {
      this.getEmailAccountList()
      this.$refs.editor && this.createWangEditor(this.$refs.editor)
      this.addRules()
      this.handleMessage()
      window.openSourceEditor = this.openSourceEditor.bind(this)
    },
    methods: {
      createWangEditor(el) {
        if (this.editor) return
        this.editor = new window.wangEditor(el)
        window.wangEditor.registerMenu('source', SourceMenu)
        this.editor.config.pasteIgnoreImg = true // 忽略粘贴内容中的图片
        this.editor.config.uploadImgMaxSize = 1024 * 1024 // 限制图片大小最大为 1MB
        this.editor.config.uploadImgMaxLength = 5 // 限制图片上传张数
        this.editor.config.focus = true
        this.editor.config.customAlert = (msg, type) => {
          this.uploading = false
          this.$errorMsg(msg, '提醒', type)
        }
        // this.editor.config.uploadImgShowBase64 = true
        this.editor.config.uploadImgServer = location.origin + '/eip-web-crm/upload/uploadFileOss'
        this.editor.config.uploadFileName = 'file'
        this.editor.config.uploadImgHooks = {
          before() {
            this.uploading = true
          },
          customInsert(insertImgFn, result) {
            this.uploading = false
            insertImgFn(result.result.path)
          },
        }
        this.editor.config.showFullScreen = false
        this.editor.config.height = 500
        this.editor.config.zIndex = 1e3
        this.editor.config.menus = [
          'head', // 标题
          'bold', // 粗体
          'fontSize', // 字号
          'fontName', // 字体
          'italic', // 斜体
          'underline', // 下划线
          'strikeThrough', // 删除线
          'foreColor', // 文字颜色
          'backColor', // 背景颜色
          'link', // 插入链接
          'list', // 列表
          'justify', // 对齐方式
          'quote', // 引用
          'image', // 插入图片
          'table', // 表格
          'source', // 源码模式
          'undo', // 撤销
          'redo' // 重复
        ]
        this.editor.create() // 创建富文本实例
        this._initForm()
      },
      getEmailAccountList() {
        Axios.post('/emailAccount/getPage', JSON2FormData({check: 1})).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg()
          this.emailAccountList = res.rows
          if (this.emailAccountList.length > 0) {
            const [{emailAccountId, sender}] = this.emailAccountList
            this.$set(this.tForm, 'emailAccountId', emailAccountId)
            this.$set(this.tForm, 'sender', sender)
          } else {
            this.$showMessage({message: '默认发件邮箱未设置，请在发件邮箱中设置并测试成功后再发送邮件！'})
          }
        }).catch(e => {
          this.$errorMsg()
          console.warn(e)
        })
      },
      getLastByOperator() {
        Axios.post('/emailLog/getLastByOperator').then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg()
          if (!res.data) return this.$message.info('暂无内容！')
          const {emailLogId, content, subject} = res.data
          this.$set(this.tForm, 'subject', subject)
          this.getDocuments(emailLogId, res => {
            this.docs = res.result.map(item => {
              const {f_doc_code, f_doc_file, f_doc_name, f_doc_suffix} = item
              return {f_doc_code, f_doc_file, f_doc_name, f_doc_suffix}
            })
          })
          this.lastContent = content
          this.editor.txt.html(content)
        }).catch(e => {
          this.$errorMsg()
          console.warn(e)
        })
      },
      addRules() {
        if (Object.keys(this.tFormRules).length === 0) {
          this.$refs.tForm && this.$refs.tForm.$children.forEach(item => {
            const {prop, label} = item
            if ('emailAccountId,subject'.split(',').includes(prop)) {
              const message = `${label}为必填项！`
              this.tFormRules[prop] = [{required: true, message, trigger: ['blur', 'change']}]
            }
          })
        }
        this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
      },
      startSendEmail() {
        const loading = this.$loading({
          fullscreen: !0,
          text: '正在发送中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        this.validateForm()
          .then(isValid => {
            if (!isValid) return Promise.reject("Unexpected data 'isValid'")
            const postData = Object.assign({}, this.tForm)
            postData.content = this.editor.txt.html()
            postData.emailBatchId = this.batchId
            if (this.receiverCount ===0) return Promise.reject(-1)
            postData.docs = this.docs
            return Axios.post('/emailLog/sendEmailGroup', postData)
          })
          .then(({data: res}) => {
            if (res.state !== 1)
              return Promise.reject(res.state)
            this.batchId = ''
            this.receiverCount = 0
            this.$confirm('发送成功！', '提示', {
              type: 'success',
              confirmButtonText: '返回',
              distinguishCancelAndClose: true,
              cancelButtonText: '再写一封',
            }).then(() => {
              //激活指定Id的标签页并发送激活消息
              if(!this.refId)return
              SendMessageByIframeId(this.refId, JSON.stringify({type: 'active'}), false)
              activeTabByTabId(this.refId.replace('iframe_', ''))
              killTab()
            }).catch(flag => {
              //再来一封
              if (flag === 'cancel') {
                this.clear('subject')
                this.$refs.uploader.clearFiles()
                this.fileList = []
                this.docs = []
                this.editor.txt.html('')
                this.$focusError(()=>this.$refs['tForm'] && this.$refs['tForm'].clearValidate())
              }
            })
            loading.close()
          })
          .catch(errors => {
            if (typeof errors === 'number') {
              let message = (errors === 2 ? '发件箱信息不存在或不完善' : '网络错误') + ', 发送失败！'
              if (errors === -1) message = '收件人不能为空'
              this.$showMessage({message})
            }
            const err = this.handleErrors(errors)
            err && console.warn(err)
            loading.close()
          })
      },
      mUploadSuccess() {
        const {result: {f_doc_code, f_doc_file, f_doc_name, f_doc_suffix}} = this.uploadSuccess(...arguments)
        this.docs.push({f_doc_code, f_doc_file, f_doc_name, f_doc_suffix})
      },
      mHandleRemove() {
        const f_doc_code = this.handleRemove(...arguments)
        const removeIndex = this.docs.findIndex(item => item.f_doc_code === f_doc_code)
        this.docs.splice(removeIndex, 1)
      },
      async processMultiplePayload(payload) {
        try {
          const {uri} = payload
          delete payload.uri
          const {data} = await Axios.post(uri, JSON2FormData({...payload, emailBatchId: this.batchId}))
          if (data.state !== 1) await Promise.reject(data.msg || '收件人加载失败！')
        } catch (e) {
          this.$errorMsg(String(e))
        }
      },
      handleMessage() {
        this.refId = getQueryString('refId')
        if (!this.refId) return console.warn('refId is undefined')
        const iframeId = getCurrentIframeId()
        //回应跳转位置的消息
        SendMessageByIframeId(this.refId, JSON.stringify({type: 'notice', iframeId}), false)
        const processData = async data => {
          if (data.type !== 'multiple' && data.type !== 'single') return
          const loading = this.$loading({background: 'rgba(0, 0, 0, 0.3)', text: '加载中...'})
          try {
            if (!data) return
            this.eventObject = data || {}
            await this.getBatchId()
            const recipient = this.$refs.recipient
            if (!recipient) return console.warn('recipient is null')
            const {type, payload} = data
            if (type === 'multiple') {
              this.isSingle = false
              await this.processMultiplePayload(payload)
              this.receiverCount = await recipient.close()
            } else if (type === 'single') {
              this.isSingle = true
              this.receiverCount = await recipient.trySaveReceiver(null, {...payload}, true)
            }
          } finally {
            loading.close()
          }
        }
        try {
          processData(JSON.parse(sessionStorage.getItem(this.refId)))
        } catch (e) {
          console.warn('获取消息失败')
        }
        ListenMessage(event => {
          try {
            processData(JSON.parse(event.data))
          } catch (e) {
            console.log(e)
          }
        })
      },
      async openReceiverEditor() {
        if (!this.batchId) {
          await this.getBatchId()
          await this.$nextTick()
        }
        const recipient = this.$refs.recipient
        if (!recipient) return console.warn('recipient is null')
        this.receiverCount = await recipient.open()
      },
      async getBatchId() {
        try {
          const {data} = await Axios.post('/batch/getBatchId')
          if (data.state !== 1) await Promise.reject()
          this.batchId = data.data
        }catch (e) {
          e && e !== 'cancel' && console.warn(e)
          this.$errorMsg('批次号获取失败')
        }
      },
      openSourceEditor() {
        if (!this.editor) return console.warn('editor is null')
        this.tmpSource = this.editor.txt.html()
        this.showSourceEditor = true
      },
      editSourceDone() {
        this.editor.txt.html(this.tmpSource)
        this.showSourceEditor = false
      }
    },
    components: {
      RecipientDialog,
      CustomDialog
    }
  }
</script>

<style scoped>
  .send-email {
    height: calc(100vh - 38px);
  }

  .send-email .tForm {
    max-width: 1550px;
  }

  .send-email .el-upload {
    display: block;
  }

  .send-email .el-upload .el-upload-dragger {
    width: auto;
    height: auto;
    line-height: 30px;
  }

  .send-email .form-wrapper {
    max-width: 920px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }

  .send-email .form-wrapper .el-form-item {
    flex-basis: 50%;
  }

  .send-email .form-wrapper .el-form-item.flex .el-form-item__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .send-email .form-wrapper .el-form-item.flex .el-select {
    flex: 1;
  }

  .send-email .form-wrapper .el-form-item.flex .el-button {
    margin-left: 10px;
    flex-basis: 76px;
  }

  .send-email .el-form-item.flex-column {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
  }

  .send-email .el-form-item.flex-column .el-form-item__label {
    width: 100% !important;
    height: 32px;
    padding-right: 0;
  }

  .el-form-item__label .content-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .el-form-item__label .content-label > span {
    flex-basis: calc(80px - 12px);
  }
  .editor-wrapper {
    position: relative;
  }

  .button-list {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    height: 40px;
    z-index: 2001;
  }
  .button-list li {
    flex: 1;
  }

  .button-list li button {
    width: 100%;
    height: 100%;
    background: none;
    border: 1px solid #eee;
    border-left-color: transparent;
  }
  .button-list li:first-child button {
    border-left-color: #eee;
  }
  .button-list li button:hover {
    color: #409EFF;
    border-color: #409EFF;
  }
</style>
<style>
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>