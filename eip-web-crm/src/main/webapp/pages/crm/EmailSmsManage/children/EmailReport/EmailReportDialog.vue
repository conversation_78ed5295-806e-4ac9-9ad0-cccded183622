<template>
  <custom-dialog
      title="查看详情"
      append2body
      @before-close="close"
      width="895px"
      :show="show">
    <el-main slot="body" class="email-report">
      <el-form
          class="tForm"
          ref="tForm"
          :model="tForm"
          label-width="80px"
          size="mini">
        <div class="form-wrapper">
          <el-form-item prop="sendEmail" label="发件邮箱">
            <el-input readonly clearable placeholder="发件邮箱" v-model="tForm.sendEmail"></el-input>
          </el-form-item>
          <el-form-item prop="sender" label="发件人">
            <el-input v-model="tForm.sender" readonly clearable placeholder="发件人"></el-input>
          </el-form-item>
          <el-form-item prop="receiveEmail" label="收件邮箱">
            <el-input v-model="tForm.receiveEmail" readonly clearable placeholder="收件邮箱"></el-input>
          </el-form-item>
          <el-form-item prop="receiver" label="收件人">
            <el-input v-model="tForm.receiver" readonly clearable placeholder="收件人"></el-input>
          </el-form-item>
          <el-form-item prop="subject" label="邮件主题" style="flex-basis: 100%">
            <el-input v-model="tForm.subject" readonly clearable placeholder="邮件主题"></el-input>
          </el-form-item>
          <el-form-item label="附件上传" style="flex-basis: 100%">
            <el-upload
                disabled
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="uploadSuccess"
                :before-remove="beforeRemove"
                :file-list="fileList"
                :action="uploadInfo.uploadAction"
                :data="uploadInfo.uploadData"
                ref="uploader"
                list-type="text"
                drag>
              <div class="el-upload__text"><em style="color: #606266">点击下方文件列表查看文件</em></div>
            </el-upload>
          </el-form-item>
          <el-form-item label="邮件正文" style="flex-basis: 100%">
            <div v-html="tForm.content" class="el-input__inner" style="height: auto; min-height: 30px;"></div>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
    <el-footer slot="footer" class="email-report-footer">
      <el-button type="primary" size="small" @click="close">确定</el-button>
    </el-footer>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  export default {
    name: "EmailSettingsDialog",
    mixins: [upload('unset', 't_email_log'), clearAndInitForm()],
    data() {
      return {
        show: false,
        tForm: {
          emailLogId: '',
          sendEmail: '',
          sender: '',
          receiveEmail: '',
          receiver: '',
          subject: '',
          content: ''
        },
      }
    },
    methods: {
      open(id) {
        this._initForm()
        this.queryById(id)
        this.show = true
      },
      queryById(emailLogId) {
        if (!emailLogId) return this.$errorMsg('数据异常，请刷新重试！')
        Axios.post('/emailLog/getById', JSON2FormData({emailLogId})).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg()
          if (res.data === null)
            return this.$errorMsg('数据可能已被删除, 请刷新重试！').finally(this.close)
          Object.keys(this.tForm).forEach(key => {
            let v = res.data[key]
            this.$set(this.tForm, key, v)
          })
          this.getDocuments(emailLogId)
        }).catch(e => {
          console.error(e)
          this.$errorMsg()
        })
      },
      close() {
        this.show = false
      },
    },
    components: {
      CustomDialog
    }
  }
</script>

<style scoped>
  .el-dialog__footer {
    padding: 0;
    background-color: #fafafa;
  }

  .email-report-footer.el-footer {
    width: 232px;
    height: 54px;
    padding: 0;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: center;
  }

  .email-report-footer.el-footer .el-button {
    width: 91px;
    height: 32px;
  }

  .email-report {
    max-height: 500px;
  }

  .email-report .el-upload {
    display: block;
  }

  .email-report .el-upload .el-upload-dragger {
    width: auto;
    height: auto;
    line-height: 30px;
  }

  .email-report .form-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }

  .email-report .form-wrapper .el-form-item {
    flex-basis: 50%;
  }

</style>