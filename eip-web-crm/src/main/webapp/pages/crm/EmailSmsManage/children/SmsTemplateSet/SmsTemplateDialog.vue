<template>
  <custom-dialog
    title="短信模板"
    append2body
    id="smsTemplate"
    @before-close="close"
    @closed="saving=false"
    @opened="addRules"
    width="635px"
    :show="show">
    <el-main slot="body" class="sendSMS">
      <el-header height="40px">每67个字收取一条短信费，含空格、标点符号以及签名字数</el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :disabled="mode === 'read'"
        :model="tForm"
        :rules="tFormRules"
        label-width="118px"
        size="mini">
        <el-form-item label="模板名称" prop="tempName" required>
          <el-input v-model="tForm.tempName" v-focus style="width: 100%;" clearable></el-input>
        </el-form-item>
        <el-form-item style="margin-bottom: 10px;">
          <el-checkbox-group @change="sendWayChange" v-model="tForm.sendWay">
            <el-checkbox :label="1">逐条发送（慢）</el-checkbox>
            <el-checkbox :label="2">打包发送（快）</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="tForm.sendWay[0]===2" label="打包每批次" prop="phoneNum">
          <el-input-number
            :precision="0"
            :min="1" :max="10000"
            :controls="false"
            v-model="tForm.phoneNum"
            style="width: 100%;" ></el-input-number>
        </el-form-item>
        <el-form-item label="短信内容" prop="content" required>
          <el-input ref="textA" type="textarea" :class="{single: tForm.sendWay[0]===2}" placeholder="请输入短信内容"
                    v-model="tForm.content" maxlength="255">
          </el-input>
          <el-button-group v-if="tForm.sendWay[0]===1">
            <el-button size="small" plain @click="insertText('<姓名>')">嵌入姓名</el-button>
            <el-button size="small" plain @click="insertText('<性别>')">嵌入女士/先生</el-button>
            <el-button size="small" plain @click="insertText('<展会名称>')">嵌入展会名称</el-button>
            <el-button size="small" plain @click="insertText('<会员账号>')">嵌入会员账号</el-button>
            <el-button size="small" plain @click="resetText">还原</el-button>
          </el-button-group>
          <el-button-group v-if="tForm.sendWay[0]===2" :style="{width:'100%'}">
            <el-button size="small" :style="{flex:'1'}" plain @click="insertText('<展会名称>')">嵌入展会名称</el-button>
            <el-button size="small" :style="{flex:'1'}" plain @click="resetText">还原</el-button>
          </el-button-group>
        </el-form-item>
        <el-form-item label="签名" class="sms-sign" prop="sign" required>
          【
          <el-input style="width: 200px;" clearable v-model="tForm.sign"></el-input>
          】
        </el-form-item>
        <el-form-item label="短信报备情况" required prop="report">
          <el-select v-model="tForm.report" style="width: 100%;" placeholder="报备情况" clearable>
            <el-option label="已报备" :value="1"></el-option>
            <el-option label="未报备" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信通道账号" required prop="smsAccountId">
          <el-select
            v-model="tForm.smsAccountId"
            style="width: 100%;"
            placeholder="短信通道账号"
            filterable
            default-first-option
            clearable>
            <el-option
              v-for="item in smsAccountList"
              :key="item.id"
              :label="item.text"
              style="height: auto;"
              :value="item.id">
              <div style="line-height: 25px;padding: 6px 0;">
                <span>{{ item.text }}<br/></span>
                <span style="font-weight: normal;color: #999;">{{ item.memo }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联团队" prop="workTeamId">
          <el-select
            v-model="tForm.workTeamId"
            style="width: 100%;"
            placeholder="关联团队"
            filterable
            default-first-option
            clearable>
            <el-option
              v-for="item in teamList"
              :key="item.id"
              :label="item.text"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="部门" prop="branchId">
            <el-select ref="bsel" clearable v-model="tForm.branchName" @clear="clear('branchId', 'branchName')">
              <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
                <el-tree
                  :expand-on-click-node="false"
                  :data="departmentList"
                  :default-expanded-keys="departmentList[0] ? [(departmentList[0]||{})['f_branch_id']] : null"
                  :props="{ children: 'children', label: 'text'}"
                  highlight-current
                  node-key="id"
                  @node-click="departmentChange"
                  ref="tree">
                </el-tree>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联人员" prop="operatorId">
            <el-select
              v-model="tForm.operatorId"
              clearable
              filterable
              default-first-option
              placeholder="业绩归属人">
              <el-option
                v-for="item in operatorList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
    <el-footer slot="footer" class="sendSMSFooter">
      <el-button plain size="small" @click="close">取消</el-button>
      <el-button type="primary" size="small" @click="save" :loading="saving">保存{{ saving ? '中...' : '' }}</el-button>
    </el-footer>
  </custom-dialog>
</template>

<script>

const CustomDialog = importC('CustomDialog')
export default {
  name: "SmsAccountDialog",
  mixins: [Mixins.requestOperator()],
  data() {
    return {
      cursorPosition: {...window.CURSOR_POSITION},
      tForm: {
        smsTempId: '',
        tempName: '',
        sendWay: [1],
        phoneNum: 100,
        report: '',
        content: '',
        sign: '',
        workTeamId: '',
        smsAccountId: '',
        createTime: '',
        operatorId: '',
        branchId: '',
        branchName: '',
      },
      tFormRules: {},
      mode: 'add',
      counter: null,
      show: false,
      saving: false,
      isChange: false,
      smsAccountList: [],
      departmentList: [],
      teamList: [],
      oldContent: '' //用于还原内容
    }
  },
  methods: {
    resetText() {
      this.$set(this.tForm, 'content', this.oldContent)
    },
    clear(...keys) {
      keys.forEach(key => {
        this.$set(this.tForm, key, '')
      })
    },
    sendWayChange(val) {
      val.length >= 2 && this.tForm.sendWay.shift()
    },
    insertText(text) {
      let textarea = this.$refs.textA.$el.firstChild;
      let pos = this.cursorPosition.get(textarea);
      let newVal = this.cursorPosition.add(textarea, pos, text)
      if (newVal.length >= 255) {
        this.tForm.content = newVal.slice(0, 255);
        return this.$errorMsg('内容不能超过255个字符', '提示', 'warning')
      }
      this.tForm.content = newVal;
    },
    save() {
      if (this.mode === 'read') return
      this.saving = true
      const postData = {}
      Object.keys(this.tForm).forEach(key => {
        const value = this.tForm[key]
        if (!value) return
        postData[key] = value
      })
      postData.sendWay = postData.sendWay[0]
      postData.operEntrance = '短信模板设置'
      this.$refs.tForm.validate()
        .then(() => {
          return Axios.post('/smsTemp/save', JSON2FormData(postData))
        })
        .then(({data: res}) => {
          if (res.state !== 1)
            return Promise.reject(res.msg || '保存失败')
          this.$msg('保存成功！', 'success')
          this.$emit('operation-finished')
          this.isChange = false
          this.close()
        })
        .catch(e => {
          if (typeof e === 'boolean') {
            return this.$errorMsg('带 * 为必填项!').finally(() => this.saving = false)
          } else if (typeof e === 'string') {
            return this.$errorMsg(e).finally(() => this.saving = false)
          }
          console.log(e);
          this.saving = false
        })

    },
    close() {
      if (this.isChange) {
        return this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.isChange = false
          this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
          this.show = false
        }).catch(e => e)
      }
      this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
      this.show = false
    },
    addRules() {
      if (Object.keys(this.tFormRules).length === 0) {
        this.$refs.tForm && this.$refs.tForm.$children.forEach(item => {
          const {prop, label} = item
          if (!prop || !label || ['workTeamId', 'operatorId', 'branchId'].includes(prop)) return
          const message = `${label}为必填项！`
          this.tFormRules[prop] = [
            {required: true, message, trigger: ['blur', 'change']},
          ]
        })
        this.tFormRules.phoneNum = [
          {required: true, message: `打包每批次数量为必填项`, trigger: ['blur', 'change']},
        ]
      }
      if (!this.counter) {
        let counter = document.createElement('span')
        counter.setAttribute('class', 'el-input__counter')
        this.$refs.textA && this.$refs.textA.$el.appendChild(counter)
        counter.textContent = '0/1';
        this.counter = counter;
        const content = this.oldContent
        const len = content.length + this.tForm.sign.length + 2
        this.counter.textContent = len + '/' + Math.ceil(len / 67)
      }
      this.$refs.tForm && this.$refs.tForm.clearValidate()
    },
    open(mode, smsTempId) {
      this.mode = mode
      if ((mode === 'read' || mode === 'edit') && smsTempId) {
        this.requestById(smsTempId, () => setTimeout(() => this.isChange = false))
      } else if (mode === 'add') {
        this.resetForm()
        setTimeout(() => this.isChange = false)
      } else return console.warn('数据异常！')
      this.show = true
    },
    requestById(id, callback) {
      Axios.post('/smsTemp/getById', JSON2FormData({smsTempId: id})).then(({data: res}) => {
        if (res.state !== 1)
          return this.$msg('模板数据加载失败！')
        Object.keys(this.tForm).forEach(key => {
          let value = res.data[key]
          if (key === 'sendWay')
            value = [value]
          else if (key === 'content')
            this.oldContent = value
          else if (key === 'branchId') {
            const item = this.findDepartment(value)
            if (!item) return
            return this.departmentChange(item, true)
          } else if (key === 'branchName') return
            else if (key === 'phoneNum') value = value || 100
          this.$set(this.tForm, key, value)
        })
        callback && callback()
      }).catch(e => {
        console.warn(e)
        this.$msg('模板数据加载失败！')
      })
    },
    resetForm() {
      const rest = {
        sendWay: [1],
        workTeamId: window.top.__DEFAULT_TEAM_ID__ || '',
        phoneNum: 100
      }
      this.clear(...Object.keys(this.tForm))
      Object.keys(rest).forEach(key => this.$set(this.tForm, key, rest[key]))
      this.oldContent = ''
    },
    getSmsAccount() {
      Axios.post('/smsAccount/getPage').then(({data: res}) => {
        if (res.state !== 1) return Promise.reject()
        this.smsAccountList = res.rows
      }).catch(err => {
        console.error(err)
      })
    },
    getTeamList() {
      Axios.post('workTeam/getList').then(({data: res}) => {
        if (res.length < 1) return Promise.reject()
        this.teamList = res
      }).catch(err => {
        console.error(err)
      })
    },
    async getDepartmentList() {
      try {
        const {data} = await Axios.post('/branch/getTree')
        this.departmentList = data
      } catch (e) {
        this.$errorMsg('获取部门列表失败！')
      }
    },
    departmentChange({id, text}, skipClear) {
      this.$set(this.tForm, 'branchId', id)
      this.$set(this.tForm, 'branchName', text)
      !skipClear && this.clear('operatorId')
      this.requestOperatorBase({branchId: id})
      this.$refs.bsel.blur()
    },
    findDepartment(id) {
      let result
      const deep = (list) => {
        if (!Array.isArray(list) || !list.length) return
        list.forEach(item => {
          if (item.id === id) {
            result = item
            return
          }
          deep(item.children)
        })
      }
      deep(this.departmentList)
      return result
    }
  },
  mounted() {
    this.getSmsAccount()
    this.getTeamList()
    this.getDepartmentList()
    this.requestOperatorBase()
  },
  watch: {
    'tForm.content'(_new) {
      if (!this.counter) return;
      const len = _new.length + this.tForm.sign.length + 2
      this.counter.textContent = len + '/' + Math.ceil(len / 67)
    },
    'tForm.sign'(_new) {
      if (!this.counter) return;
      const len = _new.length + 2 + this.tForm.content.length
      this.counter.textContent = len + '/' + Math.ceil(len / 67)
    },
    'tForm.sendWay'(_new, _old) {
      if (!_new.length && _old.length !== 0) {
        this.$set(this.tForm, 'sendWay', _old)
      }
    },
    tForm: {
      deep: true,
      handler() {
        if (!this.isChange) this.isChange = true
      }
    },
  },
  components: {
    CustomDialog,
  },
}
</script>

<style scoped>
#smsTemplate .sendSMS {
  padding: 0;
}

#smsTemplate .sendSMS .el-header {
  padding: 0;
  color: #f74444;
  box-shadow: 0 0 10px #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}

#smsTemplate .sendSMS .tForm {
  padding: 20px 40px 20px 30px;
}

#smsTemplate .sendSMS .tForm .el-textarea textarea {
  height: 175px;
  padding-bottom: 36px;
  border-color: #DCDFE6;
}

#smsTemplate .sendSMS .tForm .el-textarea textarea:focus {
  box-shadow: 0 0 5px #DCDFE6;
}

#smsTemplate .sendSMS .tForm .el-textarea .el-input__counter {
  color: #409EFF;
  position: absolute;
  font-size: 12px;
  bottom: 36px;
  right: 10px;
}

#smsTemplate .sendSMS .tForm .el-textarea.single .el-input__counter {
  bottom: 5px;
}

#smsTemplate .sendSMS .tForm .el-button-group {
  /*width: 434px;*/
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

#smsTemplate .sendSMS .tForm .el-button-group .el-button {
  /*width: 109px;*/
}

#smsTemplate .sendSMS .tForm .el-button-group .el-button:focus,
#smsTemplate .sendSMS .tForm .el-button-group .el-button:hover {
  border-color: #DCDFE6;
}

#smsTemplate .sendSMS .tForm .el-button-group .el-button:first-child {
  border-bottom-left-radius: 4px;
}

#smsTemplate .sendSMS .tForm .el-button-group .el-button:last-child {
  border-bottom-right-radius: 4px;
  /*width: 108px;*/
}

#smsTemplate .sendSMS .tForm .sms-sign .el-form-item__error {
  padding-left: 15px;
}

#smsTemplate .sendSMSFooter {
  width: 230px;
  margin: auto;
  padding: 0;
  height: 46px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #efefef;
}

#smsTemplate .sendSMSFooter .el-button {
  width: 90px;
  height: 32px;
}

#smsTemplate .el-dialog__footer {
  margin-top: -20px;
  background: #f0f0f0;
  padding: 0;
}

#smsTemplate .is-disabled,
#smsTemplate .is-disabled:hover,
#smsTemplate .is-disabled *,
#smsTemplate .is-disabled *:hover {
  background-color: transparent !important;
  border-color: #aaa !important;
  color: #666 !important;
}

</style>