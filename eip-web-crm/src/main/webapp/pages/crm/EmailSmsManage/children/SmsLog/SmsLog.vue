<template>
  <div>
    <sms-log-header
        ref="header"
        @export="exportTable"
        @show-query="$refs['cQuery'].open()"
        @combined-query="$refs['cQuery'].query($event)"
        @delete="deleteSmsLogger"
        @resend="resendSms"
        @search="search">
    </sms-log-header>
    <div class="all-Indicate-areas" style="display: flex; justify-content: space-between">
      <div class="all_areas">
        <p>当前检索：</p>
        <p class="all_areas_now_text" :title="queryTip + superQueryTip">
          {{ queryTip ? '普通查询 > ' +  queryTip : ''}}
          {{ superQueryTip ? '组合查询 > ' +  superQueryTip : ''}}
        </p>
      </div>
    </div>
    <sms-log-body
        ref="body"
        @table-select="tableSelection=$event"
        @read-row="$refs.dialog && $refs.dialog.open($event)"
        @clear-search="$refs.header && $refs.header.clearAll($event)">
    </sms-log-body>
    <combined-query ref="cQuery" @combined-query="searchQuery"></combined-query>
    <resend-sms ref="resend"></resend-sms>
  </div>
</template>

<script>
  const SmsLogHeader  = importC('SmsLogHeader')
  const SmsLogBody    = importC('SmsLogBody')
  const SmsLogDialog  = importC('SmsLogDialog')
  const CombinedQuery = importC('CombinedQuery')
  const ResendSms = importC('ResendSms')
  export default {
    name: "SmsLog",
    data() {
      return {
        tableSelection: [],
        queryTip: '',
        superQueryTip: '',
      }
    },
    provide() {
      return {
        // 1：验证及通知短信  2：单发营销短信 3：群发营销短信
        sendModeList: [
          {text: '验证及通知短信', id: 1},
          {text: '单发营销短信', id: 2},
          {text: '群发营销短信', id: 3},
        ],
      }
    },
    methods: {
      _export(_exportList, fileName, loading) {
        const $body = this.$refs['body']
        const $mainFields = $body.fields
        const propList = $mainFields.map(it => it.prop)
        const labelList = $mainFields.map(it => it.label)
        const tHead = ['序号', ...labelList]
        const mapFunc = (item, index) => {
          let temp = {index: index + 1}
          propList.forEach(prop => {
            let v = item[prop]
            if (prop === 'state') {
              v = v ? '成功' : '失败'
            }
            temp[prop] = v
          })
          return temp
        }
        const exportList = _exportList.map(mapFunc)
        exportObject2Excel({
          header: tHead,
          body: exportList,
          title: '短信发送日志',
          search: $('.all_areas_now_text').text(),
          fileName,
        })
        loading && loading.close()
      },
      exportTable() {
        const loading = this.$loading({
          fullscreen: !0,
          text: '正在导出...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        if (this.tableSelection.length < 1)
          return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
            .then(() => this.exportAll(loading))
            .catch(() => loading.close())
        this._export(this.tableSelection, `短信发送日志`, loading)
      },
      exportAll(loading) {
        const data = {...this.$refs['body'].requestHistory}
        console.log('exportAll' , data)
        delete data.rows
        delete data.page
        Axios.post('/sms/getPage', JSON2FormData(data)).then(({data: res}) => {
          if (res.state !== 1) return Promise.reject()
          if(res.rows && !res.rows.length) return this.$errorMsg('没有数据可以导出!')
          this._export(res.rows,`短信发送日志-导出全部`, loading)
        }).catch(e => {
          e && console.warn(e)
          this.$errorMsg('导出失败!')
        })
      },
      async deleteSmsLogger() {
        try {
          if (this.tableSelection.length < 1)
            return this.$errorMsg('请选择要删除的数据')
          await this.$confirm('确定要删除选中的数据吗？', '提示', {type: 'warning'})
          const xhs = this.tableSelection.map(it => it.xh)
          const {data} = await Axios.post('/sms/delete', JSON2FormData({xhs, operEntrance: '短信发送日志'}))
          if (data.state !== 1) await Promise.reject('删除失败')
          this.$message.success('删除成功')
          this.$refs.body.refresh()
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '删除失败')
        }
      },
      async resendSms() {
        const loading = this.$loading({
          fullscreen: !0,
          text: '正在重发...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        try {
          if (this.tableSelection.length < 1)
            await this.$confirm('您未选择数据，是否重发全部失败的数据', '提示', {type: 'info'})
          const post = {}
          if (this.tableSelection.length < 1) {
            Object.assign(post, this.$refs['body'].requestHistory)
          } else {
            post.xhs = this.tableSelection.map(it => it.xh)
          }
          const {data} = await Axios.post('/sms/getResendNum', JSON2FormData(post))
          if (data.state !== 1) await Promise.reject()
          const config = data.data
          const result = await this.$refs.resend.open(config)
          if (!config.resend) await Promise.reject('没有需要发送的短信')
          console.log(result)
          const sendPost = Object.assign({}, post, result)
          const {data: sendResult} = await Axios.post('/sms/resend', JSON2FormData(sendPost))
          if (sendResult.state !== 1) await Promise.reject()
          this.$message.success('重发成功')
          this.$refs.body.refresh()
        }catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          this.$errorMsg(e || '删除失败')
        } finally {
          loading.close()
        }
      },
      // clearAll(){
      //   this.tags = []
			// 	for(let k of Object.keys(this.form)) {
			// 		this.$set(this.form, k , '')
			// 	}
      // },
      // 组合查询
      searchQuery({tags,params}){
        // clear 普通查询
        this.queryTip = ''
        this.$refs['header'].clearAll()
        let msg = ''
        if(tags) {
          Object.keys(tags).map(i=> msg += ' ' + tags[i])
        }
        this.superQueryTip = msg
        this.$refs['body'].search({
          requestData: {
            sjhLeft: params.phone.v,
            xm: params.receiver.v,
            pch: params.batchNo.v,
            pcmc: params.batchName.v,
            phoneNum: params.count.v,
            state: params.state.v,
            sendTimeStart: params.sendTimeStart.v,
            sendTimeEnd: params.sendTimeEnd.v,
            planTimeStart: params.planTimeStart.v,
            planTimeEnd: params.planTimeEnd.v,
            xxnr: params.content.v,
            account: params.account.v,
            serviceType: params.serviceType.v,
            operatorId: params.operatorId.v,
            sendMode: params.sendMode.v,
          },
          breadcrumbList: tags
        })
      },
      // 普通搜索
      search({requestData,breadcrumbList}){
        // clear 组合查询
        this.superQueryTip = '';
        this.$refs['cQuery'].formReset()
        let tip = '';
        for(let i in breadcrumbList) {
          if(breadcrumbList[i]) tip +=  '  ' + i + ': ' + breadcrumbList[i] + ';'
        }
        this.queryTip = tip
        const $body = this.$refs['body']
        $body.search({requestData,breadcrumbList})
      }
    },
    components: {
      SmsLogHeader,
      SmsLogBody,
      SmsLogDialog,
      CombinedQuery,
      ResendSms,
      // DialogCust: dialog_mon
    }
  }
</script>

<style scoped>
/* 所有页面跳转指示 */
.all-Indicate-areas {
	width: 100%;
	overflow: hidden;
	box-sizing: border-box;
	background-color: #fff;
	/* border-top: 1px solid #e0e0e0; */
  padding-left: 18px;
}

.all_areas {
	float: left;
	/* margin-top: 4px; */
	font-size: 14px;
  margin-bottom: -5px;
}

.all_areas_now_text {
	float: left;
}

.all_areas_now_text>p {
	max-width: 340px;
}

.all_areas p {
	float: left;
	color: #999;
	font-size: 14px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
  margin: 5px;
}

.all_areas p a {
	font-size: 14px;
	color: #666;
	max-width: 431px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	vertical-align: bottom;
}

.all_areas p a:hover {
	color: #30B5FF;
}
</style>