<template>
  <el-main style="padding: 0 20px">
    <div style="text-align:center">
      <span class="title">邮件发送批次记录</span>
    </div>
    <el-divider></el-divider>
    <el-header class="top-header" height="40px" style="min-width: 1310px">
      <el-form inline size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="批次" prop="batchName">
          <el-input v-model="searchForm.batchName" style="width: 200px;" placeholder="批次" v-enter="search"
                    clearable></el-input>
        </el-form-item>
        <el-form-item prop="sendTimeStart" label="发送时间">
          <el-date-picker
            style="width: 200px"
            v-model="searchForm.sendTimeStart"
            type="datetime"
            default-time="00:00:00"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="mini"
            clearable
            @clear="clear('sendTimeStart')"
            placeholder="选择时间">
          </el-date-picker>
          到
          <el-date-picker
            style="width: 200px"
            v-model="searchForm.sendTimeEnd"
            type="datetime"
            default-time="23:59:59"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="mini"
            clearable
            @clear="clear('sendTimeStart')"
            placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业务员" prop="operatorName">
          <el-select
            v-model="searchForm.operatorName"
            placeholder="选择业务员"
            clearable
            filterable
            default-first-option
            @clear="clear('operatorName','operatorId')"
            @change="$bothWayStore($event,'operators','operatorId','operatorName','searchForm')"
          >
            <el-option
              v-for="operator in operators"
              :label="operator.operatorName"
              :key="operator.operatorId"
              :value="operator.operatorName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发送邮箱" prop="sendEmail">
          <el-input v-model="searchForm.sendEmail" style="width: 200px;" placeholder="发送邮箱" v-enter="search"
                    clearable></el-input>
        </el-form-item>


        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
    </el-header>
  </el-main>
</template>

<script>
export default {
  name: "EmailSettingsHeader",
  data() {
    return {
      searchForm: {
        operatorId: '',
        operatorName: '',
        batchName: '',
        sendEmail: '',
        sendTimeStart: [new Date(top.$GlobalDefine.getSystemTime(null).setDate(1)).format('yyyy-MM-dd'), '00:00:00'].join(' '),
        sendTimeEnd: '',
      },
      dataMap: {
        batchName: '批次名称',
        sendEmail: '发送邮箱',
        operatorName: '业务员',
        sendTimeStart: '发送时间起',
        sendTimeEnd: '发送时间止',
      },
      operators: [],
    }
  },
  methods: {
    search(isFirst) {
      let searchValues = {
          batchName: this.searchForm.batchName,
          sendTimeStart: this.searchForm.sendTimeStart,
          sendTimeEnd: this.searchForm.sendTimeEnd,
          sendEmail: this.searchForm.sendEmail,
          operatorId: this.searchForm.operatorId,
        },
        breadcrumbValues = {
          batchName: this.searchForm.batchName,
          sendTimeStart: this.searchForm.sendTimeStart,
          sendTimeEnd: this.searchForm.sendTimeEnd,
          sendEmail: this.searchForm.sendEmail,
          operatorName: this.searchForm.operatorName,
        },
        result = {
          requestData: {},
          breadcrumbList: []
        }
      let breadcrumbKeys = Object.keys(breadcrumbValues)
      Object.keys(searchValues).forEach((k, index) => {
        let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
        if (!val) return
        result.requestData[k] = val
        result.breadcrumbList.push(`${this.dataMap[bk]}: ${bv}`)
      })
      this.$emit('search', result, isFirst === true)
    },
    clear(...key) {
      key.forEach(k => this.$set(this.searchForm, k, ''))
    },
    getOperatorList() {
      Axios.post('/operator/selectByMap', JSON2FormData({workTeamId: teamId, showAllState: true})).then(({data: res}) => {
        this.operators = res.map(v => ({operatorId: Number(v.id), operatorName: v.text}))
      })
    },
    clearAll() {
      this.clear(...Object.keys(this.searchForm))
    }
  },
  mounted() {
    this.getOperatorList()
    this.search(true)
  },
}
</script>

<style scoped>
.title {
  font-family: MicrosoftYaHei Sans-serif serif;
  font-size: 20px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 52px;
  padding-top: 10px;
  letter-spacing: 0;
  color: #000000;
}

.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.el-input {
  width: 120px;
}

.el-date-editor {
  width: 245px;
}

.right-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-btn-box-title {
  display: block;
  float: right;
  margin-top: -37px;
  margin-right: 12px;
}

.right-btn-box .el-button.btn, .right-btn-box-title .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right-btn-box .el-button.btn:hover, .right-btn-box-title .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}
</style>