<template>
  <div>
    <div class="content-main" id="page-service">
      <div class="main">
        <div class="top">
          <div v-if="serviceOpen" class="top-img">
            <img src="../image/ring.png" class="rotating" alt="">
            <img src="../image/email.png" alt="" style="position: absolute;left: 8px;">
          </div>
          <div v-else class="top-img">
            <img src="../image/pause.png" alt="">
          </div>
          <div v-if="serviceOpen" style="flex: 1;">
            <div class="top-text1">短信服务开启中</div>
            <div class="top-text2">短信服务检测正常，通知类、营销类通道短信正在发送</div>
          </div>
          <div v-else style="flex: 1">
            <div class="top-text1">短信服务已暂停</div>
            <div class="top-text2">短信发送批次已暂停发送</div>
          </div>
          <el-button size="mini" type="text"
                     @click="serviceSetting.show = true"
                     icon="el-icon-time"
          >{{ serviceSetting.limit ? '超' + serviceSetting.limit + '小时不再发送' : '设置短信超时时长' }}
          </el-button>
          <el-button size="mini" :disabled="serviceFreshing" :class="{'no-animate': !serviceFreshing}"
                     icon="el-icon-loading" @click="serviceInit">刷新
          </el-button>
          <el-button v-if="serviceOpen" size="mini" @click="serviceStateChange(false)">关闭服务</el-button>
          <el-button v-else size="mini" type="primary" @click="serviceStateChange(true)">开启服务</el-button>
        </div>
        <div style="margin: 20px 0;width:100%;border-top:1px solid #ebedf0;"></div>
        <div class="top-sum">
          <div style="width: 323px;">
            <div class="top-sum-text1">待发送任务数</div>
            <div class="top-sum-text2" v-cloak @click="openSmsLog(0)" style="color:#1890ff;cursor: pointer;">
              {{ serviceSum.unSendSms.unSendSmsNum | number3 }}
            </div>
            <div class="top-sum-text3" v-cloak @click="openSmsLog(0)" style="cursor: pointer;">包含手机
              {{ serviceSum.unSendSms.includeMobileNum | number3 }} 个
            </div>
          </div>
          <div style="width: 2px;height: 37px;background: #eff0f3;margin-right: 70px; "></div>
          <div>
            <div class="top-sum-text1">今日已发送短信数</div>
            <div class="top-sum-text2" v-cloak @click="openSmsLog(1)" style="color:#1890ff;cursor: pointer;">
              {{ serviceSum.sendSms.sendSmsNum | number3 }}
            </div>
            <div class="top-sum-text3" v-cloak @click="openSmsLog(1)" style="cursor: pointer;">包含手机
              {{ serviceSum.sendSms.includeMobileNum | number3 }} 个
            </div>
          </div>
        </div>
      </div>
    </div>
    <custom-dialog
      title="设置短信超时时长"
      :show="serviceSetting.show"
      append2body
      width="450px"
      class="mock-dialog"
      @before-close="serviceSetting.show=false">
      <div slot="body" style="padding: 20px 35px;">
        <div style="padding: 0 0 20px;display: flex;flex-flow: row nowrap;justify-content: space-between">
          <div></div>
          <el-button
            :disabled="!serviceSetting.limit"
            size="mini"
            type="primary"
            @click="saveServiceSetting"
            icon="el-icon-circle-check">确定
          </el-button>
        </div>
        <div>
          <div>
            短信发送任务时间超过
            <el-input
              v-model.number="serviceSetting.limit"
              style="width: 91px;"
              size="mini"
              placeholder="默认168小时(7天)"
              clearable>
            </el-input>
            小时，取消发送。
          </div>
        </div>
      </div>
    </custom-dialog>

  </div>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "SmsServiceStatus",
  components: {CustomDialog},
  data() {
    return {
      serviceOpen: false, // 是否开启了短信服务
      serviceFreshing: false,
      serviceSetting: {
        show: false,
        limit: 0,    // 短信未发超时时间, 小时, 默认168h
      },
      serviceSum: {
        sendSms: {
          sendSmsNum: 0,
          includeMobileNum: 0,
        },
        unSendSms: {
          includeMobileNum: 0,
          unSendSmsNum: 0,
        },
      },
      debounceTimer: null, // 防抖器
    }
  },
  mounted() {
    this.serviceInit(true)
  },
  methods: {
    debounceMethods(func, ...args) {
      console.log('click');
      let context = this;
      let callNow = !this.debounceTimer;    //是否立即执行
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.debounceTimer = null;
      }, 500)
      if (callNow) {
        func.apply(context, args)
      } else {
        console.log('操作过快 ! 已阻止 !')
      }
    },
    fmtMoney(val, pos = 2) {
      return (+val || 0).toFixed(pos);
    },
    obj2qs(o) {
      o = o || {}
      return JSON.stringify(o).replace(/:/g, '=').replace(/,/g, '&').replace(/{/g, '?').replace(/}/g, '').replace(/"/g, '')
    },
    obj2fd(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      return this.$alert(msg, title, {type})
    },
    error(msg, throws = true) {
      msg = msg || '请求失败';
      const p = this.errorMsg(msg, '提醒', 'warning');
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message) {
      message = message || '操作成功!';
      return this.$message({
        message,
        type: 'success'
      });
    },
    checkRet(obj, errMsg = '', okMsg = '') {
      const {state, msg, data} = obj
      if (state !== 1) this.error(errMsg || msg)
      if (okMsg) this.ok(okMsg === true ? '' : okMsg);
    },
    async getServiceSetting() {
      const {data} = await Axios
        .post('sysSettingOrg/querySmsTimeOut', this.obj2fd())
        .catch(_ => this.error())
      this.checkRet(data)
      this.serviceSetting.limit = +data.data || 168
    },
    async saveServiceSetting() {
      const {data} = await Axios
        .post('sysSettingOrg/setSmsTimeOut', this.obj2fd({
          settingCode: 'smsTimeOut',
          settingValue: this.serviceSetting.limit,
        }))
        .catch(_ => this.error())
      this.checkRet(data, '', '设置成功')
      this.serviceSetting.show = false;
    },
    serviceFreshWaiting(wait = true) {
      if (wait) {
        setTimeout(() => {
          this.serviceFreshing = false
        }, 500);
      } else {
        this.serviceFreshing = false
      }
    },
    openSmsLog(state) {
      state = +state;
      const paras = {
        state,
      }
      if (state === 1) { // 今天的
        const today = top.$GlobalDefine.getSystemTime('yyyy-MM-dd')
        paras.sendTimeStart = today + ' 00:00:00';
        paras.sendTimeEnd = today + ' 23:59:59';
      }
      const extend = btoa(encodeURIComponent(JSON.stringify(paras)));
      FinalLinkOpen('EmailSmsManage/index.html?page=sms_log&teamId=' + teamId + '&extend=' + extend, null, true, false,
        '短信发送日志(待发送)');
    },
    serviceInit(wait = true) { // 短信服务初始化
      this.serviceFreshing = true
      Promise.all([
        this.serviceStateGet(),
        this.getServiceSetting(),
        this.serviceSumGet()
      ]).then(_ => {
        this.serviceFreshWaiting(wait)
      }).catch(e => {
        this.serviceFreshWaiting(wait)
      });
    },
    serviceStateGet() {
      return Axios.post('/sysSettingOrg/querySmsSwitch', {})
        .then(({data: rsp}) => {
          const {state, msg} = rsp
          if (state !== 1) this.$msg('查询发生错误，错误信息：' + msg)
          this.serviceOpen = msg === 'true'
        }).catch(e => {
          console.warn(e)
          this.$errorMsg()
        })
    },
    serviceSumGet() {
      this.serviceSum = {
        sendSms: {
          sendSmsNum: 0,
          includeMobileNum: 0,
        },
        unSendSms: {
          includeMobileNum: 0,
          unSendSmsNum: 0,
        },
      };
      return Axios.post('/sms/queryCountSmsNum', {})
        .then(({data: rsp}) => {
          const {state, msg, data} = rsp
          if (state !== 1) this.$msg('查询发生错误，错误信息：' + msg)
          this.serviceSum.sendSms.sendSmsNum = data.sendSms.sendSmsNum || 0;
          this.serviceSum.sendSms.includeMobileNum = data.sendSms.includeMobileNum || 0;
          this.serviceSum.unSendSms.includeMobileNum = data.unSendSms.includeMobileNum || 0;
          this.serviceSum.unSendSms.unSendSmsNum = data.unSendSms.unSendSmsNum || 0;
        }).catch(e => {
          console.warn(e)
          this.$errorMsg()
        })
    },
    serviceStateChange(flag) {
      return Axios.post('/sysSettingOrg/saveSmsSwitch', JSON2FormData({
        settingCode: 'smsPauseSwitch',
        settingValue: !!flag,
        operEntrance: '短信服务状态',
      }))
        .then(({data}) => {
          const {state, msg} = data
          if (state !== 1) this.$msg('查询发生错误，错误信息：' + msg)
          this.serviceStateGet()
        }).catch(e => {
          console.warn(e)
          this.$errorMsg()
        })
    },
  }
}
</script>

<style scoped>
.no-animate i {
  animation: unset;
}

.rotating {
  animation: rotating 4s infinite linear;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

#page-service {
  background-color: #F0F2F5;
  padding: 0;
}

#page-service .main {
  padding: 32px 112px 25px;
  background-color: white;
}

#page-service .top {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}

#page-service .top-img {
  width: 136px;
  padding-left: 8px;
  position: relative;
}

#page-service .top-text1 {
  color: #333;
  font-size: 20px;
  padding-bottom: 11px;
}

#page-service .top-text2 {
  color: #666a77;
  font-size: 15px;
}

#page-service .top-sum {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#page-service .top-sum-text1 {
  color: #01163c;
  font-size: 16px;
}

#page-service .top-sum-text2 {
  color: #262626;
  font-size: 24px;
  padding: 11px 0 5px;
}

#page-service .top-sum-text3 {
  color: #8c8c8c;
  font-size: 12px;
}

#page-service .el-button--text {
  color: #409EFF !important;
}
</style>