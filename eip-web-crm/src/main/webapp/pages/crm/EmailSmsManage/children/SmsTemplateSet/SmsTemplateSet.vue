<template>
  <div>
    <el-main style="padding: 20px 0 0">
      <el-header class="top-header" height="30px">
        <el-form inline size="mini" :model="searchForm" ref="searchForm" class="search-form-inline">
          <el-form-item label="模板名称" prop="tempName">
            <el-input
              v-model="searchForm.tempName"
              placeholder="模板名称"
              v-focus
              v-enter="search"
              clearable></el-input>
          </el-form-item>
          <el-form-item label="模板内容" prop="content">
            <el-input v-model="searchForm.content" placeholder="模板内容" v-enter="search" clearable></el-input>
          </el-form-item>
          <el-form-item label="报备情况" prop="report">
            <el-select v-model="searchForm.report" placeholder="报备情况" clearable>
              <el-option label="已报备" :value="1"></el-option>
              <el-option label="未报备" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="search">检索</el-button>
          </el-form-item>
        </el-form>
      </el-header>
      <el-divider></el-divider>
    </el-main>
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right-btn-box">
        <el-button size="mini" class="btn" @click="addRows">新增</el-button>
        <el-button size="mini" class="btn" @click="deleteRows($refs.table.selection())">删除</el-button>
      </div>
    </el-header>
    <div>
      <alex-table
        ref="table"
        :data="templateData"
        height="calc(100vh - 200px)"
        border
        :fields="fields"
        size="small"
        class="alex-table"
        mode="multiple"
        :operation-width="90"
        @sort-change="sort"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="tempName">
          <el-link type="primary" :underline="false" @click.stop="readRow(row)">{{ row.tempName }}</el-link>
        </template>
        <template slot-scope="{row}" slot="report">{{ row.report === 1 ? '已报备' : '未报备' }}</template>
        <template slot-scope="{row}" slot="sendWay">{{ row.sendWay === 1 ? '逐条发送' : '打包发送' }}</template>
        <template slot-scope="{row}" slot="alex-operation">
          <div class="operation">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="editRow(row)">修改
            </el-link>
          </div>
        </template>
      </alex-table>
    </div>
    <sms-template-dialog ref="SmsTemplateDialog" @operation-finished="refresh"></sms-template-dialog>
  </div>
</template>

<script>
const SmsTemplateDialog = importC('SmsTemplateDialog')
export default {
  name: "SmsTemplateSet",
  mixins: [Mixins.table(), Mixins.clearForm()],
  components: {SmsTemplateDialog},
  data() {
    return {
      searchForm: {
        tempName: '',
        content: '',
        report: '',
      },
      dataMap: {
        tempName: '模板名称',
        content: '模板内容',
        report: '报备情况',
      },
      breadcrumbList: [],
      templateData: [],
      fields: [],
      total: 0,
      loading: false
    }
  },
  created() {
    this.fields = [
      {label: '模板名称', prop: 'tempName'},
      {label: '报备情况', prop: 'report'},
      {label: '发送方式', prop: 'sendWay'},
      {label: '短信内容', prop: 'content'},
      {label: '短信通道账号', prop: 'smsAccountName', sortable: false},
      {label: '团队名称', prop: 'workTeamName'},
      {label: '部门', prop: 'branchName'},
      {label: '关联人员', prop: 'employeeName'},
      {label: '创建时间', prop: 'createTime'}
    ]
  },
  mounted() {
    this.refresh()
  },
  methods: {
    search() {
      const data = {}
      let breadcrumbKey
      switch (this.page) {
        case 'template':
          breadcrumbKey = 'breadcrumbList'
          break
        case 'account':
          breadcrumbKey = 'accountBreadcrumbList'
          break
      }
      this.breadcrumbList.length = 0
      Object.keys(this.searchForm).forEach(key => {
        if (!this.searchForm[key]) return
        const value = this.searchForm[key]
        let text = `${this.dataMap[key]}: ${value}`
        if (key === 'report')
          text = `${this.dataMap[key]}: ${+value === 1 ? '已报备' : +value === 2 ? '未报备' : ''}`
        data[key] = value
        this.breadcrumbList.push(text)
      })
      this.requestTableData(data)
    },
    requestTableData(queryData = {}, callback) {
      queryData = queryData || {}
      callback = callback || (() => this.loading = false)
      this.loading = true
      this.requestHistory = queryData
      if (!getAuthority('fu_all_team_manage')) {
        queryData['workTeamIdOrNull'] = window.teamId || window.top.teamId
      }
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      Axios.post('/smsTemp/getPage', JSON2FormData(queryData)).then(({data: res}) => {
        if (res.state !== 1)
          return Promise.reject()
        this.templateData = res.rows
        this.total = res.total
      }).catch(e => {
        e && console.log(e)
        return this.$errorMsg('数据加载失败！')
      }).finally(() => this.loading = false)
    },
    addRows() {
      this.editRow({}, 'add')
    },
    readRow(row) {
      this.editRow(row, 'read')
    },
    editRow(row, mode) {
      if (Array.isArray(row)) {
        if (row.length === 0)
          return this.$errorMsg('请选择要修改的数据', '提醒', 'warning')
        if (row.length > 1)
          return this.$errorMsg('单次只能修改一条数据', '提醒', 'warning')
        row = row[0]
      }
      this.$refs.SmsTemplateDialog && this.$refs.SmsTemplateDialog.open(mode || 'edit', row.smsTempId)
    },
    deleteRows(list) {
      this.delBatch(list.map(item => ({smsTempId: item.smsTempId, operEntrance: '短信模板设置'})), '/smsTemp/delBatch')
    },
  }
}
</script>

<style scoped>
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  min-width: 900px;
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.top-header .el-input {
  width: 120px;
}

.el-date-editor {
  width: 245px;
}

.right-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-btn-box .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right-btn-box .el-button.btn:last-child {
  margin: 0;
}

.right-btn-box .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}

.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.clear-search:hover > span {
  cursor: pointer !important;
  color: #2e82e4 !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>