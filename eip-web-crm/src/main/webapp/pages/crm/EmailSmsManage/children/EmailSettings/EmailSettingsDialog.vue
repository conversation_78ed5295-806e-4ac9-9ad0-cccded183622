<template>
  <custom-dialog
      :title="title"
      append2body
      @before-close="close"
      @opened="addRules"
      @closed="saving=false"
      width="895px"
      :show="show">
    <el-main slot="body" class="email-settings">
      <el-form
          class="tForm"
          ref="tForm"
          :disabled="mode === 'read'"
          :model="tForm"
          :rules="tFormRules"
          label-width="130px"
          size="mini">
        <el-form-item prop="emailHost" label="SMTP服务器" required>
          <el-input v-model="tForm.emailHost" clearable></el-input>
        </el-form-item>
        <el-form-item prop="emailPort" label="SMTP端口号" required>
          <el-input-number :min="0" :max="65535" v-model="tForm.emailPort" :precision="0" clearable
                           :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item prop="sendEmail" :label="isOrgSetting ? '验证码发件邮箱' : '发件邮箱'" required>
          <el-input v-model="tForm.sendEmail" clearable></el-input>
        </el-form-item>
        <el-form-item prop="password" label="授权码/密码" required>
          <el-input v-model="tForm.password" :show-password="mode !== 'read'" clearable></el-input>
        </el-form-item>
        <el-form-item prop="sender" label="发件人">
          <el-input v-model="tForm.sender" clearable></el-input>
        </el-form-item>
        <el-form-item prop="replyEmail" label="回复邮箱">
          <el-input v-model="tForm.replyEmail"  clearable></el-input>
        </el-form-item>
        <el-form-item prop="isDefault" label="默认发送账号">
          <el-select v-model="tForm.isDefault" style="width: 100%">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="emailSsl" label="SSL协议加密">
          <el-checkbox-group v-model="tForm.emailSsl" @change="res => res.length === 2 && res.shift()">
            <el-checkbox :label="true">是</el-checkbox>
            <el-checkbox :label="false">否</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
          <div>
            <div style="vertical-align: top; text-align: center;margin-left: 30px;
        width: 13px;height: 13px;font-size: 10px;
        margin-top: 4px;line-height: 13px;background-color: rgb(245, 108, 108);
        border-radius: 50%; display: inline-block; color: rgb(255, 255, 255);">?</div>
            <div style="width:740px;line-height:20px;color:#b7b7b7;font-size:13px;display: inline-block;"> 发件箱为：QQ邮箱、163邮箱、126邮箱、139邮箱、新浪邮箱需填写授权码。有的平台需要填写邮箱密码，因此，账号密码认证失败时，可以授权码和密码都试试。<br >
            SSL加密与否，对应的SMTP端口号可能不一样，具体以各邮箱平台的说明为准。</div>
          </div>
      </el-form>
    </el-main>
    <el-footer slot="footer" class="email-settings-footer">
      <el-button plain size="small" @click="close">取消</el-button>
      <el-button type="primary" :disabled="mode === 'read'" size="small" :loading="saving" @click="save">保存{{saving? '中...': ''}}</el-button>
    </el-footer>
  </custom-dialog>
</template>

<script>
  const CustomDialog = importC('CustomDialog')
  export default {
    name: "EmailSettingsDialog",
    mixins:[listenChange(), clearAndInitForm()],
    inject: {
      isOrgSetting: {
        default: false
      }
    },
    data() {
      return {
        mode: 'add',
        show: false,
        saving: false,
        tForm: {
          emailAccountId: '',
          emailHost: '',
          emailPort: '',
          emailSsl: [false],
          sendEmail: '',
          password: '',
          sender: '',
          replyEmail: '',
          isDefault: false,
        },
        tFormRules: {}
      }
    },
    methods: {
      open(mode, id) {
        this.mode = mode
        this.show = true
        if (mode === 'edit' || mode === 'read') {
          this.queryById(id, this.startChange)
        } else {
          this.initForm()
        }
      },
      queryById(emailAccountId, callback){
        if (!emailAccountId) return this.$errorMsg('数据异常，请刷新重试！')
        Axios.post('/emailAccount/getById', JSON2FormData({emailAccountId})).then(({data: res})=>{
          if(res.state !== 1)return this.$errorMsg()
          if (res.data === null)
            return this.$errorMsg('数据可能已被删除, 请刷新重试！').finally(this.close)
          Object.keys(this.tForm).forEach(key=>{
            let v
            if(key==='emailSsl') v = [!!res.data[key]]
            else v = res.data[key]
            this.$set(this.tForm, key, v)
          })
          callback(res.data)
        }).catch(e=>{
          console.error(e)
          this.$errorMsg()
        })
      },
      save(){
        this.saving = true
        this.validateForm()
          .then(isValid=>{
          if(!isValid)return Promise.reject("Unexpected data 'isValid'")
          const postData = Object.assign({}, this.tForm)
          postData.emailSsl = !!postData.emailSsl[0]
          postData.accountType = this.isOrgSetting ? 2 : 1
          return Axios.post('/emailAccount/save', JSON2FormData(postData))
        })
          .then(({data: res})=>{
            if(res.state !== 1) return Promise.reject(res.state)
            this.$message.success('保存成功')
            this.isChange = false
            this.close()
            this.$emit('operation-complete')
          })
          .catch(errors=>{
            if(typeof errors === 'number'){
              let message
              switch (errors) {
                case 2:
                  message = '该SMTP服务器下已存在此邮箱账号，不能保存！'
                  break
                case 3:
                  message = '已有默认发送邮箱，请先取消原默认发送邮箱！'
                  break
                default:
                  message = '数据加载失败！'
              }
              message && this.$showMessage({message})
            }
            else{
              const err = this.handleErrors(errors)
              err && console.warn(err)
            }
            this.saving = false
          })
      },
      initForm(){
        this._initForm({
          emailSsl: [false],
          isDefault: false,
        }, this.startChange)
      },
      close() {
        return this._close()
      },
      addRules() {
        if (Object.keys(this.tFormRules).length === 0) {
          this.$refs.tForm && this.$refs.tForm.$children.forEach(item => {
            const {prop, label} = item
            if ('emailHost,emailPort,sendEmail,password'.split(',').includes(prop)) {
              const message = `${label}为必填项！`
              this.tFormRules[prop] = [{required: true, message, trigger: ['blur', 'change']}]
            }
          })
          this.tFormRules['sendEmail'].push({type: 'email', message: '请输入正确的邮箱！', trigger: ['change', 'blur']})
          this.tFormRules['emailPort'].push({type: 'number', min: 1, message: '端口号必须大于0！', trigger: ['change', 'blur']})
          this.tFormRules['replyEmail'] = [{type: 'email', message: '请输入正确的邮箱！', trigger: ['change', 'blur']}]
        }
        this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
      },
    },
    computed: {
      title() {
        switch (this.mode) {
          case 'add':
            return '新增'
          case 'edit':
            return '编辑'
          case 'read':
            return '查看'
        }
      }
    },
    components: {
      CustomDialog
    }
  }
</script>

<style scoped>
  .el-main.email-settings{
    padding: 0;
  }
  .el-form.tForm{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    padding: 30px 50px;
  }
  .el-form.tForm .el-form-item{
    flex-basis: 50%;
  }
  .el-dialog__footer{
    padding: 0;
    background-color: #fafafa;
  }
  .email-settings-footer.el-footer{
    width: 232px;
    height: 54px;
    padding: 0;
    display: flex;
    margin: auto;
    align-items: center;
    justify-content: space-between;
  }
  .email-settings-footer.el-footer .el-button{
    width: 91px;
    height: 32px;
  }
</style>