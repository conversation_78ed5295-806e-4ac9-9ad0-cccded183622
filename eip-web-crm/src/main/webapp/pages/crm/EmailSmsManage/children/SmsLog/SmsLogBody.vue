<template>
  <el-container style="padding:0 20px;">
    <!-- <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
              class="clear-search"
              @click.native="clearSearch"
              title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right"></div>
    </el-header> -->
    <alex-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 300px)"
        border
        size="small"
        class="alex-table"
        mode="multiple"
        :fields="fields"
        :handle-pagination="handlePagination"
        @sort-change="sort"
        @selection-change="$emit('table-select', $event)"
        :total="total"
        :show-operation="false"
        fit-last
        :loading="loading">
      <!-- <template slot="opera" slot-scope="{row}">
        <div style="text-align:center;margin-left: -10px;">
          <el-link :underline="false" type="primary" @click.stop="showDetails(row)">查看详情</el-link>
        </div>
      </template> -->
      <template slot="state" slot-scope="{row}">{{row.state|state2text}}{{row.stateName ? (': ' + row.stateName): ''}}</template>
      <template v-if="row.sjh" slot="sjh" slot-scope="{row}">
        {{row.sjh.length > 60 ? `${row.sjh.substring(0, 60)}...` : row.sjh}}
      </template>
    </alex-table>
    <el-footer class="table-footer">
      <div v-for="summation in summationData" :key="summation.prop">
        <p v-if="summation.prop == 'all'">{{summation.label}}：<span style="color: #409EFF">{{summation.data}}个</span></p>
        <p v-else-if="summation.prop == 'success'">{{summation.label}}：<span style="color: #409EFF">{{summation.data}}条</span></p>
        <p v-else>{{summation.label}}：<span style="color: #409EFF">{{summation.data | toFixed2}}条</span></p>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
  const AlexTable = importC('AlexTable')
  export default {
    name: "SmsLogBody",
    data() {
      return {
        breadcrumbList: [],
        fields: [],
        total: 0,
        summationData: [],
        tableData: [],
        loading: false,
        requestHistory: {rows: 20, page: 1},
      }
    },
    methods: {
      sort({column, prop, order}) {
        let data = this.requestHistory;
        if (order) {
          data.order = order.replace('ending', '');
          data.sort = prop;
        } else {
          delete (data.order)
          delete (data.sort)
        }
        this.requestTableData(data)
      },
      clearSearch() {
        this.breadcrumbList = [];
        this.requestTableData({page: 1, rows: 20});
        this.$emit('clear-search')
      },
      handlePagination({page, rows}) {
        this.requestHistory.page = page
        this.requestHistory.rows = rows
        this.requestTableData(this.requestHistory)
      },
      createFields(callback) {
        this.fields = [
          {label: '手机号', prop: 'sjh', sortable: false, width: 120},
          {label: '接收人', prop: 'xm', sortable: false, width: 100},
          {label: '短信服务商', prop: 'serviceTypeName', sortable: false, width: 100},
          {label: '短信账号', prop: 'account', sortable: false, width: 100},
          {label: '批次号', prop: 'pch', sortable: false, width: 200},
          {label: '批次名称', prop: 'pcmc', sortable: false, width: 200},
          {label: '手机总数', prop: 'phoneNum', sortable: false, width: 90},
          {label: '短信内容', prop: 'xxnr', sortable: false},
          {label: '短信签名', prop: 'signature', sortable: false, width: 90},
          {label: '发送状态', prop: 'state', sortable: false, width: 90},
          {label: '计划发送时间', prop: 'planTime', width: 180},
          {label: '实际发送时间', prop: 'sendTime', width: 180},
          {label: '操作员', prop: 'employeeName', width: 180},
        ]
        callback && callback.call(this)
      },
      requestTableData(queryData = {}, callback = () => this.loading = false) {
        this.loading = true
        this.requestHistory = queryData
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        Axios.post('/sms/getPage', JSON2FormData(queryData)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败')
          this.total = res.total
          this.tableData = res.rows
          // 表格底部
          const {all, wait, failed, succsess} = res.data
          this.summationData = [
            {label: '合计手机总数', prop: 'all', data: all || 0},
            {label: '发送成功', prop: 'success', data: succsess || 0},
            {label: '发送失败', prop: 'failed', data: failed || 0},
          ]
          callback(this.$refs.table, res.rows)
        }).catch(e => {
          this.$errorMsg('数据加载失败')
          this.loading = false
        })
      },
      updateColumnDisplay() {
        this.createFields(() => {
          // this.requestTableData(this.requestHistory)
        })
      },
      search({requestData, breadcrumbList}) {
        requestData.page = 1
        requestData.rows = 20
        // this.breadcrumbList = breadcrumbList
        this.requestTableData(requestData)
      },
      refresh() {
        this.requestTableData(this.requestHistory)
      }
    },
    mounted() {
      this.updateColumnDisplay()
    },
    filters: {
      state2text(value) {
        // 1 成功 0 待发送 -2 失败
        switch (value) {
          case '-3':
            value = '超时未发';break;
          case '-2':
            value = '失败';break;
          case '-1':
            value = '失败';break;
          case '0':
            value = '待发送';break;
          case '1':
            value = '成功';break;
          default:
            value = '';break;
        }
        return value
      }
    },
    components: {
      AlexTable,
    }
  }
</script>

<style scoped>
  .main-body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3px;
  }

  .alex-table {
    width: 100%;
    padding-top: 10px;
  }

  .alex-table .el-table th, .el-table--small td, .el-table--small th {
    padding: 4px 0 !important;
  }
 .alex-table .el-table .cell{
    line-height: 20px !important;
  }
  .table-footer {
    /*margin-top: 15px;*/
    height: 30px !important;
    padding: 5px 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;

  }
  .table-footer >div{
    margin-right: 45px;
    /*min-width: 200px;*/
  }
  .table-footer >div p {
    /*flex-basis: 250px;*/
    margin: 0;
  }

  .table-footer > p:nth-of-type(1) {
    text-align: left;
  }
</style>