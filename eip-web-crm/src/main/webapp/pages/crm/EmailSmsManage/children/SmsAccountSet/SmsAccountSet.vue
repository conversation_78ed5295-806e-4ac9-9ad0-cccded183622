<template>
  <div>
    <el-main style="padding: 20px 0 0">
      <el-header class="top-header" height="30px">
        <el-form inline size="mini" :model="searchForm" ref="searchForm" class="search-form-inline">
          <el-form-item label="接口服务商" prop="channelCode">
            <el-select
              v-model="searchForm.channelCode"
              placeholder="接口服务商"
              filterable
              default-first-option
              clearable>
              <el-option
                v-for="item in channelList"
                :key="item.id"
                :label="item.text"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道类型" prop="smsKind">
            <el-select v-model="searchForm.smsKind" placeholder="通道类型" clearable>
              <el-option label="营销类" value="营销类"></el-option>
              <el-option label="通知类" value="通知类"></el-option>
              <el-option label="验证码类" value="验证码类"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="短信账号" prop="account">
            <el-input v-model="searchForm.account" placeholder="短信账号" v-enter="search" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="search">检索</el-button>
          </el-form-item>
        </el-form>
      </el-header>
      <el-divider></el-divider>
    </el-main>
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right-btn-box">
        <el-button size="mini" class="btn" @click="addRows">新增</el-button>
        <el-button size="mini" class="btn" @click="deleteRows($refs.table.selection())">删除</el-button>
      </div>
    </el-header>
    <div>
      <alex-table
        :cell-class-name="getCellClassName"
        ref="table"
        :data="templateData"
        height="calc(100vh - 200px)"
        border
        :fields="fields"
        size="small"
        class="alex-table"
        :operation-width="90"
        @sort-change="sort"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="channelName">
          <el-link type="primary" :underline="false" @click.stop="editRow(row)">{{ row.channelName }}</el-link>
        </template>
        <template v-slot="{row}" slot="smsKind">
          {{ row.smsKind }}
          <template v-if="row.smsKind === '验证码类' && row.moreSign"> - 多签名</template>
        </template>
        <template slot-scope="{row}" slot="test">
          <div style="text-align:center;">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="testSMS(row)">发送
            </el-link>
          </div>
        </template>
        <template slot-scope="{row}" slot="alex-operation">
          <div class="operation">
            <el-link
              v-if="row.smsKind === '验证码类'"
              :underline="false"
              :style="row.isDefault ? {color: '#606266'}: {}"
              :type="row.isDefault ? 'default': 'primary'"
              @click.stop="!row.isDefault && setDefaultAccount(row)">设为默认
            </el-link>
            <el-link
              :underline="false"
              type="primary"
              @click.stop="selectSmsBalance(row)">余额查询
            </el-link>
          </div>
        </template>
      </alex-table>
    </div>
    <sms-account-dialog @load-success="channelList=$event" @operation-finished="refresh"
                        ref="SmsAccountDialog"></sms-account-dialog>
  </div>
</template>

<script>
const SmsAccountDialog = importC('SmsAccountDialog')
const RANDOM_CLASS = `is-default__${Math.random().toString(36).slice(2)}`
export default {
  name: "SmsAccountSet",
  mixins: [Mixins.table(), Mixins.clearForm()],
  components: {SmsAccountDialog},
  data() {
    return {
      searchForm: {
        channelCode: '',
        smsKind: '',
        account: '',
      },
      dataMap: {
        channelCode: '接口服务商',
        smsKind: '通道类型',
        account: '短信账号',
      },
      breadcrumbList: [],
      templateData: [],
      fields: [],
      total: 0,
      loading: false,
      channelList: [],
    }
  },
  created() {
    this.fields = [
      {label: '接口服务商', prop: 'channelName'},
      {label: '通道类型', prop: 'smsKind'},
      {label: '短信账号', prop: 'account'},
      {label: '默认签名', prop: 'sign'},
      {label: '备注', prop: 'memo', sortable: false},
      {label: '人工测试短信', prop: 'test', sortable: false},
    ]
  },
  mounted() {
    this.refresh()
  },
  methods: {
    search() {
      const data = {}
      let breadcrumbKey
      switch (this.page) {
        case 'template':
          breadcrumbKey = 'breadcrumbList'
          break
        case 'account':
          breadcrumbKey = 'accountBreadcrumbList'
          break
      }
      this.breadcrumbList.length = 0
      Object.keys(this.searchForm).forEach(key => {
        if (!this.searchForm[key]) return
        const value = this.searchForm[key]
        let text = `${this.dataMap[key]}: ${value}`
        if (key === 'channelCode') {
          const curKind = this.channelList.find(({id}) => id === value)
          if (!curKind) return
          text = `${this.dataMap[key]}: ${curKind['text']}`
        }
        data[key] = value
        this.breadcrumbList.push(text)
      })
      this.requestTableData(data)
    },
    requestTableData(queryData = {}, callback) {
      queryData = queryData || {}
      callback = callback || (() => this.loading = false)
      this.loading = true
      this.requestHistory = queryData
      if (!getAuthority('fu_all_team_manage')) {
        queryData['workTeamIdOrNull'] = window.teamId || window.top.teamId
      }
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      Axios.post('/smsAccount/getPage', JSON2FormData(queryData)).then(({data: res}) => {
        if (res.state !== 1)
          return Promise.reject()
        this.templateData = res.rows
        this.total = res.total
        //  add default tag
        this.$nextTick(() => {
          $(`.default-tag`).remove()
          const $defaultTags = $(`.${RANDOM_CLASS} .cell`)
          // if (!$defaultTags.find('div').length) return
          $defaultTags.each(function () {
            const $this = $(this)
            const index = $this.text().trim()
            $this.html(`<span class="default-tag"></span>${index}`)
          })
        })
      }).catch(e => {
        e && console.log(e)
        return this.$errorMsg('数据加载失败！')
      }).finally(() => this.loading = false)
    },
    addRows() {
      this.editRow({}, 'add')
    },
    readRow(row) {
      this.editRow(row, 'read')
    },
    editRow(row, mode) {
      if (Array.isArray(row)) {
        if (row.length === 0)
          return this.$errorMsg('请选择要修改的数据', '提醒', 'warning')
        if (row.length > 1)
          return this.$errorMsg('单次只能修改一条数据', '提醒', 'warning')
        row = row[0]
      }
      this.$refs.SmsAccountDialog && this.$refs.SmsAccountDialog.open(mode || 'edit', row.smsAccountId)
    },
    async deleteRows(list) {
      try {
        if (list.length < 1)
          return this.$errorMsg('请选择需要删除的数据！', '警告', 'warning')
        await this.$confirm(`确定要删除这条数据吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const {data} = await Axios.post('/smsAccount/delete', JSON2FormData({
          smsAccountId: list[0]['smsAccountId'],
          operEntrance: '短信账号设置'
        }))
        if (data.state !== 1) return this.$errorMsg('删除失败！')
        this.$message.success('删除成功！')
        this.refresh()
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    },
    testSMS({smsAccountId}) {
      this.$testMessage(smsAccountId)
    },
    async setDefaultAccount({smsAccountId, account}) {
      try {
        await this.$confirm(`确定将账号"${account}"设为默认账号吗？`, '提醒', {type: 'warning'})
        await Axios.post('/smsAccount/setDefault', JSON2FormData({smsAccountId, operEntrance: '短信账号设置'}))
        this.requestTableData()
        this.$msg('设置成功！', 'success')
      } catch (e) {
        if (e === 'cancel' || e === 'close')
          return
        e && console.warn(e)
        this.$errorMsg('设置失败')
      }
    },
    selectSmsBalance({smsAccountId, account}) {
      const orgNum = getCookie('orgNum')
      Axios.post('/smsAccount/selectSmsBalance', JSON2FormData({smsAccountId, orgNum}))
        .then(({data}) => {
          if (data.state !== 1) return this.$errorMsg()
          const {code, msg, type, balanceResult} = data.data
          if (code !== 0) return this.$msg('查询发生错误，错误信息：' + msg)
          const unit = type === 1 ? ['金额', '元'] : ['条数', '条']
          this.$showMessage({
            title: '余额查询',
            message: `<span style="font-size: 13px;">短信账号${account}可发送短信${unit[0]}为：<span><br/>
                        <span class="el-button--text" style="margin-top: 20px;line-height: 30px;display: block;font-size: 16px;">${balanceResult}${unit[1]}<span>`,
            allowHTML: !0
          })
        })
        .catch(e => {
          console.warn(e)
          this.$errorMsg()
        })
    },
    getCellClassName({row, columnIndex}) {
      if (columnIndex === 0 && row.smsKind === '验证码类' && row.isDefault) {
        return RANDOM_CLASS
      }
      return ''
    }
  }
}
</script>

<style scoped>
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  min-width: 900px;
  padding-left: 3px;
  padding-right: 3px;
}

.search-form-inline {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.search-form-inline .el-form-item {
  margin: 0 15px 0 0;
}

.el-divider {
  margin: 10px auto;
}

.top-header .el-input {
  width: 120px;
}

.el-date-editor {
  width: 245px;
}

.right-btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-btn-box .el-button.btn {
  border: none;
  background-color: rgb(159, 169, 179);
  height: 28px;
  color: #fff;
  margin: 0 10px 0 0;
}

.right-btn-box .el-button.btn:last-child {
  margin: 0;
}

.right-btn-box .el-button.btn:hover {
  background-color: rgb(99, 169, 251);
}

.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.clear-search:hover > span {
  cursor: pointer !important;
  color: #2e82e4 !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.default-tag{
  width: 0;
  height: 0;
  border: 17px solid #409eff;
  border-bottom-color: transparent;
  border-right-color: transparent;
  position: absolute;
  left: 0;
  top: 0;
}
.default-tag::before{
  content: '';
  position: absolute;
  left: -17px;
  top: -17px;
  border: 8px solid rgb(244,244,244);
  border-bottom-color: transparent;
  border-right-color: transparent;
}
.default-tag::after{
  content: '默认';
  font-size: 12px;
  position: absolute;
  width: 30px;
  transform: rotate(-45deg) scale(0.75);
  left: -17px;
  top: -17px;
  color: #fff;
}
</style>