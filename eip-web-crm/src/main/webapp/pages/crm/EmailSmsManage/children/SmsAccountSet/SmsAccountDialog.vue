<template>
  <custom-dialog
    title="短信通道账号"
    append2body
    id="smsAccount"
    @before-close="close"
    @closed="saving=false"
    @opened="addRules"
    width="600px"
    :show="show">
    <el-main slot="body" class="sendAccount">
      <el-form
        class="tForm"
        ref="tForm"
        :disabled="mode === 'read'"
        :model="tForm"
        :rules="tFormRules"
        label-width="118px"
        size="small">
        <el-form-item label="接口服务商" required prop="channelCode">
          <el-select
            v-model="tForm.channelCode"
            style="width: 100%;"
            placeholder="接口服务商"
            filterable
            default-first-option
            :disabled="tForm.smsAccountId"
            clearable>
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="item.text"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通道类型" required prop="smsKind">
          <el-select
            v-model="tForm.smsKind"
            style="width: 100%;"
            placeholder="通道类型"
            clearable>
            <el-option label="营销类" value="营销类"></el-option>
            <el-option label="通知类" value="通知类"></el-option>
            <el-option label="验证码类" value="验证码类"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信账号" prop="account" required>
          <el-input
            v-model="tForm.account"
            style="width: 100%;"
            placeholder="短信账号"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="短信密码" prop="password" required>
          <el-input
            v-model="tForm.password"
            style="width: 100%;"
            show-password
            placeholder="短信密码"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="默认签名" prop="sign">
          <el-input
            v-model="tForm.sign"
            style="width: 100%;"
            placeholder="如:【展之科技】"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="moreSign" v-if="tForm.smsKind === '验证码类'">
          <el-checkbox v-model="tForm.moreSign">启用多签名</el-checkbox>
        </el-form-item>
        <!--扩展码，目前只是漫道用到，所以编辑的时候，选了漫道接口再显示这个字段-->
        <el-form-item label="扩展码" prop="ext" v-if="tForm.channelCode === 'MD' || tForm.channelCode === 'YDT'">
          <el-input
            v-model="tForm.ext"
            style="width: 100%;"
            placeholder="扩展码"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="短信类别ID" prop="smsType" v-if="tForm.channelCode === 'YDT'">
          <el-input
            v-model="tForm.smsType"
            style="width: 100%;"
            placeholder="短信类别ID"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input
            v-model="tForm.memo"
            style="width: 100%;"
            maxlength="15"
            placeholder="备注，15字以内"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="startBalanceAwoke">
          <el-checkbox v-model="tForm.startBalanceAwoke">
            {{smsCriticalText}}低于
            <el-input-number style="width:100px!important" v-model="tForm.smsCriticalValue" :controls="false" :min="0" :precision="0"></el-input-number>
            时，进行提醒
          </el-checkbox>
        </el-form-item>
      </el-form>
    </el-main>
    <el-footer slot="footer" class="sendAccountFooter">
      <el-button plain size="small" @click="close">取消</el-button>
      <el-button type="primary" size="small" @click="save" :loading="saving">保存{{saving? '中...': ''}}</el-button>
    </el-footer>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "SmsAccountDialog",
  data() {
    return {
      tForm: {
        smsAccountId:'',
        smsKind:'',
        channelCode:'',
        account:'',
        password:'',
        memo:'',
        sign:'',
        moreSign: false,
        ext:'',
        smsType: '',
        startBalanceAwoke: false,
        smsCriticalValue: undefined,
      },
      tFormRules: {},
      mode: 'add',
      show: false,
      saving: false,
      isChange: false,
      channelList: []
    }
  },
  methods: {
    clear(...keys) {
      keys.forEach(key => {
        this.$set(this.tForm, key, '')
      })
    },
    save() {
      if(this.mode==='read')return
      this.saving = true
      const postData = {}
      Object.keys(this.tForm).forEach(key=>{
        const value = this.tForm[key]
        if(typeof value !== "boolean" && !value)
          return (postData[key] = '')
        // if(key === 'ext' && (this.tForm.channelCode === 'MD' || this.tForm.channelCode === 'YDT')){}
        // else return
        if(key === 'smsType' && this.tForm.channelCode !== 'YDT')return
        postData[key] = value
        postData.operEntrance = '短信账号设置'
      })
      this.$refs.tForm.validate()
        .then(() => {
          return Axios.post('/smsAccount/save', JSON2FormData(postData))
        })
        .then(({data: res}) => {
          if (res.state !== 1)
            return Promise.reject('保存失败！')
          this.$msg('保存成功！', 'success')
          this.$emit('operation-finished')
          this.isChange = false
          this.close()
        })
        .catch(e => {
          if (typeof e === 'boolean') {
            return this.$errorMsg('带 * 为必填项!').finally(() => {
              this.$focusError()
              this.saving = false
            })
          }
          else if(typeof e === 'string'){
            return this.$errorMsg(e).finally(() => {
              this.$focusError()
              this.saving = false
            })
          }
          console.log(e)
          this.saving = false
        })

    },
    close() {
      if (this.isChange) {
        return this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
          this.isChange = false
          this.show = false
        }).catch(e => e)
      }
      this.$refs['tForm'] && this.$refs['tForm'].clearValidate()
      this.show = false
    },
    addRules() {
      if (Object.keys(this.tFormRules).length === 0) {
        this.$refs.tForm && this.$refs.tForm.$children.forEach(item => {
          const {prop, label} = item
          if (!prop || !label || ['ext', 'sign', 'memo', 'smsType'].includes(prop)) return
          const message = `${label}为必填项！`
          this.tFormRules[prop] = [{required: true, message, trigger: ['blur', 'change']}]
        })
        this.$nextTick(() => this.$refs['tForm'] && this.$refs['tForm'].clearValidate())
      }
    },
    open(mode, smsAccountId) {
      this.mode = mode
      if ((mode === 'read' || mode === 'edit') && smsAccountId) {
        this.requestAccountById(smsAccountId).then(()=>this.$nextTick(()=>this.isChange=false))
      } else if (mode === 'add') {
        this.resetForm()
        setTimeout(()=>this.isChange=false)
      } else return console.warn('数据异常！')
      this.show = true
    },
    requestAccountById(id) {
      return Axios.post('/smsAccount/getById', JSON2FormData({smsAccountId: id})).then(({data: res}) => {
        if (res.state !== 1)return Promise.reject()
        Object.keys(this.tForm).forEach(key => {
          this.$set(this.tForm, key, res.data[key])
        })
        return Promise.resolve(res.data)
      }).catch(e => {
        console.warn(e)
        this.$msg('账号数据加载失败！')
      })
    },
    resetForm() {
      this.clear(...Object.keys(this.tForm))
      this.$nextTick(() => this.$refs['tForm'] && this.$refs['tForm'].clearValidate())
    },
    getChannel(){
      Axios.post('/smsAccount/getChannel').then(({data: res})=>{
        if(res.state !==1 )return Promise.reject()
        this.channelList = res.data
        this.$emit('load-success', res.data)
      }).catch(err=>{
        this.$msg('服务商加载失败！')
        console.error(err)
      })
    }
  },
  mounted(){
    this.getChannel()
  },
  components: {
    CustomDialog,
  },
  watch: {
    tForm:{
      deep: true,
      handler(){
        if(!this.isChange)this.isChange = true
      }
    },
  },
  computed: {
    smsCriticalText() {
      return this.tForm.channelCode === 'YXT' ? '剩余余额': '剩余可发条数'
    }
  }
}
</script>

<style scoped>
#smsAccount .sendAccount .tForm {
  padding: 20px 40px 20px 30px;
}

#smsAccount .sendAccountFooter {
  width: 230px;
  margin: auto;
  padding: 0;
  height: 46px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #efefef;
}

#smsAccount .sendAccountFooter .el-button {
  width: 90px;
  height: 32px;
}

#smsAccount .el-dialog__footer {
  margin-top: -20px;
  background: #f0f0f0;
  padding: 0;
}
#smsAccount .is-disabled,
#smsAccount .is-disabled:hover,
#smsAccount .is-disabled *,
#smsAccount .is-disabled *:hover{
  background-color: transparent!important;
  border-color: #aaa!important;
  color: #666!important;
}

</style>