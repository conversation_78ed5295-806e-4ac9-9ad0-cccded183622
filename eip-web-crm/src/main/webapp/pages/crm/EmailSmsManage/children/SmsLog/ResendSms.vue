<template>
  <custom-dialog
    :show="show"
    @before-close="close('exit')"
    title="重发已失败短信"
    append2body
    width="380px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div><span class="message-title"></span></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            type="primary"
            @click="save()">确认重发
          </el-button>
        </div>
      </el-header>
      <div style="padding: 10px;">
        <el-alert type="info" show-icon :closable="false">
          共{{config.total}}条发送失败数据，可重发{{config.resend}}条数据，
          {{config.notResend}}条验证码/通知短信不进行重发。(对{{config.phoneNum}}个手机号重发短信)
        </el-alert>
      </div>
      <el-form ref="tForm" :model="tForm" class="tForm" size="mini" :rules="rules">
        <el-form-item label-width="100px" label="批次名称" prop="send_pcmc">
          <el-input v-model="tForm.send_pcmc" placeholder="批次名称" v-focus clearable></el-input>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ResendSms",
  mixins: [Mixins.clearAndInitForm()],
  data() {
    return {
      show: false,
      tForm: {
        send_pcmc: '',
      },
      rules: {
        send_pcmc: [
          { required: true, message: '请输入批次名称', trigger: 'blur' },
        ],
     },
      config: {
        notResend: 0, //不允许重发的数量
        total: 0, //全部 发送失败的数量
        resend: 0, //允许重发的数量
        phoneNum: 0 //允许重发的手机号数量
      },
      resolve: Boolean,
      reject: Boolean,
    }
  },
  methods: {
    open(config) {
      config = config || {}
      Object.keys(this.config).forEach(key => {
        this.config[key] = config[key] || 0
      })
      this.tForm.send_pcmc = `${top.$GlobalDefine.getSystemTime('yyyy年M月dd日')} 重发短信`
      const promise = new Promise((_resolve, _reject) => {
        this.resolve = _resolve
        this.reject = _reject
      })
      this.show = true
      return promise
    },
    async save() {
      try {
        await this.validateForm()
        this.close()
      }catch (e) {
        e = this.handleErrors(e)
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        e && this.$errorMsg(e)
      }
    },
    close(isCancel) {
      this.show = false
      if (isCancel) return this.reject && this.reject(isCancel)
      this.resolve && this.resolve(this.tForm)
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.message-title {
  font-size: 14px;
  color: #2e82e4;
  padding-left: 10px;
}

.el-alert {
  background: rgb(230, 247, 255);
  color: rgb(80, 86, 89);
  border: 1px solid rgb(203, 237, 255);
}

.el-alert__icon {
  color: rgb(24, 144, 255);
}

</style>