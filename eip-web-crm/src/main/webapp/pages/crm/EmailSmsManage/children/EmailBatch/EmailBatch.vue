<template>
  <div>
    <email-batch-header
      ref="header"
      @export="exportTable"
      @search="$refs.body && $refs.body.search(...arguments)">
    </email-batch-header>
    <email-batch-body
      ref="body"
      @table-select="tableSelection=$event"
      @read-row="$refs.dialog && $refs.dialog.open($event)"
      @clear-search="$refs.header && $refs.header.clearAll($event)">
    </email-batch-body>
  </div>
</template>

<script>
const EmailBatchHeader = importC('EmailBatchHeader')
const EmailBatchBody = importC('EmailBatchBody')
export default {
  name: "EmailBatch",
  data() {
    return {
      tableSelection: []
    }
  },
  methods: {
    _export(_exportList, fileName, loading) {
      const $body = this.$refs['body']
      const $mainFields = $body.fields
      const propList = $mainFields.map(it => it.prop)
      const labelList = $mainFields.map(it => it.label)
      const tHead = ['序号', ...labelList.slice(0, -1)]
      const mapFunc = (item, index) => {
        let temp = {index: index + 1}
        propList.forEach(prop => {
          let v = item[prop]
          if (prop === 'state') {
            v = v ? '成功' : '失败'
          }
          temp[prop] = v
        })
        return temp
      }
      const exportList = _exportList.map(mapFunc)
      exportObject2Excel({
        header: tHead,
        body: exportList,
        fileName,
        title: '邮件发送日志'
      })
      loading && loading.close()
    },
    exportTable() {
      const loading = this.$loading({
        fullscreen: !0,
        text: '正在导出...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.1)',
        lock: !0,
        body: !0
      })
      if (this.tableSelection.length < 1)
        return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          .then(() => this.exportAll(loading))
          .catch(() => loading.close())
      this._export(this.tableSelection, `邮件发送日志`, loading)
    },
    exportAll(loading) {
      const postData = {...this.$refs['body'].requestHistory}
      delete postData.rows
      delete postData.page
      Axios.post('/emailLog/getPage', JSON2FormData(postData)).then(({data: res}) => {
        if (res.state !== 1) return Promise.reject()
        if (res.rows && !res.rows.length) return this.$errorMsg('没有数据可以导出!')
        this._export(res.rows, `邮件发送日志-导出全部`, loading)
      }).catch(e => {
        e && console.warn(e)
        this.$errorMsg('导出失败!')
      })
    }
  },
  components: {
    EmailBatchHeader,
    EmailBatchBody,
  }
}
</script>