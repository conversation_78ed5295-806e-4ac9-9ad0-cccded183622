<template>
  <custom-dialog
    :show="show"
    title="编辑收件人"
    @before-close="close"
    width="750px"
  >
    <el-main slot="body" class="dialog-body" style="margin: 10px" ref="body">
      <div class="top-button">
        <el-link
          @click="addReceiver"
          :underline="false"
          icon="el-icon-circle-plus-outline"
          size="mini">新增
        </el-link>
        <el-link
          @click="deleteReceiver"
          :underline="false"
          icon="el-icon-delete"
          size="mini">删除
        </el-link>
      </div>
      <alex-table
        ref="table"
        :data="receiverDtlList"
        :height="300"
        border
        mode="multiple"
        :fields="fields"
        size="small"
        class="alex-table"
        :show-operation="false"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading"
      >
        <template slot-scope="{row, $index}" slot="receiveEmail">
          <el-input
            v-model="row.receiveEmail"
            @click.native.stop
            @blur="trySaveReceiver('receiveEmail', row)"
            size="mini"
            :ref="`receiveEmail-` + row.emailBatchDtlId"
            clearable>
          </el-input>
        </template>
        <template slot-scope="{row, $index}" slot="receiver">
          <el-input
            v-model="row.receiver"
            @click.native.stop
            @blur="trySaveReceiver('receiver', row)"
            size="mini"
            :ref="`receiver-` + row.emailBatchDtlId"
            clearable>
          </el-input>
        </template>
      </alex-table>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "RecipientDialog",
  mixins: [Mixins.table()],
  props: {
    batchId: String
  },
  data() {
    return {
      show: false,
      resolve: null,
      fields: [
        {label: '收件邮箱', prop: 'receiveEmail', sortable: false, width: 230},
        {label: '收件人', prop: 'receiver', sortable: false},
      ],
      receiverDtlList: [],
      total: 0,
      loading: false
    }
  },
  components: {CustomDialog},
  methods: {
    isEmail(value) {
      const reg = /^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i
      return reg.test(value)
    },
    async requestTableData(queryData = {}) {
      try {
        this.loading = true
        this.requestHistory = queryData
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        queryData.emailBatchId = this.batchId
        const {data} = await Axios.post('/emailBatch/getDtlByBatchId', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject()
        this.total = data.total
        this.receiverDtlList = data.rows.map(item => {
          return {
            ...item,
            _receiveEmail: item.receiveEmail || '',
            _receiver: item.receiver || ''
          }
        })
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
        this.$errorMsg('数据加载失败')
      } finally {
        this.loading = false
      }
    },
    addReceiver() {
      this.receiverDtlList.unshift({
        receiveEmail: '',
        receiver: '',
        emailBatchDtlId: `$${Math.random().toString(36).slice(2)}`
      })
    },
    async trySaveReceiver(key, row, quiet) {
      try {
        if (!quiet) {
          const value = row[key]
          if (!value) await Promise.reject('require')
          if (key === 'receiveEmail' && !this.isEmail(value)) await Promise.reject('email')
          if (
            !row.receiveEmail ||
            !this.isEmail(row.receiveEmail) ||
            !row.receiver ||
            (row.receiveEmail === row._receiveEmail &&
            row.receiver === row._receiver)
          ) return
        }
        const {emailBatchDtlId, receiveEmail, receiver} = row
        const post = {receiveEmail, receiver, emailBatchId: this.batchId}
        if (emailBatchDtlId && !emailBatchDtlId.startsWith('$'))
          post.emailBatchDtlId = emailBatchDtlId
        const {data} = await Axios.post('/emailBatch/saveDtl', JSON2FormData(post))
        if (!quiet) {
          if (data.state !== 1) await Promise.reject('request')
          this.$message.success('保存成功')
        }
        await this.refresh()
        return this.total
      } catch (e) {
        if (typeof e !== 'string') return console.warn(e)
        const message = ({
          require: key === 'receiveEmail' ? '请输入收件邮箱' : '请输入收件人',
          email: '请输入正确的邮箱格式',
          request: '保存'
        })[e] || '数据加载失败'
        const input = this.$refs[`${key}-${row.emailBatchDtlId}`]
        this.$message.warning(message)
        input && input.focus && input.focus()
      }
    },
    async deleteReceiver() {
      const loading = this.$loading({background: 'rgba(0, 0, 0, 0.3)'})
      try {
        const selection = this.$refs.table.selection()
        if (!selection.length) await Promise.reject('请选择要删除的数据')
        const list = selection.map(row => row.emailBatchDtlId).filter(id => !id.startsWith('$'))
        await this.$confirm(`确定删除${list.length}条数据吗？`, '提示', {type: 'info'})
        const post = {
          emailBatchId: this.batchId,
          emailBatchDtlIds: list.toString()
        }
        const {data} = await Axios.post('/emailBatch/deleteDtl', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject('删除失败')
        this.refresh()
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
        this.$errorMsg(String(e))
      } finally {
        loading.close()
      }
    },
    open() {
      return new Promise(resolve => {
        this.refresh()
        this.resolve = resolve
        this.show = true
      })
    },
    async close() {
      await this.$nextTick()
      await this.refresh()
      const count = this.total
      this.resolve && this.resolve(count)
      this.show = false
      return count
    }
  }
}
</script>

<style scoped>
.alex-table .el-table__header .cell {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

.alex-table .el-table__body td:not(.el-table-column--selection),
.alex-table .el-table__body td:not(.el-table-column--selection) .cell {
  padding: 0 !important;
}

.alex-table .el-table__body .cell .el-input__inner {
  height: 35px;
  border-radius: 0;
  border-color: transparent;
}

.alex-table .el-table__body .cell .el-input__inner:focus {
  border-color: #409EFF;
}

.top-button {
  flex-basis: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  margin-bottom: -10px;
}

.top-button i {
  color: #63A9FB;
}

.top-button a {
  padding: 0 15px;
  height: 100%;
}

.top-button a > i {
  font-size: 1rem;
}

.top-button a:hover {
  background-color: #eee;
}
</style>