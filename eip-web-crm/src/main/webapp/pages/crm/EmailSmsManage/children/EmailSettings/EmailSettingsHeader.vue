<template>
  <el-main style="padding: 20px 20px 0;">
    <el-header class="top-header" height="30px">
      <div></div>
      <div class="right-btn-box">
        <el-button size="mini" class="btn" @click="$emit('add')">新增</el-button>
        <el-button size="mini" style="margin-left: 0;" class="btn" @click="$emit('delete')">删除</el-button>
      </div>
    </el-header>
    <el-divider></el-divider>
  </el-main>
</template>

<script>
  export default {
    name: "EmailSettingsHeader",
  }
</script>

<style scoped>
  .top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    min-width: 768px;
    padding-left: 3px;
    padding-right: 3px;
  }

  .el-divider {
    margin: 10px auto;
  }


  .right-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-btn-box .el-button.btn {
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 20px;
    border-radius: 0;
  }

  .right-btn-box .el-button.btn:hover {
    background-color: rgb(99, 169, 251);
  }
</style>