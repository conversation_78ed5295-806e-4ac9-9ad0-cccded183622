<template>
  <el-container style="padding:0 20px;">
    <alex-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 170px)"
        border
        size="small"
        class="alex-table"
        mode="multiple"
        :fields="fields"
        :handle-pagination="handlePagination"
        :cell-class-name="getCellClassName"
        @selection-change="$emit('table-select', $event)"
        :total="total"
        @row-dblclick="readRow"
        operation-width="200"
        :loading="loading">
      <template slot="alex-operation" slot-scope="{row}">
        <div>
          <el-link :underline="false" type="primary" @click.stop="editRow(row)">修改</el-link>
          <el-link style="margin-left: 15px;" :underline="false" type="primary" @click.stop="testAccount(row)">账号测试
          </el-link>
        </div>
      </template>
      <template slot="isDefault" slot-scope="{row}">{{row.isDefault | bool2text}}</template>
      <template slot="check" slot-scope="{row}">{{row.check | check2text}}</template>
    </alex-table>
  </el-container>
</template>

<script>
  const AlexTable = importC('AlexTable')
  const RANDOM_CLASS = `is-default__${Math.random().toString(36).slice(2)}`
  export default {
    name: "EmailSettingsMainBody",
    inject: ['isOrgSetting'],
    data() {
      return {
        fields: [],
        total: 0,
        tableData: [],
        loading: false,
        requestHistory: {rows: 20, page: 1},
        summationData: []
      }
    },
    methods: {
      handlePagination({page, rows}) {
        this.requestHistory.page = page
        this.requestHistory.rows = rows
        this.requestTableData(this.requestHistory)
      },
      createFields(callback) {
        const sendEmailLabel = this.isOrgSetting ? '验证码发件邮箱' : '发件邮箱'
        this.fields = [
          {label: 'SMTP服务器', prop: 'emailHost', sortable: false},
          {label: '端口号', prop: 'emailPort', sortable: false},
          {label: sendEmailLabel, prop: 'sendEmail', sortable: false},
          {label: '发件人', prop: 'sender', sortable: false},
          {label: '回复邮箱', prop: 'replyEmail', sortable: false},
          {label: '默认邮箱', prop: 'isDefault', sortable: false},
          {label: '测试结果', prop: 'check', sortable: false},
        ]
        callback && callback.call(this)
      },
      requestTableData(queryData = {}, callback = () => this.loading = false) {
        this.loading = true
        this.requestHistory = queryData
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        queryData.accountType = this.isOrgSetting ? 2 : 1
        Axios.post('/emailAccount/getPage', JSON2FormData(queryData)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败')
          this.total = res.total
          this.tableData = res.rows
          //  add default tag
          this.$nextTick(() => {
            $(`.default-tag`).remove()
            const $defaultTags = $(`.${RANDOM_CLASS} .cell`)
            // if (!$defaultTags.find('div').length) return
            $defaultTags.each(function () {
              const $this = $(this)
              const index = $this.text().trim()
              $this.html(`<span class="default-tag"></span>${index}`)
            })
          })
          callback(this.$refs.table, res.rows)
        }).catch(e => {
          this.$errorMsg('数据加载失败')
          this.loading = false
        })
      },
      updateColumnDisplay() {
        this.createFields(() => {
          this.requestTableData(this.requestHistory, table => {
            table.computedWidth().then(() => {
              this.loading = false
            })
          })
        })
      },
      refresh() {
        this.requestTableData(this.requestHistory)
      },
      editRow(row) {
        this.$emit('operation-row', ['edit', row['emailAccountId']])
      },
      readRow(row) {
        this.$emit('operation-row', ['read', row['emailAccountId']])
      },
      testAccount({emailAccountId}) {
        const loading = this.$loading({
          fullscreen: !0,
          text: '正在测试中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.1)',
          lock: !0,
          body: !0
        })
        Axios.post('/emailAccount/check', JSON2FormData({emailAccountId}))
        .then(({data: res}) => {
          if (res.state === 1) return this.$message.success('测试成功！')
          const message = res.msg
          this.$showMessage({
            title: '测试失败',
            message,
            confirm: close => {
              close && close()
            }
          })
        }).catch(e => {
          this.$errorMsg()
          console.warn(e)
        }).finally(()=>{
          this.refresh()
          loading.close()
        })
      },
      delBatch(rows){
        if(!rows.length)
          return this.$errorMsg('请选择需要删除的行！', '警告', 'warning')
        this.$confirm(`确定要删除这${rows.length}条数据吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          return Axios.post('/emailAccount/delBatch', rows)
        }).then(({data: res}) => {
          if (res.state !== 1)
            return Promise.reject('')
          this.$message.success('删除成功！')
          this.refresh()
        }).catch(e => {
          e !== 'cancel' && this.$errorMsg('删除失败！')
        })
      },
      getCellClassName({row, columnIndex}) {
        if (columnIndex === 0 && row.isDefault) {
          return RANDOM_CLASS
        }
        return ''
      }
    },
    mounted() {
      this.updateColumnDisplay()
    },
    filters: {
      check2text(value) {
        if (!value) return '未校验'
        return value === 1 ? '成功' : '失败'
      }
    },
    components: {
      AlexTable,
    }
  }
</script>

<style scoped>
  .main-body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3px;
  }

  .alex-table {
    width: 100%;
    padding-top: 10px;
  }

  .alex-table .el-table th, .el-table--small td, .el-table--small th {
    padding: 4px 0 !important;
  }

  .default-tag{
    width: 0;
    height: 0;
    border: 17px solid #409eff;
    border-bottom-color: transparent;
    border-right-color: transparent;
    position: absolute;
    left: 0;
    top: 0;
  }
  .default-tag::before{
    content: '';
    position: absolute;
    left: -17px;
    top: -17px;
    border: 8px solid rgb(244,244,244);
    border-bottom-color: transparent;
    border-right-color: transparent;
  }
  .default-tag::after{
    content: '默认';
    font-size: 12px;
    position: absolute;
    width: 30px;
    transform: rotate(-45deg) scale(0.75);
    left: -17px;
    top: -17px;
    color: #fff;
  }
</style>