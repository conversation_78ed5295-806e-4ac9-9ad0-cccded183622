<template>
  <custom-dialog
    :show="show"
    append2body
    displaying-hide-bar
    @before-close="close"
    title="组合查询"
    width="780px"
    class="combined-query"
  >
    <el-main slot="body" class="cq-body">
      <cq-header :tag-data="tagData" @tag-close="tagClose"></cq-header>
      <el-form class="search-form" ref="basicInfo" :model="basicInfo" label-width="125px" size="mini">
        <el-form-item label="手机号">
          <el-input v-model="basicInfo.phone.v" clearable></el-input>
        </el-form-item>
        <el-form-item label="接收人">
          <el-input v-model="basicInfo.receiver.v" clearable></el-input>
        </el-form-item>
        <el-form-item label="发送方式">
          <el-select v-model="basicInfo.sendMode.v" placeholder="发送方式" clearable filterable default-first-option>
            <el-option
              v-for="item in sendModeList"
              :label="item.text"
              :key="item.id"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员">
          <el-select v-model="basicInfo.operatorId.v" placeholder="操作员" clearable filterable default-first-option>
            <el-option
              v-for="v in operatorList"
              :label="v.text"
              :key="v.id"
              :value="v.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信服务商">
          <el-select v-model="basicInfo.serviceType.v"
                     placeholder="请选择" clearable filterable @change="clear('basicInfo', 'account')">
            <el-option v-for="item in serviceTypes"
                       :key="item.id"
                       :label="item.text"
                       :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信账号">
          <el-select v-model="basicInfo.account.v" clearable filterable default-first-option>
            <el-option v-for="(account, index) in smsAccountListSuggest" :key="index" :label="account" :value="account"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批次号">
          <el-input v-model="basicInfo.batchNo.v" clearable></el-input>
        </el-form-item>
        <el-form-item label="批次名称">
          <el-input v-model="basicInfo.batchName.v" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机总数">
          <el-input type="number" min="0" v-model="basicInfo.count.v" clearable></el-input>
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select v-model="basicInfo.state.v"
            placeholder="请选择" clearable filterable>
            <el-option v-for="item in states"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划发送时间起">
          <el-date-picker
              v-model="basicInfo.planTimeStart.v"
              type="datetime"
              default-time="00:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              :picker-options="pickerOptions"
              placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划发送时间止">
          <el-date-picker
              v-model="basicInfo.planTimeEnd.v"
              type="datetime"
              default-time=23:59:59"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              :picker-options="pickerOptions"
              placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际发送时间起">
          <el-date-picker
              v-model="basicInfo.sendTimeStart.v"
              type="datetime"
              default-time="00:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              :picker-options="pickerOptions"
              placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际发送时间止">
          <el-date-picker
              v-model="basicInfo.sendTimeEnd.v"
              type="datetime"
              default-time=23:59:59"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              :picker-options="pickerOptions"
              placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="短信内容" class="full">
          <el-input v-model="basicInfo.content.v" clearable></el-input>
        </el-form-item>
      </el-form>
    </el-main>
    <el-footer slot="footer" height="50px">
      <div>
        <el-button
            type="primary"
            size="mini"
            style="width: 60px"
            @click="formSubmit"
        >检索
        </el-button>
        <el-button
            plain
            size="mini"
            style="width: 60px"
            @click="formReset"
        >清空
        </el-button>
      </div>
    </el-footer>
  </custom-dialog>
</template>

<script>
const CqHeader = importC('CqHeader')
const CustomDialog = importC('CustomDialog')
const pickerOptions =  {
  shortcuts: [{
    text: '最近一周',
    onClick(picker) {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', start);
    }
  }, {
    text: '最近一个月',
    onClick(picker) {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit('pick', start);
    }
  }, {
    text: '最近三个月',
    onClick(picker) {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      picker.$emit('pick', start);
    }
  }]
}
export default {
  name: "CombinedQuery",
  inject: ['sendModeList'],
  mixins: [Mixins.requestOperator()],
  data() {
    return {
      show: false,
      // { [field]:{v:value,t:tagName} }
      basicInfo: {
        phone: {v:'',t:'手机号'},
        receiver: {v:'',t:'接收人'},
        batchNo:{v:'',t:'批次号'},
        batchName:{v:'',t:'批次名'},
        count:{v:'',t:'手机数'},
        state:{v:'',t:'状态'},
        serviceType:{v:'',t:'短信服务商'},
        sendMode: {v:'',t:'发送方式'},
        operatorId: {v:'',t:'操作员'},
        account:{v:'',t:'短信账号'},
        sendTimeStart: {v:'',t:'实际时间'},
        sendTimeEnd: {v:'',t:''},
        planTimeStart: {v:'',t:'计划时间'},
        planTimeEnd: {v:'',t:''},
        content:{v:'',t:'内容'}
      },
      pickerOptions,
      states: {
        '1':{id:'1',text:'成功'},
        '0':{id:'0',text:'待发送'},
        '-2':{id:'-2',text:'失败'}
      },
      serviceTypes: {},
      tagData: {},
      formKeys: ['basicInfo'],
      smsAccountList: []
    }
  },
  computed: {
    smsAccountListSuggest() {
      const accountList = (() => {
        if (!this.basicInfo.serviceType.v) return this.smsAccountList
        return this.smsAccountList.filter(item => item.channelCode === this.basicInfo.serviceType.v)
      })().map(i => i.account)
      return new Set(accountList)
    },
  },
  methods: {
    open() {
      this.show = true
    },
    close() {
      this.show = false
    },
    query(params) {
      Object.keys(params).forEach(i =>{
        if(this.basicInfo.hasOwnProperty(i)){
          this.basicInfo[i]['v'] = params[i]
        }
      })
      this.$nextTick(_ => {
        this.formSubmit();
      })
    },
    formSubmit() {
      this.$emit('combined-query', {
        tags  : this.tagData,
        params: this.basicInfo
      })
      this.close()
    },
    formReset() {
      this.clear('basicInfo', ...Object.keys(this.basicInfo))
    },
    clear(targetKey, ...key) {
      key.forEach(k => {
        // this.$set(this[targetKey], k, '')
        this.basicInfo[k].v = ''
      })
    },
    //tag生成器
    tagGenerator(data,oldData, table, excludeFields=[]) {
      const tagData = {}
      let tagName
      let val
      Object.keys(data).forEach(k => {
        if(excludeFields.includes(k)) return
        val = data[k]['v']
        if (k.startsWith('sendTime')) {
          if(val){
            tagData['sendTime'] = `${data['sendTimeStart']['t']}: ${data['sendTimeStart']['v'] || ''} ~ ${data['sendTimeEnd']['v'] || ''}`
          }
        }else if (k.startsWith('planTime')) {
          if(val){
            tagData['planTime'] = `${data['planTimeStart']['t']}: ${data['planTimeStart']['v'] || ''} ~ ${data['planTimeEnd']['v'] || ''}`
          }
        }else if (k === 'state') {
          if(val!=='' && this.states[val]){
            tagName = data[k]['t']
            val     = this.states[val]['text']
            tagData[k] = `${tagName}: ${val}`
          }
        } else if (k === 'sendMode') {
          if (val !== '') {
            tagName = data[k]['t']
            const currentSendMode = this.sendModeList.find(i => i.id === val)
            const value = currentSendMode && currentSendMode.id ? currentSendMode.text : val
            tagData[k] = `${tagName}: ${value}`
          }
        } else if (k === 'operatorId') {
          if (val !== '') {
            tagName = data[k]['t']
            const currentOperator = this.operatorList.find(i => i.id === val)
            const value = currentOperator && currentOperator.id ? currentOperator.text : val
            tagData[k] = `${tagName}: ${value}`
          }
        }else if (k === 'serviceType') {
          if(val!=='' && this.serviceTypes[val]){
            tagName = data[k]['t']
            val     = this.serviceTypes[val]['text']
            tagData[k] = `${tagName}: ${val}`
          }
        }else{
          tagName = data[k]['t']
          if(val) tagData[k] = `${tagName}: ${val}`
        }
      })
      this.tagData = tagData
    },
    // 清除表单字段
    tagClose(name) {
      if(name === 'sendTime'){
        this.clear('basicInfo','sendTimeStart','sendTimeEnd')
      }else if(name === 'planTime'){
        this.clear('basicInfo','planTimeStart','planTimeEnd')
      }else{
        this.clear('basicInfo',name)
      }
    },
    getSmsAccount() {
      Axios.post('/smsAccount/getPage').then(({data: res}) => {
        if (res.state !== 1) return Promise.reject()
        this.smsAccountList = res.rows
      }).catch(err => {
        console.error(err)
      })
    },
  },
  async mounted() {
    this.requestOperator()
    this.getSmsAccount()
    const {data: {data}} = await Axios.post('/smsAccount/getChannel')
    data.forEach(item => {
      this.$set(this.serviceTypes, item.id, item)
    })
  },
  watch: {
    basicInfo: {
      handler: function (newVal,oldVal) {
        // console.log(newVal,oldVal)
        this.tagGenerator(newVal,oldVal,'basicInfo')
      },
      deep: true //深度监听
    }
  },
  components: {
    CqHeader,
    CustomDialog
  },
}
</script>

<style scoped>
.cq-body {
  padding: 0;
  /* height: 500px; */
}

.el-tabs__header.is-left, .el-tabs__header.is-left * {
  border: none !important;
}

.el-tabs--left .el-tabs__header.is-left {
  width: 195px;
  height: 500px;
  background-color: #F5F7FA;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  padding-left: 0;
  text-align: center;
  line-height: 40px;
  user-select: none;
  transition: all .3s;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
.el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
  background-color: #fff;
  color: #2e82e4;
}

.combined-query .el-footer,
.combined-query .el-dialog__footer {
  padding: 0;
}

.combined-query .el-dialog__footer {
  display: flex;
  justify-content: flex-end;
  background-color: #f0f0f0;
}

.combined-query .el-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.combined-query .search-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20px 40px 20px 25px;
}

.combined-query .search-form .el-input, .el-select {
  width: 100%;
}

.combined-query .search-form .el-form-item.el-form-item--mini {
  flex: 0 0 47%;
}
.combined-query .search-form .el-form-item.el-form-item--mini.full {
  flex: 0 0 100%;
}
.combined-query .placeholder {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #e7afaf;
  font-size: 12px;
  font-weight: bold;
}
</style>