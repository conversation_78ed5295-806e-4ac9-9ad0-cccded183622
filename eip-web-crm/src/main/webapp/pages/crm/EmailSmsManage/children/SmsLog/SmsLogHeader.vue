<template>
<div>
  <div>
    <center class="title">短信发送日志查询</center>
    <div class="right-btn-box-title">
      <el-button v-if="true" size="mini" icon="el-icon-caret-right" @click="$emit('resend')">重发已发失败短信</el-button>
      <el-button v-if="getAuthority('fu_send_sms_log_del')" size="mini" plain @click="$emit('delete')">删除</el-button>
      <el-button v-if="getAuthority('fu_report_export')" style="margin:0 0 0 10px" size="mini" class="btn" @click="$emit('export')">导出</el-button>
    </div>
  </div>
  <el-divider></el-divider>
  <el-main style="padding: 0 20px 10px;">
    <el-header class="top-header" height="35px">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="批次">
          <el-select v-model="searchForm.pc.v"
            clearable
            filterable
            remote
            :remote-method="getPcList"
            @focus="getPcList"
            default-first-option
            style="width:270px"
            popper-class="selectPc"
            class="el-select-pc"
            placeholder="批次 | 批次号"
            >
            <el-option v-for="v in pcList"
              :key="v.id"
              :value="v.id"
              >
              <div :title="'批次号: '+v.id">
                <span style="float:left;width:260px">{{ v.id }}</span>
                <span style="float:left;width:340px;text-align: left;">{{ v.text }}</span>
              </div>
              </el-option>
              <div style="text-align: center;position: absolute;bottom: 9px;width: 100%;">
                  <span class="text" @click.stop="prePage"
                    v-show="pcSelectPage > 1"
                  > 上一页</span>
                  <span class="text" style="padding-left: 30px;" @click.stop="nextPage"
                    v-show="pcSelectPage < pcPageCount">下一页</span>
              </div>
          </el-select>

        </el-form-item>
        <el-form-item label="发送状态">
          <el-select v-model="searchForm.state.v"
            placeholder="发送状态"
            clearable
            filterable
            default-first-option
            >
            <el-option v-for="v in states"
              :label="v.text"
              :key="v.id"
              :value="v.id"
              ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.sjh.v"
           style="width: 145px;" placeholder="手机号" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="计划发送时间从">
          <el-date-picker v-model="searchForm.startTime.v"
            style="width:200px"
            type="datetime"
            default-time="00:00:00"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="到">
          <el-date-picker v-model="searchForm.endTime.v"
            style="width:200px"
            editable
            type="datetime"
            default-time="23:59:59"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="发送方式">
          <el-select v-model="searchForm.sendMode.v" placeholder="发送方式" clearable filterable default-first-option>
            <el-option
              v-for="item in sendModeList"
              :label="item.text"
              :key="item.id"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员">
          <el-select v-model="searchForm.operatorId.v" placeholder="操作员" clearable filterable default-first-option>
            <el-option
              v-for="v in operatorList"
              :label="v.text"
              :key="v.id"
              :value="v.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
      <div class="right-btn-box">
          <el-button size="mini" class="btn" @click="$emit('show-query')" style="margin-right:0">组合查询</el-button>
        </div>
    </el-header>
  </el-main>
</div>
</template>

<script>
  export default {
    name: "SmsLogHeader",
    mixins: [Mixins.requestOperator()],
    inject: ['sendModeList'],
    data() {
      return {
        searchForm: {
          pc: {v:'',t:'批次号'},
          sjh: {v:'',t:'手机号'},
          state: {v:'',t:'状态'},
          startTime: {v:'',t:'计划发送时间'},
          endTime: {v:'',t:'~'},
          sendMode: {v:'',t:'发送方式'},
          operatorId: {v:'',t:'操作员'},
        },
        pcTotal: null, // 获取总数据量
        // pcPageCount: null, // 获取总页数
        pcSelectPage: 1, // 当前页数
        pcPageSize: 20, // 当前页数
        // pcListloading: false,
        pcList: [], // 批次查询
        states: {
          '1':{id:1,text:'成功'},
          '0':{id:0,text:'待发送'},
          '-2':{id:-2,text:'失败'},
          '-3':{id:-3,text:'超时未发'},
        },
      }
    },
    computed: {
      pcPageCount(){
        return Math.ceil(this.pcTotal / this.pcPageSize)
      },
      planTimeTip() {
        const v1 = this.searchForm.startTime.v || ''
        const v2 = this.searchForm.endTime.v || ''
        return (v1 || v2 ) ? `${v1} ~ ${v2}` : ''
      }
    },
    methods: {
      search() {
        let searchValues = {
          pc: this.searchForm.pc.v || '',
          sjhLeft: this.searchForm.sjh.v || '',
          state: this.searchForm.state.v,
          planTimeStart: this.searchForm.startTime.v || '',
          planTimeEnd: this.searchForm.endTime.v || '',
          sendMode: this.searchForm.sendMode.v,
          operatorId: this.searchForm.operatorId.v || '',
        };
        const breadcrumbList = {}
        Object.keys(this.searchForm).map(i => {
          if (i === 'endTime') return
          let {t: tagName, v: tagVal} = this.searchForm[i]
          if (i === 'startTime') {
            if (this.planTimeTip && tagName) {
              breadcrumbList[tagName] = this.planTimeTip
            }
          } else if (i === 'state') {
            if (tagVal !== '' && tagName) {
              breadcrumbList[tagName] = this.states['' + tagVal].text
            }
          } else if (i === 'sendMode') {
            if (tagVal !== '' && tagName) {
              const currentSendMode = this.sendModeList.find(i => i.id === tagVal)
              breadcrumbList[tagName] = currentSendMode && currentSendMode.id ? currentSendMode.text : tagVal
            }
          } else if (i === 'operatorId') {
            if (tagVal !== '' && tagName) {
              const currentOperator = this.operatorList.find(i => i.id === tagVal)
              breadcrumbList[tagName] = currentOperator && currentOperator.id ? currentOperator.text : tagVal
            }
          } else {
            if (tagVal && tagName) {
              breadcrumbList[tagName] = tagVal
            }
          }
        })
        this.$emit('search', {
          requestData: searchValues,
          breadcrumbList
        })
      },
      clear(...key) {
        key.forEach(k => {
          // this.$set(this.searchForm, k, '')
          this.searchForm[k].v = ''
        })
      },
      async getPcList(query='') {
        if(typeof query == 'object') { // focusEvent
          query = ''
        }
        try{
          // this.pcListloading = true;
          setTimeout(async () => {
            const rsp = await Axios.post('/sms/getBatchNum', JSON2FormData({
              pc: query,
              page: this.pcSelectPage,
              rows: this.pcPageSize
            }));
            const {rows,total} = rsp.data
            this.pcTotal = total
            if(rows && rows.length){
              this.pcList =  rows.map(v => ({
                id: v.pch,
                text: v.pcmc,
              }))
            }
          }, 100);
        }catch(e){
          console.log('error : ', e)
          this.$errorMsg('数据加载失败')
        }finally{
          // this.pcListloading = false;
        }
      },
      clearAll() {
        this.clear(...Object.keys(this.searchForm))
      },
      prePage () {
        --this.pcSelectPage;
        if (this.pcSelectPage < 1) {
          this.pcSelectPage = 1;
        }
        this.getPcList();
      },
      nextPage () {
        if (this.pcSelectPage < this.pcPageCount) {
          ++this.pcSelectPage;
          this.getPcList();
        }
      },
      // changePc(val){
      //   console.log("changePc :", val, this.pcList)
      //   for (const i of this.pcList) {
      //     if(i.id == val){
      //       this.searchForm.pc.v = i.id
      //       break;
      //     }
      //   }
      // }
    },
    mounted() {
      this.requestOperator()
      if(Object.keys(extend).length){ // 组合查询
        this.$emit('combined-query', extend)
        // Object.keys(extend).forEach(i =>{
        //   if(this.searchForm.hasOwnProperty(i)){
        //     this.searchForm[i]['v'] = extend[i]
        //   }
        // })
      } else {
        this.getPcList()
        setTimeout(() => {
          this.searchForm.startTime.v = [new Date(top.$GlobalDefine.getSystemTime(null).setDate(1)).format('yyyy-MM-dd'), '00:00:00'].join(' ')
          this.search()
        })
      }
    },
  }
</script>
<style>
.selectPc{
  width:650px;
  background-color: #f5f9ff;
  height: calc(100% - 265px);
}
.selectPc .el-scrollbar{
  height: 100% !important;
}

.selectPc .el-select-dropdown__wrap{
  max-height: calc(100vh - 301px) !important;
}
/* .selectPc .el-select-dropdown__list{
  height: calc(100vh - 430px) !important;
} */
.selectPc span{
  width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.selectPc .text {
  padding-left: 10px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  color: #64acff;
}
.selectPc .el-select-dropdown__item.hover,.selectPc .el-select-dropdown__item:hover{
  background-color: #64acff;
  color: white;
}
</style>
<style scoped>
  .title{
    font-family: MicrosoftYaHei;
    font-size: 20px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 52px;
    padding-top: 10px;
    letter-spacing: 0px;
    color: #000000;
  }
  .top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    padding-left: 3px;
    padding-right: 3px;
    overflow: hidden;
    min-width:1860px;
  }
  .search-form-inline {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .search-form-inline .el-form-item {
    margin: 0 15px 0 0;
  }
  .el-divider {
    margin: 10px auto;
  }
  .el-input {
    width: 120px;
  }
  .el-select-pc .el-input {
    width: 270px;
  }
  .el-date-editor {
    width: 245px;
  }
  .right-btn-box-title {
    display: block;
    float:right;
    margin-top:-37px;
    margin-right:12px;
  }
  .right-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .right-btn-box .el-button.btn
  ,.right-btn-box-title  .el-button.btn{
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 0;
  }
  .right-btn-box .el-button.btn:hover
  ,.right-btn-box-title .el-button.btn:hover{
    background-color: rgb(99, 169, 251);
  }
</style>