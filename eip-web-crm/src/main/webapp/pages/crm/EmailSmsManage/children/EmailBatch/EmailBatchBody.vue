<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="clearSearch"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right"></div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 250px)"
      border
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      :handle-pagination="handlePagination"
      @sort-change="sort"
      @selection-change="$emit('table-select', $event)"
      operation-width="120px"
      :total="total"
      :loading="loading">
      <template slot="batchName" slot-scope="{row}">
        <el-link :underline="false" type="primary" @click.stop="gotoDetails(row)">
          {{ row.batchName }}
        </el-link>
      </template>
      <template slot="alex-operation" slot-scope="{row}">
        <el-link :underline="false" type="primary" @click.stop="toggleSendState(row)">
          {{ row.state === 0 ? '中止发送' : row.state === 2 ? '继续发送' : '' }}
        </el-link>
      </template>
      <template slot="process" slot-scope="{row}">
        <span>{{ row['successNumber'] + row['failedNumber'] }}/{{ row['sendNumber'] }}</span>
      </template>
      <template slot="state" slot-scope="{row}">{{ row.state | state2text }}</template>
    </alex-table>
  </el-container>
</template>

<script>
const AlexTable = importC('AlexTable')
export default {
  name: "EmailSettingsMainBody",
  inject: ['dumpToLoggers'],
  data() {
    return {
      breadcrumbList: [],
      fields: [],
      total: 0,
      tableData: [],
      loading: false,
      requestHistory: {rows: 20, page: 1},
    }
  },
  methods: {
    sort({column, prop, order}) {
      let data = this.requestHistory;
      if (order) {
        data.order = order.replace('ending', '');
        data.sort = prop;
      } else {
        delete (data.order)
        delete (data.sort)
      }
      this.requestTableData(data)
    },
    clearSearch() {
      this.breadcrumbList = [];
      this.requestTableData({page: 1, rows: 20});
      this.$emit('clear-search')
    },
    handlePagination({page, rows}) {
      this.requestHistory.page = page
      this.requestHistory.rows = rows
      this.requestTableData(this.requestHistory)
    },
    createFields(callback) {
      this.fields = [
        {label: '邮件批次', prop: 'batchName', width: 300},
        {label: '发送总数', prop: 'sendNumber', width: 150},
        {label: '邮件主题', prop: 'subject', width: 200},
        {label: '发送进度', prop: 'process', width: 100},
        {label: '成功', prop: 'successNumber', minWidth: 100},
        {label: '失败', prop: 'failedNumber', width: 100},
        {label: '发送状态', prop: 'state', width: 100},
        {label: '发送时间', prop: 'sendTime', width: 180},
        {label: '操作员', prop: 'employeeName', width: 120},
        {label: '发件邮箱', prop: 'sendEmail', width: 220},
      ]
      callback.call(this)
    },
    requestTableData(queryData = {}, callback = () => this.loading = false) {
      this.loading = true
      this.requestHistory = queryData
      //对分页器的值绑定
      if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
        this.$refs.table.setPager(queryData.page, queryData.rows)
      }
      Axios.post('/emailBatch/getPage', JSON2FormData(queryData)).then(({data: res}) => {
        if (res.state !== 1) return this.$errorMsg('数据加载失败')
        this.total = res.total
        this.tableData = res.rows
        callback(this.$refs.table, res.rows)
      }).catch(e => {
        this.$errorMsg('数据加载失败')
        this.loading = false
      })
    },
    updateColumnDisplay() {
      this.createFields(() => {
        this.requestTableData(this.requestHistory)
      })
    },
    async toggleSendState(row) {
      try {
        let uri = ''
        if (row.state === 0) {
          uri = '/emailLog/stopSend'
        } else if (row.state === 2) {
          uri = '/emailLog/sendUnfinishedEmail'
        }
        if (!uri || !row.emailBatchId) return
        const {data} = await Axios.post(uri, JSON2FormData({emailBatchId: row.emailBatchId}))
        if (data.state !== 1) await Promise.reject(data.msg)
        this.$message.success('操作成功')
        this.requestTableData(this.requestHistory)
      } catch (e) {
        this.$errorMsg('操作失败' + (e ? `, ${e}` : ''))
      }
    },
    gotoDetails({emailBatchId, batchName}) {
      if (!emailBatchId) return
      this.dumpToLoggers()
      $bus.$emit('eb-details', {emailBatchId, batchName})
    },
    search({requestData, breadcrumbList}, isFirst) {
      if (isFirst) this.createFields(Boolean)
      requestData.page = 1
      requestData.rows = 20
      this.breadcrumbList = breadcrumbList
      this.requestTableData(requestData)
    }
  },
  filters: {
    state2text(value) {
      if (value === null || value === void 0) return ''
      return ['发送中', '已完成', '已停止'][+value]
    }
  },
  components: {
    AlexTable,
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.alex-table .el-table th, .el-table--small td, .el-table--small th {
  padding: 4px 0 !important;
}
</style>