<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
              class="clear-search"
              @click.native="clearSearch"
              title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right"></div>
    </el-header>
    <alex-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 300px)"
        border
        size="small"
        class="alex-table"
        mode="multiple"
        :fields="fields"
        :handle-pagination="handlePagination"
        @sort-change="sort"
        @selection-change="$emit('table-select', $event)"
        :total="total"
        :show-operation="false"
        :loading="loading">
      <template slot="opera" slot-scope="{row}">
        <div style="text-align:center;margin-left: -10px;">
          <el-link :underline="false" type="primary" @click.stop="showDetails(row)">查看详情</el-link>
        </div>
      </template>
      <template slot="state" slot-scope="{row}">{{row.state | state2text}}</template>
      <template slot="sendMode" slot-scope="{row}">{{row.sendMode | sendMode2text}}</template>
      <template slot="batchName" slot-scope="{row}" v-if="row.batchName">
        {{row.batchName}} <template v-if="row.employeeName"> | {{row.employeeName}}</template>
      </template>
    </alex-table>
  </el-container>
</template>

<script>
  const AlexTable = importC('AlexTable')
  export default {
    name: "EmailSettingsMainBody",
    data() {
      return {
        breadcrumbList: [],
        fields: [],
        total: 0,
        tableData: [],
        loading: false,
        requestHistory: {rows: 20, page: 1},
      }
    },
    methods: {
      sort({column, prop, order}) {
        let data = this.requestHistory;
        if (order) {
          data.order = order.replace('ending', '');
          data.sort = prop;
        } else {
          delete (data.order)
          delete (data.sort)
        }
        this.requestTableData(data)
      },
      clearSearch() {
        this.breadcrumbList = [];
        this.requestTableData({page: 1, rows: 20});
        this.$emit('clear-search')
      },
      handlePagination({page, rows}) {
        this.requestHistory.page = page
        this.requestHistory.rows = rows
        this.requestTableData(this.requestHistory)
      },
      createFields(callback) {
        this.fields = [
          {label: '批次号', prop: 'batchName', width: 300},
          {label: '收件邮箱', prop: 'receiveEmail', width: 200},
          {label: '发送时间', prop: 'sendTime', width: 180},
          {label: '发送类型', prop: 'sendMode', width: 120},
          {label: '发件邮箱', prop: 'sendEmail', width: 200},
          {label: '业务员', prop: 'employeeName', width: 120},
          {label: '邮件主题', prop: 'subject', minWidth: 300},
          {label: '发送结果', prop: 'state', width: 120},
          {label: '操作', prop: 'opera', sortable: false, width: 120, headerAlign: 'center', fixed: 'right'},
        ]
        callback.call(this)
      },
      requestTableData(queryData = {}, callback = () => this.loading = false) {
        this.loading = true
        this.requestHistory = queryData
        //对分页器的值绑定
        if (this.$refs.table && ('rows' in queryData || 'page' in queryData)) {
          this.$refs.table.setPager(queryData.page, queryData.rows)
        }
        Axios.post('/emailLog/getPage', JSON2FormData(queryData)).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('数据加载失败')
          this.total = res.total
          this.tableData = res.rows
          callback(this.$refs.table, res.rows)
        }).catch(e => {
          this.$errorMsg('数据加载失败')
          this.loading = false
        })
      },
      updateColumnDisplay() {
        this.createFields(() => {
          this.requestTableData(this.requestHistory)
        })
      },
      showDetails({emailLogId}) {
        this.$emit('read-row', emailLogId)
      },
      search({requestData, breadcrumbList}) {
        requestData.page = 1
        requestData.rows = 20
        this.breadcrumbList = breadcrumbList
        this.requestTableData(requestData)
      }
    },
    mounted() {
      this.updateColumnDisplay()
    },
    filters: {
      state2text(value) {
        return value === 1 ? '成功' : '失败'
      },
      sendMode2text(value) {
        return value === 1 ? '自动通知类' : '手动发送'
      }
    },
    components: {
      AlexTable,
    }
  }
</script>

<style scoped>
  .main-body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3px;
  }

  .alex-table {
    width: 100%;
    padding-top: 10px;
  }

  .alex-table .el-table th, .el-table--small td, .el-table--small th {
    padding: 4px 0 !important;
  }
</style>