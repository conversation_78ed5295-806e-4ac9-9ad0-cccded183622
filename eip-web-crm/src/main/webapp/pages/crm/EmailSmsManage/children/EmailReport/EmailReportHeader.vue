<template>
  <el-main style="padding: 0 20px">
    <div>
      <center class="title">邮件发送日志查询</center>
      <div class="right-btn-box-title">
        <el-button v-if="getAuthority('fu_report_export')" size="mini" class="btn" @click="$emit('export')">导出</el-button>
      </div>
    </div>
    <el-divider></el-divider>
    <el-header class="top-header" height="auto">
      <el-form :inline="true" size="mini" :model="searchForm" class="search-form-inline">
        <el-form-item label="发件邮箱" prop="sendEmail">
          <el-input v-model="searchForm.sendEmail" style="width: 200px;" placeholder="发件邮箱" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="收件邮箱" prop="receiveEmail">
          <el-input v-model="searchForm.receiveEmail" style="width: 200px;" placeholder="收件邮箱" v-focus v-enter="search" clearable></el-input>
        </el-form-item>
        <el-form-item label="业务员" v-if="getAuthority('fu_client_data_overview')" prop="operatorName">
          <el-select
              v-model="searchForm.operatorName"
              placeholder="选择业务员"
              clearable
              filterable
              default-first-option
              @clear="clear('operatorName','operatorId')"
              @change="$bothWayStore($event,'operators','operatorId','operatorName','searchForm')"
          >
            <el-option
                v-for="operator in operators"
                :label="operator.operatorName"
                :key="operator.operatorId"
                :value="operator.operatorName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="邮件主题" prop="subject">
          <el-input v-model="searchForm.subject" style="width: 200px;" placeholder="邮件主题" v-enter="search" clearable></el-input>
        </el-form-item>

        <el-form-item label="发送类型" prop="sendMode">
          <el-select
            v-model="searchForm.sendMode"
            placeholder="发送类型"
            clearable
            filterable
            default-first-option>
            <el-option label="全部" value=""></el-option>
            <el-option label="自动通知类" :value="1"></el-option>
            <el-option label="手动发送" :value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="sendTimeStart" label="发送时间">
          <el-date-picker
            style="width: 200px"
            v-model="searchForm.sendTimeStart"
            type="datetime"
            default-time="00:00:00"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="mini"
            clearable
            @clear="clear('sendTimeStart')"
            placeholder="选择时间">
          </el-date-picker>
          <el-date-picker
            style="width: 200px"
            v-model="searchForm.sendTimeEnd"
            type="datetime"
            default-time="23:59:59"
            value-format="yyyy-MM-dd HH:mm:ss"
            size="mini"
            clearable
            @clear="clear('sendTimeStart')"
            placeholder="选择时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发送状态" prop="state">
          <el-select
            v-model="searchForm.state"
            placeholder="发送状态"
            clearable
            filterable
            default-first-option>
            <el-option label="全部" value=""></el-option>
            <el-option label="成功" :value="1"></el-option>
            <el-option label="失败" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">检索</el-button>
        </el-form-item>
      </el-form>
    </el-header>
  </el-main>
</template>

<script>
  export default {
    name: "EmailSettingsHeader",
    data() {
      return {
        searchForm: {
          operatorId: '',
          operatorName: '',
          state: '',
          sendMode: '',
          subject: '',
          sendEmail: '',
          receiveEmail: '',
          sendTimeStart: [new Date(top.$GlobalDefine.getSystemTime(null).setDate(1)).format('yyyy-MM-dd'), '00:00:00'].join(' '),
          sendTimeEnd: '',
        },
        dataMap: {
          subject: '邮件主题',
          sendEmail: '发送邮箱',
          receiveEmail: '收件邮箱',
          operatorName: '业务员',
          sendTimeStart: '发送时间起',
          sendTimeEnd: '发送时间止',
          state: '发送状态',
          sendMode: '发送类型',
        },
        operators: [],
      }
    },
    methods: {
      search() {
        const {
          sendEmail,
          subject,
          operatorId,
          operatorName,
          sendMode,
          sendTimeEnd,
          sendTimeStart,
          receiveEmail,
          state
        } = this.searchForm
        const searchValues = {
            sendEmail,
            subject,
            operatorId,
            state,
            sendMode,
            receiveEmail,
            sendTimeStart,
            sendTimeEnd,
          },
          breadcrumbValues = {
            sendEmail,
            subject,
            operatorName,
            state: state === 1 ? '成功': '失败',
            sendMode: sendMode ? sendMode === 1 ? '自动通知类': '手动发送' : '',
            receiveEmail,
            sendTimeStart,
            sendTimeEnd,
          },
          result = {
            requestData: {},
            breadcrumbList: []
          }
        let breadcrumbKeys = Object.keys(breadcrumbValues)
        Object.keys(searchValues).forEach((k, index) => {
          let val = searchValues[k], bk = breadcrumbKeys[index], bv = breadcrumbValues[bk]
          if (!val && val !== 0) return
          result.requestData[k] = val
          result.breadcrumbList.push(`${this.dataMap[bk]}: ${bv}`)
        })
        this.$emit('search', result)
      },
      clear(...key) {
        key.forEach(k => this.$set(this.searchForm, k, ''))
      },
      getOperatorList() {
        Axios.post('/operator/selectByMap', JSON2FormData({workTeamId: teamId, showAllState: true})).then(({data: res}) => {
          this.operators = res.map(v => ({operatorId: Number(v.id), operatorName: v.text}))
        })
      },
      clearAll() {
        this.clear(...Object.keys(this.searchForm))
      }
    },
    mounted() {
      getAuthority('fu_client_data_overview') && this.getOperatorList()
    },
  }
</script>

<style scoped>
  .title{
    font-family: MicrosoftYaHei;
    font-size: 20px;
    font-weight: bold;
    font-stretch: normal;
    line-height: 52px;
    padding-top: 10px;
    letter-spacing: 0;
    color: #000000;
  }
  .top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    padding-left: 3px;
    padding-right: 3px;
  }

  .search-form-inline {
    display: flex;
    flex-wrap: wrap;
  }

  .search-form-inline .el-form-item {
    margin: 0 15px 15px 0;
  }

  .el-divider {
    margin: 10px auto;
  }

  .el-input {
    width: 120px;
  }

  .el-date-editor {
    width: 245px;
  }

  .right-btn-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-btn-box-title {
    display: block;
    float:right;
    margin-top:-37px;
    margin-right:12px;
  }

  .right-btn-box .el-button.btn
  ,.right-btn-box-title  .el-button.btn{
    border: none;
    background-color: rgb(159, 169, 179);
    height: 28px;
    color: #fff;
    margin: 0 10px 0 0;
  }
  .right-btn-box .el-button.btn:hover
  ,.right-btn-box-title .el-button.btn:hover{
    background-color: rgb(99, 169, 251);
  }
</style>