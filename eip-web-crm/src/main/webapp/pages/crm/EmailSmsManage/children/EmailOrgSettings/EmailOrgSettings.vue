<template>
  <div>
    <email-settings-header @delete="deleteItem" @add="openDetails('add')"></email-settings-header>
    <email-settings-main-body
        ref="body"
        @table-select="tableSelect"
        @operation-row="openDetails(...$event)">
    </email-settings-main-body>
    <email-settings-dialog ref="dialog" @operation-complete="$refs.body && $refs.body.refresh()"></email-settings-dialog>
  </div>
</template>

<script>
  const EmailSettingsDialog = importC('EmailSettingsDialog')
  const EmailSettingsHeader = importC('EmailSettingsHeader')
  const EmailSettingsMainBody = importC('EmailSettingsMainBody')

  export default {
    name: "EmailOrgSettings",
    provide() {
      return {
        isOrgSetting: true
      }
    },
    data() {
      return {
        tableSelection: []
      }
    },
    methods: {
      deleteItem() {
        return this.$refs['body'].delBatch(this.tableSelection.map(v=>({emailAccountId:v.emailAccountId})))
      },
      tableSelect(rows){
        this.$set(this,'tableSelection', rows)
      },
      openDetails(mode, id){
        this.$refs['dialog'].open(mode, id)
      }
    },
    components: {
      EmailSettingsDialog,
      EmailSettingsHeader,
      EmailSettingsMainBody
    },
  }
</script>