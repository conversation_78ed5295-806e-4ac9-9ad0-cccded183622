<template>
  <div>
    <el-tabs
      v-if="!isFullScreen"
      class="panel-box"
      v-model="activeName"
      :before-leave="beforeLeave"
      tab-position="left"
      type="card"
    >
      <el-tab-pane
        v-for="data in pageMap"
        :label="data.text"
        :name="data.id"
        :key="data.id"
      >
        <component :is="data.component" :id="data.id"></component>
      </el-tab-pane>
    </el-tabs>
    <component v-else :is="component"></component>
  </div>
</template>
<script>
const SmsTemplateSet = importC('SmsTemplateSet')
const SmsAccountSet = importC('SmsAccountSet')
const SmsServiceStatus = importC('SmsServiceStatus')
const EmailSettings = importC('EmailSettings')
const SendEmail = importC('SendEmail')
const EmailReport = importC('EmailReport')
const EmailBatch = importC('EmailBatch')
const SmsLog = importC('SmsLog')
const EmailOrgSettings = importC('EmailOrgSettings')
const SmsSpecialSettings = importC('SmsSpecialSettings')
export default {
  name: "App",
  provide() {
    return {
      dumpToSettings: this.dumpToSettings,
      dumpToLoggers: this.dumpToLoggers
    }
  },
  data() {
    return {
      pageMapList: [],
      pageMapEmail: [],
      activeName: 'null',
      component: 'PlaceHolder',
      pageMap: [],
    }
  },
  created() {
    this.pageMapList = [
      {
        id: 'sms-template',
        text: '短信模板设置',
        component: 'PlaceHolder',
        authority: getAuthority('fu_crm_msgset_temp'),
      },
      {
        id: 'sms-account',
        text: '短信通道账号设置',
        component: 'PlaceHolder',
        authority: getAuthority('fu_crm_msgset_account'),
      },
      {
        id: 'sms-service',
        text: '短信服务状态',
        component: 'PlaceHolder',
        authority: getAuthority('fu_crm_msgset_service'),
      },
      {
        id: 'send',
        text: '邮件内容',
        component: 'PlaceHolder',
        authority: 1,
      },
      {
        id: 'settings',
        text: '营销类发件邮箱设置',
        component: 'PlaceHolder',
        authority: 1,
      },
      {
        id: 'org-email',
        text: '验证码发件邮箱设置',
        component: 'PlaceHolder',
        authority: getAuthority('fu_crm_org_email_set'),
      },
      {
        id: 'special-settings',
        text: '特定短信邮件设置',
        component: 'PlaceHolder',
        authority: true,
      },
    ].filter(v => v.authority)
    this.pageMapEmail = [
      {
        id: 'email-report',
        text: '邮件发送日志',
        component: 'PlaceHolder',
        authority: true,
      },
      {
        id: 'email-batch',
        text: '邮件发送批次记录',
        component: 'PlaceHolder',
        authority: true,
      },
    ].filter(v => v.authority)
  },
  mounted() {
    if (this.isFullScreenChecker(page)) {
      this.activeName = page
      this.component = 'SmsLog'
      return
    }
    const pageMap = this.isEmailLoggerChecker(page) ? this.pageMapEmail : this.pageMapList
    this.pageMap = pageMap
    if (!pageMap.length) return this.$errorMsg('权限不足!')
    // 没传正确的值,取有权限的第一个
    const currentPage = pageMap.find(it => page === it.id)
    if (!currentPage) return (this.activeName = pageMap[0].id)
    this.activeName = currentPage.id

  },
  methods: {
    dumpToSettings() {
      this.activeName = 'settings'
    },
    dumpToLoggers() {
      this.activeName = 'email-report'
    },
    beforeLeave(activeName) {
      const $current = this.pageMap.find(it => activeName === it.id)
      if ($current.component !== 'PlaceHolder') {
        if (!this.isEmailLoggerChecker(page)) {
          sessionStorage.setItem('esm-page', activeName)
        }
        return true
      }
      switch (activeName) {
        case "sms-template":
          $current.component = 'SmsTemplateSet'
          break
        case "sms-account":
          $current.component = 'SmsAccountSet'
          break
        case "sms-service":
          $current.component = 'SmsServiceStatus'
          break
        case "send":
          $current.component = 'SendEmail'
          break
        case "settings":
          $current.component = 'EmailSettings'
          break
        case "email-report":
          $current.component = 'EmailReport'
          break
        case "email-batch":
          $current.component = 'EmailBatch'
          break
        case "org-email":
          $current.component = 'EmailOrgSettings'
          break
        case "special-settings":
          $current.component = 'SmsSpecialSettings'
          break
      }
      if (!this.isEmailLoggerChecker(page)) {
        sessionStorage.setItem('esm-page', activeName)
      }
      return true
    },
  },
  components: {
    SmsTemplateSet,
    SmsAccountSet,
    SmsServiceStatus,
    SendEmail,
    EmailSettings,
    EmailReport,
    EmailBatch,
    SmsLog,
    EmailOrgSettings,
    SmsSpecialSettings,
    PlaceHolder: {template: '<div style="height: 100vh;display:flex;align-items:center;justify-content:center;color: #aaa">暂无数据</div>'}
  },
  watch: {
    activeName(newVal) {
      const $current = this.pageMapList.find(it => newVal === it.id)
      if (!$current) return
      switch (newVal) {
        case 'send':
          $current.component = 'SendEmail'
          break
        case 'settings':
          $current.component = 'EmailSettings'
          break
      }
    }
  },
  computed: {
    isEmailLoggerChecker() {
      return page => page === 'email-report' || page === 'email-batch'
    },
    isEmailLogger() {
      return this.isEmailLoggerChecker(this.activeName)
    },
    isFullScreen() {
      return this.isFullScreenChecker(this.activeName)
    },
    isFullScreenChecker() {
      return name => name === 'sms_log'
    }
  }
}
</script>

<style scoped>

.el-tabs__header.is-left, .el-tabs__header.is-left * {
  border: none !important;
}

.el-tabs--left .el-tabs__header.is-left {
  width: 238px;
  height: 100vh;
  background-color: #fafafa;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  padding: 0 0 0 40px;
  line-height: 40px;
  user-select: none;
  transition: all .3s;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  background: no-repeat 20% center;
  background-size: 16px;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
.el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
  background-color: #e9f1fd;
  color: #2e82e4;
}
</style>