;!(function (vue) {
  vue.prototype.$errorMsg = function (msg, title = '错误', type = 'error') {
    return this.$alert(msg, title, {type})
  }
  const Axios = axios.create({
    baseURL: variableSponsor,
    transformResponse: data => {
      if (typeof data !== 'string') return data
      try {
        const re1 = data.replace(/"\s*:\s*([0-9]{18,})\s*(,?)/g, '": "$1" $2')
        return JSON.parse(re1)
      } catch (e) {
        return JSON.parse(data)
      }
    }
  })
  //混入对象
  const mixin = {
    methods: {
      closeTips(e) {
        return typeof e === 'function' || typeof e === 'object' ?
          this.$confirm('确定取消发送吗？', '提示', {type: 'warning'}) :
          Promise.resolve()
      }
    },
    components: {customDialog}
  }
  //选择短信模板
  const st = `<custom-dialog :show="show" @before-close="close" @closed="saving=false" title="选择短信模板" width="572px" id="SelectTemplate">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <el-button
          @click="saveTemplate(!0)"
          size="mini"
          icon="el-icon-document-remove"
          :loading="saving"
          type="info">
            空白模板
        </el-button>
        <el-button
          @click="saveTemplate()"
          size="mini"
          icon="el-icon-circle-check"
          :loading="saving"
          type="primary">
            确定
        </el-button>
      </el-header>
      <el-main>
        <el-radio-group class="template-list" v-model="selected" v-if="templateList.length">
          <el-radio :label="item.smsTempId" :key="item.smsTempId" v-for="item in templateList">
            <span class="title">
              {{item.tempName}}<el-link @click.native.stop="item.expand = !item.expand" :icon="'el-icon-arrow-' + (item.expand ? 'up' : 'down')" type="primary" :underline="false">{{item.expand ? '收起': '详情'}}</el-link>
            </span>
             <el-collapse-transition>
               <p v-show="item.expand" class="content" @click.stop>【{{item.sign}}】{{item.content}}</p>
            </el-collapse-transition>
          </el-radio>
        </el-radio-group>
        <div class="template-list" v-else>该通道暂无已报备模板，请使用空白模板</div>
      </el-main>
    </el-main>
  </custom-dialog>`
  //发送短信的对话框
  const ss = `<custom-dialog 
      title="发送短信" 
      append2body 
      id="SendSms" 
      @before-close="close" 
      @closed="sending=false"
      width="620px"
      :show="show">
      <el-main slot="body" class="sendSMS">
        <el-header height="40px">
          <div class="header-message">每67个字收取一条短信费，含空格、标点符号以及签名字数</div>
        </el-header>
        <el-form 
          :show-message="false"
          class="tForm"
          @submit.native.prevent
          ref="tForm" 
          :model="tForm" 
          label-width="118px"
          size="mini">
          <el-form-item label="短信内容" prop="content" required>
            <el-input type="textarea" v-if="show" v-focus :rows="4" resize="none"  placeholder="请输入短信内容" v-model="tForm.content">
            </el-input>
          </el-form-item>
          <el-form-item label="签名" prop="sign" required>
            <el-input v-model="tForm.sign" @change="signChange" placeholder="【展之科技】"></el-input>
          </el-form-item>
          <el-form-item label="发送手机号" prop="mobileStr" required>
            <el-input type="textarea" :rows="4" resize="none" placeholder="一行一个手机号" v-model="tForm.mobileStr">
            </el-input>
          </el-form-item>
        </el-form>
      </el-main>
      <el-footer slot="footer" class="sendSMSFooter">
        <el-button plain size="small" @click="close">取消</el-button>
        <el-button type="primary" size="small" @click="startSend" :loading="sending">发送{{sending? '中...': ''}}</el-button>
      </el-footer>
    </custom-dialog>`
  //对应的组件对象
  const stC = {
    template: st,
    mixins: [mixin],
    data() {
      return {
        show: false,
        saving: false,
        selected: '',
        templateList: []
      }
    },
    methods: {
      open(smsAccountId) {
        this.show = true
        this.requestTemplates(smsAccountId)
      },
      requestTemplates(smsAccountId) {
        Axios.post('/smsTemp/getPage', JSON2FormData({report: 1, smsAccountId})).then(({data: res}) => {
          if (res.state !== 1) return Promise.reject()
          this.templateList = res.rows.map(item => ({...item, expand: false}))
        }).catch(e => {
          this.$errorMsg(e + '')
        })
      },
      saveTemplate(force) {
        if(force) {
          this.$emit('finished', {})
          return this.close()
        }
        if (!this.selected)
          return this.$errorMsg('请先选择短信模板！')
        const selected = this.templateList.find(item => item.smsTempId === this.selected)
        this.$emit('finished', selected || {})
        this.close()
      },
      close(e) {
        this.closeTips(e).then(() => {
          this.show = false
          this.selected = ''
          this.templateList = []
          e &&this.$emit('cancel')
        }).catch(() => {})
      },
    }
  }
  const ssC = {
    template: ss,
    mixins: [mixin],
    data() {
      return {
        tForm: {
          sign: '',
          content: '',
          mobileStr: '',
          smsAccountId: '',
        },
        show: false,
        sending: false,
      }
    },
    methods: {
      close(e) {
        this.closeTips(e).then(() => {
          this.show = false
          this.$refs.tForm && this.$refs.tForm.clearValidate()
          e && this.$emit('cancel')
        }).catch(e=>e)
      },
      open(testId, {smsTempId}) {
        // clear form
        Object.keys(this.tForm).forEach(key =>this.$set(this.tForm, key, ''))
        // fill data
        smsTempId && this.requestById(smsTempId)
        this.tForm.smsAccountId = testId
        this.show = true
      },
      startSend() {
        this.$refs.tForm.validate().then(()=>{
          this.sending = true
          const url = '/smsAccount/testSendMessage'
          const data = Object.assign({}, this.tForm)
          data.mobile = data.mobileStr.trim().split('\n').toString()
          delete data.mobileStr
          Axios.post(url, JSON2FormData(data)).then(({data: res}) => {
            if (res.state !== 1)
              return Promise.reject(res.msg)
            this.$message.success('发送成功！')
            this.close()
          }).catch(e => {
            console.warn(e)
            this.sending = false
            this.$message.error('发送失败！ 错误信息:' + e)
          }).finally(()=>{
            this.$emit('finished')
          })
        }).catch(e=>{
          if(e === false) return this.$errorMsg('带 \'*\' 为必填项！').finally(this.$focusError)
          console.warn(e)
        })

      },
      requestById(smsTempId, callback) {
        Axios.post('/smsTemp/getById', JSON2FormData({smsTempId})).then(({data: res}) => {
          if (res.state !== 1)
            return this.$errorMsg('模板数据加载失败！')
          const {content, sign} = res.data
          this.$set(this.tForm, 'content', content)
          this.$set(this.tForm, 'sign', sign)
          callback && callback()
        }).catch(e => {
          console.warn(e)
          this.$msg('模板数据加载失败！')
        })
      },
      signChange(val) {
        val && this.$set(this.tForm, 'sign', `【${val.replace(/[【】]/g, '')}】`)
      },
    },
  }
  //模版
  const template = `<div>
      <st-c ref="stC" @finished="elem.ss.open(options.testId, $event)" @cancel="cancel"></st-c>
      <ss-c ref="ssC" @finished="finished" @cancel="cancel"></ss-c>
    </div>`
  //主调用者
  const Index = {
    template,
    data(){
      return {
        options: {},
        elem:{
          st: null,
          ss: null,
        },
      }
    },
    mounted(){
      const refs = this.$refs
      this.elem.st = refs.stC || null
      this.elem.ss = refs.ssC || null
    },
    methods:{
      finished(){
        const _callback = this.options['callback']
        _callback && typeof _callback === 'function' && _callback.call(this)
        this.options = {}
      },
      cancel(){
        const _cancel = this.options['cancel']
        _cancel && typeof _cancel === 'function' && _cancel.call(this)
      },
      $testMessage(testId, finished, cancel){
        if(!testId) return this.$errorMsg('数据异常，请刷新重试！')
        this.options = {testId, finished, cancel}
        this.elem.st.open(testId)
      }
    },
    components:{stC, ssC}
  }
  $ && $(function () {
    const IndexConstructor = vue.extend(Index)
    const instance = new IndexConstructor()  //创建Index子实例
    instance.$mount(document.createElement('div'))  //挂载实例到我们创建的DOM上
    document.body.appendChild(instance.$el)
    window.$testMessage = vue.prototype.$testMessage = instance.$testMessage
  })
})(Vue)