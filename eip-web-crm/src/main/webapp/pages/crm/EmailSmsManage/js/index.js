// * -----------------------TODO: 在此添加组件路径（相对于导入他的html文件位置）-----------------------
function SetComponentList() {
  const commonComponents = [
    'App.vue',
    '../VueCommonComponents/CustomDialog.vue',
    '../VueCommonComponents/CompanySelect.vue',
    '../VueCommonComponents/AlexTable.vue',
    '../VueCommonComponents/ProjectSelect.vue?v=240109',
    '../VueCommonComponents/EmbedInput.vue',
  ], _page = page || ''

  let pageComponents = []
  switch (_page) {
    case "sms-template":
    case "sms-account":
    case "sms-service":
    case "settings":
    case "send":
    case "org-email":
    case "special-settings":
      //发送邮件和短信设置（聚合页面）
      pageComponents = [
        "children/EmailOrgSettings/EmailOrgSettings.vue",
        "children/EmailSettings/EmailSettings.vue",
        "children/EmailSettings/EmailSettingsHeader.vue",
        "children/EmailSettings/EmailSettingsMainBody.vue?v=240410",
        "children/EmailSettings/EmailSettingsDialog.vue",
        "children/SendEmail/SendEmail.vue?v=241105",
        "children/SendEmail/RecipientDialog.vue?v=241105",
        "children/SmsTemplateSet/SmsTemplateSet.vue",
        "children/SmsTemplateSet/SmsTemplateDialog.vue?v=250607",
        "children/SmsAccountSet/SmsAccountDialog.vue?v=250607",
        "children/SmsAccountSet/SmsAccountSet.vue?v=240410",
        "children/SmsServiceStatus/SmsServiceStatus.vue",
        "children/SmsSpecialSettings/SmsSpecialSettings.vue?v=250618",
      ];
      break;
    case "sms_log":
      //sms日志
      pageComponents = [
        "children/SmsLog/SmsLog.vue?v=250607",
        "children/SmsLog/SmsLogHeader.vue?v=250607",
        "children/SmsLog/SmsLogBody.vue?v=250607",
        "children/SmsLog/ResendSms.vue?v=250607",
        "children/SmsLog/CombinedQuery/CombinedQuery.vue?v=250607",
        "children/SmsLog/CombinedQuery/CqHeader.vue",
      ];
      break;
    case "email-report":
    case "email-batch":
      //邮件报表
      pageComponents = [
        "children/EmailReport/EmailReport.vue",
        "children/EmailReport/EmailReportHeader.vue?v=240311",
        "children/EmailReport/EmailReportMainBody.vue?v=240311",
        "children/EmailReport/EmailReportDialog.vue",
        "children/EmailBatch/EmailBatch.vue?v=231228",
        "children/EmailBatch/EmailBatchBody.vue?v=231228",
        "children/EmailBatch/EmailBatchHeader.vue?v=231228",
      ];
      break;
  }
  return [
    ...commonComponents,
    ...pageComponents
  ]
}


// * -----------------------TODO: 在此添加Vue全局配置-----------------------

function SetVueExtent(Vue) {
  //develop消息(提醒测试)
  Vue.prototype.$developMessage = function () {
    this.$message.info('敬请期待...')
  }
  Vue.prototype.$bothWayStore = function(val, listKey, setIdKey, listValKey, setKey) {
    if (!val) return
    let current = this[listKey].find(item => item[listValKey] === val)
    if (!current) {
      console.warn(`No object associated with ${val} was found in vm.${listKey}`)
      return
    }
    setKey && this.$set(this[setKey], setIdKey, current[setIdKey])
    return current[setIdKey]
  }
  Vue.prototype.$errorMsg = function (msg = '数据加载失败！', title = '错误', type = 'error') {
    return this.$alert(msg, title, {type}).catch(() => Promise.resolve())
  }
  Vue.prototype.$msg = function (msg, type = 'error') {
    return this.$message[type](msg)
  }
  Vue.prototype.getAuthority = getAuthority
  window.Axios = axios.create({
    baseURL: variableSponsor
  });
  Vue.prototype.$Axios = window.Axios
  Vue.prototype.$focusError = callback => {
    setTimeout(() => {
      let isError = document.getElementsByClassName("is-error")
      isError.length && isError[0].querySelector('input,textarea').focus()
      callback && callback()
    }, 100)
  }
  Vue.filter('bool2text', (bool, resolveNull) => {
    return !resolveNull ? '否是'.split('')[+bool] : bool === null ? '' : bool ? '是' : '否'
  })
}


// * -----------------------TODO: 在此添加Mixin-----------------------

//监听是否修改
function listenChange(formKey = 'tForm') {
  return {
    data() {
      return {
        isChange: false,
      }
    },
    methods: {
      startChange() {
        //延迟开启检测
        setTimeout(() => {
          this.isChange = false
        }, 10)
      },
      _close(confirmFunc, defaultFunc) {
        confirmFunc = confirmFunc || (() => this.show = false)
        if (this.isChange) {
          return this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            confirmFunc && confirmFunc.call(this)
            this.$nextTick(() => this.$refs[formKey] && this.$refs[formKey].clearValidate())
          }).catch(e => e)
        }
        defaultFunc ? defaultFunc.call(this) : confirmFunc && confirmFunc.call(this)
        this.$nextTick(() => this.$refs[formKey] && this.$refs[formKey].clearValidate())
      },
    },
    watch: {
      [formKey]: {
        //监听他改变了数据没
        handler() {
          if (!this.isChange)
            this.isChange = true
        },
        deep: true
      }
    },
  }
}

//清除表单和初始化表单
function clearAndInitForm(formKey = 'tForm') {
  return {
    methods: {
      clear(...key) {
        key.forEach(k => this.$set(this[formKey], k, ''))
      },
      _initForm(initVal, insertFunc) {
        this.clear(...Object.keys(this[formKey]))
        initVal = initVal || {}
        Object.entries(initVal).forEach(([key, value]) => this.$set(this[formKey], key, value))
        insertFunc && insertFunc.call(this)
        this.$nextTick(() => this.$refs[formKey] && this.$refs[formKey].clearValidate())
      },
      validateForm(formRef) {
        formRef = formRef || formKey
        return new Promise((resolve, reject) => {
          const form = this.$refs[formRef]
          if (!form || !form.validate) return reject()
          form.validate((isValid, invalidFields) => {
            isValid ? resolve(isValid) : reject(invalidFields)
          })
        })
      },
      handleErrors(errors) {
        if (!errors || typeof errors !== 'object' || Object.keys(errors).length < 1) return errors
        const [errorKey] = Object.keys(errors)
        const [{message, callback}] = errors[errorKey]
        callback
          ? callback(message, errorKey)
          : this.$errorMsg(message).finally(this.$focusError)
      },
    },
  }
}

//混入上传相关函数
function upload(idKey, tableName) {
  return {
    data() {
      return {
        fileList: [],
        attrsCodeMap: {}
      }
    },
    methods: {
      uploadSuccess(resp, file, fileList) {
        if (resp.state !== 1)
          return this.$errorMsg(file.name + '上传失败！')
        if (this[idKey])
          this.getDocuments(this[idKey])
        else
          this.attrsCodeMap[resp.result.f_doc_code] = file.uid
        return resp
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${file.name}？`, '警告', {type: 'warning'})
      },
      handleRemove(file, fileList) {
        const accId = file.response ? file.response.result.f_doc_code : file.accId
        Axios.post('/client/delAccById', JSON2FormData({accId})).then(({data: res}) => {
          // if(res.state !== 1)return this.$errorMsg('附件删除失败！')
          delete this.attrsCodeMap[accId]
        })
        return accId
      },
      handlePreview(file) {
        const elementA = document.createElement('A')
        elementA.href = file.url
        elementA.download = file.name
        elementA.click();
      },
      getDocuments(id, callback, table = tableName) {
        let url = `/client/getDocumentsByfOrgNum?recordCode=${id}&tableName=${table}`
        Axios.get(url).then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('附件加载失败！')
          this.attrsCodeMap = {}
          this.fileList = res.result.map(item => {
            this.attrsCodeMap[item.f_doc_code] = item.f_upload_time
            return {
              accId: item.f_doc_code,
              name: item.f_doc_name + item.f_doc_suffix,
              url: attachUrl + item.f_doc_file.replace('\\', '/')
            }
          })
          callback && callback(res)
        })
      },
    },
    computed: {
      uploadInfo() {
        const id = this[idKey]
        const uploadAction = id ?
          '/client/uploadDocumentsByfOrgNum' :
          '/client/uploadDocument'
        const uploadData = id ? {
          tableName: tableName,
          recordCode: id
        } : {}
        return {
          uploadAction: variableSponsor + uploadAction,
          uploadData
        }
      },
    },
  }
}
