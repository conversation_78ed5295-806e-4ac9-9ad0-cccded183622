<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title>会员访问日志</title>
  <!--  开发版-->
  <!--<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>-->
  <script src="../../../vue/vue.js"></script>
  <script src="../../../vue/httpVueLoader.js"></script>
  <script src="../../../vue/directive/dialogDrag.js?v=23.1"></script>
  <script src="../../../vue/axions.js"></script>
  <script src="../../../vue/element-ui/index.js"></script>
  <script src="../js/xlsx-style/cpexcel.js"></script>
  <script src="../js/xlsx-style/FileSaver.js"></script>
  <script src="../js/xlsx-style/jszip.js"></script>
  <script src="../js/xlsx-style/ods.js"></script>
  <script src="../js/xlsx-style/xlsx.js"></script>
  <script src="../js/xlsx-style/xlsx-v.js"></script>
  <link rel="stylesheet" href="../VueCommonComponents/base.css">
  <link rel="stylesheet" href="../../../vue/element-ui/index.css">
  <script src="../VueCommonComponents/mixins.js?v=240401"></script>
  <script src="../js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="../common/utils.js?v=241025"></script>
  <script src="./js/index.js?v=250218"></script>
  <script src="../VueCommonComponents/runtime.js"></script>
</head>
<body>
<div id="app">
  <App/>
</div>
<div id="load"
     v-loading.fullscreen.lock="loading"
     element-loading-text="拼命加载中"
     element-loading-spinner="el-icon-loading"
     element-loading-background="rgba(0, 0, 0, 0.1)">
</div>
<script>
  const storageKey = 'mv-page'
  const teamId = getQueryString("teamId"); //团队id
  const queryType = getQueryString("queryType") || sessionStorage.getItem(storageKey) || '' //页面标识
  const refId = getQueryString("refId"); //其他页面跳过来的Id
  const extend = (_ex => {
    try {
      return JSON.parse(decodeURIComponent(atob(_ex)))
    } catch (e) {
      return {}
    }
  })(getQueryString('extend'))
  window.run() //启动Vue
</script>

</body>
</html>