<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="$emit('clear-search', true)"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" plain @click="$emit('export')">导出</el-button>
        <el-button size="mini" type="primary" @click="$emit('batch-add-opportunity')">批量新增商机</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 190px)"
      border
      :show-operation="false"
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      @sort-change="sort"
      :padding="10"
      operation-width="125px"
      :handle-pagination="handlePagination"
      @selection-change="$emit('table-select', $event)"
      :total="total"
      :loading="loading">
      <template slot="visitState" slot-scope="{row}">
        <span style="cursor: unset"
              :class="['el-link', ['el-link--info', 'el-link--warning', 'el-link--success'][row.visitState - 1]] ">
          {{ (visitStateList.find(item => +item.id === row.visitState) || {}).text || '访问未登记' }}
        </span>
      </template>
      <template slot="existPreorder" slot-scope="{row}">
        <el-link
          v-if="row.existPreorder"
          :underline="false"
          @click.stop="gotoPreorderDetails(row)"
          type="primary">查看
        </el-link>
        <span v-else></span>
      </template>
      <template slot="opportunityId" slot-scope="{row}">
        <el-link
          v-if="row.opportunityId"
          :underline="false"
          @click.stop="readOpportunity(row)"
          type="primary">查看
        </el-link>
        <el-link
          v-else
          :underline="false"
          @click.stop="addNewOpportunity(row)"
          type="primary">新增商机
        </el-link>
      </template>
    </alex-table>
  </el-container>
</template>

<script>
export default {
  name: 'ExhibitionApplyBody',
  inject: ['visitStateList'],
  mixins: [Mixins.table()],
  data() {
    return {
      loading: false,
      tableData: [],
      fields: [],
      total: 0,
      breadcrumbList: []
    }
  },
  mounted() {
    this.initEventListener()
  },
  methods: {
    createFields() {
      this.fields = [
        {label: '项目', prop: 'projectName', width: '200'},
        {label: '会员等级', prop: 'memberGradeName', width: '110'},
        {label: '会员', prop: 'userName', width: '150'},
        {label: '公司名称', prop: 'companyName', width: '180'},
        {label: '联系人', prop: 'linkmanName', width: '120'},
        {label: '职位', prop: 'position', width: '110'},
        {label: '联系方式', prop: 'linkmanMobile', width: '150'},
        {label: '访问状态', prop: 'visitState', width: '120'},
        {label: '相关数据', prop: 'existPreorder', width: '110'},
        {label: '关联商机', prop: 'opportunityId', width: '110'},
        {label: '访问时间', prop: 'visitTime', width: '180'},
      ]
    },
    initEventListener() {
      const tradeIframeIdKey = getCurrentIframeId() + '_tradeIframeId'
      ListenMessage(event => {
        try {
          const data = JSON.parse(event.data)
          switch (data.type) {
            case 'notice':
              window.tradeIframeId = data.iframeId
              if (window.tradeIframeId) {
                sessionStorage.setItem(tradeIframeIdKey, window.tradeIframeId)
              }
              break
            case 'refresh':
            case '$LINK-ACTIVE$':
            case '$ACTIVE$':
              this.refresh()
              break
          }
        } catch (e) {
          console.log(e)
        }
      })
      window.tradeIframeId = sessionStorage.getItem(tradeIframeIdKey)
    },
    search(data, isFirst) {
      const {requestData = {}, breadcrumbList = []} = data || {}
      const postData = {...requestData, page: 1, rows: 20}
      this.breadcrumbList = breadcrumbList
      isFirst && this.createFields()
      this.requestTableData(postData)
    },
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        const {data} = await Axios.post('/boothLayoutVisitsLog/getPage', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject('数据加载失败')
        // 绑定表格分页器
        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = data.total
        this.tableData = data.rows
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    gotoPreorderDetails(row) {
      const url = `${variableSponsorTrue}/backstage/exhibit_center?endPos=EX--${row.clientId}&exhibitCode=${row.exhibitCode}&projectId=${row.projectId}&version=SUBSYS_EIP_M_A`
      window.open(url, '_blank')
    },
    readOpportunity(row) {
      FinalLinkOpen('business_details.html?opportunityId=' + row.opportunityId + "&teamId=" + teamId, null, true, false, row.companyName + '定展商机详情')
    },
    async addNewOpportunity(row) {
      try {
        this.loading = true
        const visitLogId = row.id
        const ret = await this.queryExhibitorOpportunity(visitLogId)
        if (!ret) return
        const {exist} = ret
        if (exist) return this.updateExhibitorOpportunity(visitLogId, ret.opportunityId)
        const k = 'OPPORTUNITY_TMP_' + visitLogId
        sessionStorage.setItem(k, JSON.stringify({action: 'add', from: getCurrentIframeId(), ...ret}))
        FinalLinkOpen(`attendance-record.html?teamId=${teamId}&k=${k}`, null, true, false, '定展商机')
      } finally {
        this.loading = false
      }
    },
    async queryExhibitorOpportunity(visitLogId) {
      try {
        if (!visitLogId) return console.warn('缺少参数: visitLogId')
        const {data} = await Axios.post('/opportunity/queryExhibitorOpportunity', JSON2FormData({
          visitLogId,
          workTeamId: teamId
        }))
        if (data.state === 2) return {exist: true, opportunityId: data.data}
        if (data.state !== 1) await Promise.reject(data.msg)
        return data.data
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '数据加载失败')
      }
    },
    async updateExhibitorOpportunity(visitLogId, opportunityId) {
      try {
        await this.$confirm('已存在商机，是否更新商机描述信息？', '提醒', {type: 'warning'})
        const post = {visitLogId, opportunityId}
        const {data} = await Axios.post('/opportunity/updateExhibitorOpportunity', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject(data.msg)
        this.$message.success({
          message: '商机更新成功',
          showClose: true
        })
        this.refresh()
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '数据加载失败')
      }
    }
  }
}
</script>

<style scoped>
.el-main {
  padding-bottom: 0 !important;
}

.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.el-link.el-link--primary {
  color: #2e82e4;
}
</style>