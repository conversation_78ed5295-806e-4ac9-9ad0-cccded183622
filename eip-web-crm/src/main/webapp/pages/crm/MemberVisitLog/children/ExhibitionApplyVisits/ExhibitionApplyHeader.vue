<template>
  <el-main class="header-layout">
    <div v-show="!isPackUp" class="placeholder"></div>
    <el-header class="top-header" :class="{'pack-down': !isPackUp}" height="auto">
      <el-form inline size="mini" :model="searchForm" ref="searchForm" class="search-form-inline">
        <el-form-item label="项目名称">
          <el-tooltip
            :content="'项目名称:' + searchForm.projectName"
            placement="bottom"
            :disabled="!searchForm.projectName"
            :open-delay="300">
            <el-input
              class="project"
              v-model="searchForm.projectName"
              readonly
              @click.native="showProjectSelect = true"
              placeholder="选择项目名称"
            >
              <i slot="suffix" class="el-input__icon el-icon-delete" @click.stop="clear('projectName','projectId')"></i>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="会员">
          <el-input
            v-enter="search"
            v-model="searchForm.userName"
            placeholder="输入会员账号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="访问状态">
          <el-select
            v-model="searchForm.visitState"
            clearable
            filterable
            default-first-option>
            <el-option
              v-for="item in visitStateList"
              :key="item.id"
              :value="item.id"
              :label="item.text"
            ></el-option>
          </el-select>
          <el-checkbox style="margin-left: 10px" v-model="searchForm.onlyLastVisitTime">仅查询会员最后一次访问数据</el-checkbox>
        </el-form-item>
        <el-collapse-transition>
          <el-form-item v-if="!isPackUp" label="渠道">
            <el-select
              v-model="searchForm.channelTypeId"
              @change="channelTypeChange"
              clearable
              filterable
              default-first-option>
              <el-option
                v-for="item in channelTypes"
                :key="item.channelTypeId"
                :value="item.channelTypeId"
                :label="item.channelTypeName"
              ></el-option>
            </el-select>
            <el-select
              v-if="searchForm.channelTypeId && ![2,3,5,6].includes(searchForm.channelTypeId)"
              v-model="searchForm.channelId"
              clearable
              filterable
              default-first-option>
              <el-option
                v-for="item in channelNames"
                :label="item['channelName']"
                :value="item['channelId']"
                :key="item['channelId']">
              </el-option>
            </el-select>
          </el-form-item>
        </el-collapse-transition>
        <el-collapse-transition>
          <el-form-item v-if="!isPackUp" label="访问时间">
            <el-date-picker
              v-model="searchForm.visitTimeStart"
              type="datetime"
              placeholder="访问时间起"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="00:00:00"
              clearable>
            </el-date-picker>
            <el-date-picker
              v-model="searchForm.visitTimeEnd"
              type="datetime"
              placeholder="访问时间止"
              default-time="23:59:59"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-collapse-transition>
      </el-form>
      <div class="right">
        <el-button type="primary" size="mini" icon="el-icon-search" plain @click="search">检索</el-button>
        <el-link class="pack-button" :class="{rotate: isPackUp}" :underline="false" @click="isPackUp=!isPackUp">{{isPackUp? '展开': '收起'}} <i class="el-icon-arrow-up"></i></el-link>
      </div>
    </el-header>
    <el-divider></el-divider>
    <project-select :show="showProjectSelect" @close="showProjectSelect=false" @finish="selectFinish"/>
  </el-main>
</template>

<script>
const ProjectSelect = importC('ProjectSelect')
export default {
  name: 'ExhibitionApplyHeader',
  inject: ['visitStateList'],
  mixins: [Mixins.clearForm(), Mixins.requestOperator()],
  data() {
    return {
      searchForm: {
        projectIds: '',
        projectName: '',
        userName: '',
        visitState: '',
        channelTypeId: '',
        channelId: '',
        visitTimeStart: '',
        visitTimeEnd: '',
        onlyLastVisitTime: false,
      },
      searchMap: {
        projectIds: '登记项目',
        userName: '会员',
        visitState: '访问状态',
        channelTypeId: '渠道类型',
        channelId: '渠道名称',
        visitTimeStart: '访问时间起',
        visitTimeEnd: '访问时间止',
        onlyLastVisitTime: '仅查询会员最后一次访问数据',
      },
      channelTypes: [],
      channelNames: [],
      showProjectSelect: false,
      isPackUp: true
    }
  },
  components: {
    ProjectSelect
  },
  mounted() {
    const {projectId, projectName} = extend
    if (projectId && projectName) {
      this.selectFinish({projectId, projectName})
    }
    this.requestOperator()
    this.search(null, true)
  },
  methods: {
    clearAllEx(isNeedRequest) {
      this.clearAll()
      const {projectId, projectName} = extend
      if (projectId && projectName) {
        this.selectFinish({projectId, projectName})
      }
      isNeedRequest && this.search(null, false)
    },
    channelTypeChange(channelTypeId) {
      this.clear('channelId')
      if (!channelTypeId) return
      //如果是主办方
      if (channelTypeId === 1)
        this.channelNames = [].concat(this.operatorList.map(oper => ({channelId: oper['id'], channelName: oper['text']})))
      //如果是自定义渠道类型
      if (![1, 2, 3, 5, 6].includes(channelTypeId)) {
        const postData = {channelTypeId, projectId: this.searchForm.projectId}
        Axios.post('/channel/queryChannelName', JSON2FormData(postData)).then(({data: res}) => {
          this.channelNames = [].concat(res.data)
        })
      }
    },
    selectFinish(row) {
      let {projectId, projectName} = row;
      this.$set(this.searchForm, 'projectIds', projectId);
      this.$set(this.searchForm, 'projectName', projectName);
      this.getChannelTypes(projectId)
    },
    async getChannelTypes(projectId) {
      const postData = projectId ? {projectId} : {}
      postData.actorCode = 'Trader'
      const {data} = await Axios.post('buyerReg/getChannelType', JSON2FormData(postData))
      this.channelTypes = data
    },
    search(_, isFirst) {
      const result = {
        requestData: {},
        breadcrumbList: []
      }
      Object.keys(this.searchForm).forEach(key => {
        const val = this.searchForm[key]
        // 跳过无效值保留 false
        if (typeof val !== 'boolean' && typeof val !== 'number' && !val) return
        let breadcrumb
        if (key in this.searchMap) {
          const searchMapValue = this.searchMap[key]
          let bv = val
          switch (key) {
            case 'projectIds':
              bv = this.searchForm.projectName
              break
            case 'channelTypeId':
              bv = (this.channelTypes.find(it => it.channelTypeId === val) || {}).channelTypeName || ''
              break
            case 'channelId':
              bv = (this.channelNames.find(it => it.channelId === val) || {}).channelName || ''
              break
            case 'visitState':
              bv = (this.visitStateList.find(it => it.id === val) || {}).text || ''
              break
          }
          breadcrumb = bv && `${searchMapValue}: ${bv}`
        }
        breadcrumb && result.breadcrumbList.push(breadcrumb)
        result.requestData[key] = val
      })
      this.$emit('search', result, isFirst && typeof isFirst === 'boolean')
    },
  }
}
</script>

<style scoped>@import "../../../VueCommonComponents/header.css?v=240306";</style>
<style scoped>
.el-date-editor--datetime {
  width: 200px!important;
}
 .w100px {
   width: 100px;
 }
 .w100px .el-input {
   width: 100%!important;
 }
</style>