<template>
  <div v-loading.fullscreen.lock="exportLoading"
       element-loading-text="正在导出..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.1)">
    <booth-layout-header ref="header" @search="caller($refs['body'].search, ...arguments)"></booth-layout-header>
    <booth-layout-body
      ref="body"
      @export="exportTable"
      @batch-add-opportunity="batchAddOpportunity"
      @table-select="tableSelect"
      @clear-search="caller($refs['header'].clearAllEx, ...arguments)"></-apply-body>
  </div>
</template>

<script>
const BoothLayoutHeader = importC('BoothLayoutHeader')
const BoothLayoutBody = importC('BoothLayoutBody')
const XLSXExport = Mixins.XLSXExportEx({
  title: '展位定展访问日志',
  fieldsFunc(self) {
    const fields = self.$refs['body'].fields
    const labelSet = new Set()
    return fields.map(item => {
      let {prop: fFieldName, label: fFieldNameCn} = item
      if (labelSet.has(fFieldNameCn))
        fFieldNameCn += '_1'
      labelSet.add(fFieldNameCn)
      return {fFieldName, fFieldNameCn}
    })
  }
})
export default {
  name: 'BoothLayoutVisits',
  mixins: [Mixins.caller(), XLSXExport],
  provide() {
    return {
      visitStateList: this.visitStateList
    }
  },
  data() {
    return {
      visitStateList: [
        {id: '', text: '全部'},
        {id: '1', text: '访问未订展'},
        {id: '2', text: '订展未成功'},
        {id: '3', text: '已订展'},
      ],
      exportList: [],
      selection: [],
      exportLoading: false,
      all: false,
      workbook: null,
      rowsCont: 10000,
      total: 0,
      page: 1,
      params: {},
    }
  },
  computed: {
    visitStateMap() {
      const result = {}
      this.visitStateList.filter(item => item.id).forEach(item => result[item.id] = item.text)
      return result
    }
  },
  methods: {
    //表格选择
    tableSelect(selection) {
      this.selection = selection
      let propList = this.$refs['body'].fields.map(it => it.prop)
      this.exportList = selection.map((item, index) => {
        let temp = {index: index + 1};
        propList.forEach(prop => {
          temp[prop] = item[prop]
          if (prop === 'visitState') {
            temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
          }
        })
        return temp
      })
    },
    //xlsx格式导出
    getDataloadData() {
      if (this.page > this.pages) return this.downloadXlsx([])
      const propList = this.$refs['body'].fields.map(it => it.prop)
      Axios.post('/boothLayoutVisitsLog/getPage', JSON2FormData(this.params))
        .then(({data}) => {
          this.downloadXlsx(data.rows.map((item, index) => {
            let temp = {index: index + 1};
            propList.forEach(prop => {
              temp[prop] = item[prop]
              if (prop === 'visitState') {
                temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
              }
            })
            return temp
          }))
        })
        .catch(err => {
          this.$errorMsg('导出失败')
          console.warn(err)
        }).finally(() => {
        this.exportLoading = false
      })
    },
    //导出
    exportTable() {
      this.total = this.$refs['body'].total
      this.workbook = XLSX.utils.book_new()
      if (this.exportList.length < 1) {
        return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          .then(() => this.exportAll()).catch(e => e)
      }
      this.all = false
      this.downloadXlsx(this.exportList)
    },
    exportAll() {
      const propList = this.$refs['body'].fields.map(it => it.prop)
      this.exportLoading = true
      const $main = this.$refs['body']
      let data = {...$main.requestHistory}
      this.params = {...$main.requestHistory}
      this.params = Object.assign({}, data, {
        page: this.page,
        rows: this.rowsCont > this.total ? this.total : this.rowsCont,
        exportList: true
      })
      Axios.post('/boothLayoutVisitsLog/getPage', JSON2FormData(this.params))
        .then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('导出数据失败！')
          this.$set(this.params, 'maxId', res.data['maxId'] || '')
          if (!Array.isArray(res.rows) || res.rows.length === 0)
            return this.$errorMsg('没有可导出的数据！')
          this.all = true
          this.downloadXlsx(res.rows.map((item, index) => {
            let temp = {index: index + 1};
            propList.forEach(prop => {
              temp[prop] = item[prop]
              if (prop === 'visitState') {
                temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
              }
            })
            return temp
          }))
          this.exportLoading = false
          this.$message.success('导出成功！')
        })
        .catch(err => {
          console.warn(err)
          this.$errorMsg('导出数据失败！')
        })
        .finally(() => this.exportLoading = false)
    },
    async  batchAddOpportunity() {
      const loading = this.$loading({
        fullscreen: !0,
        text: '正在生成商机...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.1)',
        lock: !0,
        body: !0
      })
      try {
        const post = { workTeamId: teamId }
        let count = this.selection.length
        if (!count) {
          Object.assign(post, this.$refs['body'].requestHistory)
          count = this.$refs['body'].total
        } else {
          post.visitLogIds = this.selection.map(it => it.id)
        }
        await this.$confirm(`即将生成${count}条商机，是否确认`, '提示', {type: 'info'})
        const {data: {data, state, msg}} = await Axios.post('/opportunity/batchSaveExhibitorOpportunity', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg || '数据加载失败')
        const {
          failNum,
          insertNum,
          updateNum,
          existOppNum
        } = data
        const labelMap = {
          failNum: '新增失败 %d 条商机',
          insertNum: '生成 %d 条商机',
          updateNum: '更新 %d 条已有商机描述信息',
          existOppNum: '已存在 %d 条关联商机',
        }
        const msgList = Object.entries({
          failNum,
          insertNum,
          updateNum,
          existOppNum
        }).filter(it => it[1]).map(([k, v]) => `<li>${labelMap[k].replace('%d', v)}</li>`)
        this.$msgbox({
          message: `<div><h4 style="margin:0">批量生成商机完成</h4><ul style="margin:10px 0;padding-left: 20px">${msgList.join('')}<ul></div>`,
          title: '提醒',
          type: 'warning',
          dangerouslyUseHTMLString: true
        })
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e||'操作失败')
      } finally {
        loading.close()
      }
    }
  },
  components: {
    BoothLayoutHeader,
    BoothLayoutBody,
  }
}
</script>