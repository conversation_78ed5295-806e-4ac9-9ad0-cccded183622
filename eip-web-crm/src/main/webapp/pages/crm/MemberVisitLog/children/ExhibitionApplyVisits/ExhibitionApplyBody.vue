<template>
  <el-container style="padding:0 20px;">
    <el-header class="main-body-header" height="30px">
      <div class="left">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            class="clear-search"
            @click.native="$emit('clear-search', true)"
            title="清除检索">当前检索 <span style="color: #409EFF;font-size: 13px;">重置</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="String(Math.random())">{{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" plain @click="$emit('export')">导出</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 190px)"
      border
      :show-operation="false"
      size="small"
      class="alex-table"
      mode="multiple"
      :fields="fields"
      @sort-change="sort"
      :padding="10"
      operation-width="125px"
      :handle-pagination="handlePagination"
      @selection-change="$emit('table-select', $event)"
      :total="total"
      :loading="loading">
      <template slot="visitState" slot-scope="{row}">
        <span style="cursor: unset" :class="['el-link', row.visitState === 2 ? 'el-link--success': 'el-link--info'] ">
          {{ (visitStateList.find(item => +item.id === row.visitState) || {}).text || '访问未登记' }}
        </span>
      </template>
      <template slot="traderId" slot-scope="{row}">
        <el-link
          v-if="row.traderId"
          :underline="false"
          @click.stop="gotoTradeDetails(row)"
          type="primary">查看
        </el-link>
        <span v-else></span>
      </template>
    </alex-table>
  </el-container>
</template>

<script>
export default {
  name: 'ExhibitionApplyBody',
  inject: ['visitStateList'],
  mixins: [Mixins.table()],
  data() {
    return {
      loading: false,
      tableData: [],
      fields: [],
      total: 0,
      breadcrumbList: []
    }
  },
  mounted() {
    this.initEventListener()
  },
  methods: {
    createFields() {
      this.fields = [
        {label: '项目', prop: 'projectName', width: '200'},
        {label: '会员等级', prop: 'memberGradeName', width: '110'},
        {label: '会员', prop: 'userName', width: '150'},
        {label: '公司名称', prop: 'companyName', width: '180'},
        {label: '联系人', prop: 'linkmanName', width: '120'},
        {label: '职位', prop: 'position', width: '110'},
        {label: '联系方式', prop: 'linkmanMobile', width: '150'},
        {label: '访问状态', prop: 'visitState', width: '120'},
        {label: '相关数据', prop: 'traderId', width: '110'},
        {label: '渠道类型', prop: 'channelTypeName', width: '180'},
        {label: '渠道名称', prop: 'channelName', width: '180'},
        {label: '访问时间', prop: 'visitTime', width: '180'},
      ]
    },
    initEventListener() {
      const tradeIframeIdKey = getCurrentIframeId() + '_tradeIframeId'
      ListenMessage(event => {
        try {
          const data = JSON.parse(event.data)
          switch (data.type) {
            case 'notice':
              window.tradeIframeId = data.iframeId
              if (window.tradeIframeId) {
                sessionStorage.setItem(tradeIframeIdKey, window.tradeIframeId)
              }
              break
            case 'refresh':
            case '$LINK-ACTIVE$':
              this.refresh()
              break
          }
        } catch (e) {
          console.log(e)
        }
      })
      window.tradeIframeId = sessionStorage.getItem(tradeIframeIdKey)
    },
    search(data, isFirst) {
      const {requestData = {}, breadcrumbList = []} = data || {}
      const postData = {...requestData, page: 1, rows: 20}
      this.breadcrumbList = breadcrumbList
      isFirst && this.createFields()
      this.requestTableData(postData)
    },
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        const {data} = await Axios.post('/regVisitsZzyUser/getPageByTrader', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject('数据加载失败')
        // 绑定表格分页器
        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = data.total
        this.tableData = data.rows
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    gotoTradeDetails(row) {
      if(!getAuthority('fn_trader_edit')){
        return this.$alert('抱歉，你还没有编辑参展申请的权限', '提示', {type: 'error'})
      }
      const originData = {
        mode: 'edit',
        title: '编辑参展申请： 参展申请ID:' + row.traderId,
        projectId: row.projectId,
        projectName: row.projectName,
        iframeId: getCurrentIframeId(),
        traderId: row.traderId,
        onlySearchTraderId: true
      }
      const dataString = JSON.stringify(Object.assign(originData, {page: 'exhibitor'}))
      sessionStorage.setItem(originData.iframeId, dataString)
      if(window['tradeIframeId']){
        SendMessageByIframeId(window['tradeIframeId'], dataString, false)
      }
      const url = `VisitorRegistration/visitor-registration.html?queryType=exhibitor&menuId=725&teamId=${teamId}&refId=${originData.iframeId}`
      FinalLinkOpen(url, null, true, false, '参展申请')
    }
  }
}
</script>

<style scoped>
.el-main {
  padding-bottom: 0 !important;
}

.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}

.alex-table {
  width: 100%;
  padding-top: 10px;
}

.el-table--small td, .el-table--small th {
  padding: 4px 0;
}

.el-link.el-link--primary {
  color: #2e82e4;
}
</style>