<template>
  <div v-loading.fullscreen.lock="exportLoading"
       element-loading-text="正在导出..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.1)">
    <register-visits-header ref="header" @search="caller($refs['body'].search, ...arguments)"></register-visits-header>
    <register-visits-body
      ref="body"
      @export="exportTable"
      @table-select="tableSelect"
      @clear-search="caller($refs['header'].clearAllEx, ...arguments)"></register-visits-body>
  </div>
</template>

<script>
const RegisterVisitsHeader = importC('RegisterVisitsHeader')
const RegisterVisitsBody = importC('RegisterVisitsBody')
const XLSXExport = Mixins.XLSXExportEx({
  title: '观众登记访问日志',
  fieldsFunc(self) {
    const fields = self.$refs['body'].fields
    const labelSet = new Set()
    return fields.map(item => {
      let {prop: fFieldName, label: fFieldNameCn} = item
      if (labelSet.has(fFieldNameCn))
        fFieldNameCn += '_1'
      labelSet.add(fFieldNameCn)
      return {fFieldName, fFieldNameCn}
    })
  }
})
export default {
  name: 'RegisterVisits',
  mixins: [Mixins.caller(), XLSXExport],
  provide() {
    return {
      visitStateList: this.visitStateList
    }
  },
  data() {
    return {
      visitStateList: [
        {id: '', text: '全部'},
        {id: '1', text: '访问未登记'},
        {id: '2', text: '已登记'},
      ],
      exportList: [],
      exportLoading: false,
      all: false,
      workbook: null,
      rowsCont: 10000,
      total: 0,
      page: 1,
      params: {},
    }
  },
  computed: {
    visitStateMap() {
      const result = {}
      this.visitStateList.filter(item => item.id).forEach(item => result[item.id] = item.text)
      return result
    }
  },
  methods: {
    //表格选择
    tableSelect(selection) {
      let propList = this.$refs['body'].fields.map(it => it.prop)
      this.exportList = selection.map((item, index) => {
        let temp = {index: index + 1};
        propList.forEach(prop => {
          temp[prop] = item[prop]
          if (prop === 'visitState') {
            temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
          }
        })
        return temp
      })
    },
    //xlsx格式导出
    getDataloadData() {
      if (this.page > this.pages) return this.downloadXlsx([])
      const propList = this.$refs['body'].fields.map(it => it.prop)
      Axios.post('/regVisitsZzyUser/getPage', JSON2FormData(this.params))
        .then(({data}) => {
          this.downloadXlsx(data.rows.map((item, index) => {
            let temp = {index: index + 1};
            propList.forEach(prop => {
              temp[prop] = item[prop]
              if (prop === 'visitState') {
                temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
              }
            })
            return temp
          }))
        })
        .catch(err => {
          this.$errorMsg('导出失败')
          console.warn(err)
        }).finally(() => {
        this.exportLoading = false
      })
    },
    //导出
    exportTable() {
      this.total = this.$refs['body'].total
      this.workbook = XLSX.utils.book_new()
      if (this.exportList.length < 1) {
        return this.$confirm('您未选择数据，是否需要导出全部数据', '提示', {type: 'info'})
          .then(() => this.exportAll()).catch(e => e)
      }
      this.all = false
      this.downloadXlsx(this.exportList)
    },
    exportAll() {
      const propList = this.$refs['body'].fields.map(it => it.prop)
      this.exportLoading = true
      const $main = this.$refs['body']
      let data = {...$main.requestHistory}
      this.params = {...$main.requestHistory}
      this.params = Object.assign({}, data, {
        page: this.page,
        rows: this.rowsCont > this.total ? this.total : this.rowsCont,
        exportList: true
      })
      Axios.post('/regVisitsZzyUser/getPage', JSON2FormData(this.params))
        .then(({data: res}) => {
          if (res.state !== 1) return this.$errorMsg('导出数据失败！')
          this.$set(this.params, 'maxId', res.data['maxId'] || '')
          if (!Array.isArray(res.rows) || res.rows.length === 0)
            return this.$errorMsg('没有可导出的数据！')
          this.all = true
          this.downloadXlsx(res.rows.map((item, index) => {
            let temp = {index: index + 1};
            propList.forEach(prop => {
              temp[prop] = item[prop]
              if (prop === 'visitState') {
                temp[prop] = this.visitStateMap[item[prop]] || '访问未登记'
              }
            })
            return temp
          }))
          this.exportLoading = false
          this.$message.success('导出成功！')
        })
        .catch(err => {
          console.warn(err)
          this.$errorMsg('导出数据失败！')
        })
        .finally(() => this.exportLoading = false)
    },
  },
  components: {
    RegisterVisitsHeader,
    RegisterVisitsBody,
  }
}
</script>