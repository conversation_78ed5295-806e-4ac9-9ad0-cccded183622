<template>
  <div>
    <el-tabs
      class="panel-box"
      v-model="activeName"
      tab-position="left"
      type="card"
      :before-leave="beforeLeave"
    >
      <el-tab-pane
        v-for="data in pageMapList"
        :label="data.text"
        :name="data.id"
        :key="data.id"
      >
        <component :is="data.component" :id="data.id"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
const RegisterVisits = importC('RegisterVisits')
const ExhibitionApplyVisits = importC('ExhibitionApplyVisits')
const BoothLayoutVisits = importC('BoothLayoutVisits')
const OnlineViewVisits = importC('OnlineViewVisits')
export default {
  name: "App",
  data() {
    return {
      pageMapList: [],
      activeName: 'null',
      eventMapper: {},
    }
  },
  created() {
    this.pageMapList = [
      {
        id: 'register',
        text: '观众登记访问日志',
        component: 'PlaceHolder',
        authority: true //getAuthority('fu_crm_login_log'),
      },
      {
        id: 'exhibit-apply',
        text: '参展申请访问日志',
        component: 'PlaceHolder',
        authority: true //getAuthority('fu_crm_login_log'),
      },
      {
        id: 'booth-layout',
        text: '展位定展访问日志',
        component: 'PlaceHolder',
        authority: true //getAuthority('fu_crm_login_log'),
      },
      {
        id: 'online-view',
        text: '展商、展品访问日志',
        component: 'PlaceHolder',
        authority: true //getAuthority('fu_crm_login_log'),
      },
    ].filter(v => v.authority)
  },
  mounted() {
    if (!this.pageMapList.length) return this.$errorMsg('权限不足!')
    // 没传正确的值,取有权限的第一个
    const currentPage = this.pageMapList.find(it => queryType === it.id)
    if (!currentPage) return (this.activeName = this.pageMapList[0].id)
    this.activeName = currentPage.id
    console.log(this.activeName)
  },
  provide() {
    return {
      readEventByName: this.readEventByName
    }
  },
  methods: {
    beforeLeave(activeName) {
      const $current = this.pageMapList.find(it => activeName === it.id)
      if (!$current) return false
      if ($current.component !== 'PlaceHolder') {
        sessionStorage.setItem(storageKey, activeName)
        return 'already'
      }
      switch (activeName) {
        case 'register':
          $current.component = 'RegisterVisits'
          break
        case 'exhibit-apply':
          $current.component = 'ExhibitionApplyVisits'
          break
        case 'booth-layout':
          $current.component = 'BoothLayoutVisits'
          break
        case 'online-view':
          $current.component = 'OnlineViewVisits'
          break
      }
      sessionStorage.setItem(storageKey, activeName)
      return 'first'
    },
    readEventByName(name) {
      if (!name) return
      const event = this.eventMapper[name]
      if (!event) return {}
      this.$delete(this.eventMapper, name)
      return event
    },
  },
  components: {
    RegisterVisits,
    ExhibitionApplyVisits,
    BoothLayoutVisits,
    OnlineViewVisits,
    PlaceHolder: {template: '<div style="height: 100vh;display:flex;align-items:center;justify-content:center;color: #aaa">暂无数据</div>'}
  }
}
</script>

<style scoped>

.el-tabs__header.is-left, .el-tabs__header.is-left * {
  border: none !important;
  margin-right: 0;
}

.el-tabs--left .el-tabs__header.is-left {
  width: 238px;
  height: 100vh;
  background-color: #fafafa;
}

.el-tabs__nav.is-left {
  padding-top: 15px;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  /*padding: 0 0 0 30%;*/
  line-height: 40px;
  user-select: none;
  transition: all .3s;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  background: no-repeat 20% center;
  background-size: 16px;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
.el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
  background-color: #e9f1fd;
  color: #2e82e4;
}
</style>