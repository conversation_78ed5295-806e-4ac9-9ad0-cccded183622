// * -----------------------TODO: 在此添加组件路径（相对于导入他的html文件位置）-----------------------
function SetComponentList() {
  return [
    'App.vue?v=240827',
    '../VueCommonComponents/AlexTable.vue',
    '../VueCommonComponents/CustomDialog.vue',
    '../VueCommonComponents/ProjectSelect.vue?v=240109',
    'children/RegisterVisits/RegisterVisits.vue',
    'children/RegisterVisits/RegisterVisitsBody.vue',
    'children/RegisterVisits/RegisterVisitsHeader.vue?v=250218',
    'children/ExhibitionApplyVisits/ExhibitionApplyVisits.vue',
    'children/ExhibitionApplyVisits/ExhibitionApplyBody.vue',
    'children/ExhibitionApplyVisits/ExhibitionApplyHeader.vue?v=250218',
    'children/BoothLayoutVisits/BoothLayoutVisits.vue?v=241017',
    'children/BoothLayoutVisits/BoothLayoutBody.vue?v=241017',
    'children/BoothLayoutVisits/BoothLayoutHeader.vue?v=250218',
    'children/OnlineViewVisits/OnlineViewVisits.vue',
    'children/OnlineViewVisits/OnlineViewBody.vue',
    'children/OnlineViewVisits/OnlineViewHeader.vue?v=250218',
  ]
}


// * -----------------------TODO: 在此添加Vue全局配置-----------------------

function SetVueExtent(Vue) {
  Vue.prototype.$errorMsg = function (msg, title = '错误', type = 'error') {
    return this.$alert(msg, title, {type})
  }
  Vue.prototype.getAuthority = getAuthority
  Vue.prototype.getChildRef = function (key) {
    const ref = this.$refs[key]
    if (!ref) return null
    if (Array.isArray(ref) && ref.length > 0) return ref[0]
    else return ref
  }
  Vue.filter('bool2text', bool => bool === null ? '' : bool ? '是' : '否')
  Vue.prototype.$postBackMessage = async function (type, data) {
    //回传信息
    data = data || {}
    const iframeId = getCurrentIframeId()
    const _message = JSON.stringify({type, iframeId, ...data})
    if (!refId) return
    if (type === 'notice') {
      SendMessageByIframeId(refId, _message, false)
    } else if (type === 'refresh') {
      try {
        await this.$confirm('操作成功！是否立即返回？', '提示', {
          type: 'info',
          confirmButtonText: '确定返回',
          cancelButtonText: '留在当前页'
        })
        //激活指定Id的标签页并发送消息
        SendMessageByIframeId(refId, _message, false)
        activeTabByTabId(refId)
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    }
  }
  Vue.prototype.$bothWayStore = function (val, listKey, setIdKey, listValKey, setKey) {
    if (!val) return
    let current = this[listKey].find(item => item[listValKey] === val)
    if (!current) {
      console.warn(`No object associated with ${val} was found in vm.${listKey}`)
      return
    }
    setKey && this.$set(this[setKey], setIdKey, current[setIdKey])
    return current[setIdKey]
  }
}