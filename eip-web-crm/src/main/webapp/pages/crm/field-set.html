<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>自定义字段</title>
  <link rel="stylesheet" type="text/css" href="../../jquery-easyui-*******/themes/default/easyui.css"/>
  <link rel="stylesheet" type="text/css" href="../../jquery-easyui-*******/themes/icon.css"/>
  <link rel="stylesheet" type="text/css" href="css/client-list.css"/>
  <script src="js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="js/jquery-ui.min.js"></script>
  <script src="js/index.js" type="text/javascript" charset="utf-8"></script>
  <script type="text/javascript" src="../../jquery-easyui-*******/jquery.easyui.min.js"></script>
  <script type="text/javascript" src="../../jquery-easyui-*******/locale/easyui-lang-zh_CN.js"></script>
  <script type="text/javascript" src="common/variable.js?v=241025"></script>
  <script type="text/javascript" src="./CRM_files/Common.js.下载"></script>
  <link href="javascript/listjs/fileinput.min.css" rel="stylesheet">
  <link href="javascript/listjs/index.css" rel="stylesheet">
  <link href="css/client-list-new.css" rel="stylesheet">
  <script src="javascript/listjs/index.js"></script>
  <link href="css/custom-page.css" rel="stylesheet">
  <script type="text/javascript" src="common/ajaxSetup.js"></script>
  <style>
    #client-list-right-show-body{
      margin-left: 260px;
      width: calc(100vw - 280px) !important;
      height: calc(100vh - 60px) !important;
    }
    .custom-page-center{
      height: 90%;
    }
    .custom-page-center .field-set{
      overflow: auto;
    }
    .custom-page-lists li.active > h1,
    .custom-page-lists li:hover > h1{
      color: #2e82e4;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .custom-page-lists ul li span{
      background-color: transparent!important;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .custom-page-lists li:nth-child(1) > span{
      background-image: url('img/field-settings/company.png');
    }
    .custom-page-lists li:nth-child(2) > span{
      background-image: url('img/field-settings/linkman.png');
    }
    .custom-page-lists li:nth-child(3) > span{
      background-image: url('img/field-settings/touch.png');
    }
    .custom-page-lists li:nth-child(4) > span{
      background-image: url('img/field-settings/opportunity.png');
    }
    .custom-page-lists li:nth-child(5) > span{
      background-image: url('img/field-settings/contract.png');
    }
    .custom-page-lists li:nth-child(6) > span{
      background-image: url('img/field-settings/exhLog.png');
    }
    .custom-page-lists li:nth-child(7) > span{
      background-image: url('img/field-settings/member.png');
    }
    .custom-page-lists li:nth-child(8) > span{
      background-image: url('img/field-settings/failureReason.png');
    }
    .custom-page-lists li:nth-child(1).active-li > span,
    .custom-page-lists li:nth-child(1):hover > span{
      background-image: url('img/field-settings/company-active.png');
    }
    .custom-page-lists li:nth-child(2).active-li > span,
    .custom-page-lists li:nth-child(2):hover > span{
      background-image: url('img/field-settings/linkman-active.png');
    }
    .custom-page-lists li:nth-child(3).active-li > span,
    .custom-page-lists li:nth-child(3):hover > span{
      background-image: url('img/field-settings/touch-active.png');
    }
    .custom-page-lists li:nth-child(4).active-li > span,
    .custom-page-lists li:nth-child(4):hover > span{
      background-image: url('img/field-settings/opportunity-active.png');
    }
    .custom-page-lists li:nth-child(5).active-li > span,
    .custom-page-lists li:nth-child(5):hover > span{
      background-image: url('img/field-settings/contract-active.png');
    }
    .custom-page-lists li:nth-child(6).active-li > span,
    .custom-page-lists li:nth-child(6):hover > span{
      background-image: url('img/field-settings/exhLog-active.png');
    }
    .custom-page-lists li:nth-child(7).active-li > span,
    .custom-page-lists li:nth-child(7):hover > span{
      background-image: url('img/field-settings/member-active.png');
    }
    .custom-page-lists li:nth-child(8).active-li > span,
    .custom-page-lists li:nth-child(8):hover > span{
      background-image: url('img/field-settings/failureReason-active.png');
    }
  </style>
</head>
<body>
<div class="all-content" style="overflow: hidden;">
  <!-- 左侧内容 -->
  <div class="client-list-left">
    <div class="custom-page-lists">
      <ul>
        <li class="active-li">
          <span></span>
          <h1 class="active-h1">客户字段设置</h1>
        </li>
        <li>
          <span></span>
          <h1>联系人字段设置</h1>
        </li>
        <li><span></span>
          <h1>联系记录字段设置</h1>
        </li>
        <li><span></span>
          <h1>商机字段设置</h1>
        </li>
        <li><span></span>
          <h1>合同字段设置</h1>
        </li>
        <li><span></span>
          <h1>参展记录设置</h1>
        </li>
        <li><span></span>
          <h1>会员字段设置</h1>
        </li>
        <li><span></span>
          <h1>个人团体邀请设置</h1>
        </li>
      </ul>
    </div>
  </div>
  <!-- 右侧内容 -->
  <div class="client-list-right" id="client-list-right-show-body">
    <div class="custom-page-title">
      <h1>客户字段设置</h1>
    </div>
    <div class="custom-page-center">
      <div class="field-save-div" style="position: sticky;top: 0;background: #fff;z-index: 2;">
        <input type="button" name="" onclick="saveFieldSet()" value="保存"/>
      </div>
      <div class="all-field">
        <div class="field-set">
          <div class="basic-set">
            <div class="basic-field-set fl field-name">
              <h6>基本信息字段 | 启用 | 必输 </h6>
              <ul>
                <li>公司名称
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyName"
                          checked="checked" disabled/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyNameIsNull"
                          checked="checked" disabled/>
                </li>
                <li>公司简称
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyAbbr"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyAbbrIsNull"
                  />
                </li>
                <li>公司英文名称
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyEnglish"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-companyEnglishIsNull"
                  />
                </li>
                <li>公司别名
                  |<input type="checkbox" id="field-set-settings-info-checkbox-clientAlias"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-clientAliasIsNull"
                  />
                </li>
                <li>网址
                  |<input type="checkbox" id="field-set-settings-info-checkbox-website"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-websiteIsNull"
                  />
                </li>
                <li>国家/地区
                  |<input type="checkbox" id="field-set-settings-info-checkbox-country"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-countryIsNull"
                  />
                </li>
                <li>省份
                  |<input type="checkbox" id="field-set-settings-info-checkbox-province"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-provinceIsNull"
                  />
                </li>
                <li>城市
                  |<input type="checkbox" id="field-set-settings-info-checkbox-city"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-cityIsNull"
                  />
                </li>
                <li>区县
                  |<input type="checkbox" id="field-set-settings-info-checkbox-district"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-districtIsNull"
                  />
                </li>
                <li>地址
                  |<input type="checkbox" id="field-set-settings-info-checkbox-address"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-addressIsNull"
                  />
                </li>
                <li>英文地址
                  |<input type="checkbox" id="field-set-settings-info-checkbox-addressEnglish"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-addressEnglishIsNull"
                  />
                </li>
                <li>办公电话
                  |<input type="checkbox" id="field-set-settings-info-checkbox-tel"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-telIsNull"
                  />
                </li>
                <li>传真
                  |<input type="checkbox" id="field-set-settings-info-checkbox-fax"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-faxIsNull"
                  />
                </li>
                <li>邮编
                  |<input type="checkbox" id="field-set-settings-info-checkbox-postcode"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-postcodeIsNull"
                  />
                </li>
                <li>电子邮箱
                  |<input type="checkbox" id="field-set-settings-info-checkbox-email"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-emailIsNull"
                  />
                </li>
                <li>公司简介
                  |<input type="checkbox" id="field-set-settings-info-checkbox-cltBrief"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-cltBriefIsNull"
                  />
                </li>
                <li>备注
                  |<input type="checkbox" id="field-set-settings-info-checkbox-memo"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-info-checkbox-memoIsNull"
                  />
                </li>
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>分类和关联字段 | 启用 | 必输 </h6>
              <ul>
                <li>资源类型
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-sourceType"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-sourceTypeIsNull"
                  />
                </li>
                <li>行业分类
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-tradeId"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-tradeIdIsNull"
                  />
                </li>
                <li>客户来源
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-source"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-sourceIsNull"
                  />
                </li>
                </li>
                <li>客户等级
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-grade"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-gradeIsNull"
                  />
                </li>
                <li>产品类别
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-productType"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-productTypeIsNull"
                  />
                </li>
                <li>主营产品
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-mainProduct"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-mainProductIsNull"
                  />
                </li>
                <li>重点市场
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-keyMarket"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-keyMarketIsNull"
                  />
                </li>
                <li>企业规模
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-companySize"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-companySizeIsNull"
                  />
                </li>
                <li>企业性质
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-nature"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-natureIsNull"
                  />
                </li>
                <li>品牌名称
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-brandName"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-brandNameIsNull"
                  />
                </li>
                <li>区域
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-area"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-areaIsNull"
                  />
                </li>
                <li>上级机构
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-parentClient"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-parentClientIsNull"
                  />
                </li>
                <li>分组
                  |<input type="checkbox" id="field-set-settings-companyClassIf-checkbox-group"
                          checked="checked" disabled/>
                  |<input type="checkbox"
                          id="field-set-settings-companyClassIf-checkbox-groupIsNull"
                  />
                </li>
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>其他信息设置 </h6>
              <ul>
                <li>新增客户必须新增联系人
                  |<input type="checkbox" id="field-set-settings-companyOther-checkbox-linkman"
                  />
                </li>

              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>自定义字段设置 | 启用 | 必输</h6>
              <ul id="field-set-settings-customField">
                <!--  <li>新增客户必须新增联系人
                      |<input type="checkbox" id="field-set-settings-customField-checkbox-linkman"/>
                  </li>-->
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="linkman-set">
            <div class="linkman-field-set fl field-name">
              <h6>联系人字段 | 启用 | 必输 </h6>
              <ul>
                <li>姓名
                  |<input type="checkbox" id="field-set-settings-person-checkbox-personName"
                          checked="checked" disabled/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-personNameIsNull"
                          checked="checked" disabled/>
                </li>
                <li>英文名
                  |<input type="checkbox" id="field-set-settings-person-checkbox-englishName"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-englishNameIsNull"
                  />
                </li>
                <li>工作单位
                  |<input type="checkbox" id="field-set-settings-person-checkbox-companyName"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-companyNameIsNull"
                  />
                </li>
                <li>所属客户
                  |<input type="checkbox" id="field-set-settings-person-checkbox-parentClientName"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-parentClientNameIsNull"
                  />
                </li>
                <li>国家/地区
                  |<input type="checkbox" id="field-set-settings-person-checkbox-country"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-countryIsNull"
                  />
                </li>
                <li>省份
                  |<input type="checkbox" id="field-set-settings-person-checkbox-province"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-provinceIsNull"
                  />
                </li>
                <li>城市
                  |<input type="checkbox" id="field-set-settings-person-checkbox-city"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-cityIsNull"
                  />
                </li>
                <li>区县
                  |<input type="checkbox" id="field-set-settings-person-checkbox-district"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-districtIsNull"
                  />
                </li>
                <li>主联系人
                  |<input type="checkbox" id="field-set-settings-person-checkbox-isMain"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-isMainIsNull"
                  />
                </li>
                <li>部门
                  |<input type="checkbox" id="field-set-settings-person-checkbox-department"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-departmentIsNull"
                  />
                </li>
                <li>职务
                  |<input type="checkbox" id="field-set-settings-person-checkbox-position"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-positionIsNull"
                  />
                </li>
                <li>生日
                  |<input type="checkbox" id="field-set-settings-person-checkbox-birthday"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-birthdayIsNull"
                  />
                </li>
                <li>性别
                  |<input type="checkbox" id="field-set-settings-person-checkbox-sex"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-sexIsNull"
                  />
                </li>
                <li>地址
                  |<input type="checkbox" id="field-set-settings-person-checkbox-address"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-addressIsNull"
                  />
                </li>
                <li>手机1
                  |<input type="checkbox" id="field-set-settings-person-checkbox-mobile"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-mobileIsNull"
                  />
                </li>
                <li>手机2
                  |<input type="checkbox" id="field-set-settings-person-checkbox-mobile2"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-mobile2IsNull"
                  />
                </li>
                <li>家庭电话
                  |<input type="checkbox" id="field-set-settings-person-checkbox-homeTel"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-homeTelIsNull"
                  />
                </li>
                <li>办公电话
                  |<input type="checkbox" id="field-set-settings-person-checkbox-tel"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-telIsNull"
                  />
                </li>
                <li>QQ
                  |<input type="checkbox" id="field-set-settings-person-checkbox-qq"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-qqIsNull"
                  />
                </li>
                <li>微信
                  |<input type="checkbox" id="field-set-settings-person-checkbox-wechat"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-wechatIsNull"
                  />
                </li>
                <li>电子邮箱
                  |<input type="checkbox" id="field-set-settings-person-checkbox-email"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-emailIsNull"
                  />
                </li>
                <li>传真
                  |<input type="checkbox" id="field-set-settings-person-checkbox-fax"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-faxIsNull"
                  />
                </li>
                <li>邮编
                  |<input type="checkbox" id="field-set-settings-person-checkbox-postcode"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-postcodeIsNull"
                  />
                </li>
                <li>skype
                  |<input type="checkbox" id="field-set-settings-person-checkbox-skype"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-skypeIsNull"
                  />
                </li>
                <li>身份证号
                  |<input type="checkbox" id="field-set-settings-person-checkbox-idcardNo"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-idcardNoIsNull"
                  />
                </li>
                <li>个人简介
                  |<input type="checkbox" id="field-set-settings-person-checkbox-cltBrief"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-cltBriefIsNull"
                  />
                </li>
                <li>备注
                  |<input type="checkbox" id="field-set-settings-person-checkbox-memo"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-person-checkbox-memoIsNull"
                  />
                </li>
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>分类和关联字段 | 启用 | 必输 </h6>
              <ul>
                <li>资源类型
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-sourceType"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-personClassif-checkbox-sourceTypeIsNull"
                  />
                </li>
                <li>职业
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-occupation"
                          checked="checked"/>
                  |<input type="checkbox"
                          id="field-set-settings-personClassif-checkbox-occupationIsNull"
                  />
                </li>
                <li>兴趣爱好
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-hobby"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-hobbyIsNull"
                  />
                </li>
                <li>客户等级
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-grade"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-gradeIsNull"
                  />
                </li>
                <li>客户来源
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-source"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-sourceIsNull"
                  />
                </li>
                <li>区域
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-area"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-areaIsNull"
                  />
                </li>
                <li>介绍上家
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-clientName"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-clientNameIsNull"
                  />
                </li>
                <li>分组
                  |<input type="checkbox" id="field-set-settings-personClassif-checkbox-group"
                          checked="checked" disabled/>
                  |<input type="checkbox"
                          id="field-set-settings-personClassif-checkbox-groupIsNull"
                  />
                </li>
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>自定义字段设置 | 启用 | 必输</h6>
              <ul id="field-set-settings-personal-customField">
                <!--  <li>新增客户必须新增联系人
                      |<input type="checkbox" id="field-set-settings-customField-checkbox-linkman"/>
                  </li>-->
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>身份信息设置 | 启用 | 必输</h6>
              <ul id="field-set-settings-personal-personIdInfo">
                <!-- @GOTO: Function >> loadPersonIdInfo() -->
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>联系记录字段 | 启用 | 必输</h6>
              <ul>
                <li>联系时间
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchTime"
                          checked="checked" disabled/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchTimeIsNull"
                          checked="checked" disabled/>
                </li>
                <li>联系类型
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchTypeId"
                          checked="checked" disabled/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchTypeIdIsNull"
                          checked="checked" disabled/>
                </li>
                <li>联系主题
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-subject"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-subjectIsNull"
                          checked="checked"/>
                </li>
                <li>联系人
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-clientId"
                          checked="checked" disabled/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-clientIdIsNull"
                  />
                </li>
                <li>联系方式
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-contactWayId"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-contactWayIdIsNull"
                  />
                </li>
                <li>联系结果
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-state"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-stateIsNull"
                  />
                </li>
                <li>联系记录
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchNote"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-touchNoteIsNull"
                  />
                </li>
                <li>附件上传
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-attachment"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-attachmentIsNull"
                          name="attachment"/>
                </li>
                <li style="display: none">需要跟进提醒
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-isNeedReminder"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-isNeedReminderIsNull"
                          checked="checked"/>
                </li>
                <li style="display: none">联系之前状态
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-lastStateId"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-lastStateIdIsNull"
                          checked="checked"/>
                </li>
                <li style="display: none">联系之后状态
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-nowStateId"
                          checked="checked"/>
                  |<input type="checkbox" id="field-set-settings-touch-checkbox-nowStateIdIsNull"
                          checked="checked"/>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>基本信息字段 | 启用 | 必输</h6>
              <ul id="oppBasicField"></ul>
            </div>
          </div>
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>处理状态字段 | 启用 | 必输</h6>
              <ul id="oppStateField"></ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>基本信息字段 | 启用 | 必输</h6>
              <ul id="contractBasicField"></ul>
            </div>
          </div>
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>
                <input type="checkbox" checked onchange="hideChildren(this)" id="field-set-settings-fo_contract_field_set-checkbox-linkManInfo" />
                联系人信息字段 | 启用 | 必输
              </h6>
              <ul id="contractLinkmanField"></ul>
            </div>
          </div>
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>
                <input type="checkbox" checked onchange="hideChildren(this)" id="field-set-settings-fo_contract_field_set-checkbox-boothNumInfo" />
                展位信息字段 | 启用 | 必输
              </h6>
              <ul id="contractBoothField"></ul>
            </div>
          </div>
          <div class="relation-record-set">
            <div class="record-field-set fl field-name">
              <h6>其他信息设置</h6>
              <ul id="contract-other-field">
                <li>
                  合同编号自动生成|
                  <input
                    type="checkbox"
                    id="field-set-settings-fo_contract_field_set-checkbox-autoGen_contractCode" />
                </li>
                <li>
                  收款账户|
                  <input
                    type="checkbox"
                    id="field-set-settings-fo_contract_field_set-checkbox-accountId" />
                </li>
                <li>
                  纸质合同归档|
                  <input
                    type="checkbox"
                    id="field-set-settings-fo_contract_field_set-checkbox-paperContractState" />
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="booth-set">
            <div class="booth-field-set fl field-name">
              <h6>参展详情设置 </h6>
              <ul id="boothBasicField">
                <!-- <li>
                  启用展位需求
                  |<input
                    type="checkbox"
                    id="field-set-settings-fo_serve_booth_field_set-checkbox-enableBoothDemand" />
                </li> -->
                <li>
                  启用参展人员
                  |<input
                    type="checkbox"
                    id="field-set-settings-fo_serve_booth_field_set-checkbox-enableExhibitorDemand" />
                </li>
              </ul>
            </div>
          </div>
          <div class="classify-set">
            <div class="classify-field-set fl field-name">
              <h6>
                参展记录移交客服必填字段
              </h6>
              <ul id="field-set-settings-fo_serve_booth_field_set-requireField">
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="member-set">
            <div class="member-base-field-set fl field-name">
              <h6>会员个人信息 | 启用 | 必输</h6>
              <ul id="member-base-field"></ul>
            </div>
          </div>
          <div class="member-company-set">
            <div class="member-company-field-set fl field-name">
              <h6>会员公司信息 | 启用 | 必输</h6>
              <ul id="member-company-field">
              </ul>
            </div>
          </div>
        </div>
        <div class="field-set" style="display: none;">
          <div class="reason-set">
            <div class="reason-base-field-set fl field-name">
              <h6>邀请记录必填字段</h6>
              <ul>
                <li>失败原因 | <input type="checkbox" id="field-set-settings-failReason-checkbox-failureReasonIsNull"/></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</body>
<script type="text/javascript">
  let teamId = getQueryString("teamId");

  function saveFieldSet() {
    saveOrdinaryFieldSet();
    saveCustomizeFieldSet();
    $.messager.alert('提示', '保存成功！', 'info');
  }

  /**
   * 保存普通字段
   */
  function saveOrdinaryFieldSet() {
    var value;
    var postData = {};
    var orgnum = localStorage.getItem("orgnum"),field,pre;
    //公司基本信息是否启用
    var param = ['companyName', 'companyAbbr', 'companyEnglish', 'clientAlias', 'website', 'country',
      'province', 'city', 'district', 'address', 'addressEnglish',
      'tel', 'fax', 'postcode', 'email', 'cltBrief', 'memo',
    ]; //17
    for (let i = 0; i < param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'companyInfo';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-info-checkbox-' + param[i] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-info-checkbox-' + param[i] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    var currentPos = 18;
    //公司分类和关联字段
    param = ['sourceType', 'tradeId', 'source', 'grade', 'productType', 'mainProduct', 'keyMarket',
      'companySize', 'nature', 'brandName', 'area', 'parentClient', 'group']; //13
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'companyClassIf';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-companyClassIf-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-companyClassIf-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    //联系人基本信息
    param = ['personName', 'englishName', 'companyName', 'parentClientName', 'country', 'province', 'city', 'district', 'isMain', 'department', 'position',
      'birthday', 'sex', 'address', 'mobile', 'mobile2', 'homeTel', 'tel', 'qq', 'wechat', 'email',
      'fax', 'postcode', 'skype', 'idcardNo', 'cltBrief', 'memo']; //26
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'person';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-person-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-person-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    //联系人分类和关联字段
    param = ['sourceType', 'occupation', 'hobby', 'grade', 'source', 'area', 'clientName', 'group']; //8
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'personClassif';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-personClassif-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-personClassif-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    //联系记录
    param = ['touchTime', 'subject', 'touchTypeId', 'clientId', 'contactWayId', 'state', 'touchNote', 'attachment', 'isNeedReminder', 'lastStateId', 'nowStateId']; //11
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'touch';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-touch-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-touch-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }

    currentPos += param.length
    //公司客户 其他信息
    param = ['linkman']; //1
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'companyOther';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      //value = $('#field-set-settings-companyOther-checkbox-' + param[i-currentPos] + 'IsNull').prop('checked');
      // if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      //  else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-companyOther-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    param = window.PersonIdInfoFields
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'personIdInfo';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-personIdInfo-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-personIdInfo-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    param = window.oppFields
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'fo_opportunity_field_set';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-fo_opportunity_field_set-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-fo_opportunity_field_set-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    currentPos += param.length
    param = window.contractFields
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'fo_contract_field_set';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      value = $('#field-set-settings-fo_contract_field_set-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].isNull'] = '1';
      else postData['exhibitSettings[' + i + '].isNull'] = '0';
      value = $('#field-set-settings-fo_contract_field_set-checkbox-' + param[i - currentPos] + '').prop('checked');
      if (value) postData['exhibitSettings[' + i + '].value'] = '1';
      else postData['exhibitSettings[' + i + '].value'] = '0';
    }
    //单独添加一个自动生成的标识
    currentPos++
    postData['exhibitSettings[' + currentPos + '].orgNum'] = localStorage.getItem("orgnum");
    postData['exhibitSettings[' + currentPos + '].taskKindCode'] = 'fo_contract_field_set';
    postData['exhibitSettings[' + currentPos + '].paramKind'] = 'show';
    postData['exhibitSettings[' + currentPos + '].param'] = 'autoGen_contractCode'
    postData['exhibitSettings[' + currentPos + '].paramMemo'] = '是否启用';
    postData['exhibitSettings[' + currentPos + '].teamId'] = teamId;
    value = $('#field-set-settings-fo_contract_field_set-checkbox-autoGen_contractCode').prop('checked');
    if (value){
      postData['exhibitSettings[' + currentPos + '].isNull'] = '1'
      postData['exhibitSettings[' + currentPos + '].value'] = '1'
    }
    else{
      postData['exhibitSettings[' + currentPos + '].isNull'] = '0'
      postData['exhibitSettings[' + currentPos + '].value'] = '0'
    }
    //单独添加一个收款账户的标识
    currentPos++
    postData['exhibitSettings[' + currentPos + '].orgNum'] = localStorage.getItem("orgnum");
    postData['exhibitSettings[' + currentPos + '].taskKindCode'] = 'fo_contract_field_set';
    postData['exhibitSettings[' + currentPos + '].paramKind'] = 'show';
    postData['exhibitSettings[' + currentPos + '].param'] = 'accountId'
    postData['exhibitSettings[' + currentPos + '].paramMemo'] = '是否启用';
    postData['exhibitSettings[' + currentPos + '].teamId'] = teamId;
    value = $('#field-set-settings-fo_contract_field_set-checkbox-accountId').prop('checked');
    if (value){
      postData['exhibitSettings[' + currentPos + '].isNull'] = '1'
      postData['exhibitSettings[' + currentPos + '].value'] = '1'
    }
    else{
      postData['exhibitSettings[' + currentPos + '].isNull'] = '0'
      postData['exhibitSettings[' + currentPos + '].value'] = '0'
    }
    //单独添加一个纸质合同归档的标识
    currentPos++
    postData['exhibitSettings[' + currentPos + '].orgNum'] = localStorage.getItem("orgnum");
    postData['exhibitSettings[' + currentPos + '].taskKindCode'] = 'fo_contract_field_set';
    postData['exhibitSettings[' + currentPos + '].paramKind'] = 'show';
    postData['exhibitSettings[' + currentPos + '].param'] = 'paperContractState'
    postData['exhibitSettings[' + currentPos + '].paramMemo'] = '是否启用';
    postData['exhibitSettings[' + currentPos + '].teamId'] = teamId;
    value = $('#field-set-settings-fo_contract_field_set-checkbox-paperContractState').prop('checked');
    if (value){
      postData['exhibitSettings[' + currentPos + '].isNull'] = '1'
      postData['exhibitSettings[' + currentPos + '].value'] = '1'
    }
    else{
      postData['exhibitSettings[' + currentPos + '].isNull'] = '0'
      postData['exhibitSettings[' + currentPos + '].value'] = '0'
    }
    // 参展记录设置
    currentPos += param.length;
    param = window.BoothRequireFields;
    for (let i = currentPos; i < currentPos + param.length; i++) {
      postData['exhibitSettings[' + i + '].orgNum'] = localStorage.getItem("orgnum");
      postData['exhibitSettings[' + i + '].taskKindCode'] = 'fo_serve_booth_field_set';
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = param[i - currentPos];
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      if(['enableBoothDemand','enableExhibitorDemand'].includes(param[i - currentPos])) {
        value = $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + param[i - currentPos]).prop('checked');
        postData['exhibitSettings[' + i + '].value'] = value ? '1' : '0';
        postData['exhibitSettings[' + i + '].isNull'] = '0';
      } else {
        postData['exhibitSettings[' + i + '].value'] = '1';
        value = $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + param[i - currentPos] + 'IsNull').prop('checked');
        postData['exhibitSettings[' + i + '].isNull'] = value ? '1' : '0';
      }
    }
    // 会员设置
    currentPos += param.length;
    param = window.memberFields.base || [];
    pre = 'fo_zzyuser_info_field_set';
    for (let i = currentPos; i < currentPos + param.length; i++) {
      field = param[i - currentPos];
      postData['exhibitSettings[' + i + '].orgNum'] = orgnum;
      postData['exhibitSettings[' + i + '].taskKindCode'] = pre;
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = field;
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      postData['exhibitSettings[' + i + '].value'] = getCheckValue(pre,field) ? '1' : '0';
      postData['exhibitSettings[' + i + '].isNull'] = getCheckValue(pre,field,true) ? '1' : '0';
    }
    currentPos += param.length;
    param = window.memberFields.company || [];
    pre = 'fo_zzyuser_company_info_field_set';
    for (let i = currentPos; i < currentPos + param.length; i++) {
      field = param[i - currentPos];
      postData['exhibitSettings[' + i + '].orgNum'] = orgnum;
      postData['exhibitSettings[' + i + '].taskKindCode'] = pre;
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = field;
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      postData['exhibitSettings[' + i + '].value'] = getCheckValue(pre,field) ? '1' : '0';
      postData['exhibitSettings[' + i + '].isNull'] = getCheckValue(pre,field,true) ? '1' : '0';
    }
    currentPos += param.length;
    param = ['failureReason']
    pre = 'failReason';
    for (let i = currentPos; i < currentPos + param.length; i++) {
      field = param[i - currentPos];
      postData['exhibitSettings[' + i + '].orgNum'] = orgnum;
      postData['exhibitSettings[' + i + '].taskKindCode'] = pre;
      postData['exhibitSettings[' + i + '].paramKind'] = 'show';
      postData['exhibitSettings[' + i + '].param'] = field;
      postData['exhibitSettings[' + i + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + i + '].teamId'] = teamId;
      postData['exhibitSettings[' + i + '].value'] = '1';
      postData['exhibitSettings[' + i + '].isNull'] = getCheckValue(pre,field,true) ? '1' : '0';
    }
    saveData(postData, true);
  }

  function getCheckValue(pre ='', field='', isNull = false) {
    return $(`#field-set-settings-${pre}-checkbox-${field}${isNull ? 'IsNull' : ''}`).prop('checked')
  }
  /**
   * 保存自定义字段
   */
  function saveCustomizeFieldSet() {
    var postData = {};
    let index = 0;
    //公司客户自定义字段
    $('#field-set-settings-customField li').each(function (i) {
      let fieldname = $(this).attr("fieldname");
      postData['exhibitSettings[' + index + '].taskKindCode'] = 'fo_client_com';
      postData['exhibitSettings[' + index + '].paramKind'] = 'show';
      postData['exhibitSettings[' + index + '].param'] = fieldname;
      postData['exhibitSettings[' + index + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + index + '].teamId'] = teamId;
      postData['exhibitSettings[' + index + '].value'] = $('#field-set-settings-customField-checkbox-' + fieldname + '').prop('checked') ? 1 : 0;
      postData['exhibitSettings[' + index + '].isNull'] = $('#field-set-settings-customField-checkbox-' + fieldname + 'IsNull').prop('checked') ? 1 : 0;
      index++;
    });
    //联系人自定义字段
    $('#field-set-settings-personal-customField li').each(function (i) {
      let fieldname = $(this).attr("fieldname");
      postData['exhibitSettings[' + index + '].taskKindCode'] = 'fo_client_per';
      postData['exhibitSettings[' + index + '].paramKind'] = 'show';
      postData['exhibitSettings[' + index + '].param'] = fieldname;
      postData['exhibitSettings[' + index + '].paramMemo'] = '是否启用';
      postData['exhibitSettings[' + index + '].teamId'] = teamId;
      postData['exhibitSettings[' + index + '].value'] = $('#field-set-settings-customField-checkbox-' + fieldname + '').prop('checked') ? 1 : 0;
      postData['exhibitSettings[' + index + '].isNull'] = $('#field-set-settings-customField-checkbox-' + fieldname + 'IsNull').prop('checked') ? 1 : 0;
      index++;
    });
    //公司基本信息是否启用
    // console.log(postData);
    saveData(postData);
  }

  function saveData(postData, flag) {
    if (postData == null) return false;
    $.ajax({
      url: variableSponsor + '/exhibition/saveByCrmExhibitSetting',
      dataType: "json",
      type: "post",
      data: postData,
      success: function (data) {
        if (flag) showSetting();
      }
    });
  }

  /**
   * 查询是否勾选
   */
  function showSetting(taskKindCode) {
    $.ajax({
      url: variableSponsor + '/exhibition/selectExhibitSetting',
      dataType: "json", //返回数据类型
      type: "post",
      data: {taskKindCode, teamId},
      success: function (data) {
        for (var i = 0; i < data.length; i++) {
          let newVar = data[i].taskKindCode;
          if (newVar === 'companyInfo') { //公司基本信息字段
            if (data[i].param == 'companyName') continue;
            if (data[i].value === '0') {
              $('#field-set-settings-info-checkbox-' + data[i].param).prop("checked", false);
            }
            $('#field-set-settings-info-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          } else if (newVar === 'companyClassIf') { //分类和关联字段
            if (data[i].value === '0') {
              if (data[i].param == 'group') continue;
              $('#field-set-settings-companyClassIf-checkbox-' + data[i].param).prop("checked", false);
            }
            $('#field-set-settings-companyClassIf-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          } else if (newVar === 'person') { //联系人基本信息字段
            if (data[i].param == 'personName') continue;
            //console.log(data[i].param +"-----"+data[i].value +"------"+data[i].isNull);
            if (data[i].value === '0') {
              $('#field-set-settings-person-checkbox-' + data[i].param).prop("checked", false);
            }
            $('#field-set-settings-person-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          } else if (newVar === 'personClassif') { //联系人分类和关联字段
            if (data[i].value === '0') {
              if (data[i].param == 'group') continue;
              $('#field-set-settings-personClassif-checkbox-' + data[i].param).prop("checked", false);
            }
            $('#field-set-settings-personClassif-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          } else if (newVar === 'touch') { //联系记录
            if (data[i].param == 'touchTime' || data[i].param == 'touchTypeId') continue;
            if (data[i].value === '0' && data[i].param != 'clientId') {
              $('#field-set-settings-touch-checkbox-' + data[i].param).prop("checked", false);
            }
            $('#field-set-settings-touch-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          } else if (newVar === 'companyOther') { //公司客户其他信息
            if (data[i].value === '1') {
              $('#field-set-settings-companyOther-checkbox-' + data[i].param).prop("checked", true);
            }
          } else if (newVar === 'fo_client_com' || newVar === 'fo_client_per') { //公司、个人客户自定义信息
            $('#field-set-settings-customField-checkbox-' + data[i].param).prop("checked", data[i].value == '1');
            $('#field-set-settings-customField-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          }
          else if (newVar === 'personIdInfo') { //公司、个人客户自定义信息
            $('#field-set-settings-personIdInfo-checkbox-' + data[i].param).prop("checked", data[i].value == '1');
            $('#field-set-settings-personIdInfo-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          }
          else if (newVar === 'fo_opportunity_field_set') { //商机字段的设置
            $('#field-set-settings-fo_opportunity_field_set-checkbox-' + data[i].param).prop("checked", data[i].value == '1');
            $('#field-set-settings-fo_opportunity_field_set-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
          }
          else if (newVar === 'fo_contract_field_set') { //合同字段的设置
            $('#field-set-settings-fo_contract_field_set-checkbox-' + data[i].param).prop("checked", data[i].value == '1');
            $('#field-set-settings-fo_contract_field_set-checkbox-' + data[i].param + 'IsNull').prop("checked", data[i].isNull);
            if(data[i].param === 'linkManInfo' || data[i].param === 'boothNumInfo'){
              // 是否显示下面的字段
              hideChildren('#field-set-settings-fo_contract_field_set-checkbox-' + data[i].param)
            }
          }
          // 参展记录的设置
          else if (newVar === 'fo_serve_booth_field_set') { //合同字段的设置
            if(['enableBoothDemand','enableExhibitorDemand'].includes(data[i].param)) {
              $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + data[i].param).prop("checked", data[i].value == '1');
              $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + data[i].param + 'IsNull').prop("checked", false);
            } else {
              $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + data[i].param).prop("checked", true);
              $('#field-set-settings-fo_serve_booth_field_set-checkbox-' + data[i].param + 'IsNull').prop("checked", !!data[i].isNull);
            }
          }// 会员设置
          else if (['fo_zzyuser_info_field_set','fo_zzyuser_company_info_field_set'].includes(newVar)) {
            $(`#field-set-settings-${newVar}-checkbox-${data[i].param}`).prop("checked", data[i].value == '1');
            $(`#field-set-settings-${newVar}-checkbox-${data[i].param}IsNull`).prop("checked", !!data[i].isNull);
          }// 个人团体邀请设置
          else if (newVar === 'failReason') {
            $(`#field-set-settings-${newVar}-checkbox-${data[i].param}IsNull`).prop("checked", !!data[i].isNull);
          }
        }
        disabledBut();
        // 调整合同编号自动生成的规则
        const $contractCodeIsNull = $('#field-set-settings-fo_contract_field_set-checkbox-contractCodeIsNull')
        const $autoGen_contractCode = $('#field-set-settings-fo_contract_field_set-checkbox-autoGen_contractCode')
        $autoGen_contractCode.change(function(){
          const checked = $(this).prop('checked')
          checked && $contractCodeIsNull.prop('checked', false)
          $contractCodeIsNull.attr('disabled', checked)
        }).change()
        $contractCodeIsNull.change(function(){
          const checked = $(this).prop('checked')
          checked && $autoGen_contractCode.prop('checked', false)
          $autoGen_contractCode.attr('disabled', checked)
        })
      }
    });
  }

  window.onload = function () {
    //联系人详情页的身份信息
    loadPersonIdInfo()
    loadOpportunityInfo()
    loadBoothRecord(loadContractInfo)
    loadMemberInfo()
    // showSetting("companyInfo")
    // showSetting("companyClassIf")
    // showSetting("person")
    // showSetting("personClassif")
    // showSetting("touch")
    // showSetting("companyOther")
    //公司自定义字段
    // showSetting("fo_client_com")
    showSetting()
    //公司客户
    loadCustomizedFieldSetting('fo_client_com')
    //个人客户
    loadCustomizedFieldSetting('fo_client_per', 1)
  }
  // 切换
  $(".custom-page-lists ul li").each(function (i) {
    var index = $(".custom-page-lists ul li").index(this);
    $(this).on("click", function () {
      $(this).addClass("active-li").siblings("li").removeClass("active-li");
      $(this).children("h1").addClass("active-h1");
      $(this).siblings("li").children("h1").removeClass("active-h1");
      $(".field-set").eq(index).fadeIn().siblings(".field-set").hide()
      $('.custom-page-title h1').text($(".custom-page-lists ul li.active-li h1").text())
    })
  })

  function disabledBut() {
    //选中
    $(".field-name ul li").each(function (i) {
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-info-checkbox-companyName') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-person-checkbox-personName') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-touch-checkbox-touchTime') {
        return true;
      }
      /* if ( $(this).children("input:checkbox").attr("id")=='field-set-settings-touch-checkbox-subject'){
          return true;
      } */
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-touch-checkbox-touchTypeId') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-companyClassIf-checkbox-group') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-personClassIf-checkbox-group') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-fo_opportunity_field_set-checkbox-workTeamId') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-fo_opportunity_field_set-checkbox-projectName') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-fo_opportunity_field_set-checkbox-actorCode') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-fo_contract_field_set-checkbox-clientName') {
        return true;
      }
      if ($(this).children("input:checkbox").attr("id") == 'field-set-settings-fo_contract_field_set-checkbox-projectName') {
        return true;
      }
      let jQuery = $(this).children("input:checkbox").eq(0);
      checkIsChecked(jQuery)
      jQuery.on("click", function () {
        checkIsChecked(this)
      })
    })
  }

  function checkIsChecked(obj) {
    let isChecked = $(obj).prop('checked');
    let next = $(obj).next();
    if (!isChecked) {
      next.prop("checked", false)
      next.attr("disabled", true)
    } else {
      //附件上传的必输不能取消
      if (next.attr("name") != 'attachment') next.attr("disabled", false)
    }
  }

  /**
   * 加载自定义字段
   * @param formName
   * @param type
   */
  function loadCustomizedFieldSetting(formName, type) {
    $.ajax({
      url: variableSponsor + '/customizedField/getAll',
      dataType: "json",
      type: "post",
      data: {f_form_name: formName},
      success: function (data) {
        var html = "";
        f_customizedField_data = data;
        for (var i = 0; i < f_customizedField_data.length; i++) {
          let fCustomizedFieldDatum = f_customizedField_data[i];
          html += '<li fieldName="' + fCustomizedFieldDatum.f_field_name + '" formName="' + fCustomizedFieldDatum.f_form_name + '">'
            + fCustomizedFieldDatum.f_field_label +
            '    |<input type="checkbox" fieldName="' + fCustomizedFieldDatum.f_field_name + '" formName="' + fCustomizedFieldDatum.f_form_name + '"' +
            ' id="field-set-settings-customField-checkbox-' + fCustomizedFieldDatum.f_field_name + '" checked="checked" />' +
            '|<input type="checkbox" fieldName="' + fCustomizedFieldDatum.f_field_name + '"  formName="' + fCustomizedFieldDatum.f_form_name + '" ' +
            'id="field-set-settings-customField-checkbox-' + fCustomizedFieldDatum.f_field_name + 'IsNull" />' +
            ' </li>';
        }
        if (type == 1) {
          $('#field-set-settings-personal-customField').html(html);
          //公司自定义字段启用必输
          showSetting("fo_client_com")
        } else {
          $('#field-set-settings-customField').html(html);
          showSetting("fo_client_per")
        }
      }
    });
  }

  //加载联系人详情页的身份信息
  function loadPersonIdInfo() {
    const fields = {
      personName: '证件姓名',
      idType: '证件类型',
      idNum: '证件号码',
      vaildDate: '有效日期',
      companyTitle: '公司头衔',
      gusetType: '嘉宾类型',
      goodField: '擅长领域',
      costRefer: '出场费参考',
      receptionRefer: '接待参考',
      goodStyle: '擅长风格',
      tag1: '标签1',
      tag2: '标签2',
    }
    $('#field-set-settings-personal-personIdInfo').html(dynamicGenerateField(fields, 'personIdInfo'))
    window.PersonIdInfoFields = [...Object.keys(fields)]
  }
  // 加载参展记录字段
  let isFullProcess = false;
  function loadBoothRecord(callback) {
    // 是否全流程
    $.ajax({
      url: variableSponsor + '/sysSettingOrg/selectProjectFlow',
      async: false,
      type: 'post',
      success(data) {
        if(!data || data.state !==1) {
          $.messager.alert('错误','查询系统流程设置失败');
          throw new Error('查询系统流程设置失败');
        }
        isFullProcess = data.data === 'fullProcess';
        callback && callback()
      },
      error(e){
        $.messager.alert('错误','查询系统流程设置失败');
        throw new Error('查询系统流程设置失败');
      }
    })
    // 参展记录移交客服必填字段
    const boothRequireField = {
      companyNameAbbr: '公司简称',
      companyEnName: '公司客户英文名',
      //
      address: '中文地址',
      addressEn: '英文地址',
      linkman: '联系人',
      position: '职务',
      linkmanTel: '电话',
      linkmanMobile: '手机',
      linkmanWebsite: '网址',
      linkmanEmail: 'EMAIL',
      // sectionCode: '展馆名称',
      boothNum: '展位号',
      // boothArea: '面积',
      // boothSpec: '展位规格',
      // openInfoId: '开口情况',
      exhibitProductRange: '参展范围',
      // boothTypeCode: '展位类型',
      memo: '备注',
    }
    const allKeys = Object.keys(boothRequireField);
    $('#field-set-settings-fo_serve_booth_field_set-requireField').html(dynamicGenerateField(boothRequireField, 'fo_serve_booth_field_set', allKeys,'','', BUILD_TYPE.ONLY_ISNULL));
    if(isFullProcess) {
      'sectionCode,boothArea,boothSpec,openInfoId,boothTypeCode'.split(',').map(it =>
        $('#field-set-settings-fo_serve_booth_field_set-checkbox-'+ it).closest('li').hide()
        )
    }
    window.BoothRequireFields = ['enableBoothDemand','enableExhibitorDemand',...allKeys]
  }

  //加载会员字段
  function loadMemberInfo() {
    const baseField = {
      fullName:'联系人名字',
      // position:'职务',
      // mobile:'手机号',
      // email:'邮箱',
      age:'年龄',
      sex:'性别',
      // wechat:'微信号',
      // qq:'qq',
      birthday:'出生日期',
      occupation:'工作领域',
      hobby:'兴趣领域',
      idCard:'身份证号',
      // address:'地址',
    }
    const companyField = {
      companyName: '公司名称',
      companyNameEn: '公司名称(英文)',
      companyNameAbbr: '公司简称',
      companyNameAbbrEn: '公司简称(英文)',
      nature: '企业类型',
      customerGroupList: '客户群体',
      keyMarketList: '重点市场',
      companyTel: '联系电话 ',
      companyEmail: '公司邮箱 ',
      companyFax: '传真 ',
      tradeAndProductTypeList: '分类',
      companySite: '公司网址 ',
      country: '国家',
      province: '省份',
      city: '城市',
      district: '区县',
      address: '公司地址',
      socialReditCode: '社会信用代码 ',
      companyLogo: '公司Logo ',
      companyPic: '公司图片 ',
      businessLicense: '营业执照 ',
      companyProfile: '公司简介',
      companyProfileEn: '公司简介(英文)',
    }
    $('#member-base-field').html(dynamicGenerateField(baseField, 'fo_zzyuser_info_field_set'))
    $('#member-company-field').html(dynamicGenerateField(companyField, 'fo_zzyuser_company_info_field_set'))
    window.memberFields = {
      base: [...Object.keys(baseField)],
      company: [...Object.keys(companyField)],
    }
  }

  //加载合同字段
  function loadContractInfo() {
    const contractBasicField = {
      clientName: '公司名称',
      projectName: '项目名称',
      contractCode: '合同编号',
      contractDtlList: '产品明细',
      attractType: '招展类型',
      clientRelations: '客户关系',
      purchaserArea: '采购区域分布',
      accessory: '附件'
    }
    //linkManInfo: '联系人信息字段'
    const contractLinkmanField = {
      linkmanPersonName: '联系人',
      linkmanPosition: '职务',
      linkmanEmail: '邮箱',
      linkmanTel: '电话',
      linkmanWebsite: '网址',
      linkmanAddress: '地址',
      mobile: '手机号',
      linkmanFax: '传真'
    }
    //boothNumInfo: '展位信息字段'
    const contractBoothField = {
      section: '展馆',
      boothNum: '展位号',
      exhibitsName: '展品范围',
      boothType: '展位类型',
      boothArea: '展位面积',
      boothCount: '展位数量',
      boothSpec: '展位规格',
      openInfo: '开口情况',
      designer: '设计师'
    }
    $('#contractBasicField').html(dynamicGenerateField(contractBasicField, 'fo_contract_field_set', ['clientName', 'projectName', 'contractDtlList'], ['clientName', 'projectName', 'contractDtlList'], ['clientName', 'projectName']))

    // 附件移动到其他信息设置
    $('#contractBasicField li:last-child').appendTo($('#contract-other-field'))

    $('#contractLinkmanField').html(dynamicGenerateField(contractLinkmanField, 'fo_contract_field_set'))
    $('#contractBoothField').html(dynamicGenerateField(contractBoothField, 'fo_contract_field_set'))
    if (isFullProcess) {
      Object.keys(contractBoothField).filter(k => k !== 'boothNum' && k !== 'exhibitsName' && k !== 'designer').forEach(k => {
        $('#field-set-settings-fo_contract_field_set-checkbox-' + k).closest('li').hide()
      })
    }
    window.contractFields = [
      ...Object.keys(contractBasicField),
      ...Object.keys(contractLinkmanField),
      ...Object.keys(contractBoothField),
      'linkManInfo', 'boothNumInfo'
    ]
  }

  //加载商机字段
  function loadOpportunityInfo(){
    const oppBasicField = {
      oppTheme: '商机主题',
      source: '商机来源',
      oppConent: '商机描述',
      projectName: '关联项目',
      clientName: '客户名称',
      actorCode: '业务类型',
      linkmanId: '联系人',
    }
    const oppStateField = {
      workTeamId: '所属团队',
      operatorId: '归属人',
      oppStateId: '商机阶段',
      possibility: '可能性',
      expectPrice: '预期金额',
      currency: '币种',
    }
    $('#oppBasicField').html(dynamicGenerateField(oppBasicField, 'fo_opportunity_field_set', ['projectName', 'actorCode'], ['projectName', 'actorCode'], ['projectName', 'actorCode']))
    $('#oppStateField').html(dynamicGenerateField(oppStateField, 'fo_opportunity_field_set', ['workTeamId'], ['workTeamId'], ['workTeamId']))
    window.oppFields = [
      ...Object.keys(oppBasicField),
      ...Object.keys(oppStateField),
    ]
  }
  const BUILD_TYPE = {
    ONLY_ISNULL : 1,
  }
  //动态生成字段 返回html字符串
  function dynamicGenerateField(fields, taskTypeCode, disabledKeys, isNullDisabledKeys, isNullCheckedKeys , type=''){
    const isNullOnly = type === BUILD_TYPE.ONLY_ISNULL;
    return Object.keys(fields).map(fieldKey=>{
      const disabled = Array.isArray(disabledKeys) && disabledKeys.includes(fieldKey) ? 'disabled' : ''
      const isNullDisabled = Array.isArray(isNullDisabledKeys) && isNullDisabledKeys.includes(fieldKey) ? 'disabled' : ''
      const isNullChecked = Array.isArray(isNullCheckedKeys) && isNullCheckedKeys.includes(fieldKey) ? 'checked' : ''
      isNullChecked && console.log(fieldKey, isNullChecked);
      if(isNullOnly) {
        return `<li>${fields[fieldKey]}
        <input style="display: none" type="checkbox" id="field-set-settings-${taskTypeCode}-checkbox-${fieldKey}" checked ${disabled} />
        |<input type="checkbox" id="field-set-settings-${taskTypeCode}-checkbox-${fieldKey}IsNull" ${isNullChecked} ${isNullDisabled} />
        </li>`
      }
      return `<li>${fields[fieldKey]}
      |<input type="checkbox" id="field-set-settings-${taskTypeCode}-checkbox-${fieldKey}" checked ${disabled} />
      |<input type="checkbox" id="field-set-settings-${taskTypeCode}-checkbox-${fieldKey}IsNull" ${isNullChecked} ${isNullDisabled} />
      </li>`
    }).join('\n')
  }
  function hideChildren(dom) {
    $(dom).parent().next('ul').stop(true, true)[$(dom).prop('checked') ? 'slideDown' : 'slideUp']()
  }
</script>
</html>
