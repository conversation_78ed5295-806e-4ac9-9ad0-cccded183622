// * -----------------------TODO: 在此添加组件路径（相对于导入他的html文件位置）-----------------------
function SetComponentList() {
  return [
    'App.vue?v=250623',
    '../VueCommonComponents/AlexTable.vue?v=241217',
    '../VueCommonComponents/CustomDialog.vue?v=241217',
    '../VueCommonComponents/ProjectSelect.vue?v=241217',
    'children/OptionWrapperPanel/OptionWrapperPanel.vue?v=250623',
    'children/TaskKindPanel/TaskKindPanel.vue?v=241217',
    'children/TaskKindPanel/SelectTaskKind.vue?v=241217',
    'children/OppThemePanel/OppThemePanel.vue?v=241217',
    'children/OppThemePanel/OppThemeEditor.vue?v=241217',
    'children/OppStatePanel/OppStatePanel.vue?v=241217',
    'children/OppStatePanel/OppStateEditor.vue?v=241217',
    'children/InviteResultPanel/InviteResultPanel.vue?v=241217',
    'children/InviteResultPanel/InviteResultEditor.vue?v=241217',
    'children/ProvinceRegionPanel/ProvinceRegionPanel.vue?v=241217',
    'children/ProvinceRegionPanel/BatchChangeArea.vue?v=241217',
    'children/ReturnVisitPanel/ReturnVisitPanel.vue?v=241217',
    'children/ReturnVisitPanel/ReturnVisitEditor.vue?v=241217',
    'children/RegisterRolePanel/RegisterRolePanel.vue?v=241217',
    'children/RegisterRolePanel/RegisterRoleEditor.vue?v=250620',
    'children/BlankAccountPanel/BlankAccountEditor.vue?v=241217',
    'children/BlankAccountPanel/BlankAccountPanel.vue?v=241217',
    'children/InvoiceMain/InvoiceMainPanel.vue?v=241217',
    'children/InvoiceMain/InvoiceMainDetail.vue?v=241217',
    'children/InvoiceContent/InvoiceContentPanel.vue?v=241217',
    'children/InvoiceContent/InvoiceContentDetail.vue?v=241217',
    'children/InvoiceType/InvoiceTypePanel.vue?v=241217',
    'children/InvoiceType/InvoiceTypeDetail.vue?v=241217',
    'children/ContactResultPanel/ContactResultEditor.vue?v=241217',
    'children/ContactResultPanel/ContactResultPanel.vue?v=241217',
    'children/InviteFailureReasonPanel/InviteFailureReasonPanel.vue?v=241217',
    'children/InviteFailureReasonPanel/InviteFailureReasonEditor.vue?v=241217',
    'children/ReturnVisitFailureReasonPanel/ReturnVisitFailureReasonPanel.vue?v=241217',
    'children/ReturnVisitFailureReasonPanel/ReturnVisitFailureReasonEditor.vue?v=241217',
    'children/TradeProdPanel/TradeProdPanel.vue?v=250408',
    'children/TradeProdPanel/TradeEditor.vue?v=250303',
    'children/TradeProdPanel/ProductTypeEditor.vue?v=241217',
    'children/ItemCodePanel/ItemCodeEditor.vue?v=250506',
    'children/ItemCodePanel/ItemCodePanel.vue?v=241217',
    'children/ItemCodePanel/ItemCodeDialog.vue?v=241217',
  ]
}


// * -----------------------TODO: 在此添加Vue全局配置-----------------------

function SetVueExtent() {}

