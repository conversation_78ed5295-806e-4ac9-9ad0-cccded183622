<template>
  <custom-dialog
    :show="show"
    @before-close="close(false)"
    title="行业分类设置"
    width="400px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="100px"
        size="mini">
        <el-form-item prop="tradeId" label="编号">
          <el-input v-model="tForm.tradeId" disabled clearable placeholder="行业编号由系统自动生成"></el-input>
        </el-form-item>
        <el-form-item prop="tradeName" label="行业名称">
          <el-input v-model="tForm.tradeName" v-focus clearable placeholder="行业名称"></el-input>
        </el-form-item>
        <el-form-item prop="tradeCode" label="英文名称">
          <el-input v-model="tForm.tradeCode" clearable placeholder="英文名称"></el-input>
        </el-form-item>
        <el-form-item label="上级行业" prop="parentName">
          <el-select
            ref="tsel"
            clearable
            :title="tForm.parentName"
            v-model="tForm.parentName"
            @visible-change="onTreePanelVisibleChange"
            @clear="clear('parentId', 'parentName')">
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="tradeClassList"
                :default-expanded-keys="tradeClassList[0] ? [(tradeClassList[0]||{})['tradeId']] : null"
                :props="{ children: 'children', label: 'tradeName'}"
                highlight-current
                node-key="id"
                @node-click="tradeClassChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "TradeEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance', 'getTradeClassListPrue'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        tradeId: '',   // 行业ID
        tradeName: '', // 行业名称
        parentId: '',  // 上级行业ID， -1代表没有上级行业
        parentName: '',// 上级行业名称
        tradeCode: '', // 英文名称
      },
      rules: {
        tradeName: [{required: true, message: '请输入行业名称', trigger: ['blur', 'change']}],
      },
      openHandler: {
        resolve: null,
        reject: null,
      },
      tradeClassList: [],
    }
  },
  methods: {
    async open(row) {
      row = row || {}
      this.tradeClassList = await this.getTradeClassListPrue()
      return new Promise((resolve, reject) => {
        this.openHandler.resolve = resolve
        this.openHandler.reject = reject
        const parentId = row.parentId || ''
        let parentName = row.parentName || ''
        const treePathName = row.treePathName || ''
        if (parentId && +parentId !== -1 && treePathName) {
          parentName = treePathName.split('->').slice(0, -1).join('->') || parentName
        }
        this._initForm({
          tradeId: row.tradeId || '',
          tradeName: row.tradeName || '',
          tradeCode: row.tradeCode || '',
        })
        if (parentId && parentName) {
          this.tradeClassChange({tradeId: parentId, treePathName: parentName})
        }
        this.show = true
      })
    },
    close(isOk) {
      this.show = false
      isOk ? this.openHandler.resolve({...this.tForm}) : this.openHandler.reject('close')
      this.openHandler.resolve = null
      this.openHandler.reject = null
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        delete post.parentName
        const {data: {state, msg}} = await Axios.post('/tradeClass/save', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        this.$emit('operation-complete')
        this.close(true)
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    },
    tradeClassChange(data) {
      this.tForm.parentName = data.treePathName
      this.tForm.parentId = data.tradeId
      this.$nextTick(() => {
        this.$refs.tsel.$el.querySelector('.el-input__inner').scrollLeft = 9999
        this.$refs.tsel.blur()
      })
    },
    onTreePanelVisibleChange(show) {
      if (!show) return false
      setTimeout(() => this.$refs.tsel.$refs.scrollbar.wrap.scrollTo(0, 0))
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>