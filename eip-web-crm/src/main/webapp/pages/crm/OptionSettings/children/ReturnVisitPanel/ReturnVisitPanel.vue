<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left"></div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addReturnVisit">新增</el-button>
        <el-button size="mini" @click="delReturnVisit">删除</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="typeTargetList">
        <span>{{(row.typeTargetList||[]).map(item=> item.targetName).join('、')}}</span>
      </template>
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editReturnVisit(row)">修改</el-link>
      </template>
    </alex-table>
    <return-visit-editor @operation-complete="refresh" ref="editor"></return-visit-editor>
  </el-container>
</template>

<script>
const ReturnVisitEditor = importC('ReturnVisitEditor')
export default {
  name: "ReturnVisitPanel",
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/returnVisitType/getList', JSON2FormData(this.requestHistory))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addReturnVisit() {
      this.editReturnVisit()
    },
    async delReturnVisit() {
      try {
        const selection = this.$refs.table.selection()
        if (!selection.length) {
          await this.$errorMsg('请选择要删除的回访类别', '提醒', 'warning')
          return
        }
        await this.$confirm(`确定要删除选中的${selection.length}个回访类别吗？`, '提醒', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const post = {
          operEntrance: this.operEntrance,
          returnVisitTypeIds: selection.map(({returnVisitTypeId}) => returnVisitTypeId).toString()
        }
        const {data: {state}} = await Axios.post('/returnVisitType/delete', JSON2FormData(post))
        if (state !== 1) await this.$errorMsg('删除失败')
        this.$message.success('删除成功')
        this.refresh()
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    },
    editReturnVisit(row) {
      this.$refs.editor.open(row)
    }
  },
  mounted() {
    this.refresh()
  },
  created () {
    this.fields = [
      {label: '回访类别ID', prop: 'returnVisitTypeId', sortable: true, fixedWidth: true, width: 200},
      {label: '回访类别名称', prop: 'returnVisitTypeName', sortable: true},
      {label: '回访类别排序', prop: 'showOrder', sortable: true},
      {label: '适用回访对象', prop: 'typeTargetList'},
      {label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120},
    ]
  },
  components: {
    ReturnVisitEditor
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>
