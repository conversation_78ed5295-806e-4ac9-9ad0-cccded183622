<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left">
        <span style="font-size: 14px;color: #666">当前商机阶段数量： {{ total }}</span>
        <div v-if="+teamId !== -1">
          <el-radio-group v-model="oppStateMode" @change="updateOppStateMode">
            <el-radio :label="1">默认公司统一设置商机阶段</el-radio>
            <el-radio :label="0">启用团队自定义商机阶段</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addOppState">新增</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editOppState(row)">修改</el-link>
        <el-link type="primary" :underline="false" @click.stop="delOppState(row)">删除</el-link>
      </template>
      <template slot-scope="{row}" slot="f_key_point">{{ row.f_key_point|keyPointFilter }}</template>
    </alex-table>
    <opp-state-editor @operation-complete="refresh" ref="editor" :key-points="keyPointList"></opp-state-editor>
  </el-container>
</template>

<script>
const OppStateEditor = importC('OppStateEditor')
const keyPointList = [
  {label: '开始保护', value: 'start_protect'},
  {label: '退出保护', value: 'quit_protect'},
  {label: '客户成交', value: 'make_bargain'},
  {label: '无', value: ''}
]
export default {
  name: "OppStatePanel",
  components: {OppStateEditor},
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
      keyPointList,
      teamId,
      oppStateMode: 0,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/OppState/getList', JSON2FormData(Object.assign({f_work_team_id: teamId}, this.requestHistory)))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addOppState() {
      this.editOppState()
    },
    async delOppState({f_opp_state_id}, del=false) {
      try {
        if (!del) await this.$confirm('此操作将删除该阶段, 是否继续?', '提示', {type: 'warning'})
        const {data: {state, msg}} = await Axios.post('/OppState/del', JSON2FormData({del, f_opp_state_id, operEntrance: this.operEntrance}))
        if (state === 3) {
          try {
            await this.$confirm('当前商机阶段仍有商机跟进的联系记录关联，是否强制删除？', '提醒', {type: 'info'})
            return await this.delOppState({f_opp_state_id}, true)
          } catch (e) {
            if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          }
        }
        if (state !== 1) await Promise.reject(msg)
        this.$message.success('删除成功！')
        this.refresh()
      } catch (e) {
        if (e === 'cancel') return
        e && console.warn(e)
        this.$errorMsg(e || void 0)
      }
    },
    editOppState(row) {
      this.$refs.editor.open(row)
    },
    createFields() {
      this.fields = [
        {label: 'ID', prop: 'f_opp_state_id', sortable: true, fixedWidth: true, width: 200},
        {label: '商机阶段', prop: 'f_opp_state_name', sortable: true},
        {label: '关键点标识', prop: 'f_key_point', sortable: true},
        {label: '排序号', prop: 'f_state_order', sortable: true, fixedWidth: true, width: 120},
      ]
      if (!this.oppStateMode) {
        this.fields.push({label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120})
      }
    },
    async getOppStateMode() {
      const post = {f_work_team_id: this.teamId}
      let {data: {data}} = await Axios.post('/OppState/queryDefaultOppState', JSON2FormData(post))
      this.oppStateMode = Number(data)
      this.createFields()
    },
    async updateOppStateMode(v) {
      try {
        v && await this.$confirm(
          '是否确认切换商机阶段设置模式？ 切换后，商机的商机阶段将清空，需要对商机重新设置商机阶段！',
          '提醒',
          {type: 'warning'})
        const post = {
          f_work_team_id: this.teamId,
          defaultOppState: Boolean(v),
          operEntrance: this.operEntrance
        }
        const {data: {state, msg}} = await Axios.post('/OppState/updateDefaultOppState', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        await this.getOppStateMode()
        this.$message.success('保存成功！')
        this.refresh()
      } catch (e) {
        if (e === 'cancel') return this.getOppStateMode()
        e && console.warn(e)
      }
    }
  },
  mounted() {
    this.refresh()
    this.getOppStateMode()
  },
  created() {
    this.createFields()
  },
  filters: {
    keyPointFilter(_value) {
      _value = _value || ''
      const current = keyPointList.find(({value}) => _value === value)
      return current ? current.label : ''
    }
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>