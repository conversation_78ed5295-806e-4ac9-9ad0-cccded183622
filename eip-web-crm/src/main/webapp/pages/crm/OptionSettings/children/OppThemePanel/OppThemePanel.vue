<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left">
        <span style="font-size: 14px;color: #666">当前商机主题数量： {{total}}</span>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addOppTheme()">新增</el-button>
        <el-button size="mini" @click="delOppTheme">删除</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editOppTheme(row)">修改</el-link>
      </template>
    </alex-table>
    <opp-theme-editor @operation-complete="refresh" ref="editor"></opp-theme-editor>
  </el-container>
</template>

<script>
const OppThemeEditor = importC('OppThemeEditor')
export default {
  name: "OppThemePanel",
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/oppTheme/getPage', JSON2FormData(this.requestHistory))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addOppTheme() {
      this.editOppTheme()
    },
    delOppTheme() {
      const selection = this.$refs.table.selection()
      const list = selection.map(({id}) => ({id, operEntrance: this.operEntrance}))
      this.delBatch(list, '/oppTheme/deleteBatch')
    },
    editOppTheme(row) {
      this.$refs.editor.open(row)
    }
  },
  mounted() {
    this.refresh()
  },
  created () {
    this.fields = [
      {label: 'ID', prop: 'id', sortable: true, fixedWidth: true, width: 200},
      {label: '商机主题', prop: 'text', sortable: true},
      {label: '排序号', prop: 'order', sortable: true},
      {label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120},
    ]
  },
  components: {
    OppThemeEditor
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>