<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    title="批量修改区域"
    width="400px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="60px"
        size="mini">
        <el-alert
          type="info"
          :closable="false"
          show-icon>
          <template slot="title">
            <span>已选择
              <span style="color: #1890FF">{{provinceListInfo.number}}</span> 项
              <span style="margin-left: 5px">{{provinceListInfo.text}}</span>
            </span>
          </template>
        </el-alert>
        <el-form-item prop="areaId" label="区域">
          <el-select v-model="tForm.areaId" placeholder="区域" filterable default-first-option clearable>
            <el-option
              v-for="item in area"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "BatchChangeArea",
  mixins: [Mixins.clearAndInitForm(), Mixins.itemKindCode()],
  inject: ['operEntrance'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        areaId: '',
        provinceList: []
      },
      rules: {
        areaId: [{required: true, message: '请选择区域', trigger: ['blur', 'change']}]
      },
      area: []
    }
  },
  methods: {
    open(provinceList) {
      provinceList = provinceList || []
      this._initForm({provinceList})
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        const {data: {state, message}} = await Axios.post('/provinceArea/save', post)
        if (state !== 1) await Promise.reject(message)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    }
  },
  mounted() {
    this.getItemKindCode('area')
  },
  computed: {
    provinceListInfo() {
      const list = this.tForm.provinceList
      return {
        number: list.length,
        text: list.join('、')
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.el-alert {
  margin-bottom: 18px;
  background-color: #E6F7FF;
}

.el-alert .el-alert__icon {
  color: #1890FF;
}
</style>