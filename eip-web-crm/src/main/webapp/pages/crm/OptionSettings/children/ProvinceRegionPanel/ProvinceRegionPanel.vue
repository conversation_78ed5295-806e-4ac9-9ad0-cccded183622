<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left"></div>
      <div class="right">
        <el-link style="margin-right: 10px" :underline="false" type="primary" @click="openIcDialog">设置区域</el-link>
        <el-button size="mini" type="primary" @click="changeArea">修改区域</el-button>
      </div>
    </el-header>
    <alex-table
      style="flex-grow: 1"
      ref="table"
      :data="tableData"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      @sort-change="sort"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
    </alex-table>
    <batch-change-area ref="editor" @operation-complete="refresh"></batch-change-area>
    <item-code-dialog title="设置区域" ref="icDialog" :config="config" :item="item"></item-code-dialog>
  </el-container>
</template>

<script>
const BatchChangeArea = importC('BatchChangeArea')
const ItemCodeDialog = importC('ItemCodeDialog')
export default {
  name: "ProvinceRegionPanel",
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    item: {
      type: Object,
      default: () => ({})
    }
  },
  components: {BatchChangeArea, ItemCodeDialog},
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
      teamId,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/provinceArea/getList', JSON2FormData(Object.assign(this.requestHistory)))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    changeArea() {
      const selection = this.$refs.table.selection()
      if (selection.length <= 0)
        return this.$errorMsg('请选择需要修改的省份！', '警告', 'warning')
      this.$refs.editor.open(selection.map(({province}) => province))
    },
    openIcDialog() {
      this.$refs.icDialog.open()
    }
  },
  mounted() {
    this.refresh()
  },
  created() {
    this.fields = [
      {label: '省份', prop: 'province'},
      {label: '区域名称', prop: 'areaName'},
    ]
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3px;
}


.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>
