<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :disabled="!tForm.invoiceKindId"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <!-- :rules="rules" -->
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        label-width="110px"
        size="mini">
        <el-form-item label="发票类型">
        <!-- <el-form-item prop="billingContent" label="发票类型" :rules="rulesRequired" required> -->
          <el-input v-model.trim="invoiceKindName" disabled placeholder="发票类型"></el-input>
        </el-form-item>
        <el-form-item label="业务员可用">
          <el-checkbox v-model="tForm.employeeUse"></el-checkbox>
        </el-form-item>
        <el-form-item label="会员可用">
          <el-checkbox v-model="tForm.memberUse"></el-checkbox>
        </el-form-item>
        <!-- <el-form-item prop="sort" label="排序号">
          <el-input-number
            :controls="false"
            :precision="0"
            placeholder="排序号"
            v-model="tForm.sort">
          </el-input-number>
        </el-form-item> -->
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "InvoiceTypeDetail",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        invoiceKindId: '',
        employeeUse: true,
        memberUse: true,
      },
      invoiceKindName: '',
      rulesRequired: [
        {
          required: true, message: '此为必输项 !', type: 'blur',
        },
      ],
      title: '开票类型设置'
    }
  },
  methods: {
    error(msg = '请求失败', throws = true) {
      if(this.saving) this.saving = false
      this.$errorMsg(msg)
      if(throws) throw new Error(msg)
    },
    ok(msg = '操作成功！', throws = false) {
      this.$message.success(msg)
      if(throws) throw new Error(msg)
    },
    open(row) {
      // this.title = row ? '修改开票内容' : '新增开票内容'
      const {invoiceKindId,invoiceKindName, employeeUse, memberUse} = row || {}
      this._initForm(
        row ?
          {
            invoiceKindId,
            employeeUse: employeeUse===false ? false : true,
            memberUse: memberUse===false ? false : true,
          } :
          null
      )
      this.invoiceKindName = invoiceKindName || '';
      this.show = true
    },
    async saveForm() {
      this.saving = true
      const {data: {state, msg}} = await Axios.post('/fnInvoiceKind/updateInvoiceKindById',
        JSON2FormData(this.tForm)
      ).catch(_ => this.error())
      this.show = false
      if (state !== 1) this.error(msg)
      this.$emit('operation-complete')
      this.ok('操作成功！')
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
  margin-bottom: 8px;
}
</style>