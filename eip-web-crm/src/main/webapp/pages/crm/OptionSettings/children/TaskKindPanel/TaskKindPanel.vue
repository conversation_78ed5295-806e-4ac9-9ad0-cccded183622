<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left"></div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addTaskKind">新增</el-button>
        <el-button size="mini" @click="delTaskKind">删除</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      :data="tableData"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editTaskKind(row)">修改</el-link>
      </template>
    </alex-table>
    <select-task-kind ref="taskSelector"></select-task-kind>
  </el-container>
</template>

<script>
const SelectTaskKind = importC('SelectTaskKind')
export default {
  name: "TaskKindPanel",
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/taskKindOrg/getList', JSON2FormData(this.requestHistory))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    async addTaskKind() {
      try {
        const result = await this.$refs.taskSelector.open()
        const post = result.map(task => {
          const {taskKindCode, taskKindName} = task
          return {taskKindCode, taskKindName}
        })
        const {data: {state}} = await Axios.post('/taskKindOrg/insertBatch', post)
        switch (state) {
          case 1:
            this.$message.success('操作成功！')
            break
          case 2:
            this.$message.success('操作成功，已自动按类别代号过滤重复！')
            break
          default:
            await Promise.reject(Error('操作失败！'))
        }
        this.refresh()
      } catch (e) {
        if (e instanceof Error) {
          this.$errorMsg(e.message)
        }
        console.warn(e)
      }
    },
    delTaskKind() {
      const selection = this.$refs.table.selection()
      this.delBatch(selection.map(({id}) => ({id})), '/taskKindOrg/deleteBatch', true)
    },
    async editTaskKind(row) {
      try {
        const {id, taskKindName} = row
        const {value} = await this.$prompt('请输入类别名称', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: taskKindName,
          inputPattern: /^.+$/,
          inputErrorMessage: '类别名称不能为空'
        })
        const {data: {state}} = await Axios.post('/taskKindOrg/updateBatch', [{id, taskKindName: value}])
        if (state !== 1) await Promise.reject(Error('操作失败!'))
        this.$message.success('操作成功!')
        this.refresh()
      } catch (e) {
        if (e instanceof Error) {
          this.$errorMsg(e.message)
        }
        console.warn(e)
      }
    }
  },
  mounted() {
    this.refresh()
  },
  created() {
    this.fields = [
      {label: '序号', prop: 'id', sortable: true, fixedWidth: true, width: 120},
      {label: '类别代号', prop: 'taskKindCode', sortable: true},
      {label: '类别名称', prop: 'taskKindName', sortable: true},
      {label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120},
    ]
  },
  components: {
    SelectTaskKind
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>