<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left">
        <span style="font-size: 14px;color: #666">当前邀请结果数量： {{ total }}</span>
        <div v-if="+teamId !== -1">
          <el-radio-group v-model="customContactResult" @change="updateContactResultMode">
            <el-radio :label="false">默认公司统一设置联系结果</el-radio>
            <el-radio :label="true">启用团队自定义联系结果</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="right">
        <el-button v-if="customContactResult" size="mini" type="primary" @click="addContactResult">新增</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editContactResult(row)">修改</el-link>
        <el-link type="primary" :underline="false" @click.stop="delContactResult(row)">删除</el-link>
      </template>
    </alex-table>
    <contact-result-editor ref="editor" @operation-complete="refresh" ></contact-result-editor>
  </el-container>
</template>

<script>
const ContactResultEditor = importC('ContactResultEditor')
export default {
  name: "ContactResultPanel",
  components: {ContactResultEditor},
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
      teamId,
      customContactResult: false,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows,
            data
          }
        } = await Axios.post('/contactResult/getPage', JSON2FormData(Object.assign({workTeamId: teamId}, this.requestHistory)))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
        this.customContactResult = +teamId === -1 || Boolean(data)
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addContactResult() {
      this.editContactResult()
    },
    async delContactResult({contactResultId}) {
      try {
        const post = {
          workTeamId: this.teamId,
          contactResultId,
          operEntrance: this.operEntrance
        }
        await this.$confirm('此操作将删除该联系结果, 是否继续?', '提示', {type: 'warning'})
        const {data: {state, msg}} = await Axios.post('/contactResult/delete', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        this.$message.success('删除成功！')
        this.refresh()
      } catch (e) {
        if (e === 'cancel') return
        e && console.warn(e)
        this.$errorMsg(e || void 0)
      }
    },
    editContactResult(row) {
      this.$refs.editor.open(row)
    },
    createFields() {
      this.fields = [
        {label: 'ID', prop: 'contactResultId', sortable: true, fixedWidth: true, width: 200},
        {label: '联系结果名称', prop: 'contactResultName', sortable: true},
        {label: '排序号', prop: 'showOrder', sortable: true, fixedWidth: true},
      ]
      if (this.customContactResult) {
        this.fields.push({label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120})
      }
    },
    async updateContactResultMode(v) {
      try {
        const post = {
          workTeamId: this.teamId,
          customContactResult: Boolean(v),
          operEntrance: this.operEntrance
        }
        const {data: {state, msg}} = await Axios.post('/contactResult/updateCustomContactResult', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        this.$message({
          message: '保存成功！',
          showClose: true,
          type: 'success'
        })
        this.refresh()
      } catch (e) {
        if (e === 'cancel') return
        e && console.warn(e)
        this.$errorMsg(e || void 0)
      }
    }
  },
  mounted() {
    this.refresh()
  },
  created() {
    this.createFields()
  },
  watch: {
    customContactResult() {
      this.createFields()
    }
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>