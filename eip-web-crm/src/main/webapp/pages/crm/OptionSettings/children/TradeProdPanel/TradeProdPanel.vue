<template>
  <el-container>
    <el-aside width="260px;" style="position: relative;padding-bottom: 40px;border-right: 1px solid rgb(245, 245, 245);">
      <div class="trade-setting">
        <el-link type="primary" :underline="false" icon="el-icon-plus" @click="tradeCrudHandler('add')">新增行业</el-link>
        <el-link :underline="false" icon="el-icon-delete" @click="tradeCrudHandler('del')">删除</el-link>
        <!--<div class="trade-setting-toolbar" v-if="currentTrade">-->
        <!--  <span style="color: #666;font-size: 14px;" class="text-over-hidden">{{ currentTrade['tradeName'] }}</span>-->
        <!--  <el-dropdown-->
        <!--    placement="top-start"-->
        <!--    @command="tradeCrudHandler"-->
        <!--    trigger="click">-->
        <!--    <i class="el-icon-more-outline" style="font-size: 18px;vertical-align: middle;color: #409EFF;"-->
        <!--       title="删除行业"></i>-->
        <!--    <el-dropdown-menu slot="dropdown" class="contract-invalid">-->
        <!--      <el-dropdown-item style="white-space: nowrap;" command="del">-->
        <!--        删除行业-->
        <!--      </el-dropdown-item>-->
        <!--    </el-dropdown-menu>-->
        <!--  </el-dropdown>-->
        <!--</div>-->
      </div>
      <div class="trade-wrapper">
        <el-tree
          ref="tree"
          :default-expanded-keys="defaultExpanded"
          :data="tradeClassList"
          :props="{ children: 'children', label: 'tradeName'}"
          :default-expanded-keys="tradeClassList[0] ? [(tradeClassList[0]||{})['tradeId']] : null"
          highlight-current
          node-key="id"
          @node-click="tradeSearch"
          style="min-width: 200px;max-width: 325px;"
          ref="tree">
        </el-tree>
      </div>
      <div class="search-tree-wrapper">
        <el-checkbox
          @change="search"
          style="height: 30px;line-height: 30px;"
          v-model="searchForm.showChild">
          子节点产品全显示
        </el-checkbox>
      </div>
    </el-aside>
    <el-main style="padding: 0">
      <el-header height="auto" class="top-header">
        <div class="top-header-inner" v-if="currentTrade">
          <h3 style="margin: 10px 0;">
            <span style="margin-right: 10px">当前行业：{{ currentTrade['tradeName'] }}</span>
            <el-link :underline="false" type="primary" icon="el-icon-edit" @click="tradeCrudHandler('edit')">
              编辑
            </el-link>
            <el-link v-if="currentTrade && currentTrade.showPush" style="margin-left: 10px" :underline="false" type="primary" icon="el-icon-s-promotion" @click="tradeCrudHandler('push')">
              批量推送
            </el-link>
          </h3>
          <div class="trade-info-wrap">
            <div class="trade-info-item" v-if="currentTrade['tradeId']">
              <span>行业编号</span>
              <span>{{ currentTrade['tradeId'] }}</span>
            </div>
            <div class="trade-info-item" v-if="currentTrade['tradeCode']">
              <span>英文名称</span>
              <span :title="currentTrade['tradeCode']">{{ currentTrade['tradeCode'] }}</span>
            </div>
            <div class="trade-info-item" v-if="currentTrade['parentName']">
              <span>上级行业</span>
              <span :title="currentTrade['parentName']">{{ currentTrade['parentName'] }}</span>
            </div>
          </div>
        </div>
      </el-header>
      <el-main class="table-wrapper">
        <div class="table-toolbar">
          <div>产品类别</div>
          <el-button style="margin-left: auto" type="primary" size="mini" @click="productCrudHandler('add')">新增产品类别</el-button>
          <el-button plain size="mini" @click="productCrudHandler('del')">删除</el-button>
        </div>
        <alex-table
          operation-width="100px"
          :index="false"
          mode="multiple"
          class="alex-table-ant"
          :data="productTypeList"
          :loading="loading"
          :fields="fields"
          @sort-change="sort"
          :handle-pagination="handlePagination"
          :total="total"
          height="calc(100vh - 280px)"
          ref="table">
          <template slot="alex-operation" slot-scope="{row}">
            <el-link type="primary" :underline="false" @click.stop="productCrudHandler('edit', row)">编辑</el-link>
          </template>
        </alex-table>
      </el-main>
    </el-main>
    <trade-editor ref="tradeEditor"></trade-editor>
    <product-type-editor ref="productTypeEditor"></product-type-editor>
  </el-container>
</template>

<script>
const AlexTable = importC('AlexTable')
const TradeEditor = importC('TradeEditor')
const ProductTypeEditor = importC('ProductTypeEditor')

export default {
  name: 'TradeProdPanel',
  mixins: [
    Mixins.table(),
    Mixins.itemKindCode(),
    Mixins.clearForm()
  ],

  provide() {
    return {
      getTradeClassListPrue: this.getTradeClassListPrue,
      operEntrance: this.operEntrance
    }
  },
  components: {
    AlexTable,
    TradeEditor,
    ProductTypeEditor,
  },
  data() {
    return {
      loading: false,
      tradeClassList: [],
      productTypeList: [],
      total: 0,
      currentTrade: null,
      fields: [],
      searchForm: {
        f_trade_id: '',
        showChild: false
      },
      defaultExpanded: [],
      operEntrance: '选项编码行业及产品类别设置'
    }
  },
  async mounted() {
    await this.getTradeClassList()
    this.createFields()
  },
  methods: {
    createFields() {
      this.fields = [
        {label: '编号', prop: 'f_product_type_id', fixedWidth: true, width: 100},
        {label: '产品名称', prop: 'f_type_name', fixedWidth: true, width: 320},
        {label: '行业分类', prop: 'tradeTreePathName', fixedWidth: true, width: 320},
        {label: '英文名称', prop: 'f_type_code', fixedWidth: true, width: 320},
        {label: '显示序号', prop: 'f_show_order', fixedWidth: true, width: 100},
      ]
    },
    async getTradeClassList() {
      this.tradeClassList = await this.getTradeClassListPrue()
      if (!this.currentTrade && this.tradeClassList[0]) {
        this.tradeSearch(this.tradeClassList[0])
      }
    },
    async getTradeClassListPrue() {
      try {
        const {data} = await Axios.post('/tradeClass/getAll', JSON2FormData({showPush: true}))
        if (data.state !== 1) await Promise.reject(data.msg || '部门加载失败')
        return data.data
      } catch (e) {
        console.warn(e)
        this.$errorMsg('部门加载失败')
        return []
      }
    },
    async requestTableData(post) {
      this.loading = true
      this.requestHistory = post
      try {
        const {data} = await Axios.post('/productTypeBase/getList', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject('数据加载失败')
        // 绑定表格分页器
        const tableRef = this.$refs.table
        if (tableRef && ('rows' in post || 'page' in post)) {
          tableRef.setPager(post.page, post.rows)
        }
        this.total = data.total
        this.productTypeList = data.rows
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    search() {
      const {showChild, f_trade_id} = {...this.searchForm}
      const post = {[showChild ? 'parentTradeIds' : 'f_trade_id']: f_trade_id}
      post.page = 1
      post.rows = 20
      this.requestTableData(post)
    },
    tradeSearch(data) {
      const {parentId, parentName, treePathName} = data
      this.currentTrade = data
      if (parentId && +parentId !== -1 && treePathName) {
        this.currentTrade.parentName = treePathName.split('->').slice(0, -1).join('->') || parentName
      }
      this.searchForm.f_trade_id = data.tradeId
      this.search()
    },
    clearSearch() {
      this.clearAll()
      this.search()
    },
    /**
     * @param {'add'|'edit'|'del'|'push'} opt
     * @param {any[]} args
     * */
    async tradeCrudHandler(opt, ...args) {
      const trade = this.currentTrade
      const editor = this.$refs.tradeEditor
      const add = async () => {
        const form = await editor.open(
          trade ? {
              parentId: trade.tradeId,
              parentName: trade.treePathName,
            } :
            {})
        if (form.parentId && form.parentId !== -1) {
          this.defaultExpanded = [form.parentId]
        }
        await this.getTradeClassList()
      }
      const edit = async () => {
        const form = await editor.open(trade)
        this.tradeSearch({...form})
        if (form.parentId && form.parentId !== -1) {
          this.defaultExpanded = [form.parentId]
        }
        await this.getTradeClassList()
      }
      const beforeDel = async (f_trade_id) => {
        const {data} = await Axios.post('/productTypeBase/getCount', JSON2FormData({f_trade_id}))
        if (!+data.data) return
        await this.$confirm(`该行业存在${data.data}个产品，是否确认删除行业及产品信息?`, '提示', {type: 'warning'})
      }
      const del = async () => {
        const {tradeId, children, parentId} = trade
        if (children && children.length)
          await Promise.reject('存在下级行业分类，删除失败!')
        await beforeDel(tradeId)
        const post = {tradeId, operEntrance: this.operEntrance}
        const {data} = await Axios.post('/tradeClass/delete', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject(data.msg || '删除失败')
        this.defaultExpanded = parentId && parentId !== -1 ? [parentId] : []
        this.currentTrade = null
        await this.getTradeClassList()
      }

      const push = async () => {
        const {tradeId} = trade
        if (!tradeId) await Promise.reject('请先选择行业')
        const post = {tradeId}
        await this.$confirm('是否确认推送当前行业及下级行业?', '提醒', {type: 'warning'})
        const {data} = await Axios.post('/tradeClass/push', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject(data.msg || '推送失败')
      }

      const loading = this.$loading({background: 'rgba(0, 0, 0, 0.3)', text: '加载中...'})
      try {
        await ({add, edit, del, push}[opt](...args))
        this.$message.success({
          showClose: true,
          message: '操作成功',
        })
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e)
      } finally {
        loading.close()
      }
    },
    /**
     * @param {'add'|'edit'|'del'} opt
     * @param {any[]} args
     * */
    async productCrudHandler(opt, ...args) {
      const trade = this.currentTrade
      const editor = this.$refs.productTypeEditor
      const table = this.$refs.table

      const add = async () => {
        const {tradeId, treePathName, tradeName, parentName} = trade
        await editor.open({
          f_trade_id: tradeId,
          tradeTreePathName: treePathName || [parentName, tradeName].filter(Boolean).join('->')
        })
        await this.refresh()
      }
      const edit = async () => {
        const [currentRow] = args
        await editor.open(currentRow)
        await this.refresh()
      }

      const del = async () => {
        const selection = table.selection()
        if (!selection.length) await Promise.reject('请选择要删除的数据')
        await this.$confirm(`已选择${selection.length}个产品，是否确认删除?`, '提示', {type: 'warning'})
        const f_product_type_id = selection.map(({f_product_type_id}) => f_product_type_id).join(',')
        const post = {f_product_type_id, operEntrance: this.operEntrance}
        const {data} = await Axios.post('/productTypeBase/delete', JSON2FormData(post))
        if (data.state !== 1) await Promise.reject(data.msg || '删除失败')
        await this.search() // 回到第一页
      }

      const loading = this.$loading({background: 'rgba(0, 0, 0, 0.3)', text: '加载中...'})
      try {
        await ({add, edit, del}[opt](...args))
        this.$message.success({
          showClose: true,
          message: '操作成功',
        })
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e)
      } finally {
        loading.close()
      }
    }
  }
}
</script>

<style scoped>
.trade-wrapper {
  padding: 0 12px;
}

.trade-wrapper .el-tree {
  max-height: calc(-130px + 100vh);
  overflow: auto;
  max-width: 240px;
  min-width: 200px;
}

.trade-wrapper .el-tree .el-tree-node__expand-icon:not(.is-leaf) {
  transform: rotate(0);
}

.trade-wrapper .el-tree .el-tree-node__expand-icon:not(.is-leaf)::before {
  content: "\e6d9";
  border: 1px solid #C0C4CC;
}

.trade-wrapper .el-tree .el-tree-node__expand-icon.expanded:not(.is-leaf)::before {
  content: "\e6d8";
  border: 1px solid #C0C4CC;
}

.trade-wrapper .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e0e7ee;
}
.trade-wrapper .el-tree .el-tree-node__label {
  max-width: calc(100% - 26px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.trade-setting {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(255, 255, 255);
  text-align: center;
  height: 40px;
}

.trade-setting .el-link + .el-link {
  text-align: center;
  margin-left: 20px;
}

.search-tree-wrapper {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  text-align: center;
}
.trade-setting-toolbar {
  margin-top: 10px;
  height: 32px;
  background-color: #f2f2f2;
  border: solid 1px #d9d9d9;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}

.top-header {
  padding: 20px;
  overflow: auto;
}

.top-header-inner {
  display: flex;
  min-width: 750px;
  flex-direction: column;
}

.trade-info-wrap {
  display: flex;
}

.trade-info-wrap .trade-info-item {
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
  max-width: 30vw;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.trade-info-wrap .trade-info-item + .trade-info-item {
  margin-left: 40px;
}

.trade-info-wrap .trade-info-item span:first-child {
  color: #999;
  margin-right: 10px;
}

.table-wrapper {
  padding-right: 0;
  background-color: #f5f5f5;
}

.table-wrapper .table-toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #fff;
  height: 40px;
  padding: 10px 10px 10px 20px;
}

.table-wrapper .alex-table-ant {
  padding: 0 10px 20px 20px;
  background-color: #fff;
}
</style>