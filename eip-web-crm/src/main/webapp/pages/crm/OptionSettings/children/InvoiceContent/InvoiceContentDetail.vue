<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :disabled="!tForm.billingContent"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <!-- :rules="rules" -->
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        label-width="110px"
        size="mini">
        <el-form-item label="开票内容名称" required>
        <!-- <el-form-item prop="billingContent" label="开票内容名称" :rules="rulesRequired" required> -->
          <el-input v-model.trim="tForm.billingContent" v-focus clearable placeholder="开票内容名称"></el-input>
        </el-form-item>
        <el-form-item prop="sort" label="排序号">
          <el-input-number
            :controls="false"
            :precision="0"
            placeholder="排序号"
            v-model="tForm.sort">
          </el-input-number>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "InvoiceContentDetail",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        id: '',
        billingContent: '',
        sort: '',
      },
      rulesRequired: [
        {
          required: true, message: '此为必输项 !', type: 'blur',
        },
      ],
      title: '新增开票内容'
    }
  },
  methods: {
    error(msg = '请求失败', throws = true) {
      if(this.saving) this.saving = false
      this.$errorMsg(msg)
      if(throws) throw new Error(msg)
    },
    ok(msg = '操作成功！', throws = false) {
      this.$message.success(msg)
      if(throws) throw new Error(msg)
    },
    open(row) {
      this.title = row ? '修改开票内容' : '新增开票内容'
      const {id, billingContent, sort} = row || {}
      this._initForm(
        row ?
          {
            id: id || '',
            billingContent: billingContent || '',
            sort: sort || '',
          } :
          null
      )
      if (!this.tForm.sort) this.tForm.sort = void 0
      this.show = true
    },
    async saveForm() {
      this.saving = true
      const post = {...this.tForm}
      post.sort === void 0 && (post.sort = '')
      const {data: {state, msg}} = await Axios.post('/billingContent/save', JSON2FormData(post)).catch(_ => this.error())
      this.show = false
      if (state !== 1) this.error(msg)
      this.$emit('operation-complete')
      this.ok('操作成功！')
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>