<template>
  <custom-dialog
    :show="show"
    append2body
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="110px"
        size="mini">
        <el-form-item prop="code" label="代码编号">
          <el-input v-model="tForm.code" disabled clearable placeholder="代码编号由系统自动生成"></el-input>
        </el-form-item>
        <el-form-item prop="name" label="代码名称">
          <el-input v-model="tForm.name" v-focus clearable placeholder="代码名称"></el-input>
        </el-form-item>
        <el-form-item prop="englishName" label="英文名称">
          <el-input v-model="tForm.englishName" clearable placeholder="英文名称"></el-input>
        </el-form-item>
        <el-form-item prop="order" label="排序号">
          <el-input-number
            :controls="false"
            :precision="0"
            placeholder="排序号"
            v-model="tForm.order">
          </el-input-number>
        </el-form-item>
        <template v-if="isInferiorTreeMode">
          <el-form-item v-if="showParent" prop="parentName" label="上级分类">
            <el-select
              clearable
              filterable
              default-first-option
              v-model="tForm.parentId">
              <el-option
                v-for="item in treeList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item v-else-if="isTreeMode" prop="parentName" label="上级分类">
          <el-select ref="tsel" clearable v-model="tForm.parentName" @clear="clear('parentId', 'parentName')">
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="treeList"
                :props="{ children: 'children', label: 'name'}"
                :default-expanded-keys="treeList[0] ? [(treeList[0]||{})['id']] : null"
                highlight-current
                node-key="id"
                @node-click="treeItemChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ItemCodeEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance', 'isInferiorTreeModeFn', 'isTreeModeFn'],
  props: {
    itemKindCode: String,
    title: String
  },
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        id: '',
        code: '',
        name: '',
        englishName: '',
        parentId: '',
        parentName: '',
        order: undefined,
      },
      treeList: [],
      showParent: false,
      rules: {
        name: [{required: true, message: '请输入代码名称', trigger: ['blur', 'change']}],
      },
    }
  },
  computed: {
    isTreeMode() {
      return Boolean(typeof this.isTreeModeFn === 'function' && this.isTreeModeFn())
    },
    isInferiorTreeMode() {
      return Boolean(typeof this.isInferiorTreeModeFn === 'function' && this.isInferiorTreeModeFn())
    }
  },
  methods: {
    open(row) {
      this.loadItemCodeTree().finally()
      const {id, code, name, englishName, order, parentId, parentName} = row || {}
      this._initForm(row ? {
        id,
        code,
        name: name || '',
        englishName: englishName || '',
        parentId: parentId || '',
        parentName: parentName || '',
        order
      } : null)
      if (!this.tForm.order) this.tForm.order = void 0
      this.showParent = false
      if (this.isInferiorTreeMode) {
        if (row && row.id) {
          this.checkHaveChild(row.id).then(haveChild => {
            this.showParent = haveChild === false
          })
        }
      }
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign(
          {
            operEntrance: this.operEntrance,
            itemKindCode: this.itemKindCode
          },
          this.tForm
        )
        post.order === void 0 && (post.order = '')
        const {data: {state, msg}} = await Axios.post('/itemCode/save', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    },
    async loadItemCodeTree() {
      try {
        if (this.isInferiorTreeMode) {
          const {data: {rows}} = await Axios.post('/itemCode/getList', JSON2FormData({itemKindCode: this.itemKindCode, parentIdIsNull: true}))
          this.treeList = rows || []
        } else if (this.isTreeMode) {
          const {data: {data}} = await Axios.post('/itemCode/getTree', JSON2FormData({itemKindCode: this.itemKindCode}))
          this.treeList = data || []
        } else {
          this.treeList = []
        }
      } catch (e) {
        this.$errorMsg(e)
        console.warn(e)
      }
    },
    treeItemChange({id, name}) {
      this.tForm.parentId = id || ''
      this.tForm.parentName = name || ''
      this.$refs.tsel.blur()
    },
    async checkHaveChild(id) {
      try {
        if (!id) return null
        const {data: {data}} = await Axios.post('/itemCode/haveChild', JSON2FormData({id}))
        return data
      } catch (e) {
        this.$errorMsg(e)
        console.warn(e)
      }
    },
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>