<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left"></div>
      <div class="right">
        <el-button size="mini" type="primary" @click="add">新增</el-button>
        <el-button size="mini" @click="del">删除</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="edit(row)">修改</el-link>
      </template>
    </alex-table>
    <invoice-content-detail @operation-complete="refresh" ref="detail"></invoice-content-detail>
  </el-container>
</template>

<script>
const InvoiceContentDetail = importC('InvoiceContentDetail')
export default {
  name: "InvoiceContentPanel",
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [
        {label: '开票内容ID', prop: 'id', width: '300'},
        {label: '开票内容名称', prop: 'billingContent',},
        {label: '开票内容排序', prop: 'sort', },
        {label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120},
      ],
      loading: false,
      total: 0,
    }
  },
  methods: {
    error(msg = '请求失败', throws = true) {
      if(this.loading) this.loading = false
      this.$errorMsg(msg)
      if(throws) throw new Error(msg)
    },
    ok(msg = '操作成功！', throws = false) {
      this.$message.success(msg)
      if(throws) throw new Error(msg)
    },
    async requestTableData() {
      this.loading = true
      const {
        data: {
          state,msg,total,rows
        }
      } = await Axios
      .post('/billingContent/getPage', JSON2FormData(this.requestHistory))
      .catch(_ => this.error());
      this.loading = false
      if (state !== 1) this.error(msg)
      this.tableData = rows
      this.total = total
    },
    async add() {
      this.edit();
    },
    async edit(row) {
      this.$refs.detail.open(row)
    },
    async del() {
      const list = this.$refs.table.selection()
      if (list.length < 1) return this.$errorMsg('请选择需要删除的行！', '警告', 'warning')
      await this.$confirm(`确定要删除这${list.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const {data: {state}} = await Axios
      .post('/billingContent/batchDel',JSON2FormData({
        ids: list.map(it => it.id).join()
      }))
      .catch(_ => this.error())
      if (state !== 1) this.error('操作失败!')
      this.ok('删除成功！')
      this.refresh()
    },
  },
  mounted() {
    this.refresh()
  },
  created() {
  },
  components: {
    InvoiceContentDetail
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>