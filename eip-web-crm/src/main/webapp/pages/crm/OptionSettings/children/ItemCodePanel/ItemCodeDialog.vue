<template>
  <custom-dialog width="800px" :show="show" :title="finalTitle" @before-close="show=false">
    <div slot="body" style="height: 50vh">
      <item-code-panel :config="config" :item="item"></item-code-panel>
    </div>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
const ItemCodePanel = importC('ItemCodePanel')
export default {
  name: 'ItemCodeDialog',
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    item: {
      type: Object,
      default: () => ({})
    },
    title: String
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
    finalTitle() {
      return this.title || ('设置' + (this.item && this.item.text))
    },
  },
  methods: {
    open() {
      this.show = true
    }
  },
  components: {
    CustomDialog,
    ItemCodePanel
  }
}
</script>
