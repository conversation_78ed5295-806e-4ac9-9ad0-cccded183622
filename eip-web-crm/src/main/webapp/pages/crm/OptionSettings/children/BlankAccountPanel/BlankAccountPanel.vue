<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left"></div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addBlankAccount">新增</el-button>
        <el-button size="mini" @click="delBlankAccount">删除</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="accountType">{{row.accountType | accountTypeName}}</template>
      <template slot-scope="{row}" slot="alex-operation">
        <el-link type="primary" :underline="false" @click.stop="editBlankAccount(row)">修改</el-link>
      </template>
    </alex-table>
    <blank-account-editor ref="editor" @operation-complete="refresh"></blank-account-editor>
  </el-container>
</template>

<script>
const BlankAccountEditor = importC('BlankAccountEditor')
export default {
  name: "BlankAccountPanel",
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/fnAccount/getPage', JSON2FormData(this.requestHistory))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addBlankAccount() {
      this.editBlankAccount()
    },
    editBlankAccount(row) {
      row = row || {}
      this.$refs.editor.open(row.accountId)
    },
    async delBlankAccount() {
      try {
        const selection = this.$refs.table.selection()
        if (!selection.length) {
          await this.$errorMsg('请选择要删除的收款账号', '提醒', 'warning')
          return
        }
        await this.$confirm(`确定要删除选中的${selection.length}个收款账号吗？`, '提醒', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const post = {
          operEntrance: this.operEntrance,
          accountIds: selection.map(({accountId}) => accountId).toString()
        }
        const {data: {state}} = await Axios.post('/fnAccount/delete', JSON2FormData(post))
        if (state !== 1) await this.$errorMsg('删除失败')
        this.$message.success('删除成功')
        this.refresh()
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    },
  },
  mounted() {
    this.refresh()
  },
  created () {
    this.fields = [
      {label: '账户简称', prop: 'accountAbbr', sortable: true},
      {label: '账户名称', prop: 'accountName', sortable: true},
      {label: '银行账号', prop: 'accountNumber', sortable: true},
      {label: '银行名称', prop: 'bankName', sortable: true},
      {label: '账号类型', prop: 'accountType', sortable: true},
      {label: '归属', prop: 'teamOrBranchName', sortable: true},
      {label: '备注', prop: 'memo', sortable: true},
    ]
  },
  filters: {
    accountTypeName(id) {
      return '无限制账号,团队账号,部门账号'.split(',')[id || 0]
    }
  },
  components: {
    BlankAccountEditor
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small td, .el-table--small th {
  padding: 6px 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>