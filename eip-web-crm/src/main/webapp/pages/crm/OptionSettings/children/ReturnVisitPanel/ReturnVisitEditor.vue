<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="110px"
        size="mini">
        <el-form-item prop="returnVisitTypeName" label="回访类别名称">
          <el-input v-model="tForm.returnVisitTypeName" v-focus clearable placeholder="回访类别名称"></el-input>
        </el-form-item>
        <el-form-item prop="order" label="排序号">
          <el-input-number
            :controls="false"
            :precision="0"
            placeholder="排序号"
            v-model="tForm.showOrder">
          </el-input-number>
        </el-form-item>
        <el-form-item prop="typeTargetList" label="适用回访对象">
          <el-select v-model="tForm.typeTargetList" clearable placeholder="适用回访对象" multiple filterable>
            <el-option label="回访个人" :value="1"></el-option>
            <el-option label="回访团队" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ReturnVisitEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        returnVisitTypeId: '',
        returnVisitTypeName: '',
        showOrder: undefined,
        typeTargetList: [],
      },
      rules: {
        returnVisitTypeName: [{required: true, message: '请输入回访类别名称', trigger: ['blur', 'change']}],
        typeTargetList: [{required: true, message: '请选择适用回访对象', trigger: ['blur', 'change']}]
      },
      title: '新增回访类别'
    }
  },
  methods: {
    open(row) {
      this.title = row ? '修改回访类别' : '新增回访类别'
      const {returnVisitTypeId, returnVisitTypeName, showOrder, typeTargetList} = row || {}
      this._initForm(
        row ?
          {
            returnVisitTypeId,
            returnVisitTypeName,
            typeTargetList: (typeTargetList||[]).map(({target}) => target),
            showOrder: showOrder || void 0,
          } :
          null
      )
      if (!this.tForm.showOrder) this.tForm.showOrder = void 0
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        post.showOrder === void 0 && (post.showOrder = '')
        post.typeTargetList = post.typeTargetList.map(target => ({target}))
        const {data: {state, msg}} = await Axios.post('/returnVisitType/save', post)
        if (state !== 1) await Promise.reject(msg)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>