<template>
  <custom-dialog
    :show="show"
    @before-close="close(false)"
    title="产品设置"
    width="400px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="100px"
        size="mini">
        <el-form-item label="行业分类" prop="f_trade_name">
          <el-select
            ref="tsel"
            clearable
            :title="tForm.f_trade_name"
            v-model="tForm.f_trade_name"
            @visible-change="onTreePanelVisibleChange"
            @clear="clear('f_trade_id', 'f_trade_name')">
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="tradeClassList"
                :default-expanded-keys="tradeClassList[0] ? [(tradeClassList[0]||{})['tradeId']] : null"
                :props="{ children: 'children', label: 'tradeName'}"
                highlight-current
                node-key="id"
                @node-click="tradeClassChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="f_product_type_id" label="编号">
          <el-input v-model="tForm.f_product_type_id" disabled clearable placeholder="产品编号由系统自动生成"></el-input>
        </el-form-item>
        <el-form-item prop="f_type_name" label="产品名称">
          <el-input v-model="tForm.f_type_name" v-focus clearable placeholder="产品名称"></el-input>
        </el-form-item>
        <el-form-item prop="f_type_code" label="英文名称">
          <el-input v-model="tForm.f_type_code" clearable placeholder="英文名称"></el-input>
        </el-form-item>
        <el-form-item prop="f_show_order" label="显示序号">
          <el-input-number :max="9e8" :controls="false" v-model="tForm.f_show_order" placeholder="显示序号"></el-input-number>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ProductTypeEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance', 'getTradeClassListPrue'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        f_trade_id: '',
        f_trade_name: '',
        f_product_type_id: '',
        f_type_name: '',
        f_type_code: '',
        f_show_order: void 0,
      },
      rules: {
        f_trade_name: [{required: true, message: '请选择行业分类', trigger: ['blur', 'change']}],
        f_type_name: [{required: true, message: '请输入产品名称', trigger: ['blur', 'change']}],
      },
      openHandler: {
        resolve: null,
        reject: null,
      },
      tradeClassList: [],
    }
  },
  methods: {
    async open(row) {
      row = row || {}
      this.tradeClassList = await this.getTradeClassListPrue()
      return new Promise((resolve, reject) => {
        this.openHandler.resolve = resolve
        this.openHandler.reject = reject
        this._initForm({
          f_product_type_id: row.f_product_type_id || '',
          f_type_name: row.f_type_name ||'',
          f_type_code: row.f_type_code || '',
          f_show_order: row.f_show_order !== null ? row.f_show_order : void 0,
        })
        if (row.f_trade_id && row.tradeTreePathName) {
          this.tradeClassChange({tradeId: row.f_trade_id, treePathName: row.tradeTreePathName})
        }
        this.show = true
      })
    },
    close(isOk) {
      this.show = false
      isOk ? this.openHandler.resolve({...this.tForm}) : this.openHandler.reject('close')
      this.openHandler.resolve = null
      this.openHandler.reject = null
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        if (post.f_show_order === void 0) post.f_show_order = ''
        const {data: {state, msg}} = await Axios.post('/productTypeBase/save', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg)
        this.$emit('operation-complete')
        this.close(true)
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    },
    tradeClassChange(data) {
      this.tForm.f_trade_name = data.treePathName
      this.tForm.f_trade_id = data.tradeId
      this.$nextTick(() => {
        this.$refs.tsel.$el.querySelector('.el-input__inner').scrollLeft = 9999
        this.$refs.tsel.blur()
      })
    },
    onTreePanelVisibleChange(show) {
      if (!show) return false
      setTimeout(() => this.$refs.tsel.$refs.scrollbar.wrap.scrollTo(0, 0))
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>