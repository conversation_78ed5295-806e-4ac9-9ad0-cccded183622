<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    :title="title"
    width="800px"
    append2body>
    <el-main slot="body" class="dialog-body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right" style="flex: none">
          <el-button size="mini" type="primary" @click="confirm()">确定</el-button>
        </div>
      </el-header>
      <alex-table
        ref="table"
        :data="tableData"
        style="padding: 0"
        height="500px"
        border
        size="small"
        class="alex-table-ant"
        mode="multiple"
        :fields="fields"
        @sort-change="sort"
        :handle-pagination="handlePagination"
        :show-operation="false"
        :total="total"
        :loading="loading">
      </alex-table>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  name: "SelectTaskK<PERSON>",
  props: {
    title: {
      type: String,
      default: '选择任务类别'
    }
  },
  data() {
    return {
      show: false,
      promise: null,
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
    }
  },
  methods: {
    async open() {
      await this.requestTableData()
      this.show = true
      return new Promise((resolve, reject) => this.promise = {resolve, reject})
    },
    confirm(rows) {
      const result = rows ? [rows] : this.$refs['table'].selection()
      if (!result.length)
        return this.$errorMsg('请至少选择一条数据', '提醒', 'warning')
      this.$emit('result', result)
      this.promise && this.promise.resolve(result)
      this.close(!0)
    },
    close(noReject) {
      this.show = !1
      noReject && this.promise && this.promise.reject()
      this.promise = null
    },
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/taskKind/getList', JSON2FormData(this.requestHistory))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    }
  },
  created() {
    this.fields = [
      {label: '序号', prop: 'id', sortable: true, fixedWidth: true, width: 80},
      {label: '类别代号', prop: 'taskKindCode', sortable: true},
      {label: '类别名称', prop: 'taskKindName', sortable: true},
    ]
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.dialog-header .dialog-header-left {
  color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
  padding: 0 30px 0 5px;
  line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
}

.dialog-body i {
  color: inherit !important;
}

.alex-table-ant .el-table table[class^="el-table__"] tr > :last-child .cell{
  text-align: left!important;
}
</style>