<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="500px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="100px"
        size="mini">
        <el-form-item prop="accountAbbr" label="账户简称">
          <el-input v-model="tForm.accountAbbr" v-focus clearable placeholder="账户简称"></el-input>
        </el-form-item>
        <el-form-item prop="accountName" label="账户名称">
          <el-input v-model="tForm.accountName" clearable placeholder="账户名称"></el-input>
        </el-form-item>
        <el-form-item prop="accountNumber" label="银行账号">
          <el-input v-model="tForm.accountNumber" clearable placeholder="银行账号"></el-input>
        </el-form-item>
        <el-form-item prop="bankName" label="银行名称">
          <el-input v-model="tForm.bankName" clearable placeholder="银行名称"></el-input>
        </el-form-item>
        <el-form-item prop="accountType" label="账号类型">
          <el-radio-group v-model="tForm.accountType">
            <el-radio :label="0">无限制账号</el-radio>
            <el-radio :label="1">团队账号</el-radio>
            <el-radio :label="2">部门账号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="tForm.accountType === 1" prop="accountTeamList" label="归属团队">
          <el-select v-model="tForm.accountTeamList" filterable clearable multiple>
            <el-option v-for="team in teamList" :value="team.fWorkTeamId" :key="team.fWorkTeamId" :label="team.fWorkTeamName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="tForm.accountType === 2" prop="accountBranchList" label="归属部门">
          <el-select ref="bsel" clearable @clear="removeBranch(null)" @remove-tag="removeBranch" :value="tForm.accountBranchList" multiple>
            <el-option value="" style="background-color:transparent;font-weight: normal;height:auto">
              <el-tree
                :expand-on-click-node="false"
                :data="departmentList"
                :props="{ children: 'children', label: 'text'}"
                :default-expanded-keys="departmentList[0] ? [(departmentList[0]||{})['f_branch_id']] : null"
                highlight-current
                node-key="id"
                @node-click="departmentChange"
                ref="tree">
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="memo" label="备注">
          <el-input
            type="textarea"
            resize="none"
            :autosize="{minRows: 2, maxRows: 6}"
            v-model="tForm.memo"></el-input>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "OppThemeEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  mounted() {
    this.getTeamList()
    this.getDepartmentList()
  },
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        accountId: '',  //id
        accountAbbr: '',// 账户简称
        bankName: '', //银行名称
        accountName: '', //账户名称
        accountNumber: '', //银行账号
        memo: '', //备注
        accountType: 0,//账号类型：0 无限制账号 1 团队账号 2 部门账号 (可能为null，也默认为0)
        accountTeamList: [],
        accountBranchList: [],
      },
      rules: {
        accountAbbr: [{required: true, message: '请输入账户简称', trigger: ['blur', 'change']}],
        accountName: [{required: true, message: '请输入账户名称', trigger: ['blur', 'change']}],
        accountNumber: [{required: true, message: '请输入银行账号', trigger: ['blur', 'change']}],
        bankName: [{required: true, message: '请输入银行名称', trigger: ['blur', 'change']}]
      },
      title: '收款账号设置',
      teamList: [],
      departmentList: [],
      departmentSelected: [],
    }
  },
  methods: {
    async open(accountId) {
      this._initForm({accountType: 0})
      this.departmentSelected = []
      if (accountId) await this.requestById(accountId)
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        post.accountTeamList = post.accountTeamList ? post.accountTeamList.map(workTeamId => ({workTeamId})) : []
        post.accountBranchList = this.departmentSelected ? this.departmentSelected.map(branchId => ({branchId})) : []
        const {data: {state, message}} = await Axios.post('/fnAccount/save', post)
        if (state !== 1) await Promise.reject(message)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    },
    async requestById(accountId) {
      try {
        const {data: {data, state}} = await Axios.post('/fnAccount/getById', JSON2FormData({accountId}))
        if (state !== 1) await Promise.reject()
        Object.keys(this.tForm).forEach(key => {
          let value = data[key] || ''
          switch (key) {
            case 'accountTeamList':
              value = value || []
              value = value.map(item => item.workTeamId)
              break
            case 'accountBranchList':
              value = value || []
              value = value.map(item => {
                this.departmentSelected.push(item.branchId)
                return item.branchName
              })
              break
            case 'accountType':
              value = value || 0
              break
          }
          this.setForm(key, value)
        })
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
        this.$errorMsg(`数据加载失败`)
      }
    },
    async getTeamList() {
      try {
        const {data} = await Axios.post('/workTeam/getList')
        this.teamList = data
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    },
    async getDepartmentList() {
      try {
        const {data} = await Axios.post('/branch/getTree')
        this.departmentList = data
      } catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }
    },
    departmentChange({id, text}) {
      if (!this.tForm.accountBranchList) this.tForm.accountBranchList = []
      if (!this.departmentSelected) this.departmentSelected = []
      const alreadyIndex = this.departmentSelected.findIndex(item => item === id)
      if (alreadyIndex !== -1) {
        this.tForm.accountBranchList.splice(alreadyIndex, 1)
        this.departmentSelected.splice(alreadyIndex, 1)
      } else {
        this.tForm.accountBranchList.push(text)
        this.departmentSelected.push(id)
      }
      this.$refs.bsel.blur()
    },
    removeBranch(value) {
      if (value === null) {
        this.tForm.accountBranchList = []
        this.departmentSelected = []
        return
      }
      const alreadyIndex = this.tForm.accountBranchList.findIndex(item => item === value)
      if (alreadyIndex !== -1) {
        this.tForm.accountBranchList.splice(alreadyIndex, 1)
        this.departmentSelected.splice(alreadyIndex, 1)
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>