<template>
  <el-container>
    <el-aside width="auto" class="tab-nav" v-if="!hideAside">
      <ul>
        <li
          :class="{active: currentItem && item === currentItem}"
          v-for="item in children" @click="currentItem=item"
          :key="item.id"
        >{{ item.text }}
        </li>
      </ul>
    </el-aside>
    <div v-else style="height: 100vh"></div>
    <el-main class="option-body-wrapper">
      <el-header class="option-body-header">
        <span>{{ title }}</span>
      </el-header>
      <el-main style="flex-grow: 1;padding-bottom: 35px;">
        <div class="option-body-container" v-if="currentItem">
          <component :key="currentItem.id" :is="currentItem.component || 'PlaceHolder'" :config="config" :item="currentItem"></component>
        </div>
      </el-main>
    </el-main>
  </el-container>
</template>

<script>

const TaskKindPanel = importC('TaskKindPanel')
const OppThemePanel = importC('OppThemePanel')
const OppStatePanel = importC('OppStatePanel')
const InviteResultPanel = importC('InviteResultPanel')
const InviteFailureReasonPanel = importC('InviteFailureReasonPanel')
const ReturnVisitFailureReasonPanel = importC('ReturnVisitFailureReasonPanel')
const ProvinceRegionPanel = importC('ProvinceRegionPanel')
const ReturnVisitPanel = importC('ReturnVisitPanel')
const RegisterRolePanel = importC('RegisterRolePanel')
const BlankAccountPanel = importC('BlankAccountPanel')
const InvoiceMainPanel = importC('InvoiceMainPanel')
const InvoiceContentPanel = importC('InvoiceContentPanel')
const InvoiceTypePanel = importC('InvoiceTypePanel')
const ContactResultPanel = importC('ContactResultPanel')
const ItemCodePanel = importC('ItemCodePanel')

export default {
  name: "OptionWrapperPanel",
  components: {
    TaskKindPanel,
    OppThemePanel,
    OppStatePanel,
    InviteResultPanel,
    InviteFailureReasonPanel,
    ReturnVisitFailureReasonPanel,
    ProvinceRegionPanel,
    ReturnVisitPanel,
    RegisterRolePanel,
    BlankAccountPanel,
    InvoiceContentPanel,
    InvoiceTypePanel,
    InvoiceMainPanel,
    ContactResultPanel,
    ItemCodePanel,
    PlaceHolder: '<div style="color: #333;">NOT FOUNT</div>'
  },
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      operEntrance: this.operEntrance
    }
  },
  data() {
    return {
      currentItem: null
    }
  },
  mounted() {
    this.resetCurrentItem()
  },
  computed: {
    children() {
      return this.config.children || []
    },
    title() {
      return this.currentItem ? this.currentItem.text || '' : ''
    },
    operEntrance() {
      return '选项编码' + this.config.text
    },
    hideAside() {
      return !!this.config.hideAside
    }
  },
  watch: {
    children() {
      this.resetCurrentItem()
    }
  },
  methods: {
    resetCurrentItem() {
      if (!this.currentItem && this.children.length) {
        this.currentItem = this.children[0]
      }
    }
  },
}
</script>

<style scoped>
.tab-nav {
  margin: 0;
  list-style: none;
  min-width: 200px;
  max-width: 240px;
  padding: 20px 20px 40px;
  height: 100vh;
}

.tab-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.tab-nav li {
  padding: 8px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  border-radius: 4px;
  margin-bottom: 5px;
}

.tab-nav li.active,
.tab-nav li:hover {
  background-color: rgb(245, 245, 245);
}

.option-body-wrapper {
  padding: 0;
  background-color: rgb(240, 242, 245);
  display: flex;
  flex-direction: column;
}

.option-body-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 30px;
  font-size: 20px;
  letter-spacing: 1px;
  color: #333;
  background-color: #fff;
  flex-shrink: 0;
}

.option-body-container {
  background-color: #fff;
  height: 100%;
}
</style>