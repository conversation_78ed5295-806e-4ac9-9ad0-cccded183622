<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="100px"
        size="mini">
        <el-form-item prop="f_opp_state_name" label="商机阶段">
          <el-input v-model="tForm.f_opp_state_name" v-focus clearable placeholder="商机阶段"></el-input>
        </el-form-item>
        <el-form-item prop="f_state_order" label="排序号">
          <el-input-number :controls="false" v-model="tForm.f_state_order" placeholder="排序号"></el-input-number>
        </el-form-item>
        <el-form-item prop="f_key_point" label="关键点标识">
          <el-select v-model="tForm.f_key_point" placeholder="关键点标识" filterable default-first-option clearable>
            <el-option
              v-for="item in keyPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "OppStateEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  props: {
    keyPoints: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        f_work_team_id: teamId,
        f_opp_state_name: '',
        f_opp_state_id: '',
        f_state_order: '',
        f_key_point: '',
      },
      rules: {
        f_opp_state_name: [{required: true, message: '请输入商机阶段', trigger: ['blur', 'change']}]
      },
      title: '新增商机阶段'
    }
  },
  methods: {
    open(row) {
      this.title = row ? '修改商机阶段' : '新增商机阶段'
      const {
        f_opp_state_name,
        f_opp_state_id,
        f_state_order,
        f_key_point,
        f_work_team_id,
      } = row || {}
      this._initForm(row ? {
        f_opp_state_name,
        f_opp_state_id,
        f_state_order: f_state_order || void 0,
        f_key_point,
        f_work_team_id,
      } : {f_work_team_id: teamId, f_state_order: void 0})
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        if (post.f_state_order === void 0) post.f_state_order = ''
        const {data: {state, message, msg}} = await Axios.post('/OppState/saveE', JSON2FormData(post))
        if (state !== 1) await Promise.reject(msg || message)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>