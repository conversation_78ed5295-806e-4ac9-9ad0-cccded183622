<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="120px"
        size="mini">
        <el-form-item prop="contactResultName" label="联系结果名称">
          <el-input v-model="tForm.contactResultName" v-focus clearable placeholder="联系结果名称"></el-input>
        </el-form-item>
        <el-form-item prop="f_state_order" label="排序号">
          <el-input v-model.number="tForm.showOrder" clearable placeholder="排序号"></el-input>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "ContactResultEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  props: {
    keyPoints: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        workTeamId: teamId,
        contactResultName: '',
        contactResultId: '',
        showOrder: '',
      },
      rules: {
        contactResultName: [{required: true, message: '请输入联系结果名称', trigger: ['blur', 'change']}]
      },
      title: '新增联系结果'
    }
  },
  methods: {
    open(row) {
      this.title = row ? '修改联系结果' : '新增联系结果'
      const {
        contactResultName,
        contactResultId,
        showOrder,
        workTeamId,
      } = row || {}
      this._initForm(row ? {
        contactResultName,
        contactResultId,
        showOrder,
        workTeamId,
      } : {workTeamId: teamId})
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        const {data: {state, message}} = await Axios.post('/contactResult/save', JSON2FormData(post))
        if (state !== 1) await Promise.reject(message)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>