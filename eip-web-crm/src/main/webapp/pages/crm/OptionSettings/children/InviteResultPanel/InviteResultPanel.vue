<template>
  <el-container style="padding:20px;height: 100%;">
    <el-header class="main-body-header" height="50px">
      <div class="left">
        <span style="font-size: 14px;color: #666">当前邀请结果数量： {{ total }}</span>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" @click="addInviteResult">新增</el-button>
      </div>
    </el-header>
    <alex-table
      ref="table"
      :data="tableData"
      style="flex-grow: 1;"
      height="calc(100% - 40px)"
      border
      size="small"
      class="alex-table task-kind-table"
      mode="multiple"
      :fields="fields"
      :operation-width="100"
      :handle-pagination="handlePagination"
      :show-operation="false"
      :total="total"
      :loading="loading">
      <template slot-scope="{row}" slot="options">
        <el-link type="primary" :underline="false" @click.stop="editInviteResult(row)">修改</el-link>
        <el-link type="primary" :underline="false" @click.stop="delInviteResult(row)">删除</el-link>
      </template>
      <template slot-scope="{row}" slot="f_key_point">{{ row.f_key_point|keyPointFilter }}</template>
    </alex-table>
    <invite-result-editor @operation-complete="refresh" ref="editor" :key-points="keyPointList"></invite-result-editor>
  </el-container>
</template>

<script>
const InviteResultEditor = importC('InviteResultEditor')
const keyPointList = [
  {label: '待邀请', value: 'wait'},
  {label: '考虑中', value: 'think'},
  {label: '成功', value: 'success'},
  {label: '失败', value: 'failed'},
  {label: '无', value: ''},
]
export default {
  name: "InviteResultPanel",
  components: {InviteResultEditor},
  mixins: [Mixins.table()],
  inject: ['operEntrance'],
  data() {
    return {
      tableData: [],
      fields: [],
      loading: false,
      total: 0,
      keyPointList,
      teamId,
    }
  },
  methods: {
    async requestTableData() {
      this.loading = true
      try {
        const {
          data: {
            state,
            total,
            rows
          }
        } = await Axios.post('/OppState/getPage', JSON2FormData(Object.assign(this.requestHistory)))
        if (state !== 1) await Promise.reject()
        this.tableData = rows
        this.total = total
      } catch (e) {
        this.$errorMsg()
        console.warn(e)
      } finally {
        this.loading = false
      }
    },
    addInviteResult() {
      this.editInviteResult()
    },
    async delInviteResult({f_opp_state_id}) {
      try {
        await this.$confirm('此操作将删除该阶段, 是否继续?', '提示', {type: 'warning'})
        const {data: {state, msg}} = await Axios.post('/OppState/delete', JSON2FormData({f_opp_state_id, operEntrance: this.operEntrance}))
        if (state !== 1) await Promise.reject(msg)
        this.$message.success('删除成功！')
        this.refresh()
      } catch (e) {
        if (e === 'cancel') return
        e && console.warn(e)
        this.$errorMsg(e || void 0)
      }
    },
    editInviteResult(row) {
      this.$refs.editor.open(row)
    },
  },
  mounted() {
    this.refresh()
  },
  created() {
    this.fields = [
      {label: 'ID', prop: 'f_opp_state_id', sortable: true, fixedWidth: true, width: 200},
      {label: '邀请阶段', prop: 'f_opp_state_name', sortable: true},
      {label: '关键点标识', prop: 'f_key_point', sortable: true},
      {label: '排序号', prop: 'f_state_order', sortable: true, fixedWidth: true, width: 120},
      {label: '操作', prop: 'options', sortable: false, fixedWidth: true, width: 120}
    ]
    this.$set(this.requestHistory, 'f_opp_state_type', 2)
  },
  filters: {
    keyPointFilter(_value) {
      _value = _value || ''
      const current = keyPointList.find(({value}) => _value === value)
      return current ? current.label : ''
    }
  }
}
</script>

<style scoped>
.main-body-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.main-body-header .left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.alex-table.task-kind-table {
  width: 100%;
  padding: 0;
}

.el-table--small div.el-table__header-wrapper th:nth-last-child(2) > .cell {
  text-align: center;
}
</style>