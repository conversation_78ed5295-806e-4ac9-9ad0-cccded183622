<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="saving=false"
    :title="title"
    width="430px">
    <el-main slot="body" class="dialog-body" ref="body">
      <el-header class="dialog-header" height="40px">
        <div></div>
        <div class="dialog-header-right">
          <el-button
            size="mini"
            icon="el-icon-circle-check"
            type="primary"
            :loading="saving"
            @click="saveForm()">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </el-header>
      <el-form
        class="tForm"
        ref="tForm"
        :model="tForm"
        :rules="rules"
        label-width="100px"
        size="mini">
        <el-form-item prop="text" label="商机主题">
          <el-input v-model="tForm.text" v-focus clearable placeholder="商机主题"></el-input>
        </el-form-item>
        <el-form-item prop="order" label="排序号">
          <el-input-number :controls="false" v-model="tForm.order" placeholder="排序号"></el-input-number>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')
export default {
  name: "OppThemeEditor",
  mixins: [Mixins.clearAndInitForm()],
  inject: ['operEntrance'],
  data() {
    return {
      saving: false,
      show: false,
      tForm: {
        text: '',
        order: '',
        id: '',
      },
      rules: {
        text: [{required: true, message: '请输入商机主题', trigger: ['blur', 'change']}]
      },
      title: '新增商机主题'
    }
  },
  methods: {
    open(row) {
      this.title = row ? '修改商机主题' : '新增商机主题'
      const {text, order, id} = row || {}
      this._initForm(row ? {text, order: order || void 0, id} : {order: void 0})
      this.show = true
    },
    async saveForm() {
      this.saving = true
      try {
        await this.validateForm()
        const post = Object.assign({operEntrance: this.operEntrance}, this.tForm)
        if (post.order === void 0) post.order = ''
        const {data: {state, message}} = await Axios.post('/oppTheme/save', JSON2FormData(post))
        if (state !== 1) await Promise.reject(message)
        this.$emit('operation-complete')
        this.$message.success('操作成功！')
        this.show = false
      } catch (err) {
        if (this.handleErrors(err)) {
          this.$errorMsg(err)
          console.warn(err)
        }
      } finally {
        this.saving = false
      }
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 100%;
}

.tForm {
  padding: 20px;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>