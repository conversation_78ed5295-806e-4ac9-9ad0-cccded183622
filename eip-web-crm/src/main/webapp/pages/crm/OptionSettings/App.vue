<template>
  <div>
    <el-tabs
      class="panel-box"
      v-model="activeName"
      tab-position="left"
      type="card"
      :before-leave="beforeLeave"
    >
      <el-tab-pane
        v-for="page in pageMapList"
        :label="page.text"
        :name="page.id"
        :key="page.id"
      >
        <component :is="page.component || 'PlaceHolder'" :config="page"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>

const TradeProdPanel = importC('TradeProdPanel')
const OptionWrapperPanel = importC('OptionWrapperPanel')
/**
 * CompanySize  企业规模
 * nature  企业性质
 * key_market  企业重点市场
 * company_create_time  企业创建时间
 * goodField  联系人擅长领域
 * gusetType  联系人嘉宾类型
 * f_position_id  联系人职位
 * department  联系人部门
 * hobby  联系人兴趣爱好
 * awards  联系人奖项
 * client_source  客户来源
 * client_grade  客户等级
 * SourceType  客户资源类型
 * area  区域
 * ContractWay  联系方式
 * Subject  联系主题
 * job_title  票证兑换职称
 * attract_type  招展类型
 * client_relations  客户关系
 * f_education_id  文化程度
 * f_pass_type_id  护照类型
 * PayWay  付款方式
 * employee_position  职员职位
 * opp_source  商机来源
 * presentation_project_type  发布会项目分类
 * media  媒体名称
 * customer_group  客户群体
 * product_apply_area  产品应用领域
 * business_type	经营类型
 * exhibition_req_doc	参展必备证件
 * affiliation_group	所属组团单位
 * booth_shape	展位形状选择
 * */
const PageMapList = [
  {
    id: 'client',
    text: '客户相关设置',
    component: 'OptionWrapperPanel',
    /**
     * 企业规模
     * 企业性质
     * 企业重点市场
     * 企业创建时间
     * 联系人擅长领域
     * 联系人嘉宾类型
     * 联系人职位
     * 联系人部门
     * 联系人兴趣爱好
     * 联系人奖项
     * 客户来源
     * 客户等级
     * 客户资源类型
     * 省份与区域【原功能】
     * */
    children: [
      {
        id: 'CompanySize',
        text: '企业规模',
        component: 'ItemCodePanel',
      },
      {
        id: 'nature',
        text: '企业性质',
        component: 'ItemCodePanel',
      },
      {
        id: 'key_market',
        text: '企业重点市场',
        component: 'ItemCodePanel',
      },
      {
        id: 'company_create_time',
        text: '企业创建时间',
        component: 'ItemCodePanel',
      },
      {
        id: 'goodField',
        text: '联系人擅长领域',
        component: 'ItemCodePanel',
      },
      {
        id: 'gusetType',
        text: '联系人嘉宾类型',
        component: 'ItemCodePanel'
      },
      {
        id: 'f_position_id',
        text: '联系人职位',
        component: 'ItemCodePanel'
      },
      {
        id: 'department',
        text: '联系人部门',
        component: 'ItemCodePanel'
      },
      {
        id: 'hobby',
        text: '联系人兴趣爱好',
        component: 'ItemCodePanel'
      },
      {
        id: 'awards',
        text: '联系人奖项',
        component: 'ItemCodePanel'
      },
      {
        id: 'client_source',
        text: '客户来源',
        component: 'ItemCodePanel'
      },
      {
        id: 'client_grade',
        text: '客户等级',
        component: 'ItemCodePanel'
      },
      {
        id: 'SourceType',
        text: '客户资源类型',
        component: 'ItemCodePanel'
      },
      {
        id: 'area',
        text: '省份与区域',
        component: 'ProvinceRegionPanel'
      }
    ],
    handler: null,
    authority: 'fu_crm_sys_code_client',
  },
  {
    id: 'trade-product',
    text: '行业及产品类别设置',
    component: 'TradeProdPanel',
    children: null,
    handler: null,
    authority: 'fu_crm_trade_product_type_base_set',
  },
  {
    id: 'customer-group',
    text: '客户分组设置',
    component: null,
    children: null,
    handler: () => top.tree_set(),
    authority: 'fu_setup_customer_group',
  },
  {
    id: 'task-contact',
    text: '任务、联系记录相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'taskKind',
        text: '任务类别',
        component: 'TaskKindPanel',
      },
      {
        id: 'ContractWay',
        text: '联系方式',
        component: 'ItemCodePanel',
      },
      {
        id: 'Subject',
        text: '联系主题',
        component: 'ItemCodePanel',
      },
      {
        id: 'contact-result',
        text: '联系结果',
        component: 'ContactResultPanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_task',
  },
  {
    id: 'opportunity',
    text: '商机相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'opp-theme',
        text: '商机主题',
        component: 'OppThemePanel',
      },
      {
        id: 'opp-state',
        text: '商机阶段',
        component: 'OppStatePanel',
      },
      {
        id: 'opp_source',
        text: '商机来源',
        component: 'ItemCodePanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_opportunity',
  },
  {
    id: 'reg-invite',
    text: '登记与邀请相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'invite-result',
        text: '邀请结果',
        component: 'ContactResultPanel',
      },
      {
        id: 'invite-fail-reason',
        text: '邀请失败原因',
        component: 'InviteFailureReasonPanel',
      },
      {
        id: 'register-role',
        text: '登记身份',
        component: 'RegisterRolePanel',
      },
      {
        id: 'job_title',
        text: '票证兑换职称',
        component: 'ItemCodePanel',
      },
      {
        id: 'affiliation_group',
        text: '所属组团单位',
        component: 'ItemCodePanel',
      },
      {
        id: 'booth_shape',
        text: '展位形状选择',
        component: 'ItemCodePanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_invite_reg',
  },
  {
    id: 'return-visit',
    text: '回访相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'RVType',
        text: '回访类型',
        component: 'ReturnVisitPanel',
      },
      {
        id: 'RVFailReason',
        text: '回访失败原因',
        component: 'ReturnVisitFailureReasonPanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_return_visit',
  },
  {
    id: 'finance',
    text: '财务相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'BlankAccount',
        text: '收款账号',
        component: 'BlankAccountPanel',
      },
      {
        id: 'InvoiceMain',
        text: '开票主体',
        component: 'InvoiceMainPanel',
      },
      {
        id: 'InvoiceContent',
        text: '开票内容',
        component: 'InvoiceContentPanel',
      },
      {
        id: 'InvoiceType',
        text: '发票类型',
        component: 'InvoiceTypePanel',
      },
      {
        id: 'PayWay',
        text: '付款方式',
        component: 'ItemCodePanel',
      }
    ],
    handler: null,
    authority: 'fu_crm_sys_code_finance',
  },
  {
    id: 'contract',
    text: '合同相关选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'client_relations',
        text: '客户关系',
        component: 'ItemCodePanel',
      },
      {
        id: 'attract_type',
        text: '招展类型',
        component: 'ItemCodePanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_contract',
  },
  {
    id: 'exhibitors',
    text: '展商参展信息选项',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'f_pass_type_id',
        text: '护照类型',
        component: 'ItemCodePanel',
      },
      {
        id: 'f_education_id',
        text: '文化程度',
        component: 'ItemCodePanel',
      },
      {
        id: 'presentation_project_type',
        text: '发布会项目分类',
        component: 'ItemCodePanel',
      },
      {
        id: 'media',
        text: '媒体名称',
        component: 'ItemCodePanel',
      },
      {
        id: 'hotel_grade',
        text: '酒店等级',
        component: 'ItemCodePanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_exhibitor',
  },
  {
    id: 'position',
    text: '职员职位设置',
    hideAside: true, // 只有一个先隐藏左边
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'employee_position',
        text: '职员职位',
        component: 'ItemCodePanel',
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_employee_position',
  },
  {
    id: 'zzyuser',
    text: '会员选项设置',
    component: 'OptionWrapperPanel',
    children: [
      {
        id: 'customer_group',
        text: '客户群体',
        component: 'ItemCodePanel',
        type: 'inferior-tree',
      },
      {
        id: 'product_apply_area',
        text: '产品应用领域',
        component: 'ItemCodePanel',
        type: 'inferior-tree',
      },
      {
        id: 'business_type',
        text: '经营类型',
        component: 'ItemCodePanel',
      },
      {
        id: 'exhibition_req_doc',
        text: '参展必备证件',
        component: 'ItemCodePanel'
      },
    ],
    handler: null,
    authority: 'fu_crm_sys_code_member_option',
  },
]
const PAGE_STORAGE_KEY = 'os2-page'


export default {
  name: "App",
  data() {
    return {
      pageMapList: PageMapList.filter(v => {
        const authority = v.authority
        if (typeof authority === 'boolean') return authority
        if (typeof authority === 'string') return getAuthority(authority)
        if (typeof authority === 'function') return authority()
        return false
      }),
      activeName: '',
      currentComponent: 'PlaceHolder'
    }
  },
  mounted() {
    if (!this.pageMapList.length) return this.$errorMsg('权限不足!')
    // 没传正确的值,取有权限的第一个
    const pf = page || sessionStorage[PAGE_STORAGE_KEY]
    const currentPage = this.pageMapList.find(it => pf === it.id)
    if (!currentPage) return (this.activeName = this.pageMapList[0].id)
    this.activeName = currentPage.id
  },
  methods: {
    beforeLeave(activeName) {
      const current = this.pageMapList.find(it => activeName === it.id)
      if (!current) return false
      if (typeof current.handler === 'function') {
        current.handler()
        return false
      }
      this.currentComponent = current.component
      sessionStorage.setItem(PAGE_STORAGE_KEY, activeName)
      return true
    }
  },
  components: {
    TradeProdPanel,
    OptionWrapperPanel,
    PlaceHolder: {template: '<div style="height: 100vh;display:flex;align-items:center;justify-content:center;color: #aaa">暂无数据</div>'}
  }
}
</script>

<style scoped>

.el-tabs__header.is-left, .el-tabs__header.is-left * {
  border: none !important;
}

.el-tabs--left .el-tabs__header.is-left {
  width: 238px;
  height: calc(100vh - 50px);
  background-color: #fafafa;
  margin-right: 0;
  padding-bottom: 40px;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  padding: 0 0 0 40px;
  line-height: 40px;
  user-select: none;
  transition: all .3s;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  background: no-repeat 20% center;
  background-size: 16px;
}

.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active,
.el-tabs--left.el-tabs--card .el-tabs__item.is-left:hover {
  background-color: #e9f1fd;
  color: #2e82e4;
}


.el-tabs__nav-prev,
.el-tabs__nav-next {
  color: #409EFF;
  font-size: 20px;
}
</style>