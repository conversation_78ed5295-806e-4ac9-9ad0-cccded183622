<!doctype html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>选项编码设置</title>
  <script src="../../../vue/vue.js"></script>
  <!--<script src="../../../js/vue2.dev.js"></script>-->
  <script src="../../../vue/httpVueLoader.js"></script>
  <script src="../../../vue/directive/dialogDrag.js?v=23.1"></script>
  <script src="../../../vue/axions.js"></script>
  <script src="../../../vue/element-ui/index.js"></script>
  <link rel="stylesheet" href="../VueCommonComponents/base.css">
  <link rel="stylesheet" href="../../../vue/element-ui/index.css">
  <script src="../js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="../common/utils.js?v=241025"></script>
  <script src="js/index.js?v=250623"></script>
  <script src="../VueCommonComponents/runtime.js"></script>
  <script src="../VueCommonComponents/mixins.js?v=250122"></script>
</head>
<body>
<div id="app"><App/></div>
<div id="load"
     v-loading.fullscreen.lock="loading"
     element-loading-text="拼命加载中"
     element-loading-spinner="el-icon-loading"
     element-loading-background="rgba(0, 0, 0, 0.1)">
</div>
<script>
  const teamId = getQueryString("teamId") //团队id
  const page = getQueryString('page') || sessionStorage.getItem('os2-page') || '' // 页面标识
  window.run()
</script>
</body>
</html>