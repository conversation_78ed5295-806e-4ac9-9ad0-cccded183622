package com.eip.web.business.controller.api.webSiteApi.tradeSite;

import com.eip.common.entity.PageParam;
import com.eip.common.exception.BusinessException;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.ServeInquiryDto;
import com.eip.facade.vip.dto.ZzyUserDto;
import com.eip.facade.vip.entity.ServeInquiry;
import com.eip.facade.vip.entity.ServeInquiryMsg;
import com.eip.facade.vip.entity.ServeProduct;
import com.eip.facade.vip.service.ServeInquiryMsgService;
import com.eip.facade.vip.service.ServeInquiryService;
import com.eip.facade.vip.service.ServeProductService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.facade.vip.vo.inquiry.InquiryContentVo;
import com.eip.facade.vip.vo.inquiry.InquiryPersonInfoVo;
import com.eip.web.business.common.CommonController;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2025-01-13 13:52
 * @DESCRIPTION: 询盘第三方接口
 */
@RestController
@RequestMapping(value = "/api/set/inquiry")
public class ApiSiteServeInquiryController extends CommonController {

    @Autowired
    private ServeInquiryService serveInquiryService;
    @Autowired
    private ServeInquiryMsgService serveInquiryMsgService;
    @Autowired
    private ServeProductService serveProductService;


    /**
     * 查询询盘人个人信息
     * @param serveInquiryDto
     * @return
     */
    @RequestMapping(value = "/queryInquiryInfo",method = RequestMethod.POST)
    public AjaxResponse queryInquiryInfo(ServeInquiryDto serveInquiryDto) {
        AuthLoginRespVo userInfo = getMemberLoginInfo();
        if(serveInquiryDto.getServeProductId() == null) return AjaxResponse.create(ParamExceptionEnum.SERVE_PRODUCT_ID_EMPTY);
        ServeProduct serveProduct = serveProductService.selectById(serveInquiryDto.getServeProductId());
        if(Objects.isNull(serveProduct)) return AjaxResponse.create(ParamExceptionEnum.SERVE_PRODUCT_EMPTY);
        serveInquiryDto.setOrgNum(userInfo.getFocusOrgNum());
        serveInquiryDto.setZzyUserId(userInfo.getZzyUserId());
        return serveInquiryService.queryInquiryInfo(serveInquiryDto);
    }

    /**
     * 更新询盘人信息
     * @param serveInquiry
     * @return
     */
    @RequestMapping(value = "/saveInquiryInfo",method = RequestMethod.POST)
    public AjaxResponse addInquiry(ServeInquiry serveInquiry) {
        if(StringUtils.isBlank(serveInquiry.getInquiryPeople())) throw new BusinessException(ParamExceptionEnum.INQUIRY_NAME_IS_BLANK);
        if(StringUtils.isBlank(serveInquiry.getInquiryPhone())) throw new BusinessException(ParamExceptionEnum.INQUIRY_MOBILE_IS_BLANK);
        if(serveInquiry.getServeProductId() == null) throw new BusinessException(ParamExceptionEnum.SERVE_PRODUCT_ID_EMPTY);
        ServeProduct serveProduct = serveProductService.selectById(serveInquiry.getServeProductId());
        if(Objects.isNull(serveProduct)) return AjaxResponse.create(ParamExceptionEnum.SERVE_PRODUCT_EMPTY);
        AuthLoginRespVo userInfo = getMemberLoginInfo();
        serveInquiry.setOrgNum(userInfo.getFocusOrgNum());
        serveInquiry.setZzyuserId(userInfo.getZzyUserId());
        return serveInquiryService.saveInquiryInfo(serveInquiry);
    }

    /**
     * 发送询盘消息
     * @param serveInquiryMsg
     * @return
     */
    @RequestMapping(value = "/saveInquiryMsg", method = {RequestMethod.POST})
    public AjaxResponse addInquiryMsg(ServeInquiryMsg serveInquiryMsg){
        AuthLoginRespVo userInfo = getMemberLoginInfo();
        if(serveInquiryMsg.getCommonId()==null) throw new BusinessException(ParamExceptionEnum.SERVE_PRODUCT_ID_EMPTY);
        if(StringUtils.isBlank(serveInquiryMsg.getMsgContent())) throw new BusinessException(ParamExceptionEnum.INQUIRY_CONTENT_EMPTY);
        if(StringUtils.isBlank(serveInquiryMsg.getInquiryType())) throw new BusinessException(ParamExceptionEnum.INQUIRY_ROLE_EMPTY);
        ServeProduct serveProduct = serveProductService.selectById(serveInquiryMsg.getCommonId());
        if(Objects.isNull(serveProduct)) return AjaxResponse.create(ParamExceptionEnum.SERVE_PRODUCT_EMPTY);
        serveInquiryMsg.setOrgNum(userInfo.getFocusOrgNum());
        serveInquiryMsg.setSenderId(userInfo.getZzyUserId());//发送方Id
        return serveInquiryMsgService.saveInquiryMsg(serveInquiryMsg);
    }

    /**
     * 展商自助中心点击查看跳转到线上网站对应的产品详情页面 调取接口
     * @return
     */
    @RequestMapping(value = "/locateInquiryMsg",method = RequestMethod.POST)
    public AjaxResponse locateInquiryMsg(ZzyUserDto zzyUserDto) {
        AuthLoginRespVo userInfo = getMemberLoginInfo();
        if(zzyUserDto.getServeInquiryJoinId() == null) return AjaxResponse.create(ParamExceptionEnum.SERVE_INQUIRY_JOIN_ID_EMPTY);
        InquiryPersonInfoVo inquiryPersonInfoVo = serveInquiryService.locateInquiryMsg(zzyUserDto);
        return AjaxResponse.success(inquiryPersonInfoVo);
    }

    /**
     * 刷新接口
     * @return
     */
    @RequestMapping(value = "/refreshInquiry",method = RequestMethod.POST)
    public AjaxResponse refreshInquiry(PageParam pageParam,ZzyUserDto zzyUserDto) {
        AuthLoginRespVo userInfo = getMemberLoginInfo();
        if(zzyUserDto.getServeInquiryJoinId() == null) return AjaxResponse.create(ParamExceptionEnum.SERVE_INQUIRY_JOIN_ID_EMPTY);
        PageInfo<InquiryContentVo> pageInfo = serveInquiryMsgService.refreshInquiry(pageParam,zzyUserDto);
        return AjaxResponse.pageSuccess(pageInfo.getTotal(),pageInfo.getList());
    }

}
