/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-07-01 14:35
 * @description：
 */
package com.eip.web.business.controller.api.webSiteApi.site10000;

import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.CatalogueDto;
import com.eip.facade.vip.service.CatalogueService;
import com.eip.facade.vip.vo.MerchantVo;
import com.eip.web.business.common.CommonController;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-07-01 14:35
 * @description： 10000机构号下的行业网站
 */
@RestController
@RequestMapping(value = "/api/merchant")
public class ApiMerchantController extends CommonController {

    @Autowired
    private CatalogueService catalogueService;


    /**
     * 行业网站 推荐商家以及商家列表
     * @param pageParam
     * @param catalogueDto
     * @return
     */
    @RequestMapping(value = "/getPage",method = RequestMethod.POST)
    public AjaxResponse getPage(PageParam pageParam,CatalogueDto catalogueDto){
        if(catalogueDto.getPid()==null){
            return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        }
        List<MerchantVo> catalogueList = Lists.newArrayList();
        int count = catalogueService.countMerchant(catalogueDto);
        if(count > 0){
            catalogueList = catalogueService.getMerchantPage(pageParam,catalogueDto);
        }
        return AjaxResponse.pageSuccess(count,null,catalogueList);
    }

    /**
     * 行业网站 根据会刊Id查询会刊详情
     * @return
     */
    @RequestMapping(value = "/queryCataDetail/{cataId}",method = RequestMethod.POST)
    public AjaxResponse getPage(@PathVariable(value = "cataId") Integer cataId){
        if(cataId==null){
            return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        }
        MerchantVo catalogue = catalogueService.queryCataDetail(cataId);
        return AjaxResponse.success(catalogue);
    }


}
