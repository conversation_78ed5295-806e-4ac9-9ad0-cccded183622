/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-07-01 13:57
 * @description：
 */
package com.eip.web.business.controller.api.webSiteApi.site10000;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.NewDto;
import com.eip.facade.vip.entity.News;
import com.eip.facade.vip.entity.NewsType;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.service.NewsService;
import com.eip.facade.vip.service.NewsTypeService;
import com.eip.facade.vip.service.ProjectService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-07-01 13:57
 * @description： 获取新闻
 */
@RestController
@RequestMapping(value = "/api/new")
public class ApiNewsController extends BaseController {
    @Autowired
    private NewsService newsService;
    @Autowired
    private NewsTypeService newsTypeService;
    @Autowired
    private ProjectService projectService;

    /**
     * 获取新闻动态
     * @return
     */
    @RequestMapping(value = "/queryNews",method = RequestMethod.POST)
    public AjaxResponse queryNews(PageParam pageParam,NewDto newDto){
        if(newDto.getPid() == null){ return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS); }
        Project project = projectService.getById(newDto.getPid());
        if(Objects.isNull(project)){ return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST); }
        newDto.setOrgNum(project.getF_org_num());
        PageInfo<News> newsList = newsService.getPage(pageParam,newDto);
        return AjaxResponse.pageSuccess(newsList.getTotal(),null,newsList.getList());
    }

    /**
     * 根据新闻Id查询
     * @return
     */
    @RequestMapping(value = "/getById",method = RequestMethod.POST)
    public AjaxResponse getById(NewDto newDto){
        if(newDto.getPid() == null || newDto.getNewsId()==null){ return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS); }
        Project project = projectService.getById(newDto.getPid());
        if(Objects.isNull(project)){ return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST); }
        newDto.setOrgNum(project.getF_org_num());
        News news = newsService.getById(newDto);
        return AjaxResponse.success(news);
    }

    /**
     * 获取新闻类别
     * @return
     */
    @RequestMapping(value = "/queryNewsType",method = RequestMethod.POST)
    public AjaxResponse queryNewsType(PageParam pageParam,NewDto newDto){
        if(StringUtils.isBlank(newDto.getNewsTypeCode()) || newDto.getPid() == null){
            return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        }
        Project project = projectService.getById(newDto.getPid());
        if(Objects.isNull(project)){
            return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST);
        }
        newDto.setOrgNum(project.getF_org_num());
        List<NewsType> newsTypeList = newsTypeService.queryNewsType(newDto);
        return AjaxResponse.success(newsTypeList);
    }


    @RequestMapping(value = "/selectNewsTypeList", method = {RequestMethod.POST})
    public AjaxResponse selectNewsTypeList(@RequestParam("projectId") Integer projectId, String typeCode){
        try {
            NewsType newsType = new NewsType();
            newsType.setProjectId(projectId);
            newsType.setTypeCode(typeCode);
            List<NewsType> newsTypes = newsTypeService.selectNewsTypeList(newsType);
            return AjaxResponse.success(newsTypes);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return AjaxResponse.failure();
        }
    }


}
