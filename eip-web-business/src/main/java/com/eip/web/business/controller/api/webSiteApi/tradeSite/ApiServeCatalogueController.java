package com.eip.web.business.controller.api.webSiteApi.tradeSite;

import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.page.wrapper.Wrapper;
import com.eip.common.util.request.RequestUtil;
import com.eip.facade.vip.dto.MemberVisitLogDto;
import com.eip.facade.vip.dto.api.ApiServeCatalogueDto;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.service.api.ApiServeCatalogueService;
import com.eip.facade.vip.service.api.ApiServeCompanyService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeCataDtlVo;
import com.eip.facade.vip.vo.api.webSiteVo.ApiServeCatalogueVo;
import com.eip.web.business.async.ZzyUserAwokeAsync;
import com.eip.web.business.common.CommonController;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @description：api展商接口
 */
@RestController
@RequestMapping("/api/set/serveCatalogue")
public class ApiServeCatalogueController extends CommonController {

    @Autowired
    private ApiServeCatalogueService apiServeCatalogueService;
    @Autowired
    private ApiServeCompanyService apiServeCompanyService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ZzyUserAwokeAsync zzyUserAwokeAsync;

    /**
     * PC  后期PC和手机统一使用该接口
     * 根据行业类别获取展商列表（关联会刊的信息）
     * @param pageParam
     * @param apiServeCatalogueDto
     */
    @RequestMapping(value = "/selectByTypeFocusCata", method = RequestMethod.POST)
    public AjaxResponse getListFocusCataByType(PageParam pageParam,ApiServeCatalogueDto apiServeCatalogueDto) throws ParseException {
        if(apiServeCatalogueDto.getBrandProjectId()==null){
            if(StringUtils.isBlank(apiServeCatalogueDto.getExhibitCode())){
                if(apiServeCatalogueDto.getProjectId()==null) return AjaxResponse.paramMiss();
                if(0 == apiServeCatalogueDto.getProjectId()) return AjaxResponse.create(CommonResponseStateEnum.PARAMETER_INVALID);
                Project byId = projectService.getById(apiServeCatalogueDto.getProjectId());
                if(Objects.isNull(byId)) return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST);
                if(StringUtils.isBlank(byId.getF_exhibit_code())) return AjaxResponse.failure("项目未关联展会");
                apiServeCatalogueDto.setExhibitCode(byId.getF_exhibit_code());
            }
        }
        PageInfo<ApiServeCatalogueVo> catalogueDtoPageInfo = apiServeCatalogueService.selectByTypeFocusCata(pageParam, apiServeCatalogueDto);
        return AjaxResponse.pageSuccess(catalogueDtoPageInfo.getTotal(), catalogueDtoPageInfo.getList());
    }

    /**
     * 查询单个展商（关联会刊的信息）
     * @param apiServeCatalogueDto
     */
    @RequestMapping(value = "/getMoreByIdFocusCata", method = RequestMethod.POST)
    public AjaxResponse getServeCataDtlById(ApiServeCatalogueDto apiServeCatalogueDto){
        if(apiServeCatalogueDto.getServeCatalogueId() == null) return AjaxResponse.failure("会刊Id为空");
        ApiServeCataDtlVo serveCataDtlVo = apiServeCatalogueService.getServeCataDtlById(apiServeCatalogueDto);
        //更新阅读量
        if(Objects.nonNull(serveCataDtlVo)){
            apiServeCompanyService.updateReadNumUpOne(serveCataDtlVo.getServeCompanyId());
        }
        try {
            AuthLoginRespVo userInfo = getUserInfo();
            if(Objects.nonNull(serveCataDtlVo) && Objects.nonNull(userInfo)){
                MemberVisitLogDto build = MemberVisitLogDto.builder()
                        .cataId(apiServeCatalogueDto.getServeCatalogueId())
                        .zzyUserId(userInfo.getZzyUserId())
                        .orgNum(userInfo.getFocusOrgNum())
                        .mobileClientFlag(RequestUtil.isMobileClient())
                        .visitIp(RequestUtil.getClientIP())
                        .build();
                zzyUserAwokeAsync.saveVisitServeCataDtlLog(build);
            }
        }catch (Exception e){
            logger.error("记录访问会刊详情日志异常：{}",e);
        }
        return AjaxResponse.success(serveCataDtlVo);
    }

    /**
     * 获取首页的热门推荐展品展商
     * @param pid
     * @return
     */
    @RequestMapping(value = "/getHotComAndProduct",method = RequestMethod.POST)
    public Wrapper<Object> getHotComAndProduct(Integer pid, Integer page, Integer rows) {
        if(pid == null) return errorResult("项目Id为空");
        PageInfo<ApiServeCatalogueDto> serveCatalogueDtos = apiServeCatalogueService.getHotComAndProduct(pid, page, rows);
        return pageHandleResult(serveCatalogueDtos.getList(), serveCatalogueDtos.getPages(), serveCatalogueDtos.getTotal());
    }





    //============================================================接口不维护==========================================================================

    /**
     * PC
     * 根据关键词(产品名和公司名)搜索商户(默认搜索中文)
     * @param pageParam
     * @param apiServeCatalogueDto
     * @param languageType CN中文版 EN英文版
     * @return
     */
    @RequestMapping(value = "/selectByKeyword", method = RequestMethod.POST)
    public AjaxResponse selectByKeyword(PageParam pageParam, ApiServeCatalogueDto apiServeCatalogueDto,@RequestParam("languageType") String languageType) throws ParseException {
        if(apiServeCatalogueDto.getProjectId()==null) return AjaxResponse.failure("项目ID不能为空");
        PageInfo<ApiServeCatalogueDto> list = apiServeCatalogueService.selectByKeyword(pageParam, apiServeCatalogueDto,languageType);
        // 将dto中的数据拷贝到vo中
        List<ApiServeCatalogueDto> apiServeCatalogueDtos = list.getList();
        List<ApiServeCatalogueVo> apiServeCatalogueVos = dtoVo(apiServeCatalogueDtos);
        return AjaxResponse.pageSuccess(list.getTotal(), apiServeCatalogueVos);
    }


    /**
     * 手机端 后期慢慢废弃该接口
     * 获取展商列表
     * @param pageParam
     * @param apiServeCatalogueDto
     * @return
     */
    @RequestMapping(value = "/getMorePageFocusCata", method = RequestMethod.POST)
    public AjaxResponse getMorePage(PageParam pageParam, ApiServeCatalogueDto apiServeCatalogueDto) {
        if(StringUtils.isBlank(apiServeCatalogueDto.getExhibitCode())) return AjaxResponse.failure("展会Code不能为空");
        PageInfo<ApiServeCatalogueDto> list = apiServeCatalogueService.selectMorePageFocusCata(pageParam, apiServeCatalogueDto);
        return AjaxResponse.pageSuccess(list.getTotal(), list.getList());
    }


    /**
     * 将dto转换为vo
     * @param apiServeCatalogueDtos
     * @return
     */
    private List<ApiServeCatalogueVo> dtoVo(List<ApiServeCatalogueDto> apiServeCatalogueDtos) {
        // 将dto中的数据拷贝到vo中
        List<ApiServeCatalogueVo> apiServeCatalogueVos = new ArrayList<>();
        for (ApiServeCatalogueDto apiServeCatalogueDto1 : apiServeCatalogueDtos) {
            ApiServeCatalogueVo apiServeCatalogueVo = new ApiServeCatalogueVo();
            BeanUtils.copyProperties(apiServeCatalogueDto1, apiServeCatalogueVo);
            apiServeCatalogueVos.add(apiServeCatalogueVo);
        }
        return apiServeCatalogueVos;
    }

    //=============================================================接口不维护=========================================================================

}
