package com.eip.web.business.filter;

import com.alibaba.druid.util.ServletPathMatcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @USER: zwd
 * @DATE: 2025-01-06 14:57
 * @DESCRIPTION: xss过滤器
 */
@Slf4j
public class XssFilter implements Filter {


    /**
     * 静态文件查询不拦截
     */
    private Set<String> exclusionStaticFilesSets;

    /**
     * 排除不拦截URL
     */
    private Set<String> exclusionUrlsSets;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String exclusionStaticFiles = filterConfig.getInitParameter("exclusionStaticFiles");
        if (StringUtils.isNotBlank(exclusionStaticFiles)) {
            exclusionStaticFilesSets = new HashSet(Arrays.asList(exclusionStaticFiles.split("\\s*,\\s*")));
        }
        String exclusionUrls = filterConfig.getInitParameter("exclusionUrls");
        if (StringUtils.isNotBlank(exclusionUrls)) {
            exclusionUrlsSets = new HashSet(Arrays.asList(exclusionUrls.split("\\s*,\\s*")));
        }
    }


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        if (handleExcludeURL(req, resp)) {
            chain.doFilter(request, response);
            return;
        }
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper((HttpServletRequest) request);
        chain.doFilter(xssRequest, response);
    }

    private boolean handleExcludeURL(HttpServletRequest request, HttpServletResponse response) {
        String method = request.getMethod();
        // GET DELETE 不过滤
        if (method == null || method.matches("GET") || method.matches("DELETE")) return true;

        //原始路径：http://localhost:9010/eip-web-vip/zzyUser/selectZzyUserInfoById

//        String requestURI = request.getRequestURI();
//        log.info("requestURI1:{}",requestURI);  //结果：eip-web-vip/zzyUser/selectZzyUserInfoById
//        String contextPath = request.getContextPath();
//        log.info("contextPath:{}",contextPath); //结果：/eip-web-vip
        String servletPath = request.getServletPath();
//        log.info("servletPath:{}",url);  //结果：/zzyUser/selectZzyUserInfoById

        if(StringUtils.isBlank(servletPath)) return true;

//        if (contextPath != null && requestURI.startsWith(contextPath)) {
//            requestURI = requestURI.substring(contextPath.length());
//            if (!requestURI.startsWith("/")) {
//                requestURI = "/" + requestURI;
////                log.info("requestURI2:{}",requestURI);
//            }
//        }
        servletPath = StringUtils.lowerCase(servletPath);
        if(CollectionUtils.isNotEmpty(exclusionStaticFilesSets)){
            for(String modeStr : exclusionStaticFilesSets){
                //说明匹配了要排除的静态文件请求 直接跳过
                if(ServletPathMatcher.getInstance().matches(modeStr,servletPath)) return true;
            }
        }
        if(CollectionUtils.isNotEmpty(exclusionUrlsSets)){
            for(String modeStr : exclusionUrlsSets){
                //说明匹配了要排除的相关接口 直接跳过
                if(ServletPathMatcher.getInstance().matches(modeStr,servletPath)) return true;
            }
        }
        return false;
    }

    @Override
    public void destroy() {

    }
}
