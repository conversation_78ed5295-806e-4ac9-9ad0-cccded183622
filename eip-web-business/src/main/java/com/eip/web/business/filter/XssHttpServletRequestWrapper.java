package com.eip.web.business.filter;

import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

/**
 * @USER: zwd
 * @DATE: 2025-01-06 14:58
 * @DESCRIPTION: xss过滤器包装
 */
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);
        if (values == null) {
            return null;
        }
        int length = values.length;
        String[] escapseValues = new String[length];
        for (int i = 0; i < length; i++) {
            // 防xss攻击和过滤前后空格
//            escapseValues[i] = EscapeUtil.clean(values[i]).trim();//这个是强制清除html的字符
//            escapseValues[i] = StringUtils.trim(cleanXss(values[i]));//这个只是进行转码
            escapseValues[i] = StringUtils.trim(values[i]);//清除前后空格
        }
        return escapseValues;
    }

//    @Override
//    public String getParameter(String parameter) {
//        String value = super.getParameter(parameter);
//        if (value == null) {
//            return null;
//        }
//        return cleanXss(value);
//    }
//
//    @Override
//    public String getHeader(String name) {
//        String value = super.getHeader(name);
//        if (value == null) {
//            return null;
//        }
//        return cleanXss(value);
//    }


    private String cleanXss(String value) {

        value = value.replaceAll("<", "& lt;").replaceAll(">", "& gt;");

        value = value.replaceAll("\\(", "& #40;").replaceAll("\\)", "& #41;");

        value = value.replaceAll("'", "& #39;");

        value = value.replaceAll("eval\\((.*)\\)", "");

        value = value.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");

        return value;

    }

}
