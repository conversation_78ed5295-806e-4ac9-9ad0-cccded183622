package com.eip.web.business.controller.api;

import com.eip.common.entity.PageParam;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.InvitationDto;
import com.eip.facade.vip.entity.Exhibition;
import com.eip.facade.vip.entity.ProjectInformation;
import com.eip.facade.vip.service.ExhibitionService;
import com.eip.facade.vip.service.InvitationService;
import com.eip.facade.vip.service.ProjectInformationService;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.vo.InvitationRankingVo;
import com.eip.facade.vip.vo.InvitationVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-05-16 13:39
 * @DESCRIPTION:邀请函
 */
@RestController
@RequestMapping(value = "/api/invitation")
public class ApiInvitationController {

    @Autowired
    private InvitationService invitationService;
    @Autowired
    private ProjectInformationService projectInformationService;
    @Autowired
    private ExhibitionService exhibitionService;
    @Autowired
    private ProjectService projectService;

    /**
     * 获取展商邀请函详情
     * @param invitationDto
     * @return
     */
    @RequestMapping(value = "/getInvitationDtl",method = RequestMethod.POST)
    public AjaxResponse getInvitationDtl(InvitationDto invitationDto){
        if(invitationDto.getInvitationId() == null) return AjaxResponse.create(ParamExceptionEnum.INVITATION_ID_EMPTY);
        InvitationVo invitationVo = invitationService.getInvitationDtl(invitationDto);
        return AjaxResponse.success(invitationVo);
    }

    /**
     * 更新邀请函浏览量
     * @param invitationDto
     * @return
     */
    @RequestMapping(value = "/updateInvitationReadNum",method = RequestMethod.POST)
    public AjaxResponse updateInvitationReadNum(InvitationDto invitationDto){
        if(invitationDto.getInvitationId() != null) {
            //if(BooleanUtils.isTrue(invitationDto.getReqFlag())){
                invitationService.updateReadNumUpOne(invitationDto.getInvitationId());
            //}
        }
        return AjaxResponse.success();
    }
    /**
     * 邀请函热度排行
     * @return
     */
    @RequestMapping(value = "/selectInviteHotNumber",method = RequestMethod.POST)
    public AjaxResponse selectInviteHotNumber(PageParam pageParam,InvitationDto invitationDto) {
        if(StringUtils.isBlank(invitationDto.getExhibitCode())) return AjaxResponse.create(ParamExceptionEnum.EXHIBIT_CODE_EMPTY);
        Exhibition exhibition = exhibitionService.getOne(invitationDto.getExhibitCode());
        if(Objects.isNull(exhibition)) return AjaxResponse.create(ParamExceptionEnum.EXHIBITION_NOT_EXIST);
        invitationDto.setVersion(invitationDto.getVersion()!=null ? invitationDto.getVersion() : 1);
        PageInfo<InvitationRankingVo> pageInfo = invitationService.selectInvitationHotRanking(invitationDto,pageParam);
        //邀请函活动排行榜设置
        List<ProjectInformation> projectInformationList = Lists.newArrayList();
        if(exhibition.getF_project_id()!=null){
            Map<String, Object> hashMap = Maps.newHashMapWithExpectedSize(6);
            hashMap.put("types","113,114");
            if(invitationDto.getVersion()!=null && 2 == invitationDto.getVersion()){
                hashMap.put("types","113,114,1008,1009");
            }
            hashMap.put("projectId",exhibition.getF_project_id());
            projectInformationList = projectInformationService.selectProjectInformation(hashMap);
        }
        return AjaxResponse.pageSuccess(pageInfo.getTotal(),projectInformationList,pageInfo.getList());
    }

    /**
     * 记录邀请函分享链接异常日志
     * @param invitationDto
     * @return
     */
    @RequestMapping(value = "/saveExhibitorInviteLinkLog",method = RequestMethod.POST)
    public AjaxResponse saveExhibitorInviteLinkLog(InvitationDto invitationDto){
        invitationService.saveInvitationEventInfo(invitationDto);
        return AjaxResponse.success();
    }
}
