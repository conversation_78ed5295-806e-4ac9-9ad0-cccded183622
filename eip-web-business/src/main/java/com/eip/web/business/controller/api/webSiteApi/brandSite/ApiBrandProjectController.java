package com.eip.web.business.controller.api.webSiteApi.brandSite;

import com.eip.common.constant.PageConstant;
import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.request.RequestUtil;
import com.eip.facade.vip.dto.ExhibitDocDto;
import com.eip.facade.vip.dto.MemberVisitLogDto;
import com.eip.facade.vip.dto.NewDto;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.service.*;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.facade.vip.vo.api.brandWebSiteVo.*;
import com.eip.web.business.async.ZzyUserAwokeAsync;
import com.eip.web.business.common.CommonController;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-07-19 14:26
 * @DESCRIPTION: 品牌首页项目
 */
@RestController
@RequestMapping(value = "/api/set/brand")
public class ApiBrandProjectController extends CommonController {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private NewsService newsService;
    @Autowired
    private ExhibitDocService exhibitDocService;
    @Autowired
    private CatalogCompanyProductService catalogCompanyProductService;
    @Autowired
    private ZzyuserService zzyuserService;
    @Autowired
    private ZzyUserAwokeAsync zzyUserAwokeAsync;

    //====================================首页======================================================

    /**
     * 获取首页项目
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getHomePageBrandProject",method = RequestMethod.POST)
    public AjaxResponse getHomePageProject(ProjectDto projectDto){
        if(projectDto.getProjectId() == null) return AjaxResponse.failure("项目Id为空");
        if(0 == projectDto.getProjectId()) return AjaxResponse.failure("项目Id非法");
        List<BrandProjectVo> brandProjectVos = projectService.getHomePageProject(projectDto);
        return AjaxResponse.success(brandProjectVos);
    }

    /**
     * 获取项目层级
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getProjectLevel",method = RequestMethod.POST)
    public AjaxResponse getProjectLevel(ProjectDto projectDto){
        if(projectDto.getProjectId() == null) return AjaxResponse.failure("项目Id为空");
        if(0 == projectDto.getProjectId()) return AjaxResponse.failure("项目Id非法");
        projectDto.setQueryProjectLevel(true);
        projectDto.setQueryHorizontalFlag(true);
        List<BrandProjectVo> brandProjectVos = projectService.getHomePageProject(projectDto);
        return AjaxResponse.success(brandProjectVos);
    }

    //====================================品牌网站搜索======================================================

    /**
     * 品牌网站全局搜索
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getSearchLabels",method = RequestMethod.POST)
    public AjaxResponse getSearchLabels(NewDto newDto){
        if(StringUtils.isBlank(newDto.getExhibitCode())) return AjaxResponse.failure("展会Code为空");
        if(StringUtils.isBlank(newDto.getSearchContent())) return AjaxResponse.failure("搜索内容为空");
        if(StringUtils.isBlank(newDto.getNewTypeIds()) && StringUtils.isBlank(newDto.getNewGroupIds()))
            return AjaxResponse.failure("栏目Id和分组Id不能同时为空");
        List<SearchLabelVo> searchLabels = Lists.newArrayList();
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(8);
        //新闻
        paramMap.put("exhibitCode",newDto.getExhibitCode());
        paramMap.put("searchContent",newDto.getSearchContent());
        paramMap.put("newTypeIds",newDto.getNewTypeIds());
        paramMap.put("newGroupIds",newDto.getNewGroupIds());
        paramMap.put("newGroupIdParamFlag",false);
        //栏目和分组 只能传递一个 同时传后端默认栏目
        if(StringUtils.isNotBlank(newDto.getNewGroupIds())){
            paramMap.put("newGroupIdParamFlag",true);
        }
        List<SearchLabelVo> newSearchLabels = newsService.getSearchLabels(paramMap);
        //剔除为空的新闻栏目分组
//        if(CollectionUtils.isNotEmpty(newSearchLabels)){
//            Iterator<SearchLabelVo> iterator = newSearchLabels.iterator();
//            while (iterator.hasNext()){
//                SearchLabelVo next = iterator.next();
//                paramMap.put("newsGroupId",next.getNewsGroupId());
//                int newsCount = newsService.selectCount(paramMap); //统计查询
//                if(newsCount <= 0){
//                    iterator.remove();
//                    continue;
//                }
//                next.setContentSize(newsCount);
//            }
//        }
        searchLabels.addAll(newSearchLabels);
        //下载中心
        paramMap.clear();
        paramMap.put("f_exhibit_code",newDto.getExhibitCode());
        paramMap.put("f_doc_name",newDto.getSearchContent());
        paramMap.put("fileType",20);
        int exhibitDocNum = exhibitDocService.selectCount(paramMap);
        SearchLabelVo exhibitDocBuild = SearchLabelVo.builder().isFileDownFlag(true).contentSize(exhibitDocNum).labelName("文件下载").build();
        searchLabels.add(exhibitDocBuild);
        return AjaxResponse.success(searchLabels);
    }

    /**
     * 品牌网站全局搜索
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/search",method = RequestMethod.POST)
    public AjaxResponse search(PageParam pageParam,NewDto newDto){
        if(StringUtils.isBlank(newDto.getExhibitCode())) return AjaxResponse.failure("展会Code为空");
        if(StringUtils.isBlank(newDto.getSearchContent())) return AjaxResponse.failure("搜索内容为空");
        if(BooleanUtils.isFalse(newDto.getFileDownFlag())) {//新闻类型
            if(StringUtils.isBlank(newDto.getNewTypeIds()) && StringUtils.isBlank(newDto.getNewGroupIds()))
                return AjaxResponse.failure("栏目Id和分组Id不能同时为空");
            PageInfo<BrandSearchVo> pageInfo = newsService.search(pageParam,newDto);
            return AjaxResponse.pageSuccess(pageInfo.getTotal(),pageInfo.getList());
        }else{
            ExhibitDocDto exhibitDocDto = new ExhibitDocDto();
            exhibitDocDto.setExhibitCode(newDto.getExhibitCode());
            exhibitDocDto.setSearchContent(newDto.getSearchContent());
            PageInfo<BrandSearchVo> pageInfo = exhibitDocService.search(pageParam, exhibitDocDto);
            return AjaxResponse.pageSuccess(pageInfo.getTotal(),pageInfo.getList());
        }
    }

    //====================================文章咨询======================================================

    /**
     * 获取品牌新闻或文件下载条件标签
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getBrandNewOrFileLabels",method = RequestMethod.POST)
    public AjaxResponse getBrandNewLabels(NewDto newDto){
        if(newDto.getOrgNum() == null) return AjaxResponse.failure("机构号为空");
        Map<String,Object> brandNewsOrFileLabelMap = Maps.newHashMapWithExpectedSize(6);
        if(BooleanUtils.isFalse(newDto.getFileDownFlag())){//新闻类型
            brandNewsOrFileLabelMap = newsService.getBrandNewLabels(newDto);
        }else{//文件下载
            if(StringUtils.isBlank(newDto.getExhibitCode()))  return AjaxResponse.failure("展会Code为空");
            ExhibitDocDto exhibitDocDto = new ExhibitDocDto();
            exhibitDocDto.setOrgNum(newDto.getOrgNum());
            exhibitDocDto.setExhibitCode(newDto.getExhibitCode());
            brandNewsOrFileLabelMap = exhibitDocService.getFileDownLabels(exhibitDocDto);
        }
        return AjaxResponse.success(brandNewsOrFileLabelMap);
    }

    /**
     * 获取品牌新闻
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getBrandNewPage",method = RequestMethod.POST)
    public AjaxResponse getBrandNewPage(PageParam pageParam,NewDto newDto){
        if(newDto.getOrgNum() == null) return AjaxResponse.failure("机构号为空");
        PageInfo<BrandNewsVo> pageInfo = newsService.getBrandNewPage(pageParam,newDto);
        return AjaxResponse.pageSuccess(pageInfo.getTotal(),pageInfo.getList());
    }

    /**
     * 获取文件下载
     * @param exhibitDocDto
     * @return
     */
    @RequestMapping(value = "/getFileList",method = RequestMethod.POST)
    public AjaxResponse getFileList(PageParam pageParam, ExhibitDocDto exhibitDocDto){
        if(exhibitDocDto.getOrgNum() == null) return AjaxResponse.failure("机构号为空");
        PageInfo<BrandFileVo> pageInfo = exhibitDocService.getFileList(pageParam,exhibitDocDto);
        return AjaxResponse.pageSuccess(pageInfo.getTotal(),pageInfo.getList());
    }


    /**
     * 获取根据新闻类型新闻列表
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getBrandNewsList",method = RequestMethod.POST)
    public AjaxResponse getBrandNewsList(NewDto newDto){
        if(newDto.getOrgNum() == null) return AjaxResponse.failure("机构号为空");
        List<BrandNewsVo> brandNewsList = newsService.getBrandNewsList(newDto);
        return AjaxResponse.success(brandNewsList);
    }


    /**
     * 获取单个新闻详情
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getSingleNews",method = RequestMethod.POST)
    public AjaxResponse getSingleNews(NewDto newDto){
        if(newDto.getNewsId() == null) return AjaxResponse.failure("新闻Id为空");
        BrandNewsVo brandNewsVo = newsService.getSingleNews(newDto);
        return AjaxResponse.success(brandNewsVo);
    }


    /**
     * 获取分组或栏目名称
     * @param newDto
     * @return
     */
    @RequestMapping(value = "/getNewsTypeOrGroup",method = RequestMethod.POST)
    public AjaxResponse getNewsTypeOrGroup(NewDto newDto){
        Integer orgNum = newDto.getOrgNum();
        if(orgNum == null) return AjaxResponse.failure("机构号为空");
        List<Map<String, Object>> resultList = newsService.getNewsTypeOrGroup(newDto);
        return AjaxResponse.success(resultList);
    }




    //====================================品牌首页======================================================

    /**
     * 获取单品牌信息
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getOneBrandInfo",method = RequestMethod.POST)
    public AjaxResponse getOneBrandInfo(ProjectDto projectDto){
        if(projectDto.getProjectId() == null) return AjaxResponse.failure("项目ID为空");
        BrandProjectVo brandProjectVo = projectService.getOneBrandInfo(projectDto);
        return AjaxResponse.success(brandProjectVo);
    }



    //====================================我要参展 - 我要参观======================================================

    /**
     * 获取我要参展  - 我要参观
     * 前提条件只有后台设置项调用功能为:品牌当前项目参展申请、品牌当前项目观众登记
     * query 1 我要参展（展商申请）  2 我要参观（观众登记）
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getApplyRegLinkProjectList",method = RequestMethod.POST)
    public AjaxResponse getApplyRegLinkProjectList(ProjectDto projectDto){
        if(projectDto.getQueryType() == null) return AjaxResponse.failure("查询标识为空");
        if(projectDto.getProjectId() == null) return AjaxResponse.failure("项目ID为空");
        List<ProjRegSetVo> projRegSetVos = projectService.getApplyRegLinkProjectList(projectDto);
        return AjaxResponse.success(projRegSetVos);
    }

    //====================================展商======================================================

    /**
     * 获取展商
     * @param pageParam
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getExhibitorList",method = RequestMethod.POST)
    public AjaxResponse getExhibitorList(PageParam pageParam,ProjectDto projectDto){
        if(StringUtils.isBlank(projectDto.getExhibitCode())) return AjaxResponse.failure("展会Code为空");
        List<BrandExhibitorVo> brandExhibitorVoList = Lists.newArrayList();
        int count = catalogCompanyProductService.countBrandExhibitor(projectDto);
        if(count > 0){
            pageParam.initLimitPageParam(count);
            if(count >= PageConstant.requestTotalNum) count = PageConstant.requestTotalNum;
            brandExhibitorVoList = catalogCompanyProductService.getBrandExhibitorList(pageParam,projectDto);
        }
        return AjaxResponse.pageSuccess(count,brandExhibitorVoList);
    }

    /**
     * 获取单个展商
     * @param catalogId
     * @return
     */
    @RequestMapping(value = "/getSingleExhibitor",method = RequestMethod.POST)
    public AjaxResponse getSingleExhibitor(Integer catalogId){
        if(catalogId == null) return AjaxResponse.failure("会刊Id为空");
        BrandExhibitorVo brandExhibitorVo = catalogCompanyProductService.getSingleExhibitor(catalogId);
        try {
            AuthLoginRespVo userInfo = getUserInfo();
            if(Objects.nonNull(brandExhibitorVo) && Objects.nonNull(userInfo)){
                MemberVisitLogDto build = MemberVisitLogDto.builder()
                        .cataId(catalogId)
                        .zzyUserId(userInfo.getZzyUserId())
                        .orgNum(userInfo.getFocusOrgNum())
                        .mobileClientFlag(RequestUtil.isMobileClient())
                        .visitIp(RequestUtil.getClientIP())
                        .build();
                zzyUserAwokeAsync.saveVisitServeCataDtlLog(build);
            }
        }catch (Exception e){
            logger.error("记录访问会刊详情日志异常：{}",e);
        }
        return AjaxResponse.success(brandExhibitorVo);
    }


    //====================================小程序模板网站======================================================

    /**
     * 统计会员业务数据值
     * orgNum
     * zzyUserId
     * projectId
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getMemberStatistics",method = RequestMethod.POST)
    public AjaxResponse getMemberStatistics(ProjectDto projectDto){
        if(projectDto.getOrgNum() == null) return AjaxResponse.failure("机构号为空");
        if(projectDto.getZzyUserId() == null) return AjaxResponse.failure("会员Id为空");
        Map<String,Object> memberStatisticsMap =zzyuserService.getMemberStatistics(projectDto);
        return AjaxResponse.success(memberStatisticsMap);
    }



    //====================================产品(废弃)======================================================

    /**
     * 获取展商产品
     * @param pageParam
     * @param projectDto
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/getExhibitorProductList",method = RequestMethod.POST)
    public AjaxResponse getExhibitorProductList(PageParam pageParam,ProjectDto projectDto){
        if(StringUtils.isBlank(projectDto.getExhibitCode()) && projectDto.getBrandProjectId() == null)
            return AjaxResponse.failure("展会Code和品牌Id为空");
        List<BrandExhibitorProductVo> brandExhibitorProductVos = Lists.newArrayList();
        int count = catalogCompanyProductService.countBrandExhibitorProduct(projectDto);
        if(count > 0){
            brandExhibitorProductVos = catalogCompanyProductService.getBrandExhibitorProductList(pageParam,projectDto);
        }
        return AjaxResponse.pageSuccess(count,brandExhibitorProductVos);
    }

    /**
     * 获取单个展品
     * @param productId
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/getSingleProduct",method = RequestMethod.POST)
    public AjaxResponse getSingleProduct(Integer productId){
        if(productId == null) return AjaxResponse.failure("产品Id为空");
        BrandExhibitorProductVo brandExhibitorProductVo = catalogCompanyProductService.getSingleProduct(productId);
        return AjaxResponse.success(brandExhibitorProductVo);
    }
    //====================================产品(废弃)======================================================
}
