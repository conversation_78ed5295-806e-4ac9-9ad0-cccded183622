/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-06-30 16:50
 * @description：行业分类
 */
package com.eip.web.business.controller.api.webSiteApi.common;

import com.eip.common.controller.BaseController;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.TradeClassDto;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.entity.TradeClass;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.service.TradeClassService;
import com.eip.facade.vip.vo.TradeClassVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-06-30 16:50
 * @description：行业分类
 */
@RestController
@RequestMapping(value = "/api/set/tradeClass")
public class ApiSiteTradeClassController extends BaseController {

    @Autowired
    private TradeClassService tradeClassService;
    @Autowired
    private ProjectService projectService;


    /**
     * 根据项目Id查询项目属于类别
     * @return
     */
    @RequestMapping(value = "/selectTradeTree", method = RequestMethod.POST)
    public AjaxResponse selectTradeTree(TradeClassDto tradeClassDto){
        if(tradeClassDto.getPid()==null) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        Project project = projectService.getById(tradeClassDto.getPid());
        if(Objects.isNull(project)) return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST);
        if(project.getTradeId()==null) return AjaxResponse.failure("该项目所属行业为空");
        tradeClassDto.setTradeId(project.getTradeId());
        List<TradeClass> tradeClasses = tradeClassService.selectTradeTree(tradeClassDto);
        return AjaxResponse.success(tradeClasses);
    }

    /**
     * 根据项目Id查询项目属于类别
     * @return
     */
    @RequestMapping(value = "/selectTradeTreeAndProductType", method = RequestMethod.POST)
    public AjaxResponse selectTradeTreeAndProductType(TradeClassDto tradeClassDto){
        if(tradeClassDto.getPid()==null) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        List<TradeClassVo> tradeClasses = tradeClassService.selectTradeTreeAndProductType(tradeClassDto);
        return AjaxResponse.success(tradeClasses);
    }





}
