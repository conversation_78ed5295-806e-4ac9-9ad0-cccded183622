package com.eip.web.business.controller.api.webSiteApi.common;

import com.eip.common.controller.BaseController;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.service.ExhibitSectionService;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.vo.ExhibitSectionQueryVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-05-18 11:34
 * @DESCRIPTION:
 */
@RestController
@RequestMapping(value = "/api/set/section")
public class ApiSiteExhibitSectionController extends BaseController {

    @Resource
    private ExhibitSectionService exhibitSectionService;
    @Autowired
    private ProjectService projectService;

    /**
     * 线上网站-展商 获取展馆展区条件(平级显示)
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getSectionsHorizontalDisplay",method = RequestMethod.POST)
    public AjaxResponse getSectionsHorizontalDisplay(ProjectDto projectDto){
        if(StringUtils.isBlank(projectDto.getExhibitCode())) {
            if( projectDto.getProjectId() == null) return AjaxResponse.failure("项目Id为空");
        }
        if(StringUtils.isBlank(projectDto.getExhibitCode()) && projectDto.getProjectId() !=null){
            Project project = projectService.getById(projectDto.getProjectId());
            if(Objects.isNull(project)) return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST);
            if(StringUtils.isBlank(project.getF_exhibit_code())) return AjaxResponse.failure("项目未关联展会");
            projectDto.setExhibitCode(project.getF_exhibit_code());
        }
        projectDto.setParentId(-1);
        List<ExhibitSectionQueryVo> exhibitSectionQueryVos = exhibitSectionService.getSectionsHorizontalDisplay(projectDto);
        return AjaxResponse.success(exhibitSectionQueryVos);
    }

    /**
     * 线上网站-展商 获取展馆展区条件（树形结构）
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getSectionsTreeDisplay",method = RequestMethod.POST)
    public AjaxResponse getSectionTrees(ProjectDto projectDto){
        if(StringUtils.isBlank(projectDto.getExhibitCode())){
            if( projectDto.getProjectId() == null) return AjaxResponse.failure("项目Id为空");
        }
        if(StringUtils.isBlank(projectDto.getExhibitCode()) && projectDto.getProjectId() !=null){
            Project project = projectService.getById(projectDto.getProjectId());
            if(Objects.isNull(project)) return AjaxResponse.create(CommonResponseStateEnum.PROJECT_NOT_EXIST);
            if(StringUtils.isBlank(project.getF_exhibit_code())) return AjaxResponse.failure("项目未关联展会");
            projectDto.setExhibitCode(project.getF_exhibit_code());
        }
        ExhibitSectionQueryVo  exhibitSectionQueryVo = exhibitSectionService.getSectionsTreeDisplay(projectDto);
        return AjaxResponse.success(exhibitSectionQueryVo);
    }
}
