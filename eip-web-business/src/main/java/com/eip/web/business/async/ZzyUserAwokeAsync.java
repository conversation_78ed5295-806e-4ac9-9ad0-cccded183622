package com.eip.web.business.async;

import com.eip.common.util.DateUtil;
import com.eip.facade.vip.dto.MemberVisitLogDto;
import com.eip.facade.vip.entity.Catalogue;
import com.eip.facade.vip.entity.MemberVisitsLog;
import com.eip.facade.vip.entity.ServeProduct;
import com.eip.facade.vip.service.CatalogueService;
import com.eip.facade.vip.service.MemberVisitsLogService;
import com.eip.facade.vip.service.NewIntegerIdService;
import com.eip.facade.vip.service.ServeProductService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-12-05 9:48
 * @DESCRIPTION: 会员访问日志
 */
@Slf4j
@Component
public class ZzyUserAwokeAsync {

    @Autowired
    private CatalogueService catalogueService;
    @Autowired
    private ServeProductService serveProductService;
    @Autowired
    private MemberVisitsLogService memberVisitsLogService;
    @Autowired
    private NewIntegerIdService newIntegerIdService;

    /**
     * 访问展商详情页
     * @param visitLogDto
     */
    @Async
    public void saveVisitServeCataDtlLog(MemberVisitLogDto visitLogDto) {
        if(visitLogDto.getCataId() == null) return;
        Catalogue catalogue = catalogueService.getServeCataById(visitLogDto.getCataId());
        if(Objects.isNull(catalogue)) return;
        try {
            Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(8);
            paramMap.put("projectId",catalogue.getF_project_id());
            paramMap.put("orgNum",visitLogDto.getOrgNum());
            paramMap.put("zzyuserId",visitLogDto.getZzyUserId());
            paramMap.put("eqCurrentDate",true);
            int count = memberVisitsLogService.count(paramMap);
            if(count > 0) return;
            Date currentNow = DateUtil.getCurrentNow();
            StringBuilder builder = new StringBuilder();
            builder.append(DateUtil.date2Str(currentNow,DateUtil.YYYY_MM_DD_HH_MM_SS))
                    .append(" 访问了 ")
                    .append(StringUtils.isNotBlank(catalogue.getF_company_name()) ? catalogue.getF_company_name() : "")
                    .append(" 会刊 ").append("\r\n");
            MemberVisitsLog build = MemberVisitsLog.builder()
                    .orgNum(visitLogDto.getOrgNum())
                    .zzyuserId(visitLogDto.getZzyUserId())
                    .projectId(catalogue.getF_project_id())
                    .primaryKey(String.valueOf(visitLogDto.getCataId()))
                    .primaryKeyType("serveCatalogue")
                    .visitClientType(BooleanUtils.isTrue(visitLogDto.getMobileClientFlag()) ? "MOBILE" : "PC")
                    .memo(builder.toString())
                    .id(newIntegerIdService.snowNextId())
                    .visitIp(visitLogDto.getVisitIp())
                    .visitTime(currentNow)
                    .build();
            memberVisitsLogService.add(build);
        }catch (Exception e){
            log.error("保存访问会刊详情日志异常",e);
        }
    }

    /**
     * 访问产品详情记录日志
     * @param visitLogDto
     */
    @Async
    public void saveVisitServeProductDtlLog(MemberVisitLogDto visitLogDto) {
        if(visitLogDto.getServeProductId() == null) return;
        ServeProduct serveProduct = serveProductService.selectById(visitLogDto.getServeProductId());
        if(Objects.isNull(serveProduct)) return;
        try {
            Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(8);
            paramMap.put("projectId",serveProduct.getProjectId());
            paramMap.put("orgNum",visitLogDto.getOrgNum());
            paramMap.put("zzyuserId",visitLogDto.getZzyUserId());
            paramMap.put("eqCurrentDate",true);
            int count = memberVisitsLogService.count(paramMap);
            if(count > 0) return;
            Date currentNow = DateUtil.getCurrentNow();
            StringBuilder builder = new StringBuilder();
            builder.append(DateUtil.date2Str(currentNow,DateUtil.YYYY_MM_DD_HH_MM_SS))
                    .append(" 访问了 ")
                    .append(StringUtils.isNotBlank(serveProduct.getCompanyName()) ? serveProduct.getCompanyName() : "")
                    .append(" 会刊产品")
                    .append(StringUtils.isNotBlank(serveProduct.getProductName()) ? "("+serveProduct.getProductName()+")" : "")
                    .append("\r\n");
            MemberVisitsLog build = MemberVisitsLog.builder()
                    .id(newIntegerIdService.snowNextId())
                    .visitTime(currentNow)
                    .visitIp(visitLogDto.getVisitIp())
                    .projectId(serveProduct.getProjectId())
                    .memo(builder.toString())
                    .orgNum(visitLogDto.getOrgNum())
                    .zzyuserId(visitLogDto.getZzyUserId())
                    .primaryKey(String.valueOf(visitLogDto.getServeProductId()))
                    .primaryKeyType("serveProduct")
                    .build();
            memberVisitsLogService.add(build);
        }catch (Exception e){
            log.error("保存访问产品详情日志异常",e);
        }
    }
}
