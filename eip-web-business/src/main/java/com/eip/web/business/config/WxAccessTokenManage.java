//package com.eip.web.business.config;
//
//import com.eip.common.constant.wxpay.WxConstant;
//import com.eip.facade.vip.service.SysSettingService;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import weixin.popular.support.TokenManager;
//
///**
// * @USER: zwd
// * @DATE: 2023-09-07 18:02
// * @DESCRIPTION:
// */
//@Component
//public class WxAccessTokenManage {
//
//    @Autowired
//    private SysSettingService sysSettingService;
//    /**
//     * 启动项目时更新AccessToken
//     */
////    @PostConstruct
//    public void start() {
//        //TokenManager.init(WxConstant.APP_ID, WxConstant.APP_SECRET);
//        String appId = sysSettingService.getValue("wxAppId");
//        String appSecret = sysSettingService.getValue("wxAppSecret");
//        if(StringUtils.isBlank(appId)) appId = WxConstant.APP_ID;
//        if(StringUtils.isBlank(appSecret)) appSecret = WxConstant.APP_SECRET;
//        TokenManager.init(appId, appSecret);
//    }
//
//    /**
//     * 每隔100分钟执行方法
//     */
////    @Scheduled(fixedRate = 6000000)
//    public void flashToken() {
//        //TokenManager.init(WxConstant.APP_ID, WxConstant.APP_SECRET);
//        String appSecret = sysSettingService.getValue("wxAppSecret");
//        String appId = sysSettingService.getValue("wxAppId");
//        if(StringUtils.isBlank(appId)) appId = WxConstant.APP_ID;
//        if(StringUtils.isBlank(appSecret)) appSecret = WxConstant.APP_SECRET;
//        TokenManager.init(appId, appSecret);
//    }
//}