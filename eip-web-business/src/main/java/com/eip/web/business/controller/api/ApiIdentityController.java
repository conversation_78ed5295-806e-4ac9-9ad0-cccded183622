package com.eip.web.business.controller.api;

import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.IdentityInfoDto;
import com.eip.facade.vip.service.IdentityService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @USER: zwd
 * @DATE: 2025-05-14 15:03
 * @DESCRIPTION: 身份校验
 */
@RestController
@RequestMapping(value = "/api/identity")
public class ApiIdentityController {

    @Autowired
    private IdentityService identityService;


    /**
     * 身份校验
     * @return
     */
    @RequestMapping(value = "/idCheck",method = RequestMethod.POST)
    public AjaxResponse idCheck(IdentityInfoDto identityInfoDto){
        if(StringUtils.isBlank(identityInfoDto.getFullName())
           || StringUtils.isBlank(identityInfoDto.getIdNumber())
           || identityInfoDto.getOrgNum() == null
        )
            return AjaxResponse.paramMiss();
        return identityService.idCheck(identityInfoDto);

    }

}
