<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>联系我们-杭州展之信息技术有限公司</title>
		<meta name="keywords" content="展览软件,会展软件,展览CRM,展会CRM,展之云,展之客,展之通,展商管理,观众登记,观众邀约,供需配对,动态展位图,展会官网,客户关系管理,呼叫中心,新特软件,杭州展之" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,专业提供展览CRM,展会项目管理,会展信息门户,展会官网,呼叫中心等系统,为会展企业提供全面解决方案。展之公司十几年积累几百家展览公司成功案例和项目经验。展之科技,专注会展信息化,助力行业发展!" />
		<link rel="icon" href="img/index/logo-title.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/style.js" type="text/javascript" charset="utf-8"></script>
		<script src="layui/layui.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" href="layui/css/layui.css" media="all">
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.layui-laypage-em {
				background-color: #00a4ff !important;
			}
		</style>
		<script type="text/javascript">
			function isComputer(){ 
			var ua = window.navigator.userAgent.toLowerCase();
			if(ua.match(/(iPhone|iPod|iPad|Android|ios|SymbianOS)/i)){ 
				return false;
			}else{ 
				return true; 
			}
		} 
			if(isComputer()){ 
				//top.location.href = "/new/pc_officialWeb/index.html";
			}else{
				top.location.href = "../mobile-officialweb/contact.html";
			}
		window.onload = function(){ 
			
		};
		</script>
	</head>

	<body>
		<div class="index_fix">
			<!-- log区域 -->
			<div class="index_top oh">
				<div class="index-log fl">
					<a href="index.html">
						<img src="img/index/logo.png" alt="杭州展之信息技术有限公司">

					</a>
				</div>
				<div class="index-sealch fr">
					<input type="text" placeholder="搜索">
					<span></span>
				</div>
			</div>
			<!-- 导航栏 -->
			<div class="index-nav oh">
				<ul>
					<li style="margin-left: 5px;"><a href="index.html">首页</a></li>
					<li><a href="solution.html">解决方案</a></li>
					<li><a href="productall.html">产品中心</a></li>
					<li><a href="successful.html">成功案例</a></li>
					<!-- <li><a href="member.html">会员中心</a></li> -->
					<li><a href="micro.html">展之微刊</a></li>
					<li class="actives"><a href="about_us.html" class="activess">联系我们</a></li>
				</ul>
				<!-- <div class="index-logon fr oh">
					<a href="">注册</a>
					<a href="">登录 </a>
				</div> -->
			</div>
			<div class="product_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>展之云</h4>
					<ul>
						<li><a href="product.html#zzy1">观众登记平台</a></li>
						<li><a href="product.html#zzy2">展商会员中心<i></i></a></li>
						<li><a href="product.html#zzy3">展会管理后台</a></li>
						<li><a href="product.html#zzy4">动态展位图<i></i></a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>展之通</h4>
					<ul>
						<li><a href="product1.html#zzt1">电话助手</a></li>
						<li><a href="product1.html#zzt2">提醒推送<span></span></a></li>
						<li><a href="product1.html#zzt3">公告通知</a></li>
						<li><a href="product1.html#zzt4">短信邮件接口</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>展之客</h4>
					<ul>
						<li><a href="product2.html#zzk1">客户关系管理<span></span></a></li>
						<li><a href="product2.html#zzk2">项目管理</a></li>
						<li><a href="product2.html#zzk3">财务管理</a></li>
						<li><a href="product2.html#zzk4">文档管理</a></li>
					</ul>
				</div>
			</div>
			<div class="solution_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>项目方案</h4>
					<ul>
						<li><a href="project/myself-exhibition.html">自办展项目应用方案</a></li>
						<li><a href="project/agency-exhibition.html">国外代理展项目应用方案</a></li>
						<li><a href="project/construct.html">搭建设计项目应用方案</a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>办公方案</h4>
					<ul>
						<li><a href="project/remote-office.html">异地、移动办公解决方案</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>管理方案</h4>
					<ul>
						<li><a href="project/vistorregistration.html">观众登记管理方案</a></li>
						<li><a href="project/manage-platform.html">会员管理平台方案</a></li>

					</ul>
				</div>
			</div>

		</div>
		<!-- <script type="text/javascript">
			$(function() {

				$('.childers').each(function(i) {
					$(this).find("li").on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script> -->
		<!-- start -->
		<div class="top_picture">
			<h1>展之科技 助力企业发展</h1>
			<h2>用心倾注会展行业，专业提供全面服务</h2>
			<!-- <a href="">立即联系</a> -->
		</div>
		<div class="index-width">
			<!-- abous-list -->
			<div class="index_width about_width">
				<div class="index-conter">
					<div class="about_list oh">
						<ul>
							<li>联系我们</li>
							<li class="about_list_active">关于我们</li>
							<li>展之动态</li>
						</ul>
					</div>
				</div>
			</div>
			<!-- abous-content -->
			<div class="index-conter about_conter oh">
				<!-- 联系我们 -->
				<div class="about_connection fl" style="display: none;">
					<div class="about_connection1 oh">
						<ul>
							<li>
								<div class="about_connection_img about_connection_bg0">
									<img src="img/connect/qq.png" alt="">
								</div>
								<h4>QQ联系</h4>
								<h5></h5>
								<div class="about_erwei">
									<ul>
										<li>
											<p>售前：<i>2851704313</i></p>
										</li>
										<li>
											<p><i style="padding-left: 14%;">2851704317</i></p>
										</li>
										<li>
											<p><i style="padding-left: 14%;">**********</i></p>
										</li>
										<li style="margin-top: 10px;">
											<p>售后：<i>2851704316</i></p>
										</li>

										<li>
											<p><i style="padding-left: 14%;">2851704319</i></p>
										</li>
										<!-- <li>
											<p>客服9号：<i>8541561</i></p>
										</li>
										<li>
											<p>客服9号：<i>8541561</i></p>
										</li> -->
									</ul>
								</div>
							</li>
							<li style="width: 320px;">
								<div class="about_connection_img about_connection_bg1">
									<img src="img/connect/we.png" alt="">
								</div>
								<h4>微信联系</h4>
								<h5></h5>
								<div class="about_erwei">
									<ul>
										<li>
											<p>客服：<i>17364535378</i><span><img src="img/connect/lx.png"></span></p>
											<div class="about_erwei1" style="display: none;">
												<span><img src="img/connect/jt.png"></span>
												<img src="img/index/thumbnail2.jpg">
											</div>
										</li>
										<!-- <li>
											
										</li> -->
										<!-- <li>
											<p>客服9号：<i>56454545145</i></p>

										</li>
										<li>
											<p>客服9号：<i>56454545145</i></p>

										</li> -->
									</ul>
									<p style="margin: 6px 8px 6px;">公众号：<i>展之科技</i></p>
									<p><img src="img/index/thumbnail2.jpg" alt=""></p>
								</div>
							</li>
							<li style="width: 280px;">
								<div class="about_connection_img about_connection_bg2">
									<img src="img/connect/phone.png" alt="">
								</div>
								<h4>电话联系</h4>
								<h5></h5>
								<p>售前：<i>0571-********</i></p>
								<p>售后：<i>0571-86787186</i></p>
								<h5 style="margin-top: 30px;"></h5>
								<p>值班电话：<i>17364535378</i></p>
								<!-- <p>客服9号：<i>0222 - 52555624</i></p> -->
							</li>
							<li>
								<div class="about_connection_img about_connection_bg3">
									<img src="img/connect/mail.png" alt="">
								</div>
								<h4>邮箱联系</h4>
								<p>售前：<i><EMAIL></i></p>
								<p>售后：<i><EMAIL></i></p>
								<h5 style="margin-top: 30px;"></h5>
								<p>技术反馈：<i><EMAIL></i></p>
							</li>
						</ul>
					</div>
					<!-- 地图 -->
					<div style="margin-top: 90px;">
						<iframe src="map.html" width="1200" height="350" style="border: none;"></iframe>
					</div>
					<!-- 服务时间 -->
					<div class="about_time">
						<h5>服务时间</h5>
						<div class="about_time1">
							<p>周一</p>
							<span></span>
							<p>周五</p>
							<p>08:30</p>
							<span></span>
							<p>17:30</p>
						</div>
						<div class="about_text">
							<p>晚上和周末请联系：<i style="margin: 0 20px 0px 80px;">QQ：2851704319</i> <i>手机：17364535378</i></P>
							<P style="margin: 20px 0 0 72px;">微信：17364535378</p>

						</div>
					</div>
				</div>
				<!-- 关于我们 -->
				<div class="about_us fl">
					<div class="about_inf">
						<h3>公司简介：</h3>
						<p>杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研发和服务的高新科技企业。</P>

					</div>
					<div class="about_inf">
						<h3>重点产品：</h3>
						<p>公司2003年率先将CRM管理理念引入会展行业，目前，产品与服务已广泛应用于会展行业的主办、承办、招展、搭建及展会相关服务企业，主要有展览CRM软件、观众登记、呼叫中心、动态展位图、会员中心等。</P>
					</div>
					<div class="about_inf">
						<h3>我们的客户：</h3>
						<p>精巧的软件设计思路、实用的操作界面和优质的技术服务赢得了包括北京领汇、北京中盈展、北京北辰、上海中贸慕尼黑、上海荷瑞、上海展业、广东亚联、智奥鹏城、深圳环球、青岛海名、厦门誉颁、厦门领肯、江苏汇鸿、河北鼎亚、振威集团、鸿尔集团等近400家会展企业的支持与认可。</p>
					</div>
					<div class="about_inf">
						<h3>我们的目标：</h3>
						<p>凭借对会展行业的深刻理解和丰富的实施经验，并借助互联网应用进行行业化信息资源整合，为新老用户和整个行业的发展实施前瞻性布局。</p>
					</div>
				</div>
				<!-- 展之动态 -->
				<div class="about_new fl" style="display: none;width:100%">
					<div class="about_new_title">
						<h4>展之动态</h4>
						<!-- <h5 id="wk">展之微刊</h5> -->
					</div>
					<div class="about_news about_news_h4">
						<ul id="newsList">
							<!-- 	<li>
								<a href="product.html#zzy4">
								
									<div class="about-new-left">
										<div class="about_new_title1">动态展位图第一期上线。</div>
										<div class="about_new_text  text_height">
										2019年6月，动态展位图第一期上线。

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2019.6.12</i></span>
											<span>阅读量：<i>1892</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/2019429.html">
									
									<div class="about-new-left">
										<div class="about_new_title1">公司成为浙江省国际会议展览业协会理事单位</div>
										<div class="about_new_text  text_height">
											2019年4月，公司成为浙江省国际会议展览业协会理事单位。

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2019.4.29</i></span>
											<span>阅读量：<i>1358</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/2018816.html">
									
									<div class="about-new-left">
										<div class="about_new_title1">公司荣获杭州市高新技术企业称号</div>
										<div class="about_new_text  text_height">
											2018年8月，公司荣获杭州市高新技术企业称号
										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2018.8.16 </i></span>
											<span>阅读量：<i>1246</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/20170927222957-67.html">
									<div class="news-img">
										<img src="img/news/company-news/20170927225221922192.png">
									</div>
									<div class="about-new-left">
										<div class="about_new_title1">展之云移动CRM升级，增加微信推送提醒、地图导航、收藏夹等功能</div>
										<div class="about_new_text  text_height">
											利用展之云移动CRM，可以使工作更加轻松自如，实现如下场景应用：
											1、匆匆忙忙要出差，再也不用抄写或打印待拜访客户联系信息了，手机上随时可以看；
											2、在机场候机或坐在高铁上，使用手机可以先看看待拜访客户信息和历史联系情况；
											3、使用手机调出客户公司地址，在后面点下按钮，直接调出地图定位并导航；
											4、重要事务直接提醒到手机微信上，即使不在公司里的电脑前也不会错过重要事务；

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2017.09.27 </i></span>
											<span>阅读量：<i>962</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/201612.html">
									
									<div class="about-new-left">
										<div class="about_new_title1">公司开始研发动态展位图、会员中心等</div>
										<div class="about_new_text  text_height">
											本公司2003年率先将CRM管理理念引入会展行业，产品与服务已广泛应用于会展行业的主办、承办、招展、搭建及展会相关服务企业。凭借对会展行业的深刻理解和丰富的项目实施经验，2016年12月，公司总部迁至互联网城市-杭州，借助互联网应用，进行行业化信息资源整合，规划研发动态展位图、展商会员中心等产品体系，为新老用户和整个行业的发展实施前瞻性布局。

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2016.12.16</i></span>
											<span>阅读量：<i>1423</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/20161205175648-63.html">
									
									<div class="about-new-left">
										<div class="about_new_title1">杭州展之信息技术有限公司成立啦！原新特客户服务会有变化？</div>
										<div class="about_new_text  text_height">
											由于公司发展需要，“温州新特软件开发有限公司”名称从2016年12月1日变更为“杭州展之信息技术有限公司”，地址从“温州站南商贸城”迁至“杭州经济开发区”，届时原公司所有业务由杭州展之信息技术有限公司统一经营，原公司鉴订的合同继续有效，原有业务关系和服务承诺保持不变。
											因公司名称变更给您带来的不便，我们深表歉意!衷心感谢您一贯的支持和关怀，我们将一如既往地和您保持愉快的合作关系，并希望继续得到您的关心和支持!

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2016.12.05 </i></span>
											<span>阅读量：<i>1971</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/20161028090805-61.html">
									<div class="news-img">
										<img src="img/news/company-news/20161028091110231023.jpg">
									</div>
									<div class="about-new-left">
										<div class="about_new_title1">热烈祝贺中国会展业联盟成立</div>
										<div class="about_new_text  text_height">
											热烈祝贺中国会展业联盟成立！
											新特软件总经理季武参加会议，希望能与新特新老客户及业内朋友共同努力，把中国会展产业做大做强！

										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2016.10.28 </i></span>
											<span>阅读量：<i>853</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li>
							<li>
								<a href="news/company-news/20160715175439-52.html">
									<div class="news-img">
										<img src="img/news/company-news/20160715175975097509.jpg">
									</div>
									<div class="about-new-left">
										<div class="about_new_title1">新特软件成立16周年庆活动方案</div>
										<div class="about_new_text  text_height">
											活动主题：16年来，感谢有您！为答谢新老客户，值此新特软件16周岁生日，我们以多重优惠，多重好礼相送，力度空前！
											活动时间：2016年7月17日起至9月30日
											活动内容：
											一、新客户购买所有软件一律七折优惠！
										</div>
										<div class="about_new_time">
											<span>发布时间：<i>2016.07.15 </i></span>
											<span>阅读量：<i>1593</i></span>
											<span>发布人 ：<i></i>展之采编</span>
										</div>
									</div>
								</a>
							</li> -->
						</ul>

					</div>
					<div class="per100">
						<div class="news-list-page" style="text-align: center;padding-top: 60px;">
							<div id="pagination"></div>
						</div>

					</div>



				</div>
			</div>

		</div>
		<!-- end -->
		<div class="index-foot">
			<iframe src="foot.html" width="100%" height="100%"></iframe>
		</div>
		<div class="bank_top">
			<ul>
				<li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes"><img src="img/float/window1.png"></a></li>
				<li class="erwei">
					<img src="img/float/window2.png">
					<div class="erweima_float"><img src="img/float/Windows5.png"></div>
				</li>
				<li style="overflow: hidden;"><img src="img/float/window3.png" style="margin-rtight: 10px;">
					<span>0571-********</span>
				</li>
				<li><img src="img/float/window4.png"></li>
			</ul>
		</div>

		<script type="text/javascript">
			var page = 1;
			var rows = 10;
			var total = 0;
			//var newsTypeId = getQueryString("newsTypeId");
			//var typeName = getQueryString("typeName");


			$(function() {

				//alert(typeName)
				getPage();
				loadNews();
			})

			function loadNews() {
				//alert("a")
				$.ajax({
					url: variableSponsor + '/news/getList', //请求服务器url地址.   
					type: "post",
					data: {
						page: page,
						rows: rows,
						exhibitCode: exhibitCode,
						projectId: pid,
						newsTypeId: aboutnews,
						isShow: true
					},
					async: false,
					xhrFields: {
						withCredentials: true
					},
					success: function(data) {
						console.log(data)

						var data = JSON.parse(data);
						total = data.total;
						var result = data.rows;
						var html = "";
						console.log(result.typeName)
						// <li>
						// 	<a href="news/company-news/20160715175439-52.html">
						// 		<div class="news-img">
						// 			<img src="img/news/company-news/20160715175975097509.jpg">
						// 		</div>
						// 		
						// 			新特软件成立16周年庆活动方案
						// 				活动主题：16年来，感谢有您！为答谢新老客户，值此新特软件16周岁生日，我们以多重优惠，多重好礼相送，力度空前！
						// 				活动时间：2016年7月17日起至9月30日
						// 				活动内容：
						// 				一、新客户购买所有软件一律七折优惠！
						// 			2016.07.15 
						// 				
						// 				<span>发布人 ：<i></i>展之采编
						// 		

						for (var i = 0; i < result.length; i++) {
							if (result[i].logo != "") {
								html += '<li><a href="javascript:void(0);" onclick="loadIframe(' +
									'\'' + result[i].fileName + '\',' + result[i].newsId + ')" >' +
									'<div class="news-img"><img src="' + result[i].logo + '"></div>' +
									'<div class="about-new-left"><div class="about_new_title1">' +
									result[i].titleName + '</div><div class="about_new_text  text_height">' +
									result[i].brief + '</div><div class="about_new_time"><span>发布时间：<i>' +
									result[i].createTime.substring(0, 10) + '</i></span>' + '<span><i></i></span>' +
									'<span>新闻来源 ：<i></i>' + result[i].source + '</span>' + '	</div></div></a></li>'
							} else {
								html += '<li><a href="javascript:void(0);" onclick="loadIframe(' +
									'\'' + result[i].fileName + '\',' + result[i].newsId + ')" >' +
									'<div class="about-new-left"><div class="about_new_title1">' +
									result[i].titleName + '</div><div class="about_new_text  text_height">' +
									result[i].brief + '</div><div class="about_new_time"><span>发布时间：<i>' +
									result[i].createTime.substring(0, 10) + '</i></span>' + '<span><i></i></span>' +
									'<span>新闻来源 ：<i></i>' + result[i].source + '</span>' + '	</div></div></a></li>'
							}



						}
						//alert(html) 
						$('#newsList').empty().append(html);



						return false;
					},
					error: function(data) {
						alert("数据加载失败");
						return false;
					}
				});

			}

			function loadIframe(fileName, id) {
				window.location.href = "news-template.html" + '?newsId=' + id;
			}


			function getPage() {
				layui.use(['laypage', 'layer'], function() {
					var laypage = layui.laypage,
						layer = layui.layer;

					laypage.render({
						elem: 'pagination',
						count: total,
						layout: ['prev', 'page', 'next', 'limit', 'skip'],
						limit: 10,
						limits: [10],
						jump: function(obj, first) {
							console.log(total)
							page = obj.curr; //改变当前页码
							rows = obj.limit;
							//首次不执行
							if (!first) {
								/* alert("a") */
								loadNews(); //加载数据
							}
						}
					});
				})
			}
		</script>


	</body>
</html>
