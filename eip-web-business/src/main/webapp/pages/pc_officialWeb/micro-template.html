<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>展之微刊-杭州展之信息技术有限公司</title>
		<meta name="keywords" content="展览软件,会展软件,展览CRM,展会CRM,展之云,展之客,展之通,展商管理,观众登记,观众邀约,供需配对,动态展位图,展会官网,客户关系管理,呼叫中心,新特软件,杭州展之" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,专业提供展览CRM,展会项目管理,会展信息门户,展会官网,呼叫中心等系统,为会展企业提供全面解决方案。展之公司十几年积累几百家展览公司成功案例和项目经验。展之科技,专注会展信息化,助力行业发展!" />
		<link rel="icon" href="img/index/logo-title.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/style.js" type="text/javascript" charset="utf-8"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.about_new_time {
				margin: 88px 0 100px 0;
				padding-left: 130px;
			}
		</style>
	</head>

	<body>
		<div class="index_fix2" style="background-color: rgb(37, 43, 58) !important;">
			<!-- log区域 -->
			<div class="index_top oh">
				<div class="index-log fl">
					<a href="index.html">
						<img src="img/index/logo.png" alt="杭州展之信息技术有限公司">
					</a>
				</div>
				<div class="index-sealch fr">
					<input type="text" placeholder="搜索">
					<span></span>
				</div>
			</div>
			<!-- 导航栏 -->
			<div class="index-nav oh">
				<ul>
					<li style="margin-left: 5px;"><a href="index.html">首页</a></li>
					<li><a href="solution.html">解决方案</a></li>
					<li><a href="productall.html">产品中心</a></li>
					<li><a href="successful.html">成功案例</a></li>
					<!-- <li><a href="member.html">会员中心</a></li> -->
					<li class="actives"><a href="micro.html" class="activess">展之微刊</a></li>
					<li><a href="about_us.html">联系我们</a></li>
				</ul>
				<!-- <div class="index-logon fr oh">
					<a href="">注册</a>
					<a href="">登录 </a>
				</div> -->
			</div>
			<div class="product_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>展之云</h4>
					<ul>
						<li><a href="product.html#zzy1">观众登记平台</a></li>
						<li><a href="product.html#zzy2">展商会员中心<i></i></a></li>
						<li><a href="product.html#zzy3">展会管理后台</a></li>
						<li><a href="product.html#zzy4">动态展位图<i></i></a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>展之通</h4>
					<ul>
						<li><a href="product1.html#zzt1">电话助手</a></li>
						<li><a href="product1.html#zzt2">提醒推送<span></span></a></li>
						<li><a href="product1.html#zzt3">公告通知</a></li>
						<li><a href="product1.html#zzt4">短信邮件接口</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>展之客</h4>
					<ul>
						<li><a href="product2.html#zzk1">客户关系管理<span></span></a></li>
						<li><a href="product2.html#zzk2">项目管理</a></li>
						<li><a href="product2.html#zzk3">财务管理</a></li>
						<li><a href="product2.html#zzk4">文档管理</a></li>
					</ul>
				</div>
			</div>
			<div class="solution_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>项目方案</h4>
					<ul>
						<li><a href="project/myself-exhibition.html">自办展项目应用方案</a></li>
						<li><a href="project/agency-exhibition.html">国外代理展项目应用方案</a></li>
						<li><a href="project/construct.html">搭建设计项目应用方案</a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>办公方案</h4>
					<ul>
						<li><a href="project/remote-office.html">异地、移动办公解决方案</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>管理方案</h4>
					<ul>
						<li><a href="project/vistorregistration.html">观众登记管理方案</a></li>
						<li><a href="project/manage-platform.html">会员管理平台方案</a></li>

					</ul>
				</div>
			</div>

		</div>
		<!-- <script type="text/javascript">
			$(function() {

				$('.childers').each(function(i) {
					$(this).find("li").on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script> -->
		<!-- start -->
		<div class="index-conter oh mag-top">
			<div class="news-border mag-top-title">

				<a href="javascript:void(0)">
					<h4 class="xh">展之微刊</h4>
				</a>

			</div>
			<div class="news-xq" id="news-conter">
				<!-- <h4><i>20190721期：</i>为什么不要用EXCEL来管理客户数据</h4>
					<p>上周在关于“展商数据被买卖”的微刊中，聊到“不要用EXCEL管理”的观点后，有人来问：为什么不能用EXCEL来管理客户数据呢？</p>
					<p>参展商采购商名单是展览行业经营的核心，确保数据安全与准确是展览公司发展的基础。所以上周微刊中聊到的那几位老板，为了防止数据被盗，索性就回归原始，用EXCEL来管理了。</p>
					<p>但从最近因数据安全来找我们的两家公司来看，这样的风险更大。</p>
					<p>一家是上海的展览公司，他们公司有一个EXCEL高手，把老板电脑里的客户数据共享给四个业务员，四个业务员虽然不能同时录入信息，但可以同时查看、联系客户，在公司经营的这半年多时间里，相安无事，感觉很安全。</p>
					<p>可是不知道怎么回事，那天打开这个EXCEL，发现被破坏了，关机重启了N次，每次的提示都是“文件已损坏，无法打开”，高手也傻眼了。</p>
					<p>无奈之下，他们想到我们是软件公司，就来咨询了。但这种情况，我也只能建议他们上网找一些“数据恢复神器”。他们后来试了好几个“神器”，还请了电脑维修人员，耗了很多精力，但数据也只是恢复了一点点，经此一劫，他们公司的客户资源几乎要重新开始收集了。</p>
					<p>第二家是我们的展览CRM用户，有个业务员一周前离职了，管理员突然发现这个业务员名下的所有客户联系人电话、手机号码都被恶意修改过，现在这些数据都成了无效的垃圾数据，就问我们是否可以恢复。</p>
					<p>这家公司现在是用我们的CRM系统，当然可以恢复，如果也如第一家公司一样，是用EXCEL管理，那真的无法恢复了。</p>
					<p>事后，我看了我们对这家公司的服务记录，这个业务员曾经咨询过我们：他名下的客户数据为什么无法导出。我们说这个权限是由您公司管理层控制的。我想如果是用EXCEL管理，他一定会带走所有客户数据。</p>
					<p>所以，为了确保参展商采购商名单安全，应该找一款靠谱的展览管理软件，而不是启用EXCEL来管理，EXCEL虽然可以杜绝被不法软件商盗卖的风险，但却无法抵挡被损坏的天灾与人祸。</p>
					<p>也许有人说：为了抵挡天灾，我的EXCEL可以多多备份。那下周我们来聊聊EXCEL多份备份带来的管理难题。</p>
					<p>多谢您的关注，如回顾上周微刊，请关注微信公众号“展之科技”。</p>
					<div class="about_new_time" style="padding-left: 130px;
		margin: 88px 0 100px 0;">
					<span>发布时间：<i>2019.07.21 </i></span>
					<span>阅读量：<i>581</i></span>
					<span>发布人 ：<i></i>展之采编</span>
				</div> -->
			</div>

		</div>




		<!-- end -->
		<div class="index-foot">
			<iframe src="foot.html" width="100%" height="100%"></iframe>
		</div>
		<div class="bank_top">
			<ul>
				<li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes"><img src="img/float/window1.png"></a></li>
				<li class="erwei">
					<img src="img/float/window2.png">
					<div class="erweima_float"><img src="img/float/Windows5.png"></div>
				</li>
				<li style="overflow: hidden;"><img src="img/float/window3.png" style="margin-rtight: 10px;">
					<span>0571-********</span>
				</li>
				<li><img src="img/float/window4.png"></li>
			</ul>
		</div>
		<script type="text/javascript">
			var newsId = getQueryString("newsId");

			var temp = getQueryString("temp"); //预览，查询临时表数据

			//var newsId =18;
			$(function() {
				if(isComputer()){
					//top.location.href = "/new/pc_officialWeb/index.html";
				}else{
					top.location.href = "../mobile-officialweb/micro-template.html?newsId="+newsId;
				}
				//alert("s")

				loadNews();
			})
			
			
			function isComputer(){
				var ua = window.navigator.userAgent.toLowerCase();
				if(ua.match(/(iPhone|iPod|iPad|Android|ios|SymbianOS)/i)){ 
					return false;
				}else{ 
					return true; 
				}
			} 


			function loadNews() {
				console.log("1")
				var url = variableSponsor + '/news/selectById';
				if (temp == 1) { //预览功能

					url = variableSponsor + '/news/selectTempById';
				}




				var htmls = "";
				$.ajax({
					url: variableSponsor + '/news/selectById', //请求服务器url地址.  
					//url: url, //请求服务器url地址.
					type: "post",
					data: {
						id: newsId

					},
					async: true,
					xhrFields: {
						withCredentials: true
					},
					beforeSend: function() {},
					success: function(data) {
						//alert(data)
						//console.log(data)

						var result = JSON.parse(data);
						console.log(result)
						// console.log(result.typeName)
						// alert(result.createTime)
						// alert(result.content)
						// alert(result.source)
						if (result.source == "") {
							var sourcehtml = " "
							sourcehtml = " "
						} else {
							sourcehtml = '<span>' + "新闻来源：" + '</i></span>'
						}


						htmls += '<h4>' + result.titleName + '</h4>' + result.content +
							'<div class="about_new_time"><span>发布时间：<i>' + result.createTime.substr(0, 10) +
							'</i></span>' + '<span><i></i></span>' + sourcehtml + result.source + '</div>'





						$("#news-conter").html(htmls);
						//loadIframe(result[0].fileName);//加载第一个

					},
					complete: function() {
						return false;
					},
					error: function(data) {
						//alert("2")
						return false;
					}
				});

			}
		</script>

	</body>
</html>
