<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>产品中心-杭州展之信息技术有限公司</title>
		<meta name="keywords" content="展览软件,会展软件,展览CRM,展会CRM,展之云,展之客,展之通,展商管理,观众登记,观众邀约,供需配对,动态展位图,展会官网,客户关系管理,呼叫中心,新特软件,杭州展之" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,专业提供展览CRM,展会项目管理,会展信息门户,展会官网,呼叫中心等系统,为会展企业提供全面解决方案。展之公司十几年积累几百家展览公司成功案例和项目经验。展之科技,专注会展信息化,助力行业发展!" />
		<link rel="icon" href="img/index/logo-title.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/style.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/distpicker.data.js"></script>
		<script src="js/distpicker.js"></script>
		<script src="js/main.js"></script>
	</head>
	<body>
		<script type="text/javascript">
			// $(function() {
			// 	function GetQueryString(name) {
			// 		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			// 		var r = window.location.search.substr(1).match(reg);
			// 		if (r != null) return unescape(r[2]);
			// 		return null;
			// 	}
			// 	var secret = GetQueryString("index");
			// })
		</script>
		<script type="text/javascript">
			function isComputer() {
				var ua = window.navigator.userAgent.toLowerCase();
				if (ua.match(/(iPhone|iPod|iPad|Android|ios|SymbianOS)/i)) {
					return false;
				} else {
					return true;
				}
			}
			if (isComputer()) {
				//top.location.href = "/new/pc_officialWeb/index.html";
			} else {
				top.location.href = "../mobile-officialweb/product.html?index=1";
			}
			window.onload = function() {
		
			};
		</script>
		<div class="index_fix">
		<!-- log区域 -->
		<div class="index_top oh">
			<div class="index-log fl">
				<a href="index.html">
					<img src="img/index/logo.png" alt="杭州展之信息技术有限公司">
				</a>
			</div>
			<div class="index-sealch fr">
				<input type="text" placeholder="搜索">
				<span></span>
			</div>
		</div>
		<!-- 导航栏 -->
		<div class="index-nav oh">
			<ul>
				<li style="margin-left: 5px;"><a href="index.html">首页</a></li>
				<li><a href="solution.html">解决方案</a></li>
				<li class="actives"><a href="productall.html" class="activess">产品中心</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<!-- <li><a href="member.html">会员中心</a></li> -->
				<li><a href="micro.html">展之微刊</a></li>
				<li><a href="about_us.html">联系我们</a></li>
			</ul>
			<!-- <div class="index-logon fr oh">
				<a href="">注册</a>
				<a href="">登录 </a>
			</div> -->
		</div>
		<div class="product_childer to-bgc  oh" style="display: none;">
			<div class="product_childer1 childers fl">
				<h4>展之云</h4>
				<ul>
					<li><a href="product.html#zzy1">观众登记平台</a></li>
					<li><a href="product.html#zzy2">展商会员中心<i></i></a></li>
					<li><a href="product.html#zzy3">展会管理后台</a></li>
					<li><a href="product.html#zzy4">动态展位图<i></i></a></li>
				</ul>
			</div>
			<div class="product_childer2 childers fl">
				<h4>展之通</h4>
				<ul>
					<li><a href="product1.html#zzt1">电话助手</a></li>
					<li><a href="product1.html#zzt2">提醒推送<span></span></a></li>
					<li><a href="product1.html#zzt3">公告通知</a></li>
					<li><a href="product1.html#zzt4">短信邮件接口</a></li>
				</ul>
			</div>
			<div class="product_childer3 childers fl">
				<h4>展之客</h4>
				<ul>
					<li><a href="product2.html#zzk1">客户关系管理<span></span></a></li>
					<li><a href="product2.html#zzk2">项目管理</a></li>
					<li><a href="product2.html#zzk3">财务管理</a></li>
					<li><a href="product2.html#zzk4">文档管理</a></li>
				</ul>
			</div>
		</div>
		<div class="solution_childer to-bgc  oh" style="display: none;">
			<div class="product_childer1 childers fl">
				<h4>项目方案</h4>
				<ul>
					<li><a href="project/myself-exhibition.html">自办展项目应用方案</a></li>
					<li><a href="project/agency-exhibition.html">国外代理展项目应用方案</a></li>
					<li><a href="project/construct.html">搭建设计项目应用方案</a></li>
				</ul>
			</div>
			<div class="product_childer2 childers fl">
				<h4>办公方案</h4>
				<ul>
					<li><a href="project/remote-office.html">异地、移动办公解决方案</a></li>
				</ul>
			</div>
			<div class="product_childer3 childers fl">
				<h4>管理方案</h4>
				<ul>
					<li><a href="project/vistorregistration.html">观众登记管理方案</a></li>
					<li><a href="project/manage-platform.html">会员管理平台方案</a></li>
	
				</ul>
			</div>
		</div>
	
	</div>
	
		<!-- <script type="text/javascript">
			$(function() {

				$('.childers').each(function(i) {
					$(this).find("li").on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script> -->
		<!-- 轮播图 -->
		<div class="product-top-banner">
			<div class="index-conter oh">
				<div class="product-top-banner2-text fl">
					<h1>展之通<i>协同交互工具</i></h1>
					<p>集电话助手、通知公告、提醒推送、邮件短信群发于一身，专业的数据统计，为营销而生</p>
				</div>
				<div class="product-top-banner-a fl oh" style="width: 380px;">
					<div class="zzt-a-list">
						<a href="#zzt1">
							<div class="zzt-a-list-img">
								<img src="img/product/zzt-icon2.png">
							</div>
							<p>电话助手</p>
						</a>
					</div>
					<div class="zzt-a-list">
						<a href="#zzt2">
							<div class="zzt-a-list-img">
								<img src="img/product/zzt-icon3.png">
							</div>
							<p>提醒推送</p>
						</a>
					</div>
					<div class="zzt-a-list">
						<a href="#zzt3">
							<div class="zzt-a-list-img">
								<img src="img/product/zzt-icon4.png">
							</div>
							<p>通知公告</p>
						</a>
					</div>
					<div class="zzt-a-list">
						<a href="#zzt4">
							<div class="zzt-a-list-img">
								<img src="img/product/zzt-icon5.png">
							</div>
							<p>短信群发</p>
						</a>
					</div>
					<div class="zzt-a-list">
						<a href="#zzt4">
							<div class="zzt-a-list-img">
								<img src="img/product/zzt-icon6.png">
							</div>
							<p id="zzt1">邮件群发</p>
						</a>
					</div>
				</div>
			</div>

		</div>
		<!-- 产品中心 -->
		<!-- 	<div class="index-width">
			<div class="index-conter">
				<div class="index_cp index_cp1" style="margin: 90px 0 110px 0;">
					<ul>
						<li style="margin-left: 5px;margin-top: 5px;" class="product_select">
							<div class="product_img" style="height: 36px;margin-top: 44px;"><img src="img/index/product1.png" alt=""></div>
							<h3>展之云企业信息门户</h3>
							<p>聚合观众登记平台、展商会员中心、展会管理后台、动态展位图4大功能，专业的会展行业全流程管理信息平台</p>
							<a href="">查看详情</a>
							<span style="width: 100%;"></span>
						</li>
						<li style="margin: 0px 33px;margin-top: 5px;">
							<div class="product_img"><img src="img/index/product2.png" alt=""></div>
							<h3>展之通协同交互工具</h3>
							<p>集电话助手、通知公告、提醒推送、邮件短信群发于一身，专业的数据统计，为营销而生</p>
							<a href="">查看详情</a>
							<span></span>
						</li>
						<li style="margin-top: 5px; width: 365px;margin-left: 5px;">
							<div class="product_img"><img src="img/index/product3.png" alt=""></div>
							<h3>展之客企业内部管理</h3>
							<p>提供CRM客户关系管理，FMS财务管理，PMS项目管理，KMS文档管理4大管理板块，助力企业成长</p>
							<a href="">查看详情</a>
							<span></span>
						</li>
					</ul>
				</div>
			</div>
		</div> -->

		<div class="index-width product_list oh">
			<!-- 展之通系列 -->
			<div class="index-width fl">
				<!-- 展之通产品中心-->
				<div class="index_width bgf3 png">
					<div class="index-conter" style="padding: 50px 0 47px;">
						<div class="product_text oh">
							<div class="product_text_left fl">
								<h3>电话助手</h3>
								<ul>
									<li>
										<h5>系统一键拨号</h5>
										<p>只需要点击电话号码旁的呼出按钮即可，提高通话效率。</p>
									</li>
									<li>
										<h5>来去电弹屏</h5>
										<p>客户来电或呼出电话，都自动弹屏显示当前公司联系人信息。</p>
									</li>
									<li>
										<h5>通话日志</h5>
										<p>来电、去电记录都可以查询，并可调取通话录音。可统计分析通话数量、时长。</p>
									</li>
								</ul>
								<a href="about_us.html" id="zzt3">立即试用</a>
							</div>
							<div class="product_text_right fl">
								<img src="img/product/zzt1.png">
							</div>
						</div>
					</div>
				</div>
				<!-- 通知公告 -->
				<div class="index_width png">
					<div class="index-conter">

						<div class="product_text oh" style="padding: 90px 0 75px;">

							<div class="product_text_left fr" style="padding-left: 120px;">
								<h3>通知公告</h3>
								<ul>
									<li>
										<h5>公司公告</h5>
										<p>公司重要事务可以发布公告，全体人员看到后，确认已读。</p>
									</li>
									<li>
										<h5>应用场景</h5>
										<p>可应用于到款认领、客户保单通知等场景。</p>
									</li>
								</ul>
								<a href="about_us.html" id="zzt2">立即试用</a>
							</div>
							<div class="product_text_right fl">
								<img src="img/product/zzt2.png">
							</div>
						</div>
					</div>
				</div>
				<!-- 提醒推送-->
				<div class="index_width bgf3 png" style="padding: 72px 0 70px;">
					<div class="index-conter">
						<div class="product_text oh">
							<div class="product_text_left fl">
								<h3>提醒推送</h3>
								<ul>
									<li>
										<h5>定时提醒</h5>
										<p>与业务系统对接，可以设置定时提自己、同事、客户等。</p>
									</li>
									<li>
										<h5>移动智能办公</h5>
										<p>与业务流程节点的提交、审批、回复动作结合，实现智能化移动办公。</p>
									</li>
									<li>
										<h5>多终端推送</h5>
										<p>可将提醒推送到PC、手机端、微信、邮件、短信等。</p>
									</li>
								</ul>
								<a href="about_us.html" id="zzt4">立即试用</a>
							</div>
							<div class="product_text_right fl">
								<img src="img/product/zzt3.png">
							</div>
						</div>
					</div>
				</div>
				<!-- 邮件短信接口-->
				<div class="index_width  png">
					<div class="index-conter">

						<div class="product_text oh" style="padding: 80px 0 150px;">
							<div class="product_text_left fr" style="padding-left: 120px;">
								<h3>邮件短信接口</h3>
								<ul>
									<li>
										<h5>短信群发</h5>
										<p>根据联系人的手机号码，群发短信。</p>
									</li>
									<li>
										<h5>EDM营销平台</h5>
										<p>可以更精准分析邮件到达率、打开率、点击率等</p>
									</li>
									<li>
										<h5>邮件群发</h5>
										<p>根据联系人的邮箱，群发邮件。</p>
									</li>
								</ul>
								<a href="about_us.html">立即试用</a>
							</div>
							<div class="product_text_right fl">
								<img src="img/product/zzt4.png">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 弹窗 -->
	<!-- 弹窗 -->
	<div class="mengban"></div>
	
	<div class="windows2 windows3" style="display:none;height: 524px;">
		<div class="windows2_title">
			<h5>试用申请<i style="color: #eb4c4c;font-size: 16px">(首次申请需完善以下信息)</i></h5>
		</div>
	
		<form action="" method="post">
			<div class="windows2_inf">
				<div class="windows2_phone">
					<input type="text" name="" id="" value="" style="padding-left: 56px; width: 300px;" />
					<input type="text" name="" id="" placeholder="请输入验证码" style="padding-left: 56px;" />
					<input type="button" name="" id="" value="获取验证码" />
				</div>
				<div class="windows2_emile">
					<input type="text" name="" id="" value="" style="padding-left: 56px; width: 300px;" />
					<input type="text" name="" id="" placeholder="请输入验证码" style="padding-left: 56px;" />
					<input type="button" name="" id="" value="获取验证码" />
				</div>
				<p>姓名<input type="text" name="" id="" value="" /></p>
				<p>公司<input type="text" name="" id="" value="" /></p>
				<div data-toggle="distpicker" class="windows2_select">
					<i>地址</i>
					<select class="form-control" id="province3" data-province="浙江省"></select>
					<select class="form-control" id="city3" data-city="杭州市"></select>
				</div>
				<span class="span_img1"><img src="img/member/lk1.png"></span>
				<span><img src="img/member/number.png"></span>
			</div>
	
			<input type="submit" name="windows_qr" value="提交申请" style="margin-top: 10px;" />
			<input type="button" name="windows_qx" class="cancel" value="取消" />
		</form>
	
	</div>
	<!-- 弹窗信息提交后提示 -->
	<div class="hint">
		<h4>申请提交成功</h4>
		<p>恭喜您已申请成功，我们将尽快与您联系，您可以继续浏览当前网页或者去会员中心查看我的产品，</p>
		<div class="hint-a">
			<a href="">我的产品</a>
			<a href="javascript:void(0)" class="hint-bank cancel" >继续浏览</a>
		</div>
	</div>
		
		<div class="index-foot">
			<iframe src="foot.html" width="100%" height="100%"></iframe>
		</div>
		<div class="bank_top">
			<ul>
				<li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes"><img src="img/float/window1.png"></a></li>
				<li class="erwei">
					<img src="img/float/window2.png">
					<div class="erweima_float"><img src="img/float/Windows5.png"></div>
				</li>
				<li style="overflow: hidden;"><img src="img/float/window3.png" style="margin-rtight: 10px;">
					<span>0571-********</span>
				</li>
				<li><img src="img/float/window4.png"></li>
			</ul>
		</div>
	</body>
</html>
