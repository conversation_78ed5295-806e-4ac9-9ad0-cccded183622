<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>联系我们-杭州展之信息技术有限公司</title>
		<meta name="keywords" content="展览软件,会展软件,展览CRM,展会CRM,展之云,展之客,展之通,展商管理,观众登记,观众邀约,供需配对,动态展位图,展会官网,客户关系管理,呼叫中心,新特软件,杭州展之" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,专业提供展览CRM,展会项目管理,会展信息门户,展会官网,呼叫中心等系统,为会展企业提供全面解决方案。展之公司十几年积累几百家展览公司成功案例和项目经验。展之科技,专注会展信息化,助力行业发展!" />
		<link rel="icon" href="img/index/logo-title.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/style.js" type="text/javascript" charset="utf-8"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.about_new_time {
				margin: 88px 0 100px 0;
				padding-left: 130px;
			}
		</style>
	</head>

	<body>
		<div class="index_fix2" style="background-color: rgb(37, 43, 58) !important;">
			<!-- log区域 -->
			<div class="index_top oh">
				<div class="index-log fl">
					<a href="index.html">
						<img src="img/index/logo.png" alt="杭州展之信息技术有限公司">
					</a>
				</div>
				<div class="index-sealch fr">
					<input type="text" placeholder="搜索">
					<span></span>
				</div>
			</div>
			<!-- 导航栏 -->
			<div class="index-nav oh">
				<ul>
					<li style="margin-left: 5px;"><a href="index.html">首页</a></li>
					<li><a href="solution.html">解决方案</a></li>
					<li><a href="productall.html">产品中心</a></li>
					<li><a href="successful.html">成功案例</a></li>
					<!-- <li><a href="member.html">会员中心</a></li> -->
					<li><a href="micro.html">展之微刊</a></li>
					<li class="actives"><a href="about_us.html" class="activess">联系我们</a></li>
				</ul>
				<!-- <div class="index-logon fr oh">
					<a href="">注册</a>
					<a href="">登录 </a>
				</div> -->
			</div>
			<div class="product_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>展之云</h4>
					<ul>
						<li><a href="product.html#zzy1">观众登记平台</a></li>
						<li><a href="product.html#zzy2">展商会员中心<i></i></a></li>
						<li><a href="product.html#zzy3">展会管理后台</a></li>
						<li><a href="product.html#zzy4">动态展位图<i></i></a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>展之通</h4>
					<ul>
						<li><a href="product1.html#zzt1">电话助手</a></li>
						<li><a href="product1.html#zzt2">提醒推送<span></span></a></li>
						<li><a href="product1.html#zzt3">公告通知</a></li>
						<li><a href="product1.html#zzt4">短信邮件接口</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>展之客</h4>
					<ul>
						<li><a href="product2.html#zzk1">客户关系管理<span></span></a></li>
						<li><a href="product2.html#zzk2">项目管理</a></li>
						<li><a href="product2.html#zzk3">财务管理</a></li>
						<li><a href="product2.html#zzk4">文档管理</a></li>
					</ul>
				</div>
			</div>
			<div class="solution_childer to-bgc  oh" style="display: none;">
				<div class="product_childer1 childers fl">
					<h4>项目方案</h4>
					<ul>
						<li><a href="project/myself-exhibition.html">自办展项目应用方案</a></li>
						<li><a href="project/agency-exhibition.html">国外代理展项目应用方案</a></li>
						<li><a href="project/construct.html">搭建设计项目应用方案</a></li>
					</ul>
				</div>
				<div class="product_childer2 childers fl">
					<h4>办公方案</h4>
					<ul>
						<li><a href="project/remote-office.html">异地、移动办公解决方案</a></li>
					</ul>
				</div>
				<div class="product_childer3 childers fl">
					<h4>管理方案</h4>
					<ul>
						<li><a href="project/vistorregistration.html">观众登记管理方案</a></li>
						<li><a href="project/manage-platform.html">会员管理平台方案</a></li>

					</ul>
				</div>
			</div>

		</div>
		<!-- <script type="text/javascript">
			$(function() {

				$('.childers').each(function(i) {
					$(this).find("li").on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script> -->
		<!-- start -->
		<div class="top_picture">
			<a href="">立即联系</a>
		</div>
		<!-- abous-list -->
		<div class="index_width about_width">
			<div class="index-conter">
				<div class="news_list oh">
					<ul>
						<li><a href="about_us.html?index=0">联系我们</a></li>
						<li><a href="about_us.html?index=1">关于我们</a></li>
						<li class="news_list_active"><a href="about_us.html?index=2">展之动态</a></li>
					</ul>
				</div>
			</div>
		</div>
		<!-- 	<script type="text/javascript">
			$(function() {

				$('.news_list ul li').each(function(i) {
					$(".news_list ul li").eq(i).on("click", function() {
						$(location).attr('href', 'about_us.html?index=' + i)
					})
				})
			})
		</script> -->
		<!-- abous-content -->
		<!-- 展之动态 -->
		<div class="index-conter oh">
			<div class="about_new_title">
				<h4>展之动态</h4>
				<!-- <h5 id="wk">展之微刊</h5> -->
			</div>
			<!-- <script type="text/javascript">
				$(function() {

					$('.news-border').each(function(i) {
						$(this).find("a").on("click", function() {
							$(location).attr('href', 'about_us.html?index=2')
						})
					})
				})
			</script> -->
			<div class="news-xq" id="news-conter">
				<!-- <h4><i>20190701</i>期：杭州农业展览会</h4>
				<p>观众邀约现在有很多途径，电话邀约还是观众邀约环节中的一个重要部分。今天我特地来讲讲有关美容展展会观众邀约过程中常见的一个问题。</p>
				<p>以美容展为例，话务员邀约到一个专业观众都是要算业绩，但是美容展观众是美容院的老板，其名下经常有两家或两家以上的美容院。但是分配给话务员的时候，都是以店铺为主体进行分配。导致实际上仅邀约到一位观众，但是系统中却显示有两位观众参加。</p>
				<img src="img/connect/new4.jpg">
				<p>以美容展为例，话务员邀约到一个专业观众都是要算业绩，但是美容展观众是美容院的老板，其名下经常有两家或两家以上的美容院。但是分配给话务员的时候，都是以店铺为主体进行分配。导致实际上仅邀约到一位观众，但是系统中却显示有两位观众参加。</p>
				<img src="img/connect/new5.jpg">
				<p>以美容展为例，话务员邀约到一个专业观众都是要算业绩，但是美容展观众是美容院的老板，其名下经常有两家或两家以上的美容院。但是分配给话务员的时候，都是以店铺为主体进行分配。导致实际上仅邀约到一位观众，但是系统中却显示有两位观众参加</p>
             <div class="about_new_time" style="padding-left: 130px;
                 margin: 88px 0 100px 0;">
				<span>发布时间：<i>2019.07.04 </i></span>
				<span>阅读量：<i>956</i></span>
				<span>发布人 ：<i></i>展之采编</span>
			</div> -->
			</div>

		</div>




		<!-- end -->
		<div class="index-foot">
			<iframe src="foot.html" width="100%" height="100%"></iframe>
		</div>
		<div class="bank_top">
			<ul>
				<li><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=**********&site=qq&menu=yes"><img src="img/float/window1.png"></a></li>
				<li class="erwei">
					<img src="img/float/window2.png">
					<div class="erweima_float"><img src="img/float/Windows5.png"></div>
				</li>
				<li style="overflow: hidden;"><img src="img/float/window3.png" style="margin-rtight: 10px;">
					<span>0571-********</span>
				</li>
				<li><img src="img/float/window4.png"></li>
			</ul>
		</div>
	</body>
	<script type="text/javascript">
		var newsId = getQueryString("newsId");

		var temp = getQueryString("temp"); //预览，查询临时表数据

		//var newsId =18;
		$(function() {
			//alert("s")
			if(isComputer()){
				//top.location.href = "/new/pc_officialWeb/index.html";
			}else{
				top.location.href = "../mobile-officialweb/news-details.html?newsId="+newsId;
			}
			
			
				
			loadNews();
		})

		function isComputer(){
			var ua = window.navigator.userAgent.toLowerCase();
			if(ua.match(/(iPhone|iPod|iPad|Android|ios|SymbianOS)/i)){ 
				return false;
			}else{ 
				return true; 
			}
		} 
			





		function loadNews() {
			console.log("1")
			var url = variableSponsor + '/news/selectById';
			if (temp == 1) { //预览功能

				url = variableSponsor + '/news/selectTempById';
			}




			var htmls = "";
			$.ajax({
				url: variableSponsor + '/news/selectById', //请求服务器url地址.  
				//url: url, //请求服务器url地址.
				type: "post",
				data: {
					id: newsId

				},
				async: true,
				xhrFields: {
					withCredentials: true
				},
				beforeSend: function() {},
				success: function(data) {
					//alert(data)
					//console.log(data)

					var result = JSON.parse(data);
					console.log(result)
					// console.log(result.typeName)
					// alert(result.createTime)
					// alert(result.content)
					// alert(result.source)
					if (result.source == "") {
						var sourcehtml = " "
						sourcehtml = " "
					} else {
						sourcehtml = '<span>' + "新闻来源：" + '</i></span>'
					}


					htmls += '<h4>' + result.titleName + '</h4>' + result.content +
						'<div class="about_new_time"><span>发布时间：<i>' + result.createTime.substr(0, 10) +
						'</i></span>' + '<span><i></i></span>' + sourcehtml + result.source + '</div>'





					$("#news-conter").html(htmls);
					//loadIframe(result[0].fileName);//加载第一个

				},
				complete: function() {
					return false;
				},
				error: function(data) {
					//alert("2")
					return false;
				}
			});

		}
	</script>

</html>
