<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>解决方案</title>
		<link rel="stylesheet" type="text/css" href="css/allstyle.css" />
		<script type="text/javascript" src="js/rem.js"></script>
		<script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
		<script src="js/mobile.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/distpicker.data.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/distpicker.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/main.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body style="background-color: #ffffff;">
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="index.html">
					<img src="img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>

		<div class="contact-baner" style="margin-top: .9rem;">
			<img src="img/index/banner-vip.jpg">
			<h5>异地、移动办公解决方案</h5>
			<em></em>
			<p>产品升级限时免费体验</p>
		</div>

		<div class="index-product-top product-tit" style="padding-left: .24rem;background-color: #f5f8fd !important;">
			<p>异地、移动办公解决方案</p>
		</div>
		<div class="Project-bgc" style="background-color: #f5f8fd !important;">
			<h2>方案综述</h2>
			<p>采用云服务将分公司、办事处、出差人员等接入到企业信息门户，统一处理客户资源、商机跟进、展位状态控制、财务管理、后期服务管理等，即保证公司数据安全，又能随时随地办公。
			</p>
		</div>
		<div class=" Project-bgc">
			<h2 style="margin-top: .7rem;">需求背景</h2>
			<p>随着会展行业的快速发展，展览公司异地办公和移动办公已成为普遍需求。比如：</p>
			<p>1.展览公司在异地办展、招展等业务环节需要设立分公司和办事处；</p>
			<p>2. 异地合作办展的公司需要按项目协同办公；</p>
			<p>3.出差人员需要移动办公，或管理人员需要在家加班，或有员工长期SOHO上班；</p>
			<p>4. 展会现场需要接入公司内部查询和录入数据等等。
				这些需求最本质的问题就是怎样实现异地办公局域网中的电脑或移动接入的笔记本电脑等能实现远程接入到公司总部，连接CRM、项目管理、OA等应用服务器，实现远程办公</p>
			<div class="myself-box4-img">
				<img src="img/project/remote-office/img1.jpg">
			</div>

		</div>
		<div class="Project-bgc myself-box3 new-project">
			<h2 style="padding-bottom: 0;">多种服务模式的选择</h2>
			<p style="text-indent: 0;">同时支持私有云和公有云多种网络结构
			</p>
			<div class="myself-box3-img" style="margin-top: .42rem;">
				<img src="img/project/remote-office/img2.jpg">
			</div>
		</div>
		<div class=" myself-box4 myself-box5" style="margin-bottom: 2.32rem;">
			<h2>展之私有云服务模式特点</h2>
			<div class="myself-box5-img">
				<img src="img/project/remote-office/img3.jpg">
			</div>
			<h6>01 安全</h6>
			<p>展之云服务只进行访问过程处理和安全控制，实际数据传输仍是外网电脑跟服务器间点对点直接连接，服务器数据库由公司自己掌控，不受任何第三方介入，安全可靠。</p>
			<h6>02 速度</h6>
			<p>若服务器在公司内部，则总部局域网内的客户端访问数据库的速度不受影响。外网访问速度和公司接入互联网的宽带性能有关。若服务器若选择在云服务，则需要租用足够带宽以保证访问速度。</p>
			<h6>03 方便</h6>
			<p>外网电脑不需要了解中间过程细节，只要设置展之云机构号密码即可访问，简单方便。</p>
			<h6>04 集成</h6>
			<p>与CRM、会员中心、观众登记等系统紧密集成，减少中间环节，运行稳定，维护更省心。</p>
		</div>

		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="product.html">最新产品</a></li>
				<li><a href="manage-platform.html">展之方案</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
