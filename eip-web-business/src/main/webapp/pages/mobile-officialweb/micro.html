<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>杭州展之信息技术有限公司|展览软件|会展软件|展览CRM|展商管理|观众登记|客户关系管理|会展信息门户|呼叫中心|展之科技|新特软件</title>
		<meta name="keywords" content="展览软件,新特软件,会展软件,展览CRM,展会CRM,展会管理系统,客户管理软件,客户关系管理,呼叫中心,邮件群发,短信群发,网络传真,软电话,一键拨号,自动打电话,电话营销" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,展览CRM,会展软件,展会销售管理软件,优秀CRM解决方案,几百家展览公司成功案例，10多年专注会展行业提供客户关系管理系统和呼叫中心平台。展之科技，助力企业发展!" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />
		<title></title>
		<link rel="stylesheet" type="text/css" href="dist/swipeslider.css">
		<link rel="stylesheet" type="text/css" href="css/allstyle.css" />
		<script type="text/javascript" src="js/rem.js"></script>
		<script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
		<script type="text/javascript" src="js/mobile.js"></script>
		<script type="text/javascript" src="dist/swipeslider.min.js"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.load-more{
				background-color: #fff;
				margin-top: .3rem;
				font-size: .32rem;
				text-align: center;
				padding: .3rem 0;
				color: #00a4ff;
			}
			.load-more a{
				color: #00a4ff;
			}
		</style>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="index.html">
					<img src="img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>

		<div class="contact-baner" style="margin-top: .9rem;">
			<img src="img/index/banner-vip.jpg">
			<h5>展之微刊</h5>
			<em></em>
			<p>产品升级限时免费体验</p>
		</div>
		<!-- 解决方案 -->
		<div class="index-solution" style="background-color: #fff;">
			<div class="index-product-top" style="padding: .28rem 0 .4rem 0;">
				<p>展之微刊</p>
			</div>

			<!-- 展之微刊列表 -->
			<div class="contact-lists1 lists2">
				<ul id="newsList">
					<!-- <li>
						<a href="news/micro-news/20190721082311.html">
							<div class="contact-lists1-text">
								<h4>第6期 为什么不要用EXCEL来管理客户数据</h4>
								<p>上周在关于“展商数据被买卖”的微刊中，聊到“不要用EXCEL管理”的观点后，有人来问：为什么不能用EXCEL来管理客户数据呢？</p>
							</div>
						</a>
					</li>
					<li>
						<a href="news/micro-news/20190715150327.html">
							<div class="contact-lists1-text">
								<h4>第5期 客户数据安全就是展览业的经营保障</h4>
								<p>前两天，有个从未谋面的微信好友出于好心，给我发了广交会展商数据。我没仔细看，就发给几个平时与自己交往较多的展览行业客户，还收到了他们的感谢。</p>
							</div>
						</a>
					</li>
					<li>
						<a href="news/micro-news/20190402221327-79.html">
							<div class="contact-lists1-text">
								<h4>第4期 如何识别重复的观众邀约数据？</h4>
								<p>观众邀约现在有很多途径，电话邀约还是观众邀约环节中的一个重要部分。今天我特地来讲讲有关美容展展会观众邀约过程中常见的一个问题。</p>
							</div>
						</a>
					</li>
					<li>
						<a href="news/micro-news/20190326181750-78.html">
							<div class="contact-lists1-text">
								<h4>第3期 怎样更好的利用会刊数据？</h4>
								<p>上会展行业人员需要经常收集会刊数据，扩充自己的客户池。那怎么高效的利用这些会刊数据呢？
								</p>
							</div>
						</a>
					</li>
					<li>
						<a href="news/micro-news/20190312091658-77.html">
							<div class="contact-lists1-text">
								<h4>第2期 外展企业款项管理新思路</h4>
								<p>
									财务管理不管在哪个企业中，都是很重要的一个环节。企业是作为一种以盈利为目的的经济组织而存在，企业的根本宗旨就是盈利。盈利的实现，是以良好的财务管理为基础的。

								</p>
							</div>
						</a>
					</li>
					<li>
						<a href="news/micro-news/20190224092300-76.html">
							<div class="contact-lists1-text">
								<h4>第1期 一秒钟搞定工作日志，让销售工作更高效</h4>
								<p>
									您的销售团队还在用传统的小本子或excel表格记录客户信息和业务日志么？如果您和您的团队还在使用这种工作方式，并且在工作过程中经常出现以下问题：

								</p>
							</div>
						</a>
					</li> -->

				</ul>
				<div class="load-more" id="load">
					<a href="javascript:void(0);" onclick="loadMore()">点击加载更多</a>
				</div>
				<div class="contact-lists1-end" id="noData" style="display: none;">
					———— 没有更多了 ————
				</div>
			</div>

		</div>
		
		
		
		<script>
			//var newsTypeId = getQueryString("newsTypeId");
			//var newsTypeId = 10;
			var page = 1;
			var rows = 4;
			var num = 0;
			var total = 0;
			//var newsTypeId = getQueryString("newsTypeId");
			//var typeName = getQueryString("typeName");
		
			$(function() {
				
				loadNews();
			})
		
		
			function loadNews() {
		
				$.ajax({
					url: variableSponsor + '/news/getList', //请求服务器url地址.   
					type: "post",
					data: {
						page: page,
						rows: rows,
						exhibitCode: exhibitCode,
						projectId: pid,
						newsTypeId: micronews,
						notInId: micronews
						//isShow: true
					},
					async: false,
					xhrFields: {
						withCredentials: true
					},
					success: function(data) {
		
						console.log(data)
						var data = JSON.parse(data);
						var total = data.total;
						var result = data.rows;
	
							var newsListHtml = '';
							for (var i = 0; i < result.length; i++) {
								num++;
								newsListHtml += '<li><a href="javascript:void(0);" onclick="loadIframe(' +
									'\'' + result[i].fileNameMobile + '\',' + result[i].newsId + ')" >' +
									'<div class="contact-lists1-text"><h4>' + result[i].titleName + '</h4><p>' + result[i].brief +
									'</p></div></a></li>'
							}
							$("#newsList").append(newsListHtml);
		
						
		
		
		
						//alert(html) 
						if (newsListHtml == "") {
							//alert("没有更多了");
							$("#load").hide();
							$("#noData").show();
						} else {
							$("#newsList").append();
						}
		
						if (result.total <= num) {
							$("#load").hide();
							$("#noData").show();
						}
						return false;
					},
					error: function(data) {
						alert("数据加载失败");
						return false;
					}
				});
			}
		
			function loadMore() {
				page++;
				loadNews();
			}
			
			function loadIframe(fileName,id){
				window.location.href = "micro-template.html" +'?newsId=' + id;
			}
		</script>
		
		
		
		
		
		
	
		
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="product.html">最新产品</a></li>
				<li><a href="manage-platform.html">展之方案</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>


	</body>
</html>
