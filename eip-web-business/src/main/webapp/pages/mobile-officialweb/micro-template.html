<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>杭州展之信息技术有限公司|展览软件|会展软件|展览CRM|展商管理|观众登记|客户关系管理|会展信息门户|呼叫中心|展之科技|新特软件</title>
		<meta name="keywords" content="展览软件,新特软件,会展软件,展览CRM,展会CRM,展会管理系统,客户管理软件,客户关系管理,呼叫中心,邮件群发,短信群发,网络传真,软电话,一键拨号,自动打电话,电话营销" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,展览CRM,会展软件,展会销售管理软件,优秀CRM解决方案,几百家展览公司成功案例，10多年专注会展行业提供客户关系管理系统和呼叫中心平台。展之科技，助力企业发展!" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />
		<title></title>
		<link rel="stylesheet" type="text/css" href="dist/swipeslider.css">
		<link rel="stylesheet" type="text/css" href="css/allstyle.css" />
		<script type="text/javascript" src="js/rem.js"></script>
		<script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
		<script type="text/javascript" src="js/mobile.js"></script>
		<script type="text/javascript" src="dist/swipeslider.min.js"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.load-more{
				background-color: #fff;
				margin-top: .3rem;
				font-size: .32rem;
				text-align: center;
				padding: .3rem 0;
				color: #00a4ff;
			}
			.load-more a{
				color: #00a4ff;
			}
		</style>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="index.html">
					<img src="img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>

		<div class="contact-baner" style="margin-top: .9rem;">
			<img src="img/index/banner-vip.jpg">
			<h5>展之微刊</h5>
			<em></em>
			<p>产品升级限时免费体验</p>
		</div>
		<!-- 解决方案 -->
		<div class="index-solution" style="background-color: #fff;">
			<div class="index-product-top" style="padding: .28rem 0 .4rem 0;">
				<p>展之微刊</p>
			</div>

			<div class="news-details" id="news-conter" style="margin-top: 0;">
				<!-- <h4>杭州展之科技官网隆重上线</h4>
				<p>热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网。</p>
				<img src="img/connect/new.jpg">
				<p>热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网。 </p>
				<p>热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网隆重上线，热烈祝贺杭州展之科技官网。 </p>
				<div class="news-details-time">
					<span>发布时间:<i>2019.07.04</i></span>
					<span>阅读量：<i>956 </i></span>
					<span>发布人：<i>展之采编</i></span>
				</div> -->
			</div>
		</div>
		
		
		
		<script type="text/javascript">
			var newsId = getQueryString("newsId");
		
			var temp = getQueryString("temp"); //预览，查询临时表数据
		
			//var newsId =18;
			$(function() {
				//alert("s")
		
				loadNews();
			})
		
		
			function loadNews() {
				console.log("1")
				var url = variableSponsor + '/news/selectById';
				if (temp == 1) { //预览功能
		
					url = variableSponsor + '/news/selectTempById';
				}
		
		
		
		
				var htmls = "";
				$.ajax({
					url: variableSponsor + '/news/selectById', //请求服务器url地址.  
					//url: url, //请求服务器url地址.
					type: "post",
					data: {
						id: newsId
		
					},
					async: true,
					xhrFields: {
						withCredentials: true
					},
					beforeSend: function() {},
					success: function(data) {
						//alert(data)
						//console.log(data)
		
						var result = JSON.parse(data);
						console.log(result)
						// console.log(result.typeName)
						// alert(result.createTime)
						// alert(result.content)
						// alert(result.source)
						if (result.source == "") {
							var sourcehtml = " "
							sourcehtml = " "
						} else {
							sourcehtml = '<span>' + "新闻来源：" + '</i></span>'
						}
		
		
		
		
						htmls += '<h4>' + result.titleName + '</h4>' + '<div class="news-style">' +
							result.content + '</div>' + '<div class="news-details-time"><span>发布时间:<i>' +
							result.createTime.substr(0, 10) + '</i></span>' +
							'<span><i></i></span>'+ sourcehtml + result.source + '</div>'
							
		
		
						$("#news-conter").html(htmls);
						//loadIframe(result[0].fileName);//加载第一个
		
					},
					complete: function() {
						return false;
					},
					error: function(data) {
						//alert("2")
						return false;
					}
				});
		
			}
		</script>
		
		
		
		
	
		
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="product.html">最新产品</a></li>
				<li><a href="manage-platform.html">展之方案</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>


	</body>
</html>
