<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>新闻详情</title>
		<link rel="stylesheet" type="text/css" href="../../css/allstyle.css" />
		<script type="text/javascript" src="../../js/rem.js"></script>
		<script type="text/javascript" src="../../js/jquery-2.1.1.min.js"></script>
		<script src="../../js/mobile.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="../../index.html">
					<img src="../../img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="../../img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="../../index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="../../solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="../../successful.html" class="font-sz">成功案例</a>
				</li>

				<li class="first">
					<a href="../../micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="../../contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', '../../product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists contacts" style="padding-left: 0 ;">
			<i></i>
			<ul>
				<li>
					<a href="javascript:void(0)">
						联系我们
						<em></em>
					</a>
				</li>
				<li>
					<a href="javascript:void(0)">
						关于我们
						<em></em>
					</a>
				</li>
				<li class="contact-active">
					<a href="javascript:void(0)">
						展之动态
						<em class="contact-em-active"></em>
					</a>
				</li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.contact-lists ul li').each(function(i) {
					$('.contact-lists ul li').eq(i).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + i)
					})
				})
			});
			$(function() {
				$('.contact-lists i').eq(0).on("click", function() {
					$(location).attr('href', '../../contact.html?index=' + 2)
				})
			})
		</script>
		<!-- 新闻详情 -->
		<div class="news-details">
			<h4>20190312期：外展企业款项管理新思路</h4>
			<p>财务管理不管在哪个企业中，都是很重要的一个环节。企业是作为一种以盈利为目的的经济组织而存在，企业的根本宗旨就是盈利。盈利的实现，是以良好的财务管理为基础的。</p>
			<p>会展企业在财务管理上表现的特征主要如下：</p>
			<p>1. 收入信息量大：收入费用包括摊位费、注册费、人员费、酒店单房差、机票费等等，内容非常多。</p>
			<p>2. 收入弹性大：展会的收入费用，不是一次性合同确定好就完结，而是在摊位合同或者人员合同确定之后，客服后期跟进还不断产生新的费用。</p>
			<img src="../../img/news/micro-news/20190310225313161316.jpg">
			<p>那针对会展企业费用杂且多变的特性，我们有什么办法解决这个问题呢？</p>
			<p>展之展览crm系统针对展会款项多且杂的情况，提供展会管理，人员管理，会刊管理，搭建管理等等跟展览相关的业务模板。销售在签订合同的时候，实时将展位应收款及时录入，并上传合同附件作为审查，确保款项的正确性。</p>
			<p>客服人员在提供人员服务，搭建服务的时候，也直接在相应模板录入该项款项的详细费用。</p>
			<p>这些费用会自动统计到财务页面。这个功能估计很多软件都能实现，我们软件最大的特点就是收入任何费用如果款项如果发生了变化，财务人员都能知道，并对这些费用进行审查。如果确定费用无误，才算审核通过。确保每笔款项的正确性。</p>
			<div class="news-details-time">
				<span>发布时间:<i>2019.03.12 </i></span>
				<span>阅读量：<i>982</i></span>
				<span>发布人：<i>展之采编</i></span>
			</div>
		</div>
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="../../product.html">最新产品</a></li>
				<li><a href="../../manage-platform.html">展之方案</a></li>
				<li><a href="../../successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
