<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>新闻详情</title>
		<link rel="stylesheet" type="text/css" href="../../css/allstyle.css" />
		<script type="text/javascript" src="../../js/rem.js"></script>
		<script type="text/javascript" src="../../js/jquery-2.1.1.min.js"></script>
		<script src="../../js/mobile.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="../../index.html">
					<img src="../../img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="../../img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="../../index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="../../solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="../../successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="../../micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="../../contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', '../../product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists contacts" style="padding-left: 0 ;">
			<i></i>
			<ul>
				<li>
					<a href="javascript:void(0)">
						联系我们
						<em></em>
					</a>
				</li>
				<li>
					<a href="javascript:void(0)">
						关于我们
						<em></em>
					</a>
				</li>
				<li class="contact-active">
					<a href="javascript:void(0)">
						展之动态
						<em class="contact-em-active"></em>
					</a>
				</li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.contact-lists ul li').each(function(i) {
					$('.contact-lists ul li').eq(i).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + i)
					})
				})
			});
			$(function() {
				$('.contact-lists i').eq(0).on("click", function() {
					$(location).attr('href', '../../contact.html?index=' + 2)
				})
			})
		</script>
		<!-- 新闻详情 -->
		<div class="news-details">
			<h4>20190715期：客户数据安全就是展览业的经营保障</h4>
			<p>最近我干了一件蠢事，被老板臭骂了一顿。</p>
			<p>前两天，有个从未谋面的微信好友出于好心，给我发了广交会展商数据。我没仔细看，就发给几个平时与自己交往较多的展览行业客户，还收到了他们的感谢。</p>
			<p>于是感觉自己做了好事，屁颠屁颠地跟同事说自己的“丰功伟绩”。正好被老板听到了，被他一顿臭骂，说我们是做展览公司管理软件的，你给客户发这种名单，不是自己找死吗？有些客户不懂电脑，就以为我们能盗用他们的数据，然后去卖。</p>
			<p>我一听，慌了，因为，我还把名单发给一个刚签了合同，还没打款的客户，他要是也这么认为，我就惨了。赶紧向他解释去，还好，他明事理，说这种名单他在某宝上看人卖过，知道不是我们所为，但是，他们同事也确实在讨论数据安全的问题。我赶紧讲一下整个软件的实施安装运行过程，幸好，他信任我们。</p>
			<p>无独有偶，昨天又有一个老客户来问，说老板在北京开会，茶歇时，几个老板闲聊，讨论最近使用管理系统安全性的问题，好几个都说不安全，尤其是背后有大财团支持的公司开发的管理软件，人家盯牢的本来就不是卖软件的那仨瓜俩枣收益，而是看中用户手中的数据。</p>
			<p>于是这几个老板都把系统清空了，继续用EXCEL管理。他们老板就打电话让她来问问我们，我们的系统是否能确保安全。</p>
			<p>我气定神闲地告诉她：用我们的系统，安全问题绝对可以放心。</p>
			<p>一是所有数据都装在您公司的电脑里，完全不受我公司控制。所以，从您公司系统开始运行的那一刻起，所有的维护，都必须是您公司发起系统外申请，我们通过QQ等远程工具进行维护，平时连您公司的系统运行情况我们都无法掌握，更何况是里面的客户数据，所以，请绝对放心。</p>
			<p>二是服务器电脑设置开机密码，数据库也有密码，系统登陆又有密码，三重安全保障。</p>
			<p>三是业务员电脑不保存任何数据，业务员操作都会同步更新到服务器，做到数据统一存放，统一管理。</p>
			<p>四是可以设置不同级别的访问权限，查看、操作、删除、导出都可以通过权限设置。</p>
			<p>她说经过这么几年系统的运行，他们是绝对信任我们的，所以，也就是问问</p>
			<p>同时，我再三叮嘱他，千万别用EXCEL来管理客户数据，并讲了几个案例，具体请看下周微刊，可以关注本公司公众号“展之科技”，查看更多管理案例。</p>
			<div class="news-details-time">
				<span>发布时间:<i>2019.07.15 </i></span>
				<span>阅读量：<i>581</i></span>
				<span>发布人：<i>展之采编</i></span>
			</div>
		</div>
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="../../product.html">最新产品</a></li>
				<li><a href="../../manage-platform.html">展之方案</a></li>
				<li><a href="../../successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
