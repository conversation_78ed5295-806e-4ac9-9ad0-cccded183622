<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>新闻详情</title>
		<link rel="stylesheet" type="text/css" href="../../css/allstyle.css" />
		<script type="text/javascript" src="../../js/rem.js"></script>
		<script type="text/javascript" src="../../js/jquery-2.1.1.min.js"></script>
		<script src="../../js/mobile.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="../../index.html">
					<img src="../../img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="../../img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="../../index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="../../solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="../../successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="../../micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="../../contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', '../../product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists contacts" style="padding-left: 0 ;">
			<i></i>
			<ul>
				<li>
					<a href="javascript:void(0)">
						联系我们
						<em></em>
					</a>
				</li>
				<li>
					<a href="javascript:void(0)">
						关于我们
						<em></em>
					</a>
				</li>
				<li class="contact-active">
					<a href="javascript:void(0)">
						展之动态
						<em class="contact-em-active"></em>
					</a>
				</li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.contact-lists ul li').each(function(i) {
					$('.contact-lists ul li').eq(i).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + i)
					})
				})
			});
			$(function() {
				$('.contact-lists i').eq(0).on("click", function() {
					$(location).attr('href', '../../contact.html?index=' + 2)
				})
			})
		</script>
		<!-- 新闻详情 -->
		<div class="news-details">
			<h4><i>20190524期：</i>一秒钟搞定工作日志，让销售工作更高效</h4>
			<p>您的销售团队还在用传统的小本子或excel表格记录客户信息和业务日志么？如果您和您的团队还在使用这种工作方式，并且在工作过程中经常出现以下问题：</p>
			<img src="../../img/news/micro-news/2019052409.png">
			<p>要想要解决这些问题，必须使用先进的客户关系管理系统（CRM），借助信息化的平台，实现销售团队的高效管理。</p>
			<p> 展之CRM系统，以客户为中心，以销售团队管理为核心，着重提高客户满意度，培养客户忠诚度的一款客户关系管理系统，对于以上问题有着其独到之处。</p>
			<p>一、 联系记录管理</p>
			<p>业务员在跟每一个客户联系之后，可以直接在联系记录里面新增一条记录。记录内容包括客户联系内容、联系时间、客户状态以及联系中涉及的文件等等，方便管理人员对其进行批阅；同时还可以设置联系提醒给其他人，保证跟进客户的每一个环节不会出现纰漏。</p>
			<img src="../../img/news/micro-news/20180524111266066606.jpg">
			<p>以美容展为例，话务员邀约到一个专业观众都是要算业绩，但是美容展观众是美容院的老板，其名下经常有两家或两家以上的美容院。但是分配给话务员的时候，都是以店铺为主体进行分配。导致实际上仅邀约到一位观众，但是系统中却显示有两位观众参加</p>
			<p>二、参展记录管理</p>
			<p>对于已经成交的客户，可以记录每一个展商以及观众的参展记录，直观的分析出客户的参展意向，以及近期的业务进展情况。</p>
			<p>参展之后系统自动生成楣板、会刊、交流会、参展人员、后期服务信息等汇总自动生成，同时每届展会有销售情况、财务、业绩汇总的统计，实现参展客户的应收实收情况之间的零差错。</p>
			<img src="../../img/news/micro-news/20180524111389828982.jpg">
			<p>三、统计报表</p>
			<img src="../../img/news/micro-news/20180524111315981598.png" alt="">
			<p>1.客户及联系统计报表</p>
			<p>查询本日，本周，本月业务员联系客户的业务量，也可以查询相应业务员新增的客户数。</p>
			<p>2.联系记录客户状态统计</p>
			<p>查询每个业务人员手头客户的状态情况，全面了解项目业务的进度，方便业务主管实时，全面，准确的了解业务团队的工作状态。。</p>
			<p>3.销售汇总报表</p>
			<p>可以根据展会名称、签约日期、是否已签约等条件来进行销售汇总，并进行合计。同时可以打印或者导出为Excel表格给领导查阅。</p>

			<p> 如果您想了解更多关于展之CRM软件的功能，扫描下方的二维码，赶快关注我们吧！</p>
			<img src="../../img/news/micro-news/20180305174665396539.png">
			<div class="news-details-time">
				<span>发布时间:<i>2019.03.12 </i></span>
				<span>阅读量：<i>982</i></span>
				<span>发布人：<i>展之采编</i></span>
			</div>
		</div>
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="../../product.html">最新产品</a></li>
				<li><a href="../../manage-platform.html">展之方案</a></li>
				<li><a href="../../successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
