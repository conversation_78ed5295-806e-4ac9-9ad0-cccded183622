<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>新闻详情</title>
		<link rel="stylesheet" type="text/css" href="../../css/allstyle.css" />
		<script type="text/javascript" src="../../js/rem.js"></script>
		<script type="text/javascript" src="../../js/jquery-2.1.1.min.js"></script>
		<script src="../../js/mobile.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="../../index.html">
					<img src="../../img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
				<div class="d-firstNav s-firstNav">
					<span>超级管理员，你好</span>
					<i class="fa fa-caret-right fr"><img src="../../img/index/arrow3.png"></i>
				</div>
				<ul class="d-firstDrop s-firstDrop">
					<li class="s-secondItem"><a href="#">账号设置</a>
					</li>
					<li class="s-secondItem"><a href="#">未读消息</a>
					</li>
					<li class="s-secondItem"><a href="#">我的试用</a>
					</li>
					<li class="s-secondItem"><a href="#">退出</a>
					</li>
				</ul>
			</li> -->
				<li class="first">
					<a href="../../index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="../../solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="../../successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="../../micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
				<a href="#" class="font-sz">服务体系</a>
			</li>
			<li class="first">
				<a href="#" class="font-sz">会员中心</a>
			</li> -->
				<li class="first">
					<a href="../../contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', '../../product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists contacts" style="padding-left: 0 ;">
			<i></i>
			<ul>
				<li>
					<a href="javascript:void(0)">
						联系我们
						<em></em>
					</a>
				</li>
				<li>
					<a href="javascript:void(0)">
						关于我们
						<em></em>
					</a>
				</li>
				<li class="contact-active">
					<a href="javascript:void(0)">
						展之动态
						<em class="contact-em-active"></em>
					</a>
				</li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.contact-lists ul li').each(function(i) {
					$('.contact-lists ul li').eq(i).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + i)
					})
				})
			});
			$(function() {
				$('.contact-lists i').eq(0).on("click", function() {
					$(location).attr('href', '../../contact.html?index=' + 2)
				})
			})
		</script>
		<!-- 新闻详情 -->
		<div class="news-details">
			<h4>20190721期：为什么不要用EXCEL来管理客户数据</h4>
			<p>上周在关于“展商数据被买卖”的微刊中，聊到“不要用EXCEL管理”的观点后，有人来问：为什么不能用EXCEL来管理客户数据呢？</p>
			<p>参展商采购商名单是展览行业经营的核心，确保数据安全与准确是展览公司发展的基础。所以上周微刊中聊到的那几位老板，为了防止数据被盗，索性就回归原始，用EXCEL来管理了。</p>
			<p>但从最近因数据安全来找我们的两家公司来看，这样的风险更大。</p>
			<p>一家是上海的展览公司，他们公司有一个EXCEL高手，把老板电脑里的客户数据共享给四个业务员，四个业务员虽然不能同时录入信息，但可以同时查看、联系客户，在公司经营的这半年多时间里，相安无事，感觉很安全。</p>
			<p>可是不知道怎么回事，那天打开这个EXCEL，发现被破坏了，关机重启了N次，每次的提示都是“文件已损坏，无法打开”，高手也傻眼了。</p>
			<p>无奈之下，他们想到我们是软件公司，就来咨询了。但这种情况，我也只能建议他们上网找一些“数据恢复神器”。他们后来试了好几个“神器”，还请了电脑维修人员，耗了很多精力，但数据也只是恢复了一点点，经此一劫，他们公司的客户资源几乎要重新开始收集了。</p>
			<p>第二家是我们的展览CRM用户，有个业务员一周前离职了，管理员突然发现这个业务员名下的所有客户联系人电话、手机号码都被恶意修改过，现在这些数据都成了无效的垃圾数据，就问我们是否可以恢复。</p>
			<p>这家公司现在是用我们的CRM系统，当然可恢复，如果也如第一家公司一样，是用EXCEL管理，那真的无法恢复了。</p>
			<p>事后，我看了我们对这家公司的服务记录，这个业务员曾经咨询过我们：他名下的客户数据为什么无法导出。我们说这个权限是由您公司管理层控制的。我想如果是用EXCEL管理，他一定会带走所有客户数据。</p>
			<p>所以，为了确保参展商采购商名单安全，应该找一款靠谱的展览管理软件，而不是启用EXCEL来管理，EXCEL虽然可以杜绝被不法软件商盗卖的风险，但却无法抵挡被损坏的天灾与人祸。</p>
			<p>也许有人说：为了抵挡天灾，我的EXCEL可以多多备份。那下周我们来聊聊EXCEL多份备份带来的管理难题。</p>
			<p>多谢您的关注，如回顾上周微刊，请关注微信公众号“展之科技”。</p>
			<div class="news-details-time">
				<span>发布时间:<i>2019.07.15 </i></span>
				<span>阅读量：<i>581</i></span>
				<span>发布人：<i>展之采编</i></span>
			</div>
		</div>
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="../../product.html">最新产品</a></li>
				<li><a href="../../manage-platform.html">展之方案</a></li>
				<li><a href="../../successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
