<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>新闻详情</title>
		<link rel="stylesheet" type="text/css" href="../../css/allstyle.css" />
		<script type="text/javascript" src="../../js/rem.js"></script>
		<script type="text/javascript" src="../../js/jquery-2.1.1.min.js"></script>
		<script src="../../js/mobile.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
	<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="../../index.html">
				<img src="../../img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>
			
		</div>
			<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="../../img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="../../index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="../../solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
									<div class="d-firstNav s-firstNav clearfix ">
										<span>产品中心</span>
										<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
									</div>
									<ul class="d-firstDrop s-firstDrop">
										<li class="tiao">
											<a href="javascript:void(0)">
												展之云
											</a>
										</li>
										<li class="tiao">
											<a href="javascript:void(0)" >
												展之通
											</a>
										</li>
										<li class="tiao">
											<a href="javascript:void(0)">
												展之客
											</a>
										</li>
									</ul>
								</li>
				<li class="first">
					<a href="../../successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="../../micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="../../contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>
		
		
	<script type="text/javascript">
				$(function() {
					$('.tiao').each(function(i) {
						$('.tiao').eq(i).on("click", function() {
							console.log("1111")
							$(location).attr('href', '../../product.html?index=' + i)
						})
					})
				})
	</script>
	<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists contacts" style="padding-left: 0 ;">
			<i></i>
			<ul>
				<li>
					<a href="javascript:void(0)">
						联系我们
						<em></em>
					</a>
				</li>
				<li>
					<a href="javascript:void(0)">
						关于我们
						<em></em>
					</a>
				</li>
				<li class="contact-active">
					<a href="javascript:void(0)">
						展之动态
						<em class="contact-em-active"></em>
					</a>
				</li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.contact-lists ul li').each(function(i) {
					$('.contact-lists ul li').eq(i).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + i)
					})
				})
			});
			$(function() {
					$('.contact-lists i').eq(0).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
			})
			
		</script>
		<!-- 新闻详情 -->
		<div class="news-details">
			<h4>展之云移动CRM升级，增加微信推送提醒、地图导航、收藏夹等功能</h4>
			<p>展之云移动CRM最新版本升级上线，新版本增加了微信推送提醒、地图导航、收藏夹等功能，功能更加实用</p>
			<img src="../../img/news/company-news/20170927225221922192.png">
			<p>利用展之云移动CRM，可以使工作更加轻松自如，实现如下场景应用：</p>
			<p>1、匆匆忙忙要出差，再也不用抄写或打印待拜访客户联系信息了，手机上随时可以看；</p>
			<p>2、在机场候机或坐在高铁上，使用手机可以先看看待拜访客户信息和历史联系情况；</p>
			<p>3、使用手机调出客户公司地址，在后面点下按钮，直接调出地图定位并导航；</p>
			<p>4、重要事务直接提醒到手机微信上，即使不在公司里的电脑前也不会错过重要事务；</p>
			<p>5、随时用手机拍照识别客户名片并录入拜访情况，不用等回公司再回忆了。</p>
			<p>以上这些仅是展之云移动CRM的一部分，更多功能可以进入<a href="../../product.html">展之云移动CRM产品介绍 </a>或关注“展之科技”微信公众号了解详情</p>
			<div class="news-details-time">
				<span>发布时间:<i>2017.09.27 </i></span>
				<span>阅读量：<i>962</i></span>
				<span>发布人：<i>展之采编</i></span>
			</div>
		</div>
		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="../../product.html">最新产品</a></li>
				<li><a href="../../manage-platform.html">展之方案</a></li>
				<li><a href="../../successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
					$('.index-list ul li').eq(3).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 2)
					})
					$('.index-list ul li').eq(4).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 1)
					})
					$('.index-list ul li').eq(5).on("click", function() {
						$(location).attr('href', '../../contact.html?index=' + 0)
					})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
