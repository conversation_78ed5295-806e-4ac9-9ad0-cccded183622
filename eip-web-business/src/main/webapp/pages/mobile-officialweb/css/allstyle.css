@charset "UTF-8";

* {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-tap-highlight-color: transparent;
	-moz-tap-highlight-color: rgba(0, 0, 0, 0);
	-moz-tap-highlight-color: transparent;
	-o-tap-highlight-color: rgba(0, 0, 0, 0);
	-o-tap-highlight-color: transparent;
	-ms-tap-highlight-color: rgba(0, 0, 0, 0);
	-ms-tap-highlight-color: transparent;
	margin: 0;
	padding: 0;
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

img {
	max-width: 100%;
}

ul,
li {
	list-style: none;
}

a {
	text-decoration: none;
	color: #333;
}

i {
	font-style: normal;
}



body {
	background: #f1f4fb;
	width: 100%;
	height: 100%;
	overflow: hidden;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

input {
	border: none;
	outline: none;
}

.oh {
	position: fixed;
}

/* 头部 */
.index-logs {
	width: 100%;
	box-sizing: border-box;
	padding-left: .24rem;
	background-color: #252b3a;
	border-bottom: .01rem solid rgba(255, 255, 255, .1);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999;
	overflow: hidden;
}

.index-log {
	box-sizing: border-box;
	padding: .22rem 0;
	float: left;
}

.index-nav {
	overflow: hidden;
	float: right;
}

.index-nav i {
	width: .88rem;
	height: .88rem;
	float: left;
	background: url(../img/index/me.png) no-repeat center center;
	background-size: .44rem;
}

#index-nav1 {
	display: block;
	background: url(../img/index/menu.png) no-repeat center center;
	width: .88rem;
	height: .88rem;
	float: left;
	background-size: .44rem;
}

.index-nav1-show {
	background: url(../img/index/close.png) no-repeat center center !important;
	background-size: .36rem !important;
}

.index-log img {
	display: block;
	width: 2.05rem;
	height: .45rem;
	overflow: hidden;
}

/* 顶部导航栏 */
.fl {
	float: left;
}

.fr {
	float: right;
	line-height: .9rem;
	font-size: .27rem;
	color: #a8aab0;
}

/*清除浮动*/
.clearfix:before,
.clearfix:after {
	content: " ";
	display: table;
}

.clearfix:after {
	clear: both;
}

.clearfix {
	*zoom: 1;
	border-bottom: .01rem solid rgba(255, 255, 255, .2);
	/*IE/7/6*/
}

/*定位居中*/
.middle {
	position: absolute;
	left: 50%;
	top: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.index-ee {
	position: relative;
}

.s-side {
	width: 100%;
	position: relative;
	box-sizing: border-box;
	top: .9rem;
	left: 0;
	bottom: 0;
	margin-bottom: 100%;
	background-color: #2a3244;
	box-shadow: 0rem 0rem .1rem #313131;
	z-index: 999;
	margin-left: 103%;
	transition: margin-left .6s;
	display: none;
}

.s-side-show {
	margin-left: 0%;
	display: block;
	overflow: scroll;
	height: 91.89%;
	position: fixed;
	-webkit-overflow-scrolling: touch;
}

.d-firstNav,
.font-sz {
	padding: 0rem .23rem;
	overflow: hidden;
}

.s-side a {
	color: #dfdfe1;
	display: block;
}

.s-side .s-firstItem i {
	font-size: .2rem;
}

.s-side .s-firstItem span {
	display: inline-block;
	margin-left: .1rem;
	font-size: .2rem;
	text-shadow: 0rem 0rem .05rem #EEEEEE;
	white-space: .1rem;
}

.s-side .first {
	line-height: .9rem;
	border-bottom: .01rem solid rgba(255, 255, 255, 0.2);
}

.s-side .d-firstNav span {
	display: inline-block;
	font-size: .32rem;
	color: #dfe0e2;
	float: left;
}

.font-sz {
	font-size: .32rem;
	color: #dfe0e2;
}

.s-side .d-firstNav i.fr {
	font-size: .27rem;
	color: #a8aab0;
}

.s-side .s-secondNav,
.s-side .s-secondItem {
	line-height: .9rem;
	border-bottom: .01rem solid rgba(255, 255, 255, .1);
	font-size: .32rem;
	color: #dfdfe1;
	padding-left: .9rem;
	padding-right: .23rem;
}

.s-secondNav:hover,
.d-firstNav:hover {
	background-color: #31394d;
	cursor: pointer;

}

.iconRotate {
	transform: rotate(180deg);
	transition: transform 0.5s;
}

.s-firstDrop,
.s-secondDrop {
	display: none;
}

.s-thirdItem {
	border-bottom: .01rem solid rgba(255, 255, 255, .1);
	line-height: .9rem;
	padding-left: 1.25rem;
}

.s-thirdItem a {
	font-size: .32rem;
	color: #dfe0e2;
}

.fa-caret-right img {
	max-width: .28rem;
}

/* 首页 */
.index-product {
	box-sizing: border-box;
	padding: 0 .24rem;
	background-color: #FFFFFF;
	height: 100%;
	padding-bottom: .6rem;
}

.index-product-top {
	box-sizing: border-box;
	font-size: .32rem;
	font-weight: bold;
	color: #666666;
	padding: .5rem 0 .4rem 0;
}

.index-product-top p {
	text-indent: .16rem;
	border-left: .04rem solid #00a4ff;
}

.index-product-top p i {
	font-size: .24rem;
	font-weight: normal;
}

.index-product-botton a {
	display: block;
	width: 100%;
	height: 1.86rem;
	box-sizing: border-box;
	background-color: #f5f5f5;
	border-radius: .2rem;
	overflow: hidden;
	margin-bottom: .24rem;
}

.product-botton-bgc {
	background-color: #e5e5e5 !important;
}

.index-product-img {
	float: left;
	width: 1.66rem;
	height: 100%;
	text-align: center;
	box-sizing: border-box;
}

.index-product-img img {
	margin-top: .4rem;
	max-width: 1rem;
	max-height: 1.04rem;
}

.index-product-text {
	float: left;
	position: relative;
	width: 75%;
}

.index-product-text h4 {
	font-size: .36rem;
	font-weight: bold;
	color: #666666;
	margin-top: .4rem;
}

.index-product-text p {
	font-size: .3rem;
	color: #666666;
	width: 98%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-top: .04rem;

}

.index-product-text i {
	font-size: .26rem;
	margin-top: .1rem;
	margin-left: 1.72rem;
	height: 1rem;
	position: absolute;
	top: 0.43rem;
	line-height: 1rem;
	border-left: .01rem solid #ccc;
	display: inline-block;
	padding-left: .4rem;
}

.index-product-text em {
	position: absolute;
	top: 0;
	left: 0;
}

.index-solution {
	box-sizing: border-box;
	padding: 0 .24rem;
	height: 100%;
	overflow: hidden;
	padding-bottom: .4rem;
	background-color: #f5f8fd;
}

.index-solution-list ul li {
	width: 33.3%;
	height: 2.66rem;
	box-sizing: border-box;
	float: left;
}

.solution-list-show {
	background-color: #ebeef2;
}

.index-solution-list ul li a {
	display: block;
	width: 100%;
	height: 100%;
	font-size: .28rem;
	color: #333333;
	text-align: center;
	box-sizing: border-box;
	padding: 0 .24rem;
	overflow: hidden;

}

.index-solution-img {
	max-height: 1.21rem;
	max-width: 1.21rem;
	margin: .29rem auto 0.1rem;
}

.index-solution-img img {
	display: block;
	width: 100%;
	height: 100%;
}

.index-news {
	background-color: #FFFFFF;
	box-sizing: border-box;
	padding: .9rem .24rem;
	padding-top: .8rem;
}

.index-news-new {
	box-sizing: border-box;
	border: .02rem solid #ccc;
	margin-bottom: .24rem;
}

.index-news-newtop {
	height: 1.08rem;
	background-color: #f2f4f7;
	line-height: 1.08rem;
	overflow: hidden;
	padding: 0 .32rem;
	border-bottom: .01rem solid #ccc;
}

.index-news-newtop h4 {
	font-size: .34rem;
	font-weight: bold;
	color: #4c4c4c;
	float: left;
}

.index-news-newtop a {
	font-weight: bold;
	color: #4c4c4c;
	float: right;
	font-size: .28rem;
}

.news-color {
	color: #00a4ff !important;
}

.index-news-newlist {
	box-sizing: border-box;
	padding: 0 .32rem;
	background-color: #fff;
}

.index-news-newlist ul li {
	border-bottom: .01rem dashed #ccc;
	height: .89rem;
	line-height: .88rem;

}

.index-news-newlist ul li:last-of-type {
	border-bottom: none;

}

.index-news-newlist ul li a {
	font-size: .3rem;
	color: #4c4c4c;

}

.a-color p i {
	display: inline-block;
	width: 88%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.index-em {
	display: inline-block;
	background: url(../img/index/enter1.png) no-repeat center center;
	float: right;
	width: .24rem;
	height: .88rem;
	background-size: .14rem;

}

.index-em-on {
	background: url(../img/index/enter2.png) no-repeat center center !important;
	display: inline-block !important;
	float: right !important;
	width: .24rem !important;
	height: .88rem !important;
	background-size: .14rem !important;
}

/* .index-em img{
	  max-width: .14rem;
    max-height: .26rem;
	
} */
/* 底部 */

.index-list {
	width: 100%;
}

.index-list ul li {
	width: 100%;
	background-color: #666;
	line-height: .9rem;
	box-sizing: border-box;
	padding-left: .24rem;
	border-bottom: .01rem solid #858585;
}

.index-list ul li:last-child {
	border-bottom: none;
}

.index-list ul li a {
	width: 100%;
	font-size: .32rem;
	color: #c1c1c1;
	height: 100%;
	line-height: .9rem;
	display: block;
}

.index-foot {
	position: relative;
	width: 100%;
	background-color: #4d4d4d;
	font-size: .2rem;
	color: #7c7c7c;
	box-sizing: border-box;
	text-align: center;
}

.index-foot p:nth-of-type(1) {
	padding-top: .3rem;
	line-height: .31rem;
}

.index-foot p:nth-of-type(2) {
	text-indent: .2rem;
	padding-bottom: .28rem;

}

.index-foot img {
	position: absolute;
	top: .66rem;
	left: 2.15rem;
	width: .18rem;
}

.index-lists-active {
	background-color: #5c5c5c !important;
}






/* 联系我们 */
.contact-lists {
	background-color: #252b3a;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
	padding: 0 .7rem;
	position: fixed;
	top: .9rem;
	left: 0;
	z-index: 99;
}

.contact-lists i {
	display: block;
	width: .7rem;
	height: .88rem;
	float: left;
	background: url(../img/connect/back.png) no-repeat center center;
	background-size: .2rem;
}


.contact-lists ul li {
	color: #7c8089;
	font-size: .36rem;
	font-weight: bold;
	position: relative;
	float: left;
	line-height: .88rem;
	margin: 0 .29rem;

}

.contact-lists ul li em {
	display: none;
	width: .96rem;
	height: .04rem;
	background-color: #00a4ff;
	border-radius: .02rem;
	position: absolute;
	bottom: .09rem;
	left: 50%;
	margin-left: -33%;
}

.contact-list1 {
	margin-top: 1.78rem;
}

.contact-baner {
	width: 100%;
	height: 2.8rem;
	overflow: hidden;
	position: relative;
}
.contact-baner h5{
	font-size: .4rem;
	color: #fefefe;
	position: absolute;
	top: .8rem;
	left: .92rem;
}
.contact-baner em{
	display: block;
	width: 2.88rem;
	height: .04rem;
	background-color: #ffffff;
	position: absolute;
	top:  1.43rem;
	left: .92rem;
}
.contact-baner p{
	font-size: .28rem;
	color: #fefefe;
	position: absolute;
	top: 1.55rem;
	left: .92rem;
}


.contact-baner img {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.contact-cnt {
	box-sizing: border-box;
	margin-top: .44rem;
}

.contact-cnt-title {
	box-sizing: border-box;
	padding-left: .24rem;
	overflow: hidden;
}

.contact-cnt-title em {
	display: inline-block;
	width: .04rem;
	height: .3rem;
	background-color: #00a4ff;
	margin-right: .16rem;
	float: left;
	margin-top: 0.08rem;
}

.contact-cnt-title h4 {
	display: inline-block;
	font-size: .32rem;
	font-weight: bold;
	color: #666666;
	float: left;
}

.contact-cnt1 {
	padding: .4rem 0.4rem 0 .44rem;
	width: 100%;
	overflow: hidden;
	box-sizing: border-box;
}

.contact-cnt1-top {
	width: 1.6rem;
	height: 1.6rem;
	border-radius: 50%;
	float: left;
}

.contact-cnt1-top img {
	display: block;
	vertical-align: middle;
	width: 100%;
}

.contact-cnt1-border {
	border-bottom: .01rem solid #eaeaea;
	overflow: hidden;
	padding-bottom: .4rem;
}

.contact-cnt1-botton {
	box-sizing: border-box;
	padding-left: .4rem;
	float: left;
}

/* .contact-cnt1-botton h4 {
	font-size: .36rem;
	font-weight: bold;
	color: #666666;
	
} */

.contact-cnt1-botton h5 {
	font-size: .32rem;
	font-weight: bold;
	color: #666666;
	margin-bottom: .10rem;
}

.contact-cnt1-botton p {
	font-size: .32rem;
	color: #808080;
}

.contact-cnt1-botton p em {
	display: inline-block;
	width: .28rem;
	height: .28rem;
	background: url(../img/connect/contact5.png) no-repeat center center;
	background-size: .28rem;
}

.contact-wei {
	width: 50%;
	height: 100%;
}

.contact-wei-img {
	width: 50%;
	height: 100%;
}

.contact-wei-img img {
	width: 3.42rem;
	height: 3.42rem;
	display: block;
}

.contact-wei h6,
.contact-wei p {
	font-size: .32rem;
	font-weight: bold;
	color: #808080;
	margin: .35rem 0;

}

/* .contact-wei p{
		font-size: .32rem;
	font-weight: bold;
	color: #808080;
	
} */
.contact-time {
	position: relative;
	font-size: .34rem;
	color: #666666;
	box-sizing: border-box;
	margin-top: .44rem;
	padding: 0 .44rem;
}

.contact-time h4 {
	text-align: center;
	font-size: .32rem;
	color: #666666;
	margin-bottom: .39rem;
	font-weight: normal;
}

.contact-time p {
	position: absolute;
	top: .8rem;
	left: .44rem;
	width: 1.2rem;
	height: 1.2rem;
	border: solid .03rem #999999;
	border-radius: 50%;
	text-align: center;
	line-height: 1.2rem;
}

.contact-time span {
	position: absolute;
	top: 1.4rem;
	left: 1.67rem;
	width: .63rem;
	height: .03rem;
	background-color: #999999;
}

.contact-time p:nth-of-type(2) {
	left: 2.27rem;
}

.contact-time p:nth-of-type(3) {
	left: 3.96rem;
}

.contact-time p:nth-of-type(4) {
	left: 5.8rem;
}

.contact-time span:nth-of-type(2) {
	left: 5.2rem;
}

.contact-time-text {
	width: 100%;
	margin-top: 2rem;
	margin-bottom: 1.5rem;
}

.contact-time-text p {
	position: relative;
	top: 0rem;
	left: 0rem;
	width: 100%;
	height: 0rem;
	border: none;
	border-radius: 0;
	text-align: center;
	line-height: .44rem;
	font-size: .28rem;
}

.contact-cnt-title2 {
	padding-top: .5rem;
	margin-bottom: .15rem;
}

.contact-list2-text {
	box-sizing: border-box;
	padding: 0 .24rem;
}

.contact-list2-text h4 {
	font-size: .28rem;
	color: #00a4ff;
	margin: .38rem 0 .22rem 0;
}

.contact-list2-content {
	font-size: .26em;
	color: #666666;
	text-indent: .54rem;
}

.contact-list2-content p {
	line-height: .36rem;
}

.contact-list2 {
	margin-bottom: 1.42rem;
}

.contact-active {
	color: #078cd8 !important;
}

.contact-em-active {
	display: block !important;
}

.contact-cnt-title3 h5 {
	font-size: .28rem;
	font-weight: bold;
	color: #666666;
	margin-left: .38rem;
	display: inline-block;
	float: left;
}

.contact-lists1 {
	box-sizing: border-box;
	padding: 0 .24rem;
}

.contact-lists1 ul li {
	box-sizing: border-box;
	border-bottom: .01rem solid #ccc;
	width: 100%;
	height: 1.62rem;
	overflow: hidden;
}

.contact-lists1 ul li a {
	display: block;
	overflow: hidden;
}

.contact-lists1-img {
	float: right;
	width: 1.52rem;
	height: 1.14rem;
	margin: .24rem 0rem .24rem 0;
}

.contact-lists1-img img {
	width: 100%;
	height: 100%;
}

.contact-lists1-text {
	float: left;
	width: 100%;
}

.contact-text-width {
	float: left;
	width: 75% !important;
}

.contact-lists1-text h4 {
	font-size: .3rem;
	color: #333333;
	line-height: .68rem;
	height: .68rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.contact-lists1-text p {
	font-size: .28rem;
	color: #666666;
	height: .77rem;
	overflow: hidden;
	margin-bottom: .22rem;
}

.contact-lists1-end {
	font-size: .28rem;
	color: #999999;
	text-align: center;
	margin: .6rem 0 1.5rem 0;
}

.contact-lists-active {
	font-size: .32rem !important;
	color: #00a4ff !important;
}

/* 新闻详情 */
.contact-lists a {
	color: #7c8089;
	font-size: .36rem;
}

.news-details {
	box-sizing: border-box;
	margin-top: 2.06rem;
	padding: 0 .24rem;
}

.news-details h4 {
	font-size: .3rem;
	color: #333333;
	line-height: .52rem;
}

.news-details p {
	font-size: .28rem;
	line-height: .4rem;
	color: #666666;
	text-indent: .62rem;
	margin-bottom: .2rem;
	overflow: hidden;
}

.news-details-time {
	font-size: .24rem;
	color: #999999;
	width: 100%;
	margin: .6rem 0 1.5rem 0;
}

.news-details-time span {
	width: 33%;
	overflow: hidden;
	margin-left: .15rem;
}

.tiao {
	line-height: .9rem;
	border-bottom: .01rem solid rgba(255, 255, 255, .1);
	font-size: .32rem;
	color: #dfdfe1;
	padding-left: .9rem;
	padding-right: .23rem;
}

/* 会员中心-信息修改 */
.inf-content {
	margin-top: 1.8rem;
	box-sizing: border-box;
	width: 100%;
	padding: 0 .44rem;
}

.inf-content1 {
	box-sizing: border-box;
	padding-top: .46rem;
}

.inf-content1 p {
	font-size: .32rem;
	color: #999999;
	border-bottom: .02rem solid #ccc;
	line-height: .78rem;
}

.inf-content1 p input {
	width: 3.5rem;
	height: .6rem;
	margin-left: .84rem;
	font-size: .32rem;
	color: #666666;
	background-color: rgb(241, 244, 251, 0);
}

.inf-content1 p:nth-of-type(2) input {
	margin-left: 1.5rem;
}

#radio1,
#radio2 {
	font-size: .32rem;
	color: #666;
	padding: .25rem 0 .2rem 0;
	border-bottom: .02rem solid #ccc;
}

#radio1 label:nth-of-type(1),
#radio2 label:nth-of-type(1) {
	font-size: .32rem;
	color: #999;
}

#radio1 input {
	margin-left: 1.4rem;
}

#radio2 input {
	margin-left: .75rem;
}

#radio2 input,
#radio1 input {
	width: .26rem;
	height: .26rem;
}

#apply_qr,
#windows_qr {
	width: 6.18rem;
	height: 1rem;
	background-color: #00a4ff;
	border-radius: .5rem;
	font-size: .34rem;
	color: #ffffff;
	margin-top: 4.2rem;
}

/* 修改手机 */
.inf-content2 p input {
	width: 2.7rem;
	height: .6rem;
	margin-left: .1rem;
	font-size: .32rem;
	color: #666666;
	background-color: rgb(241, 244, 251, 0);

}

.inf-content2 p:nth-of-type(2) input {
	margin-left: .1rem;
}

.inf-content2 p:nth-of-type(2) input:nth-of-type(2) {
	width: 1.5rem;
	font-size: 0.3rem;
	color: #999999;
	height: .63rem;
	margin-left: -0.1rem;
	margin-top: -0.1rem;
}

.inf-content2 p:nth-of-type(3) input {
	margin-left: 1.4rem;
	width: 4.2rem;
}

.inf-content2 p:nth-of-type(4) input {
	margin-left: 1.4rem;
	width: 4.2rem;
}

.windows2_select {
	height: .78rem;
	font-size: .32rem;
	color: #999999;
	line-height: .78rem;
	border-bottom: .02rem solid #ccc;
}

.windows2_select select {
	height: .78rem;
	display: inline-block;
	margin-left: 1.6rem;
	font-size: .32rem;
	color: #666666;
	width: 2rem;
	overflow: hidden;
	border: none;
	background-color: rgb(241, 244, 251, 0);
	outline: none;
}

.windows2_select select:nth-of-type(2) {
	height: .78rem;
	display: inline-block;
	margin-left: 0.4rem;
	font-size: .34rem;
	color: #666666;
	width: 1.7rem;
	overflow: hidden;
}

/* 产品中心 */
/* .product-tit {
	padding-left: .24rem;
	background-color: #fff;
	padding-bottom: 0;
} */
.product-title-lists-menu {
	width: 100%;
	height: .88rem;
	background-color: #f0f3f7;
	overflow: hidden;
}

.product-title-lists-menu a {
	height: .88rem;
	box-sizing: border-box;
	display: block;
	float: left;
	text-align: center;
	line-height: .88rem;
	font-size: .28rem;
	color: #666;
	padding: 0 .132rem;
}

.product-title-a {
	background-color: #e6e9ed !important;
}

.product-title-lists-menu2 a {
	padding: 0px .306rem;
}

.product-title-lists-menu3 a {
	padding: 0 .306rem;
}

.product-title-lists {
	width: 100%;
	overflow: hidden;
}

.product-content1-list1 h4 {
	font-size: .36rem;
	color: #666666;
	padding: .3rem 0 .3rem;
}

.product-title-lists ul li {
	width: 33.3%;
	/* background-color: #eaeff1; */
	background-color: #fff;
	height: 1rem;
	float: left;
	text-align: center;
	line-height: 1rem;
	font-size: .32rem;
	color: #4f5050;
	font-weight: bold;
}

.product-title-show {
	background-color: #f0f3f7 !important;
}

.product-title-lists ul li em {
	box-sizing: border-box;
	display: inline-block;
	margin-right: .14rem;
}

.product-title-lists ul li em img {
	width: .38rem;
	height: .38rem;
	overflow: hidden;
	vertical-align: middle;
}

.product-content1 {
	box-sizing: border-box;
	background-color: #fff;
}

.product-lists-img,
.product-lists-text {
	padding: 0 .24rem;
}

.product-content1-list1 {
	width: 100%;
	height: 100%;
	padding: 0 .24rem;
	box-sizing: border-box;
	/* padding-top: .9rem; */
}

.product-lists-img img {
	vertical-align: middle;
	display: block;
	margin: 0 auto;
}

.product-lists-text ul li {
	padding-right: 0;
}

.product-lists-text h4 {
	font-size: .36rem;
	color: #666666;
	margin: .3rem 0 .02rem;
}

.product-lists-text ul li h6 {
	font-size: .28rem;
	color: #128fff;
	text-indent: .23rem;
	background: url(../img/product/title.png) no-repeat 0 .15rem;
	background-size: .13rem;
	margin-bottom: .1rem;
	margin-top: .18rem;
}

.product-lists-text ul li p {
	font-size: .26rem;
	color: #333333;
	padding-left: .24rem;
}

.product-lists-text a {
	display: block;
	width: 2.4rem;
	height: .7rem;
	border-radius: .35rem;
	border: solid .02rem #41a5ff;
	font-size: .29rem;
	color: #128fff;
	text-align: center;
	line-height: .7rem;
	margin: .38rem .24rem .9rem 0;
	margin-top: .38rem;
	margin-left: .24rem;
}

.product-lists-a {
	background: linear-gradient(to left,
		#1460de 0%,
		#00a4ff 100%) !important;
	color: #fff !important;
}

/* 产品中心-申请 */
.apply-sq {
	background-color: #fff;
	padding: .46rem .48rem 0;
}

.allsucceed {
	position: fixed;
	display: none;
	top: 25%;
	left: 50%;
	margin-left: -25%;
	z-index: 99;
	width: 4rem;
	height: 4rem;
	background-color: #ffffff;
	box-shadow: 0rem 0rem .5rem 0rem rgba(0, 0, 0, 0.1);
	border-radius: .1rem;
	box-sizing: border-box;
	padding-top: .7rem;
	transition: display .6s;
}

.allsucceed-img {
	width: 1.15rem;
	height: 1.15rem;
	margin: 0rem auto .2rem;
}

.allsucceed-img img {
	display: block;
	width: 1.15rem;
	height: 1.15rem;
	vertical-align: middle;
}

.allsucceed p {
	text-align: center;
	font-size: .32rem;
	color: #666666;
	line-height: .42rem;
}

.allsucceed-show {
	display: block;
}

/* 成功案例 */
.successful-imgs {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.successful-imgs ul li {
	width: 3.4rem;
	height: 1.32rem;
	float: left;
	box-sizing: border-box;
	border: .01rem solid #eaeaea;
	margin: 0 .22rem .22rem 0;
}

.successful-imgs ul li:nth-of-type(2n) {
	margin-right: 0;
}

/* 解决方案总页面 */
.solution-list {
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding-bottom: 2rem;
}

.solution-list ul li {
	width: 100%;
	height: 1.78rem;
	box-sizing: border-box;
	border-top: .01rem solid #eaeaea;
	padding-right: .24rem;
	padding-left: .24rem;
}

.solution-list ul li:hover {
	background-color: #f5f5f5;
}

.solution-list ul li:last-child {
	border-bottom: .01rem solid #eaeaea;

}

.solution-list ul li a {
	display: block;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.solution-list-img {
	float: left;
	width: 1.3rem;
	height: 1.3rem;
	border-radius: .12rem;
	margin-top: 3.4%;
}

.solution-list-text {
	width: 77%;
	float: left;
	margin-left: .26rem;
}

.solution-list-text h2 {
	font-size: .32rem;
	line-height: .48rem;
	color: #4d4d4d;
	margin-top: .18rem;
	margin-bottom: .06rem;
}

.solution-list-text p {
	font-size: .28rem;
	line-height: .42rem;
	color: #666666;
	height: .8rem;
	overflow: hidden;
}

/* 解决方案-六个小方案*/
.Project-bgc {
	background-color: #fff;
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding: 0 .24rem;
	padding-bottom: .6rem;
}

.Project-bgc h2,
.myself-box4 h2 {
	font-size: .3rem;
	color: #00a4ff;
	margin-bottom: 15px;
}

.Project-bgc p {
	font-size: .28rem;
	line-height: .48rem;
	color: #333333;
	text-indent: 2em;
}

.new-project {
	background-color: #f5f8fd !important;
}

.new-project h2 {
	padding-top: .62rem;
}

.new-project-img {
	margin-top: .8rem;
	margin-bottom: .3rem;
}

.myself-box3 h2 {
	padding: .7rem 0 .66rem;
}

.myself-box3-img {
	margin-bottom: 0.6rem;
}

.myself-box4 {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding: 0 .24rem;
	background-color: #f5f8fd;
}

.myself-box4 h2 {
	padding: .7rem 0 0;
}

.myself-box4 p {
	color: #4d4d4d;
	font-size: .28rem;
}

.myself-box4-img,
.myself-box5-img,
.myself-box6-img,
.myself-box7-img {
	margin: .68rem 0 .72rem;
}

.myself-box5,
.myself-box7 {
	background-color: #fff;
}

/* 代理展 */

.agency-list {
	width: 100%;
	height: 100%;
}

.agency-list ul li {
	width: 100%;
	height: 1.46rem;
	border: solid .01rem #cccccc;
	overflow: hidden;
	margin-bottom: .2rem;
}

.agency-list-left {
	float: left;
	width: 1.26rem;
	height: 1.26rem;
	margin: .1rem;
}

.agency-list-right {
	float: left;
}

.agency-list-right p {
	display: block;
	font-size: .26rem;
	line-height: .46rem;
	color: #333333;
	margin-left: -.3rem;
}

.agency-list-right p:first-child {
	margin-top: .32rem
}

.myself-box5 h6 {
	font-size: .3rem;
	color: #4d4d4d;
	margin: .12rem;
}

.vis-box5-img {
	float: left;
	width: 3.4rem;
	height: 4.22rem;
	margin-right: .2rem;
	margin-bottom: .2rem;
}

.vis-box5-img:nth-of-type(2n) {
	margin-right: 0rem;
}
/* 登陆后的状态 */
.index-me{
	float: right;
}
.index-me em{
	display: block;
    background: url(../img/index/me.png) no-repeat center center;
    width: .88rem;
    height: .88rem;
    float: left;
    background-size: .44rem;
}
.index-succeed{
	width: 100%;
    position: fixed;
	height: 62%;
    box-sizing: border-box;
    top: .9rem;
    left: 0;
    bottom: 0;
    background-color: #2a3244;
    box-shadow: 0rem 0rem 0.1rem #313131;
    z-index: 999;
   margin-left: 103%;
    transition: margin-left .6s;
    display: none;
}
.index-succeed ul li{
	width: 100%;
	box-sizing: border-box;
	padding-left: .95rem;
	 line-height: .9rem;
    border-bottom: .01rem solid rgba(255, 255, 255, 0.2);
}
.index-succeed ul li a{
	color: #dfdfe1;
    display: block;
	font-size: .32rem;
}
.index-succeed ul li:first-child{
	 background: url(../img/index/me.png) no-repeat .23rem center;
	  background-size: .44rem;
}
.index-succeed ul li:hover{
	background-color: #252b3a !important;
}
.index-succeed-show{
	margin-left: 0%;
	display: block;
}