// 顶部导航栏
$(function() {
	$('.d-firstNav').click(function(e) {
		console.log("111");
		dropSwift($(this), '.d-firstDrop');
		e.stopPropagation();
	});
	$('.d-secondNav').click(function(e) {
		dropSwift($(this), '.d-secondDrop');
		e.stopPropagation();
	});



	/**
	 * @param dom   点击的当前元素
	 * @param drop  下一级菜单
	 */
	function dropSwift(dom, drop) {
		//点击当前元素，收起或者伸展下一级菜单


		dom.next().slideToggle();

		//设置旋转效果

		//1.将所有的元素都至为初始的状态		
		dom.parent().siblings().find('.fa-caret-right').removeClass('iconRotate');

		//2.点击该层，将其他显示的下滑层隐藏		
		dom.parent().siblings().find(drop).slideUp();

		var iconChevron = dom.find('.fa-caret-right');
		if (iconChevron.hasClass('iconRotate')) {
			iconChevron.removeClass('iconRotate');
		} else {
			iconChevron.addClass('iconRotate');
		}
	}
})
$(function() {
	$("#index-nav1").on("click", function() {
		$(this).toggleClass("index-nav1-show");
		$(".s-side").toggleClass("s-side-show");
		$(".index-aa").toggleClass("oh");
		$("body").toggleClass("oh")
	});
})
// 首页点击效果

$(function() {
	$(".index-solution-list ul li").on("click", function() {
		$(this).toggleClass("solution-list-show");
	});
	$(".news-newtop-gd ").on("click", function() {
		$(".news-newtop-gd").toggleClass("news-color");
	});
	$(".news-newtop-gd2 ").on("click", function() {
		$(".news-newtop-gd2").toggleClass("news-color");
	});
	$(".a-color").each(function(i) {
		$(".a-color").eq(i).on("click", function(i) {
			p = $(".a-color").index();
			$(this).find("em").toggleClass("index-em-on");
			$(this).toggleClass("news-color");
			
		})
	})
	$(".index-product-botton").children("a").on("click", function() {
		$(this).toggleClass("product-botton-bgc");
	});
	$(".index-list ul li").on("click", function() {
		$(this).toggleClass("index-lists-active");
	});
})

// 登陆效果
$(function(){
	$(".index-me").on("click",function(){
		$(".index-succeed").toggleClass("index-succeed-show")
		
	})
	// $("body").on("click",function(){
	// 	$(".index-succeed").removeClass("index-succeed-show")
	// })
})




// 产品中心tab
$(function() {
	$(".product-title-lists li").each(function(i) {
		$(".product-title-lists ul li").eq(i).on("click", function(i) {
			p = $(this).index();
			$(this).addClass("product-title-show").siblings().removeClass("product-title-show");
			$(".product-content1").eq(p).show().siblings().hide()
		
		})
		var i = 0;
		i = GetQueryString("index");
		$(".product-title-lists ul li").eq(i).addClass('product-title-show').siblings().removeClass('product-title-show');
		$(".product-content1").eq(i).show().siblings().hide()
		
		function GetQueryString(name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			var r = window.location.search.substr(1).match(reg);
			if (r != null) return unescape(r[2]);
			return null;
		}
		
	
	});
	

});
// 点击效果
$(function(){
	$(".product-lists-text a").each(function(i) {
		$(".product-lists-text a").eq(i).on("click", function(i) {
			$(this).toggleClass("product-lists-a");
		})
	})	
})
// 联系我们tab
$(function() {
	var i = 0;
	i = GetQueryString("index");
	$(".contact-lists ul li").eq(i).addClass('contact-active').siblings().removeClass('contact-active');
	$(".contact-lists ul li").eq(i).children("em").addClass("contact-em-active").parent().siblings().children("em").removeClass(
		"contact-em-active")
	$(".contact-list1").eq(i).show().siblings().hide()

	function GetQueryString(name) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if (r != null) return unescape(r[2]);
		return null;
	}
	$(".contact-lists ul li").each(function(i) {
		$(".contact-lists ul li").eq(i).on("click", function(i) {
			p = $(this).index();
			$(this).addClass("contact-active").siblings().removeClass("contact-active");
			$(this).children("em").addClass("contact-em-active").parent().siblings().children("em").removeClass(
				"contact-em-active")
			$(".contact-list1").eq(p).show().siblings().hide()
		})
	})

});
// 联系我们-新闻tab
$(function() {
	$(".contact-cnt-title3 h4").on("click", function() {
		$(".contact-cnt-title3 h4").addClass("contact-lists-active").siblings("h5").removeClass("contact-lists-active");
		$(".contact-cnt-title3 h4").css("font-size", ".32rem").siblings("h5").css("font-size", ".27rem");
		$(".lists1").show().siblings(".lists2").hide()
	})
	$(".contact-cnt-title3 h5").on("click", function() {
		$(".contact-cnt-title3 h5").addClass("contact-lists-active").siblings("h4").removeClass("contact-lists-active");
		$(".contact-cnt-title3 h5").css("font-size", ".32rem").siblings("h4").css("font-size", ".27rem");
		$(".lists2").show().siblings(".lists1").hide()
	})

});
// 联系我们tab
$(function() {
	$(".contact-lists ul li").each(function(i) {
		$(".contact-lists ul li").eq(i).on("click", function(i) {
			p = $(this).index();
			$(this).addClass("contact-active").siblings().removeClass("contact-active");
			$(".inf-content1").eq(p).show().siblings().hide()
		})
	})

});

//会员中心提交
$(function() {
	$("#apply_qr").on("click", function() {
		$(".allsucceed").toggleClass("allsucceed-show");
		$(".allsucceed").css("display", "block")

		setInterval(function hidd() {
			$(".allsucceed").css("display", "none")
		}, 1500)
	});
	window.clearInterval(setInterval(function hidd() {
		$(".allsucceed").css("display", "none")
	}, 1500))
})


$(function(){
	var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?e04d88685d617b02bae8c2d3188b88bc";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
})