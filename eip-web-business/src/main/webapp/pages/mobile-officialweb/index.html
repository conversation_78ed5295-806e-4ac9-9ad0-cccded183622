<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>杭州展之信息技术有限公司|展览软件|会展软件|展览CRM|展商管理|观众登记|客户关系管理|会展信息门户|呼叫中心|展之科技|新特软件</title>
		<meta name="keywords" content="展览软件,新特软件,会展软件,展览CRM,展会CRM,展会管理系统,客户管理软件,客户关系管理,呼叫中心,邮件群发,短信群发,网络传真,软电话,一键拨号,自动打电话,电话营销" />
		<meta name="description" content="杭州展之信息技术有限公司（原新特软件）,展览CRM,会展软件,展会销售管理软件,优秀CRM解决方案,几百家展览公司成功案例，10多年专注会展行业提供客户关系管理系统和呼叫中心平台。展之科技，助力企业发展!" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />
		<link rel="stylesheet" type="text/css" href="dist/swipeslider.css">
		<link rel="stylesheet" type="text/css" href="css/allstyle.css" />
		<script type="text/javascript" src="js/rem.js"></script>
		<script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
		<script type="text/javascript" src="js/mobile.js"></script>
		<script type="text/javascript" src="dist/swipeslider.min.js"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>

	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="index.html">
					<img src="img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>
			
			
			<div class="index-me">
				<em></em>
			</div>


		</div>
		
		
		<!-- 登陆后显示的菜单 -->
		<div class="index-succeed">
			<ul>
				<li><a href="">超级管理员，你好</a></li>
				<li><a href="">账号设置</a></li>
				<li><a href="">未读消息 <i>5</i></a></li>
				<li><a href="">我的产品</a></li>
				<li><a href="">退出</a></li>
			</ul>
		</div>
		
		
		
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 轮播 -->

		<div class="jq22-container">
			<article class="container">
				<section>

					<figure id="full_feature" class="swipslider">
						<ul class="sw-slides">
							<li class="sw-slide">
								<a href="agency-exhibition.html">
									<img src="img/index/mbanner1.jpg">
								</a>

							</li>
							<li class="sw-slide">
								<a href="product.html?index=0#floorplan">
									<img src="img/index/mbanner2.jpg">
								</a>

							</li>
							<li class="sw-slide">
								<a href="product.html?index=1">
									<img src="img/index/mbanner3.jpg">
								</a>

							</li>
							<li class="sw-slide">
								<a href="product.html?index=2">
									<img src="img/index/mbanner4.jpg">
								</a>

							</li>
						</ul>
					</figure>

				</section>
			</article>

		</div>
		<!-- <script type="text/javascript">
			$(function() {
				$('#full_feature a').each(function(i) {
					$(this).on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script> -->
		<script type="text/javascript">
			$(window).load(function() {
				$('#full_feature').swipeslider();
				$('#content_slider').swipeslider({
					transitionDuration: 600,
					autoPlayTimeout: 10000,
					sliderHeight: '300px'
				});
				$('#responsiveness').swipeslider();
				$('#customizability').swipeslider({
					transitionDuration: 1500,
					autoPlayTimeout: 4000,
					timingFunction: 'cubic-bezier(0.38, 0.96, 0.7, 0.07)',
					sliderHeight: '30%'
				});
			});
		</script>
		<!-- 产品中心 -->
		<div class="index-product">
			<div class="index-product-top">
				<p>产品中心<i>/ 融会需求，精心设计，应变自如</i></p>
			</div>
			<div class="index-product-botton">
				<a href="javascript:void(0);">
					<div class="index-product-img"><img src="img/index/logo1.png"></div>
					<div class="index-product-text">
						<h4>展之云</h4>
						<p>会展信息门户 <i>查看详情</i> </p>

						<em></em>
					</div>
				</a>
				<a href="javascript:void(0);">
					<div class="index-product-img"><img src="img/index/logo2.png"></div>
					<div class="index-product-text">
						<h4>展之通</h4>
						<p>协同交互工具 <i>查看详情</i> </p>

					</div>
				</a>
				<a href="javascript:void(0);">
					<div class="index-product-img"><img src="img/index/logo3.png"></div>
					<div class="index-product-text">
						<h4>展之客</h4>
						<p>企业管理平台 <i>查看详情</i> </p>

					</div>
				</a>
			</div>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.index-product-botton a').each(function(i) {
					$(this).on("click", function() {
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>
		<!-- 解决方案 -->
		<div class="index-solution">
			<div class="index-product-top" style="padding: .28rem 0 .4rem 0;">
				<p>解决方案<i>/ 针对行业，再造流程，提升效率</i></p>
			</div>
			<div class="index-solution-list">
				<ul>
					<li>
						<a href="myself-exhibition.html">
							<div class="index-solution-img">
								<img src="img/index/plan1.png">
							</div>
							<p>自办展项目应用方案</p>
						</a>
					</li>
					<li>
						<a href="agency-exhibition.html">
							<div class="index-solution-img">
								<img src="img/index/plan2.png">
							</div>
							<p>国外代理展项目应用方案</p>
						</a>
					</li>
					<li>
						<a href="vistorregistration.html">
							<div class="index-solution-img">
								<img src="img/index/plan3.png">
							</div>
							<p>观众登记管理方案</p>
						</a>
					</li>
					<li>
						<a href="remote-office.html">
							<div class="index-solution-img">
								<img src="img/index/plan4.png">
							</div>
							<p>异地、移动办公解决方案</p>
						</a>
					</li>
					<li>
						<a href="construct.html">
							<div class="index-solution-img">
								<img src="img/index/plan5.png">
							</div>
							<p>搭建设计项目应用方案</p>
						</a>
					</li>
					<li>
						<a href="manage-platform.html">
							<div class="index-solution-img">
								<img src="img/index/plan6.png">
							</div>
							<p>会员管理平台方案</p>
						</a>
					</li>
				</ul>
			</div>
		</div>
		<!-- 展之动态 -->
		<div class="index-news">
			<!-- <div class="index-product-top" style="padding: .28rem 0 .4rem 0;">
				<p>展之动态<i>/ 最新方案，最新软件，最新技术</i></p>
			</div> -->
			<div class="index-news-new">
				<div class="index-news-newtop">
					<h4>展之动态</h4>
					<a href="javascript:void(0)" class="news-newtop-gd2">查看更多</a>
				</div>
				<div class="index-news-newlist">
					<ul id="newsList">
						<!-- <li>
							<a href="product.html?index=0#floorplan" class="a-color">
								<p><i>动态展位图第一期上线。</i><em class="index-em"></em></p>
							</a>
						</li>
						<li>
							<a href="news/bulletin/2019429.html" class="a-color">
								<p><i>公司成为浙江省国际会议展览业协会理事单位。</i><em class="index-em"></em></p>
							</a>
						</li>
						<li>
							<a href="news/bulletin/2018816.html" class="a-color">
								<p><i>公司荣获杭州市高新技术企业称号。</i><em class="index-em"></em></p>
							</a>
						</li>
						<li>
							<a href="news/bulletin/201612.html" class="a-color">
								<p><i>公司开始研发动态展位图、会员中心等</i><em class="index-em"></em></p>
							</a>
						</li> -->
					</ul>
				</div>
			</div>
			<div class="index-news-new">
				<div class="index-news-newtop">
					<h4>展之微刊</h4>
					<a href="micro.html" class="news-newtop-gd2">查看更多</a>
				</div>
				<div class="index-news-newlist">
					<ul id="newsListtwo">
						<!-- <li>
							<a href="news/micro-news/20190721082311.html" class="a-color">
								<p><i>第6期 为什么不要用EXCEL来管理客户数据</i><em class="index-em"></em></p>
							</a>
						</li>
						<li>
							<a href="news/micro-news/20190715150327.html" class="a-color">
								<p><i>第5期 客户数据安全就是展览业的经营保障</i><em class="index-em"></em></p>
							</a>
						</li>
						<li>
							<a href="news/micro-news/20190402221327-79.html" class="a-color">
								<p><i>第4期 如何识别重复的观众邀约数据？</i><em class="index-em"></em></p>
							</a>
						</li> -->
					</ul>
				</div>
			</div>
			<script type="text/javascript">
			

					$('.news-newtop-gd2').eq(0).on("click", function() {
						$(location).attr('href', 'contact.html?index=' + 2)
					})
					var page = 1;
					var rows = 4;
					var total = 0;
					//var newsTypeId =12
					//var newsTypeId = getQueryString("newsTypeId");
					//var typeName = getQueryString("typeName");


					$(function() {

						//getPage();
						loadNews();
						loadNewstwo()
					})

					function loadNews() {
						//alert("a")
						$.ajax({
							url: variableSponsor + '/news/getList', //请求服务器url地址.   
							type: "post",
							data: {
								page: page,
								rows: rows,
								exhibitCode: exhibitCode,
								projectId: pid,
								newsTypeId: indexnewsoneId,
								isShow: true
							},
							async: false,
							xhrFields: {
								withCredentials: true
							},
							success: function(data) {
								console.log(data)

								var data = JSON.parse(data);
								total = data.total;
								var result = data.rows;
								var html = "";
								console.log(result.typeName)

								for (var i = 0; i < result.length; i++) {

									html += '<li><a href="javascript:void(0);" class="a-color" onclick="loadIframe(' +
										'\'' + result[i].fileName + '\',' + result[i].newsId + ')" >' +
										'<p><i>' + result[i].titleName + '</i><em class="index-em"></em></p></a></li>'
								}
								//alert(html) 
								$('#newsList').empty().append(html);

								return false;
							},
							error: function(data) {
								alert("数据加载失败");
								return false;
							}
						});
						
						
					}
					function loadIframe(fileName, id) {
						//alert("a");
						window.location.href = "news-details.html" + '?newsId=' + id;
					}
                     // var newsTypeId = 10;
					function loadNewstwo() {
						//alert("a")
						$.ajax({
							url: variableSponsor + '/news/getList', //请求服务器url地址.   
							type: "post",
							data: {
								page: page,
								rows: rows,
								exhibitCode: exhibitCode,
								projectId: pid,
								newsTypeId: indexnewstwoId,
								isShow: true
							},
							async: false,
							xhrFields: {
								withCredentials: true
							},
							success: function(data) {
								console.log(data)

								var data = JSON.parse(data);
								total = data.total;
								var result = data.rows;
								var html = "";
								console.log(result.typeName)
								for (var i = 0; i < result.length; i++) {

										html += '<li><a href="javascript:void(0);" class="a-color" onclick="load(' +
											'\'' + result[i].fileName + '\',' + result[i].newsId + ')" >' +
											'<p><i>' + result[i].titleName + '</i><em class="index-em"></em></p></a></li>'
									}
								//alert(html) 
								$('#newsListtwo').empty().append(html);

								return false;
							},
							error: function(data) {
								alert("数据加载失败");
								return false;
							}
						});
						
					}
					
                    function load(fileName, id) {
                    	window.location.href = "micro-template.html" + '?newsId=' + id;
                    } 


				
			</script>
		</div>

		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="product.html">最新产品</a></li>
				<li><a href="manage-platform.html">展之方案</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.index-list ul li').eq(3).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 2)
				})
				$('.index-list ul li').eq(4).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 1)
				})
				$('.index-list ul li').eq(5).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 0)
				})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>


	</body>
</html>
