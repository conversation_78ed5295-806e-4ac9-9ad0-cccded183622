<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>联系我们</title>
		<link rel="stylesheet" type="text/css" href="css/allstyle.css" />
		<script type="text/javascript" src="js/rem.js"></script>
		<script type="text/javascript" src="js/jquery-2.1.1.min.js"></script>
		<script src="js/mobile.js" type="text/javascript" charset="utf-8"></script>
		<script src="common/variable.js" type="text/javascript" charset="utf-8"></script>
		<style type="text/css">
			.load-more {
				background-color: #f1f4fb;
				margin-top: .3rem;
				font-size: .32rem;
				text-align: center;
				padding: .3rem 0;
				color: #00a4ff;
			}

			.load-more a {
				color: #00a4ff;
			}
		</style>
	</head>
	<body>
		<!-- log -->
		<div class=" index-logs">
			<div class="index-log">
				<a href="index.html">
					<img src="img/index/logo.png">
				</a>
			</div>
			<div class="index-nav">
				<!-- <i></i> -->
				<em id="index-nav1"></em>
			</div>

		</div>
		<!-- 顶部菜单栏 -->
		<div class="s-side">
			<ul>
				<!--这部分是导航栏信息。-->
				<!-- <li class="first">
					<div class="d-firstNav s-firstNav">
						<span>超级管理员，你好</span>
						<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="s-secondItem"><a href="#">账号设置</a>
						</li>
						<li class="s-secondItem"><a href="#">未读消息</a>
						</li>
						<li class="s-secondItem"><a href="#">我的试用</a>
						</li>
						<li class="s-secondItem"><a href="#">退出</a>
						</li>
					</ul>
				</li> -->
				<li class="first">
					<a href="index.html" class="font-sz">首页</a>
				</li>
				<li class="first">
					<a href="solution.html" class="font-sz">解决方案</a>
				</li>
				<li class="first">
					<div class="d-firstNav s-firstNav clearfix ">
						<span>产品中心</span>
						<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
					</div>
					<ul class="d-firstDrop s-firstDrop">
						<li class="tiao">
							<a href="javascript:void(0)">
								展之云
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之通
							</a>
						</li>
						<li class="tiao">
							<a href="javascript:void(0)">
								展之客
							</a>
						</li>
					</ul>
				</li>
				<li class="first">
					<a href="successful.html" class="font-sz">成功案例</a>
				</li>
				<li class="first">
					<a href="micro.html" class="font-sz">展之微刊</a>
				</li>
				<!-- <li class="first">
					<a href="#" class="font-sz">服务体系</a>
				</li>
				<li class="first">
					<a href="#" class="font-sz">会员中心</a>
				</li> -->
				<li class="first">
					<a href="contact.html" class="font-sz">联系我们</a>
				</li>
			</ul>
		</div>


		<script type="text/javascript">
			$(function() {
				$('.tiao').each(function(i) {
					$('.tiao').eq(i).on("click", function() {
						console.log("1111")
						$(location).attr('href', 'product.html?index=' + i)
					})
				})
			})
		</script>

		<!-- 联系我们tab -->
		<!-- 联系我们tab 切换标题-->
		<div class="contact-lists">
			<ul>
				<li class="contact-active">
					联系我们
					<em class="contact-em-active"></em>
				</li>
				<li>
					关于我们
					<em></em>
				</li>
				<li>
					展之动态
					<em></em>
				</li>
			</ul>
		</div>
		<!-- 联系我们tab 三个内容 -->
		<div class="contact-contans">
			<!-- 联系我们tab 内容一 -->
			<div class="contact-list1">
				<div class="contact-baner">
					<img src="img/index/banner-vip.jpg">
					<h5>联系我们</h5>
					<em></em>
					<p>产品升级限时免费体验</p>
				</div>
				<div class="contact-cnt">
					<div class="contact-cnt-title">
						<em></em>
						<h4>联系我们</h4>
					</div>
					<div class="contact-cnt1 contact-cnt3">
						<div class="contact-cnt1-border">
							<div class="contact-cnt1-top bg3">
								<img src="img/connect/contact3.png">
							</div>
							<div class="contact-cnt1-botton">
								<h5>电话联系：</h5>
								<p>0571 - 86787239</p>
								<p>17364535378</p>
							</div>
						</div>
					</div>
					<div class="contact-cnt1">
						<div class="contact-cnt1-border">
							<div class="contact-cnt1-top">
								<img src="img/connect/contact1.png">
							</div>
							<div class="contact-cnt1-botton">
								<h5>QQ联系：</h5>
								<p>2851704317</p>
								<p>2851704318</p>
							</div>
						</div>
					</div>
					<div class="contact-cnt1 contact-cnt4">
						<div class="contact-cnt1-border">
							<div class="contact-cnt1-top bg4">
								<img src="img/connect/contact4.png">
							</div>
							<div class="contact-cnt1-botton" style="border: none;">
								<h5>邮箱联系：</h5>
								<p><EMAIL></p>
							</div>
						</div>
					</div>
					<div class="contact-cnt1 contact-cnt2">
						<div class="contact-cnt1-border">
							<div class="contact-cnt1-top bg2 contact-wei-img">
								<img src="img/connect/wechat.jpg">
							</div>
							<div class="contact-cnt1-botton contact-wei">
								<h6>展之微信公众平台</h6>
								<p>获取更多资讯，体验展之最新产品，扫码关注展之科技微信公众号</p>
							</div>
						</div>
					</div>
				</div>
				<!-- 服务时间 -->
				<div class="contact-time">
					<h4>服务时间</h4>
					<p>周一</p>
					<span></span>
					<p>周六</p>
					<p>08:30</p>
					<span></span>
					<p>17:30</p>
					<div class="contact-time-text">
						<p>欢迎拨打客户热线，我们会对您的问题认真解答，<br />
							发送邮件我们将尽快回复</p>
					</div>
				</div>
			</div>
			<!-- 联系我们tab 内容二 -->
			<div class="contact-list1 contact-list2" style="display: none;">
				<div class="contact-cnt-title contact-cnt-title2">
					<em></em>
					<h4>关于我们</h4>
				</div>
				<div class="contact-list2-text">
					<h4>公司简介：</h4>
					<div class="contact-list2-content">
						<p>杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研发和服务的高新科技企业。</p>
					</div>
				</div>
				<div class="contact-list2-text">
					<h4>重点产品：</h4>
					<div class="contact-list2-content">
						<p>公司2003年率先将CRM管理理念引入会展行业，目前，产品与服务已广泛应用于会展行业的主办、承办、招展、搭建及展会相关服务企业，主要有展览CRM软件、观众登记、呼叫中心、动态展位图、会员中心等。</p>
					</div>
				</div>
				<div class="contact-list2-text">
					<h4>我们的客户：</h4>
					<div class="contact-list2-content">
						<p>精巧的软件设计思路、实用的操作界面和优质的技术服务赢得了包括北京领汇、北京中盈展、北京北辰、上海中贸慕尼黑、上海荷瑞、上海展业、广东亚联、智奥鹏城、深圳环球、青岛海名、厦门誉颁、厦门领肯、江苏汇鸿、河北鼎亚、振威集团、鸿尔集团等近400家会展企业的支持与认可。</p>
					</div>
				</div>
				<div class="contact-list2-text">
					<h4>我们的目标：</h4>
					<div class="contact-list2-content">
						<p>凭借对会展行业的深刻理解和丰富的实施经验，并借助互联网应用进行行业化信息资源整合，为新老用户和整个行业的发展实施前瞻性布局。</p>
					</div>
				</div>
			</div>
			<!-- 联系我们tab 内容三 -->
			<div class="contact-list1 contact-list3" style="display: none;">
				<div class="contact-cnt-title contact-cnt-title2 contact-cnt-title3">
					<em></em>
					<h4 class="contact-lists-active">展之动态</h4>
					<!-- <h5>展之微刊</h5> -->
				</div>
				<!-- 公司新闻列表 -->
				<div class="contact-lists1 lists1">
					<ul id="newsList">
						<!-- <li>
							<a href="product.html">
								<div class="contact-lists1-text">
									<h4>动态展位图第一期上线。</h4>
									<p>2019年6月，动态展位图第一期上线。</p>
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/2019429.html">
								<div class="contact-lists1-text contact-text-width">
									<h4>公司成为浙江省国际会议展览业协会理事单位。</h4>
									<p>2019年4月，公司成为浙江省国际会议展览业协会理事单位。</p>
								</div>
								<div class="contact-lists1-img">
									<img src="img/news/company-news/img2.jpg">
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/2018816.html">
								<div class="contact-lists1-text contact-text-width">
									<h4>公司荣获杭州市高新技术企业称号。</h4>
									<p>2018年8月，公司荣获杭州市高新技术企业称号。</p>
								</div>
								<div class="contact-lists1-img">
									<img src="img/news/company-news/img1.jpg">
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/201612.html">
								<div class="contact-lists1-text">
									<h4>公司开始研发动态展位图、会员中心等</h4>
									<p>本公司2003年率先将CRM管理理念引入会展行业，产品与服务已广泛应用于会展行业的主办、承办、招展、搭建及展会相关服务企业。</p>
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/20170927222957-67.html">
								<div class="contact-lists1-text contact-text-width">
									<h4>展之云移动CRM升级，增加微信推送提醒、地图导航、收藏夹等功能</h4>
									<p>展之云移动CRM最新版本升级上线，新版本增加了微信推送提醒、地图导航、收藏夹等功能，功能更加实用。</p>
								</div>
								<div class="contact-lists1-img">
									<img src="img/news/company-news/20170927225221922192.png">
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/20161205175648-63.html">
								<div class="contact-lists1-text">
									<h4>杭州展之信息技术有限公司成立啦！原新特客户服务会有变化？</h4>
									<p>由于公司发展需要，“温州新特软件开发有限公司”名称从2016年12月1日变更为“杭州展之信息技术有限公司”，地址从“温州站南商贸城”迁至“杭州经济开发区”，届时原公司所有业务由杭州展之信息技术有限公司统一经营，原公司鉴订的合同继续有效，原有业务关系和服务承诺保持不变。</p>
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/20161028090805-61.html">-->
						<!-- 有图添加类名contact-text-width -->
						<!-- <div class="contact-lists1-text contact-text-width">
									<h4>热烈祝贺中国会展业联盟成立</h4>
									<p>新特软件总经理季武参加会议，希望能与新特新老客户及业内朋友共同努力，把中国会展产业做大做强！</p>
								</div>
								<div class="contact-lists1-img">
									<img src="img/news/company-news/20161028091110231023.jpg">
								</div>
							</a>
						</li>
						<li>
							<a href="news/bulletin/20160715175439-52.html">-->
						<!-- 有图添加类名contact-text-width -->
						<!-- <div class="contact-lists1-text contact-text-width">
									<h4>新特软件成立16周年庆活动方案</h4>
									<p>活动主题：16年来，感谢有您！为答谢新老客户，值此新特软件16周岁生日，我们以多重优惠，多重好礼相送，力度空前！.</p>
								</div>
								<div class="contact-lists1-img">
									<img src="img/news/company-news/20160715175975097509.jpg">
								</div>
							</a>
						</li> -->
					</ul>
					<div class="load-more" id="load">
						<a href="javascript:void(0);" onclick="loadMore()">点击加载更多</a>
					</div>
					<div class="contact-lists1-end"  id="noData" style="display: none;">
						———— 没有更多了 ————
					</div>
				</div>

			</div>

		</div>



		<script>
			//var newsTypeId = getQueryString("newsTypeId");
			// var newsTypeId = 12;
			var page = 1;
			var rows = 4;
			var num = 0;
			var total = 0;
			//var newsTypeId = getQueryString("newsTypeId");
			//var typeName = getQueryString("typeName");

			$(function() {

				loadNews();
			})


			function loadNews() {

				$.ajax({
					url: variableSponsor + '/news/getList', //请求服务器url地址.   
					type: "post",
					data: {
						page: page,
						rows: rows,
						exhibitCode: exhibitCode,
						projectId: pid,
						newsTypeId: aboutnews,
						//notInId: topNewsId
						//isShow: true
					},
					async: false,
					xhrFields: {
						withCredentials: true
					},
					success: function(data) {

						console.log(data)
						var data = JSON.parse(data);
						var total = data.total;
						var result = data.rows;

						

						var newsListHtml = '';
						for (var i = 0; i < result.length; i++) {
							num++;
							if (result[i].logo != "") {
								newsListHtml += '<li><a href="javascript:void(0);" onclick="loadIframe(' +
									'\'' + result[i].fileNameMobile + '\',' + result[i].newsId + ')" >' +
									'<div class="contact-lists1-text contact-text-width"><h4>' + result[i].titleName +
									'</h4><p>' + result[i].brief + '</p></div><div class="contact-lists1-img">' +
									'<img src=" ' + result[i].logo + ' ">' + '</div></a></li>'

							} else {

								newsListHtml += '<li><a href="javascript:void(0);" onclick="loadIframe(' +
									'\'' + result[i].fileNameMobile + '\',' + result[i].newsId + ')" >' +
									'<div class="contact-lists1-text contact-text-width"><h4>' + result[i].titleName +
									'</h4><p>' + result[i].brief + '</p></div>' +
									'</a></li>'

							}
						}
						$("#newsList").append(newsListHtml);











						//alert(html) 
						if (newsListHtml == "") {
							//alert("没有更多了");
							$("#load").hide();
							$("#noData").show();
						} else {
							$("#newsList").append();
						}

						if (result.total <= num) {
							$("#load").hide();
							$("#noData").show();
						}
						return false;
					},
					error: function(data) {
						alert("数据加载失败");
						return false;
					}
				});
			}

			function loadMore() {
				page++;
				loadNews();
			}

			function loadIframe(fileName, id) {
				window.location.href = fileName + '?newsId=' + id;
			}
		</script>






		<!-- 底部及列表 -->
		<div class="index-list">
			<ul>
				<li><a href="product.html">最新产品</a></li>
				<li><a href="manage-platform.html">展之方案</a></li>
				<li><a href="successful.html">成功案例</a></li>
				<li><a href="javascript:void(0)">展之动态</a></li>
				<li><a href="javascript:void(0)">关于我们</a></li>
				<li><a href="javascript:void(0)">联系我们</a></li>
			</ul>
		</div>
		<script type="text/javascript">
			$(function() {
				$('.index-list ul li').eq(3).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 2)
				})
				$('.index-list ul li').eq(4).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 1)
				})
				$('.index-list ul li').eq(5).on("click", function() {
					$(location).attr('href', 'contact.html?index=' + 0)
				})
			})
		</script>
		<div class="index-foot">
			<p>Copyright 2016-2019 杭州展之信息技术有限公司 All rights reserved</p>
			<p><a href="http://www.beian.miit.gov.cn" style="color: #7c7c7c;">浙ICP备18032944号</a></p>
		</div>
	</body>
</html>
