.jq22-container {
	margin-top: .88rem;
	height: 3.5rem;
	overflow: hidden;
}

.swipslider {
	position: relative;
	overflow: hidden;
	display: block;
	padding-top: 47% !important;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.swipslider .sw-slides {
	display: block;
	padding: 0;
	list-style: none;
	width: 100%;
	height: 100%;
	white-space: nowrap;
	font-size: 0;
	-webkit-transform: translateX(0);
	transform: translateX(0);
	position: absolute;
	bottom: 0;
}

.swipslider .sw-slide {
	width: 100%;
	height: 100%;
	margin: auto;
	display: inline-block;
	position: relative;
}

.swipslider .sw-slide>img {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	max-height: 100%;
	max-width: 100%;
	margin-left: auto;
	margin-right: auto;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.swipslider .sw-slide .sw-content {
	width: 100%;
	height: 100%;
	margin-left: 0;
	margin-right: 0;
	font-size: .14rem;
}

.sw-bullet {
	position: absolute;
	bottom: 0;
	list-style: none;
	display: block;
	width: 100%;
	text-align: center;
	padding: 0;
	margin: 0;
}

.sw-bullet li {
	width: .15rem;
	height: .15rem;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: .07rem;
	display: inline-block;
	cursor: pointer;
	transition: all .2s ease-out;
	margin-top: .9rem;
	opacity: .5;
}

.sw-bullet li:hover {
	background-color: rgba(255, 255, 255, 0.74);
}

.sw-bullet li.active {
	width: .32rem;
	background-color: #00a4ff;
	box-shadow: 0 0 .02rem rgba(160, 160, 160, 0.53);
}

.sw-bullet li:not(:last-child) {
	margin-right: .24rem;
}
