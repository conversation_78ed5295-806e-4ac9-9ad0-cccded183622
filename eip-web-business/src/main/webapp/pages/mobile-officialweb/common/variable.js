
/*	var variable = window.location.hostname+":"+window.location.port+"/eip-web-business";     
	var variablePort = "8080";
	var  bsadress = window.location.hostname;
	var sponsorURL = window.location.hostname+":"+window.location.port+"/eip-web-sponsor";*/
	
	var exhibitCode = 'E0000000040';//展会代号
	var orgnum=1002;//机构号
	var pid = 411;//项目Id
	var indexnewsoneId = 12; //首页展之动态列表   少数新闻
	var indexnewstwoId = 10; //首页展之微刊列表   少数新闻
	var aboutnews = 12;      //关于我们页面展之动态列表  多条新闻
	var micronews = 10;      //展之微刊页面展之微刊列表  多条新闻
	var variableSponsor =  "http://"+window.location.hostname+":"+window.location.port+"/eip-web-sponsor";//window.location.hostname
	var variableBus ="http://"+ window.location.hostname+":"+window.location.port+"/eip-web-business/pages/frontend/zzy-denglu.html?orgNum="+orgnum;  //展商会员中心
	var variablePic = "http://"+ window.location.hostname+":"+window.location.port+"/image/"; //图片Url
	var variableReg =  "http://"+window.location.hostname+":"+window.location.port+
						"/RegSever/RegPage/pages/ex_cust_ComputerReg_wenzhou.html?EID="+exhibitCode+"&target=1&orgnum="+orgnum+"&pid="+pid; //观众登记
	var variableGraph = "http://"+window.location.hostname+":"+window.location.port+
						"/eip-web-sponsor/backstage/exhibit/section_graph_show?exhibitCode="+exhibitCode+"&sectionCode=-1&orgNum="+orgnum; //展位图
	
	//var variableSponsor =  "http://*************:8080/eip-web-sponsor";



/**************************************时间格式化处理************************************/
function dateFtt(fmt,date){ //author: meizz   
  var o = {   
    "M+" : date.getMonth()+1,                 //月份   
    "d+" : date.getDate(),                    //日   
    "h+" : date.getHours(),                   //小时   
    "m+" : date.getMinutes(),                 //分   
    "s+" : date.getSeconds(),                 //秒   
    "q+" : Math.floor((date.getMonth()+3)/3), //季度   
    "S"  : date.getMilliseconds()             //毫秒   
  };   
  if(/(y+)/.test(fmt))   
    fmt=fmt.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length));   
  for(var k in o)   
    if(new RegExp("("+ k +")").test(fmt))   
  fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));   
  return fmt;   
}


var stroage = localStorage;

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var url=decodeURI(decodeURI(window.location.search))
    var r = url.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;

}



function openBusiness(){
	var url = variableBus;
	window.open(url,'_blank');
}
function openReg(){
	var url = variableReg;
	if(url==""){
		alert("暂未开启")
		return;
	}
	window.open(url,'_blank');
}
function openGraph(){
	var url = variableGraph;
	if(url==""){
		alert("暂未开启")
		return;
	}
	window.open(url,'_blank');
}
