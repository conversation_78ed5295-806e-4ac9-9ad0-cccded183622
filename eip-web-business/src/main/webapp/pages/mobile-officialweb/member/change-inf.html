<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1" />

		<title>信息修改</title>
		<link rel="stylesheet" type="text/css" href="../css/allstyle.css" />
		<script type="text/javascript" src="../js/rem.js"></script>
		<script type="text/javascript" src="../js/jquery-2.1.1.min.js"></script>
		<script src="../js/mobile.js" type="text/javascript" charset="utf-8"></script>
		<script src="../js/distpicker.data.js" type="text/javascript" charset="utf-8"></script>
		<script src="../js/distpicker.js" type="text/javascript" charset="utf-8"></script>
		<script src="../js/main.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!-- log -->
			<div class=" index-logs">
				<div class="index-log">
					<a href="index.html">
					<img src="img/index/logo.png">
					</a>
				</div>
				<div class="index-nav">
					<!-- <i></i> -->
					<em id="index-nav1"></em>
				</div>
				
			</div>
			<!-- 顶部菜单栏 -->
			<div class="s-side">
				<ul>
					<!--这部分是导航栏信息。-->
					<!-- <li class="first">
						<div class="d-firstNav s-firstNav">
							<span>超级管理员，你好</span>
							<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
						</div>
						<ul class="d-firstDrop s-firstDrop">
							<li class="s-secondItem"><a href="#">账号设置</a>
							</li>
							<li class="s-secondItem"><a href="#">未读消息</a>
							</li>
							<li class="s-secondItem"><a href="#">我的试用</a>
							</li>
							<li class="s-secondItem"><a href="#">退出</a>
							</li>
						</ul>
					</li> -->
					<li class="first">
						<a href="index.html" class="font-sz">首页</a>
					</li>
					<li class="first">
						<div class="d-firstNav s-firstNav">
							<span>解决方案</span>
							<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
						</div>
						<ul class="d-firstDrop s-firstDrop">
							<li class="s-secondItem"><a href="../myself-exhibition.html">自办展项目管理解决方案</a>
							</li>
							<li class="s-secondItem"><a href="../agency-exhibition.html">国外代理招展解决方案</a>
							</li>
							<li class="s-secondItem"><a href="#">展会搭建设计解决方案</a>
							</li>
							<li class="s-secondItem"><a href="#">会展场馆运营解决方案</a>
							</li>
							<li class="s-secondItem"><a href="#">展会观众管理解决方案</a>
							</li>
							<li class="s-secondItem"><a href="#">异地/移动办公解决方案</a>
							</li>
						</ul>
					</li>
					<li class="first">
						<div class="d-firstNav s-firstNav clearfix">
			
							<span>产品中心</span>
							<i class="fa fa-caret-right fr "><img src="img/index/arrow3.png"></i>
						</div>
						<ul class="d-firstDrop s-firstDrop">
							<li>
								<div class="d-secondNav s-secondNav"><span>展之云</span>
									<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
								</div>
								<ul class="d-secondDrop s-secondDrop">
									<li class="s-thirdItem">
										<a href="#">观众登记平台</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">展商会员中心</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">展会管理后台</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">动态展位图</a>
									</li>
								</ul>
							</li>
							<li>
								<div class="d-secondNav s-secondNav"><span>展之通</span>
									<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
								</div>
								<ul class="d-secondDrop s-secondDrop">
									<li class="s-thirdItem">
										<a href="#">电话助手</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">提醒推送</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">公告通知</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">短信邮件接口</a>
									</li>
								</ul>
							</li>
							<li>
								<div class="d-secondNav s-secondNav">
									<span>展之客</span>
									<i class="fa fa-caret-right fr"><img src="img/index/arrow3.png"></i>
								</div>
								<ul class="d-secondDrop s-secondDrop">
									<li class="s-thirdItem">
										<a href="#">客户关系管理</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">项目管理</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">财务管理</a>
									</li>
									<li class="s-thirdItem">
										<a href="#">文档管理</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
					<li class="first">
						<a href="#" class="font-sz">成功案例</a>
					</li>
					<li class="first">
						<a href="#" class="font-sz">服务体系</a>
					</li>
					<li class="first">
						<a href="#" class="font-sz">会员中心</a>
					</li>
				</ul>
			</div>
			
			
		<script type="text/javascript">
				$(function() {
					$('.d-secondDrop').each(function(i) {
						$(this).find("li").on("click", function() {
							$(location).attr('href', 'product.html?index=' + i)
						})
					})
				})
			</script>
		<!-- 信息修改tab -->
		<!-- 信息修改tab 切换标题-->
		<div class="contact-lists" style="padding-left: 0;">
			<i></i>
			<ul>
				<li class="contact-active">
					基本信息
					<em class="contact-em-active"></em>
				</li>
				<li>
					修改手机
					<em></em>
				</li>
				<li>
					修改邮箱
					<em></em>
				</li>
			</ul>
		</div>
		<div class="inf-content">
			<!-- 基本信息 -->
			<div class="inf-content1">
				<form action="" method="post">
					<p>真实姓名：<input type="" name="" id="" value="" /></p>
					<p>年龄：<input type="" name="" id="" value="" /></p>
					<div id="radio1">
						<label>性别：</label>
						<input type="radio" checked name="sex" class="gcs-radio" id="男" />

						<label for="男">男</label>

						<input type="radio" name="sex" class="gcs-radio" id="女" style="margin-left: 1.1rem;" />

						<label for="女">女</label>
					</div>
					<div id="radio2">
						<label>会员身份：</label>
						<input type="radio" name="sex1" checked class="gcs-radio" id="展商" />

						<label for="">展商</label>

						<input type="radio" name="sex1" class="gcs-radio" id="观众" />

						<label for="">观众</label>
					</div>

					<input type="submit" name="windows_qr" id="windows_qr" value="确认修改" />
				</form>

			</div>
			<!-- 修改手机 -->
			<div class="inf-content1 inf-content2" style="display: none;">
				<form action="" method="post">
					<p>请输入手机号：<input type="" name="" id="" value="" /></p>
					<p>请输入验证码：<input type="" name="" id="" value="" />
						<input type="button" name="" id="" value="发送验证码" /></p>
					<p>公司：<input type="text" name="" id="" value="" /></p>
					<p>职务：<input type="" name="" id="" value="" /></p>
					<div data-toggle="distpicker" class="windows2_select">
						<i>地址</i>
						<select class="form-control" id="province3" data-province="浙江省"></select>
						<select class="form-control" id="city3" data-city="杭州市"></select>
					</div>
					<input type="submit" name="windows_qr" id="windows_qr" value="确认修改" />
				</form>
			</div>
			<!-- 修改邮箱 -->
			<div class="inf-content1 inf-content2 inf-content3" style="display: none;">
				<form action="" method="post">
					<p>请输入邮箱：<input type="" name="" id="" value="" style="margin-left: .42rem;width: 4.2rem;" /></p>
					<p>请输入验证码：<input type="" name="" id="" value="" />
						<input type="button" name="" id="" value="发送验证码" /></p>

					<input type="submit" name="windows_qr" id="windows_qr" value="确认修改" />
				</form>
			</div>
		</div>
	</body>
</html>
