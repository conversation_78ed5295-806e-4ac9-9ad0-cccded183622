$(function() {

	$(".idx-search-ud-top").on("click", function() {
		$(".idx-search-ud-botton").slideToggle();
		$(".idx-search-ud-top i ").toggleClass("transform180")
	})

	$(".search-key").focus(function() {
		$(".idx-search-keyword").addClass("hide");
	});

	$(".search-key").blur(function() {
		var val = $(".search-key").val()
		if (val != "") {
			return;
		} else {
			$(".idx-search-keyword").removeClass("hide");
		}

	});
	

})
function delterm(obj){
	var sig  = $(".idx-conditions").length;
	alert(sig)
	$(obj).parent(".idx-conditions").remove()
	if (sig==1) {
	$(".idx-top-conditions").addClass("hide")	
	}
}
