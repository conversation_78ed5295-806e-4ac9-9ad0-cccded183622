@charset "utf-8";

/* CSS Document  Basic CSS */
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

body,
button,
input,
select,
textarea {
	font-size: 12px;
	font-family: Arial, "微软雅黑";
	color: #333;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

fieldset,
img {
	border: 0 none;
}

img {
	max-width: 100%;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var {
	font-style: normal;
	font-weight: normal;
}

ol,
ul {
	list-style-type: none;
}

caption,
th {
	text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: 100%;
	font-weight: normal;
}

i {
	font-style: normal;
}

q:before,
q:after {
	content: ''
}

abbr,
acronym {
	border: 0;
}

/* b {
	font-weight: normal;
} */

ul,
li {
	list-style: none;
}

* {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	-webkit-tap-highlight-color: transparent;
	-moz-tap-highlight-color: rgba(0, 0, 0, 0);
	-moz-tap-highlight-color: transparent;
	-o-tap-highlight-color: rgba(0, 0, 0, 0);
	-o-tap-highlight-color: transparent;
	-ms-tap-highlight-color: rgba(0, 0, 0, 0);
	-ms-tap-highlight-color: transparent;
	/* -webkit-box-sizing:border-box; -moz-box-sizing:border-box; -o-box-sizing:border-box; -ms-box-sizing:border-box; box-sizing:border-box;*/
}

::selection {
	background: #F3760A;
	color: #fff;
}

/*链接*/
a {
	color: #333;
	text-decoration: none;
	outline: none;
}

a:hover {
	color: #ffa100;
}

/*a:visited{ color:red;}*/
/*定位*/
.oh {
	overflow: hidden;
}

.oa {
	overflow: auto;
}

.tl {
	text-align: left;
}

.tc {
	text-align: center;
}

.tr {
	text-align: right;
}

.bc {
	margin-left: auto;
	margin-right: auto;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.cb {
	clear: both;
}

.cl {
	clear: left;
}

.cr {
	clear: right;
}

.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}


*html {
	height: 1%;
}

.clearfix {
	height: 1%;
	display: block;
}

.vm {
	vertical-align: middle;
}

.pr {
	position: relative;
}

.pa {
	position: absolute;
}

.zoom {
	zoom: 1;
}

.hidden {
	visibility: hidden;
}

.none {
	display: none;
}

.ti2 {
	text-indent: 2em;
}

input:focus,
textarea:focus {
	outline: 0 none;
}

/*解决google浏览器input的默认边框*/
/*解决google浏览器textarea的默认边框*/
*html {
	background-image: url(about:blank);
	background-attachment: fixed;
}

/* 解决IE6下定位时闪动的BUG */
/*字体大小*/
.f12 {
	font-size: 12px;
}

.f13 {
	font-size: 13px;
}

.f14 {
	font-size: 14px;
}

.f16 {
	font-size: 16px;
}

.f18 {
	font-size: 18px;
}

.f20 {
	font-size: 20px;
}

.f22 {
	font-size: 22px;
}

.f24 {
	font-size: 24px;
}

.fb {
	font-weight: bold;
}

/*宽**高*/
.w {
	width: 980px;
}

.h {
	height: 100%;
}

.lh100 {
	line-height: 100%;
}

.lh120 {
	line-height: 120%;
}

.lh150 {
	line-height: 150%;
}

.lh180 {
	line-height: 180%;
}

.lh190 {
	line-height: 190%;
}

.lh200 {
	line-height: 200%;
}

.lh220 {
	line-height: 220%;
}

/*边**距*/
.m0 {
	margin: 0;
}

.ml0 {
	margin-left: 0;
}

.mr0 {
	margin-right: 0;
}

.mb0 {
	margin-bottom: 0;
}

.m5 {
	margin: 5px;
}

.m10 {
	margin: 10px;
}

.m15 {
	margin: 15px;
}

.mt5 {
	margin-top: 5px;
}

.mt10 {
	margin-top: 10px;
}

.mt15 {
	margin-top: 15px;
}

.mt20 {
	margin-top: 20px;
}

.mt25 {
	margin-top: 25px;
}

.mr5 {
	margin-right: 5px;
}

.mr10 {
	margin-right: 10px;
}

.mr15 {
	margin-right: 15px;
}

.mb5 {
	margin-bottom: 5px;
}

.mb10 {
	margin-bottom: 10px;
}

.mb15 {
	margin-bottom: 15px;
}

.mb20 {
	margin-bottom: 20px;
}

.mb25 {
	margin-bottom: 25px;
}

.ml5 {
	margin-left: 5px;
}

.ml10 {
	margin-left: 10px;
}

.ml15 {
	margin-left: 15px;
}

.ml20 {
	margin-left: 20px;
}

.ml25 {
	margin-left: 25px;
}

.p2 {
	padding: 2px;
}

.p3 {
	padding: 3px;
}

.p5 {
	padding: 5px;
}

.p10 {
	padding: 10px;
}

.p15 {
	padding: 15px;
}

.pt1 {
	padding-top: 1px;
}

.pt5 {
	padding-top: 5px;
}

.pt10 {
	padding-top: 10px;
}

.pt15 {
	padding-top: 15px;
}

.pr5 {
	padding-right: 5px;
}

.pr10 {
	padding-right: 10px;
}

.pr15 {
	padding-right: 15px;
}

.pb5 {
	padding-bottom: 5px;
}

.pb10 {
	padding-bottom: 10px;
}

.pb15 {
	padding-bottom: 15px;
}

.pl5 {
	padding-left: 5px;
}

.pl10 {
	padding-left: 10px;
}

.pl15 {
	padding-left: 15px;
}

.per45 {
	width: 45%;
}

.per50 {
	width: 50%;
}

.per55 {
	width: 55%;
}

.per60 {
	width: 60%;
}

.per70 {
	width: 70%;
}

.per75 {
	width: 75%;
}

.per80 {
	width: 80%;
}

.per100 {
	width: 100%;
}

.all-width {
	width: 100%;
	height: 100%;
}

.all-center {
	width: 1200px;
	height: 100%;
	margin: 0 auto;
}

.bgc-f5 {
	background-color: #f5f5f5;
}

/*清除浮动代码*/
.clearfloat:after {
	display: block;
	clear: both;
	content: "";
	visibility: hidden;
	height: 0
}

.clearfloat {
	zoom: 1
}

.show {
	display: block !important;
}

.hide {
	display: none !important;
}

/* 首页 */
.idx-top-log {
	width: 100%;
	height: 100%;
	z-index: 2;
}

.greeting {
	float: left;
	color: #666666;
	line-height: 32px;
	text-align: left;
}

.idx-top-logs {
	float: right;
	height: 32px;
	overflow: hidden;
}

.idx-top-logs a {
	float: left;
	height: 32px;
	display: block;
	box-sizing: border-box;
	color: #666666;
	line-height: 32px;
	padding: 0 12px;
}

.idx-top-logs a:first-child {
	color: #eb4c4c;
}

.idx-top-list {
	float: right;
}

.idx-top-list>ul>li {
	float: left;
	position: relative;
	line-height: 32px;
	cursor: pointer;
}

.idx-ud-top {
	overflow: hidden;
	height: 32px;
	box-sizing: border-box;
	padding: 0 18px;
	line-height: 32px;
}

.idx-ud-top h6 {
	display: block;
	height: 100%;
	float: left;
	color: #666666;
}

.idx-ud-top i {
	display: block;
	width: 10px;
	height: 4px;
	background-color: #999999;
	float: left;
	margin: 14px 0 0 4px;
}

.idx-ud-botton {
	width: 100px;
	box-sizing: border-box;
	border: 1px solid #f5f5f5;
	position: absolute;
	left: 0;
	border-top: none;
	background: #fff;
	z-index: 2;
	display: none;
}

.idx-ud-botton ul li {
	width: 100%;
	height: 32px;
	line-height: 32px;
	text-align: center;
	cursor: pointer;
}

.idx-ud-botton ul li a {
	width: 100%;
	height: 100%;
	display: block;
	color: #666666;
}

.idx-logo {
	padding: 30px 0;
	float: left;
}

.idx-logo img {
	display: block;
	width: 195px;
	height: 40px;
}

.idx-search {
	width: 604px;
	height: 40px;
	box-sizing: border-box;
	border: solid 2px #eb4c4c;
	border-radius: 20px;
	float: right;
	margin: 30px 180px 30px 0;
}

.idx-search-ud {
	width: 100%;
	height: 100%;
	position: relative;
}

.idx-search-ud-top {
	width: 88px;
	height: 36px;
	background-color: #f5f5f5;
	border-radius: 20px 0px 0px 20px;
	overflow: hidden;
	float: left;
	cursor: pointer;
}

.idx-search-ud-top h6 {
	font-size: 16px;
	line-height: 36px;
	color: #666;
	margin-left: 20px;
	float: left;
}

.idx-search-ud-top i {
	width: 12px;
	height: 6px;
	background: url(../img/down.png) no-repeat center center;
	background-size: 12px;
	display: block;
	float: left;
	margin: 14px 0 0 6px;
}

.transform180 {
	transform: rotate(180deg);
}

.idx-search-ud-botton {
	width: 88px;
	background-color: #FFFFFF;
	box-sizing: border-box;
	border: 1px solid #999;
	border-top: none;
	position: absolute;
	left: 0;
	top: 36px;
	z-index: 2;
	display: none;
}

.idx-search-ud-botton ul li {
	width: 100%;
	height: 32px;
	line-height: 32px;
	text-align: center;
	cursor: pointer;
}

.idx-search-inp {
	position: relative;
	width: 400px;
	height: 100%;
	float: left;
}

.search-key {
	width: 400px;
	height: 36px;
	border: none;
	box-sizing: border-box;
	padding: 0 10px;
}

.search-key::placeholder {
	color: #999;
	font-size: 14px;
}

.idx-search-keyword {
	position: absolute;
	right: 22px;
	top: 6px;
}

.idx-search-keyword a {
	box-sizing: border-box;
	padding: 4px 6px;
	color: #999999;
	background-color: #eaeaea;
	border-radius: 2px;
	display: block;
	float: left;
	margin-right: 10px;
}

.idx-search-keyword a:hover {
	background-color: #eb4c4c;
	color: #ffffff;
}

.idx-search-anniu {
	float: left;
	width: 110px;
	height: 36px;
	border: none;
	background-color: #eb4c4c;
	border-radius: 0px 20px 20px 0px;
	margin-left: 2px;
	font-size: 16px;
	color: #ffffff;
	cursor: pointer;
}

.red-botton {
	border-bottom: 2px solid #eb4c4c;
}

.idx-top-a {
	width: 100%;
	height: 58px;
	overflow: hidden;
}

.idx-top-a ul li {
	float: left;
	line-height: 58px;
	font-size: 16px;
	color: #333333;
	margin-right: 60px;
}

.idx-top-a ul li:hover a {
	color: #ed3e3e;
}

.col-red {
	color: #ed3e3e;
}

.idx-all-category {
	margin: 16px 15px 50px 0;
	float: left;
	height: 100%;
	box-sizing: border-box;
	border: 1px solid #eb4c4c;
}

.idx-all-category h6 {
	display: block;
	width: 200px;
	height: 40px;
	background-color: #eb4c4c;
	color: #fff;
	font-size: 14px;
	line-height: 40px;
	box-sizing: border-box;
	padding-left: 15px;
}

.idx-category-list ul li {
	width: 100%;
	height: 36px;
}

.idx-category-list ul li a {
	display: block;
	width: 100%;
	height: 100%;
	line-height: 36px;
	box-sizing: border-box;
	padding-left: 15px;
	font-size: 14px;
	color: #333333;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.idx-category-list ul li:hover a {
	color: #eb4c4c;
	background-color: #fff0f0;
}

/* 轮播图 */
.idx-banner {
	width: 980px;
	float: left;
	margin-top: 16px;
}

.banner {
	width: 980px;
	height: 350px;
	position: relative;
	overflow: hidden;
}

.banner ul {
	width: 6600px;
	height: 350px;
	position: absolute;
	top: 0;
	left: 0;
}

.banner ul li {
	float: left;
	width: 980px;
	height: 350px;
}

.banner ul img {
	display: block;
	width: 100%;
	height: 100%;
}

.banner ol {
	position: absolute;
	width: 100%;
	bottom: 10px;
	left: 0;
	text-align: center;
	font-size: 0px;
}

.banner ol li {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background-color: #888;
	display: inline-block;
	margin: 0 3px;
}

.banner ol li.on {
	background-color: #ff6a00;
}

.banner .btn {
	width: 30px;
	height: 50px;
	background-color: #808080;
	opacity: .2;
	position: absolute;
	top: 50%;
	margin-top: -25px;
	cursor: pointer;
	text-align: center;
	line-height: 50px;
	font-size: 40px;
	color: #fff;
	font-family: '宋体';
}

.banner .btn:hover {
	opacity: .5;
}

.banner .btn_l {
	left: 0;
}

.banner .btn_r {
	right: 0;
}

/* end */
.idx-advertised {
	width: 980px;
	height: 100%;
	overflow: hidden;
	margin-top: 12px;
}

.idx-advertised ul li {
	width: 320px;
	height: 184px;
	background-color: #999;
	float: left;
	margin-right: 10px;
}

.idx-advertised ul li:last-child {
	margin-right: 0px;
}

.idx-advertised ul li a,
.idx-advertised ul li a img {
	display: block;
	width: 100%;
	height: 100%;
}

.idx-hot-expo {
	width: 100%;
	height: 60px;
	overflow: hidden;
}

.idx-hot-expo h6 {
	font-size: 18px;
	color: #333333;
	margin: 26px 0 12px 0;
	display: block;
	float: left;
}

.hot-expo-classify,
.idx-commend-sev {
	float: right;
	margin: 24px 0 9px 0;
}

.hot-expo-classify ul li,
.idx-commend-sev ul li {
	float: left;
	width: 80px;
	height: 28px;
	border-radius: 14px;
	box-sizing: border-box;
	border: solid 1px #eb4c4c;
	margin-left: 30px;
}

.hot-expo-classify ul li a,
.idx-commend-sev ul li a {
	width: 100%;
	height: 100%;
	display: block;
	line-height: 26px;
	text-align: center;
	font-size: 14px;
	color: #eb4c4c;
}

.hot-expo-classify ul li:hover,
.idx-commend-sev ul li:hover {
	background-color: #eb4c4c;
}

.expo-class-active1 {
	background-color: #eb4c4c;
}

.expo-class-active2 {
	color: #fff !important;
}

.hot-expo-classify ul li:hover a,
.idx-commend-sev ul li:hover a {
	color: #fff;
}

.all-expo {
	width: 100%;
	height: 100%;
	overflow: hidden;
	margin: 14px 0 30px 0;
}

.all-expo-button ul li,
.all-expo-top-li {
	width: 285px;
	background-color: #fff;
	float: left;
	margin-right: 20px;
	margin-bottom: 20px;
}

.all-expo-top1 {
	width: 590px;
	height: 295px;
	background-color: #999;
	float: left;
	margin-right: 20px;
}

.all-expo-top img {
	width: 100%;
	height: 100%;
	display: block;
}

.all-expo-button ul li:last-child {
	margin-right: 0px;
}

.all-expo-button ul li a,
.all-expo-infor-img img {
	display: block;
	width: 100%;
	height: 100%;
}

.all-expo-infor-img {
	width: 285px;
	height: 170px;
	background-color: #999;
}

.all-expo-infor-text h6 {
	font-size: 16px;
	color: #333333;
	margin: 16px 0 10px 16px;
	width: 90%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.all-expo-infor-text p {
	font-size: 14px;
	color: #666666;
	margin-bottom: 10px;
	margin-left: 16px;
	width: 90%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.all-expo-infor-text p i {
	display: block;
	float: left;
	width: 16px;
	height: 17px;
	background: url(../img/time.png) no-repeat center;
	background-size: 16px;
	margin-right: 6px;
}

.all-expo-infor-text p:nth-of-type(2) i {
	display: block;
	float: left;
	width: 16px;
	height: 16px;
	background: url(../img/mark.png) no-repeat center;
	background-size: 16px;
	margin-right: 6px;
}

.all-expo-infor-text p:last-child {
	margin-bottom: 32px;
}

.all-expo-button ul li:hover .all-expo-infor-text h6 {
	color: #eb4c4c;
}

.all-expo-top-li:hover .all-expo-infor-text h6 {
	color: #eb4c4c;
}

.idx-festival {
	width: 100%;
	overflow: hidden;
}

.idx-festival h6 {
	font-size: 18px;
	color: #333333;
	margin: 26px 0 12px 0;
	display: block;
	float: left;
}

.idx-festival a {
	font-size: 14px;
	color: #eb4c4c;
	margin: 30px 0 12px 0;
	display: block;
	float: right;
}

/* .idx-festival-list {
	width: 100%;
	height: 100%;
	overflow: hidden;
	margin-bottom: 40px;
}

.idx-festival-list ul li {
	width: 376px;
	height: 90px;
	box-sizing: border-box;
	border-right: 1px solid #eaeaea;
	float: left;
	margin-top: 30px;
	margin-right: 36px;
	overflow: hidden;
}

.idx-festival-list ul li:nth-of-type(3n) {
	margin-right: 0px;
}

.idx-festival-list ul li a {
	display: block;
	width: 100%;
	height: 100%;
}

.idx-festival-list-img {
	width: 70px;
	height: 70px;
	background-color: #999;
	margin: 10px;
	float: left;
}

.idx-festival-list-img img {
	width: 100%;
	height: 100%;
	display: block;
}

.idx-festival-list-text {
	float: left;
	margin-left: 10px;
	width: 70%;
}

.idx-festival-list-text h6 {
	font-size: 15px;
	color: #333333;
	margin: 10px 0 0px 0;
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.idx-festival-list-text span {
	font-size: 14px;
	color: #666666;
	display: block;
	margin-bottom: 6px;
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.idx-festival-list-text p {
	font-size: 12px;
	color: #666666;

}

.idx-festival-list-text p em {
	font-size: 20px;
	color: #ff6633;
	margin-right: 6px;
}

.idx-festival-list ul li:hover .idx-festival-list-text h6 {
	color: #eb4c4c;
} */

/* .idx-commend-exhibitors {
	width: 100%;
	height: 100%;
	overflow: hidden;
	box-sizing: border-box;
	padding-top: 14px;
	margin-bottom: 50px;
}

.idx-commend-exhibitors ul li {
	width: 285px;
	height: 130px;
	box-sizing: border-box;
	border: solid 1px #eaeaea;
	margin: 0 20px 20px 0;
	float: left;
}

.idx-commend-exhibitors ul li:nth-of-type(4n) {
	margin-right: 0;
}

.idx-commend-exhibitors ul li:hover {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

.idx-commend-exhibitors ul li a,
.idx-commend-exhibitors ul li a img {
	display: block;
	width: 100%;
	height: 100%;
	overflow: hidden;
}
 */
.all-commend-sev,
.commend-sev-list {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.all-commend-sev {
	box-sizing: border-box;
	padding-top: 16px;
	margin-bottom: 30px;
}

.commend-sev-list ul li {
	width: 386px;
	height: 142px;
	box-sizing: border-box;
	background-color: #ffffff;
	border-radius: 4px;
	border: solid 1px #eaeaea;
	margin: 0 20px 20px 0;
	float: left;
}

.commend-sev-list ul li:nth-of-type(3n) {
	margin-right: 0;
}

.commend-sev-list ul li a {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.commend-sev-log {
	width: 120px;
	height: 120px;
	border-radius: 4px;
	background-color: #999;
	margin: 11px;
	float: left;
}

.commend-sev-log img {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.commend-sev-text {
	margin-left: 10px;
	position: relative;
	float: left;
	width: 232px;
}

.commend-sev-text h6 {
	font-size: 16px;
	color: #333333;
	width: 95%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin: 11px 0 10px 0;
}

.commend-sev-text span {
	font-size: 18px;
	color: #f74444;
	display: block;
	margin-bottom: 10px;
	width: 80%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	box-sizing: border-box;
	padding-bottom: 1px;
}

.commend-sev-text span i {
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url(../img/phon.png) no-repeat center center;
	background-size: 16px;
	margin-right: 5px;
}

.commend-sev-text span em {
	font-size: 14px;
	color: #666666;
	box-sizing: border-box;
	background-color: #ffffff;
	border-radius: 2px;
	border: solid 1px #cccccc;
	padding: 2px 4px;
	margin-right: 8px;
}

.commend-sev-text strong {
	font-size: 14px;
	color: #eb4c4c;
	position: absolute;
	right: 8px;
	top: 58%;
}

.commend-sev-text p {
	font-size: 14px;
	color: #999999;
}

.commend-sev-text p i {
	font-size: 14px;
	color: #999999;
	margin-right: 10px;
}

.idx-all-trends {
	width: 100%;
	height: 100%;
	overflow: hidden;
	box-sizing: border-box;
	padding-top: 14px;
	margin-bottom: 80px;
}

.idx-trends:nth-of-type(3n) {
	margin-right: 0;
}

.idx-trends {
	width: 385px;
	margin: 0 22px 20px 0;
	float: left;
	box-sizing: border-box;
	border-radius: 4px;
	border: solid 1px #e5e5e5;
}

.idx-trends-title {
	width: 100%;
	height: 36px;
	background-color: #f5f5f5;
	overflow: hidden;
	box-sizing: border-box;
	border-bottom: solid 1px #e5e5e5;
}

.idx-trends-title h4 {
	color: #ffffff;
	font-size: 15px;
	width: 110px;
	background: url(../img/red_bg.png) no-repeat center center;
	background-size: 110px;
	line-height: 36px;
	box-sizing: border-box;
	padding-left: 10px;
	float: left;
}

.idx-trends-title a {
	float: right;
	line-height: 36px;
	font-size: 14px;
	color: #666666;
	padding: 0 15px;
}

.idx-trends-title a:hover {
	color: #eb4c4c;
}

.idx-trends-list {
	box-sizing: border-box;
	padding-bottom: 10px;
}

.idx-trends-list ul li {
	width: 100%;
	height: 34px;
	overflow: hidden;
}

.idx-trends-list ul li a {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.idx-trends-list ul li a i {
	display: block;
	float: left;
	width: 4px;
	height: 4px;
	border-radius: 50%;
	background-color: #eb4c4c;
	margin: 16px 6px 0 10px;
}

.idx-trends-list ul li a p {
	font-size: 14px;
	color: #666666;
	float: left;
	display: inline-block;
	line-height: 36px;
	width: 92%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.idx-trends-list ul li a p:hover {
	color: #eb4c4c;
}

.idx-trends:nth-of-type(2) .idx-trends-list ul li a i {
	background-color: #3faa13;
}

.idx-trends:nth-of-type(3) .idx-trends-list ul li a i {
	background-color: #6586fc;
}

.idx-trends:nth-of-type(4) .idx-trends-list ul li a i {
	background-color: #ff8c66;
}

.idx-trends:nth-of-type(5) .idx-trends-list ul li a i {
	background-color: #58d6f5;
}

.idx-trends:nth-of-type(6) .idx-trends-list ul li a i {
	background-color: #d27af7;
}

.idx-links {
	width: 100%;
	height: 100%;
	overflow: hidden;
	margin-bottom: 36px;
}

.idx-links h4 {
	font-size: 16px;
	color: #3d3d3d;
	margin-top: 24px;
	margin-bottom: 16px;
}

.idx-links ul li {
	float: left;
	box-sizing: border-box;
	border-right: 1px solid #808080;
	margin-bottom: 12px;
}

.idx-links ul li a {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	display: block;
	padding: 0 10px;
	color: #808080;
}

.idx-links ul li a:hover {
	color: #eb4c4c;
}

.idx-botton-links {
	width: 100%;
	height: 100%;
	overflow: hidden;
	border-top: solid 1px rgba(235, 76, 76, .4);
	box-sizing: border-box;
	padding: 20px 0 30px;
	padding-left: 28%;

}

.idx-botton-links ul li {
	float: left;
	box-sizing: border-box;
	border-right: 1px solid #4d4d4d;
}

.idx-botton-links ul li a {
	width: 100%;
	height: 100%;
	padding: 0 8px;
	color: #4d4d4d;
	font-size: 14px;
}

.idx-botton-links ul li a:hover {
	color: #eb4c4c;
}

/* 搜索部分 */
.idx-top-conditions {
	width: 100%;
	height: 100%;
	overflow: hidden;

}

.idx-top-conditions span {
	float: left;
	font-size: 14px;
	color: #666666;
	display: block;
	margin: 19px 0 20px;
	margin-right: 14px;
}

.idx-conditions {
	box-sizing: border-box;
	border-radius: 2px;
	border: solid 1px #e5e5e5;
	float: left;
	margin: 14px 10px 18px 0;
}

.idx-conditions:hover {
	border: solid 1px #eb4c4c;
}

.idx-conditions:hover em {
	background-color: blanchedalmond;
}

.idx-conditions p {
	font-size: 12px;
	color: #666666;
	float: left;
	line-height: 24px;
	margin: 0 4px 0 7px;
}

.idx-conditions em {
	width: 20px;
	height: 24px;
	display: block;
	float: right;
	background-color: red;
	cursor: pointer;
}

.idx-conditions-list {
	width: 100%;
	height: 100%;
	background-color: #fff;
	box-sizing: border-box;
	padding-bottom: 20px;
}

.idx-conditions-adds {
	overflow: hidden;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	margin-bottom: 10px;
	padding: 20px 0 0 20px;

}

.idx-conditions-adds h6 {
	float: left;
	font-size: 14px;
	color: #666666;
	margin-right: 30px;
	margin-top: 2px;
}

.idx-conditions-adds select {
	box-sizing: border-box;
	border-radius: 2px;
	border: solid 1px #fff;
	float: left;
	margin-right: 14px;
	height: 24px;
	line-height: 24px;
	color: #333333;
	padding: 0px 9px;
	outline: none;

}

.idx-conditions-adds select:hover {
	border: solid 1px #eb4c4c;
	color: #eb4c4c;
}

.conditions-adds {
	width: 87%;
	margin: 0 auto;
	box-sizing: border-box;
	padding: 3px 1px 5px 1px;
	background-color: #f5f5f5;
	overflow: hidden;
}

.conditions-adds ul li {
	float: left;
	box-sizing: border-box;
	padding: 10px 15px;
	color: #333333;
	cursor: pointer;
}

.conditions-adds ul li:hover {
	color: #eb4c4c;
	text-decoration: underline;
}

.idx-conditions-adds input[type="button"] {
	width: 44px;
	height: 24px;
	border-radius: 2px;
	border: solid 1px #cccccc;
	float: left;
	background-color: #fff;
	margin-right: 14px;
}

.idx-conditions-adds input[type="button"]:hover {
	border: solid 1px #eb4c4c;
	color: #eb4c4c;
}

.add-time-list ul li {
	box-sizing: border-box;
	padding: 4px 10px;
	cursor: pointer;
	border: 1px solid #fff;
	font-size: 12px;
	color: #666;
	float: left;
	border-radius: 2px;
	margin-right: 14px;
}

.conditions-line {
	width: 91%;
	height: 1px;
	background-color: #e5e5e5;
	margin: 20px auto 0;
}

.add-time-list ul li:hover {
	border: 1px solid #eb4c4c;
	color: #eb4c4c;
}

/* end */
