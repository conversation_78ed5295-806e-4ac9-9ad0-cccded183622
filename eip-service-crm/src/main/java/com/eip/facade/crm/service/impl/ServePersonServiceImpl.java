package com.eip.facade.crm.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ExhibitHaveTools;
import com.eip.facade.crm.entity.ProjTrip;
import com.eip.facade.crm.entity.ServePerson;
import com.eip.facade.crm.entity.ServePersonDtl;
import com.eip.facade.crm.service.ServePersonService;
import com.eip.service.crm.biz.ServePersonBiz;
import com.eip.facade.crm.entity.ServeToolDtl;
import com.eip.facade.crm.entity.VisaDtlList;

@Service("servePersonService")
public class ServePersonServiceImpl implements ServePersonService {

	@Autowired
	private ServePersonBiz servePersonBiz;
	
	@Override
	public int insert(Integer staffId, ServePerson servePerson) {
		return servePersonBiz.insert(staffId, servePerson);
	}

	@Override
	public int update(ServePerson servePerson) {
		return servePersonBiz.update(servePerson);
	}

	@Override
	public ServePerson selectByServePersonId(Integer servePersonId) {
		return servePersonBiz.selectByServePersonId(servePersonId);
	}

	@Override
	public int getSysId(String fieldCode, Boolean increaseMode) {
		return servePersonBiz.getSysId(fieldCode, increaseMode);
	}

	@Override
	public int count(int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.count(key, projectId, clientId, name);
	}
	@Override
	public int countDtl(int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.countDtl(key, projectId, clientId, name);
	}

	@Override
	public List<ServePerson> select(PageParam pageParam, int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.select(pageParam, key, projectId, clientId, name);
	}
	@Override
	public List<ServePersonDtl> selectDtl(PageParam pageParam, int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.selectDtl(pageParam, key, projectId, clientId, name);
	}
	@Override
	public int countToolDtlList(int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.countToolDtlList(key, projectId, clientId, name);
	}
	@Override
	public List<ServeToolDtl> selectToolDtlList(PageParam pageParam, int key, Integer projectId, Integer clientId, String name) {
		return servePersonBiz.selectToolDtlList(pageParam, key, projectId, clientId, name);
	}

	@Override
	public int getToolsCountNotInClient(Integer projectId, Integer clientId, String name) {
		return servePersonBiz.getToolsCountNotInClient(projectId, clientId, name);
	}

	@Override
	public List<ExhibitHaveTools> getToolsNotInClient(PageParam pageParam, Integer projectId, Integer clientId,
			String name) {
		return servePersonBiz.getToolsNotInClient(pageParam, projectId, clientId, name);
	}

	@Override
	public int getToolsCountByProjectId(Integer projectId, String name) {
	
		return servePersonBiz.getToolsCountByProjectId(projectId, name);
	}

	@Override
	public List<ExhibitHaveTools> getToolsByProjectId(PageParam pageParam, Integer projectId, String name) {
		
		return servePersonBiz.getToolsByProjectId( pageParam, projectId, name);
	}

	@Override
	public int deleteBatch(List<ServePersonDtl> servePersonDtlList) {
	
		return servePersonBiz.deleteBatch(servePersonDtlList);
	}

	@Override
	public int insertDtl(ServePersonDtl servePersonDtl) {
	
		return servePersonBiz.insertDtl(servePersonDtl);
	}

	@Override
	public int updateDtl(ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.updateDtl(servePersonDtl);
	}

	@Override
	public List<ServePersonDtl> selectDtlNotEqualId(ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.selectDtlNotEqualId(servePersonDtl);
	}

	@Override
	public int countHotel(ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.countHotel(servePersonDtl);
	}

	@Override
	public List<ServePersonDtl> selectHotel(PageParam pageParam, ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.selectHotel(pageParam,servePersonDtl);
	}

	/*@Override
	public ServePersonDtl selectAllPrice(Map<String, Object> map) {
		
		return servePersonBiz.selectAllPrice(map);
	}*/
	
	@Override
	public List<ServePersonDtl>  selectAllPrice(Map<String, Object> map) {
		
		return servePersonBiz.selectAllPrice(map);
	}

	@Override
	public List<ProjTrip> selectTrip(Integer projectId, Integer clientId) {
		
		return servePersonBiz.selectTrip(projectId,clientId);
	}

	@Override
	public int IsAllowTrip(ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.IsAllowTrip(servePersonDtl);
	}

	@Override
	public int countDtl(ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.countDtl(servePersonDtl);
	}

	@Override
	public List<ServePersonDtl> selectDtl(PageParam pageParam, ServePersonDtl servePersonDtl) {
		
		return servePersonBiz.selectDtl(pageParam,servePersonDtl);
	}

	@Override
	public int insertDtl(ServePersonDtl servePersonDtl, VisaDtlList visaDtlList) {
		
		return servePersonBiz.insertDtl(servePersonDtl,visaDtlList);
	}

	@Override
	public int updateDtl(ServePersonDtl servePersonDtl, VisaDtlList visaDtlList) {
		
		return servePersonBiz.updateDtl(servePersonDtl,visaDtlList);
	}

	


}
