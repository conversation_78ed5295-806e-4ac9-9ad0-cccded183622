package com.eip.facade.crm.service.impl;


import com.eip.common.entity.PageParam;
import com.eip.common.util.CollectionUtil;
import com.eip.common.util.ReflectionUtils;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.OrderQueryDto;
import com.eip.facade.crm.entity.Order;
import com.eip.facade.crm.service.OrderService;
import com.eip.facade.crm.vo.AmountVo;
import com.eip.service.crm.biz.OrderBiz;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Service("orderService")
public class OrderServiceImpl implements OrderService {

	@Autowired
	private OrderBiz orderBiz;

/*	@Override
	public Integer orderCheck(int orderId, int checkerId) {
		
		return orderBiz.orderCheck(orderId,checkerId);
		// TODO Auto-generated method stub
		
		
		
	}

	@Override
	public List<Order_dtl> selectDtlByProjectIdClientId(int projectId, int clientId) {
		return orderBiz.selectDtlByProjectIdClientId(projectId,clientId);
		// TODO Auto-generated method stub
		//return null;
	}*/

	@Override
	public int count(Map<String, Object> map) {
		return orderBiz.count(map);
	}

	@Override
	public int countAsync(Map<String, Object> map) {
		
		return count(map);
	}

	@Override
	public PageInfo<Order> getPage(PageParam pageParam, OrderQueryDto orderQueryDto) {
		String sort = pageParam.getSort();
		String order = pageParam.getOrder();
		String orderBy = "a.f_order_id desc";
		if(StringUtils.isNotBlank(sort)){
			if(sort.equals("boothNum")){
				orderBy = "sortBoothNum "+order;
			}else{
				 orderBy = ReflectionUtils.getOrderByConvert(Order.class, sort,"a.",order) ;
				if (!orderBy.contains("f_order_id"))
					orderBy = orderBy + ",a.f_order_id  desc";
			}
		}
		PageHelper.startPage(pageParam.getPage(),pageParam.getRows(),orderBy);
		return new PageInfo<>(orderBiz.select(orderQueryDto));
	}

	@Override
	public PageInfo<Order> getPageAsync(PageParam pageParam, OrderQueryDto orderQueryDto) {
		
		return getPage(pageParam,orderQueryDto);
	}

	@Override
	public Order getMoreById(Integer orderId) {
		
		return orderBiz.getMoreById(orderId);
	}

	@Override
	public AjaxResponse deleteBatch(List<Order> list, OperatorDto operator) {
		
		return orderBiz.deleteBatch(list,operator);
	}

	@Override
	public int check(Order order,OperatorDto operator) {
		
		return orderBiz.check(order,operator);
	}

	@Override
	public int update(Order order,OperatorDto operator) {
		
		return orderBiz.update(order,operator);
	}

	@Override
	public AjaxResponse insert(Order order, OperatorDto operator) {
		
		return orderBiz.insert(order,operator);
	}

	@Override
	public int delete(Integer orderId) {
		
		return orderBiz.delete(orderId);
	}

	@Override
	public BigDecimal sumAmountRmbAsync(OrderQueryDto orderQueryDto) {
		
		return orderBiz.sumAmountRmb(orderQueryDto);
	}

	@Override
	public Order selectById(Integer orderId) {
		return orderBiz.selectById(orderId);
	}

	@Override
	public List<AmountVo> sumAmountForeignAsync(OrderQueryDto orderQueryDto) {
		return orderBiz.sumAmountForeign(orderQueryDto);
	}

	@Override
	public Integer queryMaxId(OrderQueryDto orderQueryDto) {
		orderQueryDto.setQueryColumnInternal("max(a.f_order_id) f_order_id");
		List<Order> select = orderBiz.select(orderQueryDto);
		return CollectionUtil.isNotEmpty(select) ? select.get(0).getOrderId() : null;
	}

	@Override
	public List<Order> selectExhibitorSaleOrder(OrderQueryDto orderQueryDto) {
		return orderBiz.selectExhibitorSaleOrder(orderQueryDto);
	}

	@Override
	public AjaxResponse getPurchaseOwner(Integer projectId, Integer clientId) {
		return orderBiz.getPurchaseOwner(projectId, clientId);
	}
}
