package com.eip.facade.crm.service.impl;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.TaskDetail;
import com.eip.facade.crm.service.ExamineServe;
import com.eip.service.crm.biz.ExamineBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("examineServe")
public class ExamineServeImpl implements ExamineServe{
	
	@Autowired
	private ExamineBiz examineBiz;
	@Override
	public int examine(Boolean keyBool, Integer f_project_id, Integer sid, String serve_type) {
		return examineBiz.examine(keyBool, f_project_id, sid, serve_type);
	}
	@Override
	public int examine_reject(Integer pid, Integer sid, String reason, String serve_type) {
		// TODO Auto-generated method stub
		return examineBiz.examine_reject(pid, sid, reason, serve_type);
	}
	@Override
	public int count(<PERSON>olean key, String projectName, Integer projectState,  String taskKindCode) {
		return examineBiz.count(key, projectName, projectState, taskKindCode);
	}
	@Override
	public List<TaskDetail> select(PageParam pageParam, <PERSON><PERSON><PERSON> key, String projectName, Integer projectState, String taskKindCode, Integer confirm) {
		return examineBiz.select(pageParam, key, projectName, projectState, taskKindCode,confirm);
	}
	@Override
	public String makeTreeJson(String projectId) {
		return examineBiz.makeTreeJson(projectId);
	}
	
	@Override
	public int examineChangeConfirm(int taskDtlId, int confirm) {
		return examineBiz.examineChangeConfirm(taskDtlId, confirm);
	}

}
