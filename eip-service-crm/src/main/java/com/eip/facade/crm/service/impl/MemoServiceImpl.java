package com.eip.facade.crm.service.impl;

import com.eip.common.entity.PageParam;
import com.eip.common.service.impl.BaseServiceImpl;
import com.eip.common.util.ReflectionUtils;
import com.eip.facade.crm.entity.Memo;
import com.eip.facade.crm.service.MemoService;
import com.eip.service.crm.orgdao.MemoMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * @description: TODO
 * @author： hfl
 * @date：2021-07-14 16:30
 * @File: MemoServiceImpl
 */
@Service(value = "memoService")
public class MemoServiceImpl extends BaseServiceImpl<Memo> implements MemoService {

    @Autowired
    private MemoMapper memoMapper;

    @Override
    public Integer deleteMemo(Integer memoId) {
        return memoMapper.deleteMemo(memoId);
    }

    @Override
    public Integer deleteMemoById(Integer memoId) {
        return memoMapper.deleteMemoById(memoId);
    }

    @Override
    public Integer insertMemo(Memo memo) {
        return memoMapper.insertMemo(memo);
    }

    @Override
    public Integer updateMemo(Memo memo) {
        return memoMapper.updateMemo(memo);
    }
    @Override
    public PageInfo<Memo> findMemo(Memo memo, PageParam pageParam) {
        HashMap<String, Object> map = ReflectionUtils.getObjAttr(memo);
        PageHelper.startPage(pageParam.getPage(),pageParam.getRows());
        List<Memo> list = memoMapper.findMemo(map);
        return new PageInfo<>(list);
    }
}
