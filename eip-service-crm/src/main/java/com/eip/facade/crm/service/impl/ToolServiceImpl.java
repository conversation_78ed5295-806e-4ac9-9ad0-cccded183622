package com.eip.facade.crm.service.impl;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ToolDto;
import com.eip.facade.crm.entity.Tool;
import com.eip.facade.crm.service.ToolService;
import com.eip.service.crm.biz.ToolBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("toolService")
public class ToolServiceImpl implements ToolService {

	@Autowired
	private ToolBiz toolBiz;
	
	@Override
	public int count(ToolDto toolDto) {
		return toolBiz.count(toolDto);
	}

	@Override
	public List<Tool> select(PageParam pageParam, ToolDto toolDto) {
		return toolBiz.select(pageParam, toolDto);
	}

	@Override
	public int save(Tool tool, OperatorDto operatorDto) {
		return toolBiz.save(tool,operatorDto);
	}

	@Override
	public int insert(Tool tool) {
		return toolBiz.insert(tool);
	}

	@Override
	public int update(Tool tool) {
		return toolBiz.update(tool);
	}

	@Override
	public int delete(Integer toolId) {
		return toolBiz.delete(toolId);
	}

	@Override
	public AjaxResponse deleteBatch(List<Tool> list) {
		return toolBiz.deleteBatch(list);
	}

	@Override
	public int countInExhibit(ToolDto toolDto) {
		return toolBiz.countInExhibit(toolDto);
	}

	@Override
	public List<Tool> selectInExhibit(PageParam pageParam, ToolDto toolDto) {
		return toolBiz.selectInExhibit(pageParam, toolDto);
	}

	@Override
	public int countNotInExhibit(ToolDto toolDto) {
		return toolBiz.countNotInExhibit(toolDto);
	}

	@Override
	public List<Tool> selectNotInExhibit(PageParam pageParam, ToolDto toolDto) {
		return toolBiz.selectNotInExhibit(pageParam, toolDto);
	}

	@Override
	public int countInExhibitNotInBooth(ToolDto toolDto) {
		return toolBiz.countInExhibitNotInBooth(toolDto);
	}

	@Override
	public List<Tool> selectInExhibitNotInBooth(PageParam pageParam, ToolDto toolDto) {
		return toolBiz.selectInExhibitNotInBooth(pageParam,toolDto);
	}

}
