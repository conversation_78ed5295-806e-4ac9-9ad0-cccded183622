//package com.eip.task;
//
//import com.eip.facade.crm.service.BatchDtlService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//@Component
//public class BatchTempTask {
//
//    @Autowired
//    private BatchDtlService batchDtlService;
//
//    @Scheduled(cron = "0 30 0 * * ?")
//    private void delete(){
//        batchDtlService.deleteTempByTime();
//    }
//}
