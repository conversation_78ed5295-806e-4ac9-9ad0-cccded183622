//package com.eip.task;
//
//import cn.hutool.core.date.DateUtil;
//import com.eip.common.enums.OperateTypeEnum;
//import com.eip.common.util.GetDate;
//import com.eip.common.util.PublicUtil;
//import com.eip.facade.crm.dto.OperatorDto;
//import com.eip.facade.crm.entity.*;
//import com.eip.facade.crm.service.DataLimitService;
//import com.eip.facade.crm.service.EventInfoService;
//import com.eip.facade.crm.service.TeamClientService;
//import com.eip.facade.crm.service.WorkTeamService;
//import com.eip.service.crm.orgdao.ClientDao;
//import com.eip.service.crm.orgdao.CltRelationDao;
//import com.eip.service.crm.orgdao.OperatorDao;
//import com.eip.service.crm.orgdao.OpportunityDao;
//import com.eip.util.ClearClientNameKeywordsUtils;
//import com.google.common.collect.Maps;
//
//import org.apache.zookeeper.Op;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import tk.mybatis.mapper.entity.Example;
//
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * @author: ZzzHhYyy
// * @Date: 2020-02-10 10:03
// */
//@Component
//public class ClientTask {
//    private Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Autowired
//    private ClientDao clientDao;
//    @Autowired
//    private TeamClientService teamClientService;
//    @Autowired
//    private OperatorDao speratorDao;
//    @Autowired
//    private EventInfoService eventInfoService;
//    @Autowired
//    private WorkTeamService workTeamService;
//    @Autowired
//    private OpportunityDao opportunityDao;
//    @Autowired
//    private CltRelationDao cltRelationDao;
//    @Autowired
//	private DataLimitService dataLimitService;
//
//    /**
//     * 更新到期的客户为公共客户
//     * 每天0点05分运行
//     */
//    @Scheduled(cron = "0 5 0 * * ?")
//    public void updateClientStates() {
//        logger.info("------------更新到期的客户为公共客户stater----------------");
//
//        updateClientState();
//
//        /*Example example = new Example(TeamClient.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andNotEqualTo("authStatus", 0);
//        criteria.andLessThanOrEqualTo("protectionTime", new Date());
//        List<TeamClient> list = teamClientService.selectByExample(example);
//        Date now = DateUtil.parseDate(DateUtil.today());
//        for (TeamClient tc : list) {
//            if (tc.getProtectionDay() == null) continue;
//            Date protectionTime = tc.getProtectionTime();
//            Date date = GetDate.addDateDate(protectionTime, tc.getProtectionDay());
//            //过期的保护状态
//            if (now.after(date)) {
//            	try {
//            		//写入日志
//                	String authName = "撤销"+PublicUtil.getStringNameByAuth(tc.getAuthStatus());
//    				String operateMemo = "到期系统自动" + authName +"|";
//
//					//查询团队名称
//					WorkTeam team = workTeamService.getById(tc.getfWorkteamId());
//					if(team!=null&&team.getfWorkTeamName()!=null){
//						operateMemo += team.getfWorkTeamName()+"|" ;
//					}
//					String sale = "";
//					if(tc.getSalesmanId()!=null){
//						sale = tc.getSalesmanId().toString();
//    					String salesName = speratorDao.getEmpNameByOperId(tc.getSalesmanId());
//    					if(salesName!=null)operateMemo += salesName+"|" ;
//    				}
//					operateMemo += tc.getfWorkteamId()+","+sale+"|" ;
//    				Integer evenKind = PublicUtil.getEvenKindByStringName(tc.getfClientTypeCode());
//    				eventInfoService.saveEvent(0, operateMemo, evenKind, 0,
//    						tc.getfOrgNum(), authName, tc.getfClientId().toString());
//
//
//                	 //撤销保护
//                	tc.setProtectionDay(null);
//                	tc.setProtectionTime(null);
//                	tc.setSalesmanId(null);
//                	tc.setAuthStatus(0);
//                	teamClientService.updateTrue(tc);
//
//
//
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//
//
//
//            }
//        }*/
//
//
//
//        logger.info("------------更新到期的客户为公共客户end----------------");
//    }
//    public void updateClientState(){
//    	Date now = DateUtil.parseDate(DateUtil.today());
//    	  //先处理公司客户
//    	List<TeamClient> list = queryTeamClient("B_CLIENT");
//        for (TeamClient tc : list) {
//            if (tc.getProtectionDay() == null||tc.getProtectionTime()==null) continue;
//            Date protectionTime = tc.getProtectionTime();
//            Date date = GetDate.addDateDate(protectionTime, tc.getProtectionDay());
//            //date = DateUtil.parseDate(DateUtil.format(date, "yyyy-MM-dd"));
//			Integer oldProtectionDay = tc.getProtectionDay();
//			//过期的保护状态
//            if (now.after(date)) {
//            	//先查询是否有保护的商机
//            	if(tc.getSalesmanId()!=null){
//            		Map<String,Object> map = Maps.newHashMap();
//                	map.put("clientId", tc.getfClientId());
//    				map.put("protected", true);
//    				map.put("operatorId", tc.getSalesmanId());
//    				map.put("workTeamId", tc.getfWorkteamId());
//    				List<Opportunity> oppList = opportunityDao.selectBase(map);
//    				if(oppList!=null&&oppList.size()>0){
//    					Opportunity opp = oppList.get(0);
//    					if(opp.getProtectDays()!=null&&opp.getProtectDate()!=null){
//    						Date protectDate = opp.getProtectDate();
//    						protectDate = DateUtil.parseDate(DateUtil.format(protectDate, "yyyy-MM-dd"));
//    			            Date date2 = GetDate.addDateDate(protectDate, opp.getProtectDays());
//    			            //date2 = DateUtil.parseDate(DateUtil.format(date2, "yyyy-MM-dd"));
//    			            if (!now.after(date2)) { //还在保护中 不清除 客户的保护状态
//    			            	long day = DateUtil.betweenDay(protectionTime, date2, true);
//    			            	tc.setProtectionDay(new Long(day).intValue());
//    			            	//修改授权
//    			            	try {
//    			            		updateClientAndLinkmanAuth(tc,oldProtectionDay);
//								} catch (Exception e) {
//									e.printStackTrace();
//								}
//    			            	continue;
//    			            }
//    					}else{
//    						//不清除客户的保护状态
//    						tc.setProtectionDay(null);
//    						//修改授权
//    						try {
//    							updateClientAndLinkmanAuth(tc,oldProtectionDay);
//							} catch (Exception e) {
//								e.printStackTrace();
//							}
//			            	continue;
//    					}
//    				}
//            	}
//            	revocationProtect(tc);//撤销保护
//
//            }
//        }
//        //再处理 联系人
//        List<TeamClient> linkman = queryTeamClient("C_LINKMAN");
//        for (TeamClient tc : linkman) {
//            if (tc.getProtectionDay() == null||tc.getProtectionTime()==null) continue;
//            Date protectionTime = tc.getProtectionTime();
//            Date date = GetDate.addDateDate(protectionTime, tc.getProtectionDay());
//            //过期的保护状态
//            if (now.after(date)) {
//            	revocationProtect(tc);//撤销保护
//            }
//        }
//    }
//
//    /**
//     * 查询团队客户授权数据
//     * @param clientTypeCode
//     * @return
//     */
//    public List<TeamClient> queryTeamClient(String clientTypeCode){
//  	  	Example example = new Example(TeamClient.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andNotEqualTo("authStatus", 0);
//        criteria.andLessThanOrEqualTo("protectionTime", new Date());
//        criteria.andEqualTo("fClientTypeCode", clientTypeCode);
//        criteria.andIsNotNull("protectionDay");
//        criteria.andCondition("(f_protection_time + f_protection_day -1)<now()");
//        List<TeamClient> list = teamClientService.selectByExample(example);
//        return list;
//  }
//
//   /**
//    * 撤销保护
//    * @param tc
//    */
//   public void revocationProtect(TeamClient tc){
//		 //写入日志
//	    //查询客户名称
//	   String name = "";
//	   Client client = clientDao.selectByPrimaryKey(tc.getfClientId());
//	   if(client!=null){
//		   if("B_CLIENT".equals(client.getClientTypeCode())
//				   &&client.getCompanyName()!=null)
//			   name = client.getCompanyName();
//		   else if("C_LINKMAN".equals(client.getClientTypeCode())
//				   &&client.getPersonName()!=null)
//			   name = client.getPersonName();
//	   }
//
//	   //	String authName = "撤销"+PublicUtil.getStringNameByAuth(tc.getAuthStatus());
//		//String operateMemo = name + "到期系统自动" + authName +"|";
//		String operateType = null;
//		if(tc.getAuthStatus()==1)operateType = OperateTypeEnum.CANCEL_RECEIVE.getName();
//	    else if(tc.getAuthStatus()==3)operateType = OperateTypeEnum.CANCEL_MONOPOLIZE.getName();
//
//	   StringBuffer sbf = new StringBuffer();
//	   sbf.append("到期系统自动撤销").append(PublicUtil.getStringNameByAuth(tc.getAuthStatus()));
//
//		//查询团队名称
//		WorkTeam team = workTeamService.getById(tc.getfWorkteamId());
//		if(team!=null&&team.getfWorkTeamName()!=null){
//			sbf.append("，所在团队：").append(team.getfWorkTeamName());
//		}
//		String sale = "";
//		if(tc.getSalesmanId()!=null){
//			sale = tc.getSalesmanId().toString();
//			String salesName = speratorDao.getEmpNameByOperId(tc.getSalesmanId());
//			if(salesName!=null)sbf.append("，业务员：").append(salesName);;
//		}
//	   sbf.append("|").append(tc.getfWorkteamId()).append(",").append(sale).append("|");
//		//operateMemo += tc.getfWorkteamId()+","+sale+"|" ;
//		Integer evenKind = PublicUtil.getEvenKindByStringName(tc.getfClientTypeCode());
//
//		eventInfoService.save(0, sbf.toString(), evenKind, 0,
//				tc.getfOrgNum(), operateType, tc.getfClientId().toString(),name,null);
//
//	   	//撤销保护
//	   	tc.setProtectionDay(null);
//	   	tc.setProtectionTime(null);
//	   	tc.setSalesmanId(null);
//	   	tc.setAuthStatus(0);
//	   	teamClientService.updateTrue(tc);
//	    //删除分享
//	    OperatorDto operator = new OperatorDto();
//	    operator.setOperatorId(0);
//	    dataLimitService.delShare(tc.getfTeamCltId(),operator);
//   }
//   /**
//    * 修改公司客户独占天数
//    * @param tc
//    */
//   public void updateClientAndLinkmanAuth(TeamClient tc,Integer oldProtectionDay){
//	   updateAuth(tc,oldProtectionDay);
//	   //查询公司客户下的联系人(当前团队 这个业务员的联系人)
//	   Map<String,Object> map = Maps.newHashMap();
//	   map.put("relationCode", "BtoC1");
//	   map.put("clientId", tc.getfClientId());
//	   map.put("stopped", false);
//	   List<CltRelation> list = cltRelationDao.select(map);
//	   if(list!=null&&list.size()>0){
//		    List<Integer> ids = list.stream().map(CltRelation::getRelationClientId).collect(Collectors.toList());
//			Example example = new Example(TeamClient.class);
//	        Example.Criteria criteria = example.createCriteria();
//	        //criteria.andNotEqualTo("authStatus", 0);
//	        criteria.andEqualTo("authStatus", tc.getAuthStatus());// 只更新和公司客户 一致的授权
//	        criteria.andIn("fClientId", ids);
//	        criteria.andEqualTo("fWorkteamId", tc.getfWorkteamId());
//	        criteria.andEqualTo("salesmanId", tc.getSalesmanId());
//	        criteria.andIsNotNull("protectionDay");
//	        List<TeamClient> teamClientList = teamClientService.selectByExample(example);
//	        if(teamClientList==null||teamClientList.size()==0)return;
//	        //如果有联系人 修改联系人的授权
//	        //所属客户的 授权时间、
//
//            //date = DateUtil.parseDate(DateUtil.format(date, "yyyy-MM-dd"));
//        	for(TeamClient bean :teamClientList){
//				Integer protectionDay = bean.getProtectionDay();
//				if(tc.getProtectionDay()==null){
//        			bean.setProtectionDay(null);
//        			updateAuth(bean,protectionDay);
//        		}else{
//        			Date protectionTime = tc.getProtectionTime();
//                    Date date = GetDate.addDateDate(protectionTime, tc.getProtectionDay());
//        			//判断当前联系人的 授权天数是否 小于 所属客户的授权天数
//                    //当前联系人的授权时间
//        			Date protectionTime2 = bean.getProtectionTime();
//                    Date date2 = GetDate.addDateDate(protectionTime2, bean.getProtectionDay());
//                    //date2 = DateUtil.parseDate(DateUtil.format(date2, "yyyy-MM-dd"));
//                    if(date2.before(date)){
//                    	long day = DateUtil.betweenDay(protectionTime2, date, true);
//                    	bean.setProtectionDay(new Long(day).intValue());
//                    	updateAuth(bean,protectionDay);
//                    }
//        		}
//        	}
//
//	   }
//   }
//   /**
//    * 修改独占天数
//    * @param tc
//    */
//   public void updateAuth(TeamClient tc,Integer oldProtectionDay){
//	   teamClientService.updateTrue(tc);
//	   //写入日志
//	   String name = "";
//	   Client client = clientDao.selectByPrimaryKey(tc.getfClientId());
//	   if(client!=null){
//		   if("B_CLIENT".equals(client.getClientTypeCode())
//				   &&client.getCompanyName()!=null)
//			   name = client.getCompanyName();
//		   else if("C_LINKMAN".equals(client.getClientTypeCode())
//				   &&client.getPersonName()!=null)
//			   name = client.getPersonName();
//	   }
//	   String operateType = null;
//	   if(tc.getAuthStatus()==1)operateType = OperateTypeEnum.MODIFY_RECEIVE.getName();
//	   else if(tc.getAuthStatus()==3)operateType = OperateTypeEnum.MODIFY_MONOPOLIZE.getName();
//
//	   StringBuffer sbf = new StringBuffer();
//	   sbf.append("到期系统自动根据商机修改").append(PublicUtil.getStringNameByAuth(tc.getAuthStatus()))
//			   .append("天数，由").append(oldProtectionDay==null?"空":(oldProtectionDay+"天"))
//			   .append("修改为").append(tc.getProtectionDay()==null?"空":(tc.getProtectionDay()+"天"));
//		//查询团队名称
//		WorkTeam team = workTeamService.getById(tc.getfWorkteamId());
//		if(team!=null&&team.getfWorkTeamName()!=null){
//			sbf.append("，所在团队：").append(team.getfWorkTeamName());
//		}
//		//String sale = "";
//		if(tc.getSalesmanId()!=null){
//			//sale = tc.getSalesmanId().toString();
//			String salesName = speratorDao.getEmpNameByOperId(tc.getSalesmanId());
//			if(salesName!=null)sbf.append("，业务员：").append(salesName);
//		}
//		//operateMemo += "|"+ tc.getfWorkteamId()+","+sale+"|" ;
//		Integer evenKind = PublicUtil.getEvenKindByStringName(tc.getfClientTypeCode());
//		/*try {
//			eventInfoService.saveEvent(0, operateMemo, evenKind, 0,
//					tc.getfOrgNum(), operateType, tc.getfClientId().toString(),operObjName);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}*/
//	   eventInfoService.save(0, sbf.toString(), evenKind, 0,
//			   tc.getfOrgNum(), operateType, tc.getfClientId().toString(),name,null);
//   }
//
//
//   public void createCompanyKeyName(){
//   	   Map<String,Object> map = Maps.newHashMap();
//   	   map.put("clientTypeCode","B_CLIENT");
//   	   map.put("companyKeyNameIsBlank",true);
//	   map.put("companyNameIsNotBlank",true);
//	   map.put("queryColumn","a.f_client_id,a.f_company_name");
//   	   map.put("limit",1000);
//	   List<Client> list = clientDao.selectBase(map);
//	   for(Client bean:list){
//		   String companyKeyName = ClearClientNameKeywordsUtils.clearClientNameKeywords(bean.getCompanyName(), 2);
//		   bean.setCompanyKeyName(companyKeyName);
//		   clientDao.updateByPrimaryKeySelective(bean);
//	   }
//   }
//
//	/**
//	 * 定时创建公司名称关键词
//	 * 暂定10分钟一次
//	 */
//	@Scheduled(cron = "0 */10 * * * ?")
//	public void createCompanyKeyNameTask(){
//		createCompanyKeyName();
//	}
//}
