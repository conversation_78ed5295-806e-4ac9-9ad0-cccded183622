//package com.eip.task;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import com.eip.common.enums.EventKindEnum;
//import com.eip.common.enums.OperateTypeEnum;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import com.eip.common.util.GetDate;
//import com.eip.common.util.PublicUtil;
//import com.eip.facade.crm.entity.Opportunity;
//import com.eip.facade.crm.entity.TeamClient;
//import com.eip.facade.crm.entity.WorkTeam;
//import com.eip.facade.crm.service.EventInfoService;
//import com.eip.service.crm.orgdao.OpportunityDao;
//
//import cn.hutool.core.date.DateUtil;
//
///**
// * 定时清理商机保护
// *
// */
//@Component
//public class OpportunityTask {
//
//	private Logger logger = LoggerFactory.getLogger(this.getClass());
//
//	@Autowired
//	private OpportunityDao dao;
//	@Autowired
//    private EventInfoService eventInfoService;
//	/**
//     * 每天0点01分运行
//     */
//
//	//@Scheduled(cron = "0 */1 * * * ?")
//    @Scheduled(cron = "0 1 0 * * ?")
//	public void clearProtect(){
//		logger.info("------------更新到期的商机start----------------");
//    	Map<String,Object> map = new HashMap<>();
//		map.put("protected", true);
//		map.put("leProtectDate", new Date());
//		List<Opportunity> list = dao.select(map);
//		Date now = DateUtil.parseDate(DateUtil.today());
//		if(list!=null&&list.size()>0){
//			for (Opportunity opp : list) {
//	            if (opp.getProtectDays() == null||opp.getProtectDate()==null) continue;
//	            Date protectionTime = opp.getProtectDate();
//	            Date date = GetDate.addDateDate(protectionTime, opp.getProtectDays());
//	            //过期的保护状态
//	            if (now.after(date)) {
//	            	try {
//	            		opp.setFprotected(null);
//	            		opp.setProtectDate(null);;
//	            		opp.setProtectDays(null);
//	            		opp.setCancelDate(new Date());
//	            		int i = dao.update(opp);
//	            		//日志
//	            		if(i>0){
//							StringBuffer sbf = new StringBuffer();
//							sbf.append(opp.getOppTheme() == null ? "" : opp.getOppTheme()).append(" ")
//									.append(opp.getCompanyName() == null ? "" : opp.getCompanyName()).append(" ")
//									.append(opp.getProjectName() == null ? "" : opp.getProjectName());
//	            			eventInfoService.save(0, "到期系统自动撤销保护", EventKindEnum.OPPORTUNITY.getId(), 0,
//		    						opp.getOrgNum(), OperateTypeEnum.CANCEL_PROTECT.getName(), opp.getOpportunityId().toString(),sbf.toString(),null);
//	            		}
//
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//
//	            }
//	        }
//
//
//		}
//
//	logger.info("------------更新到期的商机end----------------");
//	}
//}
