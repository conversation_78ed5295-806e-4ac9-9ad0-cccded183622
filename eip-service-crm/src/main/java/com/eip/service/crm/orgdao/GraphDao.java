package com.eip.service.crm.orgdao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.eip.facade.crm.entity.Graph;

public interface GraphDao {

	List<Graph> selectBySectionMapId(int sectionMapId);
	
	int insert(List<Graph> list);
	
	int update(Graph graph);
	
	int deleteByGraphId(int graphId);
	
	int updateCode(@Param("graphId") int graphId, @Param("code") String code);

	List<Graph> selectWithServeBooth(int sectionMapId);
	
}
