package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.Tags;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-08-10 17:28
 */
@Mapper
public interface TagsDao extends MyMapper<Tags> {

    List<Tags> getAll(Map<String, Object> map);

    List<Tags> getShared(Map<String, Object> map);

    List<Long> getIdByNameGroupByName(Map<String, Object> map);

    List<Tags> selectClientNum(@Param("ids") List<Long> ids, @Param("clientTypeCode") String clientTypeCode);
}
