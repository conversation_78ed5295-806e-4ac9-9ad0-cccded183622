package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.Task;
import com.eip.facade.crm.entity.TaskCountA;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TaskDao extends MyMapper<Task>{

	int count(Map<String, Object> paramMap);
	
	List<Task> selectByMap(Map<String, Object> paramMap);
	
	//int insert(Task task);
	
	int update(Task task);
	
	//int delete(Integer taskId);
	
	int deleteBatch(List<Task> list);
	
	List<TaskCountA> countGroupByTaskKindCode(@Param("projectId") Integer projectId);
	
	List<Task> selectByProjectId(@Param("projectId") Integer projectId);

	List<Task> selectByProjectIdOrderBy(@Param("projectId") Integer projectId);
	
	Task selectByTaskId(@Param("taskId") Integer taskId);
	
	Task selectByProjectIdAndTaskKindCode(@Param("projectId") int projectId, @Param("taskKindCode") String taskKindCode);
	
	int deleteByProjectId(int projectId);
	
	List<Task> selectByProjectId2(Integer projectId);
	
	List<Task> selectByProjectId2AndTaskKindCode(@Param("projectId") int projectId, @Param("taskKindCode") String taskKindCode);
	
	List<Task> selectTaskKindNameByProjectId(String projectId);

	int countCRM(Map<String, Object> paramMap);

	List<Task> selectCRM(Map<String, Object> paramMap);
	
	int updateCRM(Task task);

	void updateChild(Map<String, Object> map);

	List<Task> getTaskByClient(@Param("clientId")Integer clientId);

	Integer selectParentTaskId(@Param("taskId")Integer taskId);

	List<Integer> selectChildTaskId(@Param("taskId")Integer taskId);

	/**
	 * 根据任务Id  查询并统计任务关联数完成数量
	 * @param paramMap
	 * @return
	 */
	List<Task> selectCountTaskStatuByTaskId(Map<String, Object> paramMap);

	/**
	 * 查询项目任务
	 * @param paramMap
	 * @return
	 */
	List<Task> selectProjectTask(Map<String, Object> paramMap);

    int deleteTaskByExhibitCode(@Param("exhibitCode") String exhibitCode);

	List<String> selectTaskKindCode(@Param("projectIds") List<Integer> projectIds);
}
