package com.eip.service.crm.orgdao;

import com.eip.common.entity.Statistics;
import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.dto.ClientDelDto;
import com.eip.facade.crm.dto.ClientDistributionDto;
import com.eip.facade.crm.dto.OperateClientDto;
import com.eip.facade.crm.dto.SummaryDTO;
import com.eip.facade.crm.entity.Client;
import com.eip.facade.crm.entity.Memo;
import com.eip.facade.crm.entity.Summary;
import com.eip.facade.crm.entity.TradeClass;
import com.eip.facade.crm.vo.AreaVo;
import com.eip.facade.crm.vo.ClientCountdownVo;
import com.eip.facade.crm.vo.RepeatClientVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ClientDao extends MyMapper<Client> {

	int countTrader(Map<String, Object> paramMap);
	
	List<Client> selectTrader(Map<String, Object> paramMap);
	
	List<Client> selectAllTrader(Map<String, Object> paramMap);
	
	int insertTrader(Client client);
	
	int updateTrader(Client client);
	
	//展会合同选择客户
	List<Client> SelectClient(Map<String, Object> paramMap);
	
	List<Client> selectByExhibitCode(Map<String, Object> paramMap);
	
	int countByExhibitCode(Map<String, Object> paramMap);

	List<Client> selectByMap(Map<String, Object> map);

	int updateCompanyAbbr(Client client);

	Client selectById(@Param("clientId") Integer clientId);

	int count(Map<String, Object> paramMap);

	List<Client> selectListByMap(Map<String, Object> paramMap);

//	int insert(Client client);

	int update(Client client);

	List<Client> getByClientId(Client client);

	int updateCustomizedJson(Client client);

	List<Client> selectCountry(Map<String, Object> map);

	List<Client> selectProvince(Map<String, Object> map);

	List<Client> selectCity(Map<String, Object> map);

	List<Client> selectDistrict(Map<String, Object> map);

	List<String> getClientByProvince(Map<String, Object> map);
	
	int updateDel(@Param("clientId")int clientId,@Param("del")int del,@Param("updateTime")Date updateTime,
			@Param("delMemo")String delMemo,@Param("delOper")Integer delOper, @Param("cltName")String cltName);
	
	int updateSelective(Client client);
	
	int updateRelevance(Client client);

	List<String> selectByParent(String relationshId);

	List<Statistics> distribution(ClientDistributionDto clientDistributionDto);

    List<Client> queryIsExistByClientNameAndTeam(@Param("cltName") String cltName,@Param("orgNum")  Integer orgNum, @Param("companyKeyNameUp") String companyKeyNameUp,
												 @Param("workteamId") Integer workteamId, @Param("check") Integer check, @Param("notClientId") Integer notClientId);

	List<Client> getLinkman(Map<String, Object> paramMap);

    List<Integer> selectEmailOrWebsiteOrTelIsRepeat(@Param("keyWords")String keyWords,@Param("orgNum")Integer orgNum);

	int delCardImg(@Param("clientId")Integer clientId);

	List<Integer> selectClientIdByMap(Map<String, Object> paramMap);

	List<String> getClientProvinceByClientIds(@Param("province")String province,@Param("longs") List<Long> longs);

	List<Client> queryOperateClient(OperateClientDto operateClientDto);


	Integer countRemind(Integer id);

	List<Memo> findMemoNoRemind(Map<String, Object> map);

	Integer countNoRemind(Integer id);

	List<Memo> findMemoByContentRemind(Map<String, Object> map);

	Integer countContentRemind(Map<String, Object> map);

	List<Memo> findMemoByContentNoRemind(Map<String, Object> map);

	Integer countContentNoRemind(Map<String, Object> map);

/*	List<Summary> selectDaySummary(Map<String, Object> map);

	Integer countDaySummary(@Param("operatorId") Integer operatorId,@Param("state")Integer state);*/

	List<Summary> selectSummary(Map<String, Object> map);

	Integer countSummary(SummaryDTO summaryDTO);

	Integer updateSummaryDel(@Param("summaryId") Integer summaryId);

	Integer insertSummary(Summary summary);

	Integer deleteBySummaryId(@Param("summaryId") Integer summaryId);

	Summary findSummaryById(Map<String, Object> hashMap);

	Integer updateSummary(Summary summary);
	
	String selectCompanyNameById(@Param("clientId")Integer clientId);

	List<Client> selectClientByRelation(Map<String, Object> map);

	Client selectBaseById(@Param("id")Integer clientId);

	List<Client> queryIsAllowDel(ClientDelDto clientDelDto);

	List<Client> getClientAndTeamByClientIds(@Param("clientIds")Integer[] clientIds);

	int selectCountByMap(Map<String, Object> paramMap);

	List<Client> getMyClient(HashMap<String, Object> map);

	List<ClientCountdownVo> getClientCountdown(Map<String, Object> map);
	
	@Select("SELECT update_clientId(#{clientId,jdbcType=INTEGER}, #{clientIds,jdbcType=VARCHAR})")
	int updateClientToNewClient(@Param("clientId")int clientId,@Param("clientIds") String clientIds);
	
	@Select("SELECT update_projcltId(#{projCltId,jdbcType=INTEGER}, #{projCltIds,jdbcType=VARCHAR})")
	int updateProjCltIdToNewProjCltId(@Param("projCltId")Integer projCltId, @Param("projCltIds")String projCltIds);

	List<Client> queryIsExistByClientNameAndKeyName(@Param("companyName") String companyName,@Param("orgNum")  Integer orgNum, @Param("companyKeyName") String companyKeyName,
                                                 @Param("workteamId") Integer workteamId);

	List<RepeatClientVo> queryRepeatClient(Map<String, Object> map);

	List<RepeatClientVo> queryRepeatClientByLinkman(Map<String, Object> map);

	List<String> getSpitValue(@Param("field")String field, @Param("clientIds")String clientIds);

	List<Map<String, Object>> companyNameLike(Map<String, Object> map);

	Client selectByCompanyNameAndProject(@Param("companyName")String companyName, @Param("projectId")Integer projectId);

	int batchInsert(List<Client> list);

	List<Client> selectBase(Map<String, Object> map);

	List<Summary> selectSummaryByList(Map<String, Object> paramMap);

    List<Summary> selectSummarBySumId(@Param(value = "summaryIds") List<Integer> summaryIds);

	/**
	 * 创建临时表
	 * @param tempTable
	 * @param querySql
	 */
	void createTempTable(@Param("tempTable") String tempTable, @Param("querySql") String querySql);

	/**
	 * 删除临时表
	 * @param tempTable
	 */
	void delTempTable(@Param("tempTable") String tempTable);

	/**
	 * 根据表名返回查询的字段
	 */
	List<Long> selectQuerySql(@Param("tempTable") String tempTable);


	List<Map<String, Object>> queryRepeatLinkmanByCompanyTemp(Map<String, Object> map);

	List<RepeatClientVo> queryRepeatLinkmanByCompany(Map<String, Object> map);

	List<RepeatClientVo> queryRepeatLinkmanByMobile(Map<String, Object> map);

    List<Statistics> distributionTrade(ClientDistributionDto client);

	List<TradeClass> tradeCount(Map<String, Object> map);

    String getNameById(@Param("clientId") Integer clientId);

	/**
	 * 根据联系人姓名 + 联系方式（手机、邮箱、QQ、微信）排重
	 * @param map
	 * @return
	 */
    List<RepeatClientVo> getRepeatLinkmanByNameAndContactWay(Map<String, Object> map);

	/**
	 * 查询联系人的所属公司和团队 数据
	 * @param map
	 * @return
	 */
	List<RepeatClientVo> selectTeamAndParentCompany(Map<String, Object> map);

	int getRepeatLinkmanCountByNameAndContactWay(Map<String, Object> map);

	/**
	 * 查询公司客户数据
	 * @param map
	 * @return
	 */
	List<Client> queryClient(Map<String, Object> map);

    int updateZzyUserId(Map<String, Object> hashMap);

	Client queryClientByCondition(Map<String, Object> queryClientMap);

    List<AreaVo> groupCountry(Map<String,Object> map);

    int resetZzyUserIdToNull(Map<String, Object> paramMap);

	int resetZzyUserIdToNullByClientId(Map<String, Object> hashMap);

	int resetZzyUserIdToNullByLinkmanId(Map<String, Object> hashMap);

	int updateClientDelState(@Param("clientId")int clientId,@Param("del")int del,@Param("updateTime")Date updateTime);

	int updateLinkmanCltNameById(@Param("clientId")Integer clientId);

	List<Map<String, Object>> queryRepeatClientByCompanyNameAndAliasTemp(Map<String, Object> map);

	List<Map<String, Object>> queryRepeatClientByCompanyNameAndAliasFieldTemp(Map<String, Object> map);

	List<RepeatClientVo> queryRepeatClientByCompanyNameAndAlias(Map<String, Object> map);

	List<Client> queryRivalClientCount(@Param("clientIds") List<Integer> clientIds, @Param("workTeamId") Integer workTeamId);

	int updateBirthday(@Param("clientId") Integer clientId, @Param("birthday") Date birthday);

	int updateEnterpriseIdFieldInClientTableToNull(@Param("clientId") Integer clientId, @Param("enterpriseId") Long enterpriseId);

	int updateBindEnterpriseIdInClientTable(@Param("clientId") Integer clientId, @Param("enterpriseId") Long enterpriseId);

	int updateCompanyNameById(@Param("companyName") String companyName, @Param("clientId") Integer clientId);
}
