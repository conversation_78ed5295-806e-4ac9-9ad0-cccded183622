package com.eip.service.crm.orgdao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.eip.facade.crm.vo.AmountVo;
import com.eip.facade.crm.vo.ProjClientServeBoothVo;
import com.eip.facade.crm.vo.ProjectSalesAccountVo;
import org.apache.ibatis.annotations.Param;

import com.eip.common.entity.Statistics;
import com.eip.facade.crm.entity.Order;

public interface OrderDao {
	
	/*int insert(Order order);*/

	List<Order> selectBase(Map<String,Object> map);
	
	List<Order> select(Map<String,Object> map);
	
	int count(Map<String,Object> map);


	int updateClientToNewClient(@Param("oldClientId")Integer oldClientId, @Param("newClientId")Integer newClientId);


	Statistics orderStatisticsByWeek(@Param("startDate")Date startDate, @Param("operatorIds")List<Integer> operatorIds,
									 @Param("workTeamId")Integer workTeamId,@Param("orgNum")Integer orgNum);

	List<Order> getList(Map<String, Object> map);

	Order selectById(@Param("orderId")Integer orderId);

	int deleteBatch(List<Order> list);

	int check(Order order);

	int update(Order order);

	int insert(Order order);

	int delete(@Param("orderId")Integer orderId);

	BigDecimal sumAmountRmb(Map<String, Object> map);

	int updateContractIdToNull(@Param("contractId")Integer contractId);

	int updateProjCltId(@Param("projCltId")Integer projCltId, @Param("projectId")Integer projectId, @Param("clientId")Integer clientId, @Param("orderTypeCode")String orderTypeCode);

    List<AmountVo> sumAmountForeign(Map<String, Object> map);

	int updateCompanyNameById(@Param("companyName")String companyName, @Param("orderId")Integer orderId);

	List<Order> getOwner(Map<String, Object> map);
}
