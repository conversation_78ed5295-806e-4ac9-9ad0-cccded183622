package com.eip.service.crm.orgdao;

import com.eip.facade.crm.entity.Project;
import com.eip.facade.crm.vo.AreaVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface ProjectDao {

	int count(Map<String, Object> paramMap);
	
	List<Project> select(Map<String, Object> paramMap);
	
	List<Project> selectAll(Integer f_org_num);
	
	int insert(Project project);
	
	int update(Project project);
	
	int deleteBatch(List<Project> list);
	
	Project selectByProjectId(@Param("projectId") Integer projectId, @Param("f_org_num") Integer f_org_num);
	
	List<Project> selectByExhibitCode(@Param("exhibitCode") String exhibitCode, @Param("f_org_num") Integer f_org_num);
	
	int recentOperation(Map<String, Object> paramMap);

	int recentOperationUpdate(Map<String, Object> paramMap);

	int recentOperationSelect(Map<String, Object> paramMap);

	List<Project> recentProjectSelect(Map<String, Object> paramMap);

	int countRecentProjectSelect(Map<String, Object> paramMap);

	int countByMap(Map<String, Object> paramMap);

	List<Project> selectByMap(Map<String, Object> paramMap);
	
	Project selectById(@Param("projectId") Integer projectId,@Param("f_org_num") Integer f_org_num);
	
	List<Project> selectAllByExhibitCode(@Param("exhibitCode") String exhibitCode);
	
	List<Project> selectMenuSettingByProjectId(Project project);
	
	int updateMenuSettingByProjectId(Project project);

	int updateExhibitCodeByProjectId(Project project);

    void deleteExhibitCode(@Param("exhibitCode") String exhibitCode);

	int updateProjectName(@Param("projectId")Integer projectId, @Param("projectName")String projectName);
	
	List<Project> selectYear(Map<String, Object> map);

	int batchUpdateTop(@Param("ids")List<Integer> ids, @Param("isTop")boolean b);

	List<AreaVo> selectCountry(Map<String, Object> map);
	
	List<String> selectCityOrProvince(Map<String, Object> map);

	Project selectByProjectName(@Param("projectName") String projectName,@Param("orgNum") Integer orgNum);

	int selectCount(@Param("projectName") String projectName,@Param("orgNum") Integer orgNum);

	Integer getOppProtectNum(Integer projectId);

    Project selectByExhibitionEndDate(Map<String, Object> map);

	int delete(@Param("projectId")Integer projectId);

    List<Project> selectByParentId(@Param("parentId")Integer parentId);

	Project selectParentAndMainProjectId(@Param("projectId")Integer projectId);

	int updateParentId(@Param("projectId")Integer projectId, @Param("parentId")Integer parentId);

    Integer selectParentId(@Param("projectId")Integer projectId);

    String selectNameById(@Param("projectId")Integer projectId);

    List<Integer> queryMainChildProject(Map<String, Object> paramMap);

	int checkProject(Map<String, Object> paramMap);

	Project getProjectById(Map<String, Object> paramMap);

	Boolean queryRegEnglish(@Param("projectId")Integer projectId);

	/**
	 * 根据展会code查询项目ID
	 * @param paramMap
	 * @return
	 */
	List<Integer> getExhibitionRefProjectId(Map<String, Object> paramMap);

	Integer queryArchiveState(@Param("projectId")Integer projectId);

	int updateArchiveState(Project project);

	@Select("select check_project_exist_business_relation(#{projectId})")
    int countProjectExistBusinessRelation(@Param("projectId") Integer projectId);

    Integer getProjectIdByProjectName(@Param("projectName") String projectName, @Param("orgNum") Integer orgNum);

	int countExistRepeatProjectName(@Param("projectName") String projectName,  @Param("orgNum") Integer orgNum);

	int saveAccountPeriod(Project project);

	String getProjectAbbrById(@Param("projectId")Integer projectId);

	int updateContractOrOrderRefProjectQuotation(@Param("projectId") Integer projectId, @Param("contractOrOrderRefProjectQuotation") Boolean contractOrOrderRefProjectQuotation);

	int updateSyncDataId(Map<String,Object> paramMap);
}
