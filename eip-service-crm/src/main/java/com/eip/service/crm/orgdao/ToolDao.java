package com.eip.service.crm.orgdao;

import com.eip.facade.crm.entity.Tool;

import java.util.List;
import java.util.Map;

public interface ToolDao {

	int count(Map<String, Object> paramMap);
	
	List<Tool> select(Map<String, Object> paramMap);
	
	int insert(Tool tool);
	
	int update(Tool tool);
	
	int delete(Integer toolId);
	
	int deleteBatch(List<Tool> list);
	
	int countInExhibit(Map<String, Object> paramMap);
	
	List<Tool> selectInExhibit(Map<String, Object> paramMap);
	
	int countNotInExhibit(Map<String, Object> paramMap);
	
	List<Tool> selectNotInExhibit(Map<String, Object> paramMap);
	
	int countInExhibitNotInBooth(Map<String, Object> paramMap);
	
	List<Tool> selectInExhibitNotInBooth(Map<String, Object> paramMap);

}
