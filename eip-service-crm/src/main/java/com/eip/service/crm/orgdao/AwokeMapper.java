package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.Awoke;
import com.eip.facade.crm.vo.AwokeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-01-02 11:23
 */
public interface AwokeMapper extends MyMapper<Awoke> {

    List<AwokeVo> selectByAwokeTimeAfterNow(@Param("awokeFrom") String awokeFrom, @Param("type") Integer type, @Param("isHaveRead") Integer isHaveRead);

    /**
     * 查询提醒列表
     */
    List<AwokeVo> selectAwokeList(Map<String, Object> paramMap);

    /**
     * 查询联系记录提醒
     */
    AwokeVo selectAwokeForClientTouch(@Param("awokeId") Long awokeId,@Param("awokeType") String awokeType);

    /**
     * 查询申请客户共享提醒
     */
    AwokeVo selectAwokeForClientShare(@Param("awokeId") Long awokeId,@Param("awokeType") String awokeType);

    /**
     * 获取提醒未读数量
     * @param awokeFrom
     * @return
     */
    Integer getUnReadAwokeCount(@Param("awokeFrom") String awokeFrom);

    /**
     * 获取提醒未处理数量
     * @param awokeFrom
     * @return
     */
    Integer getUntreatedAwokeCount(@Param("awokeFrom") String awokeFrom);

    List<Awoke> selectByClientTouchIdMerge(Awoke awoke);

    List<Awoke> getWhenTaskAwoke(@Param("operId")String operId);

    int deleteOneAwoke(@Param("awokeId") Long awokeId);

    int deleteOneAwokeByFromId(@Param("fromId")String fromId);

    List<Awoke> selectOneAwoke(Awoke awoke);

    Integer deleteSummaryAwoke(String id);
    /**
     * 查询合同生效提醒
     */
    AwokeVo selectAwokeForContract(@Param("awokeId") Long awokeId,@Param("awokeType") String awokeType);

    /**
     * 根据指定条件查询符合的提醒
     * @param map
     * @return
     */
    Awoke selectByMap(Map<String, Object> map);

    List<Awoke> selectByAwoke(Awoke awoke);

    /**
     * 微信推送模板查询提醒
     * @param awokeMap
     * @return
     */
    List<Awoke> selectAwokeByMap(Map<String, Object> awokeMap);

    /**
     * 通过id和提醒类型查询提醒
     * @param awokeId
     * @param awokeType
     * @return
     */
    AwokeVo selectByIdAndType(@Param("awokeId") Long awokeId,@Param("awokeType") String awokeType);

    /**
     * 统计未推送到微信的提醒数量
     * @param hashMap
     * @return
     */
    int countPushAwokeWxByMap(Map<String, Object> hashMap);

    /**
     * 查询提醒给自己的总结创建人
     * @param queryMap
     * @return
     */
    List<Awoke> queryToRemindMeId(Map<String, Object> queryMap);

    int updateAwoke(Map<String, Object> paramMap);
}
