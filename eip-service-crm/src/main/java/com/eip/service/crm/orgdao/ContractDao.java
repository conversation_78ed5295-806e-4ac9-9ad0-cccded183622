package com.eip.service.crm.orgdao;

import com.eip.facade.crm.entity.Contract;
import com.eip.facade.crm.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ContractDao {

	int payment(Contract contract);
	int serve(Integer contractId);
		
	int count(Map<String, Object> paramMap);
	
	List<Contract> select(Map<String, Object> paramMap);
	
	//List<Contract> select1(Map<String, Object> paramMap);

	int insert(Contract contract);
	
	int update(Contract contract);
	
	int delete(Integer contractId);
	
	int deleteBatch(List<Contract> list);
	
	Contract selectByContractId(Integer contractId);
	
	Contract selectChief(@Param("contractId") int contractId, @Param("projectId") int projectId);
	
	int updateClientToNewClient(@Param("oldClientId")Integer oldClientId, @Param("newClientId")Integer newClientId);

    List<Contract> getList(Map<String, Object> map);

    int check(Contract contract);

	BigDecimal sumAmountRmb(Map<String, Object> map);

	@Select("select sp_import_contract2(#{contractId})")
	int sp_import_contract2(@Param("contractId") Integer contractId);


	@Select("select sp_update_proj_client(#{contractId},#{projCltId})")
	int sp_update_proj_client(@Param("contractId") Integer contractId,@Param("projCltId") Integer projCltId);



	BigDecimal sumBoothAreaAsync(Map<String, Object> map);

	BigDecimal sumBoothCountAsync(Map<String, Object> map);

	Integer sumBoothCountNumAsync(Map<String, Object> map);

	/**
	 * 首付款金额
	 * @param map
	 * @return
	 */
	BigDecimal sumFirstPayment(Map<String, Object> map);

	List<ProjContractVo> getClientList(@Param("projectIds")List<Integer> projectIds, @Param("orgNum")Integer orgNum);

	ProjCltContractVo getClient(@Param("projectId")Integer projectId, @Param("clientId")Integer clientId, @Param("orgNum")Integer orgNum);

	int initialPayment(Map<String, Object> map);

	ContractVo selectContractById(@Param("contractId") Integer contractId);

    int updateState(Contract contract);

    BigDecimal sumContractPrice(Map<String, Object> map);

    int updateProjCltId(@Param("projCltId")Integer projCltId, @Param("projectId")Integer projectId, @Param("clientId")Integer clientId);

//	int updateContractBoothNumById(@Param("boothNum")String boothNum,@Param("contractId") Integer contractId);
	int updateContractBoothNumById(Map<String, Object> parammap);

    List<Contract> queryContract(Map<String, Object> parammap);

	int updateContractExhibitProductById(@Param("productTypeNames") String productTypeNames, @Param("contractId")Integer contractId);

	Contract getChiefContractOwner(@Param("projectId")Integer projectId, @Param("clientId")Integer clientId);

	int updateInvalidContractState(Contract contract);

	int updateContractBoothInfoById(Map<String, Object> boothMap);

	int updateContractExhibitBoothInfo(Contract currentContract);

    List<AmountVo> sumAmountForeign(Map<String, Object> map);

	int updateContractInEffectiveTimeById(Map<String, Object> paramMap);

	int updateContractEffectiveTimeById(Map<String, Object> paramMap);

    int updateRetentionTimeById(Map<String, Object> paramMap);

    List<ServeBoothDetailVo> queryExhibitorInfo(Map<String, Object> paramMap);

	int updateContractFileById(Map<String, Object> paramMap);

	int updateBoothInfoByContractId(Map<String, Object> paramMap);

	int countContractNumAsync(Map<String, Object> map);

	int resetContractBoothInfo(@Param("contractId") Integer contractId);

	int getCount(Map<String, Object> map);

    int updateContractClientNameById(@Param("companyName") String companyName, @Param("contractId") Integer contractId);

	int updateContractBoothCountById(Map<String, Object> resultMap);

	int updateBoothAuthorRetainTime(Map<String, Object> paramMap);

	int updatePaperContractState(@Param("contractId") Integer contractId, @Param("paperContractState") Integer paperContractState);

	List<Contract> selectNeedUpdateChiefByClientId(@Param("clientId")Integer clientId);

	void updateChief(@Param("contractId")Integer contractId, @Param("isChief")Boolean isChief);
}
