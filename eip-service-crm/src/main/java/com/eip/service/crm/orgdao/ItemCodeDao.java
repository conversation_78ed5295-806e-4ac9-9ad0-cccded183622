package com.eip.service.crm.orgdao;

import java.util.List;
import java.util.Map;

import com.eip.common.util.tkmapper.MyMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.eip.facade.crm.entity.ItemCode;

public interface ItemCodeDao extends MyMapper<ItemCode> {

	List<ItemCode> selectByItemKindCode(String itemKindCode);

	int count(Map<String, Object> paramMap);

	List<ItemCode> selectByMap(Map<String, Object> paramMap);

	/*@Select("SELECT sys_getnewintegerid(#{fieldCode,jdbcType=VARCHAR}, #{increaseMode,jdbcType=BIT})")
	int getNewIntegerId(@Param("fieldCode") String fieldCode, @Param("increaseMode") Boolean increaseMode);*/

	/*void insert(ItemCode itemCode);

	void update(ItemCode itemCode);

	void delete(int id);*/

	void deleteBatch(List<ItemCode> itemCodes);

	List<ItemCode> selectByItemKindCodeElse(@Param("itemKindCode")String itemKindCode, @Param("orgNum")Integer orgNum);
	
	
	String getName(@Param("itemKindCode")String itemKindCode,@Param("code") Integer code,@Param("orgNum") Integer orgNum);

	Integer getCode(@Param("itemKindCode")String itemKindCode,@Param("name") String name,@Param("orgNum") Integer orgNum);

	int deleteParentId(@Param("parentId") Integer parentId, @Param("itemKindCode") String itemKindCode);

	//Boolean queryIsUserEditKind(String itemKindCode);
}
