package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.ElectronicSeal;
import com.eip.facade.crm.vo.ElectronicSealVo;

import java.util.List;
import java.util.Map;

/**
 * @USER: zwd
 * @DATE: 2023-03-01 15:50
 * @DESCRIPTION:
 */
public interface SealMapper extends MyMapper<ElectronicSeal> {


    List<ElectronicSealVo> querySeal(Map<String, Object> paramMap);
}
