package com.eip.service.crm.orgdao;

import com.eip.facade.crm.entity.OrderBS;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface OrderBSDao {

	int count(Map<String, Object> paramMap);
	
	List<OrderBS> select(Map<String, Object> paramMap);
	
	int insert(OrderBS orderBS);
	
	int update(OrderBS orderBS);
	
	int delete(Integer orderBSId);
	
	int deleteBatch(List<OrderBS> list);
	
	OrderBS selectOnly(@Param("orderBSId") int orderBSId, @Param("projectId") int projectId, @Param("clientId") int clientId);
	
	OrderBS selectExistCost( @Param("projectId") int projectId, @Param("clientId") int clientId);
	

	Integer selectOrderIdByClientIdProjectId(@Param("clientId") Integer clientId, @Param("projectId") Integer projectId);

	Integer changeChecked(OrderBS orderBS);

	OrderBS selectByOrderBsId(@Param("orderBSId") int orderBSId);

	int updateAmountRMB(OrderBS orderBs);
	
	@Select("SELECT sp_import_from_order_bs(#{orderBSId,jdbcType=INTEGER})")
	int generateOrder(@Param("orderBSId")int orderBSId);

	OrderBS selectByOrderAmountType(@Param("clientId") Integer clientId, @Param("projectId") Integer projectId, @Param("orderAmountType") String orderAmountType);

}
