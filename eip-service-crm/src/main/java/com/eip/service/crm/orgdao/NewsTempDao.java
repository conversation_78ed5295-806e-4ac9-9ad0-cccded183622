package com.eip.service.crm.orgdao;

import java.util.List;
import java.util.Map;

import com.eip.facade.crm.entity.News;

public interface NewsTempDao {

	int insert(News e);

	int update(News e);

	int delete(Integer id);

	int deleteBatch(List<News> list);

	List<News> select(Map<String, Object> map);

	int count(Map<String, Object> map);

	News selectById(Integer id);

	List<News> selectTop(Map<String, Object> map);

}
