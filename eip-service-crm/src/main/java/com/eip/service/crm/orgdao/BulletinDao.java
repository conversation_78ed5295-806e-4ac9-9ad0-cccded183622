package com.eip.service.crm.orgdao;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.Bulletin;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * @description: TODO
 * @author： hfl
 * @date：2020-11-30 18:13
 * @File: BulletinDao
 */
public interface BulletinDao   {

    int  insert(Bulletin bulletin);

    int deleteById(@Param("bulletinId")Long id);

    int update(Bulletin bulletin);

    Bulletin queryById(@Param("bulletinId") Long id);

    List<Bulletin> selectPage(Bulletin bulletin);

    int updateIsTop(@Param("isTop") Boolean isTop,@Param("bulletinId")Long bulletinId);

    int updateIsRelease(@Param("isRelease")Boolean isRelease,@Param("bulletinId") Long bulletinId);

    List<Bulletin> select(Bulletin bulletin);

    int updateBulletinByRelease(Bulletin bulletin);

    int batchUpdateHavePush(@Param("ids")List<Long> ids, @Param("havePush")Boolean havePush);

    List<Bulletin> selectByMap(Map<String, Object> map);
}
