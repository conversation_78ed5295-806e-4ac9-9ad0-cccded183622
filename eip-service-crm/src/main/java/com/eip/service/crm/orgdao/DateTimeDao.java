package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.HoliDayWorkDay;

import java.util.List;
import java.util.Map;

public interface DateTimeDao extends MyMapper<HoliDayWorkDay> {

	List<HoliDayWorkDay> selectByMap(Map<String, Object> map);

	List<HoliDayWorkDay> getPage(Map<String, Object> paramMap);

	int batchDelete(Map<String, Object> paramMap);

	List<HoliDayWorkDay> queryWorkDay(Map<String, Object> paramMap);

}
