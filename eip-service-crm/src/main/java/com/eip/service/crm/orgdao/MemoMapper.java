package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.Memo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author： hfl
 * @date：2021-07-14 16:36
 * @File: MemoMapper
 */
public interface MemoMapper extends MyMapper<Memo> {

    Integer deleteMemo(@Param("memoId") Integer memoId);

    Integer insertMemo(Memo memo);

    Integer updateMemo(Memo memo);

    List<Memo> findMemo(Map<String, Object> map);

    Integer deleteMemoById(@Param("memoId") Integer memoId);
}
