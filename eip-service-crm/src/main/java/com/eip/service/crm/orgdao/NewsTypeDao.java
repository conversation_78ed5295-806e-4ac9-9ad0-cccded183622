package com.eip.service.crm.orgdao;

import java.util.List;
import java.util.Map;

import com.eip.facade.crm.entity.NewsType;



public interface NewsTypeDao {
	
	int insert(NewsType e);

	int update(NewsType e);

	int delete(Integer id);

	int deleteBatch(List<NewsType> list);

	List<NewsType> select(Map<String, Object> map);

	int count(Map<String, Object> map);

	NewsType selectById(Integer id);

}
