package com.eip.service.crm.orgdao;

import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.Employee;
import com.eip.facade.crm.entity.OperateRole;
import com.eip.facade.crm.entity.Operator;
import com.eip.facade.crm.vo.OwnerInfoVo;
import com.eip.facade.crm.vo.PromoterInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface OperatorDao {

	List<Operator> selectByOperRoleId(Integer operRoleId);
	
	List<Operator> hasOperator(List<OperateRole> list);

	List<Operator> login(@Param("operName") String operName, @Param("password") String password, @Param("orgNum") Integer orgNum);
	
	int count(Map<String, Object> paramMap);
	
	List<Operator> select(Map<String, Object> paramMap);
	
    Operator selectByExistOperName(@Param("operName") String operName, @Param("orgNum") Integer orgNum, @Param("forbidden") Boolean forbidden);
    
    int delete(Integer operatorId);
    
    int deleteBatch(List<Operator> list);
    
    int insert(Operator operator);
    
    int update(Operator operator);

	int updatePassword(@Param("operatorId") int operatorId, @Param("password") String password, @Param("pwdGrade") Integer pwdGrade);
    
    List<Operator> selectAll(Map<String, Object> map);
    
    List<Operator> selectByProjectId(Integer projectId);

	List<Operator> selectByMap(@Param("m")Map<String, Object> map);

    List<String> getNameByIds(@Param("ids")List<Integer> ids);

    Operator getOperById(@Param("operatorId") Integer operatorId);

    String getEmpNameByOperId(@Param("operatorId")Integer operatorId);

    void updateEmployeeMsg(HashMap<String, Object> map);

	List<Operator> selectByProjectAndTeam(@Param("projectId")Integer f_project_id, @Param("workTeamId")Integer f_work_team_id);

	Operator selectOpNameById(@Param("orgNum")Integer orgNum,@Param("operName")String operName);

    int selectOpNameCount(@Param("orgNum")Integer orgNum,@Param("operName")String operName);

    List<Employee> getSuperByOperId(Map<String, Object> map);

    List<Employee> getLowerByOperId(Map<String, Object> map);

    Operator selectOperNameByOrgNumAndId(@Param("orgNum")Integer orgNum,@Param("operId")Integer operId);

    Operator selectByEmpName(@Param("name")String name, @Param("orgNum")Integer orgNum);

    /**
     * 查找业务员（操作员）的个人信息
     * @param hashMap
     * @return
     */
    OwnerInfoVo selectOwnerInfoById(Map<String, Object> hashMap);
    /**
     * 查找多个业绩归属人  这个是以英文逗号隔开的
     * @param hashMap
     * @return
     */
    OwnerInfoVo selectMoreOwners(Map<String, Object> hashMap);

    Operator selectOperatorNameById(@Param("orgNum") Integer orgNum, @Param("empCode") String empCode,@Param("operId") Integer operator);

    int updateZzyUserId(Map<String, Object> map);

    Operator selectByOperatorId(@Param("operatorId") Integer operatorId);

    List<Operator> queryOperator(Map<String, Object> paramMap);

    List<Integer> queryOperatorIdByVersion(Map<String, Object> paramMap);

    List<PromoterInfoVo> queryPromoterOperatorInfo(Map<String, Object> paramMap);

	int updatePwdGrade(@Param("operatorId") Integer operatorId, @Param("pwdGrade") Integer pwdGrade);

	List<Operator> selectRepetition(Map<String, Object> map);

	int updateForbidden(@Param("operatorId")Integer operatorId, @Param("forbidden")Boolean forbidden);

	int updateMobileAndEmail(OperatorDto operatorDto);

	List<Operator> selectForLogin(Map<String, Object> paramMap);

	int updateContainPassword(Operator operator);

	int updateLoginState(@Param("operatorId") Integer operatorId, @Param("loginState") Integer loginState);
}
