package com.eip.service.crm.orgdao;

import com.eip.common.util.tkmapper.MyMapper;
import com.eip.facade.crm.entity.Batch;
import com.eip.facade.crm.vo.BatchExportFileVo;

import java.util.List;
import java.util.Map;

public interface BatchMapper extends MyMapper<Batch> {

    int countExportFile(Map<String, Object> paramMap);

    List<BatchExportFileVo> getExportFileList(Map<String, Object> paramMap);
}
