package com.eip.util;


import com.eip.common.util.PublicUtil;
import org.apache.commons.lang.StringUtils;
import java.math.BigDecimal;
import java.text.DecimalFormat;


public class CommonUtils {

    /**
     * format   ￥188,888.00
     * @param amount
     * @param currencySymbol
     * @return
     */
    public static String formatMoney(BigDecimal amount, String currencySymbol){
        /*if(amount==null||amount.compareTo(BigDecimal.ZERO)==0)return "";
        DecimalFormat df = new DecimalFormat("#,##0.00");
        String format = df.format(amount);
        currencySymbol = StringUtils.isBlank(currencySymbol)?"":currencySymbol;
        return currencySymbol+format;*/
        return PublicUtil.formatMoney(amount,currencySymbol);
    }


}
