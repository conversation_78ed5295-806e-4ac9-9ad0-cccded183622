package com.eip.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eip.common.exception.BusinessException;
import com.eip.common.util.DateUtil;
import com.eip.common.util.http.HttpClientHelper;
import com.eip.facade.crm.entity.HoliDayWorkDay;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-07-25 8:41
 */
public class HolidayUtils {

    // 天行数据节假日api接口
    private final static String ApiUrl = "https://apis.tianapi.com/jiejiari/index";

    // 天行数据APIkey
    private final static String apiKey = "4e988e8c531386aa14e0e68e5d4bba29";

    public static List<HoliDayWorkDay> getFromApi(Integer dateYear, Integer orgNum) throws BusinessException{
        // 发送post请求从第三方api获取数据
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("type", "1");
        paramMap.put("key", apiKey);
        paramMap.put("date", dateYear.toString());
        String res = HttpClientHelper.sendHttpPost(ApiUrl, paramMap);
        JSONObject jsonObject = JSONObject.parseObject(res);
        Integer code = jsonObject.getInteger("code");
        String msg = jsonObject.getString("msg");
        if (!code.equals(200)) {
            throw new BusinessException(code, msg);
        }
        JSONObject result = jsonObject.getObject("result", JSONObject.class);
        boolean isUpdate = result.getBooleanValue("update");
        if (!isUpdate) {
            throw new BusinessException(200, "第三方节假日数据未更新");
        }
        List<JSONObject> list = JSON.parseArray(result.getJSONArray("list").toJSONString(), JSONObject.class);
        List<HoliDayWorkDay> holiDayWorkDayList = Lists.newArrayList();
        Calendar calendarInstance = Calendar.getInstance();
        for (JSONObject holiday : list) {
            String name = holiday.getString("name");
            String vacations = holiday.getString("vacation");
            String remarks = holiday.getString("remark");
            String[] vacationList = vacations.split("\\|");
            String[] remarkList = remarks.split("\\|");
            for (String vacation : vacationList) {
                if (StringUtils.isBlank(vacation)) continue;
                HoliDayWorkDay holiDayWorkDay = new HoliDayWorkDay();
                holiDayWorkDay.setOrgNum(orgNum);
                holiDayWorkDay.setHolidayName(name);
                Date currentVacation = DateUtil.str2Date(vacation, DateUtil.YYYY_MM_DD);
                holiDayWorkDay.setHolidayDate(currentVacation);
                holiDayWorkDay.setIsHoliday(true);
                calendarInstance.setTime(currentVacation);
                holiDayWorkDay.setYearDate(calendarInstance.get(Calendar.YEAR));
                holiDayWorkDayList.add(holiDayWorkDay);
            }
            for (String remark : remarkList) {
                if (StringUtils.isBlank(remark)) continue;
                HoliDayWorkDay holiDayWorkDay = new HoliDayWorkDay();
                holiDayWorkDay.setOrgNum(orgNum);
                String remarkName = name + "调休";
                holiDayWorkDay.setHolidayName(remarkName);
                Date currentRemarkDate = DateUtil.str2Date(remark, DateUtil.YYYY_MM_DD);
                holiDayWorkDay.setHolidayDate(currentRemarkDate);
                holiDayWorkDay.setIsHoliday(false);
                calendarInstance.setTime(currentRemarkDate);
                holiDayWorkDay.setYearDate(calendarInstance.get(Calendar.YEAR));
                holiDayWorkDayList.add(holiDayWorkDay);
            }
        }
        return holiDayWorkDayList;
    }


}
