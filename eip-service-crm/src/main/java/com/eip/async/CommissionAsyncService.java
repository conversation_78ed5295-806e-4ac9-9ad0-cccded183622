package com.eip.async;

import com.eip.common.util.CollectionUtil;
import com.eip.facade.crm.entity.Commission;
import com.eip.facade.crm.entity.CommissionDtl;
import com.eip.facade.crm.entity.CommissionReceive;
import com.eip.facade.crm.vo.RepeatClientVo;
import com.eip.service.crm.orgdao.CommissionDtlMapper;
import com.eip.service.crm.orgdao.CommissionReceiveMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class CommissionAsyncService {

    @Autowired
    private CommissionDtlMapper commissionDtlMapper;
    @Autowired
    private CommissionReceiveMapper commissionReceiveMapper;

    @Async
    public Future<Boolean> getCommissionDtl(List<Commission> list) {
        List<Integer> collect = list.stream().map(item -> item.getCommissionId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) return new AsyncResult<>(true);
        List<CommissionDtl> select = commissionDtlMapper.getByCommissionIds(collect);
        if (CollectionUtil.isEmpty(select)) return new AsyncResult<>(true);
        Map<Integer, List<CommissionDtl>> groupMap = select.stream().collect(Collectors.groupingBy(item -> item.getCommissionId()));
        for (Commission commission : list) {
            List<CommissionDtl> commissionDtls = groupMap.get(commission.getCommissionId());
            commission.setCommissionDtlList(commissionDtls);
        }
        return new AsyncResult<>(true);
    }

    @Async
    public Future<Boolean> getCommissionReceive(List<Commission> list) {
        List<Integer> collect = list.stream().map(item -> item.getCommissionId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) return new AsyncResult<>(true);
        List<CommissionReceive> select = commissionReceiveMapper.getByCommissionIds(collect);
        if (CollectionUtil.isEmpty(select)) return new AsyncResult<>(true);
        Map<Integer, List<CommissionReceive>> groupMap = select.stream().collect(Collectors.groupingBy(item -> item.getCommissionId()));
        for (Commission commission : list) {
            List<CommissionReceive> commissionReceives = groupMap.get(commission.getCommissionId());
            commission.setCommissionReceiveList(commissionReceives);
        }
        return new AsyncResult<>(true);
    }
}
