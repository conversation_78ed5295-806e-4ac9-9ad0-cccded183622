package com.eip.async;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.beust.jcommander.internal.Sets;
import com.eip.common.entity.Statistics;
import com.eip.common.enums.BusinessOrderTypeEnum;
import com.eip.common.enums.EventTaskCategoryEnum;
import com.eip.common.enums.ExhibitorTaskTypeEnum;
import com.eip.common.util.CollectionUtil;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ServeBoothDto;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.service.ProjTeamService;
import com.eip.facade.crm.service.SysSettingOrgService;
import com.eip.facade.crm.service.ZzyUserAwokeService;
import com.eip.facade.crm.vo.*;
import com.eip.service.crm.biz.TaskKindOrgBiz;
import com.eip.service.crm.orgdao.*;
import com.eip.service.crm.zzysysmapper.MemberGradeMapper;
import com.eip.service.crm.zzysysmapper.ZzyUserDao;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class ServeBoothAsyncService {

    @Autowired
    private TraderRegMapper  traderRegMapper;
    @Autowired
    private BoothPreorderDao boothPreorderDao;
    @Autowired
    private ExhibitBoothDao exhibitBoothDao;
    @Autowired
    private ZzyUserDao zzyUserDao;
    @Autowired
    private MemberGradeMapper memberGradeMapper;
    @Autowired
    private BoothNeedMapper boothNeedMapper;
    @Autowired
    private SysSettingOrgMapper sysSettingOrgMapper;
    @Autowired
    private ContractDao contractDao;
    @Autowired
    private SysSettingOrgService sysSettingOrgService;
    @Autowired
    private ClientDao clientDao;
    @Autowired
    private FnReceiptMapper fnReceiptMapper;
    @Autowired
    private PreorderFocusBoothMapper focusBoothMapper;
    @Autowired
    private TaskDetailDao taskDetailDao;
    @Autowired
    private ServeBoothDao serveBoothDao;
    @Autowired
    private ZzyUserAwokeService zzyUserAwokeService;
    @Autowired
    private ProjTeamService projTeamService;
    @Autowired
    private TeamClientMapper teamClientMapper;
    @Autowired
    private OrderDao orderDao;
    @Autowired
    private TaskKindOrgBiz taskKindOrgBiz;
    @Autowired
    private EnterpriseFocusMemberMapper enterpriseFocusMemberMapper;

    /**
     * 查询参展申请
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> queryTraderReg(List<ServeBoothVo> list) {
        if(CollectionUtil.isEmpty(list)) return new AsyncResult<>(true);
        List<Integer> projCltIds = list.stream().filter(Objects::nonNull).map(ServeBoothVo::getProjCltId).distinct().collect(Collectors.toList());
        List<TraderRegBaseVo> traderRegs = traderRegMapper.selectByProjCltIds(projCltIds);
        if(CollectionUtil.isEmpty(traderRegs)) return new AsyncResult<>(true);
        for(ServeBoothVo bean : list){
            if(bean.getProjCltId()==null) continue;
            Iterator<TraderRegBaseVo> iterator = traderRegs.iterator();
            while (iterator.hasNext()){
                TraderRegBaseVo next = iterator.next();
                if(bean.getProjCltId().equals(next.getProjCltId())){
                    bean.setTraderRegFlag(true);
                    bean.getTraderRegBaseVos().add(next);
                    iterator.remove();
                }
            }
        }
        return new AsyncResult<>(true);
    }

    /**
     * 查询展位预定信息
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> queryPreorder(List<ServeBoothVo> list,Integer orgNum) {
        if(CollectionUtil.isEmpty(list))return new AsyncResult<>(true);
        Map<String, Object> hashMap = Maps.newHashMap();
        hashMap.put("orgNum",orgNum);
        hashMap.put("settingCode","project_process_setting");
        SysSettingOrg projectFlowSet = sysSettingOrgMapper.selectBeanByMap(hashMap);
        hashMap.put("settingCode","effective_basis_contract");
        SysSettingOrg contractPayMent = sysSettingOrgMapper.selectBeanByMap(hashMap);
        //全流程脱离了以订单为依据的状态判断
        Map<String,Object> paramMap = Maps.newHashMap();
        for(ServeBoothVo serveBoothVo:list){
            //查询预订单
            paramMap.clear();
            paramMap.put("gtSaleState",true);
            paramMap.put("clientId",serveBoothVo.getClientId());
            paramMap.put("projectId",serveBoothVo.getContractProjectId());
            paramMap.put("orgNum",serveBoothVo.getOrgNum());
            List<BoothPreorder> boothPreorderLists = boothPreorderDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(boothPreorderLists)){
                List<Map<String, Object>> boothPreorderMapList = Lists.newArrayList();
                for(BoothPreorder boothPreorder : boothPreorderLists){
                    Map<String,Object> boothPreOrderMap = Maps.newHashMap();
                    boothPreOrderMap.put("preOrderId",String.valueOf(boothPreorder.getPreorderId()));
                    boothPreOrderMap.put("preOrderTime",boothPreorder.getPreoderTime());
                    boothPreorderMapList.add(boothPreOrderMap);
                }
                serveBoothVo.setBoothPreOrderFlag(true);
                serveBoothVo.setHavePreOrderFlag(true);
                serveBoothVo.setBoothPreOrderList(boothPreorderMapList);
            }
            //查询合同
            paramMap.clear();
            paramMap.put("clientId",serveBoothVo.getClientId());
            paramMap.put("projectId",serveBoothVo.getContractProjectId());
            paramMap.put("orgNum",serveBoothVo.getOrgNum());
            paramMap.put("state",1);
            List<Contract> contracts = contractDao.queryContract(paramMap);
            if(CollectionUtils.isNotEmpty(contracts)){
                List<Map<String, Object>> contractList =  Lists.newArrayList();
                for(Contract contract : contracts){
                    Map<String,Object> contractMap = Maps.newHashMap();
                    contractMap.put("contractId",contract.getContractId());
                    contractMap.put("fIsChief",contract.getIsChief());
                    contractMap.put("fIsPayment",contract.getIsPayment());//生效
                    contractMap.put("paymentDate", com.eip.common.util.DateUtil.date2Str(contract.getPaymentData(), com.eip.common.util.DateUtil.YYYY_MM_DD_HH_MM_SS));//生效时间
                    contractMap.put("paymentFlag",contract.getPaymentFlag());//到款
                    contractList.add(contractMap);
                }
                serveBoothVo.setContractFlag(true);
                serveBoothVo.setContractList(contractList);
            }

            //销售订单
            paramMap.clear();
            paramMap.clear();
            paramMap.put("clientId",serveBoothVo.getClientId());
            paramMap.put("projectId",serveBoothVo.getContractProjectId());
            paramMap.put("orderTypeCode", BusinessOrderTypeEnum.XSDD.getBusinessTypeCode());
            List<Order> orderList = orderDao.getList(paramMap);
            if(CollectionUtils.isNotEmpty(orderList)){
                List<Map<String, Object>> saleOrderList =  Lists.newArrayList();
                for(Order order : orderList){
                    Map<String,Object> orderMap = Maps.newHashMap();
                    orderMap.put("orderId",order.getOrderId());
//                    orderMap.put("inputTime",com.eip.common.util.DateUtil.date2Str(order.getInputTime(), com.eip.common.util.DateUtil.YYYY_MM_DD));
                    orderMap.put("signDate",com.eip.common.util.DateUtil.date2Str(order.getSignDate(), com.eip.common.util.DateUtil.YYYY_MM_DD));
                    orderMap.put("fIsCheck",order.getIsChecked());//是否审核
                    saleOrderList.add(orderMap);
                }
                serveBoothVo.setSaleOrderFlag(true);
                serveBoothVo.setSaleOrderList(saleOrderList);
            }
            //查询收款单
            paramMap.clear();
            paramMap.put("clientId",serveBoothVo.getClientId());
            paramMap.put("projectId",serveBoothVo.getContractProjectId());
            paramMap.put("orgNum",serveBoothVo.getOrgNum());
            List<FnReceipt> fnReceiptList = fnReceiptMapper.getList(paramMap);
            if(CollectionUtils.isNotEmpty(fnReceiptList)){
                List<Map<String, Object>> fnReceiptLists = Lists.newArrayList();
                for(FnReceipt fnReceipt : fnReceiptList){
                    Map<String,Object> fnReceiptMap = Maps.newHashMap();
                    fnReceiptMap.put("fnReceiptId",fnReceipt.getReceiptId());
                    fnReceiptMap.put("inputTime",com.eip.common.util.DateUtil.date2Str(fnReceipt.getInputTime(), com.eip.common.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
                    fnReceiptMap.put("bookDate",com.eip.common.util.DateUtil.date2Str(fnReceipt.getBookDate(), com.eip.common.util.DateUtil.YYYY_MM_DD));
                    fnReceiptMap.put("fnReceiptPaymentDate",com.eip.common.util.DateUtil.date2Str(fnReceipt.getPaymentDate(), com.eip.common.util.DateUtil.YYYY_MM_DD));//银行到款时间
                    fnReceiptLists.add(fnReceiptMap);
                }
                serveBoothVo.setFnReceiptList(fnReceiptLists);
                serveBoothVo.setFnReceiptFlag(true);
            }
            if(Objects.nonNull(contractPayMent)){//合同生效依据
                serveBoothVo.setContractReasonType(contractPayMent.getSettingValue());
            }
//            if(Objects.nonNull(projectFlowSet) && "fullProcess".equals(projectFlowSet.getSettingValue())){//项目流程
//                serveBoothVo.setProjectFlow(projectFlowSet.getSettingValue());
                serveBoothVo.setProjectFlow("fullProcess");
//                paramMap.clear();
//                paramMap.put("clientId",serveBoothVo.getClientId());
//                paramMap.put("contractTypeCode","XSHT");
////                paramMap.put("isChief",true);
//                paramMap.put("state",1);
//                paramMap.put("orgNum",serveBoothVo.getOrgNum());
//                if(serveBoothVo.getContractProjectId()!=null && serveBoothVo.getContractProjectId().intValue() > 0){
//                    paramMap.put("projectId",serveBoothVo.getContractProjectId());//根据参展记录中的项目Id查询  有脚本统一把f_contract_project_id为null的，填充为f_project_id
//                }else{
//                    paramMap.put("projectId",serveBoothVo.getProjectId());
//                }
//                List<Contract> contractList = contractDao.select(paramMap);
//                if(CollectionUtils.isNotEmpty(contractList) && contractList.size() > 0){//主合同
//                    serveBoothVo.setClientContractInfo(contractList.size() + "个合同");
////                    Contract contract = contractList.get(0);
////                    if(Objects.nonNull(contract) && contract.getIsChief()!=null && contract.getIsChief()){
////                        serveBoothVo.setContractId(contract.getContractId());//主合同Id
////                        serveBoothVo.setHaveMainContractFlag(true);//主合同
////                        serveBoothVo.setContractPayment(contract.getIsPayment());//合同生效
////                        serveBoothVo.setPaymentFlag(contract.getPaymentFlag());//首付款到账
////                    }
//                }
                //全流程不在查询展位订单列表，直接返回拼接的展位号  订单+合同下的展位+参展记录中的展位
//                Set<String> resultBoothCode = Sets.newHashSet();
//                String serveBoothCode = serveBoothVo.getBoothCode();
//                if(StringUtils.isNotBlank(serveBoothCode)){
//                    List<String> tempServerBoothCodeList = Lists.newArrayList(serveBoothCode.split(","));
//                    resultBoothCode = tempServerBoothCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
//                }
//                if(serveBoothVo.getClientId()!=null && serveBoothVo.getProjCltId()!=null && serveBoothVo.getProjectId()!=null){
//                    paramMap.clear();
////                    paramMap.put("saleState",1);
//                    paramMap.put("gtSaleState",true);
//                    paramMap.put("projectId",serveBoothVo.getProjectId());
//                    paramMap.put("clientId",serveBoothVo.getClientId());
//                    paramMap.put("projCltId",serveBoothVo.getProjCltId());
//                    paramMap.put("orgNum",serveBoothVo.getOrgNum());
//                    List<BoothPreorder> boothPreorders = boothPreorderDao.select(paramMap);
//                    if(CollectionUtils.isNotEmpty(boothPreorders) && boothPreorders.size() > 0){
//                        serveBoothVo.setHavePreOrderFlag(true);
//                        serveBoothVo.setClientPreorderInfo(boothPreorders.size() + "个展位订单");
//                        paramMap.clear();
//                        paramMap.put("preorderIds",boothPreorders.stream().filter(Objects::nonNull).map(BoothPreorder::getPreorderId).distinct().collect(Collectors.toList()));
//                        List<ExhibitBooth> exhibitBoothList = exhibitBoothDao.selectPreorderBooth(paramMap);
//                        if(CollectionUtils.isNotEmpty(exhibitBoothList) && exhibitBoothList.size() > 0){
//                            Set<String> preOrderBoothCode = exhibitBoothList.stream().filter(Objects::nonNull).map(ExhibitBooth::getBoothCode).distinct().collect(Collectors.toSet());
//                            resultBoothCode.addAll(preOrderBoothCode);
//                        }
//                    }
//                }
//                if(CollectionUtils.isNotEmpty(contractList) && contractList.size() > 0){
//                    Set<String> finalResultBoothCode = resultBoothCode;
//                    contractList.stream().filter(Objects::nonNull).filter(contract -> StringUtils.isNotBlank(contract.getBoothNum())).forEach(contract -> {
//                        String[] contractBoothNumSplit = StringUtils.split(contract.getBoothNum(), ",");
//                        Set<String> contractBoothNumSet = Arrays.asList(contractBoothNumSplit).stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toSet());
//                        finalResultBoothCode.addAll(contractBoothNumSet);
//                    });
//                }
//                if(CollectionUtils.isNotEmpty(resultBoothCode) && resultBoothCode.size() > 0){
//                    List<String> resultBoothCodeList = resultBoothCode.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
//                    if(resultBoothCodeList.size() > 2){
//                        String boothCodes = resultBoothCodeList.get(0) + "、"+resultBoothCodeList.get(1) + "等" + resultBoothCode.size()+ "个展位";
//                        serveBoothVo.setBoothCode(boothCodes);
//                    }else{
//                        serveBoothVo.setBoothCode(StringUtils.join(resultBoothCodeList,"、"));
//                    }
//                }

            //展位信息
            paramMap.clear();
            paramMap.put("serveBoothId",serveBoothVo.getServeBoothId());
//            paramMap.put("boothSaleStateGt0",true);
            paramMap.put("focusBoothSaleStateGt0",true);
            List<ExhibitBoothVo> exhibitBoothVos = focusBoothMapper.selectFocusBooth(paramMap);
            if(CollectionUtils.isNotEmpty(exhibitBoothVos)){
                serveBoothVo.setBoothInfoDtlFlag(true);
                serveBoothVo.setBoothReleaseTimeFlag(true);
                //展位排序规则：释放时间升序且展位号升序
                exhibitBoothVos = exhibitBoothVos.stream()
                        .filter(Objects::nonNull)
                        .sorted(
                                Comparator.comparing(ExhibitBoothVo::getRetentionTime,Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(ExhibitBoothVo::getBoothCode,Comparator.nullsLast(Comparator.naturalOrder()))
                        )
                        .collect(Collectors.toList());
                List<Map<String,Object>> boothInfoList = Lists.newArrayList();
                List<Map<String,Object>> boothReleaseTimeList = Lists.newArrayList();
                BigDecimal boothAreaBig = BigDecimal.ZERO;
                for(ExhibitBoothVo exhibitBoothVo : exhibitBoothVos){
                    BigDecimal boothArea = exhibitBoothVo.getBoothArea();
                    if(boothArea!=null && boothArea.compareTo(BigDecimal.ZERO) > 0){
                        boothAreaBig = boothAreaBig.add(boothArea);
                    }
                    //展位信息
                    Map<String, Object> boothInfoMap = Maps.newHashMapWithExpectedSize(6);
                    boothInfoMap.put("boothId",exhibitBoothVo.getBoothId());
                    boothInfoMap.put("boothCode",exhibitBoothVo.getBuildJointBoothMsg());
                    boothInfoMap.put("boothSpec",exhibitBoothVo.getBoothSpec());
                    boothInfoMap.put("saleState",exhibitBoothVo.getSaleState());
                    boothInfoList.add(boothInfoMap);
                    //释放时间
                    Map<String, Object> boothReleaseTimeMap = Maps.newHashMapWithExpectedSize(6);
                    boothReleaseTimeMap.put("boothId",exhibitBoothVo.getBoothId());
//                    boothReleaseTimeMap.put("boothCode",exhibitBoothVo.getBoothCode());
                    boothReleaseTimeMap.put("boothReleaseTime",null);
                    Date retentionTime = exhibitBoothVo.getRetentionTime();
                    if(retentionTime!=null){
                        String releaseTimeStr = com.eip.common.util.DateUtil.date2Str(retentionTime, com.eip.common.util.DateUtil.YYYY_MM_DD_HH_MM_SS);
                        boothReleaseTimeMap.put("boothReleaseTime",releaseTimeStr);
                    }
                    boothReleaseTimeList.add(boothReleaseTimeMap);
                }
                serveBoothVo.setBoothReleaseTimeList(boothReleaseTimeList);
                serveBoothVo.setBoothInfoDtlList(boothInfoList);
                serveBoothVo.setBoothArea(boothAreaBig.doubleValue());
                //展位号
//                int boothSize = exhibitBoothVos.size();//总共展位数（展位号可能为空）
                //汇总所有不为空的展位号
                List<String> boothCodeList = exhibitBoothVos.stream()
                        .filter(Objects::nonNull)
                        .filter(exhibitBoothVo -> StringUtils.isNotBlank(exhibitBoothVo.getBoothCode()))
//                        .map(ExhibitBoothVo::getBoothCode)
                        .map(ExhibitBoothVo::getBuildJointBoothMsg)
                        .collect(Collectors.toList());
                Collections.sort(boothCodeList);
//                if(boothSize >= 3){
//                    String joinBoothNum = boothCodeList.stream().filter(Objects::nonNull).limit(2).collect(Collectors.joining(","));
//                    joinBoothNum = joinBoothNum + "等"+boothSize+"个展位";
//                    serveBoothVo.setBoothCode(joinBoothNum);
//                }else{
                    serveBoothVo.setBoothCode(StringUtils.join(boothCodeList,","));
//                }
                //汇总所有不为空的展馆展区
                List<String> sectionNamesList = exhibitBoothVos.stream()
                        .filter(Objects::nonNull)
                        .filter(exhibitBoothVo -> StringUtils.isNotBlank(exhibitBoothVo.getSectionName()))
                        .map(ExhibitBoothVo::getSectionName)
                        .distinct()
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(sectionNamesList)){
                    Collections.sort(sectionNamesList);
                    serveBoothVo.setSectionNames(StringUtils.join(sectionNamesList,","));
                }
            }else{
                serveBoothVo.setBoothCode(null);
                serveBoothVo.setBoothArea(null);
                serveBoothVo.setSectionNames(null);
            }
//            }else{
//                serveBoothVo.setProjectFlow(projectFlowSet.getSettingValue());
//            }
        }
//        List<Integer> projCltIds = list.stream().map(ServeBoothVo::getProjCltId).distinct().collect(Collectors.toList());
//        //查询预定信息
//        List<BoothPreorderVo> boothPreorderVos = boothPreorderDao.selectByProjCltIds(projCltIds);//按照预定id 倒序排序的
//        if(CollectionUtil.isEmpty(boothPreorderVos))return new AsyncResult<>(true);
//        //预订单信息的订单Id
//        List<Long> collect = boothPreorderVos.stream().map(BoothPreorderVo::getPreorderId).distinct().collect(Collectors.toList());
//        //根据预定信息查询展位信息preorderIds
//        Map<String,Object> map = Maps.newHashMap();
//        map.put("preorderIds",collect);
//        List<ExhibitBooth> exhibitBooths = exhibitBoothDao.selectPreorderBooth(map);
//        List<BoothPreorderVo> boothPreorderList = Lists.newArrayList();
//        //根据展位预定ID查询对应的展位集合
//        if(Objects.nonNull(projectFlowSet) && "simpleProcess".equals(projectFlowSet.getSettingValue())) {
//             boothPreorderList = this.buildBoothPreOrder(boothPreorderVos,exhibitBooths);
//        }
//        map.clear();
//        for(ServeBoothVo bean:list){
//            if(CollectionUtil.isEmpty(boothPreorderVos))break;
//            if(bean.getProjCltId() == null)continue;
//            if(Objects.nonNull(projectFlowSet)){
//                //简化流程下 防止签合同失败
////                Iterator<BoothPreorderVo> preorderIteratorF = boothPreorderVos.iterator();
////                while (preorderIteratorF.hasNext()){
////                    //订单中的展位信息
////                    BoothPreorderVo preorder = preorderIteratorF.next();
////                    Iterator<ExhibitBooth> boothIteratorF = exhibitBooths.iterator();
////                    while (boothIteratorF.hasNext()){
////                        ExhibitBooth booth = boothIteratorF.next();
////                        if(preorder.getPreorderId().equals(booth.getPreorderId())){
////                            preorder.getExhibitBoothList().add(booth);
////                            boothIteratorF.remove();
////                        }
////                    }
////                    if(bean.getProjCltId().equals(preorder.getProjCltId())){
////                            /*bean.getBoothPreorderVos().add(preorder);
////                            preorderIterator.remove();*/
////                        //改为 一个客户只能有一个预定信息
////                        bean.setPreorderId(preorder.getPreorderId());
////                        bean.setSaleState(preorder.getSaleState());
////                        bean.setExhibitBoothList(preorder.getExhibitBoothList());
////                        bean.setHavePreOrderFlag(true);
////                        preorderIteratorF.remove();
////                        //boothPreorderVos 里的数据 是按照预定id 倒序排序的，取到数据后就结束循环
////                        break;
////                    }
////                }
//                if("simpleProcess".equals(projectFlowSet.getSettingValue())){
//                    List<BoothPreorderVo> simpleProcBoothPreOrders = Lists.newArrayList();
//                    Iterator<BoothPreorderVo> preorderIterator = boothPreorderList.iterator();//订单集合
//                    while (preorderIterator.hasNext()){
//                        BoothPreorderVo boothPreorderVo = new BoothPreorderVo();
//                        BoothPreorderVo preorder = preorderIterator.next();//单个订单
//                         if(preorder.getProjCltId()!=null && bean.getProjCltId().equals(preorder.getProjCltId())){
//                             if(CollectionUtils.isEmpty(bean.getExhibitBoothList()) && bean.getExhibitBoothList().size()<=0){
//                                 bean.setExhibitBoothList(preorder.getExhibitBoothList()); //防止简化流程下签合同报错
//                                 bean.setPreorderId(preorder.getPreorderId());
//                                 bean.setSaleState(preorder.getSaleState());
//                                 bean.setHavePreOrderFlag(true);
//                             }
//                             boothPreorderVo.setSaleState(preorder.getSaleState());
//
//                             List<ExhibitBooth> exhibitBoothList = preorder.getExhibitBoothList();//单个订单中的相关展位信息
//
//                             if(CollectionUtils.isNotEmpty(exhibitBoothList) && exhibitBoothList.size() > 0){
//
//                                 boothPreorderVo.setExhibitBoothList(exhibitBoothList);
//
//                                 List<String> jointStrList = Lists.newArrayList();
//                                 for(ExhibitBooth exhibitBooth : exhibitBoothList){
//                                     if(Objects.nonNull(exhibitBooth)){
//                                         StringBuilder strBuilder = new StringBuilder();
//                                         if(StringUtils.isNotBlank(exhibitBooth.getBoothCode())){
//                                             strBuilder.append(exhibitBooth.getBoothCode());
//                                         }
//                                         if(exhibitBooth.getBoothArea() >= 0){
//                                             strBuilder.append(" - ").append(exhibitBooth.getBoothArea()).append("m²");
//                                         }
//                                         if(preorder.getSaleState()!=null && preorder.getSaleState()>0){
//                                             String reseveTime = formartPreOrderDateTime(preorder);
//                                             if(StringUtils.isNotBlank(reseveTime)){
//                                                 strBuilder.append(" - ").append(reseveTime);
//                                             }
//                                         }
//                                         jointStrList.add(strBuilder.toString());
//                                     }
//                                 }
//                                 boothPreorderVo.setPreOrderStr(jointStrList);
//                             }
//                             simpleProcBoothPreOrders.add(boothPreorderVo);
//                         }
//                    }
//                    bean.setSimpleProcBoothPreOrders(simpleProcBoothPreOrders);
//                }else{
////                    Iterator<BoothPreorderVo> preorderIterator = boothPreorderVos.iterator();
////                    while (preorderIterator.hasNext()){
////                        //订单中的展位信息
////                        BoothPreorderVo preorder = preorderIterator.next();
////                        Iterator<ExhibitBooth> boothIterator = exhibitBooths.iterator();
////                        while (boothIterator.hasNext()){
////                            ExhibitBooth booth = boothIterator.next();
////                            if(preorder.getPreorderId().equals(booth.getPreorderId())){
////                                preorder.getExhibitBoothList().add(booth);
////                                boothIterator.remove();
////                            }
////                        }
////                        if(bean.getProjCltId().equals(preorder.getProjCltId())){
////                            /*bean.getBoothPreorderVos().add(preorder);
////                            preorderIterator.remove();*/
////                            //改为 一个客户只能有一个预定信息
////                            bean.setPreorderId(preorder.getPreorderId());
////                            bean.setSaleState(preorder.getSaleState());
////                            bean.setExhibitBoothList(preorder.getExhibitBoothList());
////                            bean.setHavePreOrderFlag(true);
////                            preorderIterator.remove();
////                            //boothPreorderVos 里的数据 是按照预定id 倒序排序的，取到数据后就结束循环
////                            break;
////                        }
////                    }
//                   /* map.put("saleState",1);
//                    map.put("projectId",bean.getProjectId());
//                    map.put("clientId",bean.getClientId());
//                    map.put("projCltId",bean.getProjCltId());
//                    List<BoothPreorder> boothPreorders = boothPreorderDao.select(map);
//                    if(CollectionUtils.isNotEmpty(boothPreorders)){
//                        BoothPreorder boothPreorder = boothPreorders.get(0);
//                        if(Objects.nonNull(boothPreorder)){
//                            bean.setHavePreOrderFlag(true);
//                            bean.setSaleState(boothPreorder.getSaleState());
//                        }
//                    }else{
//                        bean.setHavePreOrderFlag(false);
//                    }
//                    bean.setPreorderId(null);
//                    map.remove("projCltId");
//                    map.remove("saleState");
//                    map.put("queryAllExhibitBooth",true);
//                    map.put("orderSaleState",0);
//                    List<ExhibitBooth> exhibitBoothList = exhibitBoothDao.selectPreorderBooth(map);
//                    if(CollectionUtils.isEmpty(exhibitBoothList)) continue;
//                    bean.setExhibitBoothList(exhibitBoothList);*/
//
//                }
//            }
//        }

        return new AsyncResult<>(true);
    }

    private List<BoothPreorderVo> buildBoothPreOrder(List<BoothPreorderVo> boothPreorderVos, List<ExhibitBooth> exhibitBooths){
      if(CollectionUtil.isNotEmpty(boothPreorderVos) && boothPreorderVos.size() > 0){
        if(CollectionUtils.isNotEmpty(exhibitBooths) && exhibitBooths.size() > 0){
          boothPreorderVos.stream().filter(Objects::nonNull).forEach(boothPreorderVo -> {
              exhibitBooths.stream().filter(Objects::nonNull).forEach(exhibitBooth -> {
                  if(boothPreorderVo.getPreorderId().equals(exhibitBooth.getPreorderId())){
                       boothPreorderVo.getExhibitBoothList().add(exhibitBooth);
                  }
              });
          });
        }
      }
       return boothPreorderVos;
    }

    private String formartPreOrderDateTime(BoothPreorderVo preorder){
        Date nowTime = Calendar.getInstance().getTime();
        Integer saleState = preorder.getSaleState();//预订状态  1 已预订  2 签合同  3 付款  0 取消
        Date preoderTime = preorder.getPreoderTime();//预定时间
        Date preoderReserveTime = preorder.getPreoderReserveTime();//预订保留时间
        Date contractTime = preorder.getContractTime();//合同时间
        Date contractReserveTime = preorder.getContractReserveTime();//合同保留时间
        Date payTime = preorder.getPayTime();//到款时间
        if(saleState!=null){
            String formatOrder = null;
            if(saleState == 1){
                if(preoderTime!=null){
                    formatOrder = DateUtil.format(preoderTime,"yyyy/MM/dd");
                }
                if(preoderReserveTime!=null && preoderReserveTime.compareTo(nowTime) >=0){
                    String formatStr = null;
                    long betweenDay = DateUtil.betweenMs(nowTime, preoderReserveTime);
                    if(betweenDay > 24*60*60*1000){
                        formatStr = DateUtil.formatBetween(betweenDay, BetweenFormatter.Level.HOUR);
                    }else{
                        formatStr = DateUtil.formatBetween(betweenDay, BetweenFormatter.Level.MINUTE);
                    }
                    formatOrder = formatOrder + " (剩余保留时间" + formatStr + ")";
                }
            }
            else if(saleState == 2){
                if(contractTime!=null){
                    formatOrder = DateUtil.format(contractTime,"yyyy/MM/dd");
                }
                if(contractReserveTime!=null && contractReserveTime.compareTo(nowTime) >=0){
                    String formatStr = null;
                    long betweenDay = DateUtil.betweenMs(nowTime, contractReserveTime);
                    if(betweenDay > 24*60*60*1000){
                        formatStr = DateUtil.formatBetween(betweenDay, BetweenFormatter.Level.HOUR);
                    }else{
                        formatStr = DateUtil.formatBetween(betweenDay, BetweenFormatter.Level.MINUTE);
                    }
                    formatOrder = formatOrder + " (剩余保留时间" + formatStr + ")";
                }
            }else if(saleState == 3){
                if(payTime!=null && payTime.compareTo(nowTime) >= 0){
                    formatOrder = DateUtil.format(payTime,"yyyy/MM/dd");
                }
            }
            return formatOrder;
        }
        return null;
    }
    /**
     * 查询会员账号信息
     * @param list
     * @param projectId
     * @return
     */
    @Async
    public Future<Boolean> queryZzyUser(List<ServeBoothVo> list, Integer projectId) {
        if(CollectionUtil.isEmpty(list))return new AsyncResult<>(true);
        for(ServeBoothVo serveBoothVo : list){
            //企业会员
            Integer enterpriseZzyuserId = serveBoothVo.getEnterpriseZzyuserId();
            if(enterpriseZzyuserId!=null){
                ZzyUserBaseVo enterpriseMember = zzyUserDao.queryZzyUserInfoByIdAndOrgNum(enterpriseZzyuserId, serveBoothVo.getOrgNum());
                if(Objects.nonNull(enterpriseMember)){
                    serveBoothVo.setEnterpriseUserName(enterpriseMember.getUserName());
                    serveBoothVo.setEnterpriseMemberGradeId(null);
                    serveBoothVo.setEnterpriseMemberGradeName(null);
                }
            }
            //企业个人会员
            Long enterpriseId = serveBoothVo.getEnterpriseId();
            if(enterpriseId!=null){
                EnterpriseFocusMember enterpriseFocusMemberBuild = EnterpriseFocusMember.builder()
                        .enterpriseId(enterpriseId)
                        .isMain(true)
                        .state(1)
                        .build();
                List<EnterpriseFocusMember> enterpriseFocusMembers = enterpriseFocusMemberMapper.select(enterpriseFocusMemberBuild);
                if(CollectionUtils.isNotEmpty(enterpriseFocusMembers)) {
                    EnterpriseFocusMember enterpriseFocusMember = enterpriseFocusMembers.get(0);
                    Integer personalZzyUserId = enterpriseFocusMember.getZzyUserId();
//                    Integer personalZzyUserId = serveBoothVo.getZzyUserId();
                    if(personalZzyUserId!=null){
                        ZzyUserBaseVo personalMember = zzyUserDao.queryZzyUserInfoByIdAndOrgNum(personalZzyUserId, serveBoothVo.getOrgNum());
                        if(Objects.nonNull(personalMember)){
                            serveBoothVo.setZzyUserId(personalMember.getZzyUserId());
                            serveBoothVo.setUserName(personalMember.getUserName());
                            serveBoothVo.setIsForeign(personalMember.getFIsForeign());
                            serveBoothVo.setZzyUserEmail(personalMember.getEmail());
                            serveBoothVo.setZzyUserMobile(personalMember.getMobile());
                            serveBoothVo.setMemberGradeId(personalMember.getMemberGradeId());
                            serveBoothVo.setMemberGradeName(personalMember.getMemberGradeName());
                        }
                    }
                }
            }
        }
        return new AsyncResult<>(true);
    }

    /**
     * 查询展位需求
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> queryBoothNeed(List<ServeBoothVo> list) {
        if(CollectionUtil.isEmpty(list))return new AsyncResult<>(true);
        List<Integer> serveBoothIds = list.stream().map(ServeBoothVo::getServeBoothId).collect(Collectors.toList());
        List<BoothNeed> boothNeeds = boothNeedMapper.sumBoothAreaGroupBy(serveBoothIds);
        for(ServeBoothVo bean:list){
            if(CollectionUtil.isEmpty(boothNeeds))break;
            if(bean.getServeBoothId()==null)continue;
            Iterator<BoothNeed> iterator = boothNeeds.iterator();
            while (iterator.hasNext()){
                BoothNeed next = iterator.next();
                if(bean.getServeBoothId().equals(next.getServeBoothId())){
                    bean.setBoothArea(next.getBoothArea());
                    iterator.remove();
                    break;
                }
            }
        }
        return new AsyncResult<>(true);
    }

    /**
     * 统计当前查询数据的 展位需求面积
     * @param sql
     * @return
     */
    @Async
    public Future<BigDecimal> sumBoothNeedArea(String sql) {
        BigDecimal area = null;
        Map<String,Object> map = Maps.newHashMap();
        map.put("sql",sql);
        area = boothNeedMapper.sumBoothArea(map);
        return new AsyncResult<>(area);
    }
    /**
     * 统计当前查询数据的 以前合同金额
     * @param sql
     * @param orgNum
     * fullProcess  全流程
     * simpleProcess  简化流程
     * @return
     */
    @Async
    public Future<BigDecimal> sumContractPrice(String sql, Integer orgNum) {
        BigDecimal price = BigDecimal.ZERO;
        Map<String,Object> map = Maps.newHashMap();
        map.put("sql",sql);
        SysSettingOrg build = SysSettingOrg.builder().orgNum(orgNum).settingCode("project_process_setting").build();
        String value = sysSettingOrgService.getSysSettingOrgValue(build);
        if(StringUtils.isNotBlank(value)){
            if("fullProcess".equals(value)){
                BigDecimal contractPrice = contractDao.sumContractPrice(map);
                if(contractPrice!=null){
                    price = contractPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }else if("simpleProcess".equals(value)){
                map.put("gteSaleState",2); //已签合同金额
                price = boothPreorderDao.sumContractPrice(map);
            }
        }else{
            map.put("gteSaleState",2); //已签合同金额
            price = boothPreorderDao.sumContractPrice(map);
        }

        return new AsyncResult<>(price);
    }


    /**
     * 查询参展记录对应的服务工单详情
     * @param serveBoothVoList
     * @return
     */
    public void queryTaskDtl(List<ServeBoothVo> serveBoothVoList,Integer orgNum) {
        if(CollectionUtils.isEmpty(serveBoothVoList)) return;
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(20);
        for(ServeBoothVo bean : serveBoothVoList){
            if(bean.getServeBoothId() == null) continue;
            paramMap.put("serveBoothId",bean.getServeBoothId());
            paramMap.put("clientId",bean.getClientId());
            paramMap.put("taskConfirmGt0",true);
            paramMap.put("taskCategory",null);
            paramMap.put("taskKindCode", ExhibitorTaskTypeEnum.CATA.getTaskKindCode());
            List<TaskDetail> cataTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(cataTaskDetailList)){
                bean.setCataTaskState(true);
                for(TaskDetail taskDetail : cataTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getCataTaskList().add(resultMap);
                }
            }
            //进馆证
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.CERT.getTaskKindCode());
            List<TaskDetail> certTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(certTaskDetailList)){
                bean.setCertTaskState(true);
                for(TaskDetail taskDetail : certTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getCertTaskList().add(resultMap);
                }
            }
            //展具配置
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.BOOTH.getTaskKindCode());
            List<TaskDetail> boothTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(boothTaskDetailList)){
                bean.setBoothTaskState(true);
                for(TaskDetail taskDetail : boothTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getBoothTaskList().add(resultMap);
                }
            }
            //资料上传
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.FILE.getTaskKindCode());
            List<TaskDetail> fileTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(fileTaskDetailList)){
                bean.setFileTaskState(true);
                for(TaskDetail taskDetail : fileTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getFileTaskList().add(resultMap);
                }
            }
            //人员和签证
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.PERSON.getTaskKindCode());
            List<TaskDetail> personTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(personTaskDetailList)){
                bean.setPersonTaskState(true);
                for(TaskDetail taskDetail : personTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getPersonTaskList().add(resultMap);
                }
            }
            //主办方填报说明
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.WRITE_INSTRUCTIONS.getTaskKindCode());
            List<TaskDetail> writeInstructionsTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(writeInstructionsTaskDetailList)){
                bean.setWriteInstructionsTaskState(true);
                for(TaskDetail taskDetail : writeInstructionsTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getWriteInstructionsTaskList().add(resultMap);
                }
            }
            //翻译
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.TRANS.getTaskKindCode());
            List<TaskDetail> transTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(transTaskDetailList)){
                bean.setTransTaskState(true);
                for(TaskDetail taskDetail : transTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getTransTaskList().add(resultMap);
                }
            }
            //外展邀请函
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.INVITE.getTaskKindCode());
            List<TaskDetail> inviteTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(inviteTaskDetailList)){
                bean.setInviteTaskState(true);
                for(TaskDetail taskDetail : inviteTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getInviteTaskList().add(resultMap);
                }
            }
            //代办签证
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.VISA.getTaskKindCode());
            List<TaskDetail> visaTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(visaTaskDetailList)){
                bean.setVisaTaskState(true);
                for(TaskDetail taskDetail : visaTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getVisaTaskList().add(resultMap);
                }
            }
            //贵宾胸卡
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.VIP_BADGE.getTaskKindCode());
            List<TaskDetail> vipBadgeTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(vipBadgeTaskDetailList)){
                bean.setVipBadgeTaskState(true);
                for(TaskDetail taskDetail : vipBadgeTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    List<Statistics> serveVipBadgeCountList = taskDetailDao.getServeVipBadgeCount(taskDetail.getTaskDtlId());
                    Map<String, Integer> map = serveVipBadgeCountList.stream().collect(Collectors.toMap(item -> item.getName(), item -> item.getCount(), (key1, key2) -> key1));
                    resultMap.put("notReviewedNum",map.get("1") == null ? 0 : map.get("1"));//待审核
                    resultMap.put("reviewedNum",map.get("2") == null ? 0 : map.get("2"));//已审核
                    resultMap.put("notApprovedNum",map.get("3") == null ? 0 : map.get("3"));//不通过
                    bean.getVipBadgeTaskList().add(resultMap);
                }
            }
            //特邀嘉宾
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.GUEST.getTaskKindCode());
            List<TaskDetail> guestTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(guestTaskDetailList)){
                bean.setGuestTaskState(true);
                for(TaskDetail taskDetail : guestTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    List<Statistics> guestCountList = taskDetailDao.getServeGuestCount(taskDetail.getTaskDtlId());
                    Map<String, Integer> map = guestCountList.stream().collect(Collectors.toMap(item -> item.getName(), item -> item.getCount(), (key1, key2) -> key1));
                    resultMap.put("notReviewedNum",map.get("1") == null ? 0 : map.get("1"));//待审核
                    resultMap.put("reviewedNum",map.get("2") == null ? 0 : map.get("2"));//已审核
                    resultMap.put("notApprovedNum",map.get("3") == null ? 0 : map.get("3"));//不通过
                    bean.getGuestTaskList().add(resultMap);
                }
            }
            //海外特邀嘉宾
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.OVERSEAS_GUEST.getTaskKindCode());
            List<TaskDetail> overseasGuestTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(overseasGuestTaskDetailList)){
                bean.setOverseasGuestTaskState(true);
                for(TaskDetail taskDetail : overseasGuestTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    List<Statistics> guestCountList = taskDetailDao.getServeGuestCount(taskDetail.getTaskDtlId());
                    Map<String, Integer> map = guestCountList.stream().collect(Collectors.toMap(item -> item.getName(), item -> item.getCount(), (key1, key2) -> key1));
                    resultMap.put("notReviewedNum",map.get("1") == null ? 0 : map.get("1"));//待审核
                    resultMap.put("reviewedNum",map.get("2") == null ? 0 : map.get("2"));//已审核
                    resultMap.put("notApprovedNum",map.get("3") == null ? 0 : map.get("3"));//不通过
                    bean.getOverseasGuestTaskList().add(resultMap);
                }
            }
            //活动申请 - 主题演讲
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.EVENT.getTaskKindCode());
            paramMap.put("taskCategory", EventTaskCategoryEnum.SPEECH.getTaskCategory());
            List<TaskDetail> speechTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(speechTaskDetailList)){
                bean.setEvenSpeechTaskState(true);
                for(TaskDetail taskDetail : speechTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getEventSpeechTaskList().add(resultMap);
                }
            }
            //活动申请 - 发布会
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.EVENT.getTaskKindCode());
            paramMap.put("taskCategory", EventTaskCategoryEnum.PRESENTATION.getTaskCategory());
            List<TaskDetail> presentationTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(presentationTaskDetailList)){
                bean.setEventPresentationTaskState(true);
                for(TaskDetail taskDetail : presentationTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getEventPresentationTaskList().add(resultMap);
                }
            }
            //活动申请 - 现场采访
            paramMap.put("taskKindCode",ExhibitorTaskTypeEnum.EVENT.getTaskKindCode());
            paramMap.put("taskCategory", EventTaskCategoryEnum.INTERVIEW.getTaskCategory());
            List<TaskDetail> interviewTaskDetailList = taskDetailDao.select(paramMap);
            if(CollectionUtils.isNotEmpty(interviewTaskDetailList)){
                bean.setEvenInterviewTaskState(true);
                for(TaskDetail taskDetail : interviewTaskDetailList){
                    Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(12);
                    resultMap.put("taskDtlId",taskDetail.getTaskDtlId());
                    resultMap.put("confirm",taskDetail.getConfirm());
                    resultMap.put("boothCodes",taskDetail.getBoothCodes());
                    resultMap.put("taskKindCode",taskDetail.getTaskKindCode());
                    resultMap.put("taskKindName",taskDetail.getTaskKindName());
                    resultMap.put("taskName",taskDetail.getTaskTopic());
                    resultMap.put("state",taskDetail.getState());
                    bean.getEventInterviewTaskList().add(resultMap);
                }
            }
        }
    }

    /**
     * 合计展商数量（排重）
     * @param sql3
     * @return
     */
    @Async
    public Future<Integer> countExhibitorNum(String sql3) {
        if (StringUtils.isBlank(sql3)) return new AsyncResult<>(0);
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put("sql3",sql3);
        map.put("queryColumn","DISTINCT a.f_client_id");
        List<ServeBoothVo> list = serveBoothDao.getList(map);
        if(CollectionUtils.isEmpty(list)) return new AsyncResult<>(0);
        return new AsyncResult<>(list.size());
    }
    /**
     * 合计合同数量（排重）
     * @param sql4
     * @return
     */
    @Async
    public Future<Integer> countContractNum(String sql4) {
        if (StringUtils.isBlank(sql4)) return new AsyncResult<>(0);
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(1);
        map.put("sql4",sql4);
        int contractNum = contractDao.countContractNumAsync(map);
        return new AsyncResult<>(contractNum);

    }
    /**
     * 已到款数
     * @param sql5
     * @return
     */
    @Async
    public Future<Integer> countFnReceiptNum(String sql5) {
        if (StringUtils.isBlank(sql5)) return new AsyncResult<>(0);
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(1);
        map.put("sql5",sql5);
        int fnReceiptNum = fnReceiptMapper.countFnReceiptNumAsync(map);
        return new AsyncResult<>(fnReceiptNum);
    }
    /**
     * 已预订订单数
     * @param sql6
     * @return
     */
    @Async
    public Future<Integer> countBoothPreOrderNum(String sql6) {
        if (StringUtils.isBlank(sql6)) return new AsyncResult<>(0);
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(1);
        map.put("sql6",sql6);
        int boothPreOrderNum = boothPreorderDao.countBoothPreOrderNumAsync(map);
        return new AsyncResult<>(boothPreOrderNum);
    }

    /**
     * 展位面积、展位号、折算标摊数
     * @param sql7
     * @return
     */
    @Async
    public Future<Map<String, Object>> countBoothCodeOrArea(String sql7) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("boothCodeNum",0);
        resultMap.put("boothAreaSum",0);
        resultMap.put("qtyConvertedNum",0);
        if (StringUtils.isBlank(sql7)) return new AsyncResult<>(resultMap);
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(10);
        map.put("sql7",sql7);
        map.put("oneQueryColumn"," string_agg(teb.f_booth_code,',') bootnum ");
        map.put("twoQueryColumn"," string_agg(bootnum,',') bootnumsum ");
        Map<String, Object> bootCodeMap = serveBoothDao.countBoothCodeOrArea(map);
        if(MapUtils.isNotEmpty(bootCodeMap)){
            Object bootNumSum = bootCodeMap.get("bootnumsum");
            if(Objects.nonNull(bootNumSum)){
                Set<String> finalBoothNumSet = Sets.newHashSet();
                String[] boothNumArray = StringUtils.split(String.valueOf(bootNumSum), ",");
                List<String> converList = Arrays.asList(boothNumArray);
                for(String boothNumStr : converList){
                    finalBoothNumSet.add(StringUtils.trim(boothNumStr));
                }
                resultMap.put("boothCodeNum",finalBoothNumSet.size());
            }
        }
        //面积汇总
        map.put("oneQueryColumn","  sum(COALESCE(teb.f_booth_area,0)) bootharea ");
        map.put("twoQueryColumn"," sum(bootharea) boothareasum ");
        Map<String, Object> BootAreaMap = serveBoothDao.countBoothCodeOrArea(map);
        if(MapUtils.isNotEmpty(BootAreaMap)){
            Object boothAreaSum = BootAreaMap.get("boothareasum");
            if(Objects.nonNull(boothAreaSum)){
                resultMap.put("boothAreaSum",boothAreaSum);
            }
        }
        //折算标摊数
        String qtySql = " case when tbt.f_if_standard = true then 1 else round(COALESCE(teb.f_booth_area,0)::NUMERIC/9,2) end f_qty_converted_num_sum ";
        map.put("oneQueryColumn",qtySql);
        map.put("twoQueryColumn"," sum(f_qty_converted_num_sum) f_qty_converted_num_sum");
        Map<String, Object> qtyConvertedNumMap = serveBoothDao.countBoothCodeOrArea(map);
        if(MapUtils.isNotEmpty(qtyConvertedNumMap)){
            Object qtyConvertedNumSum = qtyConvertedNumMap.get("f_qty_converted_num_sum");
            if(Objects.nonNull(qtyConvertedNumSum)){
                resultMap.put("qtyConvertedNum",qtyConvertedNumSum);
            }
        }
        return new AsyncResult<>(resultMap);
    }

    /**
     * 批量发送启动服务流程成功的提醒
     * @param finalSendAwokeList
     * @param operatorDto
     */
    @Async
    public void buildGeneratorTaskDetailZzyUserAwoke(List<ServeBoothVo> finalSendAwokeList, OperatorDto operatorDto) {
        if (CollectionUtils.isNotEmpty(finalSendAwokeList) && Objects.nonNull(operatorDto)) {
            zzyUserAwokeService.buildGeneratorTaskDetailZzyUserAwoke(finalSendAwokeList, operatorDto);
        }
    }

    /**
     * 查询当前业务员是否有权限操作参展记录
     * @param serveBoothVoList
     * @param serveBoothDto
     * @return
     */
    @Async
    public Future<Boolean> queryOperatorServeBoothFunction(List<ServeBoothVo> serveBoothVoList, ServeBoothDto serveBoothDto) {
        Boolean fu_client_data_overview = serveBoothDto.getFu_client_data_overview();//数据总览权限
        Boolean fu_team_data_kywy = serveBoothDto.getFu_team_data_kywy();//团队内跨成员操作权限
        Boolean fu_team_manage_subordinate = serveBoothDto.getFu_team_manage_subordinate();//团队内管理下级职员 部分内跨业务员
        Boolean fu_visible_other_operator_serve_booth_info = serveBoothDto.getFu_visible_other_operator_serve_booth_info();//可见所有展商名单
        List<Integer> branchOperatorIds = serveBoothDto.getBranchOperatorIds();// 同部门或子部门操作员id
        List<Integer> currentTeamOperatorIds = serveBoothDto.getCurrentWorkTeamOperatorIds();//当前团队下的操作员
        Integer nowOperatorId = serveBoothDto.getNowOperatorId();
        if(nowOperatorId == null) return new AsyncResult<>(true);
        Map<String,Object> paramMap =  Maps.newHashMap();
        Integer workTeamId = serveBoothDto.getWorkTeamId();
        List<Map<String,Object>> emptyList = Collections.emptyList();
        for(ServeBoothVo serveBoothVo : serveBoothVoList){
            if(workTeamId==null || -1 == workTeamId || BooleanUtils.isTrue(fu_client_data_overview)){
                serveBoothVo.setCustomerServiceJumpLink(true);
                serveBoothVo.setOwnerJumpLink(true);
                serveBoothVo.setShowCustomerServiceBtn(true);
                serveBoothVo.setShowAppointOwenBtn(true);
                continue;
            }
            Integer currentProjectId = serveBoothVo.getContractProjectId() != null ? serveBoothVo.getContractProjectId() : serveBoothVo.getProjectId();
            //当前操作员管理权限为项目经理 、客服
            paramMap.clear();
            paramMap.put("currentOperatorId",nowOperatorId);
            paramMap.put("currentProjectId",currentProjectId);
            paramMap.put("currentWorkTeamId",workTeamId);
            paramMap.put("projRoleCodes","customer_service,project_manager");
            int existCustomerServiceFun = projTeamService.countOperatorExistProjRole(paramMap);
            if(existCustomerServiceFun > 0){
                serveBoothVo.setCustomerServiceJumpLink(true);
            }
            //当前操作员管理权限为项目经理 、销售经理、财务
            paramMap.clear();
            paramMap.put("currentOperatorId",nowOperatorId);
            paramMap.put("currentProjectId",currentProjectId);
            paramMap.put("currentWorkTeamId",workTeamId);
            paramMap.put("projRoleCodes","project_manager,sales_manager,financial_affairs");
            int existOwnerFun = projTeamService.countOperatorExistProjRole(paramMap);
            if(existOwnerFun > 0){
                serveBoothVo.setOwnerJumpLink(true);
            }
            //是否显示指派前端归属人按钮 销售经理、项目经理
            paramMap.clear();
            paramMap.put("currentOperatorId",nowOperatorId);
            paramMap.put("currentProjectId",currentProjectId);
            paramMap.put("currentWorkTeamId",workTeamId);
            paramMap.put("projRoleCodes","sales_manager,project_manager");
            int showAppointBtn = projTeamService.countOperatorExistProjRole(paramMap);
            if(showAppointBtn > 0){
                serveBoothVo.setShowAppointOwenBtn(true);
                serveBoothVo.setShowCustomerServiceBtn(true);
            }
            // 无总览权限，只有团队内跨业务员权限， 且项目参与团队 除当前团队外，其他参与团队不存在【销售团队】时，改操作员操作权限视为【销售经理】
            if((BooleanUtils.isFalse(serveBoothVo.getOwnerJumpLink()) || BooleanUtils.isFalse(serveBoothVo.getShowAppointOwenBtn()))
//                && BooleanUtils.isFalse(serveBoothDto.getFu_all_team_manage())//无 跨团队管理
                && BooleanUtils.isFalse(serveBoothDto.getFu_client_data_overview()) //无 数据总览
//                && BooleanUtils.isFalse(serveBoothDto.getFu_team_manage_subordinate()) //无 团队内管理下级职员
                && BooleanUtils.isTrue(serveBoothDto.getFu_team_data_kywy())//只有 团队内跨成员
            ){
                paramMap.clear();
                paramMap.put("currentProjectId",currentProjectId);
                paramMap.put("currentTeamId",workTeamId);
                int projExistCurrentTeamNum = projTeamService.countExistOtherSalesTeam(paramMap);//判断当前团队是否参与当前项目
                if(projExistCurrentTeamNum > 0){
                    paramMap.clear();
                    paramMap.put("currentProjectId",currentProjectId);
                    paramMap.put("notEqCurrentTeam",workTeamId);
                    paramMap.put("teamKindCode","Sales_Team");
                    int existOtherSalesTeamNum = projTeamService.countExistOtherSalesTeam(paramMap);//统计当前项目其它参与团队是销售团队
                    if(!(existOtherSalesTeamNum > 0)){
                        serveBoothVo.setShowAppointOwenBtn(true);
                        serveBoothVo.setOwnerJumpLink(true);
                    }
                }
            }
            Integer customerServiceId = serveBoothVo.getCustomerServiceId();//客服
            Integer ownerId = serveBoothVo.getOwnerId();//业绩归属人
            //是否显示移交客服按钮 【项目经理】、【销售经理】、【存在移交客服权限点】、【当前业绩归属人为自己】
            Boolean existCustomerServiceFunction = serveBoothDto.getExistCustomerServiceFun();
            if(existCustomerServiceFunction && (ownerId!=null && ownerId.equals(nowOperatorId))){
                serveBoothVo.setShowCustomerServiceBtn(true);
            }
            //当前操作员是客服专员
            if(customerServiceId!=null && customerServiceId.equals(nowOperatorId)){
                serveBoothVo.setCustomerServiceJumpLink(true);
            }
            //当前操作员是业绩归属人
            if(ownerId!=null && ownerId.equals(nowOperatorId)){
                serveBoothVo.setOwnerJumpLink(true);
            }
            if(CollectionUtils.isNotEmpty(branchOperatorIds)){
                //存在 部门内跨成员权限，则可以点击 【业绩归属人为同部门及子部门业务员】的参展记录
                if(fu_team_manage_subordinate && (ownerId!=null && branchOperatorIds.contains(ownerId))){
                    serveBoothVo.setOwnerJumpLink(true);
                }
                //存在 部门内跨成员权限，则可以点击 【客服专员 为同部门及子部门业务员】的参展记录
                if(fu_team_manage_subordinate && (customerServiceId!=null && branchOperatorIds.contains(customerServiceId))){
                    serveBoothVo.setCustomerServiceJumpLink(true);
                }
            }
            if(CollectionUtils.isNotEmpty(currentTeamOperatorIds)){
                if(fu_team_data_kywy && (ownerId!=null && currentTeamOperatorIds.contains(ownerId))){
                    //存在 团队内跨成员权限，则可以点击 【业绩归属人为当前团队人员】的参展记录
                    serveBoothVo.setOwnerJumpLink(true);
                }
                //存在 团队内跨成员权限，则可以点击 【客服专员为当前团队人员】的参展记录
                if(fu_team_data_kywy && (customerServiceId!=null &&currentTeamOperatorIds.contains(customerServiceId))){
                    serveBoothVo.setCustomerServiceJumpLink(true);
                }
            }
            //当前展商是否是当前业务员领用、独占、共享的客户
            Integer specialClientNum = serveBoothVo.getSpecialClientNum();
            if(specialClientNum!=null && specialClientNum >= 1){
                serveBoothVo.setOwnerJumpLink(true);
            }
            Boolean ownerJumpLink = serveBoothVo.getOwnerJumpLink();
            Boolean customerServiceJumpLink = serveBoothVo.getCustomerServiceJumpLink();
            //可看所有展商名单且ownerJumpLink=false且customerServiceJumpLink=false
            if(fu_visible_other_operator_serve_booth_info && !ownerJumpLink && !customerServiceJumpLink){
                serveBoothVo.setShowViewBtn(false);
                //隐藏数据
                serveBoothVo.setCertTaskState(false);
                serveBoothVo.setCertTaskList(emptyList);
                serveBoothVo.setCataTaskState(false);
                serveBoothVo.setCataTaskList(emptyList);
                serveBoothVo.setBoothTaskState(false);
                serveBoothVo.setBoothTaskList(emptyList);
                serveBoothVo.setFileTaskState(false);
                serveBoothVo.setFileTaskList(emptyList);
                serveBoothVo.setVisaTaskState(false);
                serveBoothVo.setVisaTaskList(emptyList);
                serveBoothVo.setTransTaskState(false);
                serveBoothVo.setTransTaskList(emptyList);
                serveBoothVo.setInviteTaskState(false);
                serveBoothVo.setInviteTaskList(emptyList);
                serveBoothVo.setPersonTaskState(false);
                serveBoothVo.setPersonTaskList(emptyList);
                serveBoothVo.setWriteInstructionsTaskState(false);
                serveBoothVo.setWriteInstructionsTaskList(emptyList);
                serveBoothVo.setZzyUserEmail(null);
                serveBoothVo.setZzyUserMobile(null);
                serveBoothVo.setZzyUserId(null);
                serveBoothVo.setUserName(null);
                serveBoothVo.setMemberGradeId(null);
                serveBoothVo.setMemberGradeName(null);
                serveBoothVo.setLinkman(null);
                serveBoothVo.setLinkmanMobile(null);
                serveBoothVo.setLinkmanEmail(null);
                serveBoothVo.setLinkmanPosition(null);
                serveBoothVo.setLinkmanQQ(null);
                serveBoothVo.setLinkmanWebsite(null);
                serveBoothVo.setLinkmanWechat(null);
                serveBoothVo.setLinkmanId(null);
                serveBoothVo.setSex(null);
                serveBoothVo.setMemo(null);
            }
        }
        return new AsyncResult<>(true);
    }


    /**
     * 查询单个参展记录的权限项
     * @param serveBooth
     * @param serveBoothDto
     */
    public void queryOneServeBoothFunction(ServeBooth serveBooth, ServeBoothDto serveBoothDto) {
        Boolean fu_client_data_overview = serveBoothDto.getFu_client_data_overview();//数据总览权限
        Boolean fu_team_data_kywy = serveBoothDto.getFu_team_data_kywy();//团队内跨成员操作权限
        Boolean fu_team_manage_subordinate = serveBoothDto.getFu_team_manage_subordinate();//团队内管理下级职员 部分内跨业务员
        List<Integer> branchOperatorIds = serveBoothDto.getBranchOperatorIds();// 同部门或子部门操作员id
        List<Integer> currentTeamOperatorIds = serveBoothDto.getCurrentWorkTeamOperatorIds();//当前团队下的操作员
        Integer nowOperatorId = serveBoothDto.getNowOperatorId();
        if(nowOperatorId == null) ;
        Map<String,Object> paramMap =  Maps.newHashMap();
        Integer workTeamId = serveBoothDto.getWorkTeamId();
        if(workTeamId==null || -1 == workTeamId || BooleanUtils.isTrue(fu_client_data_overview)){
            serveBooth.setCustomerServiceJumpLink(true);
            serveBooth.setOwnerJumpLink(true);
            serveBooth.setShowCustomerServiceBtn(true);
            serveBooth.setShowAppointOwenBtn(true);
            serveBooth.setShowCancelCustomerServiceBtn(true);
            return;
        }
        Integer currentProjectId = serveBooth.getContractProjectId() != null ? serveBooth.getContractProjectId() : serveBooth.getProjectId();
        //当前操作员管理权限为项目经理 、客服
        paramMap.clear();
        paramMap.put("currentOperatorId",nowOperatorId);
        paramMap.put("currentProjectId",currentProjectId);
        paramMap.put("currentWorkTeamId",workTeamId);
        paramMap.put("projRoleCodes","customer_service,project_manager");
        int existCustomerServiceFun = projTeamService.countOperatorExistProjRole(paramMap);
        if(existCustomerServiceFun > 0){
            serveBooth.setCustomerServiceJumpLink(true);
        }
        //当前操作员管理权限为项目经理 、销售经理、财务
        paramMap.clear();
        paramMap.put("currentOperatorId",nowOperatorId);
        paramMap.put("currentProjectId",currentProjectId);
        paramMap.put("currentWorkTeamId",workTeamId);
        paramMap.put("projRoleCodes","project_manager,sales_manager,financial_affairs");
        int existOwnerFun = projTeamService.countOperatorExistProjRole(paramMap);
        if(existOwnerFun > 0){
            serveBooth.setOwnerJumpLink(true);
        }
        //是否显示指派前端归属人按钮 销售经理、项目经理
        paramMap.clear();
        paramMap.put("currentOperatorId",nowOperatorId);
        paramMap.put("currentProjectId",currentProjectId);
        paramMap.put("currentWorkTeamId",workTeamId);
        paramMap.put("projRoleCodes","sales_manager,project_manager");
        int showAppointBtn = projTeamService.countOperatorExistProjRole(paramMap);
        if(showAppointBtn > 0){
            serveBooth.setShowCustomerServiceBtn(true);
            serveBooth.setShowAppointOwenBtn(true);
        }
        // 无总览权限，只有团队内跨业务员权限， 且项目参与团队 除当前团队外，其他参与团队不存在【销售团队】时，改操作员操作权限视为【销售经理】
        if((BooleanUtils.isFalse(serveBooth.getOwnerJumpLink()) || BooleanUtils.isFalse(serveBooth.getShowAppointOwenBtn()))
//                && BooleanUtils.isFalse(serveBoothDto.getFu_all_team_manage())//无 跨团队管理
                && BooleanUtils.isFalse(serveBoothDto.getFu_client_data_overview()) //无 数据总览
//                && BooleanUtils.isFalse(serveBoothDto.getFu_team_manage_subordinate()) //无 团队内管理下级职员
                && BooleanUtils.isTrue(serveBoothDto.getFu_team_data_kywy())//只有 团队内跨成员
        ){
            paramMap.clear();
            paramMap.put("currentProjectId",currentProjectId);
            paramMap.put("currentTeamId",workTeamId);
            int projExistCurrentTeamNum = projTeamService.countExistOtherSalesTeam(paramMap);//判断当前团队是否参与当前项目
            if(projExistCurrentTeamNum > 0){
                paramMap.clear();
                paramMap.put("currentProjectId",currentProjectId);
                paramMap.put("notEqCurrentTeam",workTeamId);
                paramMap.put("teamKindCode","Sales_Team");
                int existOtherSalesTeamNum = projTeamService.countExistOtherSalesTeam(paramMap);//统计当前项目其它参与团队是销售团队
                if(!(existOtherSalesTeamNum > 0)){
                    serveBooth.setShowAppointOwenBtn(true);
                    serveBooth.setOwnerJumpLink(true);
                }
            }
        }
        Integer customerServiceId = serveBooth.getCustomerServiceId();//客服
        Integer ownerId = serveBooth.getOwnerId();//业绩归属人
        //是否显示移交客服按钮  【项目经理】、【销售经理】、【存在移交客服权限点】、【当前业绩归属人为自己】
        Boolean existCustomerServiceFunction = serveBoothDto.getExistCustomerServiceFun();
        if(existCustomerServiceFunction || (ownerId!=null && ownerId.equals(nowOperatorId))){
            serveBooth.setShowCustomerServiceBtn(true);
        }
        //当前操作员是客服专员
        if(customerServiceId!=null && customerServiceId.equals(nowOperatorId)){
            serveBooth.setCustomerServiceJumpLink(true);
        }
        //当前操作员是业绩归属人
        if(ownerId!=null && ownerId.equals(nowOperatorId)){
            serveBooth.setOwnerJumpLink(true);
        }
        if(CollectionUtils.isNotEmpty(branchOperatorIds)){
            //存在 部门内跨成员权限，则可以点击 【业绩归属人为同部门及子部门业务员】的参展记录
            if(fu_team_manage_subordinate && (ownerId!=null && branchOperatorIds.contains(ownerId))){
                serveBooth.setOwnerJumpLink(true);
            }
            //存在 部门内跨成员权限，则可以点击 【客服专员 为同部门及子部门业务员】的参展记录
            if(fu_team_manage_subordinate && (customerServiceId!=null && branchOperatorIds.contains(customerServiceId))){
                serveBooth.setCustomerServiceJumpLink(true);
            }
        }
        if(CollectionUtils.isNotEmpty(currentTeamOperatorIds)){
            if(fu_team_data_kywy && (ownerId!=null && currentTeamOperatorIds.contains(ownerId))){
                //存在 团队内跨成员权限，则可以点击 【业绩归属人为当前团队人员】的参展记录
                serveBooth.setOwnerJumpLink(true);
            }
            //存在 团队内跨成员权限，则可以点击 【客服专员为当前团队人员】的参展记录
            if(fu_team_data_kywy && (customerServiceId!=null &&currentTeamOperatorIds.contains(customerServiceId))){
                serveBooth.setCustomerServiceJumpLink(true);
            }
        }
        //是否显示取消客服按钮
        Integer saleState = serveBooth.getSaleState();
        if(saleState !=null && 9 == saleState){
            Boolean showCustomerServiceBtn = serveBooth.getShowCustomerServiceBtn();
            if(showCustomerServiceBtn!=null && showCustomerServiceBtn){
                serveBooth.setShowCancelCustomerServiceBtn(true);
            }
        }
        //当前展商是否是当前业务员领用、独占、共享的客户
        if(serveBooth.getClientId()!=null && (serveBoothDto.getWorkTeamId()!=null && serveBoothDto.getWorkTeamId()!=-1) && serveBoothDto.getNowOperatorId()!=null){
            paramMap.clear();
            paramMap.put("clientId",serveBooth.getClientId());
            paramMap.put("wortTeamId",serveBoothDto.getWorkTeamId());
            paramMap.put("nowOperatorId",serveBoothDto.getNowOperatorId());
            Integer specialClientNum = teamClientMapper.countCurrentOperatorClient(paramMap);
            if(specialClientNum!=null && specialClientNum >= 1){
                serveBooth.setOwnerJumpLink(true);
            }
        }
    }
}
