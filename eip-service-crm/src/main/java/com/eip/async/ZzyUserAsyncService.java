package com.eip.async;

import com.eip.common.enums.ZzyUserTypeEnum;
import com.eip.facade.crm.dto.ZzyUserDto;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.service.UserFocusProjService;
import com.eip.service.crm.orgdao.EnterpriseMapper;
import com.eip.service.crm.orgdao.MemberInfoMapper;
import com.eip.service.crm.orgdao.OperatorDao;
import com.eip.service.crm.zzysysmapper.ZzyUserDao;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @USER: zwd
 * @DATE: 2023-12-22 10:08
 * @DESCRIPTION:
 */
@Slf4j
@Component
public class ZzyUserAsyncService {
    @Autowired
    private OperatorDao operatorDao;
    @Autowired
    private EnterpriseMapper enterpriseMapper;
    @Autowired
    private MemberInfoMapper memberInfoMapper;
    @Autowired
    private ZzyUserDao zzyUserDao;
    @Autowired
    private UserFocusProjService userFocusProjService;

    /**
     * 查询会员其它信息
     * @param zzyUserlist
     * @param onlyOrgNum
     * @return
     */
    @Async
    public Future<Boolean> queryZzyUserOtherInfo(List<ZzyUser> zzyUserlist,Integer onlyOrgNum) {
        if(CollectionUtils.isEmpty(zzyUserlist)) return new AsyncResult<>(true);
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        for(ZzyUser zzyUser : zzyUserlist){
            paramMap.clear();
            //查询会员个人信息
            if(zzyUser.getMemberInfoId()!=null){
                MemberInfo memberInfo = memberInfoMapper.selectByPrimaryKey(zzyUser.getMemberInfoId());
                if(Objects.nonNull(memberInfo)){
                    zzyUser.setLinkman(memberInfo.getFullName());
                    zzyUser.setF_qq(memberInfo.getQq());
                    zzyUser.setF_wechat(memberInfo.getWechat());
                    zzyUser.setSex(memberInfo.getSex());
                }
            }
            if(ZzyUserTypeEnum.PERSONAL.getCode().equals(zzyUser.getZzyuserType())){ //查询个人会员所属公司
                paramMap.put("orgNum",onlyOrgNum);
                paramMap.put("zzyUserId",zzyUser.getF_zzyuser_id());
                List<Enterprise> enterpriseList = enterpriseMapper.selectByMap(paramMap);
                if(CollectionUtils.isNotEmpty(enterpriseList)){
                    List<String> enterpriseNames = enterpriseList.stream().filter(Objects::nonNull).filter(enterprise -> StringUtils.isNotBlank(enterprise.getCompanyName())).map(Enterprise::getCompanyName).distinct().collect(Collectors.toList());
                    zzyUser.setCompanyNames(enterpriseNames);
                    zzyUser.setCompanyName(StringUtils.join(enterpriseNames,","));
                }
            }else if(ZzyUserTypeEnum.ENTERPRISE.getCode().equals(zzyUser.getZzyuserType())){ //查询企业会员公司
                paramMap.put("orgNum",onlyOrgNum);
                paramMap.put("enterpriseZzyuserId",zzyUser.getF_zzyuser_id());
                List<Enterprise> enterpriseList = enterpriseMapper.selectByMap(paramMap);
                if(CollectionUtils.isNotEmpty(enterpriseList)){
                    Enterprise enterprise = enterpriseList.get(0);
                    zzyUser.setEnterpriseId(enterprise.getEnterpriseId());
                    zzyUser.setCompanyNames(Lists.newArrayList(enterprise.getCompanyName()));
                    zzyUser.setCompanyName(enterprise.getCompanyName());
                }
            }

        }
        return new AsyncResult<>(true);
    }

    @Async
    public Future<Boolean> queryOperatorName(List<ZzyUser> zzyUserlist) {
        if(CollectionUtils.isEmpty(zzyUserlist)) return new AsyncResult<>(true);
        for(ZzyUser zzyUser : zzyUserlist){
            if(zzyUser.getOperatorId() == null) continue;
            zzyUser.setOperName(operatorDao.getEmpNameByOperId(zzyUser.getOperatorId()));
        }
        return new AsyncResult<>(true);
    }

    @Async
    public Future<Boolean> queryZzyUserInfo(List<MemberOperatorLog> list) {
        if(CollectionUtils.isEmpty(list)) return new AsyncResult<>(true);
        for(MemberOperatorLog memberOperatorLog : list){
            if(memberOperatorLog.getOperatorId() == null) continue;
            ZzyUser zzyUser = zzyUserDao.selectByPrimaryKey(memberOperatorLog.getOperatorId());
            if(Objects.isNull(zzyUser)) continue;
            String f_mobile = zzyUser.getF_mobile();
            String f_email = zzyUser.getF_email();
            String f_user_name = zzyUser.getF_user_name();
            if(StringUtils.isNotBlank(f_mobile)){
                memberOperatorLog.setUserName(f_mobile);
                continue;
            }
            if(StringUtils.isNotBlank(f_email)){
                memberOperatorLog.setUserName(f_email);
                continue;
            }
            if(StringUtils.isNotBlank(f_user_name)){
                memberOperatorLog.setUserName(f_user_name);
                continue;
            }
        }
        return new AsyncResult<>(true);
    }


    /**
     * 判断当前会员是否存在指定角色
     * @param zzyUserDto
     */
    @Async
    public void insertUserFocusProjAsync(ZzyUserDto zzyUserDto) {
        try {
            userFocusProjService.insertUserFocusProj(zzyUserDto);
        }catch (Exception e){
            log.error("绑定会员项目参与角色异常:{}",e);
        }
    }

}
