package com.eip.async;

import com.eip.common.enums.ClientTypeCodeEnum;
import com.eip.facade.crm.entity.AuthRule;
import com.eip.facade.crm.entity.TeamClient;
import com.eip.facade.crm.service.AuthRuleService;
import com.eip.service.crm.orgdao.OpportunityDao;
import com.eip.service.crm.orgdao.TeamClientMapper;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Future;

@Component
public class WorkTeamMemberAsyncService {
	@Autowired
	private TeamClientMapper teamClientMapper;
	@Autowired
	private OpportunityDao opportunityDao;
	@Autowired
	private AuthRuleService authRuleService;

	@Async
	public Future<Integer> getAuthCountByDataTransfer(Integer workTeamId, Integer source, Integer target, Integer authType) {
		AuthRule authRule = authRuleService.queryByOpertorAndTeam(target, workTeamId, authType);
		if (authRule == null || authRule.getF_max_amount() == null) return new AsyncResult<>(0);//没有超出最大数据
		if (authType == 1 || authType == 3) { //公司客户领用和独占
			Integer sourceCount = queryTeamClientCount(workTeamId, source, authType);
			if (sourceCount == 0) return new AsyncResult<>(0);//没有超出最大数据
			Integer targetCount = queryTeamClientCount(workTeamId, target, authType);
			//超出数据
			Integer number = sourceCount + targetCount - authRule.getF_max_amount();
			number = number > sourceCount ? sourceCount : (number < 0 ? 0 : number);
			return new AsyncResult<>(number);//超出数据
		} else if (authType == 2) { //商机保护
			Integer sourceCount = queryProtectOppCount(workTeamId, source);
			if (sourceCount == 0) return new AsyncResult<>(0);//没有超出最大数据
			Integer targetCount = queryProtectOppCount(workTeamId, target);
			//超出数据
			Integer number = sourceCount + targetCount - authRule.getF_max_amount();
			number = number > sourceCount ? sourceCount : (number < 0 ? 0 : number);
			return new AsyncResult<>(number);//超出数据
		}
		return new AsyncResult<>(0);
	}

	private Integer queryTeamClientCount(Integer workTeamId, Integer salesmanId, Integer authType) {
		TeamClient build = TeamClient.builder()
				.fWorkteamId(workTeamId)
				.fClientTypeCode(ClientTypeCodeEnum.B_CLIENT.getCode())
				.salesmanId(salesmanId)
				.authStatus(authType)
				.build();
		int count = teamClientMapper.selectCount(build);
		return count;
	}

	private Integer queryProtectOppCount(Integer workTeamId, Integer operatorId) {
		Map<String, Object> map = Maps.newHashMapWithExpectedSize(6);
		map.put("workTeamId", workTeamId);
		map.put("operatorId", operatorId);
		map.put("fprotected", true);
		int count = opportunityDao.count(map);
		return count;
	}
}
