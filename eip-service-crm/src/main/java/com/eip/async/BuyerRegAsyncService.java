package com.eip.async;

import cn.hutool.crypto.digest.MD5;
import com.beust.jcommander.internal.Lists;
import com.eip.common.constant.CommonConstant;
import com.eip.common.enums.EventKindEnum;
import com.eip.common.enums.OperateTypeEnum;
import com.eip.common.util.CollectionUtil;
import com.eip.common.util.DateUtil;
import com.eip.common.util.http.HttpClientHelper;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.BuyerReg;
import com.eip.facade.crm.entity.FieldControl;
import com.eip.facade.crm.entity.ProjRegTemp;
import com.eip.facade.crm.entity.RegCustomizedFieldValue;
import com.eip.facade.crm.service.ChannelService;
import com.eip.facade.crm.service.ChannelTypeService;
import com.eip.facade.crm.service.EventInfoService;
import com.eip.service.crm.orgdao.BuyerRegMapper;
import com.eip.service.crm.orgdao.FieldControlMapper;
import com.eip.service.crm.orgdao.ProjRegTempMapper;
import com.eip.service.crm.orgdao.ProjectDao;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class BuyerRegAsyncService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BuyerRegMapper buyerRegMapper;
    @Autowired
    private ProjRegTempMapper projRegTempMapper;
    @Autowired
    private FieldControlMapper fieldControlMapper;
    @Autowired
    private ChannelTypeService channelTypeService;
    @Autowired
    private ChannelService channelService;
    @Autowired
    private EventInfoService eventInfoService;
    @Autowired
    private ProjectDao projectDao;

    /**
     * 获取每条数据截止兑奖日前的邀请数量
     * @param list 这个list数据里的项目，登记语种，身份应该都一致
     * @return
     */
    @Async
    public Future<Boolean> getCutoffDateInviteCount(List<BuyerReg> list) {
        if (CollectionUtil.isEmpty(list)) return new AsyncResult<>(true);
        Integer projectId = list.get(0).getProjectId();
        Integer version = list.get(0).getVersion() == null ? 1 : list.get(0).getVersion();
        Integer targetType = list.get(0).getTargetType() == null ? 1 : list.get(0).getTargetType();
        //查询 是否开启扩邀兑奖 和 兑奖截止日
        Example example = new Example(ProjRegTemp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("f_project_id", projectId);
        criteria.andCondition("COALESCE(f_target,'1') = " + targetType);
        criteria.andCondition("COALESCE(f_version,'1') = " + version);
        List<String> tempCodes = Arrays.asList("buyer_enable_redeem", "buyer_redeem_cutoff_date",
                "buyer_invite_count_need_mobile_reg", "buyer_invite_count_need_id_check", "buyer_invite_count_need_entrance");
        criteria.andIn("f_temp_type", tempCodes);
        List<ProjRegTemp> temps = projRegTempMapper.selectByExample(example);
        Date buyer_redeem_cutoff_date = null;
        String buyer_enable_redeem = null;//是否开启扩邀兑奖
        String buyer_redeem_cutoff_date_str = null; //扩邀兑奖截止日
        String buyer_invite_count_need_mobile_reg = null; //手机号登记方式才计入邀请结果
        String buyer_invite_count_need_id_check = null; //身份证实名认证才计入邀请结果
        String buyer_invite_count_need_entrance = null;//入场才计入邀请结果
        for (ProjRegTemp temp : temps) {
            if ("buyer_enable_redeem".equals(temp.getF_temp_type())) {
                buyer_enable_redeem = temp.getF_temp();
            } else if ("buyer_redeem_cutoff_date".equals(temp.getF_temp_type())) {
                buyer_redeem_cutoff_date_str = temp.getF_temp();
            } else if ("buyer_invite_count_need_mobile_reg".equals(temp.getF_temp_type())) {
                buyer_invite_count_need_mobile_reg = temp.getF_temp();
            } else if ("buyer_invite_count_need_id_check".equals(temp.getF_temp_type())) {
                buyer_invite_count_need_id_check = temp.getF_temp();
            } else if ("buyer_invite_count_need_entrance".equals(temp.getF_temp_type())) {
                buyer_invite_count_need_entrance = temp.getF_temp();
            }
        }
        if ("true".equals(buyer_enable_redeem) && StringUtils.isNotBlank(buyer_redeem_cutoff_date_str)) {
            buyer_redeem_cutoff_date = DateUtil.str2Date(buyer_redeem_cutoff_date_str, DateUtil.YYYY_MM_DD_HH_MM_SS);
        }
        if (buyer_redeem_cutoff_date != null || "true".equals(buyer_invite_count_need_mobile_reg) || "true".equals(buyer_invite_count_need_id_check)
                || "true".equals(buyer_invite_count_need_entrance)) {
            for (BuyerReg buyerReg : list) {
                buyerReg.setRedeemCutoffDate(buyer_redeem_cutoff_date);
                buyerReg.setMobileRegInvite("true".equals(buyer_invite_count_need_mobile_reg));
                buyerReg.setIdCheckInvite("true".equals(buyer_invite_count_need_id_check));
                buyerReg.setEntranceInvite("true".equals(buyer_invite_count_need_entrance));
                Example build = Example.builder(BuyerReg.class).build();
                Example.Criteria criteria1 = build.createCriteria();
                criteria1.andEqualTo("channelType", 3);
                criteria1.andEqualTo("channelId", buyerReg.getBuyerId());
                if (buyer_redeem_cutoff_date != null) criteria1.andLessThanOrEqualTo("regTime", buyer_redeem_cutoff_date);
                if ("true".equals(buyer_invite_count_need_mobile_reg)) criteria1.andEqualTo("regType", 1);
                if ("true".equals(buyer_invite_count_need_id_check)) criteria1.andEqualTo("idCheck", true);
                if ("true".equals(buyer_invite_count_need_entrance)) criteria1.andEqualTo("isEntrance", true);
                int count = buyerRegMapper.selectCountByExample(build);
                buyerReg.setCutoffDateInviteCount(count);
            }
        } else {
            for (BuyerReg buyerReg : list) {
                //没有截止日，那么等于邀请数量
                buyerReg.setCutoffDateInviteCount(buyerReg.getInviteCount());
            }
        }
        return new AsyncResult<>(true);
    }

    @Async
    public void saveUpdateBuyerLog(BuyerReg newBean, BuyerReg oldBean, String updateField, OperatorDto operatorDto, String type) {
        String updateLog = buildUpdateLog(newBean, oldBean, updateField, operatorDto.getF_org_num());
        if (updateLog.length() == 0) {
            //if ("import".equals(type)) updateLog = "导入动作，数据未变动";
            if ("import".equals(type)) updateLog = "导入";
            else return;
        }
        String eventName = buildEventName(newBean.getProjectId(), newBean.getBuyerName(), newBean.getRegNum());
        eventInfoService.saveEventOrEventChild(operatorDto, updateLog, EventKindEnum.BUYER_REG.getId(),
                OperateTypeEnum.MODIFY.getName(), newBean.getBuyerId().toString(), eventName);
    }


    public String buildEventName(String projectName, String buyerName, String regNum) {
        projectName = projectName == null ? "" : projectName;
        buyerName = buyerName == null ? "" : buyerName;
        regNum = regNum == null ? "" : regNum;
        return projectName + " " + buyerName + " " + regNum;
    }

    public String buildEventName(Integer projectId, String buyerName, String regNum) {
        String projectName = projectDao.selectNameById(projectId);
        return buildEventName(projectName, buyerName, regNum);
    }

    private String buildUpdateLog(BuyerReg newBean, BuyerReg oldBean, String updateField, Integer orgNum) {
        StringBuffer sbf = new StringBuffer();
        String different = eventInfoService.compareObjectDifferentAttributes(oldBean, newBean, updateField, orgNum);
        sbf.append(different);
        if ((newBean.getChannelType() != null && !newBean.getChannelType().equals(oldBean.getChannelType())) ||
                (oldBean.getChannelType() != null && !oldBean.getChannelType().equals(newBean.getChannelType()))) {
            String newName = buildLogChannelTypeName(newBean.getChannelType());
            String oldName = buildLogChannelTypeName(oldBean.getChannelType());
            sbf.append("'渠道类型'由'").append(oldName).append("'变为'").append(newName).append("'").append("\r\n");
        }
        if ((newBean.getChannelId() != null && !newBean.getChannelId().equals(oldBean.getChannelId())) ||
                (oldBean.getChannelId() != null && !oldBean.getChannelId().equals(newBean.getChannelId()))) {
            String newName = buildLogChannelName(newBean.getChannelType(), newBean.getChannelId());
            String oldName = buildLogChannelName(oldBean.getChannelType(), oldBean.getChannelId());
            sbf.append("'渠道'由'").append(oldName).append("'变为'").append(newName).append("'").append("\r\n");
        }
        if (CollectionUtil.isEmpty(newBean.getFieldValueList())) return sbf.toString();
        //自定义字段
        List<Integer> controlIds = newBean.getFieldValueList().stream().filter(item -> item.getControlId() != null)
                .map(item -> item.getControlId()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(controlIds)) return sbf.toString();
        //查询自定义字段 中文名称
        Example example = Example.builder(FieldControl.class).select("controlId", "fieldName").build();
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("controlId", controlIds);
        List<FieldControl> fieldControls = fieldControlMapper.selectByExample(example);
        Map<Integer, String> fieldNameMap = fieldControls.stream().collect(Collectors.toMap(item -> item.getControlId(), item -> item.getFieldName(), (key1, key2) -> key1));
        //之前填报的自定义字段信息
        List<RegCustomizedFieldValue> fieldValueList = oldBean.getFieldValueList() == null ? Lists.newArrayList() : oldBean.getFieldValueList();
        Map<Integer, String> fieldValueMap = fieldValueList.stream().collect(Collectors.toMap(item -> item.getControlId(), item -> item.getFieldValue(), (key1, key2) -> key1));
        for (RegCustomizedFieldValue value : newBean.getFieldValueList()) {
            if (value.getControlId() == null) continue;
            String fieldName = fieldNameMap.get(value.getControlId());
            if (StringUtils.isBlank(fieldName)) continue;
            String oldValue = fieldValueMap.get(value.getControlId());
            oldValue = StringUtils.isBlank(oldValue) ? "空" : oldValue;
            String newValue = StringUtils.isBlank(value.getFieldValue()) ? "空" : value.getFieldValue();
            if (oldValue.equals(newValue)) continue;
            sbf.append("'").append(fieldName).append("'").append("由")
                    .append("'").append(oldValue).append("'")
                    .append("变为").append("'").append(newValue).append("'")
                    .append("\r\n");
        }
        return sbf.toString();
    }

    private String buildLogChannelTypeName(Integer channelTypeId) {
        if (channelTypeId == null) return "空";
        String channelTypeName = channelTypeService.getChannelTypeName(channelTypeId);
        return channelTypeName == null ? channelTypeId.toString() : channelTypeName;
    }

    private String buildLogChannelName(Integer channelTypeId, Integer channelId) {
        if (channelId == null) return "空";
        String name = channelService.getChannelName(channelTypeId, channelId);
        return name == null ? channelId.toString() : name;
    }


    @Async
    public void sendRegResultMsg(String requestIntegralUrl, Integer buyerId) {
        if (StringUtils.isBlank(requestIntegralUrl) || buyerId == null) {
            logger.warn("发送观众登记回执信息缺少参数：requestIntegralUrl = " + requestIntegralUrl + "，buyerId = " + buyerId);
            return;
        }
        try {
            Map<String, Object> map = Maps.newHashMap();
            map.put("buyerId", buyerId);
            String sign = MD5.create().digestHex(CommonConstant.CIPHER_FACTOR + buyerId).toUpperCase();
            map.put("sign", sign);
            String url = requestIntegralUrl + CommonConstant.BUYER_RESULT_MSG_URL;
            HttpClientHelper.sendIntegralHttpPost(url, map);
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
    }
}
