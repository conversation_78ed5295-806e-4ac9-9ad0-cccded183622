package com.eip.async;

import com.eip.common.util.CollectionUtil;
import com.eip.facade.crm.entity.SalesPlan;
import com.eip.facade.crm.entity.SalesPlanProject;
import com.eip.service.crm.orgdao.OrderDtlDao;
import com.eip.service.crm.orgdao.SalesPlanMapper;
import com.eip.service.crm.orgdao.SalesPlanProjectMapper;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class SalesPlanAsyncService {

    @Autowired
    private OrderDtlDao orderDtlDao;
    @Autowired
    private SalesPlanMapper mapper;
    @Autowired
    private SalesPlanProjectMapper salesPlanProjectMapper;

    /**
     * 查询已完成数量
     *
     * @param salesPlan
     * @return
     */
    @Async
    public Future<Boolean> getFinishedQuantity(SalesPlan salesPlan) {
        this.getFinishedQuantityBase(salesPlan, null);
        return new AsyncResult<>(true);
    }

    /**
     * 查询已完成数量
     *
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> getFinishedQuantity(List<SalesPlan> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(bean -> {
                this.getFinishedQuantityBase(bean, null);
            });
        }
        return new AsyncResult<>(true);
    }

    private void getFinishedQuantityBase(SalesPlan salesPlan, Date finishedEndDate) {
        HashMap<String, Object> map = Maps.newHashMap();
        //map.put("projectId", salesPlan.getProjectId());
        map.put("startDate", salesPlan.getStartDate());
        map.put("endDate", finishedEndDate != null ? finishedEndDate : salesPlan.getEndDate());
        map.put("ownTeamId", salesPlan.getWorkTeamId());
        map.put("ownerId", salesPlan.getOwnerId());
        map.put("orgNum", salesPlan.getOrgNum());
        map.put("orderTypeCode", "XSDD");//销售订单
        map.put("ownBranchId", salesPlan.getBranchId());
        if (salesPlan.getPlanId() != null) {
            SalesPlanProject build = SalesPlanProject.builder().planId(salesPlan.getPlanId()).build();
            List<SalesPlanProject> salesPlanProjects = salesPlanProjectMapper.select(build);
            List<Integer> projectIdList = salesPlanProjects.stream().filter(item -> item.getProjectId() != null)
                    .map(item -> item.getProjectId()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(projectIdList)) {
                map.put("projectIdList", projectIdList);
            }
        }
        BigDecimal finishedQuantity = orderDtlDao.getFinishedQuantityByAlgorithm(map);
        salesPlan.setFinishedQuantity(finishedQuantity);
    }

    /**
     * 查询是否存在子子计划
     *
     * @param list
     */
    @Async
    public Future<Boolean> queryIsHaveChild(List<SalesPlan> list, Map<String,Object> param) {
        HashMap<String, Object> map = Maps.newHashMap(param);
        List<Integer> collect = list.stream().map(item -> item.getPlanId()).collect(Collectors.toList());
        map.remove("parentId");
        map.put("parentIds",collect);
        List<SalesPlan> select = mapper.queryIsHaveChild(map);
        for (SalesPlan salesPlan : list) {
            if (CollectionUtil.isEmpty(select)) {
                salesPlan.setHaveChild(0);//无子计划
                continue;
            }
            Iterator<SalesPlan> iterator = select.iterator();
            while (iterator.hasNext()) {
                SalesPlan next = iterator.next();
                if (salesPlan.getPlanId().equals(next.getParentId())) {
                    salesPlan.setHaveChild(1);//有子计划
                    iterator.remove();
                    break;
                }
            }
            if (salesPlan.getHaveChild() == null) salesPlan.setHaveChild(0);
        }
        return new AsyncResult<>(true);
    }

    /**
     * 查询已完成金额
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> getFinishedAmount(List<SalesPlan> list) {
        if (CollectionUtil.isEmpty(list)) return new AsyncResult<>(true);
        List<Integer> collect = list.stream().map(item -> item.getPlanId()).collect(Collectors.toList());
        List<SalesPlan> amountList = mapper.getFinishedAmountByPlanIds(collect);
        Map<Integer, BigDecimal> decimalMap = amountList.stream()
                .collect(Collectors.toMap(item -> item.getPlanId(), item -> item.getFinishedAmount(), (key1, key2) -> key1));
        for (SalesPlan salesPlan : list) {
            salesPlan.setFinishedAmount(decimalMap.get(salesPlan.getPlanId()));
        }
        return new AsyncResult<>(true);
    }

    @Async
    public Future<SalesPlan> selectById(Integer planId, Date endDate) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("planId", planId);
        List<SalesPlan> list = mapper.selectByMap(map);
        if (CollectionUtil.isEmpty(list)) return new AsyncResult<>(null);
        SalesPlan salesPlan = list.get(0);
        //查询完成金额
        BigDecimal finishedAmountByPlanId = mapper.getFinishedAmountByPlanId(planId, endDate);
        salesPlan.setFinishedAmount(finishedAmountByPlanId);
        //查询完成数量
        getFinishedQuantityBase(salesPlan, endDate);
        return new AsyncResult<>(salesPlan);
    }
}
