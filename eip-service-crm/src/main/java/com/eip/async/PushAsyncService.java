package com.eip.async;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.eip.common.constant.CommonConstant;
import com.eip.common.enums.*;
import com.eip.common.push.eastFair.EastFairSaveResponse;
import com.eip.common.push.eastFair.EastFairUtils;
import com.eip.common.util.PushUtils;
import com.eip.common.util.RedisUtil;
import com.eip.common.util.http.HttpClientHelper;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.service.CertificateService;
import com.eip.service.crm.orgdao.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.*;

@Component
public class PushAsyncService {

    private Log logger = LogFactory.getLog(PushAsyncService.class);

    @Autowired
    private BuyerRegMapper buyerRegMapper;
    @Autowired
    private PushApiMapper pushApiMapper;
    @Autowired
    private PushFieldMapper pushFieldMapper;
    @Autowired
    private BuyerReserveEntranceTimeMapper buyerReserveEntranceTimeMapper;
    @Autowired
    private CertificateService certificateService;
    @Autowired
    private BuyerCertificateMapper buyerCertificateMapper;
    @Autowired
    private RedisUtil redisUtil;


    public String Md5(String str){
        return  MD5.create().digestHex(str);
    }

    /*@Async
    public Future<Boolean> push(BuyerReg buyerReg, String dataType, String pushNode, boolean retry) {
        List<BuyerReg> list = Lists.newArrayList(buyerReg);
        pushData(list,dataType,pushNode,retry);
        return new AsyncResult<>(true);
    }

    @Async
    public Future<Boolean> push(List<BuyerReg> list,String dataType,String pushNode,boolean retry) {
        if(list!=null&&list.size()>0)pushData(list,dataType,pushNode,retry);
        return new AsyncResult<>(true);
    }*/
    /**
     *
     * @param list  数据list
     * @param dataType 数据类型
     * @param pushNode 推送节点   为空代表手动推送
     * @param retry
     */
    private void pushData(List<BuyerReg> list,String dataType,String pushNode,boolean retry) {
        if(CollectionUtils.isEmpty(list)||StringUtils.isBlank(dataType))return;

        Map<String,List<PushApi>> pushApiMap = Maps.newHashMap();

        Map<String,List<PushField>> pushFieldMap = Maps.newHashMap();

        for(BuyerReg bean:list){
            if(bean==null||bean.getProjectId()==null)continue;
            String key = bean.getProjectId() + "_" + dataType+ "_" + bean.getTargetType() + "_" + pushNode;
            List<PushApi> apiList = pushApiMap.get(key);
            if(apiList==null){
                Map<String,Object> param = Maps.newHashMap();
                param.put("projectId", bean.getProjectId());
                param.put("dataType", dataType);
                param.put("isOpen", true);//开启的
                param.put("type", BuyerApiTypeEnum.PUSH.getCode());//推送接口
                //param.put("targetType", bean.getTargetType());
                if (!PushDataTypeEnum.CERT.getCode().equals(dataType)) param.put("targetType", bean.getTargetType());
                if(StringUtils.isNotBlank(pushNode))param.put(pushNode, true);
                apiList = pushApiMapper.selectByProjectAndDataType(param);
                pushApiMap.put(key, apiList);
            }

            Integer success = 1;
            String result = "";
            int  undefinedCount = 0;

            if(CollectionUtils.isEmpty(apiList)){
                if(StringUtils.isNotBlank(pushNode))continue;//非手动推送的，没开启推送不记录状态
                else {
                    success = 0;
                    result = "未开启推送";
                }
            }else{
                for(PushApi api:apiList){
                    String fKey = api.getApiId() + "_" + dataType + "_" + bean.getTargetType();
                    List<PushField> pushFieldList = pushFieldMap.get(fKey);
                    if(pushFieldList==null){
                        PushField pushField = new PushField();
                        pushField.setApiId(api.getApiId());
                        pushField.setDataType(dataType);
                        //pushField.setTargetType(bean.getTargetType());
                        if (!PushDataTypeEnum.CERT.getCode().equals(dataType)) pushField.setTargetType(bean.getTargetType());
                        pushFieldList = pushFieldMapper.select(pushField);
                        //没有设置 给予默认字段
                        if(pushFieldList.size()==0){
                            pushField.setFieldCode(BuyerPushFieldEnum.F_BAR_CODE.getCode());
                            pushField.setIsPush(true);
                            pushFieldList.add(pushField);
                        }
                        pushFieldMap.put(fKey, pushFieldList);
                    }

                    if(PushApiTypeEnum.HUI_ZHAN.getCode().equals(api.getApiType())){ //推送到慧展的接口
                        Map<String, Object> huiZhanPush = huiZhanPush(api,pushFieldList,bean,retry);
                        if((int)huiZhanPush.get("state")!=1){
                            success = 0;
                            result += "|" + api.getApiType()+" : " + (String)huiZhanPush.get("result") + "|";
                        }
                    }else if(PushApiTypeEnum.ZHAN_CHUANG.getCode().equals(api.getApiType())){ //推送到展创的接口
                        Map<String, Object> zhanChuangPush = zhanChuangPush(api,pushFieldList,bean,retry);
                        if((int)zhanChuangPush.get("state")!=1){
                            success = 0;
                            result += "|" + api.getApiType()+" : " + (String)zhanChuangPush.get("result") + "|";
                        }
                    }else if(PushApiTypeEnum.TONG_GAO.getCode().equals(api.getApiType())){ //推送到同高的接口
                        Map<String, Object> tongGaoPush = tongGaoPush(api,pushFieldList,bean,retry);
                        if((int)tongGaoPush.get("state")!=1){
                            success = 0;
                            result += "|" + api.getApiType()+" : " + (String)tongGaoPush.get("result") + "|";
                        }
                    }else if(PushApiTypeEnum.DONNOR.getCode().equals(api.getApiType())){ //推送到德纳的接口
                        Map<String, Object> tongGaoPush = donnorPush(api,pushFieldList,bean,retry);
                        if((int)tongGaoPush.get("state")!=1){
                            success = 0;
                            result += "|" + api.getApiType()+" : " + (String)tongGaoPush.get("result") + "|";
                        }
                    }else if (PushApiTypeEnum.RONG_FENG.getCode().equals(api.getApiType())) { //推送到荣峰的接口
                        Map<String, Object> pushResult = rongFengPush(api, pushFieldList, bean, retry);
                        if ((int) pushResult.get("state") != 1) success = 0;
                        result += "|" + api.getApiType() + " : " + pushResult.get("result") + "|";
                    }else if (PushApiTypeEnum.RONG_FENG_2023.getCode().equals(api.getApiType())) { //推送到荣峰2023的接口
                        Map<String, Object> pushResult = rongFeng2023Push(api, pushFieldList, bean);
                        if ((int) pushResult.get("state") != 1) success = 0;
                        result += "|" + api.getApiType() + " : " + pushResult.get("result") + "|";
                    } else if (PushApiTypeEnum.MEHZ.getCode().equals(api.getApiType())) { //推送到MEHZ的接口
                        Map<String, Object> pushResult = pushMEHZ(api, pushFieldList, bean);
                        if ((int) pushResult.get("state") != 1) success = 0;
                        result += "|" + api.getApiType() + " : " + pushResult.get("result") + "|";
                    } else if (PushApiTypeEnum.EAST_FAIR.getCode().equals(api.getApiType())) {//昆仑亿发
                        Map<String, Object> pushResult = pushEastFair(api, pushFieldList, bean);
                        if ((int) pushResult.get("state") != 1) success = 0;
                        result += "|" + api.getApiType() + " : " + pushResult.get("result") + "|";
                    }else{
                        undefinedCount ++;
                    }
                }
                if(undefinedCount==apiList.size()){
                    success = null;
                }
            }
            if(result.length()>250)result = result.substring(0, 250);
            /*BuyerReg param = new BuyerReg();
            param.setBuyerId(bean.getBuyerId());
            param.setDataPush(success);
            param.setDataPushMemo(result);
            buyerRegMapper.updateByPrimaryKeySelective(param);*/
            if (PushDataTypeEnum.BUYER.getCode().equals(dataType) && bean.getBuyerCertificateId() != null) {
                //查询门票证件
                BuyerCertificate buyerCertificate = new BuyerCertificate();
                buyerCertificate.setBuyerCertificateId(bean.getBuyerCertificateId());
                buyerCertificate.setDataPush(success);
                buyerCertificate.setDataPushMemo(result);
                buyerCertificateMapper.updateByPrimaryKeySelective(buyerCertificate);
            } else {
                BuyerReg param = new BuyerReg();
                param.setBuyerId(bean.getBuyerId());
                param.setDataPush(success);
                param.setDataPushMemo(result);
                buyerRegMapper.updateByPrimaryKeySelective(param);
            }
        }

    }

    /**
     * 是否有可以推送的字段
     * @param pushFieldList
     * @return
     */
    public boolean havePushField(List<PushField> pushFieldList){
        boolean flag = false;
        for(PushField pushField:pushFieldList){
            if(BooleanUtils.isTrue(pushField.getIsPush())){
                flag = true;
                break;
            }
        }
        return flag;
    }


    /**
     * 推送到慧展
     * @param api
     * @param buyer
     * @param retry
     * @return
     */
    private Map<String, Object> huiZhanPush(PushApi api,List<PushField> pushFieldList,BuyerReg buyer,boolean retry) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if(StringUtils.isBlank(api.getUrl())){
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if(StringUtils.isBlank(api.getApiProjectId())){
            returnMap.put("state", 2);
            returnMap.put("result", "apiProjectId未配置");
            return returnMap;
        }
        if(!havePushField(pushFieldList)){
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        Map<String,Object> map =  new HashMap<>();
        map.put("projectId", api.getApiProjectId());//项目 ID，慧展平台定义给到第三方
        map.put("attendeeId", buyer.getBarCode()); //证件 ID，第三方平台定义，项目唯一，推送相同证件 ID 更新数据
        if("E".equals(buyer.getType()))map.put("typeName", "展商");  //证件类型，用于打印不同模板证件
        else map.put("typeName", "观众");
        for(PushField bean:pushFieldList){
            if(BooleanUtils.isNotTrue(bean.getIsPush()))continue;
            if(BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                map.put("badgeNumber", buyer.getBarCode());
            else if(BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("name", buyer.getBuyerName());
            else if(BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("company", buyer.getBuyerCompany());
            else if(BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("position", buyer.getPosition());
            else if(BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("phone", buyer.getMobile());
            else if(BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode()))
                map.put("idCard", buyer.getIdNum());
        }
        try {
            Map<String, Object> result = null;
            if(retry){
                result = PushUtils.postJsonRetry(api.getUrl(),map);
            }else{
                result = PushUtils.postJson(api.getUrl(),map);
            }
            if(result!=null){
                if("true".equals(result.get("success").toString())){
                    returnMap.put("state", 1);
                    returnMap.put("result",null);
                }else{
                    returnMap.put("result", result.get("pushResult"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnMap;
    }
    /**
     * 推送到展创
     * @param api
     * @param buyer
     * @param retry
     * @return
     */
    private Map<String, Object>  zhanChuangPush(PushApi api,List<PushField> pushFieldList,BuyerReg buyer,boolean retry) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if(StringUtils.isBlank(api.getUrl())){
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if(!havePushField(pushFieldList)){
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        Map<String,Object> map =  new HashMap<>();
        for(PushField bean:pushFieldList){
            if(BooleanUtils.isNotTrue(bean.getIsPush()))continue;
            if(BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                map.put("code", buyer.getBarCode());
            else if(BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode()))
                map.put("shfenz", buyer.getIdNum());
        }
        try {
            Map<String, Object> result = null;
            if(retry){
                result = PushUtils.postRetry(api.getUrl(),map);
            }else{
                result = PushUtils.post(api.getUrl(),map);
            }
            if(result!=null){
                if(Integer.parseInt(result.get("code").toString())==200){
                    returnMap.put("state", 1);
                    returnMap.put("result",null);
                }else{
                    returnMap.put("result", result.get("pushResult"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnMap;
    }


    /**
     * 推送到德纳
     * @param api
     * @param buyer
     * @param retry
     * @return
     */
    private Map<String, Object>  donnorPush(PushApi api,List<PushField> pushFieldList,BuyerReg buyer,boolean retry){
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if(StringUtils.isBlank(api.getUrl())){
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if(!havePushField(pushFieldList)){
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        Map<String,Object> map =  new HashMap<>();
        map.put("Timestamp", buyer.getRegTime()==null?null:buyer.getRegTime().getTime()/1000);
        //map.put("exhibitcode", buyer.getExhibitCode());
        map.put("Exhibitcode", buyer.getExhibitCode());
        map.put("ProjectID", buyer.getProjectId());

        String f_channel_type_name = buyer.getChannelTypeName();
        String f_channel_name = buyer.getChannelName();

        if(StringUtils.isBlank(f_channel_type_name)&&(buyer.getChannelType()==null||buyer.getChannelType()==1))
            f_channel_type_name = "主办方";

        if(StringUtils.isBlank(f_channel_type_name)||(StringUtils.isBlank(f_channel_name)&&buyer.getChannelId()!=null)){
            Map<String, Object> param = Maps.newHashMap();
            param.put("buyerId", buyer.getBuyerId());
            List<BuyerReg> list = buyerRegMapper.selectByMap(param);
            if(CollectionUtils.isNotEmpty(list)){
                f_channel_type_name = list.get(0).getChannelTypeName();
                f_channel_name = list.get(0).getChannelName();
            }
        }
        map.put("ResourceType", f_channel_type_name);
        map.put("ResourceName", f_channel_name);

        for(PushField bean:pushFieldList){
            if(BooleanUtils.isNotTrue(bean.getIsPush()))continue;
            if(BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                map.put("Barcode", buyer.getBarCode());
            else if(BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("Name", buyer.getBuyerName());
            else if(BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("Company", buyer.getBuyerCompany());
            else if(BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("Position", buyer.getPosition());
            else if(BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("Phone", buyer.getMobile());
            else if(BuyerPushFieldEnum.F_EMAIL.getCode().equals(bean.getFieldCode()))
                map.put("Email", buyer.getEmail());
            else if(BuyerPushFieldEnum.F_COUNTRY.getCode().equals(bean.getFieldCode()))
                map.put("Country", buyer.getCountry());
            else if(BuyerPushFieldEnum.F_PROVINCE.getCode().equals(bean.getFieldCode()))
                map.put("Province", buyer.getProvince());
            else if(BuyerPushFieldEnum.F_CITY.getCode().equals(bean.getFieldCode()))
                map.put("City", buyer.getCity());
            else if(BuyerPushFieldEnum.F_DISTRICT.getCode().equals(bean.getFieldCode()))
                map.put("Area", buyer.getDistrict());
            else if(BuyerPushFieldEnum.F_ADDRESS.getCode().equals(bean.getFieldCode()))
                map.put("Address", buyer.getAddress());
            else if(BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode()))
                map.put("IDCard", buyer.getIdNum());
        }
        try {
            Map<String, Object> result = null;
            /*if(retry){
                result = PushUtils.postJsonRetry(api.getUrl(),map);
            }else{
                result = PushUtils.postJson(api.getUrl(),map);
            }*/
            if(retry){
                result = PushUtils.getRetry(api.getUrl(),map);
            }else{
                result = PushUtils.get(api.getUrl(),map);
            }
            if(result!=null){
                if("\"1\"".equals(result.get("status").toString())||"\"0\"".equals(result.get("status").toString())){
                    returnMap.put("state", 1);
                    returnMap.put("result",null);
                }else{
                    returnMap.put("result", result.get("pushResult"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnMap;
    }




    /**
     * 推送到同高2 (新接口)
     * @param api
     * @param buyer
     * @param retry
     * @return
     */
    private Map<String, Object>  tongGaoPush(PushApi api,List<PushField> pushFieldList,BuyerReg buyer,boolean retry) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if(StringUtils.isBlank(api.getUrl())){
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if(StringUtils.isBlank(api.getAccount())){
            returnMap.put("state", 2);
            returnMap.put("result", "account未配置");
            return returnMap;
        }
        if(StringUtils.isBlank(api.getPassword())){
            returnMap.put("state", 2);
            returnMap.put("result", "password未配置");
            return returnMap;
        }
        if(!havePushField(pushFieldList)){
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        Map<String, Object> map = Maps.newHashMap();

        long timestamp = System.currentTimeMillis()/1000;
        String nonce = UUID.randomUUID().toString();
        //String sign = MD5.create().digestHex(api.getAccount()+timestamp + nonce + api.getPassword());
		/*map.put("appid", api.getAccount());  //id
		map.put("sign", sign); //签名
		map.put("nonce", nonce);  //随机字符串
		map.put("timestamp", timestamp+""); //时间戳
*/
        //签名
        String sign = MD5.create().digestHex(api.getAccount()+ api.getPassword() + timestamp + nonce );
        //请求URL带参数
        StringBuffer sbf = new StringBuffer(api.getUrl());
        sbf.append("?appid=").append(api.getAccount()).append("&timestamp=").append(timestamp).
                append("&nonce=").append(nonce).append("&sign=").append(sign);
        if("1".equals(api.getMock()))sbf.append("&mock=1");
        String url = sbf.toString();
        //请求body参数
        map.put("ProjectId", buyer.getExhibitCode());  //展会代号

        if (StringUtils.isNotBlank(api.getApiCertType())) {//如果设置的有身份类型，则取设置的类型
            map.put("Type", api.getApiCertType());
        }
        if(map.get("Type")==null){
            if("E".equals(buyer.getType()))map.put("Type", "展商");  //类型
            else map.put("Type", "观众");
        }

        for(PushField bean:pushFieldList){
            if(BooleanUtils.isNotTrue(bean.getIsPush()))continue;
            if(BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                map.put("Code", buyer.getBarCode()); //唯一号（胸卡号）
            else if(BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("Name", buyer.getBuyerName()); //姓名
            else if(BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("CompanyName", buyer.getBuyerCompany()); //公司名称
            else if(BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("Title", buyer.getPosition()); //职务
            else if(BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("Mobile", buyer.getMobile()); //手机号
            else if(BuyerPushFieldEnum.F_EMAIL.getCode().equals(bean.getFieldCode()))
                map.put("Email", buyer.getEmail());
            else if(BuyerPushFieldEnum.F_COUNTRY.getCode().equals(bean.getFieldCode()))
                map.put("Country", buyer.getCountry());
            else if(BuyerPushFieldEnum.F_PROVINCE.getCode().equals(bean.getFieldCode()))
                map.put("Province", buyer.getProvince());//省份
            else if(BuyerPushFieldEnum.F_CITY.getCode().equals(bean.getFieldCode()))
                map.put("City", buyer.getCity());//城市
            else if(BuyerPushFieldEnum.F_DEPARTMENT.getCode().equals(bean.getFieldCode()))
                map.put("Department", buyer.getDepartment());
            else if(BuyerPushFieldEnum.F_SEX.getCode().equals(bean.getFieldCode()))
                map.put("Gender", buyer.getSex());
            else if(BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode())){
                if(StringUtils.isNotBlank(buyer.getIdNum())){
                    //String f_id_type = StringUtils.isBlank(buyer.getIdType())?"身份证":buyer.getIdType();
                    String f_id_type = StringUtils.isBlank(buyer.getIdType()) ? BuyerIdTypeEnum.IDENTITY_CARD.getName()
                            : BuyerIdTypeEnum.getName(buyer.getIdType());
                    map.put("IDType", f_id_type); //证件类型
                    if(BooleanUtils.isTrue(bean.getEncrypt())){ //是否加密
                        String f_id_num = buyer.getIdNum();
                        String upperCase = Md5(Md5(f_id_num.toUpperCase()) + Md5(new StringBuilder(f_id_num).reverse().toString().toUpperCase())).toUpperCase();
                        map.put("IDCard", upperCase); //身份证号
                    }else{
                        map.put("IDCard", buyer.getIdNum()); //身份证号
                    }

                }
            }
        }

        List<Map<String,Object>> list = new ArrayList<>();
        list.add(map);
        try {
            Map<String, Object> result = null;
            if(retry){
                result = PushUtils.postJsonRetry(url,list);
            }else{
                result = PushUtils.postJson(url,list);
            }
            if(result!=null){
                if(!"0".equals(result.get("code").toString())){
                    returnMap.put("result", result.get("pushResult").toString());
                }else{
                    returnMap.put("state", 1);
                    returnMap.put("result", null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnMap;
    }

    /**
     * 荣峰 接口
     * @param api
     * @param pushFieldList
     * @param buyer
     * @param retry
     * @return
     */
    private Map<String, Object> rongFengPush(PushApi api, List<PushField> pushFieldList, BuyerReg buyer, boolean retry) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if (StringUtils.isBlank(api.getUrl())) {
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if (!havePushField(pushFieldList)) {
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        //Map<String, Object> map = Maps.newHashMap();

        StringBuffer sbf = new StringBuffer(api.getUrl());

        for (PushField bean : pushFieldList) {
            if (BooleanUtils.isNotTrue(bean.getIsPush())) continue;
            if (BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                //map.put("code", buyer.getBarCode()); //唯一号（胸卡号）
                sbf.append("?code=").append(buyer.getBarCode());
            else if (BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode())) {
                if (StringUtils.isNotBlank(buyer.getIdNum())) {
                    String f_id_type = StringUtils.isBlank(buyer.getIdType()) ? BuyerIdTypeEnum.IDENTITY_CARD.getName()
                            : BuyerIdTypeEnum.getName(buyer.getIdType());
                    //map.put("IDType", f_id_type); //证件类型
                    //map.put("IDCardNum", buyer.getIdNum()); //证件号
                    String f_id_num = buyer.getIdNum();
                    try {
                        if (StringUtils.isNotBlank(f_id_type)) f_id_type = URLEncoder.encode(f_id_type, "UTF-8");
                        if (StringUtils.isNotBlank(f_id_num)) f_id_num = URLEncoder.encode(f_id_num, "UTF-8");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    sbf.append("&IDType=").append(f_id_type).append("&IDCardNum=").append(f_id_num);
                }
            }
        }
        //查询观众预约时间
        if(buyer.getBuyerId()!=null){
            String timeStr = buyerReserveEntranceTimeMapper.getTimeStrByBuyerId(buyer.getBuyerId());
            //map.put("dt", timeStr);
            try {
                if (StringUtils.isNotBlank(timeStr)) timeStr = URLEncoder.encode(timeStr, "UTF-8");
            } catch (Exception e) {
                e.printStackTrace();
            }
            sbf.append("&dt=").append(timeStr);
        }
        String url = sbf.toString();
        try {
            Map<String, Object> result = null;
            /*if (retry) {
                result = PushUtils.postRetry(api.getUrl(), map);
            } else {
                result = PushUtils.post(api.getUrl(), map);
            }*/
            result = PushUtils.postQuery(url,null);
            if (result != null) {
                if (!"\"200\"".equals(result.get("code").toString())) {
                    returnMap.put("result", result.get("pushResult").toString());
                } else {
                    returnMap.put("state", 1);
                    returnMap.put("result", result.get("pushResult").toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnMap;
    }


    /**
     * 荣峰2023 接口
     * @param api
     * @param pushFieldList
     * @param buyer
     * @return
     */
    private Map<String, Object> rongFeng2023Push(PushApi api, List<PushField> pushFieldList, BuyerReg buyer) {
        Map<String, Object> returnMap = com.beust.jcommander.internal.Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if (StringUtils.isBlank(api.getUrl())) {
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getAccount())) {
            returnMap.put("state", 2);
            returnMap.put("result", "utoken未配置");
            return returnMap;
        }
        if (!havePushField(pushFieldList)) {
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        JSONObject map = new JSONObject();
        //请求body参数
        map.put("itemId", buyer.getExhibitCode());  //展会代号
        for (PushField bean : pushFieldList) {
            if (BooleanUtils.isNotTrue(bean.getIsPush())) continue;
            if (BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode()))
                map.put("indexNum", buyer.getBarCode()); //唯一号（胸卡号）
            else if (BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("userName", buyer.getBuyerName()); //姓名
            else if (BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("busName", buyer.getBuyerCompany()); //公司名称
            else if (BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("poster", buyer.getPosition()); //职务
            else if (BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("phone", buyer.getMobile()); //手机号
            else if (BuyerPushFieldEnum.F_EMAIL.getCode().equals(bean.getFieldCode()))
                map.put("email", buyer.getEmail());
            else if (BuyerPushFieldEnum.F_DEPARTMENT.getCode().equals(bean.getFieldCode()))
                map.put("dep", buyer.getDepartment());
            else if (BuyerPushFieldEnum.F_SEX.getCode().equals(bean.getFieldCode()))
                map.put("gender", buyer.getSex());
            else if (BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode())) {
                if (StringUtils.isBlank(buyer.getIdNum())) continue;
                map.put("idCard", buyer.getIdNum()); //身份证号
                /**
                 * cn_id_card 中国居民身份证
                 * other_id_card 中华人民共和国外国人永久居民身份证
                 * hktw_id_card 港澳台居民身份证
                 * hk_cn_pass_check 港澳居民来往内地通行证
                 * tw_cn_pass_check 台湾居民来往大陆通行证
                 * passport 护照
                 */
                String f_id_type = "";
                if (StringUtils.isBlank(buyer.getIdType())) buyer.setIdType("1");//默认身份证
                switch (buyer.getIdType()) {
                    case "1":
                        f_id_type = "cn_id_card";
                        break;
                    case "2":
                        f_id_type = "passport";
                        break;
                    case "3":
                        f_id_type = "hk_cn_pass_check";
                        break;
                    case "4":
                        f_id_type = "tw_cn_pass_check";
                        break;
                }
                map.put("idType", f_id_type); //证件类型
            }
        }
        try {
            String body = HttpRequest.post(api.getUrl())
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("Accept", "application/json")
                    .header("utoken", api.getAccount())
                    .body(map.toString()).timeout(3000).execute().body();
            JSONObject resultJson = JSONObject.parseObject(body);
            returnMap.put("result", body);
            if ("true".equals(resultJson.get("code").toString())) {
                returnMap.put("state", 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnMap;
    }

    private Map<String, Object> pushMEHZ(PushApi api, List<PushField> pushFieldList, BuyerReg buyer) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if (StringUtils.isBlank(api.getUrl())) {
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getAccount())) {
            returnMap.put("state", 2);
            returnMap.put("result", "account未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getPassword())) {
            returnMap.put("state", 2);
            returnMap.put("result", "password未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getApiProjectId())) {
            returnMap.put("state", 2);
            returnMap.put("result", "apiProjectId未配置");
            return returnMap;
        }
        if (!havePushField(pushFieldList)) {
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        //请求body参数
        JSONObject map = new JSONObject();
        map.put("channelNo", api.getApiChannelId());//渠道号，主办方提供
        map.put("meetingCode", api.getApiProjectId());  //会议code，主办方提供
        if ("E".equals(buyer.getType())) map.put("identity", "EXHIBITOR");  //注册类型
        else map.put("identity", "AUDIENCE");

        String f_channel_type_name = buyer.getChannelTypeName();
        String f_channel_name = buyer.getChannelName();
        if (StringUtils.isBlank(f_channel_type_name) && (buyer.getChannelType() == null || buyer.getChannelType() == 1))
            f_channel_type_name = "主办方";
        if (StringUtils.isBlank(f_channel_type_name) || (StringUtils.isBlank(f_channel_name) && buyer.getChannelId() != null)) {
            Map<String, Object> param = Maps.newHashMap();
            param.put("buyerId", buyer.getBuyerId());
            List<BuyerReg> list = buyerRegMapper.selectByMap(param);
            if (CollectionUtils.isNotEmpty(list)) {
                f_channel_type_name = list.get(0).getChannelTypeName();
                f_channel_name = list.get(0).getChannelName();
            }
        }
        map.put("remark", f_channel_type_name + (f_channel_name == null ? "" : "+" + f_channel_name));
        for (PushField bean : pushFieldList) {
            if (BooleanUtils.isNotTrue(bean.getIsPush())) continue;
            if (BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode())) {
                //map.put("indexNum", buyer.getBarCode()); //唯一号（胸卡号）
            } else if (BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("name", buyer.getBuyerName()); //姓名
            else if (BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("company", buyer.getBuyerCompany()); //公司名称
            else if (BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("position", buyer.getPosition()); //职务
            else if (BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("phone", buyer.getMobile()); //手机号
			/*else if (BuyerPushFieldEnum.F_EMAIL.getCode().equals(bean.getFieldCode()))
				map.put("email", buyer.getF_email());*/
            else if (BuyerPushFieldEnum.F_DEPARTMENT.getCode().equals(bean.getFieldCode()))
                map.put("department", buyer.getDepartment());
			/*else if (BuyerPushFieldEnum.F_SEX.getCode().equals(bean.getFieldCode()))
				map.put("gender", buyer.getF_sex());*/
            else if(BuyerPushFieldEnum.F_COUNTRY.getCode().equals(bean.getFieldCode()))
                map.put("country", buyer.getCountry());
            else if(BuyerPushFieldEnum.F_PROVINCE.getCode().equals(bean.getFieldCode()))
                map.put("province", buyer.getProvince());
            else if(BuyerPushFieldEnum.F_CITY.getCode().equals(bean.getFieldCode()))
                map.put("city", buyer.getCity());
            else if(BuyerPushFieldEnum.F_DISTRICT.getCode().equals(bean.getFieldCode()))
                map.put("area", buyer.getDistrict());
            else if(BuyerPushFieldEnum.F_ADDRESS.getCode().equals(bean.getFieldCode()))
                map.put("street", buyer.getAddress());
            else if (BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode())) {
                if (StringUtils.isBlank(buyer.getIdNum())) continue;
                map.put("idNo", buyer.getIdNum()); //身份证号
                /**
                 * 证件类型 身份证1，港澳居民来往内地通行证2，台湾居民来往大陆通行证3，外国人永久居住证4，护照5
                 */

                String f_id_type = "";
                if (StringUtils.isBlank(buyer.getIdType())) buyer.setIdType("1");//默认身份证
                switch (buyer.getIdType()) {
                    case "1":
                        f_id_type = "1";
                        break;
                    case "2":
                        f_id_type = "5";
                        break;
                    case "3":
                        f_id_type = "2";
                        break;
                    case "4":
                        f_id_type = "3";
                        break;
                }
                map.put("idType", f_id_type); //证件类型
            }
        }
        //System.out.println(map.toString());
        //时间戳
        long timestamp = System.currentTimeMillis();
        //签名
        String sign = MD5.create().digestHex(api.getAccount() + timestamp + api.getPassword());

        try {
            HttpResponse execute = HttpRequest.post(api.getUrl())
                    .header("Content-Type", "application/json")
                    .header("charset", "utf-8")
                    .header("client_id", api.getAccount())
                    .header("sign", sign)
                    .header("timestamp", timestamp + "")
                    .body(map.toString()).timeout(3000).execute();
            int status = execute.getStatus();
            String body = execute.body();
            returnMap.put("result", body);
            if (status == 200) {
                JSONObject resultJson = JSONObject.parseObject(body);
                if ((int) resultJson.get("code") == 200){
                    JSONObject data = JSONObject.parseObject(resultJson.get("data").toString());
                    if ("true".equals(data.get("registerStatus").toString())) {
                        returnMap.put("state", 1);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnMap;
    }


    /**
     * 推送昆仑亿发
     * @param api
     * @param pushFieldList
     * @param buyer
     * @return
     */
    private Map<String, Object> pushEastFair(PushApi api, List<PushField> pushFieldList, BuyerReg buyer) {
        Map<String, Object> returnMap = Maps.newHashMap();
        returnMap.put("state", 0);
        returnMap.put("result", "请求失败");
        if (StringUtils.isBlank(api.getUrl())) {
            returnMap.put("state", 2);
            returnMap.put("result", "url未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getAccount())) {
            returnMap.put("state", 2);
            returnMap.put("result", "应用id未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getPassword())) {
            returnMap.put("state", 2);
            returnMap.put("result", "应用密钥未配置");
            return returnMap;
        }
        if (StringUtils.isBlank(api.getApiProjectId())) {
            returnMap.put("state", 2);
            returnMap.put("result", "展会id未配置");
            return returnMap;
        }

        if (!havePushField(pushFieldList)) {
            returnMap.put("state", 2);
            returnMap.put("result", "没有可以推送的字段");
            return returnMap;
        }
        //请求body参数
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("exhibitionId", api.getApiProjectId());
        //数据来源（数据来源由数据提供⽅⾃⾏定义，限 10 字符以内）
        jsonBody.put("sourceFrom", EastFairUtils.SOURCE_FROM);
        jsonBody.put("exhibitionIdType", 0);//展会类型（默认传 0)

        Map<String, Object> map = Maps.newLinkedHashMap();
        map.put("ticketStatus", 0);//票证状态 0 正常   1 禁用
        if ("E".equals(buyer.getType())) map.put("ticketTypeName", "展商证");
        else map.put("ticketTypeName", "观众证");
        for (PushField bean : pushFieldList) {
            if (BooleanUtils.isNotTrue(bean.getIsPush())) continue;
            if (BuyerPushFieldEnum.F_BAR_CODE.getCode().equals(bean.getFieldCode())) {
                map.put("cardNo", buyer.getBarCode()); //唯一号（胸卡号）
            } else if (BuyerPushFieldEnum.F_BUYER_NAME.getCode().equals(bean.getFieldCode()))
                map.put("name", buyer.getBuyerName()); //姓名
            else if (BuyerPushFieldEnum.F_BUYER_COMPANY.getCode().equals(bean.getFieldCode()))
                map.put("company", buyer.getBuyerCompany()); //公司名称
            else if (BuyerPushFieldEnum.F_POSITION.getCode().equals(bean.getFieldCode()))
                map.put("title", buyer.getPosition()); //职务
            else if (BuyerPushFieldEnum.F_MOBILE.getCode().equals(bean.getFieldCode()))
                map.put("mobile", buyer.getMobile()); //手机号
			/*else if (BuyerPushFieldEnum.F_EMAIL.getCode().equals(bean.getFieldCode()))
				map.put("email", buyer.getF_email());*/
            /*else if (BuyerPushFieldEnum.F_DEPARTMENT.getCode().equals(bean.getFieldCode()))
                map.put("department", buyer.getDepartment());*/
			/*else if (BuyerPushFieldEnum.F_SEX.getCode().equals(bean.getFieldCode()))
				map.put("gender", buyer.getF_sex());*/
            else if (BuyerPushFieldEnum.F_COUNTRY.getCode().equals(bean.getFieldCode()))
                map.put("country", buyer.getCountry());
            else if (BuyerPushFieldEnum.F_PROVINCE.getCode().equals(bean.getFieldCode()))
                map.put("province", buyer.getProvince());
            /*else if(BuyerPushFieldEnum.F_CITY.getCode().equals(bean.getFieldCode()))
                map.put("city", buyer.getCity());
            else if(BuyerPushFieldEnum.F_DISTRICT.getCode().equals(bean.getFieldCode()))
                map.put("area", buyer.getDistrict());
            else if(BuyerPushFieldEnum.F_ADDRESS.getCode().equals(bean.getFieldCode()))
                map.put("street", buyer.getAddress());*/
            else if (BuyerPushFieldEnum.F_ID_NUM.getCode().equals(bean.getFieldCode())) {
                if (StringUtils.isBlank(buyer.getIdNum())) continue;
                map.put("idNumber", buyer.getIdNum()); //身份证号
                /**
                 * 证件类型
                 * 1 - 身份证
                 * 2 - 港澳通⾏证
                 * 3 - 台胞证
                 * 4 - 外国护照
                 * 5 - 外国永久身份证
                 * 6 - 华侨护照
                 */

                String f_id_type = "";
                if (StringUtils.isBlank(buyer.getIdType())) buyer.setIdType("1");//默认身份证
                switch (buyer.getIdType()) {
                    case "1":
                        f_id_type = "1";
                        break;
                    case "2":
                        f_id_type = "4";
                        break;
                    case "3":
                        f_id_type = "2";
                        break;
                    case "4":
                        f_id_type = "3";
                        break;
                    default:
                        f_id_type = "4"; //和对方技术约定，其他类型也给4
                }
                map.put("numberType", f_id_type); //证件类型
            }
        }
        jsonBody.put("ticketList", Lists.newArrayList(map));

        Map<String, Object> tokenMap = getEastFairToken(api.getUrl(), api.getAccount(), api.getPassword());
        if ((int) tokenMap.get("state") == 0) {
            returnMap.put("result", tokenMap.get("msg"));
            return returnMap;
        }
        String token = (String) tokenMap.get("token");

        try {
            HttpResponse execute = pushEastFair(api.getUrl(), token, jsonBody);
            int status = execute.getStatus();
            String body = execute.body();
            returnMap.put("result", body);
            if (status == 200) {
                Gson gson = new Gson();
                EastFairSaveResponse response = gson.fromJson(body, EastFairSaveResponse.class);
                returnMap.put("result", gson.toJson(response));
                if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getSussesList())) {
                    returnMap.put("state", 1);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnMap;
    }

    private HttpResponse pushEastFair(String url, String token, JSONObject param) {
        HttpResponse execute = HttpRequest.post(url + EastFairUtils.SAVE_URL)
                .header("Content-Type", "application/json")
                .header("charset", "utf-8")
                .header("Blade-Auth", "bearer " + token)
                .body(param.toString())
                .timeout(3000).execute();
        return execute;
    }

    private Map<String,Object> getEastFairToken(String url, String client_id, String client_secret) {
        String token = null;
        String key = EastFairUtils.getRedisKey(client_secret, client_id);
        try {
            token = (String) redisUtil.get(key);
        } catch (Exception e) {
            logger.error("推送观众数据getToken时redis异常：{}", e);
        }
        if (StringUtils.isNotBlank(token)) {
            return buildEastFairTokenResult(1, token, null);
        }
        try {
            Map<String, Object> map = Maps.newHashMap();
            map.put("grant_type", EastFairUtils.GRANT_TYPE);
            map.put("client_secret", client_secret);
            map.put("client_id", client_id);
            HttpResponse execute = HttpRequest.post(url + EastFairUtils.TOKEN_URL)
                    .header("charset", "utf-8")
                    .form(map)
                    .timeout(3000).execute();
            int status = execute.getStatus();
            String body = execute.body();
            if (status == 200) {
                JSONObject resultJson = JSONObject.parseObject(body);
                token = (String) resultJson.get("access_token");
                Integer expires_in = (Integer) resultJson.get("expires_in");
                if (StringUtils.isBlank(token) || expires_in == null) {
                    logger.info("获取昆仑亿发token返回数据不正确：" + body);
                    return buildEastFairTokenResult(0, null, "获取token返回数据不正确");
                }
                try {
                    if (expires_in != null) expires_in = (expires_in - 120) > 0 ? (expires_in - 120) : expires_in;
                    redisUtil.set(key, token, expires_in);
                }catch (Exception e){
                    logger.error("推送观众数据setToken时redis异常：{}", e);
                }
                return buildEastFairTokenResult(1, token, null);
            } else {
                logger.info("获取昆仑亿发token失败：" + body);
                return buildEastFairTokenResult(0, null, body);
            }
        } catch (Exception e) {
            logger.info("获取昆仑亿发token失败：{} " + e);
        }
        return buildEastFairTokenResult(0, null, "获取token失败");
    }

    private Map<String, Object> buildEastFairTokenResult(Integer state, String token, String msg) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("state", state);
        result.put("token", token);
        result.put("msg", msg);
        return result;
    }

    @Async
    public void pushYZX(String requestIntegralUrl, Integer buyerId, Long certificateId) {
        if (StringUtils.isBlank(requestIntegralUrl) || buyerId == null) {
            logger.warn("推送易之行缺少参数：requestIntegralUrl = " + requestIntegralUrl + "，buyerId = " + buyerId);
            return;
        }
        try {
            Map<String, Object> map = Maps.newHashMap();
            map.put("buyerId", buyerId);
            map.put("certificateId", certificateId);
            String url = requestIntegralUrl + CommonConstant.BUYER_PUSH_YZX_URL;
            HttpClientHelper.sendIntegralHttpPost(url, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Async
    public void push(String requestIntegralUrl, Long buyerCertificateId, String pushNode) {
        if (StringUtils.isBlank(requestIntegralUrl) || buyerCertificateId == null || StringUtils.isBlank(pushNode)) {
            logger.warn("推送缺少参数：requestIntegralUrl = " + requestIntegralUrl + "，buyerCertificateId = " + buyerCertificateId
                    + "，pushNode = " + pushNode);
            return;
        }
        try {
            Map<String, Object> map = com.beust.jcommander.internal.Maps.newHashMap();
            map.put("buyerCertificateId", buyerCertificateId);
            map.put("pushNode", pushNode);
            String url = requestIntegralUrl + CommonConstant.BUYER_PUSH_URL;
            HttpClientHelper.sendIntegralHttpPost(url, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Async
    public void push(String requestIntegralUrl, Integer buyerId, Integer orgNum, String pushNode) {
        if (StringUtils.isBlank(requestIntegralUrl) || buyerId == null || orgNum == null || StringUtils.isBlank(pushNode)) {
            logger.warn("推送缺少参数：requestIntegralUrl = " + requestIntegralUrl + "，buyerId = " + buyerId
                    + "，orgNum = " + orgNum
                    + "，pushNode = " + pushNode);
            return;
        }
        try {
            Map<String, Object> map = com.beust.jcommander.internal.Maps.newHashMap();
            map.put("buyerId", buyerId);
            map.put("orgNum", orgNum);
            map.put("pushNode", pushNode);
            String url = requestIntegralUrl + CommonConstant.BUYER_PUSH_All_CERT_URL;
            HttpClientHelper.sendIntegralHttpPost(url, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Async
    public void createExhibitorRegSet(String requestIntegralUrl, Integer projectId, Integer brandProjectId) {
        if (StringUtils.isBlank(requestIntegralUrl) || projectId == null) {
            logger.warn("创建展会时同时创建参展申请设置缺少参数：requestIntegralUrl = " + requestIntegralUrl + "，projectId = " + projectId
                    + "，brandProjectId = " + brandProjectId);
            return;
        }
        try {
            Map<String, Object> map = com.beust.jcommander.internal.Maps.newHashMap();
            map.put("projectIds", projectId.toString());
            map.put("brandProjectId", brandProjectId);
            String sign = MD5.create().digestHex(CommonConstant.CIPHER_FACTOR + projectId).toUpperCase();
            map.put("sign", sign);
            String url = requestIntegralUrl + CommonConstant.CREATE_EXHIBITOR_REG_SET;
            HttpClientHelper.sendIntegralHttpPost(url, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
