package com.eip.async;

import com.eip.service.crm.zzysysmapper.CallLogMapper;
import com.eip.service.crm.zzysysmapper.DialLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @company ：杭州展之科技
 * @date ：Created in 2022-03-03 15:47
 * @description：通话记录异步方法
 */
@Component
public class CallAndDialLogAsyncService {

    @Autowired
    private CallLogMapper callLogMapper;
    @Autowired
    private DialLogMapper dialLogMapper;

    @Async
    public Future<BigDecimal> countCallTelSum(Map<String, Object> callBackTelCountMap){
        BigDecimal amount =  callLogMapper.countCallTelSum(callBackTelCountMap);
        return new AsyncResult<>(amount);
    }


    @Async
    public Future<BigDecimal> countDialLogTel(Map<String, Object> countDialTelMap) {
        BigDecimal amount =  dialLogMapper.countDialLogTel(countDialTelMap);
        return new AsyncResult<>(amount);
    }
}
