package com.eip.async;

import com.eip.common.enums.ClientTypeCodeEnum;
import com.eip.common.enums.IntegralOperatorNumEnum;
import com.eip.facade.crm.dto.IntegralDetailLogDto;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.service.IntegralDetailLogService;
import com.eip.service.crm.orgdao.ClientDao;
import com.eip.service.crm.orgdao.CltRelationDao;
import com.eip.service.crm.orgdao.ServeBoothDao;
import com.eip.service.crm.zzysysmapper.ZzyUserDao;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-05-12 17:47
 * @DESCRIPTION: 系统事件会员发放积分
 */
@Component
public class SystemSendIntegralAsyncService {

    protected Logger logger = LoggerFactory.getLogger(SystemSendIntegralAsyncService.class);

    @Autowired
    private ClientDao clientDao;
    @Autowired
    private IntegralDetailLogService detailLogService;
    @Autowired
    private CltRelationDao cltRelationDao;
    @Autowired
    private ServeBoothDao serveBoothDao;
    @Autowired
    private ZzyUserDao zzyUserDao;

    /**
     * 系统事件触犯积分
     * @param obj 传递的主体
     * @param detailLogDto 传递的实体
     */
    @Async
    public void sendIntegral(Object obj, IntegralDetailLogDto detailLogDto) {
        String operatorTypeCode = detailLogDto.getOperatorTypeCode();
        if(Objects.isNull(obj) || StringUtils.isBlank(StringUtils.trim(operatorTypeCode))) return;
        IntegralOperatorNumEnum operatorNumEnum = IntegralOperatorNumEnum.getOneEnum(operatorTypeCode);
        switch (operatorNumEnum){
            case FNRECEIPT:
                fnReceiptSendIntegral((FnReceipt)obj,detailLogDto);
                break;
            case INVITATION_RESULT:
                oppInviteSendIntegral((Opportunity)obj,detailLogDto);
                break;
            case BUYER_ENTRANCE:
            case VERIFICATION_OF_CONFERENCE_CREDENTIALS:
                buyerEntranceOrCertificateSendIntegral((BuyerReg)obj,detailLogDto);
                break;
            default:
                break;
        }
    }


    /**
     * 收款单触发发放积分
     * @param fnReceipt
     */
    private void fnReceiptSendIntegral(FnReceipt fnReceipt, IntegralDetailLogDto detailLogDto){
        try {
            if(fnReceipt.getProjectId()==null || fnReceipt.getClientId()==null
                    || fnReceipt.getAmount().compareTo(BigDecimal.ZERO) < 0) return;
            Map<String, Object> hashMap = Maps.newHashMap();
            hashMap.put("clientId",fnReceipt.getClientId());
            hashMap.put("orgNum",fnReceipt.getOrgNum());
            hashMap.put("contractProjectId",fnReceipt.getProjectId());
//            hashMap.put("clientTypeCode", ClientTypeCodeEnum.B_CLIENT.getCode());
//            List<Client> clientList = clientDao.queryClient(hashMap);
//            if(CollectionUtils.isEmpty(clientList)) return;
//            Client client = clientList.get(0);
//            if(Objects.isNull(client)) return;
            List<ServeBooth> serveBoothList = serveBoothDao.queryServeBooth(hashMap);
            if(CollectionUtils.isEmpty(serveBoothList)) return;
            ServeBooth serveBooth = serveBoothList.get(0);
            if(Objects.isNull(serveBooth)) return;
            if(serveBooth.getZzyuserId()==null || serveBooth.getZzyuserId() <= 0) return;
            detailLogDto.setZzyUserId(serveBooth.getZzyuserId());
            detailLogDto.setOrgNum(fnReceipt.getOrgNum());
            detailLogDto.setMoneyPrice(fnReceipt.getAmount());
            detailLogDto.setClientId(fnReceipt.getClientId());
            detailLogDto.setProjectId(fnReceipt.getProjectId());
            detailLogDto.setDataFrom(5);
            detailLogDto.setCommonId(String.valueOf(fnReceipt.getReceiptId()));
            detailLogService.saveZzyUserIntegral(detailLogDto);
        }catch (Exception e){
            logger.error("收款单触发发放积分异常：",e);
        }
    }

    /**
     * 联系人 邀请结果
     * @param opportunity
     * @param detailLogDto
     */
    private void oppInviteSendIntegral(Opportunity opportunity,  IntegralDetailLogDto detailLogDto){
        try {
            if(opportunity.getProjectId()==null || opportunity.getClientId()==null) return;
            Client client = clientDao.selectBaseById(opportunity.getClientId());
            if(Objects.isNull(client)) return ;
            if(StringUtils.isBlank(client.getClientTypeCode())) return ;
            Integer zzyUserId = null;
//            Map<String,Object> paramMap = Maps.newHashMap();
//            paramMap.put("orgNum",opportunity.getOrgNum());
            if(ClientTypeCodeEnum.B_CLIENT.getCode().equals(client.getClientTypeCode())){
                if(client.getZzyuserId()!=null && client.getZzyuserId() > 0){
                    zzyUserId = client.getZzyuserId();
                }else{
                    Map<String, Object> hashMap = Maps.newHashMap();
                    hashMap.put("clientId",opportunity.getClientId());
                    hashMap.put("orgNum",opportunity.getOrgNum());
                    hashMap.put("contractProjectId",opportunity.getProjectId());
                    List<ServeBooth> serveBoothList = serveBoothDao.queryServeBooth(hashMap);
                    if(CollectionUtils.isEmpty(serveBoothList)) return;
                    ServeBooth serveBooth = serveBoothList.get(0);
                    if(Objects.isNull(serveBooth)) return;
                    if(serveBooth.getZzyuserId()==null || serveBooth.getZzyuserId() <= 0) return;
                    zzyUserId = serveBooth.getZzyuserId();
                }
            }
            if(ClientTypeCodeEnum.C_LINKMAN.getCode().equals(client.getClientTypeCode())){//联系人 在查询该联系人对应的公司
//                paramMap.put("relationCode","BtoC1");
//                paramMap.put("queryLinkmanFlag",true);
//                paramMap.put("linkManId",opportunity.getClientId());
//                List<CltRelation> cltRelations = cltRelationDao.selectCltRelation(paramMap);
//                if(CollectionUtils.isEmpty(cltRelations)) return;
//                CltRelation cltRelation = cltRelations.get(0);
//                if(Objects.isNull(cltRelation)) return;
//                Integer clientId = cltRelation.getClientId();
//                if(clientId == null || clientId <=0) return;
//                client = clientDao.selectBaseById(clientId);
//                if(Objects.isNull(client)) return;
//                if(client.getZzyuserId()==null || client.getZzyuserId() <= 0) return;
                if(client.getZzyuserId()!=null && client.getZzyuserId() > 0){
                    zzyUserId = client.getZzyuserId();
                }else{
                    String mobile = client.getMobile();
                    String mobile2 = client.getMobile2();
                    String email = client.getEmail();
                    if(StringUtils.isBlank(mobile) && StringUtils.isBlank(mobile2) && StringUtils.isBlank(email)) return;
                    Map<String,Object> paramMap = Maps.newHashMap();
                    if(StringUtils.isNotBlank(mobile)){
                        paramMap.put("mobile",mobile);
                    }else{
                        if(StringUtils.isNotBlank(mobile2)){
                            paramMap.put("mobile",mobile2);
                        }
                    }
                    if(StringUtils.isNotBlank(email)){
                        paramMap.put("email",email);
                    }
                    ZzyUser zzyUser = zzyUserDao.queryZzuser(paramMap);
                    if(Objects.isNull(zzyUser)) return;
                    zzyUserId = zzyUser.getF_zzyuser_id();
                }
            }
            detailLogDto.setZzyUserId(zzyUserId);
            detailLogDto.setOrgNum(opportunity.getOrgNum());
            detailLogDto.setProjectId(opportunity.getProjectId());
            detailLogDto.setDataFrom(5);
            detailLogDto.setCommonId(String.valueOf(opportunity.getOpportunityId()));
            detailLogService.saveZzyUserIntegral(detailLogDto);
        }catch (Exception e){
            logger.error("操作联系人邀请发放积分异常：",e);
        }
    }


    /**
     * 证件核销、观众入场发放积分
     * @param buyerReg
     */
    private void buyerEntranceOrCertificateSendIntegral(BuyerReg buyerReg,IntegralDetailLogDto detailLogDto) {
        try {
            detailLogDto.setSourceType(1);
            detailLogDto.setDataFrom(5);
            detailLogDto.setBuyerRegId(buyerReg.getBuyerId());
            detailLogDto.setProjectId(buyerReg.getProjectId());
            if (buyerReg.getZzyUserId() != null && buyerReg.getZzyUserId() > 0) {
                detailLogDto.setZzyUserId(buyerReg.getZzyUserId());
            } else {
                if (buyerReg.getRegType() != null) {
                    if (1 == buyerReg.getRegType()) {
                        detailLogDto.setZzyUserName(buyerReg.getRegNum());
                    } else if (2 == buyerReg.getRegType()) {
                        detailLogDto.setZzyUserName(buyerReg.getRegNum());
                    }
                }
            }
            detailLogService.saveZzyUserIntegral(detailLogDto);
        } catch (Exception e) {
            logger.info("扫描证件，触发积分规则异常：{}", e);
        }
    }

}
