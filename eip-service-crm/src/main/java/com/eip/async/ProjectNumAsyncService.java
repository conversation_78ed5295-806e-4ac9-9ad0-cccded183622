package com.eip.async;

import com.beust.jcommander.internal.Maps;
import com.eip.facade.crm.dto.GetProjectNumDto;
import com.eip.facade.crm.entity.Project;
import com.eip.facade.crm.entity.Task;
import com.eip.facade.crm.vo.ProjectNumVo;
import com.eip.facade.crm.vo.TaskDetailVo;
import com.eip.service.crm.orgdao.OrderDao;
import com.eip.service.crm.orgdao.ProjClientDao;
import com.eip.service.crm.orgdao.ServeBoothDao;
import com.eip.service.crm.orgdao.TaskDao;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Future;

@Component
public class ProjectNumAsyncService {

	@Autowired
	private ProjClientDao projClientDao;
	
	@Autowired 
	private OrderDao orderDao;
	@Autowired
	private ServeBoothDao serveBoothDao;
	@Autowired
	private TaskDao taskDao;

	
	
	/**
	 * 查询展商和观众数量
	 * @param projectNumVo
	 * @param projectId
	 * @return
	 */
	@Async
	public Future<Boolean> queryClientNum(ProjectNumVo projectNumVo,Integer projectId){
		//查询关联观众数量
		Map<String,Object> map =  Maps.newHashMap();
		map.put("projectId", projectId);
		map.put("actorCode", "Viewer");
		map.put("clientNotDel", true);//没有删除的客户
		int viewerCount = projClientDao.count(map);
		projectNumVo.setViewerNum(viewerCount);
		//查询关联展商
		map.put("actorCode", "Trader");
		int traderCount = projClientDao.count(map);
		projectNumVo.setTraderNum(traderCount);
		return new AsyncResult<>(true);
	}
	
	/**
	 * 查询销售订单和采购订单数量
	 * @param projectNumVo
	 * @param getProjectNumDto
	 * @return
	 */
	@Async
	public Future<Boolean> queryOrderNum(ProjectNumVo projectNumVo, GetProjectNumDto getProjectNumDto){
		Integer projectId = getProjectNumDto.getProjectId();
		Map<String,Object> map =  Maps.newHashMap();
		//查询销售订单
		//map.put("operatorId", getProjectNumDto.getOperatorId());
		map.put("limitOperatorIds", getProjectNumDto.getLimitOperatorIds());
		map.put("projectId", projectId);
		map.put("orderTypeCode", "XSDD");
		map.put("isChecked", true);
		int orderCount1 = orderDao.count(map);
		projectNumVo.setCheckOrderNum(orderCount1);
		map.put("isChecked", false);
		int orderCount2 = orderDao.count(map);
		projectNumVo.setUnCheckOrderNum(orderCount2);
		//采购订单
		map.clear();
		//map.put("operatorId", getProjectNumDto.getOperatorId());
		map.put("limitOperatorIds", getProjectNumDto.getLimitOperatorIds());
		map.put("projectId", projectId);
		map.put("orderTypeCode", "CGDD");
		map.put("isChecked", true);
		int CGDDCount1 = orderDao.count(map);
		projectNumVo.setCheckCGOrderNum(CGDDCount1);
		map.put("isChecked", false);
		int CGDDCount2 = orderDao.count(map);
		projectNumVo.setUnCheckCGOrderNum(CGDDCount2);
		return new AsyncResult<>(true);
	}

	/**
	 * 查询展位确认数量
	 * @param projectNumVo
	 * @return
	 */
	@Async
	public Future<Boolean> queryServeBoothNum(ProjectNumVo projectNumVo, Map<String, Object> map) {
//		Map<String,Object> map =  Maps.newHashMap();
		map.put("contractProjectId", map.get("projectId"));
		map.put("boothConfirmed", true);//已确认数量
		map.remove("projectId");
		int count = serveBoothDao.count(map);
		projectNumVo.setConfirmServeBoothNum(count);
		map.put("boothConfirmed", false);//已确认数量
		int count2 = serveBoothDao.count(map);
		projectNumVo.setUnConfirmServeBoothNum(count2);
		return new AsyncResult<>(true);
	}

	@Async
	public Future<Boolean> selectProjectTask(List<Project> list) {
		if (CollectionUtils.isEmpty(list)) return new AsyncResult<>(true);
		for (Project project : list) {
			selectProjectTask(project);
		}
		return new AsyncResult<>(true);
	}

	public void selectProjectTask(Project project) {
		List<Task> taskList = taskDao.selectByProjectIdOrderBy(project.getProjectId());
		if (CollectionUtils.isNotEmpty(taskList)) {
			project.setProjectTaskRelease(true);
			List<TaskDetailVo> taskDetailList = Lists.newArrayList();
			taskList.stream().filter(Objects::nonNull).forEach(task -> {
				TaskDetailVo taskDetailVo = new TaskDetailVo();
				taskDetailVo.setOrgNum(task.getF_org_num());
				taskDetailVo.setTaskId(task.getTaskId());
				taskDetailVo.setTaskKindCode(task.getTaskKindCode());
				taskDetailVo.setTaskKindName(task.getTaskKindName());
				taskDetailVo.setTaskTopic(task.getTaskTopic());
				taskDetailList.add(taskDetailVo);
			});
			project.setTaskDetailVoList(taskDetailList);
		}
	}
}
