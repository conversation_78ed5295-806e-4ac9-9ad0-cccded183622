package com.eip.async;

import com.eip.service.crm.orgdao.OpportunityDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * @description: TODO
 * @author： hfl
 * @date：2021-01-08 10:15
 * @File: OppunityAsyncService
 */
@Component
public class OppunityAsyncService {
    @Autowired
    public OppunityAsyncService(OpportunityDao opportunityDao) {
        this.opportunityDao = opportunityDao;
    }

    private final OpportunityDao opportunityDao;

    /**查询所有商机*/
    @Async
    public Future<Boolean> selectAllNumForOpp(HashMap<String, Object> returnData, Map<String, Object> paramMap){
        HashMap<String, Object> map = new HashMap<>(paramMap);
        int allCount = opportunityDao.count(map);
        returnData.put("allCount",allCount);
        return new AsyncResult<>(true);
    }
    /**查询以保护商机*/
    @Async
    public Future<Boolean> selectNumOfProForOpp(HashMap<String, Object> returnData, Map<String, Object> paramMap){
        HashMap<String, Object> map = new HashMap<>(paramMap);
        map.put("protected",true);
        int proCount = opportunityDao.count(map);
        returnData.put("proCount",proCount);
        return new AsyncResult<>(true);
    }
    /**查询未保商机*/
    @Async
    public Future<Boolean> selectNumOfNoProForOpp(HashMap<String, Object> returnData, Map<String, Object> paramMap){
        HashMap<String, Object> map = new HashMap<>(paramMap);
        map.put("protected",false);
        int noProCount = opportunityDao.count(map);
        returnData.put("noProCount",noProCount);
        return new AsyncResult<>(true);
    }
}
