package com.eip.async;

import com.eip.common.enums.ClientTypeCodeEnum;
import com.eip.common.enums.OperateTypeEnum;
import com.eip.common.util.CollectionUtil;
import com.eip.common.util.ListUtils;
import com.eip.common.util.PublicUtil;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ProjClientDto;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.service.EventInfoService;
import com.eip.facade.crm.service.ProjClientService;
import com.eip.service.crm.orgdao.*;
import com.eip.service.crm.zzysysmapper.ZzyUserDao;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-05-20 14:48
 */
@Component
public class ClientAsyncService {
    @Autowired
    public ClientAsyncService(ClientDao clientDao, TeamClientMapper teamClientMapper, BuyerRegMapper buyerRegMapper
    						 , ContestantMapper contestantMapper, GuestMapper guestMapper
                             , BatchDtlMapper batchDtlMapper, OperatorDao operatorDao, EventInfoService eventInfoService
                             ,WorkTeamDao workTeamDao,FavoritesDao favoritesDao,ZzyUserDao zzyUserDao) {
        this.clientDao = clientDao;
        this.teamClientMapper = teamClientMapper;
        this.buyerRegMapper = buyerRegMapper;
        this.contestantMapper = contestantMapper;
        this.guestMapper = guestMapper;
        this.batchDtlMapper = batchDtlMapper;
        this.operatorDao = operatorDao;
        this.eventInfoService = eventInfoService;
        this.workTeamDao = workTeamDao;
        this.favoritesDao = favoritesDao;
        this.zzyUserDao = zzyUserDao;
    }

    private final ClientDao clientDao;
    private final TeamClientMapper teamClientMapper;
    private final BuyerRegMapper buyerRegMapper;
    private final ContestantMapper contestantMapper;
    private final GuestMapper guestMapper;
    private final BatchDtlMapper batchDtlMapper;
    private final OperatorDao operatorDao;
    private final EventInfoService eventInfoService;
    private final WorkTeamDao workTeamDao;
    private final FavoritesDao favoritesDao;
    private final ZzyUserDao zzyUserDao;



    @Async
    public Future<Boolean> selectCountByClientList(HashMap<String, Object> returnData, Map<String, Object> paramMap) {
        HashMap<String, Object> map = new HashMap<>(paramMap);
        //-1公共客户
        map.put("clientType", -1);
        map.put("authStatus", 0);
        long pubCount = PageHelper.count(() -> clientDao.selectListByMap(map));
        //  1我的客户
        map.put("clientType", 1);
        map.put("authStatus", -1);
        long myCount = PageHelper.count(() -> clientDao.selectListByMap(map));
        // 2团队客户
        map.put("clientType", 2);
        long teamCount = PageHelper.count(() ->clientDao.selectListByMap(map));
        returnData.put("pubCount", pubCount);
        returnData.put("myCount", myCount);
        returnData.put("teamCount", teamCount);
        return new AsyncResult<>(true);
    }

    /**
     * 领用、独占 数量
     * @param returnData
     * @return
     */
    @Async
    public Future<Boolean> selectCountByClientIdsAndAuthStates(HashMap<String, Object> returnData, Map<String, Object> paramMap) {
    	 HashMap<String, Object> map = new HashMap<>(paramMap);
    	 map.put("authStatus",1);
        //领用
        Long useCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
        returnData.put("useCount", useCount);
        //独占
        map.put("authStatus",3);
        Long engrossCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
        returnData.put("engrossCount", engrossCount);
        //分享
        try {
        	if(map.get("clientType")!=null&&(int)map.get("clientType")==1){
        		map.put("onlyShare",true);
        		map.put("authStatus",null);
                Long shareCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
                returnData.put("shareCount", shareCount);
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
  
        return new AsyncResult<>(true);
    }
    
    //公共
    @Async
    public Future<Boolean> selectCountByClientListPublic(HashMap<String, Object> returnData, Map<String, Object> map) {
        //HashMap<String, Object> map = new HashMap<>(paramMap);
        //-1公共客户
        map.put("clientType", -1);
        map.put("authStatus", 0);
        //long pubCount = PageHelper.count(() -> clientDao.selectListByMap(map));
        int pubCount = clientDao.selectCountByMap(map);
        returnData.put("pubCount", pubCount);
        return new AsyncResult<>(true);
    }
    //我的
    @Async
    public Future<Boolean> selectCountByClientListMy(HashMap<String, Object> returnData, Map<String, Object> map) {
        //HashMap<String, Object> map = new HashMap<>(paramMap);
        //  1我的客户
        map.put("clientType", 1);
        map.put("authStatus", -1);
        map.remove("onlyShare");
       // long myCount = PageHelper.count(() -> clientDao.selectListByMap(map));
        int myCount = clientDao.selectCountByMap(map);
        returnData.put("myCount", myCount);
        return new AsyncResult<>(true);
    }
    //团队
    @Async
    public Future<Boolean> selectCountByClientListTeam(HashMap<String, Object> returnData, Map<String, Object> map) {
        //HashMap<String, Object> map = new HashMap<>(paramMap);
        // 2团队客户
        map.put("clientType", 2);
        map.put("authStatus", -1);
        //long teamCount = PageHelper.count(() ->clientDao.selectListByMap(map));
        int teamCount = clientDao.selectCountByMap(map);
        returnData.put("teamCount", teamCount);
        return new AsyncResult<>(true);
    }
    //全部
    @Async
    public Future<Boolean> selectCountByClientListAll(HashMap<String, Object> returnData,
			Map<String, Object> map) {
    	 //HashMap<String, Object> map = new HashMap<>(paramMap);
         // 2团队客户
         map.put("clientType", null);
         map.put("authStatus", null);
         int allCount = clientDao.selectCountByMap(map);
         returnData.put("allCount", allCount);
         return new AsyncResult<>(true);
	}
    
    /**
     * 领用
     * @param returnData
     * @return
     */
    @Async
    public Future<Boolean> selectCountByClientIdsAndAuthStatesA(HashMap<String, Object> returnData, Map<String, Object> paramMap) {
    	if(paramMap.get("clientType")!=null&&(Integer) paramMap.get("clientType")==-1){
    		//公共客户  不查领用和独占
    		returnData.put("useCount", 0); 
            return new AsyncResult<>(true);
    	}
    	 HashMap<String, Object> map = new HashMap<>(paramMap);
    	 map.put("authStatus",1);
        //领用
        Long useCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
        returnData.put("useCount", useCount); 
        return new AsyncResult<>(true);
    }
    /**
     * 独占 
     * @param returnData
     * @return
     */
    @Async
    public Future<Boolean> selectCountByClientIdsAndAuthStatesB(HashMap<String, Object> returnData, Map<String, Object> paramMap) {
    	if(paramMap.get("clientType")!=null&&(Integer) paramMap.get("clientType")==-1){
    		//公共客户  不查领用和独占
    		returnData.put("engrossCount", 0); 
            return new AsyncResult<>(true);
    	}
    	 HashMap<String, Object> map = new HashMap<>(paramMap);
        //独占
        map.put("authStatus",3);
        Long engrossCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
        returnData.put("engrossCount", engrossCount); 
        return new AsyncResult<>(true);
    }
    /**
     * 分享数量
     * @param returnData
     * @return
     */
    @Async
    public Future<Boolean> selectCountByClientIdsAndAuthStatesC(HashMap<String, Object> returnData, Map<String, Object> paramMap) {
    	 HashMap<String, Object> map = new HashMap<>(paramMap);
        //分享
        try {
        	if(map.get("clientType")!=null&&(int)map.get("clientType")==1){
        		map.put("onlyShare",true);
        		map.put("authStatus",null);
                Long shareCount = teamClientMapper.selectCountByClientIdsAndAuthStates(map);
                returnData.put("shareCount", shareCount);
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
        return new AsyncResult<>(true);
    }
    
    @Async
	public Future<Boolean> selectLinkmanContractValidNum(Client c) {
    	int buyCount = buyerRegMapper.countByClient(c.getClientId());
    	int gueCount = guestMapper.countByClient(c.getClientId());
    	int conCount = contestantMapper.countByClient(c.getClientId()); 	
    	StringBuffer buffer = new StringBuffer("");
    	if(buyCount>0)buffer.append("观众").append(buyCount).append("次").append(",");
    	if(gueCount>0)buffer.append("嘉宾").append(gueCount).append("次").append(",");
    	if(conCount>0)buffer.append("参评").append(conCount).append("次").append(",");
    	if(buffer.indexOf(",")!=-1)buffer.deleteCharAt(buffer.lastIndexOf(","));    	
    	c.setContractValidNum(buffer.toString());
		return new AsyncResult<>(true);
	}

    /**
     * 查询导入数量
     * @param import_batchId
     * @param returnData
     * @return
     */
    @Async
    public Future<Boolean> selectImportCount(Long import_batchId, String clientTypeCode,HashMap<String, Object> returnData) {
        Map<String,Object> map = Maps.newHashMap();
        map.put("batchId",import_batchId);
        map.put("clientTypeCode",clientTypeCode);
        //全部导入数量（不考虑客户是否已被删除）
        int count = batchDtlMapper.countByClient(map);
        returnData.put("import_count",count);
        //新增数量(无重复) ，且客户没有被删除
        map.put("del",0);
        if (ClientTypeCodeEnum.C_LINKMAN.getCode().equals(clientTypeCode)) map.put("relationDataType", 1);
        else map.put("dataType", 1);
        int addCount = batchDtlMapper.countByClient(map);
        returnData.put("import_addCount",addCount);
        //重复合并数量 ，且客户没有被删除 (团队内合并)
        if (ClientTypeCodeEnum.C_LINKMAN.getCode().equals(clientTypeCode)) map.put("relationDataType", 2);
        else map.put("dataType", 2);
        int megreCount = batchDtlMapper.countByClient(map);
        returnData.put("import_megreCount",megreCount);
        //从数据总览引用合并
        if (ClientTypeCodeEnum.C_LINKMAN.getCode().equals(clientTypeCode)) map.put("relationDataType", 3);
        else map.put("dataType", 3);
        int mergeFromOverviewCount = batchDtlMapper.countByClient(map);
        returnData.put("import_mergeFromOverviewCount",mergeFromOverviewCount);
        //重复强制新增数量，且客户没有被删除
        int repeatAddCount = 0;
        /*if (ClientTypeCodeEnum.B_CLIENT.getCode().equals(clientTypeCode)){
            map.put("dataType", 4);
            repeatAddCount = batchDtlMapper.countByClient(map);
        }*/
        if (ClientTypeCodeEnum.C_LINKMAN.getCode().equals(clientTypeCode)) map.put("relationDataType", 4);
        else map.put("dataType", 4);
        repeatAddCount = batchDtlMapper.countByClient(map);
        returnData.put("import_repeatAddCount",repeatAddCount);
        //被删除数量
        int delCount = count - addCount - megreCount - repeatAddCount - mergeFromOverviewCount;
        returnData.put("import_delCount",delCount);
        return new AsyncResult<>(true);
    }

    /**
     * 领用或独占日志
     * @param list
     * @param operatorDto
     */
    @Async
    public Future<Boolean> receiveOrMonopolizeLog(List<TeamClient> list, OperatorDto operatorDto) {
        Map<String, String> salesNameMap = Maps.newHashMap();
        Map<String, String> workTeamNameMap = Maps.newHashMap();
        for (TeamClient bean : list) {
            String employeeName = getEmployeeName(salesNameMap, bean.getSalesmanId());
            String workTeamName = getWorkTeamName(workTeamNameMap, bean.getfWorkteamId());
            String objName = null;
            if (StringUtils.isNotBlank(bean.getCltName())) objName = bean.getCltName();
            else objName = clientDao.getNameById(bean.getfClientId());
            Integer evenKind = PublicUtil.getEvenKindByStringName(bean.getfClientTypeCode());
            String operateType = null;
            if (bean.getAuthStatus() == 1) operateType = OperateTypeEnum.RECEIVE.getName();
            else if (bean.getAuthStatus() == 3) operateType = OperateTypeEnum.MONOPOLIZE.getName();
            String memo = (new StringBuffer()).append("所在团队：").append(workTeamName).append("，业务员：").append(employeeName).toString();
            eventInfoService.saveEventOrEventChild(operatorDto, memo, evenKind, operateType, bean.getfClientId().toString(), objName);
        }
        //eventInfoService.saveChildNum(operatorDto.getEventId());
        return new AsyncResult<>(true);
    }

    public String getEmployeeName(Map<String, String> cacheMap, Integer operatorId) {
        if (operatorId == null) return "";
        String name = null;
        if (cacheMap != null) name = cacheMap.get(operatorId.toString());
        if (name == null) {
            name = operatorDao.getEmpNameByOperId(operatorId);
            name = name == null ? "" : name;
            if (cacheMap != null) cacheMap.put(operatorId.toString(), name);
        }
        return name;
    }

    public String getWorkTeamName(Map<String, String> cacheMap, Integer workTeamId) {
        if (workTeamId == null) return "";
        String name = null;
        if (cacheMap != null) name = cacheMap.get(workTeamId.toString());
        if (name == null) {
            WorkTeam workTeam = workTeamDao.selectByPrimaryKey(workTeamId);
            name = (workTeam != null && workTeam.getfWorkTeamName() != null) ? workTeam.getfWorkTeamName() : "";
            if (cacheMap != null) cacheMap.put(workTeamId.toString(), name);
        }
        return name;
    }

    /**
     * 撤销领用或独占日志
     *
     * @param list
     * @param operatorDto
     */
    @Async
    public Future<Boolean> revocationAuthLog(List<TeamClient> list, OperatorDto operatorDto) {
        Map<String, String> salesNameMap = Maps.newHashMap();
        Map<String, String> workTeamNameMap = Maps.newHashMap();
        for (TeamClient bean : list) {
            String employeeName = getEmployeeName(salesNameMap, bean.getSalesmanId());
            String workTeamName = getWorkTeamName(workTeamNameMap, bean.getfWorkteamId());
            String objName = null;
            if (StringUtils.isNotBlank(bean.getCltName())) objName = bean.getCltName();
            else objName = clientDao.getNameById(bean.getfClientId());
            Integer evenKind = PublicUtil.getEvenKindByStringName(bean.getfClientTypeCode());
            String operateType = null;
            if (bean.getAuthStatus() == 1) operateType = OperateTypeEnum.CANCEL_RECEIVE.getName();
            else if (bean.getAuthStatus() == 3) operateType = OperateTypeEnum.CANCEL_MONOPOLIZE.getName();
            String memo = (new StringBuffer()).append("所在团队：").append(workTeamName).append("，业务员：").append(employeeName)
                    .append(" |").append(bean.getfWorkteamId()).append(",").append(bean.getSalesmanId()==null?"":bean.getSalesmanId().toString()).append("|")
                    .toString();
            eventInfoService.saveEventOrEventChild(operatorDto, memo, evenKind, operateType, bean.getfClientId().toString(), objName);
        }
        return new AsyncResult<>(true);
    }

    /**
     * 收藏客户日志
     * @param list
     * @param operatorDto
     * @param isAdd true 新增日志  false 删除日志
     */
    @Async
    public Future<Boolean> addOrDelFavoritesClientLog(List<FavoritesClient> list, OperatorDto operatorDto,Boolean isAdd) {
        if (CollectionUtil.isEmpty(list)) return new AsyncResult<>(false);
        try {
            List<FavoritesClient> teamClients = ListUtils.deepCopy(list);
            for (FavoritesClient bean : teamClients) {
                String favoriteName = bean.getFavoriteName();
                if (StringUtils.isBlank(favoriteName)) {
                    Favorites favorites = favoritesDao.selectByPrimaryKey(bean.getFavoritesId());
                    if (favorites != null) favoriteName = favorites.getFavoriteName();
                }
                Integer eventKind = null;
                if (StringUtils.isNotBlank(bean.getClientTypeCode())) {
                    eventKind = PublicUtil.getEvenKindByStringName(bean.getClientTypeCode());
                } else {
                    Client client = clientDao.selectBaseById(bean.getClientId().intValue());
                    if (client != null) {
                        eventKind = PublicUtil.getEvenKindByStringName(client.getClientTypeCode());
                        if ("B_CLIENT".equals(client.getClientTypeCode()))
                            bean.setCltName(client.getCompanyName());
                        else if ("C_LINKMAN".equals(client.getClientTypeCode()))
                            bean.setCltName(client.getPersonName());
                    }
                }
                String objName = bean.getCltName();
                if (StringUtils.isBlank(objName))
                    objName = clientDao.getNameById(bean.getClientId().intValue());
                String memo = "收藏夹名称：" + (favoriteName == null ? "" : favoriteName);
                String type = null;
                if (isAdd) type = OperateTypeEnum.COLLECT.getName();
                else type = OperateTypeEnum.CANCEL_COLLECT.getName();
                eventInfoService.saveEventOrEventChild(operatorDto, memo, eventKind,
                        type, bean.getClientId().toString(), objName);
            }
        }catch (Exception e){
            e.printStackTrace();
            return new AsyncResult<>(false);
        }
        return new AsyncResult<>(true);
    }

    /**
     * 批量审核日志
     * @param list
     * @param isChecked
     * @param operator
     * @return
     */
    @Async
    public Future<Boolean> checkClientLog(List<Client> list, Integer isChecked, OperatorDto operator) {
        for (Client client : list) {
            String objName = null;
            if (ClientTypeCodeEnum.B_CLIENT.getCode().equals(client.getClientTypeCode()))
                objName = client.getCompanyName();
            else if (ClientTypeCodeEnum.C_LINKMAN.getCode().equals(client.getClientTypeCode()))
                objName = client.getPersonName();
            Integer eventKind = PublicUtil.getEvenKindByStringName(client.getClientTypeCode());
            String operateType = isChecked == 1 ? OperateTypeEnum.CHECK.getName() : OperateTypeEnum.CANCEL_CHECK_CLIENT.getName();
            eventInfoService.saveEventOrEventChild(operator, null, eventKind,
                    operateType, client.getClientId().toString(), objName);
        }
        return new AsyncResult<>(true);
    }

    /**
     * 根据会员ID查询会员
     * @param myClient
     * @return
     */
    @Async
    public Future<Boolean> queryZzyUser(List<Client> myClient) {
        if (CollectionUtil.isEmpty(myClient)) return new AsyncResult<>(true);
        int num = 1000;
        List<List<Client>> subList = Lists.newArrayList();
        if (myClient.size() > num) subList = ListUtils.fixedGrouping(myClient, num);
        else subList.add(myClient);
        for (List<Client> itemList : subList) {
            Set<Integer> collect = itemList.stream().filter(item -> item.getZzyuserId() != null)
                    .map(item -> item.getZzyuserId()).collect(Collectors.toSet());
            if (collect.size() == 0) continue;
            List<ZzyUser> users = zzyUserDao.selectUserNameByIds(collect);
            Map<Integer, String> userMap = Maps.newHashMap();
            users.forEach(item -> userMap.put(item.getF_zzyuser_id(), item.getF_user_name()));
            itemList.forEach(item -> {
                if (item.getZzyuserId() != null)
                    item.setZzyUserName(userMap.get(item.getZzyuserId()));
            });
        }
        return new AsyncResult<>(true);
    }

    /**
     * 查询同行合作次数
     * @param clients
     * @return
     */
    @Async
    public Future<Boolean> queryRivalClientCount(List<Client> clients, Integer workTeamId) {
        if (CollectionUtil.isEmpty(clients)) return new AsyncResult<>(true);
        List<List<Client>> lists = ListUtils.fixedGrouping(clients, 1000);
        for (List<Client> list : lists) {
            List<Integer> collect = list.stream().filter(item -> item.getClientId() != null).map(item -> item.getClientId()).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) continue;
            List<Client> select = clientDao.queryRivalClientCount(collect, workTeamId);
            if (CollectionUtil.isEmpty(select)) continue;
            Map<Integer, String> map = select.stream().collect(Collectors.toMap(Client::getClientId, Client::getContractValidNum, (key1, key2) -> key1));
            for (Client client : list) {
                if (client.getClientId() == null) continue;
                String num = map.get(client.getClientId());
                if (num == null || "0".equals(num)) continue;
                String contractValidNum = (client.getContractValidNum() == null ? "" : client.getContractValidNum()) + "（同行" + num + "）";
                client.setContractValidNum(contractValidNum);
            }
        }
        return new AsyncResult<>(true);
    }

}
