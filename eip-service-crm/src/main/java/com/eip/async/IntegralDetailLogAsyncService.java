/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-12-06 9:43
 * @description：积分发放、消耗异步方法
 */
package com.eip.async;

import com.beust.jcommander.internal.Lists;
import com.eip.facade.crm.entity.IntegralGoodsConfirmLog;
import com.eip.facade.crm.entity.Project;
import com.eip.facade.crm.vo.IntegralDetailLogVo;
import com.eip.service.crm.orgdao.ProjectDao;
import com.eip.service.crm.zzysysmapper.IntegralBatchConsumeLogMapper;
import com.eip.service.crm.zzysysmapper.IntegralDetailLogMapper;
import com.eip.service.crm.zzysysmapper.IntegralGoodsConfirmLogMapper;
import com.eip.service.crm.zzysysmapper.IntegralTypeMapper;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-12-06 9:43
 * @description：积分发放、消耗异步方法
 */
@Component
public class IntegralDetailLogAsyncService {

    @Autowired
    private IntegralDetailLogMapper detailLogMapper;
    @Autowired
    private IntegralBatchConsumeLogMapper batchConsumeLogMapper;
    @Autowired
    private IntegralGoodsConfirmLogMapper goodsConfirmLogMapper;
    @Autowired
    private ProjectDao projectDao;
    @Autowired
    private IntegralTypeMapper integralTypeMapper;

    /**
     * 统计所有的积分发放消耗合计
     * @param queryMap
     * @return
     */
    @Async
    public Future<Map<String,Object>> countIntegralDetailLog(Map<String,Object> queryMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        queryMap.put("detailType",1);
        List<IntegralDetailLogVo> sendIntegralList = detailLogMapper.countIntegralSum(queryMap);
        if(CollectionUtils.isEmpty(sendIntegralList))   return new AsyncResult<>(resultMap);
        Map<String, List<IntegralDetailLogVo>> detailLogVoResultMap = Maps.newHashMap();
        for(IntegralDetailLogVo detailLogVo : sendIntegralList){
            queryMap.clear();
            queryMap.put("memberBatchLogId",detailLogVo.getMemberBatchLogId());
            List<Map<String,Object>> consumeIntegralSumList = batchConsumeLogMapper.selectBatchConsumeIntegralLog(queryMap);
            if(CollectionUtils.isNotEmpty(consumeIntegralSumList) && consumeIntegralSumList.size() > 0){
                BigDecimal memberBatchConsumeIntegral = (BigDecimal) consumeIntegralSumList.get(0).get("consumeintegralsum");
                BigDecimal subtract = detailLogVo.getIntegralSum().subtract(memberBatchConsumeIntegral);
                detailLogVo.setIntegralSum(subtract);
            }
            if(detailLogVoResultMap.containsKey(String.valueOf(detailLogVo.getIntegralTypeId()))){
                List<IntegralDetailLogVo> detailLogVoList  = (List)detailLogVoResultMap.get(String.valueOf(detailLogVo.getIntegralTypeId()));
                detailLogVoList.add(detailLogVo);
                detailLogVoResultMap.put(String.valueOf(detailLogVo.getIntegralTypeId()),detailLogVoList);
            }else{
                List<IntegralDetailLogVo> detailLogVos = Lists.newArrayList();
                detailLogVos.add(detailLogVo);
                detailLogVoResultMap.put(String.valueOf(detailLogVo.getIntegralTypeId()),detailLogVos);
            }
        }
        for(Map.Entry<String,List<IntegralDetailLogVo>> purchaseIntegral : detailLogVoResultMap.entrySet()){
            List<IntegralDetailLogVo> integralDetailLogVos = purchaseIntegral.getValue();
            BigDecimal resultIntegral = BigDecimal.ZERO;
            String integralTypeName = null;
            for(IntegralDetailLogVo detailLogVo : integralDetailLogVos){
                if(StringUtils.isBlank(integralTypeName)){
                    integralTypeName = detailLogVo.getIntegralTypeName();
                }
                resultIntegral = resultIntegral.add(detailLogVo.getIntegralSum());
            }
            resultMap.put(integralTypeName,resultIntegral);
        }
        return new AsyncResult<>(resultMap);
    }

    /**
     * 统计所有的积分发放消耗合计
     * @param paramMap
     * @return
     */
    @Async
    public Future<Map<String, Object>> queryCheckDataInfo(Map<String, Object> paramMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        paramMap.put("groupByParam","confirmGoods");
        List<Map<String,Object>> confirmGoodsList = goodsConfirmLogMapper.asynQueryCheckGoodsInfo(paramMap);
        resultMap.put("confirmGoodsList",confirmGoodsList);

        paramMap.put("groupByParam","confirmZzyUserNum");
        Map<String,Object> confirmZzyUserNum = goodsConfirmLogMapper.countQueryCheckGoodsNum(paramMap);
        resultMap.putAll(confirmZzyUserNum);
        paramMap.put("groupByParam","confirmGoodsNum");
        Map<String,Object> confirmGoodsNum = goodsConfirmLogMapper.countQueryCheckGoodsNum(paramMap);
        resultMap.putAll(confirmGoodsNum);
        return new AsyncResult<>(resultMap);
    }

    /**
     * 查询积分发放消耗明细的其它信息
     * @param detailLogVoList
     * @return
     */
    @Async
    public Future<Boolean> queryDetailLogOtherInfo(List<IntegralDetailLogVo> detailLogVoList) {
        if(CollectionUtils.isEmpty(detailLogVoList)) return new AsyncResult<>(true);
        Map<String,Object> paramMap = Maps.newHashMap();
        detailLogVoList.stream().filter(Objects::nonNull).forEach(detailLog -> {
            if(detailLog.getProjectId()!=null && detailLog.getProjectId() > 0){
                Project project = projectDao.selectParentAndMainProjectId(detailLog.getProjectId());
                if(Objects.nonNull(project)) detailLog.setProjectName(project.getProjectAbbr());
            }
//            if(detailLog.getUpdateId()!=null && detailLog.getUpdateId() > 0){
//                Operator operator = operatorService.selectOperNameByOrgNumAndId(detailLog.getOrgNum(), detailLog.getUpdateId());
//                if(Objects.nonNull(operator)){
//                    detailLog.setUpdateName(operator.getEmployeeName());
//                }
//            }
            //查询当前这条积分发放或消耗后的积分余额
            paramMap.clear();
            paramMap.put("zzyUserId",detailLog.getZzyuserId());
            paramMap.put("orgNum",detailLog.getOrgNum());
            paramMap.put("currentDetailId",detailLog.getDetailLogId());
            paramMap.put("integralTypeId",detailLog.getIntegralTypeId());
            BigDecimal integralBalance =  detailLogMapper.sumZzyUserIntegral(paramMap);
            integralBalance = integralBalance!=null && integralBalance.compareTo(BigDecimal.ZERO) > 0 ? integralBalance : BigDecimal.ZERO;
            detailLog.setIntegralBalance(integralBalance);
        });
        return new AsyncResult<>(true);
    }

    /**
     * 统计汇总每个积分类型的积分剩余量
     * @return
     */
    @Async
    public Future<Map<String, Object>> countIntegralCollect(Map<String,Object> queryMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        /**
         * 1、分组筛选出积分类型
         * 2、根据积分类型在汇总的基础上查询统计
         */
        queryMap.put("groupIntegralType",true);
        List<IntegralDetailLogVo> integralCollectSumList = detailLogMapper.countIntegralCollect(queryMap);
        if(CollectionUtils.isNotEmpty(integralCollectSumList)){
            queryMap.remove("groupIntegralType");
            for(IntegralDetailLogVo integralDetailLogVo : integralCollectSumList){
                BigDecimal integralAddSum = BigDecimal.ZERO;
                BigDecimal integralSubtractSum = BigDecimal.ZERO;
                List<BigDecimal> integralSumInfoList =  Lists.newArrayList(3);
                Long integralTypeId = integralDetailLogVo.getIntegralTypeId();
                queryMap.put("twoQueryFlag",true);
                queryMap.put("twoIntegralTypeId",integralTypeId);
                queryMap.put("twoType",1);
                List<IntegralDetailLogVo> integralAddCollectSumList = detailLogMapper.countIntegralCollect(queryMap);
                if(CollectionUtils.isNotEmpty(integralAddCollectSumList)){
                    IntegralDetailLogVo integralAdd = integralAddCollectSumList.get(0);
                    if(Objects.nonNull(integralAdd)){
                        integralAddSum = integralAdd.getIntegralAddSum()!=null ? integralAdd.getIntegralAddSum() : BigDecimal.ZERO;
                    }
                }
                queryMap.put("twoType",2);
                List<IntegralDetailLogVo> integralSubtractCollectSumList = detailLogMapper.countIntegralCollect(queryMap);
                if(CollectionUtils.isNotEmpty(integralSubtractCollectSumList)){
                    IntegralDetailLogVo integralSubtract = integralSubtractCollectSumList.get(0);
                    if(Objects.nonNull(integralSubtract)){
                        integralSubtractSum = integralSubtract.getIntegralSubtractSum()!=null ? integralSubtract.getIntegralSubtractSum() : BigDecimal.ZERO;
                    }
                }
                //加
                integralSumInfoList.add(integralAddSum!=null ? integralAddSum : BigDecimal.ZERO);
                //减
                integralSumInfoList.add(integralSubtractSum!=null ? integralSubtractSum : BigDecimal.ZERO);
                //余额
                BigDecimal integralBalance = integralAddSum.add(integralSubtractSum);
                integralSumInfoList.add(integralBalance);

                String integralTypeName = integralDetailLogVo.getIntegralTypeName();
                resultMap.put(integralTypeName,integralSumInfoList);
            }
        }
        return new AsyncResult<>(resultMap);
    }

    /**
     * 查询会员兑换核销商品其它信息
     * @param goodsConfirmLogList
     * @return
     */
    @Async
    public Future<Boolean> queryGoodsConfirmLogOtherInfo(List<IntegralGoodsConfirmLog> goodsConfirmLogList) {
        if(CollectionUtils.isEmpty(goodsConfirmLogList)) return new AsyncResult<>(true);
        Map<String,Object> paramMap = Maps.newHashMap();
        goodsConfirmLogList.stream().filter(Objects::nonNull).forEach(goodsConfirmLog -> {
//            if(goodsConfirmLog.getUpdateId()!=null && goodsConfirmLog.getUpdateId() > 0){
//                goodsConfirmLog.setEmployeeName(operatorService.getEmpNameByOperId(goodsConfirmLog.getUpdateId()));
//            }
            if(goodsConfirmLog.getProjectId()!=null && goodsConfirmLog.getProjectId() > 0){
                Project project = projectDao.selectParentAndMainProjectId(goodsConfirmLog.getProjectId());
                if(Objects.nonNull(project)) goodsConfirmLog.setProjectName(project.getProjectAbbr());
            }
            //查询当前这条商品 兑换核销的商品剩余量
            paramMap.clear();
            paramMap.put("zzyUserId",goodsConfirmLog.getZzyuserId());
            paramMap.put("orgNum",goodsConfirmLog.getOrgNum());
            paramMap.put("goodsId",String.valueOf(goodsConfirmLog.getGoodsId()));
            paramMap.put("currentGoodsConfirmLogId",goodsConfirmLog.getId());
            List<IntegralGoodsConfirmLog> goodsConfirmLogs = goodsConfirmLogMapper.getPage(paramMap);
            Integer zzyUserGoodsSurplusNum = 0;
            if(CollectionUtils.isNotEmpty(goodsConfirmLogs)){
                Integer purchaseNumSum = 0;
                Integer confirmNumSum = 0;
                Optional<Integer> purchaseNumOptional = goodsConfirmLogs.stream().filter(Objects::nonNull)
                        .filter(integralGoodsConfirmLog -> integralGoodsConfirmLog.getState() != null && 1 == integralGoodsConfirmLog.getState())
                        .map(IntegralGoodsConfirmLog::getPurchaseNum)
                        .reduce((a, b) -> a + b);
                if(purchaseNumOptional.isPresent()){
                    purchaseNumSum = purchaseNumOptional.get();
                }
                Optional<Integer> confirmNumOptional = goodsConfirmLogs.stream().filter(Objects::nonNull)
                        .filter(integralGoodsConfirmLog -> integralGoodsConfirmLog.getState() != null && 2 == integralGoodsConfirmLog.getState())
                        .map(IntegralGoodsConfirmLog::getConfirmNum)
                        .reduce((a, b) -> a + b);
                if(confirmNumOptional.isPresent()){
                    confirmNumSum = confirmNumOptional.get();
                }
                zzyUserGoodsSurplusNum = purchaseNumSum - confirmNumSum;
            }
            goodsConfirmLog.setZzyUserGoodsBalanceNum(zzyUserGoodsSurplusNum);
        });
        return new AsyncResult<>(true);
    }
}
