var os = function() {
  var ua = navigator.userAgent,
      isWindowsPhone = /(?:Windows Phone)/.test(ua),
      isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
      isAndroid = /(?:Android)/.test(ua),
      isFireFox = /(?:Firefox)/.test(ua),
      isChrome = /(?:Chrome|CriOS)/.test(ua),
      isTablet = /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua)),
      isPhone = /(?:iPhone)/.test(ua) && !isTablet,
      isPc = !isPhone && !isAndroid && !isSymbian,
      isH5Like = (window.innerWidth || (document.body || {}).clientWidth) < 500,
      isMobile = /(?:Mobile)/.test(ua),
      isH5 = isPhone || isAndroid || isSymbian || isTablet || isH5Like || isMobile,
      load = (handler) => {
        if (window.addEventListener) {
          window.addEventListener('DOMContentLoaded', handler, false);
          // window.addEventListener('load', handler, false);
        } else if (window.attachEvent) {
          window.attachEvent('onload', handler);  //IE
        }
      },
      sites = {
        'vip': ['eip-app-vip/pages/frontend/zzy-denglu.html'
          ,'eip-app-vip/mobile-vipcenter/index.html'], // 会员中心 无参
        'spon': ['eip-web-business/pages/Mock-Sponsor/index.html'
          ,'eip-web-business/pages/mobile-sponsor/index.html'], // 主办方 无参
      };
  return {
    isH5,
    v: 2.1,
  };
}();

// eq:
// if(!os.isH5){ // PC访问
//   const params = new URLSearchParams(document.location.search.substring(1));
//   const p = params.get('pid') || ''
//   p && _goPcPreorder(p,document.location.search)
// }
// if(os.isH5){ // H5访问
//   const params = new URLSearchParams(document.location.search.substring(1));
//   const p = params.get('pid') || ''
//   p && _goH5Preorder(p,document.location.search)
// }