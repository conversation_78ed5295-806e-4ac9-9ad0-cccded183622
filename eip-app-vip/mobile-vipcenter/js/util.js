"use strict";
void 0 === window.util && (window.util =  Object.create(null));

// const variableSponsor =  window.origin + '/eip-web-sponsor'
const url = (new URL(window.location.href)).searchParams
window.apiHost = window.origin + '/'
// util
// require: axios vant
Object.assign(util,{
  STORE: window.localStorage,
  BANTYPE: Object.freeze([".php",".php5",".php4",".php3",".php2",".html",".htm",".phtml",".pht",".pHp",".pHp5",".pHp4",".pHp3",".pHp2",".Html",".Htm",".pHtml",".jsp",".jspa",".jspx",".jsw",".jsv",".jspf",".jtml",".jSp",".jSpx",".jSpa",".jSw",".jSv",".jSpf",".jHtml",".asp",".aspx",".asa",".asax",".ascx",".ashx",".asmx",".cer",".aSp",".aSpx",".aSa",".aSax",".aScx",".aShx",".aSmx",".cEr",".sWf",".swf",".htaccess",".ini"]),
  // vant: new Vue({ data: {
  // } }),
  loading(close = false,strOrObj ='') {
    if(close) {
      setTimeout(() => {
        window.loading && window.loading.close()
      }, 300);
    } else  {
      let ops = {
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '加载中 ...',
      }
      if(typeof strOrObj == 'string' && strOrObj) {
        ops.message = strOrObj
      } else {
        ops = {
          ...ops,
          ...strOrObj,
        }
      }
      window.loading = vant.Toast.loading(ops)
    }
  },
  alert(msg = '', icon = 'err', throws = false, cb =() => {}) { // 1 suc/2 err
    if(icon =='err') this.loading(true);
    if (msg) {
      if(!icon){
        vant.Toast(msg);
      }else{
        var ops
        if(typeof icon === 'object'){
          ops = icon
        }else{
          ops = { icon }
        }
        if(ops.icon == 'err'){
          vant.Toast.fail(msg)
        } else if(ops.icon == 'suc') {
          vant.Toast.success(msg)
        } else if(ops.icon == 'loading') {
          vant.Toast.loading(msg)
        } else{
          ops.message = msg
          vant.Toast(ops);
        }
      }
    }
    if (throws) throw new Error('error: ' + msg);
  },
  checkLogin(check=true){
    // deprecated => use : utilm.checkLogin()
    const orgNum = this.getQs('orgNum', 'orgnum', '')
    if(check && !this.getLocal('usrname')){
      window.location.href = `login.html?orgNum=${orgNum}`
    }
    return {
      username: this.getLocal('usrname'),
      cid: this.getLocal('cid'),
      zzyUserId: this.getLocal('zzyUserId'),
      orgNum,
    }
  },
  checkReturn(obj, msg = '', icon = '', throws = false) {
    icon = icon || 'err';
    if (obj && obj.state === 1) {
      return true;
    } else {
      if(obj.msg && ['未登录','请登陆','请先登陆','需要登陆'].includes(obj.msg.trim())){
        // this.setLocal('usrname', '');
        // const orgNum = this.getQs('orgNum', true, '')
        window.location.href = `login.html?orgNum=${uinfo.orgNum}`
      }
      this.alert(msg || obj.msg || obj.message || '操作失败', icon, throws);
      return false;
    }
  },
  get(url, data, ops){
    return axios.get(window.apiHost + url, this.JSON2FormData(data))
  },
  getImgUrl(src = '', prefix='/image/'){
    let tmp = '';
    if(!src){
    }else if(src.startsWith('http') || src.startsWith('//')){
      tmp = src;
    }else if(window.location.host === 'localhost') {
      tmp = 'http://*************:8088' + prefix + src
    } else {
      tmp = window.origin + prefix + src
    }
    return tmp
  },
  getHiddenText(mobileOrEmail) { // 后面有时间在改
    const str = String(mobileOrEmail) || '';
    const len = str.length;
    if(len > 8) { // 9+
      return str.substr(0,len -8 ) + '****' + str.substr(-4);
    } else if(len<1) { // 4-
      return '*'
    } else if(len<5) { // 4-
      return str.substr(0,1) + '*'.repeat(len -1);
    } else { // 5~8
      return str.substr(0,2) + '**' + str.substr(4);
    }
  },
  getHiddenString(value='',start=0,len=0) {
    if (!value) return ''
    const vlen = value.length
    if (vlen<0||start<0||len<0) return '';
    var hidden = '';
    for (var i = 0; i < len; i++) { hidden += '*'; }
    if(vlen <= start ){
      return hidden
    } else if(vlen <= start + len){
      return value.substring(0, start) + hidden
    } else {
      return value.substring(0, start) + hidden + value.substring(start+len);
    }
  },
  getPassLevel(pass) {
    if(typeof pass !== 'string' || !pass) return 0;
    var level = 0;
    // 小写字母，级别+1
    if (/[a-z]/.test(pass)) level++;
    if (/[A-Z]/.test(pass)) level++;
    // 数字+1
    if (/[0-9]/.test(pass)) level++;
    // 其他+1
    if (/[^a-z0-9]/.test(pass)) level++;
    return level;
  },
  getLoginRspErrorMsg(arr, allowMemerReg) {
    let msg = '';
    if (arr.state == 0) {
      msg = arr.msg || arr.message || '请求失败';
    }else if (arr.state == 1) { // ok
      // ok
    }else if (arr.state == -1) { // 验证码登录: 未注册
      if(!allowMemerReg){
        msg = '未查询到账号.';
      } else {
        // 提示验证码注册
      }
    }else if (arr.state == 2) {
      msg = '密码错误.';
    }else if (arr.state == -2) {
      msg = '验证码错误.';
    } else if (arr.state == 3) { // 密码登录: 未注册
      if(!allowMemerReg){
        msg = '未查询到账号.';
      } else {
        // 提示验证码注册
      }
    } else if (arr.state == -3) { // 不开启注册
      msg = '未查询到账号.'
    } else if (arr.state == -7){ // ?
      const {kickAcount, kickMsg} = arr.data
      if(kickAcount) msg = kickMsg;
    } else if (arr.state == -10){ // 需要验证码安全登录
    } else if (arr.state == -9){  // 未关联会员
      // 关联会员
    } else if (arr.state == -99){ // 服务器繁忙
      // 服务器繁忙
    } else {
      msg = '登录失败 :' + (arr.msg || arr.message || '')
    }
    return msg;
  },
  getLocal(k) {
    if(!k) return '';
    // return JSON.parse(this.STORE.getItem('vip-'+k))
    return this.STORE.getItem('vip_'+k)
  },
  uploadOne(file) {
    const s   = file.name || ''
    const ext = s.substr(s.lastIndexOf('.')+1).toLocaleLowerCase()
    if(this.BANTYPE.includes(ext)) this.alert('非法上传', 'err', true);
    return this.post('eip-web-sponsor/upload/uploadFileOss',{
    // return this.post('eip-web-sponsor/upload/uploadImg',{
        file
      },
      {
        'Content-Type': 'multipart/form-data'
      }
    )
  },
  getQs(k, ifNullGetLocal = false, df=''){
    let rs = url.get(k) || '';
    if(typeof ifNullGetLocal === 'string') {
      return ifNullGetLocal ? (rs || this.getLocal(ifNullGetLocal) || df) : rs;
    } else {
      return ifNullGetLocal ? (rs || this.getLocal(k) || df) : rs;
    }
  },
  JSON2FormData (obj) {
    if (typeof obj !== 'object') return;
    const formData = new FormData();
    Object.keys(obj).forEach(key => formData.append(key, obj[key]));
    return formData;
  },
  postJson(url,data,ops, throws = true) {
    let df = {
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
      changeOrigin: true,
      ...ops,
    }
    return axios.post(window.apiHost + url, data ? JSON.stringify(data) : null , df, throws)
  },
  post(url,data,ops, throws = true) {
    let df = {
      // headers: {
      //  'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      //   // 'Content-Type': 'multipart/form-data'
      //   // 'Content-Type': 'application/json;charset=utf-8',
      //   // 'Authorization': window.sessionStorage.getItem("token")
      // },
      ...ops,
      changeOrigin: true,
    }
    url = String(url).replaceAll('//','/');
    if(url.startsWith('/')) url = url.substring(1);
    return axios
      .post(window.apiHost + url, data ? this.JSON2FormData(data) : null , df)
      .catch(e=> {
        vant.Toast.fail('请求失败!');
        if(throws) throw new Error('请求失败 : ' + url);
      });
  },
  setObjVal(obj,k,v=''){
    // 未设置的属性 不支持动态绑定
    k.split(',').forEach(i =>{
      obj[i.trim()] = v
    })
  },
  setLocal(k,v) {
    if(!k) return
    this.STORE.setItem('vip_'+k,v)
    // this.STORE.setItem('vip-'+k,JSON.stringify(v))
  },
  toast (msg,duration){
    vant.Toast(msg)
    // duration=isNaN(duration)?3000:duration;
    // var m = document.createElement('div');
    // m.innerHTML = msg;
    // m.style.cssText="max-width:60%;min-width: 150px;padding:0 14px;height: 40px;color: rgb(255, 255, 255);line-height: 40px;text-align: center;border-radius: 4px;position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%);z-index: 9999999999;background: rgba(0, 0, 0,.7);font-size: 16px;";
    // document.body.appendChild(m);
    // setTimeout(function() {
    //   var d = 0.5;
    //   m.style.webkitTransition = '-webkit-transform ' + d + 's ease-in, opacity ' + d + 's ease-in';
    //   m.style.opacity = '0';
    //   setTimeout(function() { document.body.removeChild(m) }, d * 1000);
    // }, duration);
  },
})

Date.prototype.format = function (fmt) { //author: meizz
  var o = {
      "M+": this.getMonth() + 1, //月份
      "d+": this.getDate(), //日
      "H+": this.getHours(), //小时
      "m+": this.getMinutes(), //分
      "s+": this.getSeconds(), //秒
      "q+": Math.floor((this.getMonth() + 3) / 3), //季度
      "S": this.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o)
  if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}

// require : vant2 : vue
// <tip ref="tipBusy" msg="系统繁忙,请60秒后重试"></tip>
const tip = {
  name: 'tip',
  template: `
    <van-dialog
      v-bind="$attrs" v-on="$listeners"
      :title="title || '提醒'"
      :width="width"
      v-model="show"
      :message="msg || '确认要操作吗？'"
      :show-confirm-button="!noFooter"
      transition="fade"
      :before-close="(action, done)=>{
        if(!noCloseOnSure) done()
        else done(action != 'confirm')
      }"
      :confirm-button-text="sureText || '确认'"
      :show-cancel-button="!noFooter"
      :cancel-button-text="closeText || '取消'">
      <slot></slot>
    </van-dialog>`,
  data() {
    return {
      show: false,
    }
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '375px',
    },
    msg: {
      type: String,
      default: '',
    },
    noFooter: {
      type: Boolean,
      default: false,
    },
    closeText: {
      type: String,
      default: '',
    },
    sureText: {
      type: String,
      default: '',
    },
    noCloseOnSure: { // 不要确认后自动关闭
      type: Boolean,
      default: false,
    }
  },
  methods: {
    close() {
      this.show = false;
    },
    open() {
      this.show = true;
    },
  },
};