#app{
}
.main{
  overflow-y: scroll;
  /* width: 99vw; */
  height: calc( 100vh - 50.1333vw );
}
.main ul {
  padding: 10px;
}
.main li {
	background-color: white;
	border-radius: 5px;
	padding: 16px 10px;
  margin-bottom: 10px;
}
.van-tabs__line{
  width: 21.3333vw;
}

.van-tabs__content{
  overflow: hidden;
  height: calc( 100vh - 36.8vw);
  background-color: #f2f2f2;
}

.van-search .van-field__control{
  border-bottom: unset;
  box-shadow: unset;
}
.van-search .van-field__left-icon
,.van-search .van-search__action{
  display: flex;align-items: center;

}
.van-search  .van-field__left-icon img{
  height: 4.2667vw;
  width: 4.2667vw;
}
.van-search  .van-search__action svg{
  opacity: 0.6;
  height: 4.2667vw;
  width: 4.2667vw;
}
.van-search  .van-search__action{
  position: relative;
}

  .tag.check{
    color: white;
    background-color: var(--color-h5-main);
  }
  .tag{
    color: #262626;
  }