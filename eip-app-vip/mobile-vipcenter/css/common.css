/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css
 * rainbow 修改版 202000613
 */
 * {
   margin: 0;padding: 0;outline: 0;
   box-sizing: border-box;
 }
 html {
    line-height: 1.15; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }
  /* Sections
     ========================================================================== */
  main {
    display: block;
  }
  /* Grouping content
     ========================================================================== */
  hr {
    box-sizing: content-box; /* 1 */
    height: 0; /* 1 */
    overflow: visible; /* 2 */
  }
  pre {
    font-family: monospace, monospace; /* 1 */
    font-size: 1em; /* 2 */
  }

  /* Text-level semantics
     ========================================================================== */
  a {
    background-color: transparent;
  }
  abbr[title] {
    border-bottom: none; /* 1 */
    text-decoration: underline; /* 2 */
    text-decoration: underline dotted; /* 2 */
  }
  b,
  strong {
    font-weight: bolder;
  }
  code,
  kbd,
  samp {
    font-family: monospace, monospace; /* 1 */
    font-size: 1em; /* 2 */
  }
  small {
    font-size: 80%;
  }
  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }

  /* Embedded content
     ========================================================================== */
  img {
    border-style: none;
  }

  /* Forms
     ========================================================================== */
  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit; /* 1 */
    font-size: 100%; /* 1 */
    line-height: 1.15; /* 1 */
    margin: 0; /* 2 */
  }
  button,
  input { /* 1 */
    overflow: visible;
  }
  button,
  select { /* 1 */
    text-transform: none;
  }
  button,
  [type="button"],
  [type="reset"],
  [type="submit"] {
    -webkit-appearance: button;
  }
  button::-moz-focus-inner,
  [type="button"]::-moz-focus-inner,
  [type="reset"]::-moz-focus-inner,
  [type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }
  button:-moz-focusring,
  [type="button"]:-moz-focusring,
  [type="reset"]:-moz-focusring,
  [type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText;
  }
  fieldset {
    padding: 0.35em 0.75em 0.625em;
  }
  legend {
    box-sizing: border-box; /* 1 */
    color: inherit; /* 2 */
    display: table; /* 1 */
    max-width: 100%; /* 1 */
    padding: 0; /* 3 */
    white-space: normal; /* 1 */
  }
  progress {
    vertical-align: baseline;
  }
  textarea {
    overflow: auto;
  }
  [type="checkbox"],
  [type="radio"] {
    box-sizing: border-box; /* 1 */
    padding: 0; /* 2 */
  }
  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    height: auto;
  }
  [type="search"] {
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
  }

  [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-file-upload-button {
    -webkit-appearance: button; /* 1 */
    font: inherit; /* 2 */
  }
  /* Interactive
     ========================================================================== */
  details {
    display: block;
  }
  summary {
    display: list-item;
  }
  template {
    display: none;
  }
  [hidden] {
    display: none;
  }

input {
  border: none;
  outline: none;
}
body{
  /* --color-h5-main: #108ee9;
  --color-h5-text: #4f4f4f; */
  font: .8373vw Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif;
  color: #999;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.hide{
  display: none !important;
}
[v-cloak]{
  display: none;
}
.text-over-1{
  /* overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap; */
  overflow : hidden;
  white-space: unset;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.flex{
  display: flex;
}
  .flex-default {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  .flex-float{
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .flex-w{
    flex-direction: row;
  }
  .flex-h{
    flex-direction: column;
  }
  .flex-center-w{
    display: flex;
    justify-content: center;
  }
  .flex-center-h{
      display: flex;
      align-items: center;
  }
  .flex-center{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-between{
    display: flex;
    flex-flow:  row nowrap;
    justify-content: space-between;
  }
  .flex-svg-center{
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    flex-direction: column;
  }

/* #app{
  flex: 1;
  position: relative;
} */
:root{
  --btn-color: #fff,
}
#app{
  min-height: calc( 100vh - 13.3333vw );
  min-width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f2f2f2;
}
.van-cell-group{
  box-shadow: 0 8px 12px #ebedf0;
}
.df-shadow{
  box-shadow: 0 8px 12px #ebedf0;
}
.theme-shadow{
  box-shadow: 0 8px 12px var(--color-h5-main);
}
.van-tab--active span {
  color: var(--color-h5-main);
}
.van-tabs__line{
  background-color: var(--color-h5-main);
}
.van-calendar__selected-day{
  background-color: var(--color-h5-main);
}
.van-calendar__footer .van-button{
  border-color: var(--color-h5-main);
  background-color: var(--color-h5-main);
}
.van-cell-noBorder:after{
  border: unset;
}
.van-dialog__confirm, .van-dialog__confirm:active{
  color: var(--color-h5-main);
}
/* 底部 */
/* .comps-botBar .van-tabbar-item svg .b{
  clip-path:url(#a);
} */
.comps-botBar {
  height: 13.3333vw !important;
}
  .comps-botBar .van-tabbar-item{
    fill: #888;
  }
  .comps-botBar .van-tabbar-item--active{
    color: var(--color-h5-main);
  }
  .comps-botBar .van-tabbar-item--active svg{
    fill: var(--color-h5-main);
  }

.before-img{
  position: relative;
  padding-left: 5.3333vw;
}
  .before-img:before{
    position: absolute;top: 0;left: 0;
    opacity: 0.5;
  }
  .before-img-mark:before {
    content: url(../../img/newpage/future_expo_addtr.png)
  }
  .before-img-timer:before {
    content: url(../../img/newpage/future_expo_time.png)
  }

.tops {
	width: 100%;
  z-index: 99999;
	position: relative;
	background-color: var(--color-h5-main);
  box-shadow: 0 1px 1px var(--color-h5-main);
	height: 11.7333vw;
}
	.tops .account {
		color: #fff;
		font-size: 4.8vw;
		text-align: center;
		line-height: 11.7333vw;
		opacity: .9;
	}
	.back {
		width: 11.7333vw;
		height: 11.7333vw;
		display: block;
		position: absolute;
		top: 0;
		left: 0;
		background: url(../img/back2.png) no-repeat center center;
		background-size: 2.6667vw;
	}
.theme-btn.van-button {
  background-color: var(--color-h5-main);
  border-color: var(--color-h5-main);
  color: #FFFFFF;
}
.theme-svg{
  fill: var(--color-h5-main);
}
.theme-input{
  background-color: #f2f2f2;
  border-radius: 24px;
  color: #ccc;
  font-size: 3.4667vw;
  padding: .8vw 4.2667vw;
}
.skeleton-ok .van-skeleton__row
  ,.skeleton-ok .van-skeleton__title{
    background-color: unset;
}
.comps-exhiItem.van-skeleton{
    padding:0
  }
  .comps-exhiItem.van-skeleton .van-skeleton__content{
    padding-top: 0;
  }
  .comps-exhiItem.van-skeleton .van-skeleton__title+.van-skeleton__row {
    margin-top: 12px !important;
  }
  .comps-exhiItem.van-skeleton .van-skeleton__row:not(:first-child) {
    margin-top: 6px;
    line-height: 18px;
  }
  .comps-exhiItem.van-skeleton .van-skeleton__row, .van-skeleton__title{
    height: 4.2667vw;
    line-height: 5vw;
  }
  .text-center{
    text-align: center;
  }

.filter-popup{
  transform: unset;
  /* display: flex; */
  width: 85.3333vw;

  /* top: unset; */
  /* min-height: 100vh; */
  min-height: calc( 100vh - 11.7333vw );top: 11.7333vw;
}
  .filter-box{
    display: flex;flex-direction: column;
    background-color: #f2f2f2;
    min-height: calc( 100vh - 11.7333vw );
    /* min-height: 100vh; */
  }
  .filter-box .title{
    font-size: 4vw;
    color: #171717;
    margin-bottom: 4.2667vw;
    /* line-height: ; */
  }
  .filter-block{
    line-height: 4.8vw;
    background-color: white;padding: 5.3333vw 5.0667vw;
  }