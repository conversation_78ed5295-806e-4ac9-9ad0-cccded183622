#app{
  background-color: unset;
}
.van-field{
	padding: 0;
}
.van-field__control{
	line-height: 10.6667vw;
	border-bottom: 1px solid #ebedf0;
	box-shadow: inset 0 0 266.6667vw #fff !important;
}
.van-button--info{
	background-color: var(--color-h5-main);
  border: 1px solid var(--color-h5-main);
}
.<PERSON><PERSON><PERSON> {
	color: #ffffff;
	opacity: 0.9;
	font-size: 4.2667vw;
	position: absolute;
	right: 3.3333vw;
	top: 2.9333vw;
}
.log {
	width: 100%;
}
	.log img {
		display: block;
		margin: 10.6667vw auto 10.6667vw;
		width: 26.6667vw;
	}

.xinxi {
	width: 100%;
	flex: 1;
	box-sizing: border-box;
	padding: 0 8.8vw;
}
	.xinxi label {
		font-size: 4.2667vw;
		color: #999;
	}
	.val {
		width: 4rem;
		font-size: 4.5333vw;
		color: #666;
		height: 5.3333vw;
	}
	.line {
		/* width: 90%; */
		border-bottom: 1px solid #ccc;
		height: 10.6667vw;
		box-sizing: border-box;
		position: relative;
		padding-top: 8vw;
	}
	.same {
		width: 82.4vw;
		height: 13.3333vw;
		border-radius: 6.6667vw;
		font-size: 4vw;
	}
	.dl {
		/* background-color: #28ae39; */
		margin-top: 9.0667vw;
		width: 100%;
	}
	.zc {
		background-color: #FFF;
		color: #999;
		margin-top: 5.3333vw;
		border: 1px solid #ebebeb !important;
		display: block;
		text-align: center;
		line-height: 13.3333vw;
	}
.jishu {
	font-size: 3.2vw;
	color: var(--color-h5-text);
	/* left: 0;right: 0;bottom: 0;
	position: fixed; */
	/* margin: 21.3333vw 0 16vw 0; */
	text-align: center;
}