<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>历史展会</title>
	<link href="./vue/element-ui/index.css" rel="stylesheet" type="text/css"/>
	<script src="../js/common/variable.js"></script>
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<script src="./vue/element-ui/index.js"></script>
	<script src="./vue/wangEditor.min.js"></script>

	<link rel="stylesheet" href="../css/member-theme.css">
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
	</script>
	<style type="text/css">
		body, html, #app {
			margin: 0;
			padding: 0;
			height: 100%;
			width: 100%;
		}
		.text-over-2 {
				overflow : hidden;
				text-overflow: ellipsis;
				display: -webkit-box !important;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
		}
		.ex_table_text_title {
			font-size: 16px;
			color: #333333;
			display: inline-block;
			height: 50px;
			width: 253px;
			line-height: 25px;
		}

		.text-over-1{
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 100%;
		}
		.ex_table_text_sub {
			/* display: inline-flex; */
			height: 20px;
			line-height: 20px;
			font-size: 14px;
			color: #666;
			padding-left: 20px;
			position: relative;
			width: 209px;
		}

		.ex_table_text_sub:nth-of-type(2)::before {
			position: absolute;
			top: 1px;
			left: 0;
			display: inline-block;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			content: url("../img/newpage/future_expo_time.png");
		}

		.ex_table_text_sub:last-of-type::before {
			position: absolute;
			top: 1px;
			left: 0;
			display: inline-block;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			content: url("../img/newpage/future_expo_addtr.png");
		}

		.ex-table-img {
			width: 285px;
			height: 170px;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: end;
		}

		/*  .ex-table-img img {
              display: block;
              width: 100%;
              height: 100%;
              overflow: hidden;

          }*/

		.page_header {
			height: 106px;
			display: flex;
			flex-direction: column;
			align-items: start;
			justify-content: space-between;
			padding: 0 30px;
		}

		.page_title {
			align-self: center;
			font-size: 18px;
			color: #333333;
			margin-top: 18px;
		}

		.app_main, .app_foot {
			background-color: #f5f5f5;
		}

		.app_main {
			padding: 20px 100px;
		}

		.app_main_content {
			display: grid;
			grid-template-columns: repeat(auto-fill, 285px);
			grid-row-gap: 30px;
			grid-column-gap: 32px;
		}

		.app_main_content_item {
			background-color: #fff;
			border: solid 1px #eaeaea;
		}

		.app_main_content_item:hover {
			outline: 1px solid #666;
			cursor: pointer;
		}

		.app_main_content_item_info {
			font-size: 14px;
			height: 132px;
			color: #666;
			text-overflow: ellipsis;
			white-space: pre-line;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			/* align-items: start; */
			padding: 14px;
			box-sizing: border-box;
			width: 285px;
			text-align: left;
		}

		.app_main_content_item_info .tag {
			display: inline-block;
			padding: 0 5px;
			height: 20px;
			color: #fff;
			font-size: 12px;
			text-align: center;
			line-height: 20px;
			margin-left: 2px;
			border-radius: 0px 4px 0px 4px;
		}

		.tag_2 {
			background-color: #29a599; /*展商*/
		}

		.tag_4 {
			background-color: #ec6d36; /*观众*/
		}

		.tag_1 {
			background-color: #5888e0; /*主办方*/
		}

		.rang .el-form-item__label {
			padding-right: 0px;
		}
		.errorImg {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.errorImg .el-icon-picture-outline {
			font-size: 48px;
		}
		[v-cloak] {
			display: none;
		}
	</style>
</head>

<body>
<el-container id="app" v-cloak>
	<el-header height="106px" class="page_header">
		<h1 class="page_title">历史展会</h1>
		<el-form :inline="true" :model="form" size="mini">
			<el-form-item label="参与类别">
				<el-select v-model="form.f_proj_role_id">
					<el-option label="所有" value=""></el-option>
					<el-option label="主办方" value="1"></el-option>
					<el-option label="展商" value="2"></el-option>
					<el-option label="服务商" value="3"></el-option>
					<el-option label="观众" value="4"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="">
				<!-- value-format="yyyy-MM-dd" -->
				<el-date-picker
						v-model="form.f_start_date"
						type="date"
						placeholder="开始日期">
				</el-date-picker>
			</el-form-item>
			<el-form-item label="到" class="rang">
			</el-form-item>
			<el-form-item label="">
				<el-date-picker
						v-model="form.f_end_date"
						type="date"
						placeholder="结束日期">
				</el-date-picker>
			</el-form-item>
			<el-form-item label="展会名称">
				<el-input v-model="form.f_project_name"/>
			</el-form-item>

			<el-form-item>
				<el-button type="primary" @click="search">查询</el-button>
			</el-form-item>
		</el-form>
	</el-header>
	<el-main class="app_main">
		<div class="app_main_content"
			v-infinite-scroll="load"
			infinite-scroll-disabled="disabled"
			v-loading="loading"
			>
			<div class="app_main_content_item" @click="jump(item)" v-for="item in showData" :key="item.f_create_org_id">
				<div class="ex-table-img">
					<el-image :src=`//${item.f_server_ip}:${item.f_server_port}/image/${item.f_project_logo}` fit="contain" style="width:100%; height: 100%">
						<div slot="error" class="image-slot errorImg" >
							<i class="el-icon-picture-outline"></i>
						</div>
					</el-image>
				</div>
				<div class="app_main_content_item_info"> <!--1表示主办方 2表示展商 3表示服务商 4表示观众-->
					<span class="ex_table_text_title text-over-2" :title="item.f_project_name || ''">{{item.f_project_name || ''}}<span class="tag" :class="[{tag_1: item.f_proj_role_id ==1}, {tag_2: item.f_proj_role_id ==2}, {tag_4: item.f_proj_role_id ==4}]">{{item.f_proj_role_id | types}}</span></span>
					<span class="ex_table_text_sub">{{item.f_start_date | timeFormat}}-{{item.f_end_date | timeFormat}}</span>
					<span class="ex_table_text_sub text-over-1" :title="item.f_exhibit_place || ''">{{item.f_exhibit_place || ''}}</span>
				</div>
			</div>
			<!--  <div class="app_main_content_item" @click="jump">
                  <div class="ex-table-img">
                      <img src="../img/text.png">
                  </div>
                  <div class="app_main_content_item_info">
                      <span class="ex_table_text_title">2019年杭州电子展<span class="tag tag_2">观众</span></span>
                      <span class="ex_table_text_sub">2019.10.7-2019.10.9</span>
                      <span class="ex_table_text_sub"></span>
                  </div>
              </div>
              <div class="app_main_content_item" @click="jump">
                  <div class="ex-table-img">
                      <img src="../img/text.png">
                  </div>
                  <div class="app_main_content_item_info">
                      <span class="ex_table_text_title">2019年杭州电子展<span class="tag tag_3">主办方</span></span>
                      <span class="ex_table_text_sub">2019.10.7-2019.10.9</span>
                      <span class="ex_table_text_sub"></span>
                  </div>
              </div>-->
		</div>
	</el-main>
	<el-footer class="app_foot">

	</el-footer>
</el-container>

<script>
	var Axios = axios.create({
		baseURL: variableSponsor
	});
	new Vue({
		el: "#app",
		data() {
			return {
				form: {
					f_proj_role_id: '',
					f_start_date: '',
					f_end_date: '',
					f_project_name: ''
				},
				page: 1,
				size: 8,
				total: 0,
				loading: false,
				showData: [],
				user_name: stroage.getItem('usrname')
			}
		},
		methods: {
			load(){
				this.page ++
				this.getData()
			},
			disabled () {
        return this.loading || this.page*this.size < this.total
      },
			search() {
				this.page = 1
				this.getData()
			},
			jump({f_url, f_server_ip, f_server_port, f_proj_role_id, f_client_id, f_project_id, f_project_name, f_exhibit_code, f_proj_id,f_create_org_id,f_zzyuser_id}) {
				window.open(`${window.origin}/eip-web-business/pages/boothinfo/exhibitor_member.html?token=${util.buildAuth(token, f_project_id)}`);
				// let url = `http://${f_server_ip}:${f_server_port}/`
				// if (f_proj_role_id == 1) { // 主办方
				// 	url += `eip-web-sponsor/backstage/indexA?m=${this.user_name}`
				// 	window.open(url)
				// } else if(f_proj_role_id == 2) { // 展商
				// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&cid=${f_client_id}&pid=${f_project_id}&ename=${f_project_name}&exhibitCode=${f_exhibit_code}&typem=2&m=${this.user_name}`
				// 	window.open(url)
				// } else if(f_proj_role_id == 4) {
				// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&pid=${f_project_id}&ename=${f_project_name}&exhibitCode=${f_exhibit_code}&typem=4&m=${this.user_name}`
				// 	window.open(url)
				// } else if(!f_proj_role_id){ // 未关联项目
				// 	// 会员关联项目
				// 	const params = new FormData()
				// 	params.append('zzyuserId', f_zzyuser_id)
				// 	params.append('orgNum', f_create_org_id || '')//localStorage.getItem('orgNum')
				// 	params.append('projId', f_proj_id)
				// 	try{
				// 			Axios.post("http://" + variable + "/tradeProject/queryRelationFocus", params).then(res => {
				// 					let {data: {data, state, msg}} = res;
				// 					if (state == 1) {
				// 							window.open(`${url}eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&pid=${f_project_id}&exhibitCode=${f_exhibit_code}&m=${this.user_name}&typem=4&ename=${f_project_name}`)
				// 					}else{
				// 							throw new Error(msg)
				// 					}
				// 			}).catch(e => {
				// 					this.$message.error('关联项目失败 : '+ e.message)
				// 			})
				// 	}catch(e){
				// 			this.$message.error('关联项目失败 : '+ e.message)
				// 			console.dir(e)
				// 	}
				// }else{
				// 		window.open(url)
				// }
			},
			async getData() {
				this.loading = true
				let params = new FormData()
				let values = Object.entries(this.form)
				for (let [k, v] of values) {
					if (v) {
						params.set(k, v)
					}
				}
				params.set('isUnOpen', false)
				params.set('page', this.page)
				params.set('rows', this.size)
				try {
					let {data} = await Axios.post("http://" + variable + "/tradeProject/getTradeProject", params)
					this.showData = data.rows || data.data || []
					this.total 	  = Number(data.total || 0)
					// console.dir(this.showData)
				} catch (e) {
					console.log(e)
				} finally{
					this.loading = false
				}
			}
		},
		async mounted() {
			this.search()
		},
		filters: {
			types: function (v) {
				let n = Number(v)
				switch (n) {
					case 1:
						return '主办方';
					case 2:
						return '展商';
					case 4:
						return  '观众'
					default:
						return  ''

				}
			},
			timeFormat: function(date) {
				const ymd = {
					year: "numeric",
					month: "2-digit",
					day: "2-digit"
				}
				return  new Date(date).toLocaleDateString(ymd).replace(/\//g, '.')
			}
		},
	})

</script>
</body>

</html>
