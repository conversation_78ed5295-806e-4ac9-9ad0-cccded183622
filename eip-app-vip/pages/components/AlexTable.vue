<!--
  AlexTable 表格组件

  描述：
    对 Element UI 的 el-table 组件进行封装，支持索引列、选择列、自适应列宽、分页、操作列、动态字段等功能。
    支持单选、多选、禁用选择、自定义列插槽、列头插槽，便于扩展复杂业务场景。

  Props 参数：

    @prop {String}   mode - 选择模式，可选值为 'single' | 'multiple' | 'none'，控制选择列的行为，默认 'single'。
    @prop {Array}    data - 表格数据，必填。
    @prop {Array|String} fields - 表头字段配置，可为 'all'（自动生成）或字段数组，支持嵌套子字段。
    @prop {Boolean}  index - 是否显示索引列，默认 true。
    @prop {String}  indexLabel - 索引列名字。
    @prop {Number}   padding - 每列自适应宽度时的内边距值，默认 40。
    @prop {Boolean}  showPagination - 是否显示分页组件，默认 true。
    @prop {Function} handlePagination - 页码或分页大小变化时的回调函数，参数为 { page, rows }。
    @prop {Function} selectable - 控制行是否可选的函数，参数为 row。
    @prop {Boolean}  loading - 表格加载状态。
    @prop {Number}   total - 分页总数。
    @prop {Number|String} operationWidth - 操作列最小宽度，默认 0。
    @prop {Boolean}  showOperation - 是否显示操作列，默认 true。
    @prop {Boolean}  fitLast - 最后一列是否不设置宽度（自动撑满）。
    @prop {Boolean|String} fixed - 表格固定列方向，默认 'left'。
    @prop {Number}   maxWidth - 自适应列最大宽度限制，默认 300。

  插槽：

    @slot selectBefore       - selection 选择列前插槽。
    @slot [prop]             - 每一列的自定义渲染内容插槽，命名为字段名，如：`name`。
    @slot [prop-header]      - 每一列的自定义表头插槽，命名为字段名加 `-header`，如：`name-header`。
    @slot alex-operation     - 操作列插槽，传入 { row, column, $index }。

  事件继承：

    继承所有 el-table 的原生事件，例如 row-click、sort-change 等（通过 $listeners 转发）。

  方法：

    @method clearAll()        - 清空当前选择项（适用于单选）。
    @method selection()       - 获取当前选中项数组。
    @method select(row)       - 手动选择某行。
    @method turnPage(mode)    - 翻页，mode 可为 'prev' 或 'next'。
    @method setPager(page, rows) - 设置分页状态。
    @method computedWidth()   - 自动计算列宽。
    @method doLayout2()       - 手动触发表格重绘。
-->
<template>
  <el-main>
    <el-table
        v-on="$listeners"
        v-bind="$attrs"
        ref="alexTable"
        :data="data"
        @select="checkboxSelect"
        @select-all="selectAll"
        @row-click="rowClick"
        :highlight-current-row="mode !== 'none'"
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgb(255, 255, 255)"
    >
      <el-table-column
        fixed
        :label="indexLabel"
        v-if="index"
        key="index"
        type="index"
        :width="indexWidth"
        :index="i=>(currentPage - 1) * pageSize + i + 1">
      </el-table-column>
      <slot name="selectBefore"></slot>
      <el-table-column
          fixed
          key="selection"
          v-if="mode !== 'none'"
          type="selection"
          :selectable="selectable"
          width="50">
      </el-table-column>
      <el-table-column v-for="field in fieldsArray" v-bind="field" :key="JSON.stringify(field)">
        <template v-slot="{ row, column, $index }">
          <slot v-bind="{ row, column, $index }" :name="field.prop" :column="column"><span>{{row[field.prop]}}</span></slot>
        </template>
        <template v-slot:header="{column, $index}">
          <slot v-bind="{ column, $index }" :name="field.prop + '-header'" ><span>{{column.label}}</span></slot>
        </template>
        <el-table-column v-for="fieldd in (field.children || [])" v-bind="fieldd" :key="JSON.stringify(fieldd)">
          <template slot-scope="{ row, column, $index }">
            <slot v-bind="{ row, column, $index }" :name="fieldd.prop" :column="column">
              <span :style="{color: fieldd.color}">{{ row[fieldd.prop] }}</span>
            </slot>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作" v-if="showOperation" fixed="right" :min-width="operationWidth" show-overflow-tooltip>
        <template slot-scope="{ row, column, $index }">
          <slot name="alex-operation" v-bind="{ row, column, $index }"><span>操作</span></slot>
        </template>
      </el-table-column>
    </el-table>
    <el-footer height="35px" v-if="showPagination">
      <el-pagination
          class="footer-pagination"
          @size-change="pageSizeChange"
          @current-change="pageChange"
          :current-page="currentPage"
          :page-sizes="[20, 50, 100, 200, 1000]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
      </el-pagination>
    </el-footer>
  </el-main>
</template>

<script>
  export default {
    name: "AlexTable",
    props: {
      mode: {
        type: String,
        default: 'single', // or  'multiple' or none
      },
      data: {
        type: Array,
        required: true,
      },
      fields: {
        type: [Array, String],
        default: 'all',
      },
      index: {
        type: Boolean,
        default: true,
      },
      indexLabel: {
        type: String,
        default: '',
      },
      indexPadding: {
        type: Boolean,
        default: false,
      },
      padding: {
        type: Number,
        default: 40,
      },
      showPagination: {
        type: Boolean,
        default: true,
      },
      handlePagination: {
        type: Function,
        default() {
          return _ => _
        }
      },
      selectable: {
        type: Function,
      },
      loading: Boolean,
      total: {
        type: Number,
        default: 0
      },
      operationWidth: {
        type: [Number, String],
        default: 0,
      },
      showOperation: {
        type: Boolean,
        default: true
      },
      fitLast: Boolean,
      fixed: {
        type: [Boolean, String],
        default: 'left'
      },
      // 计算出来的宽度限制
      maxWidth: {
        type: Number,
        default: 300
      }
    },
    data() {
      return {
        pageSize: 20,
        currentPage: 1,
        indexWidth: 40,
        updateFlag: true,
      }
    },
    methods: {
      clearAll() {
        this.mode === 'single' && this.$refs['alexTable'].clearSelection();
      },
      selection() {
        if (this.mode === 'none') return [];
        return this.$refs['alexTable'].selection //获取表格选中行;
      },
      selectAll() {
        this.clearAll()
      },
      select(row) {
        if (this.mode === 'none') return
        if (typeof this.selectable === 'function' && !this.selectable(row)) return
        this.$refs['alexTable'].toggleRowSelection(row, this.mode === 'single' ? !!1 : undefined);
      },
      rowClick(row) {
        if (this.mode === 'multiple') {
          if (typeof this.selectable === 'function' && !this.selectable(row)) return
          this.$refs['alexTable'].clearSelection();
          this.$refs['alexTable'].toggleRowSelection(row, true);
        } else {
          this.clearAll();
          this.select(row)
        }
      },
      checkboxSelect(selection, row) {
        this.clearAll();
        this.mode === 'single' && this.select(row)
      },
      pageSizeChange(val) {
        this.pageSize = val;
        this.handlePagination.call(this, {page: this.currentPage, rows: this.pageSize})
      },
      pageChange(val) {
        this.currentPage = val;
        this.handlePagination.call(this, {page: this.currentPage, rows: this.pageSize})
      },
      getMaxLengthEx(arr) {
        return arr.reduce((acc, item) => {
          if (item) {
            const str = item.toString()
            const char = str.match(/[\u2E80-\u9FFF]/g)
            const charLen = char ? char.length : 0
            const num = str.match(/\d|\./g)
            const numLen = num ? num.length : 0
            const otherLen = str.length - charLen - numLen
            let calcLen = charLen * 1.08 + numLen * 0.65 + otherLen * 0.5
            if (acc < calcLen) {
              acc = calcLen
            }
          }
          return acc
        }, 0)
      },
      getTextWidth(str) {
        let width = 0;
        console.log(str)
        let html = document.createElement("span");
        html.innerText = str;
        html.className = "getTextWidth";
        document.querySelector('body').appendChild(html);
        width = document.querySelector(".getTextWidth").offsetWidth;
        document.querySelector(".getTextWidth").remove();
        return width;
      },
      getMaxLength(arr, extraWidth) {
        const width = this.getMaxLengthEx(arr)
        return `${Math.min(width * 14 + this.padding + 24 + extraWidth, this.maxWidth)}px`
      },
      doLayout2(){
        this.$nextTick(()=> {
          console.log('doLayout')
          this.$refs['alexTable'].doLayout();
        })
      },
      // doLayout(){
      //   if(!this.updateFlag)return
      //   this.updateFlag = false
      //   setTimeout(()=>this.updateFlag = true, 100)
      //   this.$nextTick(()=> {
      //     console.log('doLayout')
      //     this.$refs['alexTable'].doLayout();
      //   })
      // },
      turnPage(mode) {
        if (mode === 'prev') {
          if (this.currentPage <= 1) {
            return '前面没有数据了'
          } else {
            this.currentPage--
          }
        } else if (mode === 'next') {
          if (this.currentPage >= Math.ceil(this.total / this.pageSize)) {
            return '没有数据了'
          } else {
            this.currentPage++
          }
        }
      },
      computedWidth() {
        return new Promise(resolve => {
          this.$nextTick(() => {
            this.fieldsArray = this.fieldsArray.map(field => {
              if (field.fixedWidth) return
              // 获取每一列的所有数据
              const arr = this.data.map(data => data[field.prop])
              // 把每列的表头也加进去算
              arr.push(field.label)
              // 每列内容最大的宽度 + 表格的内间距(依据实际情况而定) + 附加宽度
              field.width = this.getMaxLength(arr, field.extraWidth || 0) || field.defaultWidth || 110
              return field
            });
            this.fitLast && this.$set(this.fieldsArray[this.fieldsArray.length - 1], 'width', '')
            //计算一下index的宽度
            this.calculateIndexWidth()
            this.$refs['alexTable'] && this.$refs['alexTable'].doLayout()
            resolve(this.$refs['alexTable'])
          })
        })
      },
      setPager(page, rows) {
        this.currentPage = page;
        this.pageSize = rows;
      },
      calculateIndexWidth() {
        const wi = String(this.currentPage * this.pageSize)
        let i = wi.length
        this.indexWidth = 40 + (this.indexPadding ? this.padding : 0)
        while (i > 2) {
          this.indexWidth += 10
          i--
        }
      }
    },
    computed: {
      fieldsArray: {
        get() {
          if (typeof this.fields === 'string' && this.fields === 'all' && this.data.length > 0)
            return Object.keys(this.data[0]).map(v => {
              return {prop: v, label: v}
            })
          // v-bind="propObject"的优先级低于v-bind:prop="value"
          return this.fields.map(item => {
            return {
              sortable: "custom",
              'show-overflow-tooltip': true,
              ...item //直接从field中合并进去，可以实现自定义列属性
            }
          })
        },
        set(newVal) {
          return newVal
        }
      }
    },
    watch: {
      data() {
        this.calculateIndexWidth()
        this.$nextTick(() => {
          this.$refs['alexTable'].doLayout();
        })
      },
      fieldsArray() {
        this.$nextTick(() => {
          this.$refs['alexTable'].doLayout();
        })
      }
    },
  }
</script>

<style scoped>
  .alex-table .el-footer {
    display: flex;
    align-items: center;
    justify-content: left;
    background-color: #F4F4F4;
    border: 1px solid #ddd;
    border-top-color: transparent;
  }

  .alex-table .el-table th {
    padding: 0;
  }

  .alex-table .el-table .is-center .cell {
    text-align: center;
    margin-left: -10px;
  }

  .alex-table .el-table .is-right .cell {
    text-align: right;
  }
  .alex-table .el-table .cell {
    text-align: left;
    color: #000 !important;
    font-weight: normal;
    font-size: 14px;
    line-height: 18px;
    padding: 0 15px;
  }

  .el-table--border th.gutter:last-of-type {
    display: block !important;
    width: 17px !important;
  }

  .alex-table {
    padding-left: 3px;
    padding-right: 3px;
  }

  .alex-table .el-table {
    border: 1px solid #ddd;
    width: 99.9%;
  }

  .alex-table .el-table .caret-wrapper {
    height: 30px;
  }

  .alex-table .el-table__header {
    background-color: #efefef;
    background: -webkit-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: -moz-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: -o-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: linear-gradient(to bottom, #F9F9F9 0, #efefef 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F9F9F9, endColorstr=#efefef, GradientType=0);
    border-bottom: 1px solid #ddd;
  }

  .alex-table .el-table__header .cell {
    color: #707477;
    white-space: nowrap;
    text-overflow: clip;
  }

  .alex-table .sort-caret.ascending {
    top: 3px;
  }

  .alex-table .sort-caret.descending {
    bottom: 5px;
  }

  .alex-table .el-table__header *, .alex-table .footer-pagination * {
    background-color: transparent !important;
  }

  .alex-table .el-checkbox__input.is-checked .el-checkbox__inner,
  .alex-table .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409EFF !important;
  }

  .alex-table .el-checkbox__input .el-checkbox__inner {
    background-color: #FFF !important;
  }

  .alex-table .el-table__row [class^="el-table"]:nth-child(1) {
    background-color: #efefef;
    background: -webkit-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: -moz-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: -o-linear-gradient(top, #F9F9F9 0, #efefef 100%);
    background: linear-gradient(to bottom, #F9F9F9 0, #efefef 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F9F9F9, endColorstr=#efefef, GradientType=0);
  }

  .alex-table .el-table__row [class^="el-table"]:nth-child(1) .cell {
    padding: 0;
  }

  .alex-table .el-table table[class^="el-table__"] tr > *:first-child .cell,
  .alex-table .el-table table[class^="el-table__"] tr > *:last-child .cell {
    text-align: center;
  }

  .alex-table .el-table--enable-row-transition .el-table__body td {
    transition: none;
  }

  .alex-table .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: 1px dotted #ccc;
  }

  .alex-table .el-table td, .alex-table .el-table th.is-leaf {
    border-bottom: 1px dotted #ccc;
  }
</style>
<style scoped>
  /* table css start */
  .alex-table-ant {
    width: 100%;
    padding-top: 20px;
  }

  .alex-table-ant .el-footer {
    display: flex;
    align-items: center;
    justify-content: left;
    background-color: #F4F4F4;
    border: 1px solid #ddd;
    border-top-color: transparent;
  }

  .alex-table-ant .el-table th {
    padding: 8px 0;
  }

  .alex-table-ant .el-table .is-center .cell {
    text-align: center;
    margin-left: -10px;
  }

  .alex-table-ant .el-table .cell {
    /* text-align: left; */
    color: #000 !important;
    font-weight: normal;
    font-size: 14px;
    line-height: 18px;
    padding: 0 15px;
  }

  .el-table--border th.gutter:last-of-type {
    display: block !important;
    width: 17px !important;
  }

  .alex-table-ant {
    padding-left: 3px;
    padding-right: 3px;
  }

  .alex-table-ant .el-table {
    border: 1px solid #ddd;
    width: 99.9%;
  }

  .alex-table-ant .el-table .caret-wrapper {
    height: 30px;
  }

  .alex-table-ant .el-table__header {
    background-color: #fafafa;
    /* background-color: #efefef;
    background: -webkit-linear-gradient(top,#F9F9F9 0,#efefef 100%);
    background: -moz-linear-gradient(top,#F9F9F9 0,#efefef 100%);
    background: -o-linear-gradient(top,#F9F9F9 0,#efefef 100%);
    background: linear-gradient(to bottom,#F9F9F9 0,#efefef 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F9F9F9,endColorstr=#efefef,GradientType=0);
    border-bottom: 1px solid #ddd; */
  }

  .alex-table-ant .el-table__header .cell {
    color: #707477;
    white-space: nowrap;
    text-overflow: clip;
  }

  .alex-table-ant .sort-caret.ascending {
    top: 3px;
  }

  .alex-table-ant .sort-caret.descending {
    bottom: 5px;
  }

  .alex-table-ant .el-table__header *, .alex-table-ant .footer-pagination * {
    background-color: transparent !important;
  }

  .alex-table-ant .el-checkbox__input.is-checked .el-checkbox__inner,
  .alex-table-ant .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409EFF !important;
  }

  .alex-table-ant .el-checkbox__input .el-checkbox__inner {
    background-color: #FFF !important;
  }

  .alex-table-ant .el-table__row [class^="el-table"]:nth-child(1) .cell {
    padding: 0 10px;
  }

  .alex-table-ant .el-table table[class^="el-table__"] tr > *:first-child:not([class*="is-left"]) .cell,
  .alex-table-ant .el-table table[class^="el-table__"] tr > *:last-child:not([class*="is-left"]) .cell {
    text-align: center;
  }

  .alex-table-ant .el-table--enable-row-transition .el-table__body td {
    transition: none;
  }

  .alex-table-ant .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: 1px solid #efefef;
  }

  .alex-table-ant .el-table td, .alex-table-ant .el-table th.is-leaf {
    border-bottom: 1px solid #efefef;
  }

  /* table css end */
</style>