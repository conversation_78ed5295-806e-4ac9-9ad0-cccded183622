<template>
  <custom-dialog
    :show="show"
    show-header
    @before-close="handleClose"
    title="申请认证"
    width="600px"
  >
    <template #header-right>
      <el-button :loading="loading" size="mini" type="primary" @click="save()">提交</el-button>
    </template>
    <el-container v-loading="loading">
      <el-main style="max-height: 70vh; overflow: auto">
        <el-form
          :model="form"
          ref="certificationForm"
          :rules="rules"
          label-width="100px"
          label-position="right"
          size="small"
        >
          <!--<el-form-item label="品牌" prop="brandId">
            <el-select
              v-model="form.brandId"
              placeholder="请选择品牌"
              clearable
              filterable
              style="width: 360px;"
            >
              <el-option
                v-for="item in brandList"
                :key="item.projectId"
                :label="item.projectName"
                :value="item.projectId">
              </el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item label="营业执照" prop="businessLicense">
            <!-- Upload Component -->
            <el-upload
              v-if="!form.businessLicense"
              drag
              :action="uploadUrl"
              :headers="authHeaders"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :show-file-list="false"
              style="width: 100%;"
              accept="image/*"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="display:flex;flex-direction: column">
                点击或将文件拖拽到这里上传
                <span>
                  支持扩展名：.jpg .jpeg .bmp .png .gif...
                </span>
              </div>
            </el-upload>

            <!-- Preview Uploaded Image/File -->
            <div v-else class="license-preview">
              <el-image
                v-if="isImageUrl(form.businessLicense)"
                style="width: 100px; height: 100px"
                :src="form.businessLicense"
                :preview-src-list="[form.businessLicense]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div v-else class="file-preview">
                <i class="el-icon-document"></i>
                <span>{{ getFileName(form.businessLicense) }}</span>
                <el-link :href="form.businessLicense" type="primary" target="_blank" :underline="false"
                         style="margin-left: 10px;">查看
                </el-link>
              </div>
              <el-button type="danger" icon="el-icon-delete" size="mini" circle @click="handleRemoveLicense"
                         style="margin-left: 15px;"></el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-main>
    </el-container>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue"; // Adjust path as needed
// import ZzMixin from '@/mixins/ZzMixin'; // Assuming ZzMixin path

// Placeholder for ZzMixin if not globally available
const ZzMixin = {
  data() {
    return {
      show: false,
      loading: false,
      formChange: false,
      formInit: {},
    }
  },
  watch: {
    form: {
      handler(n, o) {
        if (JSON.stringify(n) !== JSON.stringify(this.formInit)) {
          this.formChange = true;
        }
      },
      deep: true,
    },
  },
  methods: {
    // --- Basic Mixin Methods (Replace with actual ZzMixin) ---
    JSON2FormData(obj) { // Assuming ZzMixin provides this
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    initForm() {
      if (this.$refs.certificationForm) {
        this.$refs.certificationForm.resetFields(); // Reset validation and fields bound to form model
      }
      // Deep copy initial state if needed, or rely on resetFields + specific resets
      this.form = JSON.parse(JSON.stringify(this.formInitData || {brandId: '', businessLicense: ''}));
    },
    async handleClose(force = false) { // Renamed from close to avoid conflict if mixin uses 'close'
      if (force === true) this.formChange = false
      if (this.formChange && !force) {
        try {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return false // Prevent closing
        }
      }
      this.$refs.certificationForm && this.$refs.certificationForm.clearValidate()
      this.show = false
      this.formChange = false // Reset flag on successful close
      return true // Allow closing
    },
    ok(message = '操作成功!') {
      this.$message({message, type: 'success', showClose: true});
    },
    error(msg = '操作失败', throws = true) {
      this.$message.error(msg);
      this.loading = false;
      if (throws) throw new Error(msg);
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      this.$alert(msg, title, {type});
      this.loading = false;
    },
    checkRet(obj) {
      const {state, msg, data} = obj
      if (state !== 1) this.error(msg) // Assuming state 1 is success
    },
    // --- End Placeholder Mixin ---
  }
}
export default {
  name: "CertificationApplyDialog",
  mixins: [ZzMixin],
  components: {CustomDialog},
  data() {
    const validateLicense = (rule, value, callback) => {
      if (!this.form.businessLicense) {
        callback(new Error('请上传营业执照'));
      } else {
        callback();
      }
    };

    return {
      // Inherited from mixin: show, loading, formChange
      form: {
        brandId: '',
        businessLicense: '', // Stores the URL of the uploaded file
      },
      formInitData: { // Store initial structure for reset
        brandId: '',
        businessLicense: '',
      },
      rules: {
        brandId: [{required: true, message: '请选择品牌', trigger: 'change'}],
        businessLicense: [{required: true, validator: validateLicense, trigger: ['blur', 'change']}],
      },
      brandList: [], // To store brand options { id: number, name: string }
      currentEnterpriseId: null, // Store enterpriseId passed via open
      uploadUrl: '/eip-web-vip/upload/uploadFileOss', // !!! PLEASE REPLACE WITH YOUR ACTUAL UPLOAD ENDPOINT
    };
  },
  computed: {
    authHeaders() {
      // Assuming Axios headers are configured globally or accessible like this
      // Adapt based on your actual Axios setup
      return {
        Authorization: Axios.defaults.headers.common['Authorization'] || '',
        Appid: Axios.defaults.headers.common['Appid'] || ''
      };
    }
  },
  methods: {
    open({enterpriseId}) {
      if (!enterpriseId) {
        this.errorMsg('缺少必要的参数');
        return;
      }
      this.currentEnterpriseId = enterpriseId;
      this.initForm(); // Reset form using mixin/placeholder method
      this.brandList = []; // Clear previous list
      this.getBrandList();
      this.show = true;
      this.formChange = false; // Reset change flag after opening
      this.$nextTick(() => {
        this.$refs.certificationForm && this.$refs.certificationForm.clearValidate();
      });
    },
    async getBrandList() {
      this.loading = true; // Optional: Show loading during brand fetch
      try {
        const payload = {orgNum: util.getOrgNum()};
        const {data} = await Axios.post('/project/getTopLevelBrandProject', JSON2FormData(payload));

        if (data.state !== 1) {
          this.errorMsg(data.msg || '获取品牌列表失败');
          await Promise.reject(data.msg); // Stop execution if needed
        }
        this.brandList = data.data || [];
      } catch (e) {
        // Avoid showing generic error if specific error was already shown
        if (typeof e === 'string' && e !== 'exit,close,cancel') {
          console.warn('获取品牌列表失败:', e);
        } else if (!(typeof e === 'string' && e === 'exit,close,cancel')) {
          console.warn('获取品牌列表异常:', e);
          this.errorMsg('获取品牌列表时发生错误');
        }
      } finally {
        this.loading = false;
      }
    },

    beforeUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      const isTypeValid = allowedTypes.includes(file.type) || /\.(jpg|jpeg|png|gif)$/i.test(file.name);


      if (!isTypeValid) {
        this.$message.error('上传文件格式不支持!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!');
        return false;
      }
      this.loading = true;
      return true;
    },

    handleUploadSuccess(response, file, fileList) {
      this.loading = false;
      // Adjust based on your actual API response structure
      if (response.state === 1 && response.result && response.result.path) {
        this.form.businessLicense = response.result.path;
        this.formChange = true; // Mark form as changed
        this.$refs.certificationForm.validateField('businessLicense'); // Re-validate the field
        this.ok('上传成功!');
      } else {
        this.errorMsg(response.msg || '上传失败，请重试');
        this.form.businessLicense = ''; // Clear on failure
      }
    },

    handleUploadError(err, file, fileList) {
      this.loading = false;
      this.errorMsg('上传失败，请检查网络或联系管理员');
      console.error("Upload Error:", err);
      this.form.businessLicense = ''; // Clear on failure
    },

    handleRemoveLicense() {
      this.form.businessLicense = '';
      this.formChange = true;
      // Optional: If using el-upload internal file list, you might need to clear it:
      // this.$refs.uploadComponentRef.clearFiles();
    },

    isImageUrl(url) {
      if (!url) return false;
      return /\.(jpeg|jpg|gif|png)$/i.test(url);
    },

    getFileName(url) {
      if (!url) return '';
      try {
        const parsedUrl = new URL(url);
        const pathSegments = parsedUrl.pathname.split('/');
        return pathSegments.pop() || '未知文件';
      } catch (e) {
        // Fallback for non-URL strings or parsing errors
        const parts = url.split('/');
        return parts.pop() || '未知文件';
      }
    },

    async save() {
      try {
        await this.$refs.certificationForm.validate();
      } catch (e) {
        // Validation failed - element-ui usually highlights fields
        return;
      }

      this.loading = true;
      const payload = {
        enterpriseId: this.currentEnterpriseId,
        brandId: this.form.brandId,
        businessLicense: this.form.businessLicense,
      };

      try {
        const {data} = await Axios.post('/enterprise/applyCertification', payload);

        if (data.state === 1) {
          this.ok('申请成功!');
          this.$emit('finish'); // Notify parent component
          await this.handleClose(true); // Close dialog force
        } else if (data.state === *********) {
          // Duplicate company found
          await this.handleDuplicateEnterprise(data.data); // data.data contains duplicate info
        } else {
          // Other known errors (*********, *********) or general errors
          this.errorMsg(data.msg || `申请失败 (状态码: ${data.state})`);
        }
      } catch (e) {
        console.error("Save Error:", e);
        // errorMsg might be called by Axios interceptor, otherwise call here
        if (!this.loading) { // Check if errorMsg was already called setting loading to false
          this.errorMsg('提交申请时发生错误');
        }
      } finally {
        // Ensure loading is always turned off unless handled by sub-function
        // handleDuplicateEnterprise manages its own loading state
        if (this.loading && !(payload.state === *********)) {
          this.loading = false;
        }
      }
    },

    async handleDuplicateEnterprise(duplicateData) {
      if (!duplicateData || !duplicateData.enterpriseId) {
        this.errorMsg('处理同名企业信息时出错：缺少企业ID');
        this.loading = false;
        return;
      }
      const duplicateEnterpriseId = duplicateData.enterpriseId;

      try {
        await this.$confirm('已存在同名公司，是否申请成为该公司人员?', '提醒', {
          confirmButtonText: '确认申请',
          cancelButtonText: '取消',
          type: 'warning'
        });
        this.loading = true;
        const {data: applyData} = await Axios.post('/enterprisePerson/saveCardOrApply', {enterpriseId: duplicateEnterpriseId})

        if (applyData.state === 1) {
          this.ok('子账号申请已提交');
          this.$emit('finish'); // Notify parent
          await this.handleClose(true); // Force close dialog
        } else {
          this.errorMsg(applyData.msg || '提交子账号申请失败');
        }
      } catch (e) {
        // Handle cancellation or error during confirm/API call
        if (e === 'cancel') {
          console.log('用户取消申请子账号');
        } else {
          console.error("Sub-account application error:", e);
          // errorMsg might have been called by Axios interceptor
          if (this.loading) {
            this.errorMsg('提交子账号申请时发生错误');
          }
        }
      } finally {
        this.loading = false; // Ensure loading is off
      }
    }
  },
  mounted() {
    // Assign initial form structure for reset purposes
    this.formInitData = JSON.parse(JSON.stringify(this.form));
  }
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px; /* Standard element-ui margin */
}

.el-upload-dragger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.el-upload-dragger .el-icon-upload {
  margin: 0;
}

.license-preview {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
  width: 360px;
  box-sizing: border-box;
}

.license-preview .el-image {
  border: 1px solid #eee;
  border-radius: 4px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.file-preview {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.file-preview i {
  font-size: 24px;
  margin-right: 8px;
  color: #909399;
}

.file-preview span {
  max-width: 250px; /* Prevent long file names from breaking layout */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>