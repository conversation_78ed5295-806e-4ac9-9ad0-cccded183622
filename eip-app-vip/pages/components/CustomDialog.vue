<!--
  CustomDialog 组件

  描述：
    基于 Element UI 的二次封装弹窗，支持拖拽、自定义主题、双弹窗模式、自定义头部等功能。

  Props 参数：

    @prop {Boolean} doubleModal - 是否启用双弹窗模式，开启后禁用遮罩和 append-to-body，适合嵌套弹窗。
    @prop {String}  doubleModalLeft - 双弹窗模式下左侧偏移位置，默认 20px。
    @prop {String}  theme - 弹窗主题风格，可选值：'default' | 'plain'，默认 'default'。
    @prop {String}  width - 弹窗宽度，如 '600px'、'50%'。
    @prop {String}  title - 弹窗标题。
    @prop {Boolean} show - 弹窗显示控制，由 before-close 事件接管。
    @prop {Boolean} showHeader - 是否显示自定义头部（使用 header 插槽）。
    @prop {String}  headerHeight - 头部高度，默认 '40px'。
    @prop {String}  headerBgColor - 头部背景色，默认 '#fff'。
    @prop {Boolean} append2body - 是否 append 到 body，仅在非双弹窗模式下生效。

  插槽：
    @slot default       - 弹窗主体内容
    @slot header        - 自定义头部内容（需 showHeader 为 true）
    @slot header-left   - 自定义头部左侧区域（嵌套在 header 插槽内）
    @slot header-right  - 自定义头部右侧区域（嵌套在 header 插槽内）
    @slot footer        - 弹窗底部内容（映射 el-dialog 的 footer）

  事件：
    @event before-close - 弹窗关闭前触发事件，参数为关闭事件对象，可用于阻止关闭或确认操作。
-->

<template>
  <el-dialog
    :title="title"
    :visible.sync="show"
    :width="width"
    :close-on-click-modal="false"
    v-drag-move
    v-bind="$attrs"
    v-on="$listeners"
    :modal="doubleModal ? false : $attrs.modal"
    :append-to-body="doubleModal ? false : append2body"
    :custom-class="'custom-dialog--' + theme"
    :class="{ 'double-modal': doubleModal }"
    :before-close="handleClose"
  >
    <el-header class="dialog-header" :style="{ backgroundColor: headerBgColor }" :height="headerHeight" v-if="showHeader">
      <slot name="header">
        <div class="dialog-header-left">
          <slot name="header-left"></slot>
        </div>
        <div class="dialog-header-right">
          <slot name="header-right"></slot>
        </div>
      </slot>
    </el-header>
    <slot></slot>
    <slot name="footer" slot="footer"></slot>
  </el-dialog>
</template>
<script>
export default {
  name: 'CustomDialog',
  props: {
    doubleModal: {
      type: Boolean,
      default: false,
    },
    doubleModalLeft: {
      type: String,
      default: '20px',
    },
    theme: {
      type: String,
      default: 'default', // default, plain
    },
    width: {
      type: String,
    },
    title: {
      type: String,
      default: '',
    },
    show: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: false,
    },
    headerHeight: {
      type: String,
      default: '40px',
    },
    headerBgColor: {
      type: String,
      default: '#fff',
    },
    append2body: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleClose(event) {
      this.$emit('before-close', event)
    },
  },
}
</script>
<style>
:root {
  --double-modal-left: 0;
}

.el-dialog__wrapper.double-modal {
  width: 0;
  display: table;
  table-layout: fixed;
  margin-left: var(--double-modal-left, 0);
  overflow: visible;
}

.custom-dialog--default {
  min-width: 300px;
}
.custom-dialog--default .el-dialog__header {
  padding: 0 10px 0 30px;
  height: 36px;
  background-color: #2e82e4;
  color: #fff;
  font-size: 14px;
}
.custom-dialog--default .el-dialog__header .el-dialog__headerbtn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  top: 0 !important;
}
.custom-dialog--default .el-dialog__header .el-dialog__headerbtn:hover {
  background-color: #ff685e;
}
.custom-dialog--default .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
  font-size: 14px;
  line-height: 36px;
}
.custom-dialog--default .el-dialog__header .el-dialog__title {
  color: #fff;
  font-size: 14px;
  line-height: 36px;
}
.custom-dialog--default .dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
.custom-dialog--default .el-dialog__body {
  padding: 0;
}

.custom-dialog--plain {
  min-width: 300px;
}
.custom-dialog--plain .el-dialog__header {
  padding: 0 10px 0 30px;
  height: 56px;
  background: none;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 56px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.custom-dialog--plain .el-dialog__header .el-dialog__headerbtn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  top: 10px !important;
  transition: transform 0.6s;
}
.custom-dialog--plain .el-dialog__header .el-dialog__headerbtn:hover {
  transform: scale(1.2);
}
.custom-dialog--plain .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 36px;
}
.custom-dialog--plain .el-dialog__header .el-dialog__title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 36px;
}
.custom-dialog--plain .dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
.custom-dialog--plain .el-dialog__body {
  padding: 0;
}

.custom-dialog--confirm .el-dialog__body {
  padding: 0;
}
.custom-dialog--confirm .el-dialog__header .el-dialog__title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  line-height: 36px;
}
.custom-dialog--confirm .el-dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  border-bottom: 1px solid #efefef;
  /* box-shadow: 0 0 4px #ccc; */
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
.custom-dialog--confirm .el-footer {
  height: auto !important;
}
.custom-dialog--confirm .el-dialog__footer {
  padding: 10px 0 10px 0;
  background-color: #F8F8F8;
}
</style>
