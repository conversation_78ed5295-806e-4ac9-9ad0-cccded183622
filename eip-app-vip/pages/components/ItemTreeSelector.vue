<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    :title="title"
    show-header
    width="300px"
    append2body>
    <template #header-right>
      <el-button size="mini" type="primary" @click="confirm()">确定</el-button>
    </template>
    <el-main class="dialog-body">
      <el-container style="padding: 20px;">
        <el-tree
          style="flex-grow: 1;"
          check-strictly
          check-on-click-node
          :default-checked-keys="defaultCheckedKeys"
          :data="treeList"
          show-checkbox
          :props="{ children: 'children', label: 'name'}"
          :default-expanded-keys="treeList[0] ? treeList.map(item => item.id).filter(Boolean) : null"
          node-key="id"
          ref="tree">
        </el-tree>
      </el-container>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "./CustomDialog.vue";
export default {
  mixins: [Mixins.itemKindCode(false)],
  name: "ItemTreeSelector",
  props: {
    title: {
      type: String,
      default: '选择客户群体'
    },
    itemKindCode: {
      type: String,
    },
    leafSelectOnly: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      show: false,
      promise: null,
      treeList: [],
      defaultCheckedKeys: [],
    }
  },
  methods: {
    async open(itemKindCode, defaultCheckedKeys) {
      itemKindCode = itemKindCode || this.itemKindCode
      this.defaultCheckedKeys = defaultCheckedKeys || []
      if (!itemKindCode) await Promise.reject('itemKindCode is required')
      const {data} = await this.getItemKindCodeTree(itemKindCode)
      this.treeList = data.data
      if (this.leafSelectOnly) {
        const deep = list => {
          list.forEach(item => {
            item.id = item.code || item.id
            if (item.children && item.children.length) {
              item.disabled = true
              item.children = deep(item.children)
            }
          })
          return list
        }
        this.treeList = deep(this.treeList)
      }
      this.show = true
      return new Promise((resolve, reject) => this.promise = {resolve, reject})
    },
    confirm() {
      const result = this.$refs.tree.getCheckedNodes()
      if (!result.length)
        return this.$errorMsg('请至少选择一条数据', '提醒', 'warning')
      this.$emit('result', result)
      this.promise && this.promise.resolve(result)
      this.close(!0)
    },
    close(noReject) {
      this.show = !1
      noReject && this.promise && this.promise.reject()
      this.promise = null
    },
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.dialog-header .dialog-header-left {
  color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
  padding: 0 30px 0 5px;
  line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 50vh;
}


.team-table .el-table__header th {
  background-color: #63A9FB;
  color: #fff !important;
  text-align: center;
}

.team-table .el-table__row tr {
  padding: 5px 0;
}

.team-table .el-table__row > *:not(:last-of-type) {
  text-align: center;
}

.dialog-body i {
  color: inherit !important;
}
</style>