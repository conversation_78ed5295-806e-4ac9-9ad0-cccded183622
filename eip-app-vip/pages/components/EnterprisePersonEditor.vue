<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    :title="title"
    width="337px"
  >
    <div
      class="iframe-wrapper"
      v-loading="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.1)"
    >
      <iframe v-if="show" class="iframe" :src="iframeSRC" @load="onLoaded"></iframe>
    </div>

  </custom-dialog>
</template>

<script>
import CustomDialog from "./CustomDialog.vue";

export default {
  name: "EnterprisePersonEditor",
  components: {CustomDialog},
  data() {
    return {
      show: false,
      loading: false,
      resolve: null,
      options: {}
    }
  },
  computed: {
    title() {
      return ''
    },
    iframeSRC() {
      if (!this.show) return ''
      const target = new URL('/eip-app-vip/mobile-vipcenter/personCard/index.html', location.origin)
      target.search = location.search
      const {enterprisePersonId, enterpriseId, personName, readonly, component} = this.options || {}
      enterprisePersonId && target.searchParams.set('epid', enterprisePersonId)
      enterpriseId && target.searchParams.set('eid', enterpriseId)
      personName && target.searchParams.set('pn', personName)
      readonly && target.searchParams.set('r', 'true')
      component && target.searchParams.set('c', component)
      return target.href
    },
  },
  methods: {
    open(options) {
      this.loading = true
      this.show = true
      this.options = options
      window.onEnterprisePersonSaveCallback = (meta) => {
        this.close()
        if (meta && meta.isApply) {
          this.$emit('apply-handled')
        }
      }
      return new Promise((resolve) => {
        this.resolve = resolve
      })
    },
    close() {
      this.show = false
      this.options = {}
      this.resolve && this.resolve()
      this.resolve = null
      window.onEnterprisePersonSaveCallback = null
    },
    onLoaded() {
      this.$nextTick(() => {
        this.loading = false
      })
    }
  },
}
</script>

<style scoped>
.iframe-wrapper {
  width: 337px;
  height: 600px;
}

.iframe {
  border: none;
  width: 100%;
  height: 100%;
}
</style>