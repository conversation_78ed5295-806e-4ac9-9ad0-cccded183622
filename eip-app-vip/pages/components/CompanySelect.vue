<template>
  <custom-dialog
    class="company-select"
    :show="show"
    @before-close="handleClose()"
    title="公司选择"
    width="800px"
    append2body
  >
    <el-main>
      <!--header-->
      <el-row type="flex" justify="space-between">
        <el-col :span="2">
          <span style="line-height: 27px">公司名称</span>
        </el-col>
        <el-col :span="12">
          <el-input
            size="mini" @keyup.native.enter="queryCompany()" clearable @clear="queryCompany()" v-model="companyName"
            style="width: 100%;" placeholder="公司名称"></el-input>
        </el-col>
        <el-col :span="2">
          <el-button size="mini" type="info" plain @click="queryCompany()">查询</el-button>
        </el-col>
        <el-col :span="2" :offset="2">
          <el-button size="mini" type="primary" plain @click="subForm()">确定</el-button>
        </el-col>
        <el-col :span="2">
          <el-button size="mini" type="danger" plain @click="handleClose()">取消</el-button>
        </el-col>
      </el-row>
      <el-container class="table-container">
        <el-table
          ref="company"
          size="small"
          :data="companyList"
          height="250"
          v-loading="loading"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.1)"
          highlight-current-row
          @row-dblclick="subForm"
          @row-click="rowClick"
          :border="true"
          style="width: 100%">
          <!--index-->
          <el-table-column
            width="40"
            type="index">
          </el-table-column>
          <!--selection-->
          <el-table-column
            type="selection">
          </el-table-column>
          <!--projectId-->
          <el-table-column
            width="105"
            sortable
            prop="clientId"
            label="公司编号">
          </el-table-column>
          <!--projectName-->
          <el-table-column
            show-overflow-tooltip
            sortable
            prop="companyName"
            label="公司名称">
          </el-table-column>
        </el-table>
      </el-container>
      <el-footer>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[20, 50, 100, 200, 1000]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-footer>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "./CustomDialog.vue";

export default {
  name: "CompanySelect",
  data() {
    return {
      show: false,
      companyList: [],
      companyName: '',
      currentPage: 1,
      pageSize: 20,
      loading: false,
      total: 0,
      promise: null
    }
  },
  methods: {
    //点击确定或者双击行
    subForm(row) {
      let _row = null
      if (row && typeof row === 'object') {
        _row = row
      } else if (this.$refs.company && this.$refs.company.selection.length) {
        _row = this.$refs.company.selection[0]
      }
      if (!_row) return this.$alert('请先选择公司名称!', '提示', {type: 'warning'})
      this.$emit('finish', _row);
      this.handleClose(_row)
    },
    //分页器翻页
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.queryCompany({page: currentPage})
    },
    //修改页面数量
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.queryCompany()
    },
    //处理表格行点击
    rowClick(row) {
      this.$refs['company'].clearSelection();
      this.$refs['company'].toggleRowSelection(row, true);
    },
    //处理关闭事件
    handleClose(payload) {
      this.$emit('close')
      this.$refs['company'] && this.$refs['company'].clearSelection()
      this.show = false
      payload ? (this.promise.resolve && this.promise.resolve(payload)) :
        (this.promise.reject && this.promise.reject('cancel'))
    },
    queryCompany(data) {
      this.loading = true;
      this.$refs['company'] && this.$refs['company'].clearSelection();
      const queryData = {
        clientTypeCode: 'B_CLIENT',
        companyName: this.companyName,
        // fWorkteamId: teamId,
        page: 1,
        rows: this.pageSize
      }
      Axios.post('/client/getList', JSON2FormData(Object.assign(queryData, data))).then(({data: res}) => {
        this.companyList = res.rows;
        this.loading = false;
        this.total = res.total;
      }).finally(() => {
        this.loading = false;
      })
    },
    async open() {
      return new Promise((resolve, reject) => {
        this.show = true
        this.queryCompany()
        this.promise = {
          resolve,
          reject
        }
      })
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.company-select .table-container {
  margin-top: 1rem;
}

.company-select .el-pagination {
  text-align: center;
}

.company-select .el-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>