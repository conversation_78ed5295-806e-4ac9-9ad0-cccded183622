<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    :title="title"
    show-header
    width="fit-content"
    append2body>
    <template #header-right>
      <el-button size="mini" type="primary" @click="confirm()">确定</el-button>
    </template>
    <el-main class="dialog-body">
      <el-container style="padding: 20px;min-width: 300px;max-width: 1000px;flex-direction: column">
        <el-input
          style="margin-bottom: 10px"
          size="small"
          v-model="filterValue"
          clearable
          placeholder="输入关键字进行过滤"></el-input>
        <el-tree
          style="flex-grow: 1;"
          check-strictly
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="defaultCheckedKeys"
          :data="treeList"
          show-checkbox
          :props="{ children: 'children', label: labelKey}"
          node-key="idx"
          :filter-node-method="filterNode"
          @check="onTreeNodeChecked"
          ref="tree">
          <span slot-scope="{ node, data }" class="tree-node" :title="node.label">
            {{ node.label }}
          </span>
        </el-tree>
      </el-container>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "./CustomDialog.vue";

export default {
  mixins: [Mixins.loadingWrap()],
  name: "TradeProdTreeSelector",
  props: {
    title: {
      type: String,
      default: '行业及产品分类'
    },
    labelKey: {
      type: String,
      default: 'text'
    },
    multiple: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      show: false,
      promise: null,
      treeList: [],
      defaultCheckedKeys: [],
      filterValue: ''
    }
  },
  methods: {
    async open(defaultCheckedKeys) {
      this.defaultCheckedKeys = defaultCheckedKeys || []
      await this.getTradeAndProductType()
      this.show = true
      return new Promise((resolve, reject) => this.promise = {resolve, reject})
    },
    confirm() {
      const result = this.$refs.tree.getCheckedNodes()
      if (!result.length)
        return this.$errorMsg('请至少选择一条数据', '提醒', 'warning')
      this.$emit('result', result)
      this.promise && this.promise.resolve(result)
      this.close(!0)
    },
    close(noReject) {
      this.show = !1
      noReject && this.promise && this.promise.reject()
      this.promise = null
    },
    async getTradeAndProductType() {
      const deep = list => {
        list.forEach(item => {
          item.idx = [item.type, item.id].join('-')
          deep(item.children || [])
        })
      }
      return this.loadingWrap(async () => {
        const {data} = await Axios.post('/tradeClass/getTradeAndProductType', JSON2FormData({orgNum: util.getOrgNum()}))
        if (data.state !== 1) await Promise.reject(data.msg)
        const result = data.data
        deep(result)
        this.treeList = result
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return (data[this.labelKey] || '').indexOf(value) !== -1;
    },
    collapseAll() {
      const deep = node => {
        node.collapse()
        if (node.childNodes.length) {
          for (const _node of node.childNodes) {
            deep(_node)
          }
        }
      }
      deep(this.$refs.tree.root)
    },
    onTreeNodeChecked(node, {checkedKeys}) {
      const isChecked = checkedKeys.includes(node.idx)
      if (!isChecked) return
      if (!this.multiple) {
        checkedKeys = [node.idx]
        if (node.type === 'product_type' && node.parentId > -1) {
          checkedKeys.unshift(['trade', node.parentId].join('-'))
        }
        this.$refs.tree.setCheckedKeys(checkedKeys)
        return
      }
      // 多选情况
      if (node.type === 'product_type' && node.parentId > -1) {
        this.$refs.tree.setChecked(['trade', node.parentId].join('-'), true, false)
      }
    }
  },
  watch: {
    filterValue(val) {
      // 尝试恢复成默认展开状态
      if (!val) {
        this.collapseAll()
      }
      this.$refs.tree.filter(val);
    }
  },
  components: {
    CustomDialog
  }
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.dialog-header .dialog-header-left {
  color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
  padding: 0 30px 0 5px;
  line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 50vh;
}

.tree-node {
  display: inline-block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-body i {
  color: inherit !important;
}
</style>