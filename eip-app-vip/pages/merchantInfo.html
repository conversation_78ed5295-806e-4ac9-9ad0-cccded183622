<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>商家信息</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" href="../css/vip.css" >
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<!-- <script src="../js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<link rel="stylesheet" href="./vue/element-ui/index.css" >
	<script src="./vue/element-ui/index.js"></script>

	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
			var qs = '?token='+ token;
	</script>
</head>
<body class="merchantInfo flex">
<el-container id="app" v-cloak>
	<el-header height="174" class="page_header flex-float">
		<div v-if="isAuthNo" class="flex-center-h" style="padding: 18px 0 0 4px;width: 100%;">
			<img src="../img/newvip/noauth.png" alt="">
			<div style="flex: 1;margin-left: 16px;">
				<h1 style="font-size: 22px;font-weight: bold;">当前未认证商家信息</h1>
				<div style="margin-top: 20px">
					认证后发布商家信息，让更多的人了解你
					<el-link :underline="!1" @click="goAuth" :style="{marginLeft: '16px'}" type="primary">立即认证<i class="el-icon-array-right"></i> </el-link>
				</div>
			</div>
		</div>

		<div v-else-if="isAuthing" class="flex-center-h" style="padding: 24px 0;width: 100%;">
			<img :src="detail.companyPic" alt="" width="200" height="126">
			<div style="flex: 1;margin-left: 23px;">
				<h1 style="font-size: 22px;font-weight: bold;">正在审核商家信息中...</h1>
				<div style="margin-top: 20px">
					审核后即可在展之云行业服务网站上发布您的商家信息
				</div>
			</div>
		</div>

		<div v-else-if="isAuthed" class="flex-center-h" style="padding: 24px 0;width: 100%;">
			<img :src="detail.companyPic" alt="" width="200" height="126">
			<div style="flex: 1;margin-left: 23px;">
				<div class="flex-center-h">
					<h1 style="font-size: 22px;font-weight: bold;">{{ detail.companyName || ''}}</h1>
					<div v-if="isRelease" class="flex-center-h">
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<img src="../img/newvip/eye.png" height="15" alt="">&nbsp;&nbsp;
						<span>{{ detailOther.pageViewNum || 0}}</span>
					</div>&nbsp;&nbsp;&nbsp;&nbsp;
					<el-link v-if="isRelease" :underline="!1" icon="el-icon-edit" @click="goCompanyInfo" type="primary">编辑</el-link>
					<div style="flex: 1"></div>
					<el-link v-if="isRelease" :underline="!1" type="primary" @click="copyTradeSite" type="primary">分享商家</el-link>&nbsp;&nbsp;&nbsp;&nbsp;
					<el-button  v-if="isRelease" @click="goTradeSite" plain size="small">预览商家信息</el-button>
				</div>
				<div class="detail">
					<div>联系人</div>
					<span>{{ detail.contacts|| ''}}</span>
					<div>职务</div>
					<span>{{ detail.position || ''}}</span>
					<div>公司电话</div>
					<span>{{ detail.tel || ''}}</span>
					<div>公司地址</div>
					<span>{{ detail.address || ''}}</span>
					<div>网址</div>
					<span>{{ detail.site || ''}}</span>
				</div>
			</div>
		</div>

	</el-header>
	<el-main class="app_main">
		<div class="main-block">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">商家发布状态</span> &nbsp;&nbsp;&nbsp;&nbsp;
				<div class="flex-center-h" style="flex: 1;">
					<span :class="['state','state'+(+isRelease)]"></span>
					<span style="font-size: 18px;">{{ isRelease ? '已发布' : '未发布' }}</span>
				</div>
				<el-button type="primary" size="small" @click="goRelease">{{ !isRelease ? '发布商家': '修改发布信息'}}</el-button>
			</div>
		</div>
		<div style="display: grid;grid-template-columns: repeat(3,auto);grid-gap: 0 27px;padding-top: 16px;">
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/shop.png" alt="">
				<div>
					<b>被点击量</b>
					<div>{{ detailOther.pageViewNum || 0 }}</div>
				</div>
			</div>
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/cube.png" alt="">
				<div>
					<b>被收藏量</b>
					<div>{{ detailOther.favoriteNum || 0 }}</div>
				</div>
			</div>
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/shoppingcard.png" alt="">
				<div>
					<b>被询盘量</b>
					<div>{{ detailOther.inquiryNum || 0 }}</div>
				</div>
			</div>
		</div>

		<div class="main-block">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">{{ isRelease ? '已' : '待' }}发布商家信息</span>
			</div>
			<center v-if="!detailOther.cataLogueList || !detailOther.cataLogueList.length" class="empty">暂无待发布商家信息</center>
			<div v-else style="margin: 20px 0 10px 0;"  class="flex-center-h">
				<img :src="merchantInfo.pic" alt="" width="200" height="126">
				<div style="flex: 1;margin-left: 32px;">
					<div class="flex-center-h">
						<h1 style="font-size: 22px;font-weight: bold;">{{ merchantInfo.f_company_name || ''}}</h1>
					</div>
					<div class="detail">
						<div>联系人</div>
						<span>{{ merchantInfo.f_contacts_name || ''}}</span>
						<div>职务</div>
						<span>{{ merchantInfo.f_post || ''}}</span>
						<div>公司电话</div>
						<span>{{ merchantInfo.f_tel || ''}}</span>
						<div>公司地址</div>
						<span>{{ merchantInfo.f_company_address || ''}}</span>
						<div>网址</div>
						<span>{{ merchantInfo.f_site || ''}}</span>
					</div>
				</div>
			</div>
		</div>

		<div class="main-block">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">{{ isRelease ? '已' : '待' }}发布产品</span>
				<div style="flex:1"></div>
				<el-link v-if="detailOther.serveProducts && detailOther.serveProducts.length" :underline="!1"  @click="goProductList">更多  <i class="el-icon-arrow-right"></i> </el-link>
			</div>
			<center v-if="!detailOther.serveProducts || !detailOther.serveProducts.length" class="empty">暂无待发布产品信息</center>
			<center v-else  class="data">
				<el-table
					:data="detailOther.serveProducts"
					style="width: 100%"
					>
					<el-table-column
						type="index"
						label="序号"
						width="120">
					</el-table-column>
					<el-table-column
						prop="productName"
						label="产品名称" >
						<template  slot-scope="{row}">
							<el-link :underline="false" type="primary" @click="goGatewayProduct(row.commonId)">
								{{ row.productName  || ''}}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column
						prop="productSpec"
						label="产品规格">
					</el-table-column>
					<el-table-column
						prop="tradeName"
						label="产品类型">
						<template  slot-scope="{row}">
							{{ row.tradeName }} {{ row.productTypeBaseName ? ' - '+row.productTypeBaseName : ''}}
						</template>
					</el-table-column>
					<el-table-column
						prop="pic"
						label="产品图片">
						<template  slot-scope="{row}">
							<img v-if="row.pic" :src="row.pic" alt="" width="56" height="56" />
						</template>
					</el-table-column>
					<el-table-column
						v-if="isRelease"
						label="操作">
						<template  slot-scope="{row}">
							<el-link :underline="!1" type="primary" @click="goGatewayProduct(row.commonId)">查看</el-link>
						</template>
					</el-table-column>
				</el-table>
			</center>
		</div>

		<div class="main-block border">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">公司询盘</span>
				<div style="flex:1"></div>
				<el-link v-if="detailOther.serveInquirys && detailOther.serveInquirys.length" :underline="false" @click="goInquireList">更多  <i class="el-icon-arrow-right"></i> </el-link>
			</div>
			<center v-if="!detailOther.serveInquirys || !detailOther.serveInquirys.length" class="empty">暂无公司询盘信息</center>
			<center v-else  class="data">
				<el-table
					:data="detailOther.serveInquirys"
					style="width: 100%"
					>
					<el-table-column
						prop="companyName"
						label="对方公司">
					</el-table-column>
					<el-table-column
						prop="inquiryPeople"
						label="联系人"
						width="100" >
					</el-table-column>
					<el-table-column
						prop="inquiryPhone"
						label="手机号"
						width="100">
					</el-table-column>
					<el-table-column
						prop="inquiryEmail"
						label="联系邮箱"
						width="100">
					</el-table-column>
					<el-table-column
						prop="productName"
						label="询盘产品">
						<template slot-scope="{ row }">
							<el-link :underline="false" type="primary" @click="goGatewayInquire(row.commonId)">
								{{ row.productName  || ''}}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column
						prop="lastMsgTime"
						label="对方最后回复时间">
					</el-table-column>
					<el-table-column
						prop="msgContent"
						label="回复内容">
						<template slot-scope="{ row }">
							<el-link :underline="false" @click="goGatewayInquire(row)">
								<span v-if="+row.lastMsgState === 1" style="color: #F74444">【未读】</span>
								{{ row.lastMsgContent || '' }}
							</el-link>
						</template>
					</el-table-column>
				</el-table>
			</center>
		</div>

		<div class="main-block border">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">被收藏信息</span>
				<div style="flex:1"></div>
				<!-- <el-link v-if="detailOther.serveProductFavorites && detailOther.serveProductFavorites.length" :underline="!1"  @click="$message.info('TODO')">更多  <i class="el-icon-arrow-right"></i> </el-link> -->
			</div>
			<center v-if="!detailOther.serveProductFavorites || !detailOther.serveProductFavorites.length" class="empty">暂无被收藏信息</center>
			<center v-else  class="data">
				<el-table
					:data="detailOther.serveProductFavorites"
					style="width: 100%"
					>
					<el-table-column
						prop="userName"
						label="用户名">
					</el-table-column>
					<el-table-column
						prop="productName"
						label="关注内容">
						<template slot-scope="{ row }">
							<el-link :underline="false" type="primary" @click="goGatewayProduct(row.serveProductId)">
								{{ row.productName  || ''}}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column
						prop="createTime"
						label="关注时间">
					</el-table-column>
				</el-table>
			</center>
		</div>
	</el-main>
	<div style="flex: 1"></div>
	<!-- <el-footer class="app_foot">

	</el-footer> -->
	<!-- <comp-tip ref="tipEditor" :title="editor.title" @sure="sureEditor" type="form" width="650px">
		<div style="height: 350px;box-sizing: border-box;margin-top: 20px;">
			<div ref="jsEditor" ></div>
		</div>
	</comp-tip> -->
</el-container>

<script src="../js/common/_vipMixin.js?v=231103"></script>
<script>
$(function(){
	window.app = new Vue({
		el: '#app',
		mixins: [_vipMixinCommon],
		data: {
			detail: {
				companyName: '',
				companyAbbr: '',
				companyLogo: '', // 商家logo
				companyPic: '',  // 商家主图
				clientId: '',
				projectId: '',     //
				cataTaskDtlId: '', // 会刊任务id
				exhibitCode: '',   //
			},
			detailOther: {
				pageViewNum: 0,  // 阅读量或点击量
        favoriteNum: 0,   // 收藏量（展品）
        inquiryNum: 0,    // 被询盘量
        collectMerchantNum: null,
        collectProductNum: null,
        collectRequireNum: null,
				cataLogueList: [],// 待/已发布商家信息 - 基本
				companyList: [],  // 待/已发布商家信息 - pic
        serveProducts: [],// 待/已发布的展品
				serveInquirys: [],// 公司询盘
				serveProductFavorites: [],// 被收藏的 展品/..
				catalogues: null,
			},
			auth: -3,
			tabMain: 'tabDetail',
		},
		// async mounted() {
		// },
		computed: {
			isAuthNo() {  // 当前商家未认证
				return this.auth === -3
			},
			isAuthing() { // 正在审核商家信息
				return this.auth === -2
			},
			isAuthed() {  // 已认证
				return this.auth === 1
			},
			isRelease() { // 发布状态
				return !!(this.detailOther && this.detailOther.cataLogueList && this.detailOther.cataLogueList.length && this.detailOther.cataLogueList[0] && this.detailOther.cataLogueList[0].f_confirm == 4)
			},
			merchantLink() { // 行业网站商家链接
				return hostGate + '/MerchantsDetails/'+ this.detail.clientId;//  + '?p='+ this.detail.projectId
			},
			merchantInfo() {
				let tmp = (this.detailOther && this.detailOther.cataLogueList && this.detailOther.cataLogueList.length ) ? this.detailOther.cataLogueList[0] : {}
				if(this.detailOther && this.detailOther.companyList && this.detailOther.companyList.length) {
					tmp.supplyAndDemand = this.detailOther.companyList[0].supplyAndDemand
					tmp.businessLicense = this.detailOther.companyList[0].businessLicense
					tmp.logo = this.detailOther.companyList[0].logo
					tmp.pic = this.detailOther.companyList[0].pic
				}
				return tmp
			}
		},
		methods: {
			async init() {
				this._loading()
				const {state,data} = await this._getMerchantInfo(this.zzyUserId);
				this.auth = state
				if(state === -2 || state === -3) {
				} else if (state === 1) {
					this.detail = data
				}
				// 获取其他信息
				const {data: dataOther} = await _axios.post('admin/merchant/getMerchantInfo', this.obj2FormData({
					zzyUserId: this.zzyUserId,
					orgNum:this.orgnum
				}))
				this.checkReturn(dataOther)
				this.detailOther = dataOther.data
				this.$nextTick(_ => {
					this._loaded()
				})
			},
			goInquireList() {
				window.location.href = './sponsor/my-inquire.html' + qs;
			},
			goProductList() {
				window.location.href = './sponsor/my-product.html' + qs;
			},
			goAuth() { // 认证商家
				window.location.href = window.origin + `/web-reg-server/pc/exhibit-apply.html?EID=${this.detail.exhibitCode || ''}&target=4&orgnum=${this.orgnum}&pid=${this.detail.projectId || ''}&version=1&cid=&ctid=1`
			},
			goCompanyInfo() { // 本公司信息
				window.location.href = './companyInfo/company-info.html' + qs;
			},
			copyTradeSite() { // 复制行业网站 商家链接
				this._copy(this.merchantLink,'商家链接已复制至剪贴板。')
			},
			goGatewayInquire(row) {
				var id = row;
				if(typeof row == 'object') {
					id = row.commonId
					var info = btoa(JSON.stringify({
						identity: 'CLIENT',
						f: 'open',
						iid: row.inquiryMsgId,
					}));
					// if(info.endsWith('=')) info = info.substr(0, info.length-1);
					// if(info.endsWith('=')) info = info.substr(0, info.length-1);
					window.open(hostGate + '/ProductDetails/'+id + qs + '&info=' + info, '_blank')
				} else {
					window.open(hostGate + '/ProductDetails/'+id + qs, '_blank')
				}
			},
			goGatewayProduct(id) {
				window.open(hostGate + '/ProductDetails/'+id + qs, '_blank')
			},
			goTradeSite() { // 前往行业网站 商家链接
				window.open(this.merchantLink, '_blank')
			},
			goRelease() { // 发布商家/修改发布信息
				// window.location.href = `merchantAdd.html?task_dtl_id=1534191&exhibitCode=E0000000294&pid=1837`
				window.location.href = `merchantAdd.html?task_dtl_id=${this.detail.cataTaskDtlId || ''}&exhibitCode=${this.detail.exhibitCode || ''}&pid=${this.detail.projectId || ''}&token=${token}&cid=${this.detail.clientId || ''}`
				// 	this.$confirm('是否确定撤销发布 ?', '提示', {
				// 		confirmButtonText: '仍要撤销',
				// 		cancelButtonText: '不小心点错',
				// 		type: 'warning',
				// 	}).then(() => {
				// 		this.changeProjectState({isRelease: false})
				// 	}).catch( _ => {
				// 	});
			},
		}
	});
	app.init();
});
</script>
</body>
</html>