<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>公司信息表</title>
		<link rel="stylesheet" type="text/css" href="../../css/cropper.min.css" />
		<link rel="stylesheet" type="text/css" href="../../css/ImgCropping.css" />

		<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" />
		<link rel="stylesheet" href="../../css/allcsss.css" />
		<script src="../../js/jquery-1.11.1.min.js"></script>
		<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
		<script src="../../js/jquery.form.js"></script>
		<script src="../../js/common/variableFix.js"></script>

		<script src="../../js/md5/md5.js"></script>
		<script src="../../js/cropper.min.js"></script>

    <link rel="stylesheet" href="../../css/member-theme.css?v=20211103">
    <script src="../../js/member-theme.js?v=230629"></script>
		<script src="../../js/common/fixAjax.js"></script>
		<script src="../../js/common/_vipUtil.js"></script>
		<script>
				var uinfo = util.checkLogin();
				var token = uinfo.token;
		</script>
		<style>
			.layui-container {
        width: 100%;
			}
			#frame{
				height: 100%;
				overflow: auto;
				border: 2px solid red;
			}
			.card-title{
				text-align: center;
				width: 100%;
				margin-left: -20px;
				height: 56px;
				line-height: 56px;
			}

			.xs6-style>.layui-col-xs6{
				width: 46%;
				margin: 0 2%;
				margin-top: 14px;
			}
			.layui-form-pane .layui-form-label{
				padding: 8px 15px;
			}
			.menber-botton-fiexd {
					position: fixed;
					bottom: 0;
					left: 0;
					width: 100%;
					background-color: #f5f5f5;
			}
			.ComitBtnStyle{
				float: right;
				margin: 12px 62px 6px 0;
			}
			.mt0{
				margin-top: 0;
			}
			.tab-title-li{
				text-align: center;
				background-color: #f5f5f5;
			}
			.tab-title-li>li{
				font-size: 16px;
				font-weight: bold;
				color: #666666;
			}
			.layui-form-item{
				width: 49%;
			}
			.layui-form-label{width: 150px !important;}
			.upImgDivs {
					width: 200px !important;
					height: 142px !important;
					border: solid 1px #e5e5e5;
					background: url(../../img/invitation/logo.png) no-repeat center 26px;
					background-size: 42px;
					position: relative;
					float: left;
			}
			.upImgDivs>p {
					font-size: 20px;
					color: #b3b3b3;
					position: absolute;
					top: 70px;
					left: 42px;
			}
			#delCharterPic {
					width: 30px;
					height: 30px;
					background-color: rgba(0, 0, 0, .5);
					display: block;
					position: absolute;
					top: 0;
					right: 0;
					text-align: center;
					line-height: 30px;
			}
			label i {
				color: red;
				display: none;
				/* 暂时隐藏 */
			}
			.upImgs>img{
				width: 142px;
			}
			.upImgs.w200>img{
				width: 200px;
			}
			.upImgs.w200 {
				width: 200px;
			}
			.upImgs {
				width: 142px;
				height: 142px;
				border: solid 1px #e5e5e5;
				background: url(../../img/invitation/logo.png) no-repeat center 26px;
				background-size: 42px;
				position: relative;
				float: left;
				margin-top: 20px;
				margin-right: 20px;
			}
			.upImgs>p {
				font-size: 15px;
				width: 100%;
				color: #b3b3b3;
				position: absolute;
				top: 80px;
				text-align: center;
			}
			.noImgs{
				cursor: pointer;
			}
			.delPicBtn{
				cursor: pointer;
				width: 30px;
				height: 30px;
				background-color: rgba(0, 0, 0, .5);
				display: block;
				position: absolute;
				top: 0;
				right: 0;
				text-align: center;
				line-height: 30px;
			}
			.upload-tip{
				font-size: 14px;
				font-family: PingFangSC-Regular;
				color: #a6a6a6;
			}
		</style>
	</head>
	<body>
		<div class="layui-container">
			<div class="layui-tab mt0" lay-filter="demo_tab">
				<div class="layui-tab-content" style="padding:0">
					<form class="layui-form layui-form-pane" action="" onsubmit="return true;" id="CompanyInfoForm" lay-filter="CompanyInfoForm">
						<div class="fly-panel fly-panel-user" style="padding: 0;margin-bottom: 50px;">
							<div class="layui-fluid " style="width: 100%;padding: 0;">
								<div class="layui-row card-title">
									<h1 style="font-size: 18px;font-weight: bold;color: #333;margin-left:24px">公司信息</h1>
								</div>
								<div class="layui-tab layui-tab-brief mt0">
									<ul class="layui-tab-title tab-title-li">
										<li class="layui-this">公司中文信息</li>
										<li>公司英文信息</li>
									</ul>
									<div class="layui-tab-content" style="padding: 0 30px;margin-top: 20px;">
										<div style="margin-bottom: 25px;">公司信息</div>
										<div class="layui-tab-item layui-show">
											<input name="f_client_id" class="f_client_id" style="display: none;" />
											<input name="f_user_com_id" class="f_user_com_id" style="display: none ;" />
											<input name="f_zzyuser_id" id="f_zzyuser_id" type="hidden" />
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司名称</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_company_name" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司简称</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_company_abbr" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>联系电话</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_tel" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>移动电话</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_mobile" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>传真</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_fax" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>E-mail</label>
												</div>
												<div class="layui-col-lg9">
													<!-- lay-verify="required|email" -->
													<input type="text" name="f_email" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<!-- <div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>行业分类</label>
												</div>
												<div class="layui-col-lg9">
													<select name="tradeId" id="tradeId" lay-verify="required"></select>
													<!-- <input type="text" lay-verify="required" autocomplete="off" id="tradeId" name="tradeId"
													 class="layui-input required" style="width: 80%;display: inline">
													<input type="button" value="选择" class="layui-input theme-color" onclick="getProType(); " style="width: 20%;display: inline;padding-left:0px;margin: 1px 0 0 -5px;"> -- >
												</div>
											</div> -->
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>展品范围</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_type_product" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司网址</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_site" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司地址</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="f_address" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline ">
												<div class="">
													<label class="layui-form-label" style="width: 100%;">统一社会信用代码</label>
												</div>
												<div class="layui-col-lg9">
													<input type="text" name="socialReditCode" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row">
												<div class="" style="display: none;">
													<input type="text" name="businessLicense" id="businessLicense" lay-verify="" placeholder="" autocomplete="off"
													 class="layui-input">
													<input type="text" name="companyLogo" id="companyLogo" lay-verify="" placeholder="" autocomplete="off"
													 class="layui-input">
													<input type="text" name="companyPic" id="companyPic" lay-verify="" placeholder="" autocomplete="off"
													 class="layui-input">
												</div>
												<div class="layui-col-lg6" style="display: flex;flex-flow:  row nowrap;align-items: center;">
													<div class="upImgs noImgs" onclick="certUpImgDiv(1)" style="display: block;">
														<p>上传公司图片</p>
													</div>
													<div class="upImgs haveImgs" style="display: none;">
														<img src="" alt="" id="compPic" style="height: 142px;">
														<span class="delPicBtn" onclick="delphotopic(1)"><img src="../../img/del2.png"></span>
													</div>
													<div class="upload-tip">推荐上传400*300像素图片<br />支持.JPG .JPEG .PNG 格式，大小不超过2M</div>
												</div>

												<div class="layui-col-lg6" style="display: flex;flex-flow:  row nowrap;align-items: center;">
													<div class="upImgs noImgs" onclick="certUpImgDiv(2)" style="display: block;">
														<p>上传LOGO</p>
													</div>
													<div class="upImgs haveImgs" style="display: none;">
														<input style="display:none" type="file" id="companyLogoUploader" accept="image/*">
														<img src="" alt="" id="logoPic" style="height: 142px;">
														<span class="delPicBtn" onclick="delphotopic(2)"><img src="../../img/del2.png"></span>
													</div>
													<div class="upload-tip">推荐上传400*400像素图片<br />支持.JPG .JPEG .PNG 格式，大小不超过2M</div>
												</div>

												<div class="layui-col-lg6" style="display: grid;grid-template-columns: 200px 1fr;">
													<div class="upImgs noImgs w200" onclick="certUpImgDiv(3)" style="display: block;">
														<p>上传营业执照</p>
													</div>
													<div class="upImgs haveImgs w200" style="display: none;">
														<img src="" alt="" id="charterPic" style="height: 142px;">
														<span class="delPicBtn" onclick="delphotopic(3)"><img src="../../img/del2.png"></span>
													</div>
													<div class="upload-tip" style="padding: 50px 0 0 20px;">支持.JPG .JPEG .PNG 格式，大小不超过2M</div>
												</div>

											</div>
											<div class="layui-col-xs12" style="margin-top: 14px;">
												<label class="layui-form-label" style="width: 100% !important;"><i>*</i>公司简介</label>
												<div class="layui-input-block" style="margin: 0;">
													<textarea name="f_com_introduce" placeholder="" class="layui-textarea"></textarea>
												</div>
											</div>
											<div style="padding-bottom: 40px;">
												<div style="margin-bottom: 10px;line-height: 45px;">联系人信息</div>
												<div class="layui-form-item layui-row layui-inline ">
													<div class="">
														<label class="layui-form-label" style="width: 100%;"><i>*</i>联系人姓名</label>
													</div>
													<div class="layui-col-lg9">
														<input type="text" name="f_contacts" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
													</div>
												</div>
												<div class="layui-form-item layui-row layui-inline ">
													<div class="">
														<label class="layui-form-label" style="width: 100%;"><i>*</i>职务</label>
													</div>
													<div class="layui-col-lg9">
														<input type="text" name="f_position" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
													</div>
												</div>
											</div>
										</div>
										<div class="layui-tab-item xs6-style">
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司名称(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_company_name_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司简称(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_company_abbr_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>展品范围(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_type_product_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>公司地址(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_address_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<!-- <div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>行业分类(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<select name="tradeIdEn" id="tradeIdEn" lay-verify="required"></select>
													<!-- <input type="text" lay-verify="required" autocomplete="off" id="tradeIdEn" name="tradeIdEn"
													 class="layui-input required" style="width: 80%;display: inline">
													<input type="button" value="选择" class="layui-input theme-color" onclick="getProType('EN');" style="width: 20%;display: inline;padding-left:0px;margin: 1px 0 0 -5px;"> -- >
												</div>
											</div> -->
											<div class="layui-col-xs12" style="margin-top: 14px;">
												<label class="layui-form-label" style="width: 100% !important;"><i>*</i>公司简介(英文)</label>
												<div class="layui-input-block" style="margin: 0;">
													<textarea name="f_com_introduce_en" placeholder="" class="layui-textarea"></textarea>
												</div>
											</div>
											<div style="margin-bottom: 10px;line-height: 45px;">联系人信息</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>联系人(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_contacts_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline">
												<div class="">
													<label class="layui-form-label" style="width: 100%;"><i>*</i>职务(英文)</label>
												</div>
												<div class="layui-col-lg9 ">
													<input type="text" name="f_position_en" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="menber-botton-fiexd">
							<div class="layui-btn-group ComitBtnStyle">
								<button class="layui-btn" lay-submit lay-filter="CompanyCommit" id="ComitBtn" style="width: 92px;">保存</button>
							</div>
						</div>
					</form>
					<div class="layui-fluid" id="getProType" hidden="hidden">
						<div class="layui-fluid" class="" style="margin-top: 20px;">
							<table class="layui-table" id="proType" layer-filter="proType"></table>
						</div>
					</div>
					<div class="tailoring-container cardImgShow" style="display: none;">
						<div class="black-cloth" onclick="closeTailor(this)"></div>
						<div class="tailoring-content">
							<div class="tailoring-content-one">
								<label title="上传图片" for="cardChooseImg" class="l-btn choose-btn">
									<input type="file" name="file" id="cardChooseImg" onchange="selectImg(this)" class="hidden">
									选择图片
								</label>
								<div class="close-tailoring" onclick="closeTailor(this)">×</div>
							</div>
							<div class="tailoring-content-two">
								<div class="tailoring-box-parcel">
									<img id="cardTailoringImg">
								</div>
								<div class="preview-box-parcel">
									<p>图片预览：</p>
									<div class="square previewImg"></div>
									<div class="circular previewImg"></div>
								</div>
							</div>
							<div class="tailoring-content-three">
								<button class="l-btn cropper-reset-btn">复位</button>
								<button class="l-btn cropper-rotate-btn">旋转</button>
								<button class="l-btn cropper-scaleX-btn">换向</button>
								<button class="l-btn sureCut" id="sureCut">确定</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script type="text/javascript">
			//上传本人照片 : 1:公司图片,2:logo,3:营业执照
			function certUpImgDiv(type) {
				companyInfo.picType = type
				if (+type === 2) return $('#companyLogoUploader').click()
				$(".cardImgShow").show();
				$("#cardChooseImg").click();
			}
			var gfile;
			//弹出框水平垂直居中
			(window.onresize = function() {
				var win_height = $(window).height();
				var win_width = $(window).width();
				if (win_width <= 768) {
					$(".tailoring-content").css({
						"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
						"left": 0
					});
				} else {
					$(".tailoring-content").css({
						"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
						"left": (win_width - $(".tailoring-content").outerWidth()) / 2
					});
				}
			})();
			function layerMsg(msg,icon = 2,throws = true) {
				layer.msg(msg, {icon, zIndex: 20220712,anim: 6});
				if(throws) throw new Error(msg)
			}
			function checkFile(file) {
				if (!file) throw new Error('取消上传'); // layerMsg('请选择图片！');
				if(file.type.startsWith('image/')){
					if(2048*1024 < file.size) {
						layerMsg('图片过大, 上传失败 。');
					}
					if(!["image/png","image/jpeg","image/jpg"].includes(file.type)) {
						layerMsg('很抱歉，只能上传 jpg/jpeg/png格式图片!');
					};
				} else {
					layerMsg('很抱歉，只允许上传图片');
				}
			}
			//图像上传
			function selectImg(file) {
				var max_size = 10240;
				var img_size = 1024;
				var fileData = file.files[0];
				try{
					file.value = '';
					checkFile(fileData)
				}catch(e){
					$(file).val('');
					throw e
				}
				var size = fileData.size;
				$(".tailoring-box-parcel").show();
				$(".preview-box-parcel").show();
				if (size > img_size * 1024) { // >1M 压缩
					let fileObj = fileData;// document.getElementById('cardChooseImg').files[0];
					//alert(fileObj)
					var reader = new FileReader();
					reader.readAsDataURL(fileObj)
					reader.onload = function(e) {
						let image = new Image() //新建一个img标签（还没嵌入DOM节点)
						image.src = e.target.result
						image.onload = function() {
							let canvas = document.createElement('canvas'),
								context = canvas.getContext('2d'),
								imageWidth = image.width / 5, //压缩后图片的大小
								imageHeight = image.height / 5,
								data = ''
							canvas.width = imageWidth
							canvas.height = imageHeight
							context.drawImage(image, 0, 0, imageWidth, imageHeight)
							data = canvas.toDataURL('image/jpeg')
							// console.log(data)
							//压缩完成
							// document.getElementById('img').src = data
							$('#cardTailoringImg').cropper('replace', data, false)
						}
					}
				} else {
					var reader = new FileReader();
					let fileObj = fileData;// document.getElementById('cardChooseImg').files[0];
					reader.readAsDataURL(fileObj)
					reader.onload = function(evt) {
						var replaceSrc = evt.target.result;
						//更换cropper的图片
						$('#cardTailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
					}
				}
			}
			var companysizes = 4 / 3;
			//var logosize = 25 /25;
			//cropper图片裁剪
			$('#cardTailoringImg').cropper({
				aspectRatio: companysizes, //默认比例
				preview: '.previewImg', //预览视图
				guides: false, //裁剪框的虚线(九宫格)
				autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
				dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
				movable: true, //是否允许移动剪裁框
				resizable: true, //是否允许改变裁剪框的大小
				zoomable: true, //是否允许缩放图片大小
				mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
				touchDragZoom: true, //是否允许通过触摸移动来缩放图片
				rotatable: true, //是否允许旋转图片
				crop: function(e) {
					// 输出结果数据裁剪图像。
				}
			});
			//旋转
			$(".cropper-rotate-btn").on("click", function() {
				$('#cardTailoringImg').cropper("rotate", 45);
			});
			//复位
			$(".cropper-reset-btn").on("click", function() {
				$('#cardTailoringImg').cropper("reset");
			});
			//换向
			var flagX = true;
			$(".cropper-scaleX-btn").on("click", function() {
				if (flagX) {
					$('#cardTailoringImg').cropper("scaleX", -1);
					flagX = false;
				} else {
					$('#cardTailoringImg').cropper("scaleX", 1);
					flagX = true;
				}
				flagX != flagX;
			});

			function dataURLtoFile(dataurl, filename) { //将base64转换为文件
				var arr = dataurl.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new File([u8arr], filename, {
					type: mime
				});
			}

			// function selecttype(e) {
			// 	let file = e.files[0];
			// 	var file_id = e.value;
			// 	let fileObj = document.getElementById('addPic').files[0];
			// 	file_id = file_id.substring(file_id.indexOf("."));
			// 	if (file_id == ".bmp" || file_id == ".png" || file_id == ".gif" || file_id == ".jpg" || file_id == ".jpeg") {
			// 		var max_size = 10240;
			// 		var img_size = 1024;
			// 		if (!file) return;
			// 		if (!['.png','.jpg','.jpeg'].includes(file_id)) {
			// 			alert("仅支持 png、jpeg、jpg格式图片");return;
			// 		}
			// 		var size = file.size;
			// 		if (size > 2048 * 1024) {
			// 			alert("原始图片大小不能超过2M");return;
			// 		}
			// 		$(".cardImgShow").toggle();
			// 		if (size > img_size * 1024) {
			// 			var reader = new FileReader();
			// 			reader.readAsDataURL(fileObj)
			// 			reader.onload = function(e) {
			// 				let image = new Image() //新建一个img标签（还没嵌入DOM节点)
			// 				image.src = e.target.result
			// 				image.onload = function() {
			// 					let canvas = document.createElement('canvas'),
			// 						context = canvas.getContext('2d'),
			// 						imageWidth = image.width / 5, //压缩后图片的大小
			// 						imageHeight = image.height / 5,
			// 						data = ''
			// 					canvas.width = imageWidth
			// 					canvas.height = imageHeight
			// 					context.drawImage(image, 0, 0, imageWidth, imageHeight)
			// 					data = canvas.toDataURL('image/jpeg')
			// 					//压缩完成
			// 					// document.getElementById('img').src = data
			// 					$('#cardTailoringImg').cropper('replace', data, false)
			// 				}
			// 			}
			// 		} else {
			// 			var reader = new FileReader();
			// 			reader.onload = function(evt) {
			// 				var replaceSrc = evt.target.result;
			// 				//更换cropper的图片
			// 				$('#cardTailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
			// 				//图像上传
			// 			}
			// 			reader.readAsDataURL(fileObj)
			// 		}
			// 		$(".tailoring-box-parcel").show();
			// 	} else {
			// 		var max_vidoesize = 10240;
			// 		var img_vidoesize = 1024;
			// 		var vidoesize = file.size;
			// 		if (vidoesize > max_vidoesize * 1024) {
			// 			alert("视频大小不能超过10M");
			// 			return;
			// 		}
			// 		uploadImg(file)
			// 	}
			// }
			$("#sureCut").on("click", function() {
				if ($("#cardTailoringImg").attr("src") == null) return false;
				var cas = $('#cardTailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
				var base64url = cas.toDataURL()
				var timestamp = new Date().getTime()
				var file = dataURLtoFile(base64url, timestamp + ".jpg");
				uploadImg(file, true);
				$('#cardChooseImg').val('');
				//关闭裁剪框
				$(".cardImgShow").hide();
				$(".tailoring-box-parcel").hide();
				$(".preview-box-parcel").hide();
			});

			function uploadImg(file,isAfterCropper = false) {
				if(!isAfterCropper) checkFile(file);
				var formData = new FormData();
				formData.append("file", file); //data
				$.ajax({
					url: variableSponsor + "/upload/uploadImg",
					dataType: "json",
					type: "post",
					data: formData,
					processData: false,
					contentType: false,
					async: true,
					success: function(res) {
						var attachUrl = window.origin + "/image/";
						var $pic,$picVal;
						if(companyInfo.picType === 3){
							$pic = $('#charterPic');
							$picVal = $("#businessLicense");
						}else if(companyInfo.picType === 2){
							$pic = $('#logoPic');
							$picVal = $("#companyLogo");
							$('#companyLogoUploader').val('')
						}else{
							$pic = $('#compPic');
							$picVal = $("#companyPic");
						}
						$pic.attr('src', attachUrl + res.result.path).parent().show().prev().hide();
						$pic.css({backgroundColor: '#E4E4E4'})
						$picVal.val('/image/' + res.result.path);
					},
					error: function() {}
				});
			}
			//关闭裁剪框
			function closeTailor() {
				$(".tailoring-container").toggle();
			}
		</script>
		<script>
			var table,layer;
			let url = (new URL(window.location.href)).searchParams
			var companyInfo = {
				tradeList: [],
				orgnum: uinfo.orgnum,
				allowModCompanyName: true,
				tradeListEn: [],
				getTradeIds: function(lang=''){
					const that = this;
					that.post(variableSponsor + '/tradeClass/selectTradeTree',{
						projectId: '',
						lang
					},{	async: false },function(res){
						console.log('res', res);
						that.checkReturn(res);
						if(lang){
							that.tradeListEn = res.data;
						}else{
							that.tradeList = res.data;
						}
					},function(err) {
						that.alert('获取行业分类数据失败!');
					});
				},
				init: function(){
					this.getVipSetting()
					// this.getTradeIds();
					// this.getTradeIds('En');
					// this.renderTradeList();
				},
				ifDisableCompanyName(data){
					if(!this.allowModCompanyName){
						if(data.f_company_name){
							$('input[name=f_company_name]').attr('disabled', true)
						}
						if(data.f_company_name_en){
							$('input[name=f_company_name_en]').attr('disabled', true)
						}
					}
				},
				getVipSetting(){ // 会员中心设置
					const that = this
					that.post(API + '/api/sysSettingOrgNum/getValue',{
						settingCode: 'allowModCompanyName',
						orgNum: that.orgnum,
					}, { async: false },function(data){
						// const {state,data,msg} = rsp
						if(+data.state !== 1) {
							console.error(data)
							return
						}
						if(data.data && data.data.settingCode === 'allowModCompanyName'){
							that.allowModCompanyName = data.data.settingValue === 'true'
						}
					})
				},
				renderTradeList(){
					const that = this
					let list = that.tradeList;
					let tpl  = '<option value=""></option>';
					if(list && list.length){
						list.forEach(i => {
							tpl += `<option value="${i.tradeId}">${i.title}</option>`;
						})
						$('#tradeId').html(tpl);
					}
					list = that.tradeListEn;
					tpl = '<option value=""></option>';
					if(list && list.length){
						list.forEach(i => {
							tpl += `<option value="${i.tradeId}">${i.title}</option>`;
						})
						$('#tradeIdEn').html(tpl);
					}
					// table.render({
					// 	elem: '#proType'
					// 	,height: 332
					// 	// ,width: 320
					// 	,page: false
					// 	,cols: [[
					// 		{field:'0', width:60,align:'center',type: 'checkbox' },
					// 		// {width:150,align:'center',type:'numbers',title: '序号'},
					// 		{field:'tradeId',width:60,align:'center',title: 'ID',sort: true},
					// 		{field:'title',align:'center',title: '类型名称',sort: true}
					// 	]]
					// 	,data: that.tradeList
					// });
				},
				postJson: function (url, data, options, cb, cbErr) {
					const df = {
						// async:true,	// 默认,异步
						type: "post",
						url,
						data: JSON.stringify(data),
						contentType: "application/json",
						dataType: "json",
						success: cb || function (rsp) {
							console.log("rsp", rsp);
						},
						error: cbErr || function (data,type, err) {
							console.log("error", data , type , err);
						},
					};
					$.ajax(options ? Object.assign(df, options) : df);
				},
				post: function (url, data, options, cb, cbErr) {
					const df = {
						// async:true,	// 默认,异步
						type: "post",
						url,
						data,
						dataType: "json",
						success: cb || function (rsp) {
							console.log("rsp", rsp);
						},
						error: cbErr || function (data,type, err) {
							console.log("error", data , type , err);
						},
					};
					$.ajax(options ? Object.assign(df, options) : df);
				},
				alert: function (msg = '', icon = 'err', throws = false) { // 1 suc/2 err
					icon = icon === 'err' ? 2 : 1;
					if (msg) {
						return layer.msg(msg, {icon: Number(icon),success: function(layero, index) {
							layero.click(function(e) {
								layer.close(index);
							});
						}});
					}
					// $.messager.alert('提示', msg, icon ? icon : 'warning');
					if (throws) throw new Error('error: ' + msg);
				},
				checkReturn: function (obj, msg = '', icon = '', throws = false) {
					icon = icon || 'warning';
					if (obj && obj.state === 1) {
						return true;
					} else {
						this.alert(msg || obj.msg, 'err', throws);
						return false;
					}
				}
			}
			//注意导航 依赖 element 模块，否则无法进行功能性操作
			layui.use(['element', 'layer', 'form', 'table'], function() {
				/*参数*/
				/* js*/
				var layertable; //添加弹出框
				var element = layui.element;
				var form = layui.form;
				layer = layui.layer;
				table = layui.table;
				//删除图片
				window.delphotopic = function (type) {
					type = Number(type)
					var typeTip = type === 3 ? '营业执照' : type===2 ? 'LOGO' : '公司图片';
					layer.msg(`是否删除${typeTip}?此操作不可恢复!`,
						{
							btn: ['删除', '取消'],
							yes: function(){
								if(type===3){
									$("#charterPic").attr("src", "").parent().hide().prev().show();
									$("#businessLicense").val('');
								}else if(type ===2){
									$("#logoPic").attr("src", "").parent().hide().prev().show();
									$("#companyLogo").val('');
								}else{
									$("#compPic").attr("src", "").parent().hide().prev().show();
									$("#companyPic").val('');
								}
								layer.closeAll();
							}
							,btn2: function(){
								layer.closeAll();
							}
						},
					);
				}
				/* 表单验证*/
				/*所有的表单验证规则*/
				form.verify({
					userpassword: function(value, item) { //value：表单的值、item：表单的DOM对象1
						console.log($("#pass1").val());
						if (!new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$").test(value)) {
							return '密码不能有特殊字符';
						}
						if (/(^\_)|(\__)|(\_+$)/.test(value)) {
							return '密码首尾不能出现下划线\'_\'';
						}
					},
					usercode: function(value, item) { //value：表单的值、item：表单的DOM对象
							console.log($("#pass1").val());
							if (!new RegExp("^[a-zA-Z0-9_\u4e00-\u9fa5\\s·]+$").test(value)) {
								return '用户名不能有特殊字符';
							}
							if (/(^\_)|(\__)|(\_+$)/.test(value)) {
								return '用户名首尾不能出现下划线\'_\'';
							}
						}
						,
					pass: [
						/^[\S]{6,12}$/, '密码必须6到12位，且不能出现空格'
					]
				});

				form.on('submit(CompanyCommit)', function(data) { //表单提交 验证表单
					/*执行提交表单操作*/
					commitUserCompany();
					return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
				});
				/* js方法正式开始*/

				/*获取参数*/
				function GetQueryString(name) {
					var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
					var r = window.location.search.substr(1).match(reg);
					if (r != null) return unescape(r[2]);
					return null;
				}
				var zzyUserId = uinfo.zzyUserId;// GetQueryString('zzyUserId');

				function commitUserCompany() {
					$("#f_zzyuser_id").val(zzyUserId);
					if (zzyUserId == null || zzyUserId == undefined || zzyUserId == '') {
						layer.msg('请求失败，刷新重试', {
							icon: 2
						})
						return;
					}
					console.log($("#CompanyInfoForm").serialize());
					// return
					$.ajax({
						url: 'http://' + variable + '/userCompany/insert', //请求服务器url地址.   +cid+"&pid="+pid
						type: "post",
						data: $("#CompanyInfoForm").serialize(),
						datatype: 'json',
						async: true,
						xhrFields: {
							withCredentials: true
						},
						beforeSend: function() {},
						success: function(data) {
							var res = JSON.parse(data)
							var state = res.result;
							if (state == 1) {
								layer.msg('保存成功', {
									icon: 1
								});
								LoadUserCompany();
							}
						},
						complete: function() {
						},
						error: function(data) {
							return false;
						}
					});
				}

				$(function() {
					companyInfo.init();
					LoadUserCompany();
					$('#companyLogoUploader').change(function(){
						uploadImg(this.files[0])
					})
				});
				function throws(msg){
					layer.msg(msg, { icon: 2})
					throw new Error(msg);
				}
				function LoadUserCompany() {
					if (zzyUserId == null || zzyUserId == undefined || zzyUserId == '') {
						layer.msg('请求失败，请重新登录', {
							icon: 2
						})
						return;
					}
					$.ajax({
						url: 'http://' + variable + '/userCompany/getByUserId', //请求服务器url地址.   +cid+"&pid="+pid
						type: "post",
						data: {
							f_zzyuser_id: zzyUserId
						},
						datatype: 'json',
						async: true,
						xhrFields: {
							withCredentials: true
						},
						beforeSend: function() {},
						success: function(data) {
							if (data.state > 0 && data.data != null) {
								loadform(data.data);
							}
						},
						complete: function() {},
						error: function(data) {
							return false;
						}
					});
				}

				function loadform(data) {
					document.getElementById("CompanyInfoForm").reset();
					form.val("CompanyInfoForm", {
						"f_client_id": data.f_client_id,
						"f_com_introduce": data.f_com_introduce,
						"f_company_name": data.f_company_name,
						"f_company_name_en": data.f_company_name_en,
						"f_contacts": data.f_contacts,
						"check[write]": true,
						"f_email": data.f_email,
						"f_mobile": data.f_mobile,
						"f_product_introduce": data.f_product_introduce,
						"f_site": data.f_site,
						"f_user_com_id": data.f_user_com_id,
						"f_client_id": data.f_client_id,
						"f_company_abbr": data.f_company_abbr,
						"f_company_abbr_en": data.f_company_abbr_en,
						"f_position": data.f_position,
						"f_position_en": data.f_position_en,
						"f_contacts_en": data.f_contacts_en,
						"f_com_introduce_en": data.f_com_introduce_en,
						"f_address": data.f_address,
						"f_address_en": data.f_address_en,
						"f_type_product": data.f_type_product,
						"f_type_product_en": data.f_type_product_en,
						"f_fax": data.f_fax,
						"f_tel": data.f_tel,
						"f_zzyuser_id": data.f_zzyuser_id,
						"socialReditCode": data.socialReditCode,
						"businessLicense": data.businessLicense,
						"tradeId": data.tradeId,
						"tradeIdEn": data.tradeIdEn,
						"companyLogo": data.companyLogo,
						"companyPic": data.companyPic,
					})
					companyInfo.ifDisableCompanyName(data)
					// var attachUrl = "http://" + window.location.hostname + ":" + window.location.port + "/image/";
					if(data.businessLicense!=null && data.businessLicense!=""){
						$('#charterPic').attr('src', getImgUrl(data.businessLicense)).parent().show().prev().hide();
					}else{
						$('#charterPic').attr('src', '').parent().hide().prev().show();
					}
					if(data.companyLogo!=null && data.companyLogo!=""){
						$('#logoPic').attr('src', getImgUrl(data.companyLogo)).parent().show().prev().hide();
					}else{
						$('#logoPic').attr('src', '').parent().hide().prev().show();
					}
					if(data.companyPic!=null && data.companyPic!=""){
						$('#compPic').attr('src', getImgUrl(data.companyPic)).parent().show().prev().hide();
					}else{
						$('#compPic').attr('src', '').parent().hide().prev().show();
					}
				}
			});

			function getImgUrl(url) {
				return url
				// url = String(url || '')
				// if(url.startsWith('http') || url.startsWith('//')){
				// 	return url
				// }else if(url.startsWith('/image/')){
				// 	return url
				// }else{
				// 	return '/image/' + url;
				// }
			}
			// function getProType(lang='') {
			// 	companyInfo.getTradeIds(lang);
			// 	//页面层
			// 	layertable = layer.open({
			// 		type: 1,
			// 		title: '行业分类',
			// 		skin: 'layui-layer-demo', //加上边框
			// 		area: ['420px','500px'], //宽高
			// 		content: $('#getProType'),
			// 		btn: ['确定', '关闭'],
			// 		success: function(layero, index){
			// 			companyInfo.renderTradeList(lang);
			// 		},
			// 		yes: function(index, layero) {
			// 			var type = eval(layui.table.checkStatus('proType').data);
			// 			if(type && type.length === 1){
			// 			}else{
			// 				companyInfo.alert('请选择一项 !','err', true);
			// 			}
			// 			var f_type_product = $('#f_type_product').val();
			// 			var f_type_product_en = $('#f_type_product_en').val();
			// 			for (var i = 0; i < type.length; i++) {
			// 				if (f_type_product !== undefined) {
			// 					if (f_type_product === "") {
			// 						f_type_product = type[i].typeName;
			// 					} else {
			// 						if (!f_type_product.match(type[i].typeName)) {
			// 							f_type_product = f_type_product + "," + type[i].typeName;
			// 						}
			// 					}
			// 				}
			// 				if (f_type_product_en !== undefined) {
			// 					if (f_type_product_en === "") {
			// 						f_type_product_en = type[i].typeCode;
			// 					} else {
			// 						if (!f_type_product_en.match(type[i].typeCode)) {
			// 							f_type_product_en = f_type_product_en + "," + type[i].typeCode;
			// 						}
			// 					}
			// 				}
			// 			}
			// 			if (f_type_product !== undefined) {
			// 				$('#f_type_product').val(f_type_product);
			// 			}
			// 			if (f_type_product_en !== undefined) {
			// 				$('#f_type_product_en').val(f_type_product_en);
			// 			}

			// 			layer.close(index);
			// 		},
			// 		btn2: function(index, layero) {},
			// 		cancel: function() {
			// 			//询问框
			// 			var confim = layer.confirm('确认退出编辑？', {
			// 				btn: ['确认', '取消'] //按钮
			// 			}, function() {
			// 				layer.close(layertable);
			// 				layer.close(confim);
			// 			}, function() {
			// 				layer.close(confim);
			// 			});
			// 			return false
			// 		},
			// 		end: function() {
			// 			console.log("colse")
			// 		}
			// 	});
			// }
		</script>
	</body>
</html>
