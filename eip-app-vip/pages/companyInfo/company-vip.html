<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>会员信息</title>
	</head>
	<!-- <link rel="stylesheet" href="/eip-web-vip/lib/layui/css/layui.css" /> -->
  <link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" />
  <link rel="stylesheet" href="../../css/allcsss.css" />
  <script src="../../js/jquery-1.11.1.min.js"></script>
  <script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
  <script src="../../js/jquery.form.js"></script>
  <script src="../../js/common/variableFix.js"></script>

	<!--<script src="../vue/vue.js"></script>-->
	<script src="../vue/vue2.dev.js"></script>
  <script src="../vue/axions.js"></script>
	<link rel="stylesheet" href="../vue/element-ui/index.css" >
	<script src="../vue/element-ui/index.js"></script>
  <script src="../vue/dialogDrag.js"></script>
	<script src="../vue/httpVueLoaderEx.js"></script>

	<script src="../../js/common/fixAjax.js"></script>
	<script src="../../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;

			var pwdLevel = +uinfo.pwdGrade || 0;
			function getPwdLevelDesc(pwdLevel) {
				return ['', '低', '中', '中' ,'高'][pwdLevel] || '未知';
			}
	</script>
	<style>
		.window-password .layui-layer-content{
			overflow: unset !important;
		}
		.window-password .tip-state2 {
			color: #f2bd00;
		}
		.window-password .tip-state3 {
			color: green;
		}
		.window-password .tip-state3 .tip-desc2 {
			width: 100%;
			background: green;
		}
		.window-password .tip-state2 .tip-desc2 {
			width: 67%;
			background: #f2bd00;
		}
		.window-password .layui-slider {

			border-radius: 5px;
			height: 6px;
		}
		.window-password .tip-desc2 {
			height: 100%;
			background: transparent;
			width: 0;
			left:0;
			border-radius: 5px;
		}
		.window-password .tip-state1 .tip-desc2 {
			width: 34%;
			background: red;
		}
		.window-password .tip-state1 {
			color: red;
		}
		.window-password .tip .tip-state {
			background: white;border-radius: 5px;padding: 20px;border: 1px solid #eee;box-shadow: 1px 1px 50px rgba(0,0,0,.3);
		}
		.window-password .layui-layer-content {
			height: 186px !important;
		}
		.window-password .tip {
			top: 31px;
			left: 390px;
			display: none;
			position: absolute;padding: 10px 20px;
			background: transparent;width: 220px;
		}
	</style>
	<link rel="stylesheet" href="../../css/member-theme.css?v=20211103">
	<script src="../../js/member-theme.js?v=230629"></script>
	<script src="../../js/md5/md5.js"></script>

	<link rel="stylesheet" href="../../css/cropper.min.css" />
	<link rel="stylesheet" href="../../css/ImgCropping.css" />
	<link rel="stylesheet" href="css/vip.css" />
	<script src="../../js/cropper.min.js"></script>
	<script src="../../js/jquery.qrcode.min.js"></script>
	<style>
		.el-image-viewer__btn i {color: white !important;}
		body .main-ul li {
			padding-right: 0;
		}
		body .formSelf .el-form-item__error {
			padding-top: 0;
		}
		body .formCompanyEdit .el-form-item {
			flex-direction: row;
		}
		body .formSelf .el-form-item__content {
			margin-left: 0 !important;
			flex: 1;
		}
		body .formSelf .el-form-item__label {
			text-align: right;
		}
		body .formSelf .upload-tip {
			font-size: 16px;
			padding-top: 93px;
		}
		body .formSelf .layui-btn {
			height: 33px;line-height: 33px;
		}
		body .formSelf .upload-tip:after {
			font-size: 22px;
			top: 30px;
		}
		.flex-el-link {
			transition: all .3s;
		}
		.flex-el-link.hover,
		.flex-el-link:hover {
			background-color: #33333355;
		}
		.flex-el-link .el-link:hover i{
			color: white !important;
		}
		.flex-el-link .el-link i{
			font-size: 20px;
			color: #ffffffab !important;
		}
		.flex-el-link .el-link{
			display: none;
		}
		.flex-el-link.hover .el-link,
		.flex-el-link:hover .el-link{
			display: inline-block;
		}
		body .box-uploader:hover .upload-img:after {
			content: '';
			font-size: 30px;
			background-color: #33333355;
		}
		body .main-left>a {
			line-height: 25px;
			margin-top: 0;
		}
		body .main-left {
			padding: 25px 13px 35px 27px;
		}
		.bt-1 {
			border-top: 1px solid #dddddd61;
		}
	</style>
	<div class="layui-tab-content" style="overflow: hidden;">
		<!-- <div class="layui-row card-title" style="background-color: #f5f5f5;">
			<h2 style="font-size: 18px;font-weight: bold;color: #333;">会员信息</h2>
		</div> -->
		<div class="page-main" id="app" v-cloak>
			<div>
				<div class="flex-center-h" style="border-bottom: 1px solid #e9e9e9;">
					<div  style="padding: 30px 37px 25px;">
						<div class="avatar-uploader" @click="clickAvatar">
							<el-avatar :size="104" :src="cAvatar" fit="contain">
								<!-- <img src="img/avatar.png"/> -->
							</el-avatar>
						</div>
					</div>
					<div>
						<span style="color: rgba(0,0,0,0.85);font-size: 1.1976vw;display: inline-block;margin-bottom: 10px;">会员账号 <span>{{ user.base.userName || '--' }}</span>  <a href="javascript:;" onclick="app.editUser('f_user_name')" style="font-size: .8383vw;"> 修改</a></span>
						<div style="color: rgba(0,0,0,0.65);font-size: 14px;">账号设置</div>
					</div>
				</div>
			</div>
			<div class="flex-between" style="height: calc( 100vh - 2.5vw  - 165px);overflow: hidden;">
				<div class="main-left">
					<a href="#base" class="left-nav-1"> 账号信息</a>
					<a href="#self" class="left-nav-2"> 个人信息{{ getFmtFull(user.self.infoType) }}</a>
					<a href="#enterprisePerson" class="left-nav-3">主名片信息</a>
				</div>
				<div class="main-ul" style="padding: 15px 0 35px 2.0313vw;overflow: auto;">
					<a name="base"></a>
					<div class="main-block">
						<a>账户信息</a>
					</div>
					<ul style="margin-right: 2.6042vw;">
						<li>
							<div>
								<div>昵称</div>
								<div>{{ user.base.name || '--'}}</div>
							</div>
							<a href="javascript:;" onclick="app.editUser('f_name')"> 修改</a>
						</li>
						<li>
							<div>
								<div>密码</div>
								<div>
									<span>当前密码强度: <span>{{ cPassLevel || ''}}</span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
									<span>上一次修改于 <span>{{ cPassTime || ''}}</span></span>
								</div>
							</div>
							<a href="javascript:;" onclick="app.editUser('f_user_password')"> 修改</a>
						</li>
						<li>
							<div>
								<div>手机号码&nbsp;&nbsp;
									<span v-if="!user.base.mobileCertified">
										<img src="../../mobile-vipcenter/img/uncheck.png" alt="" style="margin-top: -4px">
										未认证
									</span>
									<span v-else>
										<img src="../../mobile-vipcenter/img/checked.png" alt=""  style="margin-top: -4px">
										已认证
									</span>
								</div>
								<div>
									<span>已绑定手机: <span>{{ cMobile || '--'}}</span></span>
								</div>
							</div>
							<a href="javascript:;" onclick="app.editUser('f_mobile')">{{ user.base.mobileCertified ? '修改' : '认证' }}</a>
						</li>
						<li>
							<div>
								<div>备用邮箱
									&nbsp;&nbsp;
									<span v-if="!user.base.emailCertified">
										<img src="../../mobile-vipcenter/img/uncheck.png" alt="" style="margin-top: -4px">
										未认证
									</span>
									<span v-else >
										<img src="../../mobile-vipcenter/img/checked.png" alt=""  style="margin-top: -4px">
										已认证
									</span>
								</div>
								<div>
									<span>已绑定邮箱: <span>{{ cEmail || '' }}</span></span>
								</div>
							</div>
							<a href="javascript:;" onclick="app.editUser('f_email')"> {{ user.base.emailCertified ? '修改' : '认证' }}</a>
						</li>
						<li>
							<div>
								<div>微信绑定</div>
								<div>
									<span v-if="cOpenId">
										已绑定微信: <span>{{ cOpenId }}</span>
									</span>
									<span v-else > 未绑定 </span>
								</div>
							</div>
							<a v-if="cOpenId" href="javascript:;" @click="wxUnbind"> 解绑</a>
							<!-- <a v-if="cOpenId" href="javascript:;" onclick="app.editUser('f_wechat')"> 修改</a> -->
							<a v-else href="javascript:;" onclick="app.goToWxAuth()"> 绑定 </a>
						</li>
						<li>
							<div>
								<div>微信推送</div>
								<div>
									<span>
										允许展之推送信息至微信账号
									</span>
								</div>
							</div>
							<el-switch v-model="ifWxPush" @change="pushChange"></el-switch>
						</li>
					</ul>

					<a name="self"></a>
					<div class="flex-between main-block bt-1">
						<a>个人信息{{ getFmtFull(user.self.infoType) }}</a>
						<el-link v-if="showEditSelf" :underline="false" icon="el-icon-edit" @click="setFormSelf();showEditSelf=!showEditSelf;">{{ showEditSelf ? '编辑' : '取消' }}</el-link>
					</div>
					<div v-if="showEditSelf"  style="padding-bottom: 1px;color: #666666FF;font-size: .8333vw;line-height: 1.8229vw;">
						<div v-for="it in cFormSelfFields" :key="it.field" :style="{
							display: 'inline-flex',
							width: it.width || '28.6458vw',
						}">
							<div style="text-align: right;padding-right: 20px;width: 120px;">{{ it.fieldName || ''}}</div>
							<div style="flex: 1">{{ user.self[it.field] || '--' }}</div>
						</div>
					</div>
					<div v-else class="formSelf formSelfEdit" style="padding-right: 13vw;">
						<el-form ref="formSelf" :model="formSelf" label-width="120px">
							<div style="display: grid;grid-template-columns: 28.6458vw 28.6458vw;padding-bottom: 10px;">
								<el-form-item v-for="it in cFormSelfFields" :key="it.field"
									:style="{
										display: 'inline-flex',
										flexDirection: 'row',
										width: it.width || '28.6458vw',
										...(it.full ? {'grid-column': 'span 2'}: {})
									}"
									:label="it.fieldName" :prop="it.field"
									:rules="it.required ? [
										{
											required: true, message: '此为必输项'
										},
										...(it.rules || [])
										] : (it.rules || [])"
								>
									<el-select v-if="it.type=='select'"  size="small"  v-model="formSelf[it.field]"
										:placeholder="it.fieldName" clearable
									>
										<el-option v-for="item in it.typeData" :key="item.value"
											:label="item.label"
											:value="item.value"
										></el-option>
									</el-select>
									<el-date-picker v-else-if="it.type=='date'" v-model="formSelf[it.field]"
										align="right"
										size="small"
										type="date"
										placeholder="选择日期"
										value-format="yyyy-MM-dd"
									>
									</el-date-picker>
									<el-input-number  size="small" v-else-if="it.type=='number'" v-model="formSelf[it.field]" controls-position="right" :min="0" :max="99999"></el-input-number>
									<el-input  size="small" v-else :type="it.type || 'text'" clearable v-model.trim="formSelf[it.field]" :placeholder="it.fieldName"></el-input>
								</el-form-item>
							</div>
							<div class="layui-btn" @click="saveEditSelf" style="width: 92px;margin-left: 120px;">保存</div>
							<div class="layui-btn layui-btn-primary" @click="setFormSelf();showEditSelf=!showEditSelf;" style="width: 92px;">取消</div>
						</el-form>
					</div>

          <a name="enterprisePerson"></a>
          <div class="flex-between main-block bt-1" style="margin-top: 20px;">
            <a>主名片信息</a>
            <el-link :underline="false" icon="el-icon-edit" @click="onEditEnterprisePerson(user.enterprisePerson)">编辑</el-link>
          </div>
          <div style="margin-bottom: 50px;padding-bottom: 1px;color: #666666;font-size: .8333vw;line-height: 1.8229vw;">
            <div v-for="it in formPersonCardRules" :key="it.field" :style="{
							display: 'inline-flex',
							width: it.width || '28.6458vw',
						}">
              <div style="text-align: right;padding-right: 20px;width: 120px;">{{ it.fieldName || ''}}</div>
              <div style="flex: 1">{{ user.enterprisePerson[it.field] || '--' }}</div>
            </div>
          </div>
					<!--<a name="company"></a>
					<div class="flex-between main-block bt-1" style="margin-top: 20px;">
						<a>公司信息{{ getFmtFull(user.company.infoType) }}</a>
						<el-link v-if="showEditCompany" :underline="false" icon="el-icon-edit" @click="setFormCompany();showEditCompany=!showEditCompany">{{ showEditCompany ? '编辑' : '取消' }}</el-link>
					</div>
					<div style="min-height: 70vh;">
						<div v-if="showEditCompany" style="padding-bottom: 1px;color: #666666FF;font-size: .8333vw;line-height: 1.8229vw;">
							<div v-for="it in cFormCompanyFields" :key="it.field" :style="{
								display: 'inline-flex',
								width: it.width || '28.6458vw',
							}">
								<div style="text-align: right;padding-right: 20px;width: 120px;">{{ it.fieldName || ''}}</div>
								<div style="flex: 1" v-if="it.type == 'img'">
									<el-image
										v-if="user.company[it.field] !== undefined"
										style="margin-top: 10px;width: 139px; height: 139px;padding: 10px;border: 1px solid #00000026;border-radius: 3px;"
										:preview-src-list="[getImgUrl(user.company[it.field])]"
										:src="getImgUrl(user.company[it.field])" >
										<img slot="error" src="../../img/blank.jpg" alt="" style="width: 117px; height: 117px;">
									</el-image>
								</div>
								<div style="flex: 1" v-else >{{ user.company[it.field] || '&#45;&#45;' }}</div>
							</div>
						</div>
						<div v-else class="formSelf formCompanyEdit" style="padding-right: 13vw; ">
							<el-form ref="formCompany" :model="formCompany" label-width="120px">
								&lt;!&ndash; <div style="display: grid;grid-template-columns: 28.6458vw 28.6458vw;padding-bottom: 10px;"> &ndash;&gt;
								<div style="display: flex;padding-bottom: 10px;flex-wrap: wrap;width: 60vw;">
									<el-form-item v-for="it in cFormCompanyFields" :key="it.field"
										:label="it.fieldName" :prop="it.field"
										:rules="it.required ? [
											{
												required: true, message: '此为必输项'
											},
											...(it.rules || [])
											] : (it.rules || [])"
										:style="{flexBasis: it.width || '28.6458vw'}"
									>
										<div :style="{ width: it.width || '20.8333vw'}">
											<div v-if="it.type=='img'" style="display: flex;">
												<div v-if="formCompany[it.field]" class="box-uploader"
													style="width: 140px;
													height: 140px;
													background: #ffffff;
													border: 1.3px solid #e5e5e5;">
														<div style="width: 100%;height: 100%;position: relative;">
															<el-image
																style="width: 100%;height: 100%;"
																:ref="'formCompany_'+it.field"
																:src="getImgUrl(formCompany[it.field])"
																:preview-src-list="[getImgUrl(formCompany[it.field])]" >
																<img slot="error" src="img/blank.jpg" alt="" style="width: 100%; height: 100%;">
															</el-image>
															&lt;!&ndash; <img :src="getImgUrl(formCompany[it.field])" alt="" style="width: 100%;height: 100%;"> &ndash;&gt;
															<div class="flex-el-link" style="position: absolute;inset:0; padding: 50px 0 0 40px;">
																<el-link icon="el-icon-view" @click="viewImg('formCompany_'+it.field)"  :underline="false"></el-link> &nbsp;&nbsp;
																<el-link icon="el-icon-delete" @click="formCompany[it.field] = ''" :underline="false"></el-link>
															</div>
														</div>
												</div>
												<div v-else class="box-uploader cursor"
													@click="clickUploadImg(it.field)"
													style="width: 140px;
													height: 140px;
													background: #ffffff;
													border: 1.3px solid #e5e5e5;">
													&lt;!&ndash; :src="'/image/\\0ad23dd5823d44429c11b56717b83e65.png'" &ndash;&gt;
													&lt;!&ndash; <div v-if="formCompany[it.field]" style="width: 100%;height: 100%;" class="upload-img">
														<img
															:src="getImgUrl(formCompany[it.field])"
																alt="" style="width: 100%;height: 100%;">
													</div>
													<div v-else class="upload-tip"> &ndash;&gt;
													<div class="upload-tip">
														上传{{ it.fieldName || '&#45;&#45;'}}
													</div>
													<el-input type="hidden" v-model.trim="formCompany[it.field]"></el-input>
												</div>
												<div style="flex:1;color: #999;padding-left: 15px;">
													{{ it.tip || ''}}
												</div>
											</div>
											<el-input size="small"  v-else-if="it.type=='textarea'" type="textarea"
													:autosize="{ minRows: 3, maxRows: 9}" clearable
													v-model="formCompany[it.field]" :placeholder="it.fieldName"
											></el-input>
											<el-input v-else size="small" type="it.type || 'text'"
											 :disabled="it.field == 'companyName' ? companyNameDisable : false" clearable
											 v-model.trim="formCompany[it.field]" :placeholder="it.fieldName"></el-input>
										</div>
									</el-form-item>
								</div>
								<div class="layui-btn" @click="saveEditCompany" style="width: 92px;margin-left: 120px;">保存</div>
								<div class="layui-btn layui-btn-primary" @click="setFormCompany();showEditCompany=!showEditCompany" style="width: 92px;">取消</div>
							</el-form>
						</div>
					</div>-->
				</div>
			</div>
      <enterprise-person-editor ref="epEditor"></enterprise-person-editor>
		</div>
	</div>

		<div style="border-left: 1px solid #e9e9e9;padding-left: 2.006vw;margin-bottom: 1.1976vw;display: none;">
			<div style="color: rgba(0,0,0,0.65);font-size: .8383vw;">头像</div>
			<div style="padding: .9581vw 0 .5988vw">
				<img src="../../img/newvip/df_user.png" alt="" id="js_f_headImage" style="width: 8.3832vw;height: 8.3832vw;
				border-radius: 50%;
				object-fit: cover;
		">
				<input type="file" id="addPic" style="display:none;" onchange="selecttype(this)">
			</div>
			<button class="layui-btn" style="background: #ffffff !important;
			border: 1px solid rgba(0,0,0,0.15);
			color: rgba(0,0,0,0.65);padding: 0 13px;margin-left: 25px;
			border-radius: 2px;" onclick="uploadImgByType()"><i class="layui-icon layui-icon-upload-circle"></i> 上传文件</button>
		</div>
		<!-- <div class="vipApprove border-color-theme">
			<p>会员认证：<span style="margin-left: 22px;color: #333;" id="state">未认证</span><em id="applyApprove" class="color-theme" onclick="submitApprove()">申请认证</em></p>
		</div>
		<div class="company-vip-info">
			<ul>
				<li>
					<label>用户名：</label>
					<span id="userName"></span>
				</li>
				<li>
					<label>手机号码：</label>
					<span id="mobile"></span>
				</li>
				<li>
					<label>邮箱：</label>
					<span id="email"></span>
				</li>
				<li>
					<label>密码：</label>
					<span id="updateTime"></span>
					<em onclick="amendCipher()" class="color-theme">修改</em>
				</li>
			</ul>
		</div> -->
		<!-- 已废弃 : 修改微信 -->
		<div class="layui-fluid" id="js-editUserWx" hidden="hidden" style="margin-top: 17px;">
			<div class="" style="overflow: hidden;">
				<div class="layui-col-xs12">
					<center style="font-weight: bold;">
						<i class="layui-icon layui-icon-login-wechat" style="color: #2AAE67;"></i>
						使用微信扫一扫登录
					</center>
					<center style="line-height: 30px;padding-bottom: 10px;">[会员中心]</center>
					<center id="jsf-qr" style="width: 250px;height: 250px;margin: 0 auto;overflow: hidden;">
						<!-- <img src="http://deve.smtcrm.com:81/zentao/data/upload/1/202304/1218504201381umd.png" alt=""> -->
					</center>
				</div>
			</div>
		</div>
		<script>
			$(function() {
				const preview = window.origin + '/eip-web-vip/mobile-vipcenter/home.html?orgNum=' + orgNum;
				$('#jsf-qr').html('').qrcode({
					text: preview,
					correctLevel: 0,
					width: "250",
					height: "250",
				});
			})
		</script>

		<!-- 提示 微信公众号 -->
		<div class="layui-fluid" id="js-bindWxZz" hidden="hidden" style="margin-top: 27px;">
			<div class="" style="overflow: hidden;">
				<div class="layui-col-xs12">
					<center style="font-size: 16px;padding-bottom: 5px;">
						<i class="layui-icon layui-icon-login-wechat" style="color: #2AAE67;"></i>
						使用微信扫一扫<span id="jsf-tip1"></span>
					</center>
					<center style="line-height: 30px;padding-bottom: 10px;" id="jsf-tip2">展之云</center>
					<center id="jsf-zzQr" style="width: 150px;height: 150px; margin: 0 auto;overflow: hidden;">
					</center>
				</div>
			</div>
		</div>
		<script>
			function zzWxOpen(url) {
				$('#jsf-tip2').html(vueApp.push.wxChatAccountName || '');
				if(url) {
					$('#jsf-tip1').html('绑定');
					$('#jsf-zzQr').html('').qrcode({
						text: url,
						correctLevel: 0,
						width: "150",
						height: "150",
					});
				} else {
					$('#jsf-tip1').html('关注');
					$('#jsf-zzQr').html(`<img src="${vueApp.push.wxChatQRCodeUrl}" alt="" style="width: 150px;">`)
				}
				layertable = layer.open({
					type: 1,
					title: '提醒',
					skin: 'layui-layer-demo jsf jsf-userBase', //加上边框
					area: ['300px', '350px'], //宽高
					content: $('#js-bindWxZz'),
					btn: false,
					success(layero, index){
					},
					cancel() {
						//右上角关闭回调
						//return false 开启该代码可禁止点击该按钮关闭
						// cancelTip(layertable)
						app.getPushState();
						layer.close(layertable)
					},
				});
			}
		</script>

		<!-- 修改手机号/邮箱 -->
		<div class="layui-fluid" id="js-editUserBind" hidden="hidden" style="margin-top: 17px;">
			<div class="" style="overflow: hidden;">
				<div class="layui-col-xs12 jsf-mobile-wrap">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px;">
							新手机号</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;">
							<input type="text" lay-verify="phone"  autocomplete="off" name="f_mobile" id="jsf_f_mobile" class="layui-input h40">
						</div>
					</div>
				</div>
				<div class="layui-col-xs12 jsf-email-wrap">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px;">
							新邮箱</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;">
							<input type="text" lay-verify="email" onblur="app.checkEmail(this)" autocomplete="off" name="f_email" id="jsf_f_email" class="layui-input h40">
						</div>
					</div>
				</div>
				<div class="layui-col-xs12" style="padding: 5px 0;">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px">验证码</label>
						<div class="layui-input-block layui-col-xs7" style="display: flex;margin-left: 0;">
							<input type="text" autocomplete="off" name="code" id="jsf_code" class="layui-input h40">
							&nbsp;&nbsp;
							<button class="layui-btn" id="js-sendCode" onclick="app.sendCode(60)"> 发送验证码 </button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 修改用户名/昵称 -->
		<div class="layui-fluid" id="js-editUserBase" hidden="hidden" style="margin-top: 17px;">
			<div class="" style="overflow: hidden;">
				<div class="layui-col-xs12 jsf-userName-wrap">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px;">
							原账号</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;">
							<input type="text" disabled autocomplete="off" name="f_user_name_src" id="jsf_f_user_name_src" class="layui-input h40">
						</div>
					</div>
				</div>
				<div class="layui-col-xs12 jsf-userName-wrap">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px;">
							新账号</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;">
							<input type="text" autocomplete="off" name="f_user_name" id="jsf_f_user_name" class="layui-input h40">
						</div>
					</div>
				</div>
				<div class="layui-col-xs12 jsf-name-wrap">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 100px">昵称</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;">
							<input type="text" autocomplete="off" name="f_name" id="jsf_f_name" class="layui-input h40">
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 修改密码 -->
		<div class="layui-fluid" id="amendCipher" hidden="hidden" style="margin-top: 34px;">
			<div class="tip">
				<div style="width: 0;height: 0;border: 10px solid transparent;position: absolute;top: 70px;left: -4px;					border-right-color: white;border-right-width: 15px;"></div>
				<div class="tip-state ">
					<div class="tip-desc1">强度: </div>
					<div style="padding: 15px 0;">
						<div class="layui-slider ">
							<div class="tip-desc2" class="layui-slider-bar"></div>
						</div>
					</div>
					<div style="color: #666 !important;">8-16位大小写英文字母、数字、符号组合, 且不包含空格和特殊符号</div>
				</div>
			</div>
			<div class="" style="overflow: hidden;padding: 0 10px">
				<div>
					<div style="font-size: 16px;color: #333;">账号密码  <span style="font-size: 13px;" id="js-pwdLevelDesc"></span></div>
					<!-- <div style="padding: 10px 0 20px;margin-bottom: 20px; color: #666;border-bottom: 2px solid #eeeeeea8;"></div> -->
					<div style="padding: 10px 0 20px;margin-bottom: 20px; color: #666;border-bottom: 2px solid #eeeeeea8;">为了您的账户安全，请及时修改密码并提高密码强度等级。</div>
				</div>
				<div class="layui-col-xs12" id="product-display-productName">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 80px;text-align: left;padding-left: 0;">
							<i style="color: #ff4444;">*</i>
							新密码</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;line-height: 24px;margin-top: 2px;width: 265px;">
							<input type="password" autocomplete="off"  onblur="checkPass(true)" onkeyup="checkPass(true)"  name="password" id="password" class="layui-input h40" value="">
						</div>
					</div>
				</div>
				<div class="layui-col-xs12">
					<div class="layui-form-item" style="width: auto;">
						<label class="layui-form-label" style="font-size: 14px;color: #666;width: 80px;text-align: left;padding-left: 0;">
							<i style="color: #ff4444;">*</i>
							重复密码</label>
						<div class="layui-input-block layui-col-xs7" style="margin-left: 0px;line-height: 24px;margin-top: 2px;width: 265px;">
							<input type="password" autocomplete="off" name="password1" id="password1" class="layui-input h40" value="">
						</div>
					</div>
				</div>
				<!-- <input type="button" id="button-passwork" onclick="buttonpasswork()" value="确定"> -->
			</div>
		</div>
		<script>
			function isPassValid(pass) {
				return !(/[^a-zA-Z0-9\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass));
			}
			function checkPass(ifShow=false) {
				if(!$('#password').val()) ifShow = false;
				if(ifShow){
					$('.window-password .tip-state')
						.removeClass('tip-state1')
						.removeClass('tip-state2')
						.removeClass('tip-state3')
						;
					$('.window-password .tip').show();
					const level = getPassLevel($('#password').val() || '');
					$('.window-password .tip-state').addClass('tip-state'+level);
					$('.window-password .tip-desc1').text('强度: '+ getPwdLevelDesc(level));
					// $('.window-password .tip-desc1').text('强度: '+ ['','低','中等','高'][level]);
				} else $('.window-password .tip').hide();
			}
			function getPassLevel(pass) {
				if(typeof pass !== 'string' || !pass) return 0;
				var level = 0;
				// pass = String(pass).toLocaleLowerCase();
				// 小写字母，级别+1
				if (/[a-z]/.test(pass)) level++;
				if (/[A-Z]/.test(pass)) level++;
				// 数字+1
				if (/[0-9]/.test(pass)) level++;
				// 其他+1
				if (/[\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass)) level++;
				return level;
			}
		</script>


		<!-- 认证 -->
		<div class="layui-fluid" id="submitApprove" hidden="hidden" style="margin-top: 34px;">
			<form class="layui-form" action="" onsubmit="return true;" id="approveInfoForm" lay-filter="approveInfoForm">
				<div style="overflow: hidden;">
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>公司名称：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="companyName" id="companyName" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>联系人：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="linkman" id="linkman" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>联系电话：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="tel" id="tel" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>移动电话：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="f_mobile" id="f_mobile" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>邮箱：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="f_email" id="f_email" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>职务：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="position" id="position" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row layui-inline ">
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>统一社会信用代码：</label>
						</div>
						<div class="layui-col-lg9">
							<input type="text" name="socialReditCode" id="socialReditCode" lay-verify="" placeholder="" autocomplete="off"
							 class="layui-input">
						</div>
					</div>
					<div class="layui-form-item layui-row">
						<div class="layui-col-lg3" style="display: none;">
							<input type="text" name="businessLicense" id="businessLicense" lay-verify="" placeholder="" autocomplete="off"
							 class="layui-input">
						</div>
						<div class="layui-col-lg3" style="display: none;">
							<input type="text" name="version" id="version" lay-verify="" placeholder="" autocomplete="off" class="layui-input">
						</div>
						<div class="layui-col-lg3">
							<label class="layui-form-label" style="width: auto;"><i style="color: #ff4444;">*</i>营业执照：</label>
						</div>
						<div class="">
							<div class="upImgDivs noImgs" onclick="certUpImgDiv()" style="display: block;">
								<p>上传营业执照</p>
							</div>
							<div class="upImgDivs haveImgs" style="display: none;">
								<img src="" alt="" id="charterPic" style="width: 200px;height: 142px;">
								<span id="delCharterPic" onclick="delphotopic()"><img src="../../img/del2.png"></span>
							</div>
						</div>
					</div>
				</div>
			</form>
		</div>

		<!-- 裁剪 -->
		<div class="tailoring-container cardImgShow" style="display: none;">
			<div class="black-cloth" onclick="closeTailor(this)"></div>
			<div class="tailoring-content">
				<div class="tailoring-content-one">
					<label title="上传图片" for="cardChooseImg" class="l-btn choose-btn">
						<input type="file" name="file" id="cardChooseImg" onchange="selectImg(this)" class="hidden">
						选择图片
					</label>
					<div class="close-tailoring" onclick="closeTailor(this)">×</div>
				</div>
				<div class="tailoring-content-two">
					<div class="tailoring-box-parcel">
						<img id="cardTailoringImg">
					</div>
					<div class="preview-box-parcel">
						<p>图片预览：</p>
						<div class="square previewImg"></div>
						<div class="circular previewImg"></div>
					</div>
				</div>
				<div class="tailoring-content-three">
					<button class="l-btn cropper-reset-btn">复位</button>
					<button class="l-btn cropper-rotate-btn">旋转</button>
					<button class="l-btn cropper-scaleX-btn">换向</button>
					<button class="l-btn sureCut" id="sureCut">确定</button>
				</div>
			</div>
		</div>
		<script type="text/javascript">
			var uploadType = '';
			// 上传营业执照
			function certUpImgDiv() {
				uploadType = 'cert'
				$('#cardTailoringImg').data('cropper').options.aspectRatio = companysizes;
				$(".cardImgShow").show();
				$("#cardChooseImg").click();
			}

			var gfile;
			//弹出框水平垂直居中
			(window.onresize = function() {
				var win_height = $(window).height();
				var win_width = $(window).width();
				if (win_width <= 768) {
					$(".tailoring-content").css({
						"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
						"left": 0
					});
				} else {
					$(".tailoring-content").css({
						"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
						"left": (win_width - $(".tailoring-content").outerWidth()) / 2
					});
				}
			})();

			//图像上传
			function selectImg(file) {
				var max_size = 10240;
				var img_size = 1024;
				var fileData = file.files[0];
				var size = fileData.size;
				file.value = '';
				checkFile(fileData)
				$(".tailoring-box-parcel").show();
				$(".preview-box-parcel").show();
				if (size > max_size * 1024) {
					alert("图片大小不能超过10M");
					return;
				} else if (size > img_size * 1024) {
					let fileObj = fileData; //  document.getElementById('cardChooseImg').files[0];
					//alert(fileObj)
					var reader = new FileReader();
					reader.readAsDataURL(fileObj)
					reader.onload = function(e) {
						let image = new Image() //新建一个img标签（还没嵌入DOM节点)
						image.src = e.target.result
						image.onload = function() {
							let canvas = document.createElement('canvas'),
								context = canvas.getContext('2d'),
								imageWidth = image.width / 5, //压缩后图片的大小
								imageHeight = image.height / 5,
								data = ''
							canvas.width = imageWidth
							canvas.height = imageHeight
							context.drawImage(image, 0, 0, imageWidth, imageHeight)
							data = canvas.toDataURL('image/jpeg')
							// console.log(data)
							//压缩完成
							// document.getElementById('img').src = data
							$('#cardTailoringImg').cropper('replace', data, false)
						}
					}
				} else {
					var reader = new FileReader();
					let fileObj = fileData; // document.getElementById('cardChooseImg').files[0];
					reader.readAsDataURL(fileObj)
					reader.onload = function(evt) {
						var replaceSrc = evt.target.result;
						//更换cropper的图片
						$('#cardTailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
					}
				}
			}

			function checkFile(file, ifVideo = !true) {
				if (!file) throw new Error('取消上传'); // layerMsg('请选择图片！');
				// ) { // 公司logo 公司图片
				if(file.type.startsWith('image/')){
					if(2048*1024 < file.size) {
						layerMsg('单张图片大小不得大于2M。<br/> 上传图片过大, 请重新上传');
					}
					if(!["image/png","image/jpeg","image/jpg"].includes(file.type)) {
						layerMsg('很抱歉，只能上传 jpg/jpeg/png格式图片!');
					};
				} else if(ifVideo && file.type.startsWith('video')) {
					if(10240*1024 < file.size) {
						layerMsg('视频大小不得大于10M。<br/> 上传视频过大, 请重新上传');
					}
				} else {
					layerMsg('很抱歉，只允许上传图片' + (!ifVideo ? '!' : '或视频!'));
				}
			}

			// TODO 动态比例
			var companysizes = 1.33;
			//var logosize = 25 /25;
			//cropper图片裁剪
			$('#cardTailoringImg').cropper({
				aspectRatio: companysizes, //默认比例
				preview: '.previewImg', //预览视图
				guides: false, //裁剪框的虚线(九宫格)
				autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
				dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
				movable: true, //是否允许移动剪裁框
				resizable: true, //是否允许改变裁剪框的大小
				zoomable: true, //是否允许缩放图片大小
				mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
				touchDragZoom: true, //是否允许通过触摸移动来缩放图片
				rotatable: true, //是否允许旋转图片
				crop: function(e) {
					// 输出结果数据裁剪图像。
				}
			});
			//旋转
			$(".cropper-rotate-btn").on("click", function() {
				$('#cardTailoringImg').cropper("rotate", 45);
			});
			//复位
			$(".cropper-reset-btn").on("click", function() {
				$('#cardTailoringImg').cropper("reset");
			});
			//换向
			var flagX = true;
			$(".cropper-scaleX-btn").on("click", function() {
				if (flagX) {
					$('#cardTailoringImg').cropper("scaleX", -1);
					flagX = false;
				} else {
					$('#cardTailoringImg').cropper("scaleX", 1);
					flagX = true;
				}
				flagX != flagX;
			});

			function dataURLtoFile(dataurl, filename) { //将base64转换为文件
				var arr = dataurl.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new File([u8arr], filename, {
					type: mime
				});
			}

			function selecttype(e) {
				let file = e.files[0];
				var file_id = e.value;
				e.value = '';
				checkFile(file);
				// uploadType = 'headImage'
				$('#cardTailoringImg').data('cropper').options.aspectRatio = 1;
				let fileObj = file; // document.getElementById('addPic').files[0];
				file_id = file_id.substring(file_id.indexOf("."));
				if (file_id == ".bmp" || file_id == ".png" || file_id == ".gif" || file_id == ".jpg" || file_id == ".jpeg") {
					var max_size = 10240;
					var img_size = 1024;
					if (!file) return;
					var size = file.size;
					$(".cardImgShow").toggle();
					if (size > max_size * 1024) {
						layerMsg("图片大小不能超过10M");
					} else if (size > img_size * 1024) {
						var reader = new FileReader();
						reader.readAsDataURL(fileObj)
						reader.onload = function(e) {
							let image = new Image() //新建一个img标签（还没嵌入DOM节点)
							image.src = e.target.result
							image.onload = function() {
								let canvas = document.createElement('canvas'),
									context = canvas.getContext('2d'),
									imageWidth = image.width / 5, //压缩后图片的大小
									imageHeight = image.height / 5,
									data = ''
								canvas.width = imageWidth
								canvas.height = imageHeight
								context.drawImage(image, 0, 0, imageWidth, imageHeight)
								data = canvas.toDataURL('image/jpeg')
								//压缩完成
								// document.getElementById('img').src = data
								$('#cardTailoringImg').cropper('replace', data, false)
							}
						}
					} else {
						var reader = new FileReader();
						reader.onload = function(evt) {
							var replaceSrc = evt.target.result;
							//更换cropper的图片
							$('#cardTailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
							//图像上传
						}
						reader.readAsDataURL(fileObj)
					}
					$(".tailoring-box-parcel").show();
				} else {
					var max_vidoesize = 10240;
					var img_vidoesize = 1024;
					var vidoesize = file.size;
					if (vidoesize > max_vidoesize * 1024) layerMsg("视频大小不能超过10M");
					uploadImg(file)
				}
			}
			$("#sureCut").on("click", function() {
				if ($("#cardTailoringImg").attr("src") == null) return false;
				var cas = $('#cardTailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
				var base64url = cas.toDataURL('image/jpeg', '0.0'); // 'image/jpg' 转换为base64地址形式
				var timestamp = Date.parse(new Date());
				var file = dataURLtoFile(base64url, timestamp + ".jpg");
				uploadImg(file, true);
				//关闭裁剪框
				$(".cardImgShow").hide();
				$(".tailoring-box-parcel").hide();
				$(".preview-box-parcel").hide();
			});

			function uploadImgByType(type = '') {
				uploadType = type || 'headImage';
				$('#addPic').click()
			}
			function uploadImg(file,isAfterCropper = false) {
				if (!file) layerMsg('请选择图片！');
				if(!isAfterCropper) checkFile(file);
				var formData = new FormData();
				formData.append("file", file); //data
				$.ajax({
					url: "/eip-web-business/upload/uploadFileOss",
					// url: variableSponsor + "/upload/uploadImg",
					dataType: "json",
					type: "post",
					data: formData,
					processData: false,
					contentType: false,
					async: true,
					success(res) {
						if(uploadType == 'cert') { // 营业执照
							$('#charterPic').attr('src', res.result.path);
							// $('#charterPic').attr('src', window.origin +  '/image/' + res.result.path);
							$("#businessLicense").val(res.result.path);
							$(".noImgs").hide();
							$(".haveImgs").show();
						} else if(uploadType == 'companyLogo') {
							vueApp.formCompany[uploadType] = res.result.path
						} else if(uploadType == 'companyPic') {
							vueApp.formCompany[uploadType] = res.result.path
						} else if(uploadType == 'businessLicense') {
							vueApp.formCompany[uploadType] = res.result.path
						} else if(uploadType == 'headImage') { // 头像
							app.saveUserAvatar(res.result.path)
						} else {
							layerMsg('未知的上传类型')
						}
					},
					error() {}
				});
			}
			//关闭裁剪框
			function closeTailor() {
				$(".tailoring-container").toggle();
			}
		</script>

	<script type="text/javascript">
		const url = (new URL(window.location.href)).searchParams;
		var layer;
		var openPass =  url.get('pass') || '';
		layui.use(['element', 'layer', 'form', 'table', 'jquery', 'util'], function() {
			var layertable; //添加弹出框
			var element = layui.element;
			layer = layui.layer;
			var form = layui.form;
			var table = layui.table;
			var util = layui.util;
			util.fixbar()
			// util.fixbar({
			// 	bar1: true,
			// 	bar2: true,
			// 	click: function(type){
			// 		console.log(type);
			// 		if(type === 'bar1'){
			// 			alert('点击了bar1')
			// 		}
			// 	}
			// });
			//删除图片
			window.delphotopic = function() {
				var index = layer.msg('是否删除营业执照?此操作不可恢复!', {
					btn: ['删除', '取消'],
					yes: function() {
						$("#charterPic").attr("src", "");
						$("#businessLicense").val('');
						$(".noImgs").show();
						$(".haveImgs").hide();
						layer.close(index);
					},
					btn2: function() {
						layer.close(index);
					}
				}, );
			}
			window.formDatas = function() {
				if (isgetCpInfor) {
					form.val("approveInfoForm", {
						"companyName": formData.f_company_name,
						"linkman": formData.f_contacts,
						"tel": formData.f_tel,
						"f_email": formData.f_email,
						"position": formData.f_position,
						"socialReditCode": formData.socialReditCode,
						"businessLicense": formData2.businessLicense,
						"version": formData.version,
					})
					form.val("approveInfoForm", {
						"companyName": formData.companyName,
						"linkman": formData.linkman,
						"tel": formData.tel,
						"f_mobile": formData.f_mobile,
						"f_email": formData.f_email,
						"position": formData.position,
						"socialReditCode": formData.socialReditCode,
						"businessLicense": formData.businessLicense,
						"version": formData.version,
					})
					var attachUrl = location.origin + "/image/";
					if (formData.businessLicense != null && formData.businessLicense != "") {
						$('#charterPic').attr('src', attachUrl + formData.businessLicense);
						$(".haveImgs").show()
						$(".noImgs").hide()
					} else {
						if (formData2.businessLicense != null && formData2.businessLicense != "") {
							$('#charterPic').attr('src', attachUrl + formData2.businessLicense);
							$(".haveImgs").show()
							$(".noImgs").hide()
						}else{
							$(".haveImgs").hide()
							$(".noImgs").show()
						}
					}
					if(formData.companyName ==null || formData.companyName == "" ){
						$("#companyName").val(formData2.f_company_name)
					}
					if(formData.linkman ==null || formData.linkman == "" ){
						$("#linkman").val(formData2.f_contacts)
					}
					if(formData.tel ==null || formData.tel == "" ){
						$("#tel").val(formData2.f_tel)
					}
					if(formData.f_email ==null || formData.f_email == "" ){
						$("#f_email").val(formData2.f_email)
					}
					if(formData.position ==null || formData.position == "" ){
						$("#position").val(formData2.f_position)
					}
					if(formData.socialReditCode ==null || formData.socialReditCode == "" ){
						$("#socialReditCode").val(formData2.socialReditCode)
					}
					if(formData.businessLicense ==null || formData.businessLicense == "" ){
						$("#businessLicense").val(formData2.businessLicense)
					}
				} else {
					form.val("approveInfoForm", {
						"companyName": formData.companyName,
						"linkman": formData.linkman,
						"tel": formData.tel,
						"f_mobile": formData.f_mobile,
						"f_email": formData.f_email,
						"position": formData.position,
						"socialReditCode": formData.socialReditCode,
						"businessLicense": formData.businessLicense,
						"version": formData.version,
					})
					var attachUrl = "http://" + window.location.hostname + ":" + window.location.port + "/image/";
					if (formData.businessLicense != null && formData.businessLicense != "") {
						$('#charterPic').attr('src', attachUrl + formData.businessLicense);
						$(".haveImgs").show()
						$(".noImgs").hide()
					} else {
						$(".haveImgs").hide()
						$(".noImgs").show()
					}
				}
				if(formData.f_mobile!="" && formData.f_mobile!=null){
					$("#f_mobile").val(formData.f_mobile)
					$("#f_mobile").attr("readonly",true)
				}else{
					$("#f_mobile").attr("readonly",false)
				}
			}
		});
		var zzyUserId = uinfo.zzyUserId;// GetQueryString('zzyUserId');
		var orgNum = uinfo.orgnum;//GetQueryString('orgnum');
		var version = 0
		var isgetCpInfor = true
		var formData = {}
		var formData2 = {}
		var isamend = false
		$(function() {
      Vue.use(top.httpVueLoaderExFactory(window, document, Function))
      httpVueLoaderEx.register(Vue, '../components/EnterprisePersonEditor.vue')
			window.vueApp = new Vue({
				el: '#app',
				data() {
					const isMobile = (rule, value, callback) => {
						if (!value || /^1[3-9][0-9]\d{8}$/.test(value)) {
							callback()
						} else {
							callback(new Error('手机号码格式如:138xxxx8754'))
						}
					}
					const isEmail = (rule, value, callback) => {
						if (!value || /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i.test(value)) {
							callback()
						} else {
							callback(new Error('电子邮箱格式错误!'))
						}
					}
					return {
						user: {
							base: {},
							self: {},
							company: {},
              enterprisePerson: {},
						},
						formCompany: {
							companyName: '',
							companyAbbr: '',
							address: '',
							comIntroduce: '',
							companyLogo: '',
							companyPic: '',
							tel: '',
							companyEmail: '',
							fax: '',
							site: '',
							socialReditCode: '',
							businessLicense: '',
							typeProduct: '',
						},
						formCompanyRules: [
							{
								field: 'companyName',
								need: true,
								required: false,
								fieldName: '公司名称',
							},
							{
								field: 'companyAbbr',
								need: true,
								required: false,
								fieldName: '公司简称',
							},
							{
								field: 'tel',
								need: true,
								required: false,
								fieldName: '联系电话 ',
							},
							{
								field: 'companyEmail',
								need: true,
								required: false,
								fieldName: '公司邮箱 ',
								rules:[{
									validator: isEmail,trigger:'blur'
								}]
							},
							{
								field: 'fax',
								need: true,
								required: false,
								fieldName: '传真 ',
							},
							{
								field: 'typeProduct',
								need: true,
								required: false,
								fieldName: '展品范围 ',
							},
							{
								field: 'site',
								need: true,
								required: false,
								fieldName: '公司网址 ',
							},
							{
								field: 'address',
								need: true,
								required: false,
								fieldName: '公司地址',
								width: '100%',
							},
							{
								field: 'socialReditCode',
								need: true,
								required: false,
								fieldName: '社会信用代码 ',
								width: '100%',
							},
							{
								field: 'companyLogo',
								need: true,
								required: false,
								fieldName: '公司Logo ',
								type: 'img',
								tip: '推荐400*400,大小不超过2M',
							},
							{
								field: 'companyPic',
								need: true,
								required: false,
								fieldName: '公司图片 ',
								type: 'img',
								tip: '推荐400*300,大小不超过2M',
							},
							{
								field: 'businessLicense',
								need: true,
								required: false,
								fieldName: '营业执照 ',
								width: '100%',
								type: 'img',
								tip: '大小不超过2M',
							},
							{
								field: 'comIntroduce',
								need: true,
								required: false,
								fieldName: '公司简介',
								width: '100%',
								type: 'textarea',
							},
						],
						formSelf: {
							fullName: '',
							// position: '',
							// mobile: '',
							// email: '',
							age: '',
							sex: '',
							// wechat: '',
							// qq: '',
							birthday:'',
							occupation: '',
							hobby:'',
							idCard: '',
              address: '',
            },
            formPersonCardRules: [
              {
                field: 'position',
                need: true,
                required: false,
                fieldName: '职务',
              },
              {
                field: 'mobile',
                need: true,
                required: false,
                fieldName: '手机',
                rules: [{
                  validator: isMobile, trigger: 'blur'
                }]
              },
              {
                field: 'email',
                need: true,
                required: false,
                fieldName: '邮箱',
                rules: [{
                  validator: isEmail, trigger: 'blur'
                }]
              },
              {
                field: 'wechat',
                need: true,
                required: false,
                fieldName: '微信号',
              },
              {
                field: 'qq',
                need: true,
                required: false,
                fieldName: 'QQ',
              },
            ],
						formSelfRules: [
							{
								field: 'fullName',
								need: true,
								required: false,
								fieldName: '姓名',
							},
							{
								field: 'age',
								need: true,
								required: false,
								fieldName: '年龄',
								type: 'number',
							},
							{
								field: 'sex',
								need: true,
								required: false,
								fieldName: '性别',
								type: 'select',
								typeData: [
									{
										label: '男',
										value: '男',
									},{
										label: '女',
										value: '女',
									}
								],
							},
							{
								field: 'birthday',
								need: true,
								required: false,
								fieldName: '出生日期',
								type: 'date',
							},
							{
								field: 'occupation',
								need: true,
								required: false,
								fieldName: '工作领域',
							},
							{
								field: 'hobby',
								need: true,
								required: false,
								fieldName: '兴趣领域',
							},
							{
								field: 'idCard',
								need: true,
								required: false,
								fieldName: '身份证号',
							},
              {
								field: 'address',
								need: false,
								required: false,
								fieldName: '地址',
                full: true,
                width: '57.2916vw',
                type: 'textarea'
							},
						],
						showEditSelf: true,
						showEditCompany: true,
						companyNameDisable: false, // 为了兼容
						push: {
							enable: false,
							code: '',
							openId: '',
							pushState: false,
							subscribe: false,
							show: false,
							wxAppId: '',
							wxChatAccountName: '',
							wxChatQRCodeUrl: '',
							wxChatQRCodeName: '',
						},
					}
				},
				watch: {
					'formSelf.age': {
						handler(n, o) {
							if(!n) this.formSelf.age = undefined;
						},
					}
				},
				computed: {
					ifWxPush() {
						return this.push.enable && !!this.push.pushState && !!this.push.subscribe && !!this.push.openId;
					},
					ifWxFav() {
						return !!this.push.subscribe;
					},
					ifWxBind() {
						return !!this.push.openId;
					},
					cFormSelfFields() {
						return this.formSelfRules.filter(it=> it.need);
					},
					cAvatar() {
						const url = this.user.base.headImage || 'img/avatar.png';
						if(location.origin.startsWith('https') && url.startsWith('http:')) {
							return 'https' + url.substr(4, url.length);
						} else if(url.startsWith('http') || url.startsWith('//')) {
							return url
						} else if(url.startsWith('/')){
							return '/image' + url;
						} else {
							return '/image/' + url;
						}
					},
					cFormCompanyFields() {
						return this.formCompanyRules.filter(it=> it.need);
					},
					cMobile() {
						return app.fmtMobile(this.user.base.mobile || '')
					},
					cEmail() {
						return app.fmtEmail(this.user.base.email || '')
					},
					cPassLevel() {
						return ['','低','中','中','高'][+this.user.base.pwdGrade || 0];
					},
					cPassTime() {
						let time = this.user.base.passwordUpdateTime ||''
						return time ? time.substr(0,16) : '';
					},
					cOpenIdSrc() {
						let openId = '';
						const tmp = (this.user.base.zzyUserSocials || []).find(i => i.socialtype == 32)
						if(tmp) {
							openId = tmp.openid || '';
						}
						return openId;
					},
					cOpenId() {
						let openId = this.cOpenIdSrc || '';
						if(openId) openId = openId.substr(0,4) + '*'.repeat(openId.length - 8) + openId.substr(-4);
						return openId;
					}
				},
				methods: {
					getFieldLabel(it) {
						return it.fieldName
						// let f = it.fieldName
						// if(it.type == 'img') {
						// 	if(it.field == 'companyLogo') {
						// 		return f+'(推荐400*400,大小不超过2M)'
						// 	} else if(it.field == 'companyPic') {
						// 		return f+'(推荐400*300,大小不超过2M)'
						// 	} else if(it.field == 'businessLicense') {
						// 		return f+ '(大小不超过2M)'
						// 	}
						// }
						// return f;
					},
					viewImg(field) {
						if(this.$refs[field]) this.$refs[field][0].clickHandler();
					},
					getImgUrl(url)  {
						if(!url) return 'img/';
						if(url.startsWith('//') || url.startsWith('http')) {
							return url
						} else {
							return '/image/' + url;
						}
					},
					pushChange(v) {
						if(v) {
							if(!this.ifWxBind) {
								app.goToWxAuth2();
								this.ifWxPush = false;
							} else if(!this.ifWxFav) {
								zzWxOpen();
								this.ifWxPush = false;
							} else {
								app.savePushState(true)
							}
							return;
						}
						app.savePushState(false)
					},
					clickUploadImg(field='') {
						if(field == 'companyLogo') {
							uploadImgByType(field)
						} else if(field == 'companyPic') {
							uploadImgByType(field)
						} else if(field == 'businessLicense') {
							uploadImgByType(field)
						}
					},
					setUserSelf(self) {
						this.user.self = self || {}
						this.setFormSelf();
					},
					setUserCompany(company) {
						this.user.company = company || {}
						this.setFormCompany();
					},
          setEnterprisePerson(enterprisePerson) {
						this.user.enterprisePerson = enterprisePerson || {}
					},
					setFormCompany() {
						Object.keys(this.formCompany).map(it=> {
							this.formCompany[it] = this.user.company[it] == null ? '' : this.user.company[it];
						})
					},
					setFormSelf() {
						Object.keys(this.formSelf).map(it=> {
							this.formSelf[it] = this.user.self[it] == null ? '' : this.user.self[it];
						})
					},
          async onEditEnterprisePerson({enterprisePersonId, enterpriseId}) {
            await this.$refs.epEditor.open({
              enterprisePersonId,
              enterpriseId,
              personName: encodeURIComponent(this.user.self.fullName),
            })
            app.getUserInfo()
          },
					async saveEditCompany() {
						await this.$refs['formCompany'].validate();
						let idx = layer.load();
						try{
							app.saveUserSelfOrCompany({
								zzyUserId: this.user.base.zzyUserId,
								userComId: this.user.company.userComId,
								...this.formCompany,
							}, false);
							this.showEditCompany = !this.showEditCompany
							setTimeout(() => {
								layer.close(idx);
							}, 300);
						} catch(e) {
							layer.close(idx);
						}
					},
					async saveEditSelf() {
						await this.$refs['formSelf'].validate();
						let idx = layer.load();
						try{
							app.saveUserSelfOrCompany({
								zzyUserId: this.user.base.zzyUserId,
								userComId: this.user.self.userComId,
								...this.formSelf,
							});
							this.showEditSelf = !this.showEditSelf
							setTimeout(() => {
								layer.close(idx);
							}, 300);
						} catch(e) {
							layer.close(idx);
						}
					},
					setUserSetting(setting) {
						let tmp = '';
						(setting || []).map(it=> {
							if(it.taskKindCode == 'fo_zzyuser_info_field_set') {
								tmp = this.formSelfRules.find(itt=> itt.field == it.param);
								if(tmp) {
									tmp.need = it.value == '1';
									tmp.required = !!it.isNull;
								}
							} else if(it.taskKindCode == 'fo_zzyuser_company_info_field_set') {
								tmp = this.formCompanyRules.find(itt=> itt.field == it.param);
								if(tmp) {
									tmp.need = it.value == '1';
									tmp.required = !!it.isNull;
								}
							}
						});
					},
					clickAvatar() {
						uploadType = 'headImage'
						$('#addPic').click();
					},
					getFmtFull(infoType) {
						return ['',' - 未填写',' - 未完善'][+infoType || 0];
					},
					wxUnbind() {
						app.wxUnbind(this.cOpenIdSrc)
					},
				},
        components: {

        }
			})
			app.getCompanySetting();
			app.getUserSetting();
			app.getPushSetting(); // 是否启用
			app.getPushState();
			app.getUserInfo();
			if(openPass) app.editUser('f_user_password');
			// LoadUserCompany()
			// getVipInfor()
		})
		function afterAuth(code, state) {
			// // test only
			// app.wxOpenId = 'o0BLn5qF1kOREZwy87NGzBpavsnQ';
			// app.wxState = 32;
			// app.wxBind();
			if(state == 32) { // 微信回调
				app.wxCode = code;
				app.wxState = state;
				app.getWxUserInfo();
			}
		}
		function layerMsg(msg='操作失败',icon=2,throws=true) {
			const idx = layer ? layer.msg(msg,{icon}) : 0;
			if(throws) throw new Error(msg)
			return idx
		}
		function checkRet(data) {
			if(data.state !== 1) {
				layerMsg(data.msg || data.message || '操作失败')
			}
		}
		/*获取参数*/
		function GetQueryString(name) {
			return url.get(name) || ''
			// var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			// var r = window.location.search.substr(1).match(reg);
			// if (r != null) return unescape(r[2]);
			// return null;
		}

		function LoadUserCompany() {
			if (!zzyUserId) layerMsg('请求失败，请重新登录')
			$.ajax({
				url: 'http://' + variable + '/userCompany/getByUserId', //请求服务器url地址.   +cid+"&pid="+pid
				type: "post",
				data: {
					f_zzyuser_id: zzyUserId,
				},
				datatype: 'json',
				async: true,
				xhrFields: {
					withCredentials: true
				},
				// beforeSend() {},
				success(data) {
					if (data.state > 0 && data.data != null) {
						formData2 = data.data
					}
				},
				// complete() {},
				error(e){ layerMsg(e.message) },
			});
		}

		function cancelTip(layertable) {
			layer.close(layertable);
			// var confim = layer.confirm('确认退出编辑？', {
			// 	btn: ['确认', '取消'] //按钮
			// }, function() {
			// 	layer.close(layertable);
			// 	layer.close(confim);
			// }, function() {
			// 	layer.close(confim);
			// });
		}

		// open 修改用户 手机号/邮箱
		function editUserBind(isMobile=false) {
			app.isMobile = isMobile;
			$('#js-sendCode').removeClass('layui-btn-disabled').html("发送验证码");
			layertable = layer.open({
				type: 1,
				title: isMobile ? (app.userInfo.mobileCertified ? '更换手机号':'认证手机号') : (app.userInfo.emailCertified ? '更换邮箱' : '认证邮箱'),
				skin: 'layui-layer-demo jsf jsf-userBind', //加上边框
				area: ['524px', '220px'], //宽高
				content: $('#js-editUserBind'),
				btn: ['确定', '取消'],
				success(layero, index){
					// 隐藏另外
					$('.jsf-userBind .jsf-' + (isMobile ? 'email-wrap' : 'mobile-wrap')).hide()
					$('.jsf-userBind .jsf-' + (!isMobile ? 'email-wrap' : 'mobile-wrap')).show()
					// 填充数据
					$('.jsf-userBind')
						.find('input[name=f_mobile]').val(app.userInfo.mobileCertified ? '' : (app.userInfo.mobile ||''))
						.end().find('input[name=f_email]').val(app.userInfo.emailCertified ? '' : (app.userInfo.email ||''))
				},
				yes(idx, layero) {
					app.saveUserBind(isMobile)
					layer.close(idx)
				},
				btn2() {
					cancelTip(layertable)
				},
				cancel() {
					//右上角关闭回调
					//return false 开启该代码可禁止点击该按钮关闭
					cancelTip(layertable)
				},
			});
		}
		// open 修改用户 微信
		function editUserWx(isName='') {
			layertable = layer.open({
				type: 1,
				title: isName ? '修改绑定' : '绑定微信',
				skin: 'layui-layer-demo jsf jsf-userBase', //加上边框
				area: ['350px', '400px'], //宽高
				content: $('#js-editUserWx'),
				btn: false,
				success(layero, index){
				},
				cancel() {
					//右上角关闭回调
					//return false 开启该代码可禁止点击该按钮关闭
					// cancelTip(layertable)
					layer.close(layertable)
				},
			});
		}
		// open 修改用户 昵称/用户名
		function editUserBase(isName=false) {
			layertable = layer.open({
				type: 1,
				title: isName ? '修改昵称' : '更换会员账号',
				skin: 'layui-layer-demo jsf jsf-userBase', //加上边框
				area: ['524px', isName ? '170px' : '230px'], //宽高
				content: $('#js-editUserBase'),
				btn: ['确定', '取消'],
				success(layero, index){
					// 隐藏另外
					$('.jsf-userBase .jsf-' + (isName ? 'userName-wrap' : 'name-wrap')).hide()
					$('.jsf-userBase .jsf-' + (!isName ? 'userName-wrap' : 'name-wrap')).show()
					// 填充数据
					$('.jsf-userBase')
						.find('input[name=f_user_name_src]').val(app.userInfo.userName || '')
						.end().find('input[name=f_user_name]').val('')
						.end().find('input[name=f_name]').val(app.userInfo.name || '')
					// console.log(layero, index);
				},
				yes(idx, layero) {
					app.saveUserBase(isName)
					layer.close(idx)
				},
				btn2() {
					cancelTip(layertable)
				},
				cancel() {
					//右上角关闭回调
					//return false 开启该代码可禁止点击该按钮关闭
					cancelTip(layertable)
				},
			});
		}

		// open 修改密码
		function amendCipher() {
			$('#js-pwdLevelDesc').html('(当前强度: ' + getPwdLevelDesc(pwdLevel) + ')');
			$('#password,#password1').val('');
			layertable = layer.open({
				type: 1,
				title: '修改密码',
				// skin: 'layui-layer-demo jsf', //加上边框
				// area: ['524px', 'auto'], //宽高
				skin: 'layui-layer-demo window-password', //加上边框
				area: ['430px', 'auto'], //宽高
				content: $('#amendCipher'),
				btn: ['确定', '取消'],
				success(layero, index){
					$('.window-password .tip').hide();
				},
				yes(idx, layero) {
					// console.log(idx, layero);
					buttonpasswork()
				},
				btn2() {
					cancelTip(layertable)
				},
				cancel() {
					//右上角关闭回调
					//return false 开启该代码可禁止点击该按钮关闭
					cancelTip(layertable)
				},
			});
		}

		// open 认证
		function submitApprove() {
			if(isamend){
				control(true)
			}else{
				control(false)
			}
			//$("#version").val(version)
			layertables = layer.open({
				type: 1,
				title: '申请认证',
				skin: 'layui-layer-demo', //加上边框
				area: ['1000px', '600px'], //宽高
				content: $('#submitApprove'),
				btn: ['提交'],
				yes(index, layero) {
					submitApproves()
				},
				cancel() {
					//右上角关闭回调
					//return false 开启该代码可禁止点击该按钮关闭
					cancelTip(layertable)
				},
			});
			formDatas()
		}

    function control(type){
			$("#companyName").attr("readonly",type)
			$("#linkman").attr("readonly",type)
			$("#tel").attr("readonly",type)
			$("#f_email").attr("readonly",type)
			$("#position").attr("readonly",type)
			$("#socialReditCode").attr("readonly",type)
			$("#businessLicense").attr("readonly",type)
			if(!type){
				$("#delCharterPic").show()
			}else{
				$("#delCharterPic").hide()
			}
		}

		var appLogin3  = {
			wxCode: '',
			wxAppid: '',
			wxState: '',
			wxOpenId: '',
			// ifWxPcBind: false,
			// wxBindOpenId: '',
			getWxUserInfo() {
				const that = this;
				// 获取openId
				util.post(API + `/auth/socialLogin/${this.wxState}/${this.orgNum}`, {
					socialType: this.wxState,
					orgNum: this.orgNum,
					code: this.wxCode,
					state: this.wxState,
				},{},function(data) {
					that.checkRet(data);
					that.wxOpenId = data.data.openId || '';
					that.wxBind()
				})
			},
			wxBind() {
				if(!this.wxOpenId) this.error('微信绑定失败: openId');
				const that = this;
      	util.post(API + '/socialUser/bind',{
					socialType: 32,
					openId: this.wxOpenId,
					orgNum: this.orgNum,
					zzyUserId: this.zzyUserId,
				},{}, function(data) {
					if (data.state !== 1) {
						this.checkRet(data);
					} else {
						layerMsg('绑定成功',1 , false);
						location.reload(true);
					}
				})
			},
			wxUnbind(openId) {
				if(!openId) this.error('微信还未绑定!');
				// 解绑微信
				const that = this;
				layer.confirm('即将解除微信绑定 , 是否继续？', function(index) {
					layer.close(index);
					var loading = layer.load(3, {
						shade: [0.2],
					});
					try{
						util.post(API +'/socialUser/unbind', {
							socialType: 32,
							openId // : that.wxBindOpenId,
						}, {}, function(data) {
							that.checkRet(data);
							location.reload(true);
						})
					}finally{
						layer.close(loading);
					}
      	});
			},
			getWxConfig() {
				const that = this
				util.post(API +'/api/sysSettingOrgNum/getSocialAppId', {
					socialType: 32,
					// redirect_uri: location.href,
					orgNum: this.orgNum,
				}, {}, function(data) {
					that.checkRet(data);
					that.wxAppId = data.data || '';
				})
			},
			// 在新窗口中打开URL
			OpenWindows(url, width, height, name) {
					if (name == undefined) name = "";
					var leftSpan = (window.screen.availWidth - 27) / 2 - width / 2;
					var topSpan = (window.screen.availHeight - 8) / 2 - height / 2 - 30;
					var str = "width=" + width + ",height=" + height + ",top=" + topSpan + ",left=" + leftSpan + ",resizable=1,scrollbars=yes";
					return window.open(url, name, str);
			},
			goToWxAuth() {
				// if(this.ifWxPcBind) { // 解绑
				// 	this.wxUnbind();
				// 	return;
				// }
				const socialType = 32;
				// 获取 appid
				if(!this.wxAppId) this.getWxConfig()
				if(!this.wxAppId) this.error('微信登录失败: appid');
				const url2 = HOST + `/pages/frontend/afterauth.html?appid=${this.wxAppId}&socialType=${socialType}&app=vipPc`
				// // test onlly
				// this.OpenWindows(url2, 850, 500);
				const redirect = encodeURIComponent(url2);
				const url = `https://open.weixin.qq.com/connect/qrconnect?appid=${this.wxAppId}&redirect_uri=${redirect}&response_type=code&scope=snsapi_login&state=${socialType}#wechat_redirect`;
				this.OpenWindows(url, 850, 500);
				// // 微信内嵌二维码
				// const redirect = encodeURIComponent(window.location.href)
				// this.showLogin3Qr = true
				// this.$nextTick(_ => {
				//   this.showLogin3WxQr({
				//     self_redirect: true,
				//     id: "wxLoginQr",
				//     appid: this.wxAppId,
				//     scope: "snsapi_login",
				//     redirect_uri: redirect,
				//     state: socialType,
				//     style: "black",
				//     href: encodeURIComponent(HOST + "/pages/frontend/css/wxQr.css"), // 需要https
				//   });
				// });
				// // 微信浏览器授权
				// const redirect = encodeURIComponent(window.location.href)
				// window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.wxAppId}&redirect_uri=${redirect}&response_type=code&state=${socialType}&scope=snsapi_base#wechat_redirect`
				// throw new Error("you can't go down")
				// // window.location.href = window.location.href + '&'
			},
		}
		var app = {
			isMobile: false,  // 修改手机/邮箱
			smsWaiting: false,
			orgNum,
			zzyUserId,
			userInfo: { // 用户基本信息
			},
			...appLogin3,
			editUser(field){
				if(field == 'f_user_password') { // 修改密码
					amendCipher();
				} else if(field == 'f_user_name') {
					editUserBase(false)
				} else if(field == 'f_name') {
					editUserBase(true)
				} else if(field == 'f_email') {
					editUserBind(false)
				} else if(field == 'f_wechat') { // 已废弃
					// editUserWx(this.userInfo.f_wechat)
				} else if(field == 'f_mobile') {
					editUserBind(true)
				}
			},
			sendCode(t) {
				// if(!this.isMobile) layerMsg('暂不支持');
				if(this.smsWaiting) layerMsg('请稍后重试!');
				const userName = this.isMobile ? $('#jsf_f_mobile').val() : $('#jsf_f_email').val();
				if(!userName && this.isMobile) layerMsg('需要手机号!');
				if(!userName && !this.isMobile) layerMsg('需要邮箱!');
				const that = this
				that.post('/sms/sendCode',{
						userName,
						sendType: this.isMobile ? 1 : 2,
						sources: 'VIP',
						orgNum,
						projectId: 0,
					},{
					}, data => {
						checkRet(data)
						layerMsg('发送成功', 1, false);
						that.smsWaiting = true;
						for (var i = 1; i <= t; i++) {
							window.setTimeout("app.update_a(" + i + "," + t + ")", i * 1000);
						}
					},
				);
			},
			update_a(i, t) {
				if (i >= t) {
					this.smsWaiting = false;
					$('#js-sendCode').removeClass('layui-btn-disabled').html("重新发送")
				} else {
					this.smsWaiting = true;
					$('#js-sendCode').addClass('layui-btn-disabled').html((t - i) + "秒后重发")
				}
			},
			getHiddenString(value='',start=0,len=0) {
				if (!value) return ''
				const vlen = value.length
				if (vlen<0||start<0||len<0) return '';
				var hidden = '';
				for (var i = 0; i < len; i++) { hidden += '*'; }
				if(vlen <= start ){
					return hidden
				} else if(vlen <= start + len){
					return value.substring(0, start) + hidden
				} else {
					return value.substring(0, start) + hidden + value.substring(start+len);
				}
			},
			fmtMobile(str) {
				return this.getHiddenString(str, 3,4)
			},
			fmtEmail(str) {
				return this.getHiddenString(str, 3,4)
			},
			getPushSetting() {
				const that = this
				that.post('/pushNote/checkWxAwokeSetting',{
					orgNum,
				 },{
						async: false,
					}, data => {
						if(data && data.state == 1) vueApp.push.enable = true;
						if(vueApp.push.enable) that.getPushSettingExt();
					},
				);
			},
			getPushSettingExt() {
				const that = this
				that.post('/sysSettingOrg/selectWeChatSetting',{
					orgNum,
				 },{
						async: false,
					}, data => {
						checkRet(data);
						data.data.map(it=> {
							vueApp.push[it.settingCode] = it.settingValue;
						})
					},
				);
			},
			getPushState() {
				const that = this
				that.post('/pushNote/selectAuthorById',{ },{
					async: false,
				}, data => {
					if(!data || data.state != 1) {
						vueApp.push.openId    = '';
						vueApp.push.pushState = false;
						vueApp.push.subscribe = false;
						return;
					}
					vueApp.push.openId    = data.data.openId || '';
					vueApp.push.pushState = data.data.pushState == 2;
					vueApp.push.subscribe = data.data.subscribe == 1;
				});
			},
			savePushState(ifPush) {
				const that = this
				that.post('/pushNote/enableWxPushSwitch',{
					pushState: ifPush ? 2 : 1,
				},{
						async: false,
					}, data => {
						checkRet(data);
						that.getPushState();
					},
				);
			},
			goToWxAuth2() {
				// 绑定微信 ?
				//  临时二维码
				//  https://developers.weixin.qq.com/doc/offiaccount/Account_Management/Generating_a_Parametric_QR_Code.html
				// 暂时前往手机端授权
				// 显示关注公众号
				// 获取 appid
				if(!vueApp.push.wxAppId) this.getPushSettingExt();
				if(!vueApp.push.wxAppId) this.error('微信推送开启失败: appid');
				// const url2 = HOST + `/pages/frontend/afterauth.html?appid=${vueApp.push.wxAppId}&socialType=zzy&app=vipPc`
				let url2 = HOST + `/mobile-vipcenter/home.html?f=1&token=${token}&state=zzy`;
				zzWxOpen(url2);
			},
			saveUserSelfOrCompany(para, isSelf = true) {
        const that = this
        if (isSelf) {
          para = JSON.stringify(para)
          that.post('/memberInfo/save', para, {
            async: false,
            contentType: 'application/json'
          }, data => {
            checkRet(data);
            that.getUserInfo();
          })
        } else {
          that.post('/userCompany/updateZzyUserCompanyInfoById', para, {
              async: false,
            }, data => {
              checkRet(data);
              that.getUserInfo();
            },
          );
        }
			},
			getUserInfo() {
				const that = this
				that.post('/zzyUser/selectZzyUserInfoById',{
						zzyUserId,
						orgNum,
					},{
						async: false
					}, data => {
						checkRet(data)
						// if (data.state !== 1) {
						// 	layerMsg('登录超时，请重新登录', 2, false);
						// 	top.location.href = variable + '/pages/frontend/zzy-denglu.html';
						// }
						that.userInfo = data.data.zzyUserBaseVo || {};
						vueApp.user.base = that.userInfo;
						vueApp.setUserSelf(data.data.memberInfo || {});
						vueApp.setUserCompany(data.data.zzyUserCompanyInfoVo || {});
						vueApp.setEnterprisePerson(data.data.enterprisePerson || {});
					},
				);
			},
			getUserSetting() {
				const that = this
				that.post('/exhibitSetting/selectZzyUserInfoShowField',{
					},{
					}, data => {
						checkRet(data)
						vueApp.setUserSetting(data.data || []);
					},
				);
			},
			getCompanySetting() {
				const that = this
				that.post('/sysSettingOrg/getValue',{
						orgNum,
						settingCode: 'allowModCompanyName'
					},{
					}, data => {
						checkRet(data)
						vueApp.companyNameDisable = (data.data || {}).allowModCompanyName === 'false';
					},
				);
			},
			saveUserAvatar(pic) {
				// $("#js_f_headImage").attr('src',pic);
				this.saveUserInfoSync({
					headImage: pic,
				})
			},
			saveUserBase(isName) {
				this.saveUserInfoSync(isName ? {
					name: $('.jsf-userBase input[name=f_name]').val(),
				} : {
					username: $('.jsf-userBase input[name=f_user_name]').val(),
				})
			},
			checkEmail(obj) {
				var p2   = /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i;
				var para = $(obj).val();
				if (para && !(p2.test(para))) {
					layer.tips('电子邮箱格式错误!','#jsf_f_email');
					throw new Error('电子邮箱格式错误!');
				}
			},
			saveUserBind(isMobile) {
				var p1   = /^1\d{10}$/;
				var p2   = /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i;
				var para = '';
				if(isMobile) {
					para = $('.jsf-userBind input[name=f_mobile]').val();
					if (!(p1.test(para))){
						layer.tips('手机格式错误!','#jsf_f_mobile');
						throw new Error('手机格式错误!');
					}
				} else {
					para = $('.jsf-userBind input[name=f_email]').val();
					if (!(p2.test(para))) {
						layer.tips('电子邮箱格式错误!','#jsf_f_email');
						throw new Error('电子邮箱格式错误!');
					}
				}
				this.saveUserInfoSync(isMobile ? {
					mobile: para,
				} : {
					email: para,
				})
			},
			saveUserInfoSync(paras) {
				// // newPassWord,
				// // confirmPassWord,
				// // username
				// // name
				// // mobile
				// // email
				// // headImage
				paras.zzyUserId = zzyUserId
				paras.code = $('#jsf_code').val();
				const that = this
				util.post(
					API + '/zzyUser/updateZzyuser', paras ,{
						async: false,
					}, function(data) {
						checkRet(data);
						layerMsg('修改成功', 1, false);
						that.getUserInfo();
					},
				);
			},
			checkRet(apiReturn, errMsg = '请求出错!', sucMsg='') {
				const {state, msg, data} = apiReturn
				if(state !== 1) {
					layerMsg(msg || errMsg, 2, !!errMsg)
				} else {
					if(sucMsg) layerMsg(sucMsg, 1, false);
				}
			},
			post(url, data, options, cb, cbErr) { // bug 异常抛不出
				const df = {
					async: false,
					type: "post",
					url : API + url,
					data,
					dataType: "json",
					// xhrFields: {
					// 	withCredentials: true
					// },
					...options,
					success: cb || function (rsp) { // 异常抛不出
					 console.log("rsp", rsp);
					},
					error: cbErr || function (data,type, err) {
					 console.log("error", data , type , err);
					 layerMsg('请求失败')
					},
				};
				return $.ajax(df);
				// return $.ajax(df)
				// .done(cb || function (rsp) {
				// 	console.log("rsp", rsp);
				// })
				// .fail(cbErr || function (data,type, err) {
				// 	console.log("error", data , type , err);
				// 	layerMsg('请求失败')
				// });
  		},
			error(msg, icon=2,throws = true) {
				layerMsg(msg, icon, throws)
			},
		}

		function getVipInfor() {
			$.ajax({
				url: variableBus + '/zzyUser/selectOne', //请求服务器url地址.
				type: "POST",
				datatype: 'json',
				success(data) {
					if (data.state == 1) {
						formData = data.data
						vipLevel = data.data.vipLevel
						if (vipLevel != 0) {
							isgetCpInfor = false
						}
						$("#userName").html(data.data.f_user_name)
						$("#mobile").html(data.data.f_mobile)
						$("#email").html(data.data.f_email)
						if (data.data.f_update_time != null) $("#updateTime").html("修改于 " + data.data.f_update_time)
						if (data.data.vipLevel > 0) {
							$("#state").html('已认证')
							$("#applyApprove").html('查看')
							isamend = true
						} else if (data.data.vipLevel == -1) {
							$("#state").html('审核中')
							$("#applyApprove").show()
							$("#applyApprove").html('修改认证')
							isamend = false
						} else if (data.data.vipLevel == 0) {
							$("#state").html('未认证')
							$("#applyApprove").show()
							$("#applyApprove").html('申请认证')
							isamend = false
						}
					} else {
						layerMsg('登录超时，请重新登录', 2, false);
						top.location.href = variableBus + '/pages/frontend/zzy-denglu.html';
					}
				},
				error() {
					layerMsg('修改失败')
				}
			});
		}

		function submitApproves() {
			if(isamend) layerMsg('已认证不可操作');
			var companyName = $("#companyName").val();
			var linkman = $("#linkman").val()
			var tel = $("#tel").val()
			var f_mobile = $("#f_mobile").val()
			var f_email = $("#f_email").val()
			var position = $("#position").val()
			var socialReditCode = $("#socialReditCode").val()
			var businessLicense = $("#businessLicense").val()
			if (!companyName) layerMsg('请填写公司名称！')
			if (!linkman) layerMsg('请填写联系人！')
			if (!tel) layerMsg('请填写联系电话！')
			if (!f_mobile) layerMsg('请填写移动电话！')
			if (!f_email) layerMsg('请填写邮箱！')
			if (!position) layerMsg('请填写职务！')
			if (!socialReditCode){
				layerMsg('请填写统一社会信用代码！')
			} else {
				if (socialReditCode.length > 18) layerMsg('统一社会信用代码不可超过18字符！')
			}
			if (!businessLicense) layerMsg('请上传营业执照！')
			$.ajax({
				url: variableBus + '/zzyUser/identify', //请求服务器url地址.
				type: "POST",
				datatype: 'json',
				data: $("#approveInfoForm").serialize(),
				success(data) {
					if (data.state == 1) {
						layer.msg('提交成功', {
							icon: 6
						});
						getVipInfor()
						layer.close(layertables);
					} else {
						layer.msg(data.msg, {
							icon: 2
						});
					}
				},
				error() {
					layerMsg('修改失败')
				}
			});
		}

		function getPassLevel(pass) {
			if(typeof pass !== 'string' || !pass) return 0;
			var level = 0;
			// pass = String(pass).toLocaleLowerCase();
			// 小写字母，级别+1
			if (/[a-z]/.test(pass)) level++;
			if (/[A-Z]/.test(pass)) level++;
			// 数字+1
			if (/[0-9]/.test(pass)) level++;
			// 其他+1
			if (/[\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass)) level++;
			return level;
		}
		function isPassValid(pass) {
			return !(/[^a-zA-Z0-9\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass));
		}

		function buttonpasswork() {
			let pass = $("#password").val();
			let pwdGrade = getPassLevel(pass);
			if (!pass) {
				layerMsg('请输入密码!!');
			} else if (pass!=$("#password1").val()) {
				layerMsg('两次密码不一致!!');
			} else if (pass.length < 8 || pass.length > 16) {
				layerMsg('密码为8-16位!!');
				// } else if (getPassLevel(pass) < 3) {
					// 	layerMsg('密码至少为8-16位英文字母、数字、字符组合!!');
			}else {
				if(!isPassValid(pass)) layerMsg('密码不能含空格或特殊字符!!');
			}
			pass = hex_md5(pass).toUpperCase();
			$.ajax({
				url: variableBus + '/zzyUser/updateZzyuser',
				type: "POST",
				datatype: 'json',
				data: {
					zzyUserId,
					newPassWord: pass,
					confirmPassWord: pass,
					pwdGrade,
				},
				success(data) {
					if (data.state > 0) {
						layer.msg('修改成功,1秒后刷新', {
							icon: 6
						}, function() {
						});
						layer.close(layertable);
						setTimeout(() => {
							window.location.reload()
						}, 1000);
					} else {
						layerMsg('登录超时，请重新登录');
					}
				},
				error() {
					layerMsg('修改失败');
				}
			});
		}
	</script>
</body>
</html>