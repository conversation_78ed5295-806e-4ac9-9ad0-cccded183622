		.card-title {
			text-align: center;
			line-height: 56px;
			border-bottom: 1px solid #ccc;
		}

		.layui-tab-content {
			padding: 0px;
		}

		.company-vip-info {
			box-sizing: border-box;
			padding: 16px 20px;
		}

		.company-vip-info>ul>li {
			line-height: 30px;
		}

		.company-vip-info>ul>li>label {
			font-size: 14px;
			color: #666666;
			width: 100px;
			display: inline-block;
			text-align: right;
		}

		.company-vip-info>ul>li>span {
			font-size: 14px;
			color: #333333;
		}

		.company-vip-info>ul>li>em {
			font-size: 14px;
			font-style: normal;
			color: #009688;
			margin-left: 10px;
			height: 28px;
			display: inline-block;
			padding: 0 10px;
			cursor: pointer;
		}

		#button-passwork {
			width: 128px;
			height: 34px;
			background-color: #009688;
			border-radius: 4px;
			border: none;
			font-size: 14px;
			color: #fff;
			margin: 186px auto 0;
			display: block;
		}

		.vipApprove {
			width: 523px;
			height: 64px;
			box-sizing: border-box;
			background-color: #fafffe;
			border-radius: 4px;
			border: dashed 1px #009688;
			line-height: 64px;
			padding: 0 14px;
			font-size: 14px;
			color: #666666;
			margin: 20px 0px 0px 46px;
		}

		.vipApprove>p {
			margin: 0;
		}

		#applyApprove {
			color: #009688;
			margin-left: 20px;
			font-style: normal;
			cursor: pointer;
		}

		.layui-form-item {
			width: 48%;
		}

		.upImgDivs {
			width: 200px !important;
			height: 142px !important;
			border: solid 1px #e5e5e5;
			background: url(../../../img/invitation/logo.png) no-repeat center 26px;
			background-size: 42px;
			position: relative;
			float: left;
		}

		.upImgDivs>p {
			font-size: 20px;
			color: #b3b3b3;
			position: absolute;
			top: 70px;
			left: 42px;
		}

		#delCharterPic {
			width: 30px;
			height: 30px;
			background-color: rgba(0, 0, 0, .5);
			display: block;
			position: absolute;
			top: 0;
			right: 0;
			text-align: center;
			line-height: 30px;
		}

		body,.layui-tab-content{
			min-height: 100vh;
			background-color: #f0f2f5 !important;
		}
		input.layui-input:disabled {
			border-color: #eee!important;
			background-color: #FBFBFB!important;
			color: #d2d2d2!important;
		}
	.flex{
		display: flex;
	}
  .flex-default {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  .flex-float{
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .flex-w{
    flex-direction: row;
  }
  .flex-h{
    flex-direction: column;
  }
  .flex-center-w{
    display: flex;
    justify-content: center;
  }
  .flex-center-h{
      display: flex;
      align-items: center;
  }
  .flex-center{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .flex-between{
    display: flex;
    flex-flow:  row nowrap;
    justify-content: space-between;
  }
  .flex-svg-center{
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    flex-direction: column;
  }
	::-webkit-scrollbar-track-piece {
		background-color: #f8f8f8;
	}
	::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	::-webkit-scrollbar-thumb {
		background-color: #dddddd;
		background-clip: padding-box;
		min-height: 28px;
		border-radius: 5px;
	}
	::-webkit-scrollbar-thumb:hover {
		background-color: #bbb;
	}

	.page-main {
		background: #ffffff;
		border-radius: 2px;margin: 2.5vw 4.5vw 0 3.5vw;
		/* padding:2.1557vw 1.7964vw; */
	}
	.page-main li {
		display: flex;
    flex-flow:  row nowrap;
		color: rgba(0,0,0,0.65);
		line-height: 1.4371vw;
		align-items: center;
    justify-content: space-between;
	}
	.page-main li+li {
		border-top: 1px solid #e9e9e9;
		list-style: none;
		/* padding: .7186vw 0 .7186vw; */
	}
	.page-main a {
		color: #1890FF;
	}

		.jsf .layui-layer-btn- {
			background-color: #f8f8f8;
    	padding-top: 5px;
		}
		.jsf .layui-layer-content {
			overflow: hidden !important;
		}
		.jsf .layui-form-label {
			text-align: left;
			box-sizing: border-box;
		}
		.hide,[v-cloak] {
			display: none !important;
		}
		.main-left,.main-left * {
			box-sizing: border-box;
		}
		.main-left {
			width: 330px;padding: 35px 13px 35px 27px;
			border-right: 1px solid #e9e9e9;
		}
		.main-left>a:hover,.main-left>a.hover{
			background-color: #F5F6F8;
		}
		.main-left>a img{
			height: 1.5625vw;
		}
			.left-nav-1 {
				background-image: url(../img/base.png);
			}
			.left-nav-2 {
				background-image: url(../img/self.png);
			}
			.left-nav-3 {
				background-image: url(../img/company.png);
			}
		.main-left>a{
			width: 100%;
			font-size: 18px;
			line-height: 30px;
			color: #000000D9;
			display: inline-block;
			border-radius: 6px;
			padding: 1vw 2vw 1vw 46px;
			background-repeat: no-repeat;
			background-size: auto 18px;
			background-position: 17px center;
			margin-top: 8px;
		}
		.main-title {
			color: #000000D9;
		}
		.main-ul,.main-ul * {
			box-sizing: border-box;
		}
		.main-ul {
			flex: 1;padding-right: 33.35px;
		}
      .main-block>.el-link i {
        color: #1890FF;
      }
      .main-block>.el-link {
        color: #666666FF;
        font-size: 16px;
      }
      .main-block>a {
        font-size: 18px;
        line-height: 30px;
        color: #000000D9;
      }
      .main-block+.main-block {
        border-top: 1px solid #e9e9e9;
      }
      .main-block {
        padding: 20px 0;margin-right: 2.6042vw;
      }
		.main-ul li{
			padding: 1.0417vw 2.0833vw 1.0417vw 0;
		}
		.avatar-uploader {
			/* border: 1px dashed #d9d9d9; */
			/* border-radius: 6px; */
			cursor: pointer;
			position: relative;
			overflow: hidden;
		}
		.avatar-uploader .el-avatar:after {
			transition: all .3s;
			content: '';
			background-color: transparent;
			position: absolute;
			top: 0;left: 0;
			width: 104px;
			height: 104px;
			border-radius: 50%;
		}
		.avatar-uploader:hover .el-avatar:after {
			content: '修 改';
			font-size: 20px;
			background-color: #33333355;
		}
    .formSelf .el-input input {
      border-radius: 2px;
    }
    .formSelf .el-form-item__content>div {
      width: 100%;
    }
    .formSelf .el-input {
      padding-left: 0;border: none;
    }

    .formSelf .el-form-item {
      display: flex;
      flex-direction: column;
      width: 20.8333vw;
      margin-bottom: 10px;
    }
    .formSelf .el-form-item__content {
      margin-left: 0;
    }
    .formSelf .el-form-item__label {
      text-align: left;
    }
    /* .formSelf .upload-img:hover:after {
      background-color: #ccc;
    }
    .formSelf .upload-img:after {
      content: '+';
      background-color: transparent;
      position: absolute;top:50px;left: 0;
      width: 100%;
      transform: scale(3);
      text-align: center;
      font-size: 30px;
    } */
    .cursor {
      cursor: pointer;
    }
    .formSelf .upload-tip:after{
      content: '+';
      position: absolute;top:50px;left: 0;
      width: 100%;
      transform: scale(3);
      text-align: center;
      font-size: 30px;
    }
    .formSelf .upload-tip{
      position: relative;
      overflow: hidden;
      color: #cccccc;font-size: 18px;padding-top: 120px;
			line-height: 20px;text-align: center;
    }
    .box-uploader .upload-img {
      position: relative;
    }
    .box-uploader .upload-img:after {
			transition: all .3s;
			content: '';
      padding-top: 75px;
      box-sizing: border-box;
      text-align: center;
      color: white;
			background-color: transparent;
			position: absolute;
			top: 0;left: 0;width: 100%;height: 100%;
			/* border-radius: 50%; */
		}
		/* .box-uploader .upload-img:after { */
		.box-uploader:hover .upload-img:after {
			content: '修 改';
			font-size: 35px;
			background-color: #33333355;
		}