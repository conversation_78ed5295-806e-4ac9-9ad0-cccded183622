<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title> ... </title>
	<link rel="stylesheet" href="/eip-app-vip/css/corpReset.css"/>
  <script src="../../js/jquery-1.11.1.min.js"></script>
  <link href="../vue/element-ui/index.css" rel="stylesheet">
  <script src="../vue/vue.js"></script>
  <!-- <script src="../vue/dialogDrag.js"></script> -->
  <script src="../vue/element-ui/index.js"></script>
  <script src="../vue/axions.js"></script>

  <script src="../../js/common/fixAjax.js?v=240105"></script>
  <script src="/eip-app-vip/js/L.js"></script>

  <script src="/eip-app-vip/pages/vue/httpVueLoader.js"></script>
  <script src="/eip-app-vip/pages/companyCenter/js/corpVueHelperSrc.js"></script>
  <script src="../../js/common/_vipUtil.js"></script>
	<script>
		var uinfo = util.checkLogin();
		var token = uinfo.token;
    var pid = 1837;
    var qs = 'token='+ token + '&l=' + _lang;
		document.title = L('menuv.我的企业');
	</script>
 <style type="text/css">
  body, html, #app {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
  }
  .text-over-1{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }
  .text-over-2 {
    overflow : hidden;
    text-overflow: ellipsis;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .ex-table-img {
    width: 100%;
    /* height: 170px; */
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: end;
  }
  .page_header {
    height: 106px;
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: space-between;
    padding: 0 30px;
  }
  .page_title {
    align-self: center;
    font-size: 18px;
    color: #333333;
    margin-top: 18px;
  }

  .app_main, .app_foot {
    background-color: #f5f5f5;
  }
  .app_main {
    padding: 20px 100px;
  }
  .app_main_content {
    display: grid;
    grid-template-columns: repeat(auto-fill, 285px);
    grid-row-gap: 30px;
    grid-column-gap: 32px;
  }
  .app_main_content_item {
    background-color: #fff;
    border: solid 1px #eaeaea;
  }
  .app_main_content_item:hover {
    outline: 1px solid #666;
    /* cursor: pointer; */
  }
  .app_main_content_item {
    position: relative;
  }
  .app_main_content_item .item-hover{
    position: absolute;left: 0;top: 0;right: 0;bottom: 0;
    background-color: rgba(0,0,0,0.5);
    display: none;
  }
  .app_main_content_item.roles {
    outline: none !important;
  }
  .app_main_content_item.roles:hover .item-hover{
    display: block;
  }
  </style>
</head>
<body>
  <el-container id="app" v-cloak>
    <div class="mescroll" id="mescroll" style="display: flex;background-color: #f5f5f5;width: 100%;
    flex-direction: column;min-height: calc(100vh - 3px);">
      <!-- <div style="flex: 1;display: flex;flex-direction: column;"> -->
      <div>
        <el-header height="106px" class="page_header">
          <h1 class="page_title">{{ L('menuv.我的企业') }}</h1>
        </el-header>
        <el-main class="app_main">
          <!-- 无限滚动 -->
          <!-- infinite-scroll-disabled="disabled" -->
          <ul class="app_main_content"
            v-infinite-scroll="getPageNext"
            style="overflow: auto;list-style: none;"
            v-loading="loadingTmp"
            style="min-height: 400px;"
            infinite-scroll-distance="20"
          >
            <li v-for="(it,idx) in data" :key="idx" class="app_main_content_item">
              <div @click="goDetail(it.enterpriseId)" style="cursor: pointer;">
                <div class="ex-table-img">
                  <el-image
                    style="width: 100%; height: 260px;"
                    class="cursor"
                    @click="_ => document.body.style=''"
                    fit="cover"
                    :src="it.goodsImgUrl || './img/goods_df.png'"></el-image>
                </div>
                <div  style="padding: 10px;flex: 1;height: 40px;">
                  <b class="text-over-2" style="overflow:hidden;font-weight: normal;text-align: center;">{{ it.companyName || '' }}</b>
                </div>
              </div>
            </li>
            <li class="app_main_content_item">
              <div @click="add()" style="cursor: pointer;height: 300px;">
                <div class="-ex-table-img" style="line-height: 300px;text-align: center;font-size: 30px;">
                  <i slot="default" class="el-icon-plus"></i>
                </div>
              </div>
            </li>
          </ul>
        </el-main>
      </div>
    </div>
  </el-container>
  <script>
    var _url = (new URL(window.location.href)).searchParams
    var Axios = axios.create({
      baseURL: '/eip-web-vip'
    });
    var cid = uinfo.cid;
    var pinfo = util.getPinfo(pid);
    // var layer = layui.layer;
    // var _loading = layer.load(3, {
    // 	shade: [0.2],
    // 	time: 0,
    // });
    // var _loadingTimer = setTimeout(() => {
    // 	layer.msg('请求超时, 请稍后重试!', {icon: 2,zIndex: 20220802,anim: 6})
    // 	layer.close(_loading);
    // }, 30000);
    window.closeLoading = () => {
    // 	setTimeout(() => {
    // 		layer.close(_loading);
    // 		clearTimeout(_loadingTimer);
    // 	}, 500);
    }
    var app = new Vue({
      el: '#app',
      data: {
        mescroll: '',
        detail: {},
        data: [],
        page: 0,
        size: 4,
        total: 999,
        pid,
        loadingTmp: true,
        task: {
          show: false,
          finish: 0,
          total: 0,
        }
      },
      computed: {
        noMore () {
          return this.total <= this.page* this.size;
        },
      },
      mixins: [ZzMixin],
      methods: {
        async init() {
          const that = this;
          window.closeLoading();
        },
        async fresh() {
          this.getPageOne();
        },
        // showDetail(id) {
        //   // const url = window.origin + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/vip-subpages/score-goods-detail.html?view=1&p='+ pid + '&id=' + id;
        //   const url = window.origin + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/vip-subpages/score-goods-detail.html?view=1&token='+ token + '&id=' + id;
        //   window.open(url, '', 'width=400, height=700, left=500, top=200,location=0,resizable=0,scrollbars=0');
        // },
        // goScoreDetail(id) {
        //   const search =  location.search ? location.search + (id ? '&id='+id
        //    : '') : '';
        //   location.href = 'my-score-detail.html' + search
        // },
        add() {

        },
        goDetail(cpid) {
          if(!cpid) return;
          window.open(location.origin + '/eip-app-vip/main/corp.html?' + qs + '&cp'+ cpid)
        },
        getPageOne() {
          this.page = 1
          this.getPageData()
        },
        getPageNext() {
          this.page ++;
          this.getPageData(true);
        },
        async getPageData(add = false) {
          this.loadingTmp = true
          // const {data} = await Axios
          // .post('/enterprise/getMemberEnterprise', this.obj2fd({
          //   // page: this.page,
          //   // rows: this.size,
          //   // f_zzyuser_id: uinfo.zzyUserId,
          //   brandProjectId: 2824,
          // }))
          // .catch(_=> {
          //   this.loadingTmp = false;
          //   this.error();
          // })
          const data = {
            "state": 1,
            "msg": "",
            "data": [
                {
                    "enterpriseId": "3612693193650345857",//企业Id
                    "orgNum": 1002,//机构号
                    "brandId": 2824,//品牌项目Id
                    "companyName": "吉林新和盛齐科技有限公司", //会员企业名称
                    "companyNameEn": null,//会员企业名称 英文
                    "companyNameAbbr": null,//会员企业简称
                    "companyNameAbbrEn": null,//会员企业简称 英文
                    "companyTel": null,//电话
                    "companyLogo": null,//会员企业Logo
                    "companyEmail": null,//邮箱
                    "companyProfile": null,//简介
                    "companyProfileEn": null,//简介 英文
                    "businessLicense": null,//营业执照
                    "socialReditCode": null,//社会信用代码
                    "country": null,//国家
                    "province": null,//省
                    "city": null,//市
                    "district": null,//区县
                    "mainProduct": null,//主营产品
                    "companyPic": null,//公司图片
                    "createTime": "2025-03-31 16:15:21",
                    "updateTime": "2025-03-31 16:15:21",
                    "tradeId": null,//行业Id
                    "tradeIdEn": null,//行业Id 英文
                    "certificationState": 0,//企业是否认证 0 未认证 1 已认证
                    "certificationTime": null,//认证时间
                    "brandName": "展览软件服务品牌",//品牌名称
                    "zzyUserId": null //会员Id
                }
            ],
            "total": "0",
            "rows": null
          };
          try{
            this.loadingTmp = false;
            this.checkRet(data);
            this.data = data.data || [];
            this.total = this.data.length;
            // const tmp = data.rows || []
            // this.data = add ? this.data.concat(tmp) : tmp
            // this.total = +data.total || 0;
          }catch(e) {
            throw e
          }finally{
            this.loadingTmp = false;
          }
        },
      }
    });
    app.init();
  </script>
</body>
</html>