<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title> ... </title>
	<link href="../vue/element-ui/index.css" rel="stylesheet" type="text/css"/>
	<!-- <script src="../../js/common/variable.js"></script> -->
	<script src="../../js/jquery-1.11.1.min.js"></script>
  <link rel="stylesheet" href="/eip-web-vip/lib/layui/css/layui.css" />
  <script src="/eip-web-vip/lib/layui/layui.all.js"></script>
  <script>
    function layerMsg(msg = '',icon=2) {
      layer.msg(msg,{ icon })
    }
    var layer = layui.layer;
    var _loading = layer.load(3, {
      shade: [0.2],
      time: 0,
    });
    var _loadingTimer = setTimeout(() => {
      layer.msg('请求超时, 请稍后重试!', {icon: 2,zIndex: 20220802,anim: 6})
      layer.close(_loading);
    }, 30000);
    window.closeLoading = () => {
      setTimeout(() => {
        layer.close(_loading);
        clearTimeout(_loadingTimer);
      }, 500);
    }
  </script>

	<script src="../vue/vue.js"></script>
	<script src="../vue/axions.js"></script>
	<script src="../vue/element-ui/index.js"></script>
	<!-- <script src="../vue/wangEditor.min.js"></script> -->
	<link rel="stylesheet" href="../../css/member-theme.css?v=20211103">
	<script src="../../js/member-theme.js?v=230629"></script>
	<script src="../../js/common/fixAjax.js"></script>
	<script src="/eip-app-vip/js/L.js"></script>
	<script src="../../js/common/_vipUtil.js?v=231113"></script>
	<script>
		var uinfo = util.checkLogin();
		var token  = uinfo.token;
    var orgNum = uinfo.orgnum;
    var pid    = 1837;
    var qs     = 'token=' + token + '&p=' + pid + '&l=' + _lang;
    var cpid   = util.get('cp') || util.get('cpid');;
		document.title = L('menuv.积分明细');
	</script>
    <style>
      [v-cloak]{
          display: none;
      }
      .flex{
          display: flex;
      }
      .flex-float{
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
      }
      .flex-w{
            flex-direction: row;
          }
          .flex-h{
            flex-direction: column;
          }
      .flex-center-h{
          display: flex;
          justify-content: center;
      }
      .flex-center-y{
          display: flex;
          align-items: center;
      }
        .flex-center-w{
            display: flex;
            align-items: center;
        }
          .flex-center-x{
            display: flex;
            align-items: center;
        }
      .flex-center{
          display: flex;
          align-items: center;
          justify-content: center;
      }
      .flex-between{
          display: flex;
          flex-flow:  row nowrap;
          justify-content: space-between;
      }
      .flex-svg-center{
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        flex-direction: column;
      }

    </style>
    <style>
    .empty:after{
      content: '暂无积分收支明细';
      position: absolute;
      top: 65%;left: 0;right: 0;
      text-align: center;
      color: #A8A8A8;
      font-size: .9569vw;
    }
    .empty{
      background: white url(../../img/empty_record.png) center 35% no-repeat;
      background-size: auto 50%;
      /* margin: 0 4.2667vw 2.6667vw; */
      display: block;
      /* border-radius: .5981vw; */
      /* box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.08); */
      color: #A8A8A8 !important;
      flex: 1;
      position: relative;
    }
    </style>
  </head>
  <body style="background-color: #f0f2f5;overflow: hidden;">
    <div class="flex flex-h" style="height: 100vh;">
      <div class="flex-between" style="line-height: 54px;padding: 0 20px;height: 54px;background-color: white;border-bottom: 1px solid #e9e9e9;">
        <div style="color: #009688;">
          <span class="layui-breadcrumb">
            <a href="javascript:;" onclick="history.go(-1)">我的积分</a>
            <a><cite class="taskName">查看积分明细</cite></a>
          </span>
        </div>
        <div onclick="history.go(-1)" style="cursor:pointer;">
          <img src="../../img/goback.png" alt="" style="display: inline-block;
          margin-right: 5px;
          margin-top: -3px;">
          <span>返回上级</span>
        </div>
      </div>
      <div class="layui-container" style="flex: 1;height: calc(100vh - 114px);box-sizing: border-box;
        margin: 24px 20px 10px;padding: 20px;
        background-color: white;width: calc(100% - 20px);">
        <div id="app" v-cloak>
          筛选: &nbsp;&nbsp;
          <el-select size="mini" v-model="integralTypeId"
            placeholder="积分类型"
            @change="freshTable"
            clearable
            @clear="this.integralTypeId = ''"
          >
            <el-option
              v-for="item in userScores"
              :key="item.integralTypeId"
              :label="item.integralTypeName"
              :value="item.integralTypeId">
            </el-option>
          </el-select>
          <div class="empty" v-if="showEmpty" style="height: calc( 100vh - 160px);">
          </div>
        </div>
        <table id="demo" lay-filter="test"></table>
      </div>
    </div>
    <script type="text/html" id="operatorTypeTpl">
      {{ ['','发放','兑换','积分发放回退','商品兑换回退'][d.operatorType] || '' }}
    </script>
    <script>
      var _url = (new URL(window.location.href)).searchParams
      var Axios = axios.create({
        baseURL: '/eip-web-business'
      });
      // var ZzMixin = {
      //   data() {
      //     return {
      //       show: false,
      //       loading: true, // 自行初始化后改为 false
      //       total: 0,
      //       size: 20,
      //       page: 1,
      //       data: [],
      //       debounceTimer:null, // 防抖器
      //     }
      //   },
      //   methods: {
      //     debounceMethods(func,...args){
      //       console.log('click');
      //       let context = this;
      //       let callNow = !this.debounceTimer;    //是否立即执行
      //       if (this.debounceTimer) clearTimeout(this.debounceTimer);
      //       this.debounceTimer = setTimeout(() => {
      //         this.debounceTimer = null;
      //       },500)
      //       if(callNow){
      //         func.apply(context,args)
      //       }else{
      //         console.log('操作过快 ! 已阻止 !')
      //       }
      //     },
      //     fmtMoney(val,pos=2) {
      //       return (+val || 0).toFixed(pos);
      //     },
      //     obj2qs(o) {
      //       o = o || {}
      //       return JSON.stringify(o).replace(/:/g, '=').replace(/,/g, '&').replace(/{/g, '?').replace(/}/g, '').replace(/"/g, '')
      //     },
      //     obj2fd(obj) {
      //       if (typeof obj !== 'object') return;
      //       const formData = new FormData();
      //       Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      //       return formData;
      //     },
      //     errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      //       return layer.msg(msg, {icon: 2})
      //     },
      //     error(msg, throws = true) { //  用element的 $alert
      //       msg = msg || '请求失败';
      //       const p = this.errorMsg(msg, '提醒', 'warning');
      //       if(throws) throw new Error(msg)
      //       return p;
      //     },
      //     ok(message) {
      //       message = message || '操作成功!';
      //       return layer.msg(message,{icon: 1},);
      //     },
      //     checkRet(obj, errMsg='',okMsg='') {
      //       const {state,msg,data} = obj
      //       if(state !== 1) this.error(errMsg || msg)
      //       if(okMsg) this.ok(okMsg=== true ? '' : okMsg);
      //     },
      //     sizeChange(size) {
      //       this.page = 1;
      //       this.size = size;
      //       this.getPageData();
      //     },
      //     pageChange(page) {
      //       this.page = page
      //       this.getPageData()
      //     },
      //     async getPageData() {
      //       // TODO
      //     },
      //   },
      // }
      function GetQueryString(name) {
        // var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        // var r = window.location.search.substr(1).match(reg);
        // return r != null ? unescape(r[2]) : null;
        return _url.get(name) || '';
      }
      function getQueryString(name) {
        return GetQueryString(name);
      }
      var cid = uinfo.cid;// _url.get("cid");
      // var pid = _url.get("pid");
      var pinfo = util.getPinfo(pid);
      var exhibitCode = _url.get("exhibitCode") || '';
      var integralTypeId  = _url.get("id") || ''; // _url.get("integralTypeId") || '';
      var zzyUserId  = uinfo.zzyUserId;// _url.get("zzyuserId");
    </script>
    <script>
    var app;
    var element = layui.element;
    var form = layui.form;
    var table = layui.table;
    var index;
    var mainTable;
    function initRender(){
      element.render(); // 'breadcrumb');
      form.render();
      mainTable = table.render({
        elem: '#demo'
        // ,toolbar: '#mainTableToolBar'
        // ,height: 312
        ,skin: 'line'
        ,height: 'full-180'
        ,loading: true
        ,headers: getAjaxAuth()
        ,url: '/eip-web-business/integralDetailLog/selectMyIntegralDetail' //数据接口
        ,page: true //开启分页
        ,cols: [[   //表头
          {type: 'checkbox',width:60,LAY_CHECKED: false, }
          ,{field: 'projectName',width: 220, title: '项目', }
          ,{field: 'operatorType', title: '操作类别',width: 180,align:'center',templet: '#operatorTypeTpl'}
          ,{field: 'channelName', title: '积分渠道',width: 180,align:'center',}
          ,{field: 'integralNum', title: '积分变化',width: 120,align: 'right',}
          ,{field: 'integralTypeName', title: '积分类型',width: 120 }
          ,{field: 'updateName', title: '修改人',width: 120,}
          ,{field: 'updateTime',width: 180, title: '积分变动时间',}
          ,{field: 'summaryInfo', minWidth:300, title: '摘要',}
        ]],
        where: {
          projectId: pid,
          zzyUserId,
          integralTypeId,
        },
        method: 'post',
        request: { pageName: 'page',limitName: 'rows' },
        // size: 'sm',
        page : {
          limits: [10,20,30],
          limit: 20,
          layout:['prev','page','next','skip','limit','count','refresh'],
        },
        // done(res, curr, count){
        //   if(res.state == 1 && curr ==1 && (!res.rows || res.rows.length<1)) {
        //     app.showEmpty = true;
        //   }
        // },
        response: {
          statusName: 'state',
          statusCode: 1,
          countName: 'total',
          dataName: 'rows',
        },
      });
      $('body').on("click","tr",function(obj) {
        if(!$(obj.currentTarget).hasClass('layui-table-click')) {
          // var obj = event ? event.target : event.srcElement;
          // var tag = obj.tagName;
          var checkbox = $(this).find(".laytable-cell-checkbox i,.laytable-cell-radio i");
          if(checkbox.length) checkbox.eq(0).click();
        }
      });

    }

    $(function() {

      $(".panel-loading").hide();
      initRender();
      app = new Vue({
        el: '#app',
        data: {
          integralTypeId,
          showEmpty: false,
          userScores: [],
          // confirm: f_confirm,
          // taskName,
          // confirm_memo: '',
          // completion_inst: '', // 填写提示
          // upload_inst: '', // 附件提示
          // explain_attachment: false, // 填写提示文件
        },
        // computed: {
        //   confirmTip() {
        //     if(this.state === 0) return '中止';
        //     return ['未开启','未填报','存为草稿','已提交','已审核'
        //     ,'','','','','已驳回'][this.confirm] || '';
        //   },
        //   isTip() {
        //     let flag = !!this.explain_attachment
        //     if(!flag && this.completion_inst) {
        //       flag = !!$(this.completion_inst).text().trim()
        //     }
        //     return flag
        //     // return this.completion_inst || this.explain_attachment
        //   },
        // },
        mixins: [ZzMixin],
        methods: {
          async init() {
            await this.getUserScores();
            window.closeLoading();
          },
          freshTable(){
            mainTable.config.where.integralTypeId = this.integralTypeId
            mainTable.reload({
              where: mainTable.config.where,
              page: { curr: 1 },
            })
          },
          async getUserScores() {
            const {data} = await Axios
              .post('integralDetailLog/queryMyIntegral', this.obj2fd({
                zzyUserId,
                projectId: pid,
              }))
              .catch(_=> this.error());
            this.checkRet(data);
            this.userScores = data.data || [];
          },
        }
      });
      app.init();
    })
    </script>
  </body>
  </html>