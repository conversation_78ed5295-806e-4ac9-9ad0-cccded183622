<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>展会详情</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" href="../css/vip.css" >
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<script>
	layui.config({
		base : '../js/',
		version : '',
		rainbow : 'rainbow',
	}).extend({
		'picView':'picView',
	}).use('picView');
	$(() => {
		// 单图片预览
		var imgi;
		$('body').on('click','img.js-view',function(){
			imgi && layer.close(imgi);
			layui.picView(this,{
				id:'js-layui-img-view'
				,shade:0.1 //0-1
				,anim: 5 //0-6
				,closeBtn: 2 //0-2
			},function(i){
				imgi = i;
			});
		});
		$('body').on('blur','img.js-view',function(){
			imgi && layer.close(imgi);
		});
		// window.imgView = function(obj) {
		// 	$(this).parent().prev('img').click()
		// }
		// 单图片预览 end
	})
	</script>
	<!-- <script src="../js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<link rel="stylesheet" href="./vue/element-ui/index.css" >
	<script src="./vue/element-ui/index.js"></script>
	<script src="./vue/wangEditor.min.js"></script>
	<script>var E = window.wangEditor;</script>

	<link rel="stylesheet" type="text/css" href="../css/cropper.min.css" />
	<link rel="stylesheet" type="text/css" href="../css/ImgCropping.css" />
	<script src="../js/cropper.min.js"></script>

	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
	</script>
</head>
<body class="exhiDetail flex">
<el-container id="app" v-cloak>
	<el-header height="auto" class="page_header flex-float">
		<el-image :src="detail.projectPicUrl" fit="none" style="width: 164px;height: 98px;margin-right: 32px;">
			<div slot="error" class="image-slot errorImg" >
				<img src="../img/newvip/logo.png" alt="">
			</div>
		</el-image>
		<div style="flex: 1;">
			<div class="flex-between">
				<div class="flex-center-h">
					<h1 style="font-size: 18px;line-height: 25px;color: black;">{{ detail.f_project_name }} </h1>
					<el-link type="primary" icon="el-icon-edit" :underline="false" @click="edit" :style="{
						margin: '0 30px 0 20px',
					}">编辑</el-link>
					<div class="flex-center-h" v-if="!detail.f_project_state">
						<span class="state state1"></span>
						<span>项目审核中</span>
					</div>
				</div>
				<div>
					<!-- <el-dropdown @command="selectCommand"> -->
						<el-link :underline="false" @click="copySiteLink">
							推广展会
							<!-- <i class="el-icon-arrow-down el-icon--right"></i> -->
						</el-link>
						&nbsp;&nbsp;
						<!-- <el-dropdown-menu slot="dropdown">
							<el-dropdown-item command="test">test</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown> -->
					<el-button type="primary" size="mini" v-if="!detail.publishFlag" @click="release(1)">发布展会</el-button>
					<el-button type="info" size="mini" plain v-else  @click="release(0)">撤销发布</el-button>
				</div>
			</div>
			<div class="header_main">
				<label for="">展会时间</label>
				<div v-if="detail.f_start_time || detail.f_end_time">{{ detail.f_start_time | timeFormat('/') }} 至 {{ detail.f_end_time | timeFormat('/') }} </div>
				<label for="">行业类别</label>
				<div>{{ detail.tradeName || ''}}</div>
				<label for="">地点</label>
				<div>{{ detail.address || '' }}</div>
			</div>
		</div>
	</el-header>
	<el-main class="app_main">
		<el-tabs v-model="tabMain" @tab-click="tabMainClick" class="tabMain">
			<el-tab-pane label="详情" name="tabDetail">
				<comp-block title="展会图片" main-border>
					<div style="padding: 17px 24px 20px;">
						<input style="display:none" type="file" id="jsLogoUploader" accept="image/*" onchange="selecttype(this)">
						<div class="" style="position: relative;display: flex;">
							<!-- <div class="set-bottom-right">
								<img class="js-view" src="https://pc-index-skin.cdn.bcebos.com/hiphoto/9908095120.jpg?x-bce-process=image/crop,x_131,y_0,w_1643,h_1026" alt="it.picName">
								<div class="more">
									<i class="el-icon-delete-solid" @click="$refs['tipPicDel'].open()"></i>
								</div>
							</div> -->
							<div class="set-bottom-right" v-for="it in detail.projectPics" :key="it.projectPicId">
								<img class="js-view" :src="it.pic" :alt="it.picName">
								<div class="more">
									<!-- <i class="el-icon-zoom-in" onclick="imgView(this)"></i> -->
									<i class="el-icon-delete-solid" @click="sureDelPic(it.projectPicId)"></i>
								</div>
							</div>
							<div v-if="!Array.isArray(detail.projectPics) || detail.projectPics.length<10" class="set-bottom-right upload" id="jsLogoUploaderHelper" onclick="$('#jsLogoUploader').trigger('click')" style="cursor: pointer;margin-right: 20px;">
								<!-- <img v-if="form.imgUrl" :src="form.imgUrl" alt="" class="layui-upload-img" id="logoImg" :alt="form.imgName"> -->
								<!-- <p>上传LOGO图片</p> -->
							</div>
							<!-- <div class="upload-tip color-tip" >推荐上传400*300像素图片<br /> 支持.JPG .JPEG .PNG 格式，<br />大小不超过2M, PNG大小不超过300K</div> -->
						</div>
						<center v-if="!1" class="empty">
							<el-button plain>填写展会详情</el-button>
						</center>
					</div>
				</comp-block>
				<comp-block title="展会详情" main-border>
					<template slot="title">
						<div>
							<div class="title-main">
								展会详情
								<el-link type="primary"  :style="{
									margin: '0 0 0 10px',
								}" icon="el-icon-edit" :underline="!1"
									v-if="detail.projectIntroduce"
								 @click="openEditor(1,'展会详情','projectIntroduce')">编辑</el-link>
							</div>
						</div>
					</template>
					<div class="w-e-text" v-if="detail.projectIntroduce" v-html="detail.projectIntroduce"></div>
					<center class="empty" v-else>
						<el-button plain size="small"
						 @click="openEditor(1,'展会详情','projectIntroduce')">填写展会详情</el-button>
					</center>
				</comp-block>
				<comp-block title="参展范围" main-border>
					<template slot="title">
						<div>
							<div class="title-main">
								参展范围
								<el-link type="primary"  :style="{
									margin: '0 0 0 10px',
								}" icon="el-icon-edit" :underline="!1"
									v-if="detail.exhibitionScope"
									@click="openEditor(2,'参展范围','exhibitionScope')">编辑</el-link>
							</div>
						</div>
					</template>
					<div class="w-e-text" v-if="detail.exhibitionScope" v-html="detail.exhibitionScope"></div>
					<center class="empty" v-else >
						<el-button plain size="small"
						 @click="openEditor(2,'参展范围','exhibitionScope')">填写参展范围</el-button>
					</center>
				</comp-block>
				<comp-block title="展馆信息" main-border>
					<template slot="title">
						<div>
							<div class="title-main">
								展馆信息
								<el-link type="primary"  :style="{
									margin: '0 0 0 10px',
								}" icon="el-icon-edit" :underline="!1"
								v-if="detail.exhibitSectionInfo"
								@click="openEditor(3,'展馆信息','exhibitSectionInfo')">编辑</el-link>
							</div>
						</div>
					</template>
					<div class="w-e-text" v-if="detail.exhibitSectionInfo" v-html="detail.exhibitSectionInfo"></div>
					<center class="empty" v-else >
						<el-button plain size="small"
							@click="openEditor(3,'展馆信息','exhibitSectionInfo')">选择展馆地址</el-button>
					</center>
				</comp-block>
			</el-tab-pane>
			<el-tab-pane label="数据" name="tabData">
				<section>
					敬请期待
				</section>
			</el-tab-pane>
			<el-tab-pane label="日志" name="tabLog">
				<section>
					敬请期待
				</section>
			</el-tab-pane>
		</el-tabs>
	</el-main>
	<!-- <el-footer class="app_foot">

	</el-footer> -->
	<comp-tip ref="tipEditor" :title="editor.title" @sure="sureEditor" type="form" width="650px">
		<div style="height: 350px;box-sizing: border-box;margin-top: 20px;">
			<div ref="jsEditor" ></div>
		</div>
	</comp-tip>
</el-container>
<!-- 上传裁剪相关 -->
<div class="tailoring-container ImgShow" style="display: none;">
	<div class="black-cloth" onclick="closeTailor(this)"></div>
	<div class="tailoring-content">
		<div class="tailoring-content-one">
			<label title="上传图片" for="chooseImg" class="l-btn choose-btn">
				<input type="file" name="file" id="chooseImg" onchange="selectImg(this)" class="hidden" accept="image/*">
				选择图片
			</label>
			<div class="close-tailoring" onclick="closeTailor(this)">×</div>
		</div>
		<div class="tailoring-content-two">
			<div class="tailoring-box-parcel">
				<img id="tailoringImg">
			</div>
			<div class="preview-box-parcel">
				<p>图片预览：</p>
				<div class="square previewImg"></div>
				<div class="circular previewImg"></div>
			</div>
		</div>
		<div class="tailoring-content-three">
			<button class="l-btn cropper-reset-btn">复位</button>
			<button class="l-btn cropper-rotate-btn">旋转</button>
			<button class="l-btn cropper-scaleX-btn">换向</button>
			<button class="l-btn sureCut" id="sureCut">确定</button>
		</div>
	</div>
</div>
<!-- 图片上传 裁剪 -->
<script type="text/javascript">
var gfile;
//弹出框水平垂直居中
(window.onresize = function() {
	var win_height = $(window).height();
	var win_width = $(window).width();
	if (win_width <= 768) {
		$(".tailoring-content").css({
			"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
			"left": 0
		});
	} else {
		$(".tailoring-content").css({
			"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
			"left": (win_width - $(".tailoring-content").outerWidth()) / 2
		});
	}
})();

var companysizes = 4 / 3 //100 / 64.7;
//var logosize = 25 /25;
//cropper图片裁剪
$('#tailoringImg').cropper({
	aspectRatio: companysizes, //默认比例
	preview: '.previewImg', //预览视图
	guides: false, //裁剪框的虚线(九宫格)
	autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
	dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
	movable: true, //是否允许移动剪裁框
	resizable: true, //是否允许改变裁剪框的大小
	zoomable: true, //是否允许缩放图片大小
	mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
	touchDragZoom: true, //是否允许通过触摸移动来缩放图片
	rotatable: true, //是否允许旋转图片
	crop: function(e) {
		// 输出结果数据裁剪图像。
	}
});
//旋转
$(".cropper-rotate-btn").on("click", function() {
	$('#tailoringImg').cropper("rotate", 45);
});
//复位
$(".cropper-reset-btn").on("click", function() {
	$('#tailoringImg').cropper("reset");
});
//换向
var flagX = true;
$(".cropper-scaleX-btn").on("click", function() {
	if (flagX) {
		$('#tailoringImg').cropper("scaleX", -1);
		flagX = false;
	} else {
		$('#tailoringImg').cropper("scaleX", 1);
		flagX = true;
	}
	flagX != flagX;
});
//关闭裁剪框
function closeTailor() {
	$(".tailoring-container").toggle();
}
function dataURLtoFile(dataurl, filename) { //将base64转换为文件
	var arr = dataurl.split(','),
		mime = arr[0].match(/:(.*?);/)[1],
		bstr = atob(arr[1]),
		n = bstr.length,
		u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new File([u8arr], filename, {
		type: mime
	});
}

//图像上传
function selectImg(file) {
	var max_size = 10240;
	var img_size = 1024;
	var fileData = file.files[0];
	file.value = '';
	app.checkFile(fileData.false)
	var size = fileData.size;
	$(".tailoring-box-parcel").show();
	$(".preview-box-parcel").show();
	if (size > max_size * 1024) {
		app.layerMsg("图片大小不能超过10M")
	} else if (size > img_size * 1024) {
		var reader = new FileReader();
		reader.readAsDataURL(fileData)
		reader.onload = function(e) {
			let image = new Image() //新建一个img标签（还没嵌入DOM节点)
			image.src = e.target.result
			image.onload = function() {
				let canvas = document.createElement('canvas'),
					context = canvas.getContext('2d'),
					imageWidth = image.width / 5, //压缩后图片的大小
					imageHeight = image.height / 5,
					data = ''
				canvas.width = imageWidth
				canvas.height = imageHeight
				context.drawImage(image, 0, 0, imageWidth, imageHeight)
				data = canvas.toDataURL('image/jpeg')
				// console.log(data)
				//压缩完成
				// document.getElementById('img').src = data
				$('#tailoringImg').cropper('replace', data, false)
			}
		}
	} else {
		var reader = new FileReader();
		reader.readAsDataURL(fileData)
		reader.onload = function(evt) {
			var replaceSrc = evt.target.result;
			//更换cropper的图片
			$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
			$('#replaceImg').cropper('replace', replaceSrc, false);
		}
	}
}
// 产品图片
function selecttype(e) {
	let file = e.files[0];
	e.value = '';
	app.checkFile(file, false);
	var file_id = file.type || '';
	if(file_id.startsWith('image/')) {
		var max_size = 10240;
		var img_size = 1024;
		var size = file.size;
		$(".ImgShow").toggle();
		if (size > max_size * 1024) {
			app.layerMsg("图片大小不能超过10M");
		} else if (size > img_size * 1024) {
			var reader = new FileReader();
			reader.readAsDataURL(file)
			reader.onload = function(e) {
				let image = new Image() //新建一个img标签（还没嵌入DOM节点)
				image.src = e.target.result
				image.onload = function() {
					let canvas = document.createElement('canvas'),
						context = canvas.getContext('2d'),
						imageWidth = image.width / 5, //压缩后图片的大小
						imageHeight = image.height / 5,
						data = ''
					canvas.width = imageWidth
					canvas.height = imageHeight
					context.drawImage(image, 0, 0, imageWidth, imageHeight)
					data = canvas.toDataURL('image/jpeg')
					//压缩完成
					// document.getElementById('img').src = data
					$('#tailoringImg').cropper('replace', data, false)
				}
			}
		} else {
			var reader = new FileReader();
			reader.readAsDataURL(file)
			reader.onload = function(evt) {
				var replaceSrc = evt.target.result;
				//更换cropper的图片
				$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
				$('#replaceImg').cropper('replace', replaceSrc, false);
				//图像上传
				//alert('图像上传');
			}
		}
		$(".tailoring-box-parcel").show();
	} else if( file_id.startsWith('video/') ){
		app.layerMsg("只能上传图片 !");
		// app.uploadImgOss(file)
	} else {
		// app.layerMsg("只能上传图片或视频 !");
		app.layerMsg("只能上传图片 !");
	}
}
$("#sureCut").on("click", async function() {
	if ($("#tailoringImg").attr("src") == null) return false;
	var cas = $('#tailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
	var base64url = cas.toDataURL()
	var timestamp = new Date().getTime()
	var file = dataURLtoFile(base64url, timestamp + ".jpg");
	const data = await app.uploadImgOss(file, false);
	// app.form.imgUrl = data.path
	// app.form.imgName = data.fileName
	await app.addProjectPic(data.path, data.fileName);
	console.log('upload', data);
	//关闭裁剪框
	$(".ImgShow").hide();
	$(".tailoring-box-parcel").hide();
	$(".preview-box-parcel").hide();
});
</script>

<script src="../js/common/_vipMixin.js?v=20220902"></script>
<script>
$(function(){
	// dataObj : othis,elem,value
	window.selectedCountry2 = async function (dataObj) {
		const val = dataObj.value || ''
		if(val) {
			const data = await app._getProvinces(val, false)
			let html = '<option value="">选择省份</option>';
			data.forEach(it => {
				html += `<option value="${it.f_province}">${it.f_province}</option>`;
			})
			$('select[name=select-province').html(html)
			layui.form.render('select','selectorArea');
			layui.form.on('select(select-province)', function(data) {
				selectedProvince(data)
			});
		}
	}
	// dataObj : othis,elem,value
	window.selectedProvince = async function (dataObj) {
		const val = dataObj.value
		if(val) {
			const data = await app._getAreas(val)
			let html = '<option value="">选择城市</option>';
			data.forEach(it => {
				html += `<option value="${it.f_city_name}">${it.f_city_name}</option>`;
			})
			$('select[name=select-city').html(html);
			layui.form.render('select','selectorArea');
			layui.form.on('select(select-city)', function(data) {
				app.form.city = data.value;
				layer.close(app.layerIdx);
			})
		}
	}
	window.selectIndustry = function() {
		var idx = layer.open({
			type: 1,
			title: '行业分类',
			skin: 'layui-layer-demo',
			area: ['300px', '400px'],
			shadeClose: true,
			content: '<div id="jsTradeTree" class="" style="padding-left: 20px;"></div>',
			cancel() {
				layer.close(idx);
				return false
			},
			success() {
				layui.tree.render({
					id:'tradeId', // 确保唯一
					elem: '#jsTradeTree',
					data: app.tradeTree,
					onlyIconControl: true, //是否仅允许节点左侧图标控制展开收缩
					accordion: true,
					checked: true,
					click: function(obj) {
						app.form.tradeId = obj.data.tradeId
						app.form.tradeName = obj.data.tradeName
						layer.close(idx);
					}
				});
			},
		});
	}
	window.app = new Vue({
		el: '#app',
		mixins: [_vipMixinCommon],
		data: {
			detail: {
				projectPicUrl: '',
				projectPics: [],
			},
			delPicId: 0,
			editor: {
				type: 0,
				title: '',
				detailKey: '',
				ins: '',
			},
			tabMain: 'tabDetail',
		},
		// async mounted() {
		// },
		methods: {
			async init() {
				this._loading()
				this.detail = await this._getProjectInfo(this.pid);
				this.$nextTick(_ => {
					this._loaded()
				})
			},
			copySiteLink() {
				this._copy(hostGate + '/onlineExhibition/details?id=' + this.pid,'推广链接已复制至剪贴板。')
			},
			async sureEditor() {
				if([1,2,3].includes(+this.editor.type)) {
					const content = this.editor.ins.txt.html() || '';
					const params = {f_project_id: this.pid };
					params[this.editor.detailKey] = content;
					// const params = {f_project_id: this.pid,[this.editor.detailKey]: this.detail[this.editor.detailKey]}
					const {data} = await _axios.post('project/updateFieldValue',this.obj2FormData(params))
					this.checkReturn(data);
					// this.detail[this.editor.detailKey] = content
					this.detail = await this._getProjectInfo(this.pid)
					this.$nextTick(_ => {
						this.$refs['tipEditor'].close();
					})
				} else {
					this.alerts('不支持的编辑类型')
				}
			},
			openEditor(type,title, detailKey) {
				this.editor.type = type
				this.editor.title = title
				this.editor.detailKey = detailKey
				this.$refs['tipEditor'].open();
				this.$nextTick(_ => {
					if(!this.editor.ins) {
						this.editor.ins = new E(this.$refs['jsEditor']);
						this._setEditorConfig(this.editor.ins,this.detail[detailKey] || '')
					} else {
						this.editor.ins.txt.html(this.detail[detailKey] || '')
					}
				})
			},
			sureDelPic(projectPicId) {
				this.$confirm('此操作将永久删除该图片, 是否继续?', '提示', {
          type: 'warning'
        }).then(async _ => {
					const {data} = await _axios.post('project/deleteProjectPicById',this.obj2FormData({
						projectPicId,
					}));
					this.checkReturn(data);
					// this.alerts('TODO')
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
					await this.init();
        }).catch( _ => {});
			},
			edit() { // 项目编辑
				// this.openTip('tipEdit')
				this.$confirm('编辑项目将撤销当前发布状态，并需要对项目重新进行审核，是否确认编辑？', '提示', {
          type: 'warning'
        }).then(async _ => {
					await this.changeProjectState({isRelease: false,isAuth: true})
					location.href = 'exhi_set.html?pid=' + this.pid;
        }).catch( _ => {});
			},
			// selectCommand(cmd) {
			// 	// TODO
			// 	this._loading()
			// 	this.alerts('TODO')
			// 	this.$nextTick(_ => {
			// 		this._loaded()
			// 	})
			// },
			release(state) {
				// this.openTip('tipRelease')
				if(state){
					let tip = '';
					if(!this.detail.projectIntroduce) {
						tip += (tip ? ',' : '' ) +'展会详情'
					}
					if(!this.detail.exhibitionScope) {
						tip += (tip ? ',' : '' ) + '参展范围'
					}
					if(!this.detail.exhibitSectionInfo) {
						tip += (tip ? ',' : '' ) + '展馆信息'
					}
					if(tip) {
						this.$confirm('当前'+ tip +'内容未填写，未填写状态下发布将影响您的展会展示，请您完善信息后再发布。', '提示', {
							confirmButtonText: '仍要发布',
							cancelButtonText: '完善内容',
							type: 'warning'
						}).then(() => {
							this.changeProjectState({isRelease: true})
						}).catch( _ => {
						});
					} else {
						this.changeProjectState({isRelease: true})
					}
				} else {
					this.$confirm('是否确定撤销发布 ?', '提示', {
						confirmButtonText: '仍要撤销',
						cancelButtonText: '不小心点错',
						type: 'warning'
					}).then(() => {
						this.changeProjectState({isRelease: false})
					}).catch( _ => {
					});
				}
			},
			tabMainClick() {

			},
			async changeProjectState({isRelease, isAuth}) {
				const params = {
					projectId: this.pid,
					projectPublishFlag: !!isRelease,
				};
				if(typeof isAuth !== 'undefined') {
					params.projectCheckFlag = !!isAuth
				}
				const {data} = await _axios.post('project/updateProjectState', this.obj2FormData(params))
				this.checkReturn(data)
				this.detail = await this._getProjectInfo(this.pid)
				if(isRelease) this.alerts('suc:展会已发布至展之云行业门户网站');
				else this.alerts('suc:已停止在行业网站发布此展会信息。');
			},
			async delProjectPic(projectPicId) {
				alert('TODO')
			},
			async addProjectPic(path,fileName) {
				const {data} = await _axios.post('project/saveProjectPic', this.obj2FormData({
					projectId: this.pid,
					pic: path,
					picName: fileName,
				}))
				this.checkReturn(data)
				this.detail = await this._getProjectInfo(this.pid)
			}
		}
	});
	app.init();
});
</script>
</body>
</html>