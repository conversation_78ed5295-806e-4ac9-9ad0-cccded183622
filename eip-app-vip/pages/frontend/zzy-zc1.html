<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="pragma" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache">
		<meta http-equiv="expires" content="0">
		<title>注册账户</title>
		<link rel="icon" href="../../img/logo2.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/zzy-zhuce.css" />
		<script src="js/variable.js?v=230620" type="text/javascript" charset="utf-8"></script>
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/jquery.params.js" type="text/javascript" charset="utf-8"></script>
		<!--<script src="js/layui.js" type="text/javascript" charset="utf-8"></script>-->
		<!--<script src="js/jquery.form.js" type="text/javascript" charset="utf-8"></script>-->
	</head>
	<body>
		<div class="zc-log">注册账户</div>
		<div class="procedure">
			<div class="position">
				<div class="state-color"><i class="state">1</i>设置账户</div>
				<div><i>2</i>账户验证</div>
				<div><i>3</i>设置密码</div>
				<div><i>4</i>获取结果</div>
			</div>
		</div>
		<from action="zzy-zc2.html" method="get" class="shezhizhanghu">
			<div class="juzhong">
				<div class="phone">
					<p>手机号</p>
					<select name="" class="sel">
						<option value="" selected style="border:none;">手机号注册&nbsp+86</option>
					</select>
					<input type="text" name="phone" maxlength="11" id="phone" style="margin-left: -3px;">

					<!--<input id="btnSendCode" type="button" value="请输入手机号码" onclick="sendMessage()" />-->
				</div>

				<div id="wrapper" style="position:relative; left:80px;">
					<p style="position: absolute;top: 12px;left: -59px; font-family: MicrosoftYaHei;font-size: 16px;color: #333;">验证</p>
					<div id="drag">
						<div class="drag_bg"></div>
						<div class="drag_text slidetounlock" onselectstart="return false;" unselectable="on">
							请按住滑块，拖动到最右边
						</div>
						<!--  默认值0 没有验证通过  -->
						<input type="hidden" name="name" value="0">
						<div class="handler handler_bg"></div>
					</div>
				</div>
				<input type="submit" onclick="test_()" id="" value="下一步" />
				</form>
			</div>
	</body>
	<script type="text/javascript">
		var orgNum = $.query.get("orgNum");
		console.log(orgNum);
		//$(".zhuce").attr('href', 'zzy-zc1.html?orgNum=' + orgNum);
		window.onload = function() {
			//滑动验证
			$.fn.drag = function(options) {
				var x, drag = this,
					isMove = false,
					defaults = {};
				var options = $.extend(defaults, options);
				var handler = drag.find('.handler');
				var drag_bg = drag.find('.drag_bg');
				var text = drag.find('.drag_text');
				var input = drag.find('input');
				var maxWidth = drag.width() - handler.width(); //能滑动的最大间距
				input.val(0);
				//鼠标按下时候的x轴的位置
				handler.mousedown(function(e) {
					isMove = true;
					x = e.pageX - parseInt(handler.css('left'), 10);
				});

				//鼠标指针在上下文移动时，移动距离大于0小于最大间距，滑块x轴位置等于鼠标移动距离
				$(document).mousemove(function(e) {
					var _x = e.pageX - x; // _x = e.pageX - (e.pageX - parseInt(handler.css('left'), 10)) = x
					if (isMove) {
						if (_x > 0 && _x <= maxWidth) {
							handler.css({
								'left': _x
							});
							drag_bg.css({
								'width': _x
							});
						} else if (_x > maxWidth) { //鼠标指针移动距离达到最大时清空事件
							dragOk();
						}
					}
				}).mouseup(function(e) {
					isMove = false;
					var _x = e.pageX - x;
					if (_x < maxWidth) { //鼠标松开时，如果没有达到最大距离位置，滑块就返回初始位置
						handler.css({
							'left': 0
						});
						drag_bg.css({
							'width': 0
						});
					}
				});

				//清空事件
				function dragOk() {
					handler.removeClass('handler_bg').addClass('handler_ok_bg');
					text.removeClass('slidetounlock').text('验证通过').css({
						'color': '#fff'
					});
					input.val(1);

					handler.css({
						'left': maxWidth
					});
					drag_bg.css({
						'width': maxWidth
					});
					handler.unbind('mousedown');
					$(document).unbind('mousemove');
					$(document).unbind('mouseup');

				}
			};
			$('#drag').drag();

		}
		var $phone = $("#phone");
		//号码验证
		function test_() {
			var theinput = $phone.val();
			var p1 = /^1(3|4|5|6|7|8|9)\d{9}$/;

			var Width = $(".drag_bg").width()
			console.log(Width)
			if (!(p1.test(theinput))) {
				$(this).parent().removeAttr("herf");
				alert('请填写正确电话号码!!');
				theinput = "";
			} else if (Width < 321) {
				alert('请先验证!!');
			} else {
				//var _phone = $("#phone").val();
				$.ajax({
					url: 'http://'+ variable + '/eip-web-vip/member/isRegister', //请求服务器url地址.   +cid+"&pid="+pid					type: "post",
					type: "post",
					data: {
						f_user_name: theinput
					},
					datatype: 'json',
					async: false, //同步，防止重复提交
					xhrFields: {
						withCredentials: true
					},
					beforeSend: function() {},
					success: function(data) {
						var res = JSON.parse(data);
					 	if (res.state == 1) {
					 		alert("该手机号已注册");
							location = 'zzy-denglu.html?orgNum='+orgNum;
						} else if (res.state == -1){
							alert("服务器出错");
						} else{
							localStorage.setItem('myPhone',theinput);
							window.location.href = "zzy-zc2.html?orgNum="+orgNum;
						}
					},
					complete: function() {;
					},
					error: function(data) {
						alert("数据发送失败！");
					}
				});
			}
		}
	</script>
</html>
