<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta http-equiv="pragma" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache">
		<meta http-equiv="expires" content="0">
		<title>设置密码</title>
		<link rel="icon" href="../../img/logo2.png" type="image/x-icon">
		<link rel="stylesheet" type="text/css" href="css/zzy-zhuce.css" />
		<script src="js/jquery-1.11.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/jquery.params.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/variable.js?v=230620" type="text/javascript" charset="utf-8"></script>
		<script src="js/md5/md5.js"></script>
		<style>
			.window-password .layui-layer-content{
				overflow: unset !important;
			}
			.window-password .tip-state2 {
				color: #f2bd00;
			}
			.window-password .tip-state3 {
				color: green;
			}
			.window-password .tip-state3 .tip-desc2 {
				width: 100%;
				background: green;
			}
			.window-password .tip-state2 .tip-desc2 {
				width: 67%;
				background: #f2bd00;
			}
			.window-password .layui-slider {

				border-radius: 5px;
				height: 6px;
			}
			.window-password .tip-desc2 {
				height: 100%;
				background: transparent;
				width: 0;
				left:0;
				border-radius: 5px;
			}
			.window-password .tip-state1 .tip-desc2 {
				width: 34%;
				background: red;
			}
			.window-password .tip-state1 {
				color: red;
			}
			.window-password .tip .tip-state {
				background: white;border-radius: 5px;padding: 20px;border: 1px solid #eee;
				box-shadow: 0 0 20px rgba(0,0,0,.3);
			}
			.window-password .layui-layer-content {
				height: 186px !important;
			}
			.window-password .tip {
				top: -61px;
				left: 463px;
				display: none;
				position: absolute;padding: 20px;
				background: transparent;width: 240px;
				font-size: 14px;
				z-index: 999;
			}
			.layui-slider {
					height: 4px;
					background: #e2e2e2;
					border-radius: 3px;
					position: relative;
					cursor: pointer;
			}
		</style>
	</head>

	<body>
		<div class="zc-log">注册账户</div>
		<div></div>
		<div class="procedure">
			<div class="position">
				<div><i>1</i>设置账户</div>
				<div><i>2</i>账户验证</div>
				<div class="state-color"><i class="state">3</i>设置密码</div>
				<div><i>4</i>获取结果</div>
			</div>
		</div>
		<div class="shezhizhanghu window-password">
			<form class="juzhong" style="position: relative;">
				<div class="tip">
					<div style="width: 0;height: 0;border: 10px solid transparent;position: absolute;top: 70px;left: -4px;border-right-color: white;border-right-width: 15px;"></div>
					<div class="tip-state ">
						<div class="tip-desc1">强度: </div>
						<div style="padding: 15px 0;">
							<div class="layui-slider ">
								<div class="tip-desc2" class="layui-slider-bar"></div>
							</div>
						</div>
						<div>密码至少为8-16位英文字母、数字、字符组合</div>
					</div>
				</div>
				<div class="phone3" style="position: relative;">
					<p style="text-indent: 16px;">新密码</p>
					<input type="password" name="" id="pw1" onblur="checkPass(false)" onkeyup="checkPass(true)">
					<span style="font-size: 16px; color: #eb4c4c;position: absolute;right:-333px;top: 7px; display: none;" class="zifu">密码至少为8-16位英文字母、数字、字符组合</span>
					<br />
					<p>再次输入</p>
					<input type="password" name="" id="pw2">
				</div>
				<input type="button" style="margin-left: 100px;margin-top: 0px;" class="ph3-button" id="sub" value="下一步" />
			</form>
		</div>
		<div class="mengban" style="display:none"></div>
		<div class="tishi" style="display:none">
			<span><img src="img/success.png"/></span>
			<p>恭喜您已注册成功</p>
			<p><i id="jumpTo">3</i>s后自动返回登陆页</p>
		</div>

	</body>
	<script type="text/javascript">
		var orgNum = $.query.get("orgNum");
		console.log(orgNum);
		if(orgNum == 'true'){
			orgNum = ''
		}
	    var myPhone = localStorage.getItem('myPhone');
		var myyz = localStorage.getItem('myyanzheng');
        if((myyz == null)||(myPhone == null)){
           	  window.location.href = "zzy-zc1.html?orgNum="+orgNum;
        }
		$(function(){

		});
		function checkPass(ifShow=false) {
				if(ifShow){
					$('.window-password .tip-state')
						.removeClass('tip-state1')
						.removeClass('tip-state2')
						.removeClass('tip-state3')
						;
					$('.window-password .tip').show();
					const level = getPassLevel($('#pw1').val() || '');
					$('.window-password .tip-state').addClass('tip-state'+level);
					$('.window-password .tip-desc1').text('强度: '+ ['','低','中等','高'][level]);

				}// else $('.window-password .tip').hide();
			}
			function getPassLevel(pass) {
				if(typeof pass !== 'string' || !pass) return 0;
				var level = 0;
				// 小写字母，级别+1
				if (/[a-z]/.test(pass)) level++;
				// 数字+1
				if (/[0-9]/.test(pass)) level++;
				// 其他+1
				if (/[^a-z0-9]/.test(pass)) level++;
				return level;
			}
		$("#sub").on("click", function() {
			const pass  = $("#pw1").val();
			if(!pass || $("#pw2").val() == "") {
				alert("请输入密码");
			} else if(pass != $("#pw2").val()) {
				alert("密码不一致");
			} else if(getPassLevel(pass) < 3){
				alert("密码至少为8-16位英文字母、数字、字符组合");
				// $(".zifu").css("display","block");
			} else{
				var password = $("#pw1").val();
				password = hex_md5(password).toUpperCase();
				$.ajax({
					url:'http://'+variable+'/eip-web-vip/member/registeUser', //请求服务器url地址.   +cid+"&pid="+pid
					type:"post",
					data:{f_user_name:localStorage.getItem('myPhone'),f_user_password:password,orgNum : orgNum},
					datatype: 'json',
	            	async:false,  //同步，防止重复提交
			    	xhrFields:{withCredentials:true},
					beforeSend: function() {
					},
					success: function(data) {
						 var  res = JSON.parse(data);
						 var  state = res.result;
						 var  mes = res.data;
						 if(state == 1){
							    //清空本地全局变量，防止重复提交
							    localStorage.clear();
						        $(".mengban").css("display", "block");
								$(".tishi").css("display", "block");
								$(".position div").eq(2).removeClass("state-color").children("i").removeClass("state")
								$(".position div").eq(3).addClass("state-color").children("i").addClass("state")
								setTimeout(function coun(){
								  location='zzy-denglu.html?orgNum='+orgNum;
							    },3000);

						 }else if ((state == -1)||(state == -2)||(state == -3)){
							 alert("该手机号已注册");
							 location='zzy-denglu.html?orgNum='+orgNum;
						 }else{
							 alert("注册失败");
						 }
					},
					complete: function() {
						 ;
					},
					error: function(data) {
						alert("数据发送失败！");
					}
				});

			}

		});

	</script>

</html>