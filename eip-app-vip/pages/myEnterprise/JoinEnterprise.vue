<template>
  <custom-dialog
    title="申请加入企业"
    :show="show"
    width="400px"
    show-header
    @before-close="close"
  >
    <template #header-right>
      <el-button type="primary" size="mini" @click="confirm">确定</el-button>
    </template>
    <el-main>
      <el-form :rules="rules" ref="form" class="form" size="mini" label-width="120px" :model="form" @submit.native.prevent="confirm">
        <!--<el-form-item label="品牌" prop="brandId">
          <el-select
            style="width: 100%;"
            v-model="form.brandId"
            placeholder="请选择品牌"
            clearable
            filterable
          >
            <el-option
              v-for="item in brandList"
              :key="item.projectId"
              :label="item.projectName"
              :value="item.projectId">
            </el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="企业名称" prop="companyName">
          <el-input clearable v-model="form.companyName" placeholder="企业名称"></el-input>
        </el-form-item>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue";

export default {
  name: "JoinEnterprise",
  mixins: [Mixins.clearAndInitForm('form')],
  components: {CustomDialog},
  props: {
    onConfirm: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      show: false,
      form: {
        brandId: '',
        companyName: ''
      },
      brandList: [],
      rules: {
        // brandId: [{required: true, message: '请选择品牌', trigger: ['change', 'blur']}],
        companyName: [{required: true, message: '请输入企业名称', trigger: ['change', 'blur']}]
      }
    }
  },
  methods: {
    open() {
      this.form.companyName = ''
      this.form.brandId = ''
      this.show = true
      this.getBrandList()
    },
    async confirm() {
      try {
        await this.validateForm('form')
        if (await this.onConfirm(this.form))
          this.close()
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        if (this.handleErrors(e)) {
          console.warn(e)
        }
      }
    },
    async getBrandList() {
      this.loading = true; // Optional: Show loading during brand fetch
      try {
        const payload = {orgNum: util.getOrgNum()};
        const {data} = await Axios.post('/project/getTopLevelBrandProject', JSON2FormData(payload));

        if (data.state !== 1) {
          this.errorMsg(data.msg || '获取品牌列表失败');
          await Promise.reject(data.msg); // Stop execution if needed
        }
        this.brandList = data.data || [];
      } catch (e) {
        // Avoid showing generic error if specific error was already shown
        if (typeof e === 'string' && e !== 'exit,close,cancel') {
          console.warn('获取品牌列表失败:', e);
        } else if (!(typeof e === 'string' && e === 'exit,close,cancel')) {
          console.warn('获取品牌列表异常:', e);
          this.errorMsg('获取品牌列表时发生错误');
        }
      } finally {
        this.loading = false;
      }
    },
    close() {
      this.show = false
    }
  }
}
</script>

<style scoped>

</style>