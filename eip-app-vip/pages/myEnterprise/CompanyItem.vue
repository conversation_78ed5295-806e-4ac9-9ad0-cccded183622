<template>
  <div class="company-item-wrapper">
    <div class="company-item">
      <div class="company-item-logo-container">
        <el-image
          v-if="company.companyLogo"
          class="company-item-logo"
          :src="company.companyLogo"
          fit="cover"
          :preview-src-list="[company.companyLogo]"
        >
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
          <div slot="placeholder" class="image-slot">
            Loading<span class="dot">...</span>
          </div>
        </el-image>
        <div v-else class="company-item-logo image-slot">
          <i class="el-icon-office-building"></i>
        </div>
      </div>
      <div class="company-item-info">
        <div class="info-header">
          <div class="company-profile">
            <span v-if="company.companyName" class="company-item-name">{{ company.companyName }}</span>
            <span v-if="company.companyNameEn" class="label english-label">{{ company.companyNameEn }}</span>
            <!--<el-tag style="margin-left: 10px;" v-if="company.brandName" type="primary" size="small" effect="plain">{{ company.brandName }}</el-tag>-->
            <el-tag style="margin-left: 10px;" v-if="company.certificationState === 1" type="success" size="small" effect="plain">
              <i class="el-icon-success"></i>企业已认证
            </el-tag>
            <el-tag
              v-else
              style="margin-left: 10px;cursor: pointer"
              type="warning"
              size="small"
              effect="plain"
              @click="company.certificationState !== 2 && $emit('apply-certification', company)">
              <i class="el-icon-question"></i>{{ company.certificationState === 2 ? '企业认证中' : '当前企业暂未认证' }}
            </el-tag>
            <el-button
              v-if="company.isMain !== null && company.state !== 0"
              style="padding: 0;margin-left: 10px;"
              type="text"
              icon="el-icon-edit"
              @click="$emit('edit', company)">编辑
            </el-button>
            <el-button
              v-if="company.isMain !== null && company.state !== 0"
              style="padding: 0"
              type="text"
              icon="el-icon-user"
              @click="$emit('read-person', company)">查看企业名片
            </el-button>
            <el-dropdown v-if="!company.isMain && company.enterprisePersonId" placement="bottom-start" trigger="click" @command="$emit($event, company)">
              <span role="button" style="margin-left: 10px;">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="exit-company">退出企业</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="company-main-state">
            <i class="el-icon-s-management"></i>
            {{mainState}}
          </div>
        </div>
      </div>
      <div class="company-item-action">
        <template v-if="company.state === 0 && company.applyId">
          <div class="revoking">
            <span>申请加入中</span>
            <el-link :underline="false" type="primary" @click="$emit('revoke-apply', company)">撤销申请</el-link>
          </div>
        </template>
        <el-button v-else-if="company.isMain !== null" size="small" type="primary" @click="$emit('details', company)">进入企业会员中心</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CompanyItem',
  props: {
    company: {
      type: Object,
      required: true,
    }
  },
  computed: {
     mainState() {
       if (this.company.isMain) return  '主账号'
       if (this.company.isMain === false) return '子账号'
       return '企业下职员'
     }
  }
}
</script>

<style scoped>
.company-item-wrapper {
  border-radius: 4px;
}

.company-item-wrapper:hover {
  background-color: rgb(247, 249, 251);
}

.company-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 15px 20px;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.company-item-logo-container {
  flex-shrink: 0;
}

.company-item-logo {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #eee;
  cursor: pointer;
}

.company-item-logo.image-slot {
  width: 78px;
  height: 78px;
  background-color: #eee;
  color: #c0c4cc;
  font-size: 30px;
  cursor: default;
}

.company-item-info {
  flex-grow: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.company-item-action {
  align-self: center;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 24px;
  border-radius: 6px;
}

.small-image-slot {
  font-size: 20px;
  border-radius: 4px;
}

.image-slot .dot {
  animation: blink 1.4s infinite both;
  animation-delay: -0.32s;
}

.image-slot .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.image-slot .dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes blink {
  0%, 80%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.info-header {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 8px 10px;
}

.company-item-name {
  font-size: 17px;
  font-weight: 600;
  color: #303133;
  margin-right: 5px;
  white-space: nowrap;
}

.english-label {
  color: #909399;
  white-space: nowrap;
}

.el-tag {
  height: 22px;
  line-height: 20px;
}

.revoking {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.company-main-state {
  color: #999;
  font-size: 14px;
}
</style>