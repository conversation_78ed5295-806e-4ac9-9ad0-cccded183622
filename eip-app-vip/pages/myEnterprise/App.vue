<template>
  <el-main class="container">
    <el-header class="container-title" height="auto">
      <h1>
        我的企业
        <el-button
          @click="$refs.ch && $refs.ch.open()"
          type="text">查看历史企业
        </el-button>
      </h1>
    </el-header>
    <company-item
      v-for="enterprise in enterprises"
      :key="enterprise.enterpriseId"
      @edit="handleEditEnterprise('edit', $event)"
      @revoke-apply="handleRevokeApply"
      @details="handleToEnterpriseDetails"
      @apply-certification="$refs.certAD && $refs.certAD.open(enterprise)"
      @read-person="handleReadPerson"
      @exit-company="handleEditEnterprise('exit', $event)"
      :company="enterprise"></company-item>
    <div class="create-enterprise">
      <el-button
        @click="handleEditEnterprise('add')"
        icon="el-icon-circle-plus-outline"
        type="text">创建企业
      </el-button>
      <el-button
        @click="$refs.je && $refs.je.open()"
        icon="el-icon-circle-plus-outline"
        type="text">加入已有企业
      </el-button>
    </div>
    <company-editor @check-join="handleJoinEnterprise" ref="editor" @finish="handleBackEditing(true)"></company-editor>
    <join-enterprise ref="je" :on-confirm="handleJoinEnterprise"></join-enterprise>
    <certification-apply-dialog ref="certAD" @finish="handleBackEditing(true)"></certification-apply-dialog>
    <company-history ref="ch"></company-history>
    <enterprise-person-editor ref="epEditor"></enterprise-person-editor>
  </el-main>
</template>

<script>
import CompanyItem from "./CompanyItem.vue";
import JoinEnterprise from "./JoinEnterprise.vue";
import CertificationApplyDialog from "../components/CertificationApplyDialog.vue";
import CompanyEditor from "../components/CompanyEditor.vue";
import CompanyHistory from "./CompanyHistory.vue";
import EnterprisePersonEditor from "../components/EnterprisePersonEditor.vue";

export default {
  name: 'App',
  mixins: [Mixins.loadingWrap()],
  components: {
    EnterprisePersonEditor,
    CompanyHistory,
    CertificationApplyDialog,
    JoinEnterprise,
    CompanyEditor,
    CompanyItem
  },
  data() {
    return {
      enterprises: [],
      fieldConfig: [],
    }
  },
  mounted() {
    this.getEnterprises()
    this.getFieldSet()
  },
  methods: {
    async getEnterprises() {
      this.loadingWrap(async () => {
        const {data} = await Axios.post('/enterprise/getMyEnterpriseAndApply')
        if (data.state !== 1) await Promise.reject(data.msg)
        this.enterprises = data.data
      })
    },
    async getFieldSet() {
      this.loadingWrap(async () => {
        const {data} = await Axios.post('/exhibitSetting/selectZzyUserInfoShowField')
        if (data.state !== 1) await Promise.reject(data.msg)
        this.fieldConfig = Object.freeze((data.data || []).map(item => {
          return {
            ...item,
            need: +item.value === 1,
            required: !!item.isNull,
            field: item.param,
          }
        }))
      })
    },
    handleToEnterpriseDetails(company) {
      this.loadingWrap(async () => {
        if (!company.enterpriseId) await Promise.reject('数据加载失败')
        const target = new URL('/eip-app-vip/main/corp.html?', location.origin)
        const local = new URL(location.href)
        target.searchParams.set('token', local.searchParams.get('token'))
        target.searchParams.set('p', local.searchParams.get('p') || '')
        target.searchParams.set('l', local.searchParams.get('l'))
        target.searchParams.set('cp', company.enterpriseId)
        window.open(target, 'corp')
      })

    },
    /**
     * @param {'add'|'edit'|'exit'} act
     * @param {object} [company]
     * */
    handleEditEnterprise(act, company) {
      const editor = this.$refs.editor
      const add = () => {
        editor.open({fields: this.fieldConfig})
      }
      const edit = () => {
        this.loadingWrap(async () => {
          if (!company.enterpriseId) await Promise.reject('数据加载失败')
          const {data} = await Axios.post('/enterprise/selectById', JSON2FormData({enterpriseId: company.enterpriseId}))
          if (data.state !== 1) await Promise.reject(data.msg)
          editor.open({data: data.data, fields: this.fieldConfig})
        })
      }
      const exit = () => {
        this.loadingWrap(async () => {
          if (!company.enterprisePersonId) await Promise.reject('数据加载失败')
          const {data} = await Axios.post('/enterprisePerson/exitEnterprise', JSON2FormData({enterprisePersonId: company.enterprisePersonId}))
          if (data.state !== 1) await Promise.reject(data.msg)
          this.$message.success({
            message: '退出成功',
            showClose: true
          })
          await this.getEnterprises()
        })
      }

      return {add, edit, exit}[act].call(this)
    },
    handleRevokeApply({applyId}) {
      this.loadingWrap(async () => {
        await this.$confirm('确定撤销申请成为子账号吗？', '提醒', {type: 'warning'})
        await Axios.post('/subAccountApply/deleteSubAccountApply', JSON2FormData({applyId}))
        this.$message.success({
          message: '撤销成功',
          showClose: true
        })
        await this.getEnterprises()
      })

    },
    async handleJoinEnterprise(company, fromEditor) {
      const {companyName, brandProjectId} = company
      if (!companyName) return
      return this.loadingWrap(async () => {
        const {data} = await Axios.post('/enterprise/getCertificationEnterpriseByName', JSON2FormData({
          companyName: companyName,
          brandProjectId: brandProjectId || '',
          enterpriseId: fromEditor ? (company.enterpriseId || '') : '', // 编辑时排除自己
        }))
        const {enterpriseId} = data.data || {}
        if (!enterpriseId) {
          if (fromEditor) return
          await Promise.reject('未找到对应的企业')
        }
        if (fromEditor) await this.$confirm('已存在同名公司，是否申请成为该公司人员?', '提醒', {type: 'warning'})
        const {data: applyData} = await Axios.post('/enterprisePerson/saveCardOrApply', {enterpriseId})
        if (applyData.state === 10130090) await Promise.reject('已存在申请子账号记录，请勿重复申请')
        if (applyData.state !== 1) await Promise.reject(applyData.msg)
        if (fromEditor) await this.handleBackEditing(true)
        else await this.getEnterprises()
        this.$message.success({
          message: '申请已提交',
          showClose: true,
        })
        this.$refs.je.close()
      })
    },
    async handleBackEditing(force) {
      if (!await this.$refs.editor.close(force)) return
      await this.getEnterprises()
    },
    handleSaveEnterprise() {
      this.$refs.editor.save()
    },
    handleReadPerson({enterprisePersonId, enterpriseId}) {
      this.$refs.epEditor.open({
        enterprisePersonId,
        enterpriseId,
        component: 'PersonCardDetails'
      })
    }
  }
}
</script>

<style scoped>
.container {
  background-color: #fff;
  margin: 20px;
  overflow: auto;
  height: calc(100vh - 42px);
  box-sizing: border-box;
}

.container-title {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding: 10px 0 20px;
  display: flex;
}

.container-title h1 {
  margin: 0;
  font-size: 18px;
}

.container-title .el-link {
  margin-left: 20px;
}

.create-enterprise {
  border-top: 1px solid #eee;
  text-align: center;
}

.submit-footer {
  background-color: rgb(245, 245, 245);
  position: sticky;
  bottom: -20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>