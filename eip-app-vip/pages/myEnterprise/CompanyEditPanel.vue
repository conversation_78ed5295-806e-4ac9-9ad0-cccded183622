<template>
  <el-container v-if="show" v-loading="loading">
    <el-main style="padding: 30px 0;max-width: 1000px;">
      <el-form
        :model="form"
        class="formSelf"
        label-width="130px"
        label-position="right"
        size="small"
        :rules="rules"
        ref="form">
        <div style="display: flex;padding-bottom: 10px;flex-wrap: wrap;justify-content: space-between;">
          <el-form-item
            v-for="it in cFormFields" :key="it.field"
            :label="getFieldLabel(it)"
            :prop="it.field"
            :style="{flexBasis: it.width || '48%', marginBottom: '15px'}"
          >
            <div :style="{ width: it.width || '100%'}">
              <template v-if="it.type==='img'">
                <div>
                  <div v-if="form[it.field]"
                       style="width: 188px;height: 134px;background: #fff;border: 1px solid #e5e5e5;">
                    <div style="width: 100%;height: 100%;position: relative;" class="upload-img">
                      <el-image
                        style="width: 100%;height: 100%;"
                        :ref="'form_'+it.field"
                        :src="getImgUrl(form[it.field])"
                        :preview-src-list="[getImgUrl(form[it.field])]">
                        <img slot="error" src="img/blank.jpg" alt="" style="width: 100%; height: 100%;">
                      </el-image>
                      <div class="flex-el-link" style="position: absolute;inset:0; padding: 48px 0 0 70px;">
                        <el-link icon="el-icon-view" @click="viewImg(it.field)" :underline="false"></el-link>
                        <el-link icon="el-icon-delete" @click="form[it.field] = ''" :underline="false"></el-link>
                      </div>
                    </div>
                  </div>
                  <el-upload
                    v-else
                    :ref="'upload' + it.field"
                    action="/eip-web-vip/upload/uploadFileOss"
                    :before-upload="beforeUploadNew"
                    :on-error="uploadError"
                    :headers="authHeaders"
                    :on-success="(response,file,fileList)=>uploadSuccess(response,file,fileList,it.field)"
                    :show-file-list="false"
                  >
                    <div class="box-uploader"
                         style="width: 188px;height: 134px;background: #fff;border: 1px solid #e5e5e5;">
                      <div class="upload-tip">
                        上传{{ it.fieldName || '--' }}
                      </div>
                      <el-input type="hidden" v-model="form[it.field]"></el-input>
                    </div>
                  </el-upload>
                </div>
              </template>
              <template v-else-if="it.field==='customerGroupList'">
                <el-select
                  multiple
                  clearable
                  class="fixed-arrow"
                  :collapse-tags="form.customerGroupList.length > 3"
                  popper-class="disallow-popper"
                  @click.native.stop="openCustomerGroupSelector"
                  @remove-tag="onCustomerGroupChange(form.customerGroupList)"
                  v-model="form.customerGroupList"
                  :title="selectedCustomerGroupList.map(item => item.name).toString()"
                  :placeholder="it.fieldName">
                  <el-option
                    v-for="cr in selectedCustomerGroupList"
                    :label="cr.name"
                    :value="cr.id"
                    :key="cr.id">
                  </el-option>
                </el-select>
              </template>
              <template v-else-if="it.field==='tradeAndProductTypeList'">
                <el-select
                  multiple
                  clearable
                  class="fixed-arrow"
                  :collapse-tags="form.tradeAndProductTypeList.length > 5"
                  popper-class="disallow-popper"
                  @click.native.stop="openTradeAndProductTypeSelector"
                  @remove-tag="onTradeAndProductTypeChange(form.tradeAndProductTypeList)"
                  v-model="form.tradeAndProductTypeList"
                  :title="selectedTradeAndProductTypeList.map(item => item.text).toString()"
                  :placeholder="`行业分类 - 产品分类`">
                  <el-option
                    v-for="cr in selectedTradeAndProductTypeList"
                    :label="cr.text"
                    :value="cr.idx"
                    :key="cr.idx">
                  </el-option>
                </el-select>

              </template>
              <template v-else-if="it.type==='select'">
                <el-select
                  filterable
                  clearable
                  default-first-option
                  :multiple="it.multiple"
                  v-model="form[it.field]"
                  :placeholder="it.fieldName"
                  @change="v => it.onChange && it.onChange(v)"
                >
                  <el-option
                    v-for="cr in get(it.selectListKey)"
                    :label="cr[it.labelKey||'text']" :value="cr[it.idKey||'id']"
                    :key="cr[it.idKey||'id']">
                  </el-option>
                </el-select>
              </template>
              <template v-else-if="it.type==='textarea'">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 9}"
                  clearable
                  v-model="form[it.field]"
                  :placeholder="it.fieldName"
                ></el-input>
              </template>
              <template v-else>
                <el-input
                  :type="it.type || 'text'"
                  clearable
                  @blur="(e) => it.onBlur && it.onBlur(form[it.field], e)"
                  v-model="form[it.field]"
                  :placeholder="it.fieldName"></el-input>
              </template>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
    <item-tree-selector ref="itc"></item-tree-selector>
    <trade-prod-tree-selector ref="tpts"></trade-prod-tree-selector>
  </el-container>
</template>

<script>
import TradeProdTreeSelector from "../components/TradeProdTreeSelector.vue";
import ItemTreeSelector from "../components/ItemTreeSelector.vue";

const PATTERN_EMAIL_LOOSE = /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i

const ZzMixin = {
  data() {
    return {
      show: false,
      loading: false,
      formChange: false,
      formInit: {},
    }
  },
  created() {
    this.formInit = JSON.parse(JSON.stringify(this.form));
  },
  watch: {
    form: {
      handler(n, o) {
        if (!this.formChange) {
          this.formChange = true;
        }
      },
      deep: true,
    },
  },
  methods: {
    obj2fd(obj) {
      return this.obj2FormData(obj);
    },
    obj2FormData(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      return this.$alert(msg, title, {type})
    },
    error(msg, throws = true) {
      msg = msg || '请求失败';
      const p = this.errorMsg(msg, '提醒', 'warning');
      if (this.loading) this.loading = false;
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message = '操作成功!') {
      return this.$message({
        message,
        type: 'success',
        showClose: true
      });
    },
    checkRet(obj) {
      const {state, msg, data} = obj
      if (state !== 1) this.error(msg)
    },
    initForm() {
      this.form = JSON.parse(JSON.stringify(this.formInit));
    },
    async close(force) {
      if (force === true) this.formChange = false
      if (this.formChange) {
        try {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return
        }
      }
      this.$refs.form.clearValidate()
      this.show = false
      return true
    },
  },
};
const isMobile = (rule, value, callback) => {
  if (!value || /^1[3-9][0-9]\d{8}$/.test(value)) {
    callback()
  } else {
    callback(new Error('手机号码格式如:138xxxx8754'))
  }
}
const isEmail = (rule, value, callback) => {
  if (!value || PATTERN_EMAIL_LOOSE.test(value)) {
    callback()
  } else {
    callback(new Error('电子邮箱格式错误!'))
  }
}

export default {
  name: "CompanyEditPanel",
  components: {ItemTreeSelector, TradeProdTreeSelector},
  mixins: [ZzMixin, Mixins.setValidateRules(), Mixins.itemKindCode(), Mixins.clearAndInitForm('form')],
  data() {
    return {
      form: {
        zzyUserId: '', // 会员ID
        enterpriseId: '', //企业id
        brandId: '', //品牌id
        companyName: '', //企业名称
        companyNameEn: '', //企业名称 英文
        companyNameAbbr: '', //简称
        companyNameAbbrEn: '', //英文简称
        companyTel: '', //电话
        companyFax: '', //传值
        companySite: '', //网址
        address: '', //地址
        companyLogo: '', //企业logo
        companyEmail: '', //邮箱
        companyProfile: '', //简介
        companyProfileEn: '', //英文简介
        businessLicense: '', //营业执照
        socialReditCode: '', //社会信用代码
        country: '', //国家
        province: '', //省份
        city: '', //城市
        district: '', //区县
        companyPic: '', //公司图片
        certificationState: '', //认证标识  1 已认证
        nature: '', //企业类型id
        brandName: '', //品牌名称
        zzyUserId: '',
        tradeAndProductTypeList: [], //行业和产品类型list,
        customerGroupList: [], //客户群体
        keyMarketList: [], //重点市场
      },
      formRules: [
        {
          field: 'companyName',
          need: true,
          required: false,
          fieldName: '公司名称',
          onBlur: () => {
            this.$emit('check-join', this.form, true)
          }
        },
        {
          field: 'companyNameEn',
          need: true,
          required: false,
          fieldName: '公司名称(英文)',
        },
        {
          field: 'companyNameAbbr',
          need: true,
          required: false,
          fieldName: '公司简称',
        },
        {
          field: 'companyNameAbbrEn',
          need: true,
          required: false,
          fieldName: '公司简称(英文)',
        },
        {
          field: 'nature',
          need: true,
          required: false,
          fieldName: '企业类型',
          type: 'select',
          selectListKey: 'nature',
        },
        {
          field: 'companyTel',
          need: true,
          required: false,
          fieldName: '联系电话',
        },
        {
          field: 'companyEmail',
          need: true,
          required: false,
          fieldName: '公司邮箱',
        },
        {
          field: 'companyFax',
          need: true,
          required: false,
          fieldName: '传真',
        },
        {
          field: 'tradeAndProductTypeList',
          need: true,
          required: false,
          fieldName: '分类', // 行业和产品类型
          width: '100%',
        },
        {
          field: 'customerGroupList',
          need: true,
          required: false,
          fieldName: '客户群体',
        },
        {
          field: 'keyMarketList',
          need: true,
          required: false,
          fieldName: '重点市场',
          type: 'select',
          selectListKey: 'keyMarket',
          multiple: true
        },
        {
          field: 'companySite',
          need: true,
          required: false,
          fieldName: '公司网址',
          width: '100%',
        },
        {
          field: 'country',
          need: true,
          required: false,
          fieldName: '国家',
          type: 'select',
          selectListKey: 'countries',
          labelKey: 'name',
          idKey: 'name',
          onChange: val => {
            this.countryChange(val)
          }
        },
        {
          field: 'province',
          need: true,
          required: false,
          fieldName: '省份',
          type: 'select',
          selectListKey: 'provinces',
          labelKey: 'f_province',
          idKey: 'f_province',
          onChange: val => {
            this.loadArea(-1, val)
          }
        },
        {
          field: 'city',
          need: true,
          required: false,
          fieldName: '城市',
          type: 'select',
          selectListKey: 'city',
          labelKey: 'f_city_name',
          idKey: 'f_city_name',
          onChange: val => {
            this.loadArea(val, '')
          }
        },
        {
          field: 'district',
          need: true,
          required: false,
          fieldName: '区县',
          type: 'select',
          selectListKey: 'district',
          labelKey: 'f_city_name',
          idKey: 'f_city_name',
        },
        {
          field: 'address',
          need: true,
          required: false,
          fieldName: '公司地址',
          width: '100%', // 100%
        },
        {
          field: 'socialReditCode',
          need: true,
          required: false,
          fieldName: '社会信用代码',
          width: '100%',
        },
        {
          field: 'companyLogo',
          need: true,
          required: false,
          fieldName: '公司Logo',
          type: 'img',
        },
        {
          field: 'companyPic',
          need: true,
          required: false,
          fieldName: '公司图片',
          type: 'img',
        },
        {
          field: 'businessLicense',
          need: true,
          required: false,
          fieldName: '营业执照 ',
          width: '100%',
          type: 'img',
        },
        {
          field: 'companyProfile',
          need: true,
          required: false,
          fieldName: '公司简介',
          width: '100%',
          type: 'textarea',
        },
        {
          field: 'companyProfileEn',
          need: true,
          required: false,
          fieldName: '公司简介(英文)',
          width: '100%',
          type: 'textarea',
        },
      ],
      rules: {},
      keyMarket: [],
      nature: [],
      customerGroup: [],
      selectedCustomerGroupList: [],
      selectedTradeAndProductTypeList: [],
      countries: [],
      provinces: [],
      city: [],
      district: [],
      showPCD: true,
    }
  },
  computed: {
    title() {
      return '编辑公司信息'
    },
    cFormFields() {
      const pcd = 'province,city,district'.split(',')
      return this.formRules.filter(it => {
        if (pcd.includes(it.field) && !this.showPCD) return false
        return it.need
      });
    },
    authHeaders() {
      return {
        Authorization: Axios.defaults.headers.common['Authorization'],
        Appid: Axios.defaults.headers.common['Appid']
      }
    }
  },
  props: {
    autoClose: {
      type: Boolean,
      default: true
    },
  },
  methods: {
    async beforeUploadNew(file) {
      if (!window.imageHelper) return
      if (!imageHelper.isImageFile(file)) {
        this.error('请选择图片文件', false)
        await Promise.reject()
      }
      const result = await imageHelper.checkCompressTip(file, this)
      if (!result) await Promise.reject()
      window.__TMP_VUE_LOADING = this.$loading({
        lock: true,
        text: '上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.3)'
      })
      if (result === 'compress') {
        file = await imageHelper.imageCompress(file)
      }
      return file
    },
    uploadError(err, file, fileList) {
      this.$message.error('上传失败!');
      if (window.__TMP_VUE_LOADING && typeof window.__TMP_VUE_LOADING.close === 'function') {
        window.__TMP_VUE_LOADING.close()
      }
    },
    async uploadSuccess(resp, file, fileList, field) {
      if (window.__TMP_VUE_LOADING && typeof window.__TMP_VUE_LOADING.close === 'function') {
        window.__TMP_VUE_LOADING.close()
      }
      if (resp.state !== 1) this.error(resp.msg || resp.message || '上传失败');
      if (['companyLogo', 'companyPic', 'businessLicense'].includes(field)) {
        this.$set(this.form, field, resp.result.path)
      } else {
        this.error('不支持的上传类型')
      }
    },
    getFieldLabel(it) {
      return it.fieldName || ''
    },
    getImgUrl(url) {
      if (!url) return 'img/';
      if (url.startsWith('//') || url.startsWith('http')) {
        return url
      } else {
        return '/image/' + url;
      }
    },
    open({data = {}, fields}) {
      this.show = true
      this.initForm();
      Object.keys(this.form).map(k => {
        if (data.hasOwnProperty(k)) {
          let v = (!data[k] && data[k] !== 0) ? '' : data[k];
          if (v) {
            switch (k) {
              case 'province':
                this.loadArea(-1, v, !0)
                break
              case 'city':
                this.loadArea(-1, v, !0)
                break
              case 'customerGroupList':
                this.selectedCustomerGroupList = v.map(item => {
                  return {
                    ...item,
                    name: item.name,
                    id: item.value,
                    oldId: item.id
                  }
                })
                v = v.map(it => it.value)
                break
              case 'keyMarketList':
                v = v.map(it => it.value)
                break
              case 'tradeAndProductTypeList':
                this.selectedTradeAndProductTypeList = v.map(item => {
                  return {
                    ...item,
                    text: item.name,
                    idx: [item.type, item.value].join('-')
                  }
                })
                v = v.map(it => [it.type, it.value].join('-'))
                break
            }
          }
          this.form[k] = v
        }
      });
      this.form.customerGroupList = this.form.customerGroupList || []
      this.formRules.map(it => {
        it.need = false;
        it.required = false;
      });
      this.clearValidateRules()
      fields = fields || []
      fields.forEach(it => {
        let tmp = this.formRules.find(itt => itt.field === it.field)
        if (tmp) {
          tmp.need = it.need;
          tmp.required = it.required;
          if (it.required) {
            this.setValidateRules(it.field, {
              required: true,
              message: tmp.fieldName + '不能为空',
              trigger: ['change', 'blur']
            })
          }
        }
      })
      this.setValidateRules('companyEmail', {validator: isEmail, trigger: ['change', 'blur']})
      this.countryChange()
      setTimeout(() => {
        this.formChange = false
        this.$refs.form && this.$refs.form.clearValidate()
      }, 10)
      this.loading = false;
    },
    setFormField(field, val) {
      if (this.cFormFields.find(it => it.field == field) && val) {
        this.$set(this.form, field, val)
      }
    },
    viewImg(field) {
      if (this.$refs['form_' + field]) this.$refs['form_' + field][0].clickHandler();
    },
    async save() {
      try {
        await this.validateForm('form')
      } catch (e) {
        if (!this.handleErrors(e)) console.warn(e)
        return
      }
      this.loading = true;
      const payload = {...this.form}
      payload.customerGroupList = payload.customerGroupList.map(value => ({value}))
      payload.keyMarketList = payload.keyMarketList.map(value => ({value}))
      payload.tradeAndProductTypeList = payload.tradeAndProductTypeList.map(item => {
        const [type, value] = item.split('-')
        return {type, value}
      })
      const {data} = await Axios
        .post('/enterprise/save', payload)
        .catch(e => this.error())
      this.checkRet(data);
      this.ok()
      this.loading = false;
      this.$emit('finish')
      if (this.autoClose) {
        this.formChange = false
        this.close()
      }
    },
    async openCustomerGroupSelector() {
      try {
        this.selectedCustomerGroupList = await this.$refs.itc.open('customer_group', this.form.customerGroupList)
        this.form.customerGroupList = this.selectedCustomerGroupList.map(it => it.id)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
      }
    },
    onCustomerGroupChange(value) {
      this.selectedCustomerGroupList = this.selectedCustomerGroupList.filter(it => value.includes(it.id))
    },
    onTradeAndProductTypeChange(value) {
      this.selectedTradeAndProductTypeList = this.selectedTradeAndProductTypeList.filter(it => value.includes(it.idx))
    },
    async loadProvinces() {
      const {data} = await Axios.post('/city/selectProvince')
      this.provinces = Object.freeze(data.data)
    },
    async loadCountry() {
      const {data} = await Axios.post('/city/selectNation', JSON2FormData({type: 3}))
      this.countries = data.data.map(item => {
        const _item = {...item}
        _item['f_area_code'] = Math.random().toString(32).slice(2)
        _item.name = _item.nation
        return _item
      })
    },
    async loadArea(f_parent_id, f_province, ignoreClear) {
      const key = f_parent_id === -1 ? 'city' : 'district'
      !ignoreClear && this.clear(...new Set([key, 'district']))
      if (typeof f_parent_id === 'string')
        f_parent_id = (this.city.find(({f_city_name}) => f_city_name === f_parent_id) || {})['f_city_id']
      if (!f_parent_id) return
      const post = JSON2FormData({f_parent_id, f_province})
      const {data} = await Axios.post('/city/select', post)
      this[key] = Object.freeze(data.data)
    },
    countryChange(value) {
      if (value === '中国' || !value) {
        this.showPCD = true
      } else {
        this.clear(...'province,city,district'.split(','))
        this.showPCD = false
      }
    },
    get(key) {
      return this[key]
    },
    async openTradeAndProductTypeSelector() {
      try {
        this.selectedTradeAndProductTypeList = await this.$refs.tpts.open(this.form.tradeAndProductTypeList)
        this.form.tradeAndProductTypeList = this.selectedTradeAndProductTypeList.map(it => it.idx)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
      }
    }
  },
  mounted() {
    this.getItemKindCode('key_market')
    this.getItemKindCode('nature')
    this.loadProvinces()
    this.loadCountry()
  }
}
</script>


<style scoped>
.el-form-item {
  margin-bottom: 0;
}

.el-input, .el-input, .el-select, .el-select {
  width: 100%;
}

.text-right {
  text-align: right;
}

.el-upload {
  width: 188px;
  height: 134px;
  /* margin-top: 10px;
  padding: 10px; */
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.el-upload img {
  width: 188px;
  height: 134px;
}

.el-upload .el-upload__text {
  line-height: 49px;
  font-size: 20px;
  color: #ccc;
}

.el-upload i {
  font-size: 22px;
  /* font-size: 40px; */
  color: white;
  font-weight: bold;
  padding-top: 20px;
}

.el-input__inner, .el-textarea__inner, .el-upload {
  border: 1px solid #e3e3e3;
  border-radius: 2px;
}

.el-button {
  border-radius: 0;
}

body .el-popper[x-placement^=bottom] {
  margin-top: 0;
}

body .el-dropdown-menu {
  margin: 0;
  padding: 0;
  border-radius: 0;
  min-width: 120px;
}

body .el-dropdown-menu .popper__arrow {
  display: none;
}

.el-input.el-input-fixWidth {
  width: 205px;
}

.cursor {
  cursor: pointer;
}

.formSelf .upload-tip:after {
  content: '+';
  position: absolute;
  top: 35px;
  left: 0;
  width: 100%;
  transform: scale(3);
  text-align: center;
  font-size: 25px;
}

.formSelf .upload-tip {
  position: relative;
  overflow: hidden;
  color: #cccccc;
  font-size: 18px;
  padding-top: 90px;
  line-height: 20px;
  text-align: center;
}

.box-uploader .upload-img {
  position: relative;
}

.box-uploader .upload-img:after {
  transition: all .3s;
  content: '';
  padding-top: 50px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* border-radius: 50%; */
}

/* .box-uploader .upload-img:after { */
.box-uploader:hover .upload-img:after {
  /* content: '修 改'; */
  font-size: 30px;
  background-color: #33333355;
}

.flex-el-link {
  transition: all .3s;
}

.flex-el-link.hover,
.flex-el-link:hover {
  background-color: #33333355;
}

.flex-el-link .el-link:hover i {
  color: white !important;
}

.flex-el-link .el-link i {
  font-size: 20px;
  color: #ffffffab !important;
}

.flex-el-link .el-link {
  display: none;
}

.flex-el-link.hover .el-link,
.flex-el-link:hover .el-link {
  display: inline-block;
}

.el-image-viewer__btn i {
  color: white !important;
}
</style>