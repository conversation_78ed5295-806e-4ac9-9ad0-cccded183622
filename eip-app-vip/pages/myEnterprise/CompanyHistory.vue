<template>
  <custom-dialog
    title="查看历史企业"
    :show="show"
    width="700px"
    @before-close="close"
  >
    <el-main>
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="200px"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        :index="false"
        :show-operation="false"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
      </alex-table>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue";
import AlexTable from "../components/AlexTable.vue";

export default {
  name: "CompanyHistory",
  components: {AlexTable, CustomDialog},
  mixins: [Mixins.table('getCompanyHistory')],
  data() {
    return {
      show: false,
      loading: false,
      total: 0,
      tableData: [],
      fields: [
        {
          label: '企业名称',
          prop: 'companyName',
          width: 300,
          sortable: false,
        },
        {
          label: '加入时间',
          prop: 'bindTime',
          sortable: false,
        },
        {
          label: '退出时间',
          prop: 'unbindTime',
          sortable: false,
        }
      ]
    }
  },
  methods: {
    open() {
      this.show = true
      this.refresh()
    },
    close() {
      this.show = false
    },
    async getCompanyHistory(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        const {data} = await Axios.post('/enterprisePerson/getExitEnterprise', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')
        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = +data.total
        this.tableData = data.rows
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
