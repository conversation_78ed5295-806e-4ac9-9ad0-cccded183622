<!-- 历届参展人员-->
<!DOCTYPE html>
<html style="background-color: white;">
	<head>
		<meta charset="UTF-8">
	<title>我的客户</title>
	<script src="../../js/common/variable.js"></script>
	<style>
		#frame{
			height: 100%;
			overflow: auto;
			border: 2px solid red;
			
		}
		body a{
			outline:none;
			blr:expression(this.onFocus=this.blur());
			}
		.top-title span{
			font-size: 15px;
		}
		.sub-state span{
			color: #AF3737;
		}
		.card-title{
			margin:  14px 0 20px 0;
			font-size: 22px;
				text-align: center;
			
		}
		.foot{
		    position: fixed;
		    left: 0px;
		    right: 0;
		    bottom: 0;
		    height: 44px;
		    line-height: 44px;
		    background-color: #eee;
		    text-align: center;
		}
		/*.laytable-cell-1-0-2 {width: 170px;}*/
/* 		.layui-fluid {
 
    margin-left: 45px;
} */

	</style>
	</head>
	<body style="background-color: white;">
		 
		 <div class="layui-fluid" style="min-height: 780px;">
		 	 <div class="layui-row card-title">
	  		 	<h2 style="font-size: 18px;font-weight: bold;color: #333;">人员信息</h2>
	  		 </div>
		<!-- <table class="layui-table" lay-data="{height:auto,page:true,limit: 5, id:'test',toolbar: 'default'}" lay-filter="test" id="test">-->
		 <table class="layui-table" lay-data="{ url:'', page:true, limit: 5,id:'test',toolbar: 'default'}" lay-filter="test">
				<thead>
				    <tr>
				      <th lay-data="{field:'0',  type: 'checkbox' }"></th>
				      <th lay-data="{field:'x', width:100,  sort: true,type:'numbers'}">序号</th>
				      <th lay-data="{field:'1'}">姓名</th>
				      <th lay-data="{field:'2'}">英文名</th>
				      <th lay-data="{field:'3'}">性别</th>
				      <th lay-data="{field:'4'}">职务</th>
				      <th lay-data="{field:'5'}">客户地址</th>
				      <th lay-data="{field:'6'}">邮箱</th>
				      <th lay-data="{field:'7'}">手机</th>
				      <th lay-data="{field:'8'}">备注</th>
				    </tr> 
				  </thead>
			  <tbody>
			  	<tr>
			  		<td>1</td>
			  		<td>1</td>
			  		<td>张丽</td>
			  		<td>Zhang Li</td>
			  		<td>女</td>
			  		<td>Sales Man</td>
			  		<td>3266591942****</td>
			  		<td><EMAIL></td>
			  		<td>136645****</td>
			  		<td>No</td>
			  		<td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td>
			  		<td>1</td>
			  		<td>张丽</td>
			  		<td>Zhang Li</td>
			  		<td>女</td>
			  		<td>Sales Man</td>
			  		<td>3266591942****</td>
			  		<td><EMAIL></td>
			  		<td>136645****</td>
			  		<td>No</td>
			  		<td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  	<tr>
			  		<td>1</td><td>1</td><td>张丽</td><td>Zhang Li</td><td>女</td><td>Sales Man</td><td>浙江省杭州市江干区九堡</td><td><EMAIL></td><td>136645****</td><td></td><td></td>
			  	</tr>
			  </tbody>
		 </table>
		 
 
		 <!--<div class="layui-row">
		 	<div class="layui-col-lg1 layui-col-lg-offset11 " >
		 		<a class="layui-btn layui-btn-normal layui-btn-xs"   > 导出 </a>
		 	</div>
		 	
		 </div>-->
		 <div class="layui-footer foot">
	     © 杭州展之信息技术有限公司 提供技术支持
	 	</div> 
		 </div>
		 
  		 
  		
<script>
layui.use('table', function(){
  var table = layui.table;
  var element = layui.element;
  /*table.init('demo', {
 	   height: 315 //设置高度
	  ,limit: 20 //注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
	  //支持所有基础参数
	}); */
});
$(document).on("click",".layui-table-body table.layui-table tbody tr",function(){
    var obj = event ? event.target : event.srcElement;
    var tag = obj.tagName;
    var checkbox = $(this).find("td div.laytable-cell-checkbox div.layui-form-checkbox I");
    if(checkbox.length!=0){
        checkbox.click();
    }
    
});


$(document).on("click","td div.laytable-cell-checkbox div.layui-form-checkbox",function(e){
    e.stopPropagation();

});

</script>
	
</body>
</html>
