<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>我创建的展会</title>
	<link href="./vue/element-ui/index.css" rel="stylesheet" type="text/css"/>

	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" />
	<link rel="stylesheet" href="../css/allcsss.css" />
	<link rel="stylesheet" href="../css/vip.css" />
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<script src="../js/jquery.form.js"></script>
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<script src="./vue/element-ui/index.js"></script>
	<!-- <script src="./vue/wangEditor.min.js"></script> -->

	<link rel="stylesheet" href="../css/member-theme.css">
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
	</script>
</head>
<body class="myCreateExhibition">

<el-container id="app" v-cloak>
	<el-header height="auto" class="page_header">
		<div class="flex-between" style="align-items: center;">
			<h1 class="page_title">我创建的展会</h1>
			<el-button type="primary" size="mini" icon="el-icon-plus"
				:style="{height: '35px',borderRadius: '5px'}" @click="addExhiClick">新建展会</el-button>
		</div>
		<el-form :inline="true" :model="form" size="mini">
			<div>
				<label>举办时间</label>
				<el-radio-group v-model.trim="form.searchYear" size="mini">
					<el-radio label="全部"></el-radio>
					<el-radio label="近期开展的展会"></el-radio>
					<el-radio v-for="it in searchYears" :label="it"></el-radio>
				</el-radio-group>
			</div>
			<div>
				<label style="margin-right: 30px;">
					展会搜索
				</label>
				<!-- <el-form-item label="参与类别">
					<el-select v-model="form.f_proj_role_id">
						<el-option label="所有" value=""></el-option>
						<el-option label="主办方" value="1"></el-option>
						<el-option label="展商" value="2"></el-option>
						<el-option label="服务商" value="3"></el-option>
						<el-option label="观众" value="4"></el-option>
					</el-select>
				</el-form-item> -->

				<el-form-item label="">
					<el-input v-model.trim="form.projectName"
						@keyup.enter.native="search"
						placeholder="搜索展会名称"/>
				</el-form-item>

				<el-form-item label="">
					<el-date-picker
					 	v-model.trim="form.startTime"
						value-format="yyyy-MM-dd"
						type="date"
						placeholder="开始日期">
					</el-date-picker>
				</el-form-item>
				<!-- <el-form-item label="到" class="rang">
				</el-form-item> -->
				<el-form-item label="">
					<el-date-picker
						v-model.trim="form.endTime"
						value-format="yyyy-MM-dd"
						type="date"
						placeholder="结束日期">
					</el-date-picker>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="search">查询</el-button>
				</el-form-item>
			</div>
		</el-form>
	</el-header>
	<el-main class="app_main">
		<div class="app_main_content"
			v-infinite-scroll="load"
			infinite-scroll-disabled="disabled"
			v-loading="loading"
			>
			<div class="item" v-for="item in showData" :key="item.f_exhibit_code" :title="item.f_project_name || ''">
				<div class="item-img" style="position: relative;width: 100%;">
					<el-image :src=`${host}/image/${item.projectPicUrl}` @click.native="jump(item)" fit="none" style="width:100%; height: 100%">
						<div slot="error" class="image-slot errorImg" >
							<i class="el-icon-picture-outline"></i>
						</div>
					</el-image>
					<el-dropdown @command="itemHandle" class="more" :show-timeout="Number(50)">
						<i class="el-icon-more"></i>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item :command="'del-'+item.f_project_id"><i class="el-icon-delete-solid"></i> 删除项目</el-dropdown-item>
							<el-dropdown-item :command="'show-'+item.f_project_id"><i class="el-icon-position"></i> 预览项目</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
				<div class="item_info" @click="jump(item)" >
					<span class="item_title text-over-2">
						{{item.f_project_name || ''}}
						<!-- <span class="tag" :class="[{tag_1: item.f_proj_role_id ==1}, {tag_2: item.f_proj_role_id ==2}, {tag_4: item.f_proj_role_id ==4}]">{{item.f_proj_role_id | types}}</span> -->
					</span>
					<span class="item_sub">
						<span  v-if="item.f_start_time || item.f_end_time">{{item.f_start_time | timeFormat}} - {{item.f_end_time | timeFormat}}</span>
					</span>
					<span class="flex-between item_sub">
						<span class=" text-over-1">{{item.f_exhibit_place || ''}}</span>
						<span v-if="item.publishFlag" style="color:#FB6F22">已发布</span>
					</span>
				</div>
			</div>
		</div>
		<!-- <center>
			<el-pagination
				background
				layout="prev, pager, next, jumper"
				:total="total"
			>
			</el-pagination>
		</center> -->
	</el-main>
	<el-footer class="app_foot">
	</el-footer>

	<comp-tip ref="tipDel" msg="删除后无法恢复，确认要删除 【项目名称】吗？" @sure="delTipSureClick"></comp-tip>
	<comp-tip ref="tipAuth" msg="需要认证商家信息才可新建展会。"
		@sure="authTipSureClick"
		sureText="前去认证"
		></comp-tip>
</el-container>

<script src="../js/common/_vipMixin.js?v=230628"></script>
<script>
const app = new Vue({
	el: "#app",
	mixins: [_vipMixinCommon],
	data() {
		return {
			form: {
				startTime: '',
				endTime: '',
				projectName: '',
				searchYear: '全部',
			},
			page: 1,
			size: 20,
			total: 0,
			loading: false,
			showData: [],
			searchYears: [],
			delTip: {
				projectId: '',
			},
			isAuth: false,
		}
	},
	computed: {
		recentExhibitState() {
			const tmp = parseInt(this.form.searchYear === '近期开展的展会' ? 1 : this.form.searchYear) // int / NaN
			return tmp ? tmp : '';
		},
		endExhibitTimeFlag() {
			return !!this.searchYears.length && this.form.searchYear === this.searchYears[this.searchYears.length -1]
		},
	},
	watch: {
		'form.searchYear'(val, oldVal) {
			this.page = 1;
			this.getData()
		}
	},
	methods: {
		addExhiClick() {
			if(this.isAuth){
				window.location.href = 'exhi_set.html?token=' + token;
			}	else {
				this.openTip('tipAuth')
			}
		},
		authTipSureClick() {
			window.location.href = 'member_auth.html?token=' + token;
		},
		disabled () {
			return this.loading || this.page*this.size < this.total
		},
		async getData() {
			this.loading = true
			// recentExhibitState: '', // 1:近期开展
			// endExhibitTimeFlag: false, // 多少年以后
			let params = {
				startTime: this.form.startTime,
				endTime: this.form.endTime,
				recentExhibitState: this.recentExhibitState,
				endExhibitTimeFlag: this.endExhibitTimeFlag,
				projectName : this.form.projectName,
			}
			params.page = this.page
			params.rows = this.size
			try {
				let data = await this._getExhList(params)
				this.showData = data.rows
				this.total 	  = +data.total
				// console.dir(this.showData)
			} finally{
				this.loading = false
			}
		},
		async getSearchYears() {
			try {
				let {data} = await _axios.post("project/getQueryCondition", this.obj2FormData({
					orgNum: this.orgnum,
					commonId: this.zzyUserId,
				}))
				this.checkReturn(data)
				this.searchYears = data.data || []
			} catch (e) {
				console.log(e)
			}
		},
		async delTipSureClick() {
			// const data = {
			// 	state: -1,
			// 	msg: 'test',
			// };
			try{
				let {data} = await _axios.post("project/delete", this.obj2FormData({
					projectId: this.delTip.projectId,
					commonId: this.zzyUserId,
				}))
				// console.log('del', data)
				this.checkReturn(data, '删除失败','删除成功')
				this.getData();
			} finally {
				this.closeTip('tipDel')
			}
		},
		itemHandle(command) {
			const that = this
			if(command.startsWith('del-')) { // 删除项目
				that.delTip.projectId = +(command.substr(4))
				that.openTip('tipDel')
			} else if(command.startsWith('show-')) { // 门户预览
				window.open(window.origin+'/trade-web-site4/onlineExhibition/details?id='+command.substr(5))
			}
		},
		jump({f_url, f_server_ip, f_server_port, f_proj_role_id, f_client_id, f_project_id, f_project_name, f_exhibit_code, f_proj_id,f_create_org_id,f_zzyuser_id}) {
			location.href = 'exhi_detail.html?token='+token +'&pid=' +  f_project_id;
			// TODO 跳转行业网站
			// let url = `http://${f_server_ip}:${f_server_port}/`
			// if (f_proj_role_id == 1) { // 主办方
			// 	url += `eip-web-sponsor/backstage/indexA?m=${this.user_name}`
			// 	window.open(url)
			// } else if(f_proj_role_id == 2) { // 展商
			// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&cid=${f_client_id}&pid=${f_project_id}&ename=${f_project_name}&exhibitCode=${f_exhibit_code}&typem=2&m=${this.user_name}`
			// 	window.open(url)
			// } else if(f_proj_role_id == 4) {
			// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&pid=${f_project_id}&ename=${f_project_name}&exhibitCode=${f_exhibit_code}&typem=4&m=${this.user_name}`
			// 	window.open(url)
			// } else if(!f_proj_role_id){ // 未关联项目
			// 	// 此时没有zzyuserId 取登录用户的
			// 	f_zzyuser_id = uinfo.zzyUserId || ''
			// 	// 会员关联项目
			// 	const params = new FormData()
			// 	params.append('zzyuserId', f_zzyuser_id)
			// 	params.append('orgNum', f_create_org_id || '')//localStorage.getItem('orgNum')
			// 	params.append('projId', f_proj_id)
			// 	try{
			// 			Axios.post("http://" + variable + "/tradeProject/queryRelationFocus", params).then(res => {
			// 					let {data: {data, state, msg}} = res;
			// 					if (state == 1) {
			// 							window.open(`${url}eip-web-business/pages/boothinfo/exhibitor_member.html?zzyuserId=${f_zzyuser_id}&pid=${f_project_id}&exhibitCode=${f_exhibit_code}&typem=4&m=${this.user_name}&ename=${f_project_name}`)
			// 					}else{
			// 							throw new Error(msg)
			// 					}
			// 			}).catch(e => {
			// 					this.$message.error('关联项目失败 : '+ e.message)
			// 			})
			// 	}catch(e){
			// 			this.$message.error('关联项目失败 : '+ e.message)
			// 			console.dir(e)
			// 	}
			// }else{
			// 		window.open(url)
			// }
		},
		load(){
			this.page ++
			this.getData()
		},
		async search() {
			this.page = 1
			await this.getData()
		},
	},
	async mounted() {
		this._loading()
		const [,,isAuth] = await Promise.all([this.search(),this.getSearchYears(),this._getAuth()])
		this.isAuth = !!isAuth
		this.$nextTick(_ => {
			this._loaded()
		})
	},
})
</script>
</body>
</html>