<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>我的浏览</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" href="../css/vip.css" >
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<!-- <script src="../js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<link rel="stylesheet" href="./vue/element-ui/index.css" >
	<!-- <script src="./vue/element-ui/index.js"></script> -->
	<script src="/eip-web-vip/lib/element-ui2.15.14/index.min.js"></script>

	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
  <script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
			var qs = '?token=' + token;
	</script>
</head>
<body class="myBrowse flex">
<el-container id="app" v-cloak>
	<el-header class="app_header" height="0"></el-header>
	<el-main class="app_main">
		<div style="display: grid;grid-template-columns: repeat(3,auto);grid-gap: 0 27px;padding-top: 16px;">
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/shop.png" alt="">
				<div>
					<b>已收藏商家</b>
					<div>{{ detailOther.collectMerchantNum || 0 }}</div>
				</div>
			</div>
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/cube.png" alt="">
				<div>
					<b>已收藏产品和服务</b>
					<div>{{ detailOther.collectProductNum || 0 }}</div>
				</div>
			</div>
			<div class="countBlock flex-center-h">
				<img src="../img/newvip/shoppingcard.png" alt="">
				<div>
					<b>已收藏采购信息</b>
					<div>{{ detailOther.collectRequireNum || 0 }}</div>
				</div>
			</div>
		</div>

		<div class="main-block">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">最近收藏的产品</span>
				<!-- <div style="flex:1"></div>
				<comp-link v-if="detailOther.serveProducts && detailOther.serveProducts.length" @click="$message.info('TODO')">更多  <i class="el-icon-arrow-right"></i> </comp-link> -->
			</div>
			<center v-if="!detailOther.serveProducts && !detailOther.serveProducts.length" class="empty">暂无收藏的产品信息</center>
			<div v-else  class="data flex" style="margin: 20px 0 0;">
				<comp-card-product :item="item" v-for="(item, index) in detailOther.serveProducts" :key="item.serveProductId" />
			</div>
		</div>

		<div class="main-block border">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">最近收藏的商家</span>
				<!-- <div style="flex:1"></div>
				<comp-link v-if="detailOther.catalogues && detailOther.catalogues.length" @click="$message.info('TODO')">更多  <i class="el-icon-arrow-right"></i> </comp-link> -->
			</div>
			<center v-if="!detailOther.catalogues || !detailOther.catalogues.length" class="empty">暂无收藏的商家信息</center>
			<div v-else  class="data flex"  style="margin: 20px 0 0;">
				<comp-card-merchant-slim :item="item" v-for="(item,index) in detailOther.catalogues" :key="item.f_serve_catalogue_id"  />
			</div>
		</div>
		<div class="main-block border">
			<div class="main-block-title flex-center-h">
				<span style="font-size: 16px;">我的询盘</span>
				<div style="flex:1"></div>
				<!-- <comp-link v-if="detailOther.serveInquirys && detailOther.serveInquirys.length"  @click="goInquireList">更多  <i class="el-icon-arrow-right"></i> </comp-link> -->
				<el-link :underline="false" v-if="detailOther.serveInquirys && detailOther.serveInquirys.length"  @click="goInquireList">更多  <i class="el-icon-arrow-right"></i> </el-link>
			</div>
			<center v-if="!detailOther.serveInquirys || !detailOther.serveInquirys.length" class="empty">暂无公司询盘信息</center>
			<center v-else  class="data">
				<el-table
					:data="detailOther.serveInquirys"
					style="width: 100%"
					>
					<el-table-column
						prop="companyName"
						label="对方公司">
					</el-table-column>
					<el-table-column
						prop="inquiryPeople"
						label="联系人"
						width="100" >
					</el-table-column>
					<el-table-column
						prop="inquiryPhone"
						label="手机号"
						width="100">
					</el-table-column>
					<el-table-column
						prop="inquiryEmail"
						label="联系邮箱"
						width="100">
					</el-table-column>
					<el-table-column
						prop="productName"
						label="询盘产品">
						<template  slot-scope="{row}">
							<el-link :underline="false" type="primary" @click="goGatewayInquire(row.commonId)">
								{{ row.productName  || ''}}
							</el-link>
						</template>
					</el-table-column>
					<el-table-column
						prop="lastMsgTime"
						label="对方最后回复时间">
					</el-table-column>
					<el-table-column
						prop="msgContent"
						label="回复内容">
						<template slot-scope="{ row }">
							<el-link :underline="false" @click="goGatewayInquire(row)">
								<span v-if="+row.lastMsgState === 1" style="color: #F74444">【未读】</span>
								{{ row.lastMsgContent || '' }}
							</el-link>
						</template>
					</el-table-column>
				</el-table>
			</center>
		</div>
	</el-main>
	<div style="flex: 1"></div>
	<!-- <el-footer class="app_foot" height="0"></el-footer> -->
	<!-- <comp-tip ref="tipEditor" :title="editor.title" @sure="sureEditor" type="form" width="650px">
		<div style="height: 350px;box-sizing: border-box;margin-top: 20px;">
			<div ref="jsEditor" ></div>
		</div>
	</comp-tip> -->
</el-container>

<script src="../js/common/_vipMixin.js?v=231103"></script>
<script>
$(function(){
	window.app = new Vue({
		el: '#app',
		mixins: [_vipMixinCommon],
		components: {compCardProduct, compCardMerchantSlim,},
		data: {
			detailOther: {
				// pageViewNum: 0,   // 阅读量或点击量
        // favoriteNum: 0,   // 收藏量（展品）
        // inquiryNum: 0,    // 被询盘量
        collectMerchantNum: 0,
        collectProductNum: 0,
        collectRequireNum: 0,
        serveProducts: [], 				 // 收藏的展品数据
				// cataLogueList: [], // 提交的会刊数据
				// companyList: [],   // 公司信息
				// serveProductFavorites: [], // 收藏的展品数据
				catalogues: [], 					 // 收藏的商家信息
				serveInquirys: [],				 // 被询盘的展品数据
			},
		},
		// async mounted() {
		// },
		// computed: {
		// 	merchantLink() { // 行业网站商家链接
		// 		return window.origin + '/trade-web-site4/MerchantsDetails/'+ this.detail.clientId + '?p='+ this.detail.projectId
		// 	},
		// },
		methods: {
			async init() {
				this._loading()
				// 我的浏览-商家
				const {data: dataOther} = await _axios.post('admin/merchant/getMyBrowseInfo', this.obj2FormData({
					zzyUserId: this.zzyUserId,
					orgNum:this.orgnum
				}))
				this.checkReturn(dataOther)
				this.detailOther = dataOther.data
				this.$nextTick(_ => {
					this._loaded()
				})
			},
			goGatewayInquire(row) {
				if(typeof row == 'object') {
					var info = btoa(JSON.stringify({
						identity: 'CLIENT',
						f: 'open',
						iid: row.inquiryMsgId,
					}));
					window.open(hostGate + '/ProductDetails/'+row.commonId + qs + '&info=' + info, '_blank')
				} else {
					window.open(hostGate + '/ProductDetails/'+row + qs, '_blank')
				}
			},
			goInquireList() {
				window.location.href = './sponsor/my-inquire.html' + qs;
			},
		}
	});
	app.init();
});
</script>
</body>
</html>