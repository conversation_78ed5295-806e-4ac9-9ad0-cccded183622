<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>我发布的采购</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" href="../css/vip.css" >
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<!-- <script src="../js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<link rel="stylesheet" href="./vue/element-ui/index.css" >
	<script src="./vue/element-ui/index.js"></script>
	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
		  var uinfo = util.checkLogin();
			var token = uinfo.token;
	</script>
</head>
<body class="myPublishPurchase" style="padding: 27px 19px 0;">
<div style="padding: 8px 25px 5px;background-color: white;box-sizing: border-box;">
	<table lay-filter="mainTable" id="mainTable"></table>
	<script type="text/html" id="mainTableToolBar">
		<div class="layui-btn-container">
			<div class="layui-inline" lay-event="delete">
				<i class="layui-icon layui-icon-delete"></i>
			</div>
		</div>
	</script>
	<script type="text/html" id="mainTableBar">
		<a href="javascript:void(0);" onclick="app.goGateway({{ d.requireId }})" class="">查看</a>
	</script>
	<script type="text/html" id="mainTableCheckState">
		<span class="checkState{{ d.checkState }}">{{ app.getFmtState(d.checkState) }} </span>
	</script>
	<script type="text/html" id="mainTableTradeName">
		{{ d.productTypeBaseName ? (d.tradeName + ' - ' + d.productTypeBaseName ) :  d.tradeName }}
	</script>
</div>
<script src="../js/common/_vipMixin.js?v=20220902"></script>
<script>
var table = layui.table;
function initRender() {
	table.render({
		elem: '#mainTable',
		toolbar: '#mainTableToolBar',
		url: '/eip-web-vip/requirement/selectList',
		// skin: 'row',
		height: 'full-80',
		loading: true,
		title: '我发布的采购',
    // autoSort: false, // layui2.4.4+
		cols:  [[
			{type: 'radio',width:60,}, //,LAY_CHECKED: true
			{type: 'numbers', title: '序号', width: 60,hideDisable:true,sort: true,},
			{field: 'publishTime', title: '求购日期', width: 180,sort: true,},
			{field: 'title', title: '求购标题', minWidth: 200,hideDisable:true,},
			{field: 'tradeName', title: '求购分类', width: 180, templet: '#mainTableTradeName', },
			{field: 'quantity', title: '求购数量', width: 120,sort: true,},
			{field: 'validUntil', title: '有效期', width: 180,sort: true,},
			{field: 'checkState', title: '状态', width: 180,sort: true,templet: '#mainTableCheckState',},
			{align:'left',title: '操作',toolbar: '#mainTableBar'},
		]],
		where: {
			orgNum: app.orgnum,
			zzyuserId: app.zzyUserId,
		},
		method: 'post',
		request: { pageName: 'page',limitName: 'rows' },
    // size: 'sm',
		page : {
			limits: [10,30],
			limit: 10,
			layout:['prev','page','next','skip','limit','count','refresh'],
		},
		response: {
			statusName: 'state',
			statusCode: 1,
			countName: 'total',
			dataName: 'rows',
		},
		// done(res, page, count){ // 渲染成功后
    //   // 异步 res为接口返回,直接赋值 res为：{data: [], count: 99}
    //   // console.log(res);
    //   // res.data[0].username = 'done-edit';
    //   // console.log(page,count);
    //   // res.count = 100;
    // },
	});
	table.on('toolbar(mainTable)', function(obj) {
		var checkStatus = table.checkStatus(obj.config.id);
		switch (obj.event) {
			case 'delete':
				app.delete(checkStatus.data[0]);
				break;
		}
	});
	$('body').on("click","tr",function(obj) {
		if(!$(obj.currentTarget).hasClass('layui-table-click')) {
			// var obj = event ? event.target : event.srcElement;
			// var tag = obj.tagName;
			var checkbox = $(this).find(".laytable-cell-checkbox i,.laytable-cell-radio i");
			if(checkbox.length) checkbox.eq(0).click();
		}
	});

}
$(function() {
	window.app = new Vue({
		// el: '#app',
		mixins: [_vipMixinCommon],
		// async mounted() {
		// },
		// computed: {
		// },
		methods: {
			async init() {
				this._loading()
				initRender();
				this.$nextTick(_ => {
					this._loaded()
				})
			},
			goGateway(requireId) {
				requireId && window.open(hostGate + '/supplyDemand/details/' + requireId, '_blank');
			},
			getFmtState(val,row,idx) {
				// 1 草稿 2 审核 9 审核不通过
				val = +val
				let ret = ''
				if(val === 1){
					ret = '草稿'
				} else if (val === 2) {
					ret = '审核'
				} else if (val === 9) {
					ret = '审核不通过'
				}
				return ret || '';
			},
			async deleteById(requirementId){ // 删除采购
				const {data} = await _axiosSite.post('api/requirement/delRequirement', this.obj2FormData({
					requirementId,
				}))
				this.checkReturn(data);
				table.reload('mainTable');
				this.$message.success('删除成功');
				// table.reload('mainTable',{page: {curr: 1}})
			},
			delete(data) {
				const that = this
				layer.confirm('是否确定删除 <span style="color:red">' + data.title + '</span> ?', {icon: 3, title:'提示'}, async(index) =>{
					await that.deleteById(data.requireId);
					layer.close(index);
				});
				// this.$confirm('是否确定删除?','提示', {
				// 	confirmButtonText: '仍要撤销',
				// 	cancelButtonText: '不小心点错',
				// 	type: 'warning',
				// }).then(() => {
				// 	// this.changeProjectState({isRelease: false})
				// }).catch( _ => {
				// });
			},
		}
	});
	app.init();
});
</script>
</body>
</html>