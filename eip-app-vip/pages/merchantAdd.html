<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
	<meta name="format-detection" content="telephone=no">
	<meta charset="UTF-8">
	<title>商家发布</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" type="text/css" href="../css/cropper.min.css" />
	<link rel="stylesheet" type="text/css" href="../css/ImgCropping.css" />
	<link rel="stylesheet" href="../css/allcsss.css?v=20220802" />
	<link rel="stylesheet" type="text/css" href="../css/member_catalog.css?v=2022082601" />
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>

	<script src="./vue/vue.js"></script>
	<!-- <script src="../js/vue2.dev.js"></script> -->
	<script src="./vue/axions.js"></script>
	<script src="../js/loading.js?v=20220802"></script>
	<!-- <script src="/eip-web-business/js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js?v=20220829"></script>
	<!-- <script src="../js/common/xlsx.full.min.js"></script> -->
	<!-- <script src="../../frontend/js/jquery.params.js"></script> -->
	<script src="../js/cropper.min.js"></script>
	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/boothinfo/member_catalog.js?v=2022090701"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js?v=230718"></script>
	<script>
			var uinfo = _util.checkLogin();
			var token = uinfo.token;
	</script>
	<style>
		html{ overflow: hidden;}
	 .layui-form-pane .layui-form-label,.layui-form-pane .layui-input{height:32px !important;line-height:15px;}
	 .upload-tip{
		flex: 1
	 }
	 .set-bottom-right{
		 width: 146px;height: 146px;box-sizing: border-box;
		 position: relative;
		 background-position: center 30px;
		 background-size: 43px 43px;
		}
		.set-bottom-right p i{
			position: relative !important;
			left: 0 !important;
		}
		body .set-bottom-right p{
			position: absolute;
			bottom: 20px;
			width: 100%;
			left: 0;
			text-align: center;
			background: unset !important;
			border: unset !important;
			color: #ccc;
		}
	</style>
</head>
<body style="background-color: #f0f2f5;overflow: hidden;">
	<div class="flex flex-h" style="height: 100vh;">
		<div class="flex-between" style="line-height: 54px;padding: 0 20px;height: 54px;background-color: white;border-bottom: 1px solid #e9e9e9;">
			<div style="color: #009688;">
				<span class="layui-breadcrumb">
					<a href="#">参展活动管理</a>
					<a href="javascript:;" onclick="history.go(-1)">参展信息填报</a>
					<a><cite class="taskName">商家发布信息填报</cite></a>
				</span>
			</div>
			<div onclick="history.go(-1)" style="cursor:pointer;">
				<img src="../img/goback.png" alt="" style="display: inline-block;
				margin-right: 5px;
				margin-top: -3px;">
				<span>返回上级</span>
			</div>
		</div>
		<div class="layui-container" style="flex: 1;height: calc(100vh - 114px);box-sizing: border-box;
			overflow-y: scroll;margin: 24px 20px 60px;padding: 0;
			background-color: white;width: calc(100% - 20px);">
			<div id="app">
				<div class="flex-h" style="font-size: 18px;border-bottom: solid 1px #f0f2f5;padding: 10px 24px;">
					<h1 class="taskName" style="font-size: 18px;display: inline-block;font-weight: normal;">{{ taskName || ''}}</h1>
					<span :class="['state','state'+confirm]"></span>
					<span v-cloak>{{ confirmTip || '' }}</span>
				</div>
				<div class="layui-row"
					v-show="isTip"
					v-cloak
					style="
						border: 1px solid rgba(0,0,0,0.15);
						border-radius: 2px;
						margin: 19px 24px 0;
						padding: 21px 40px;
					">
					<h6 class="titleH6 mt10" style="display: flex;align-items: center;">
						<span style="float: left;color: rgba(0,0,0,0.85);font-size: 16px;font-family: Microsoft YaHei, Microsoft YaHei-Regular;">填写说明</span>
						<span id="explain-attachment" style="float: left;margin-left: 20px;"></span>
					</h6>
					<div class="layui-fluid file-remark" id="completion_inst"  v-cloak v-html="completion_inst"></div>
				</div>
			</div>
			<!-- 主体 -->
			<div class="layui-container fly-marginTop" style="width: 100%;margin-top: 0;">
				<div class="layui-form layui-form-pane">
					<div class="layui-tab layui-tab-brief" lay-filter="user">
						<div class="layui-form layui-tab-content" id="LAY_ucm" style="padding: 0 0 20px;">
							<div class="layui-tab-item layui-show" style="">
								<!--会刊信息-->
								<form class="layui-form layui-form-pane only-fr" action="" lay-filter="formTest" id="formTest">
									<h6 class="hk-titles main-title" style="border:unset;margin-top: 0;">会刊信息</h6>
									<button type="button" class="layui-btn flex-center-w" onclick="importCompanyInfo()" style="margin-bottom: 20px;" id="importCorp">
										<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20" fill="white"><defs><style>.b{clip-path:url(#a);}.c{fill:none;}</style><clipPath id="a"><rect class="a" width="14" height="17" transform="translate(0 -0.359)"/></clipPath></defs><g transform="translate(-287 -237)"><g class="b" transform="translate(290 239.359)"><path class="a" d="M9,11.719l-4-2H4v2H0v2H4v2H5l4-3a.557.557,0,0,0,0-1m5-7-5-4c-.148-.15.209-1,0-1H2a.893.893,0,0,0-1,1v7H3v-6H8v3a1.194,1.194,0,0,0,1,1h3v9H9v2h4a.893.893,0,0,0,1-1v-10c0-.211.147-.851,0-1" transform="translate(-0.003 -0.078)" /></g><rect class="c" width="20" height="20" transform="translate(287 237)"/></g></svg>
										&nbsp;&nbsp;
										<span>一键导入公司信息</span>
									</button>
									<div class="catalog-left" style="float: left; width: 100%">
										<input type="text" id="id_f_serve_catalogue_id" name="f_serve_catalogue_id" hidden="hidden" />
										<input type="text" name="f_client_id" class="f_client_id" hidden="hidden" />
										<input type="text" name="f_project_id" class="f_project_id" hidden="hidden" />
										<input type="text" name="f_task_id" class="f_task_id" hidden="hidden" />
										<input type="text" name="f_custom_field_json" class="f_custom_field_json" hidden="hidden" />
										<input type="text" name="f_cata_product_img" class="f_cata_product_img" hidden="hidden" />
										<input type="text" name="f_cata_attach" class="f_cata_attach" hidden="hidden" />
										<blockquote class="layui-elem-quote" id="text_reason" hidden="hidden">
											<span id="reason" style="color: #FF5722;"></span>
										</blockquote>
										<div class="layui-form-item layui-row layui-inline" >
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;"><i>*</i>公司名称<span id="exhibitor-huikan-f_company_name-show">(中文)</span></label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_company_name" name="f_company_name" autocomplete="off"
													class="layui-input textss" maxlength="100">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label" style="width: 100%;border-right: 0;padding-left: 10px;"><i>*</i>公司名称(英文)</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_company_name_en" name="f_company_name_en"
													autocomplete="off" class="layui-input textss" maxlength="100">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;"><i>*</i>联系人姓名</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_contacts_name" name="f_contacts_name" autocomplete="off"
													class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;padding-left: 0px;padding-right: 0px;"><i>*</i>联系人姓名(英文)</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_contacts_en" name="f_contacts_en" autocomplete="off"
													class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>职务</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_post" name="f_post" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>职务(英文)</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_post_en" name="f_post_en" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司电话</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_tel" name="f_tel" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>传真</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_fax" name="f_fax" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label" style="width: 100%;border-right: 0px;"><i>*</i>E_Mail</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_email" name="f_email" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label" style="width: 100%;border-right: 0px;"><i>*</i>行业分类</label>
											</div>
											<!-- <div class="  layui-col-lg10">
												<select name="tradeId" id="catalogue-settings-checkbox-tradeId" class="textss"></select>
											</div> -->
											<div class="layui-col-lg9" style="width: 45%;float:left;">
												<input type="text" readonly="readonly" name="tradeName" id="catalogue-settings-checkbox-tradeName2" class="layui-input"
													autocomplete="off">
													<input type="hidden" readonly="readonly" name="tradeId" id="catalogue-settings-checkbox-tradeName" class="layui-input textss"
													autocomplete="off">
											</div>
											<div class="layui-col-lg1 show" style="width: 15%;float: left;">
												<input type="button" value="选择" class="layui-input theme-color" onclick="getIndustryDiv(); " style="padding:0;">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>行业分类(英文)</label>
											</div>
											<div class="layui-col-lg10">
												<!-- <select name="tradeIdEn" id="catalogue-settings-checkbox-tradeIdEn" class="textss"></select> -->
												<input type="text" readonly="readonly" name="tradeNameEn" id="catalogue-settings-checkbox-tradeNameEn2" class="layui-input "
													autocomplete="off">
													<input type="hidden" readonly="readonly" name="tradeIdEn" id="catalogue-settings-checkbox-tradeNameEn" class="layui-input textss"
													autocomplete="off">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>展品范围</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" name="f_type_product" id="f_type_product" class="layui-input textss" autocomplete="off">
											</div>
											<!-- <div class="
												layui-col-lg1 show" style="width: 15%;float: left;">
												<input type="button" value="选择" class="layui-input" onclick="getProType(); " style="color:#009E94 ;padding:0;">
											</div> -->
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;padding-left: 10px;"><i>*</i>展品范围(英文)</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" name="f_type_product_en" id="f_type_product_en" autocomplete="off" class="layui-input textss">
											</div>
											<!-- <div class="  layui-col-lg1 show" style="width: 15%; float: left;">
												<input type="button" value="选择" class="layui-input" onclick="getProType(); " style="color:#009E94 ;padding: 0;">
											</div> -->
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司网址</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_site" name="f_site" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司地址</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_company_address" name="f_company_address"
													autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司地址(英文)</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_company_address_en" name="f_company_address_en"
													autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>店铺链接</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_store_link" name="f_store_link" autocomplete="off"
													class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>直播地址</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_live_link" name="f_live_link" autocomplete="off"
													class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>备注</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="catalogue-settings-checkbox-f_memo" name="f_memo" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div>
											<!-- <input style="display:none" type="file" id="companyPicUploader" accept="image/*"> -->
											<div class="layui-form-item layui-row layui-inline"
												style="height: 180px;padding-top: 5px;">
												<div class="layui-col-lg12" style="display: flex;flex-flow:  row nowrap;align-items: center;">
												<!-- <div class="catalog-right" style="float: right;width: 20%; margin-bottom: 100px;margin-top: 40px;position: relative;"> -->
													<div class="set-bottom-right isUploadCompanyImg" id="choiseLogo" onclick="showtrim(1)" style="display: none;cursor: pointer;">
														<p><i>*</i>上传公司图片</p>
														<img src="" alt="" class="layui-upload-img" id="logoImg">
													</div>
													<!-- <div class="non-operational" style="display:none;"></div> -->
													<div class="upload-tip" >推荐上传400*300像素图片<br /> 支持.JPG .JPEG 大小不超过2M，.png格式大小不超过300K</div>
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline"
												style="height: 180px;padding-top: 5px;">
												<div  class="layui-col-lg12" style="display: flex;flex-flow:  row nowrap;align-items: center;">
													<!-- <div class="catalog-right" style="float: right;width: 20%; margin-bottom: 100px;margin-top: 40px;position: relative;"> -->
													<div class="set-bottom-right isUploadLogo" onclick="showtrim(3)" style="display: none;cursor: pointer;">
														<p><i>*</i>上传LOGO</p>
														<img src="" alt="" class="layui-upload-img" id="companyPicImg">
													</div>
													<div class="upload-tip">推荐上传400*400像素图片<br /> 支持.JPG .JPEG 大小不超过2M，.png格式大小不超过300K</div>
													<!-- <div class="non-operational" style="display:none;"></div> -->
													<!-- <p style="margin-top: 74px;" class="isUploadLogo">
														<span style="float: left;">logo上传要求: </span>
														<span style="color: red;display: inline-block;max-width: 210px;" id="uploadLogoClaim"></span>
													</p> -->
												</div>
											</div>
											<div class="layui-form-item layui-row layui-inline"
												style="height: 180px;padding-top: 5px;">
												<div  class="layui-col-lg12" style="display: flex;flex-flow:  row nowrap;align-items: center;">
													<div class="set-bottom-right" onclick="showtrim(4)" style="cursor: pointer;">
														<p><i></i>上传营业执照</p>
														<img src="" alt="" class="layui-upload-img" id="companyAuthImg">
													</div>
													<div class="upload-tip">支持.JPG .JPEG .PNG 格式，大小不超过2M</div>
												</div>
											</div>
										</div>
										<div class="hk_add1"></div>
										<div class="layui-form-item layui-form-text long-text">
											<label class="layui-form-label" style="text-align: center;"><i>*</i>公司简介</label>
											<div class="layui-input-block">
												<textarea placeholder="请输入内容"
												 id="catalogue-settings-checkbox-f_company_profile"
												 class="layui-textarea company-profile textss"
												 name="f_company_profile"></textarea>
											</div>
										</div>
										<div class="layui-form-item layui-form-text long-text">
											<label class="layui-form-label" style="text-align: center;"><i>*</i>公司简介(英文)</label>
											<div class="layui-input-block">
												<textarea placeholder="请输入内容"
												 id="catalogue-settings-checkbox-f_company_profile_en"
												 class="layui-textarea  company_profile_en textss"
												 name="f_company_profile_en"></textarea>
											</div>
										</div>
									</div>
								</form>
								<div class="layui-form-item layui-form-text" style="width: 20%;">
								</div>
								<!--公司信息-->
								<form class="layui-form layui-form-pane" action="" lay-filter="companyForm" id="companyForm">
									<input type="text" id="logo" name="logo" hidden="hidden" />
									<input type="text" id="companyPic" name="pic" hidden="hidden" />
									<input type="text" id="companyAuth" name="businessLicense" hidden="hidden" />
									<div class="hk_company">
										<input type="text" id="serveCompanyId" name="serveCompanyId" hidden="hidden" />
										<input type="text" name="customFieldJson" class="customFieldJson" hidden="hidden" />
										<h6 class="hk-titles main-title">公司信息</h6>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;"><i>*</i>公司名称</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-companyName" name="companyName" autocomplete="off" class="layui-input textss" maxlength="100">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;padding-left: 10px;"><i>*</i>公司名称(英文)</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-companyEngName" name="companyEngName" autocomplete="off" class="layui-input textss" maxlength="100">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司简称</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-companyAbbr" name="companyAbbr" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;padding-left: 0;padding-right: 0;"><i>*</i>联系人</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-linkman" name="linkman" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;"><i>*</i>职务</label>
											</div>
											<div class="  layui-col-lg10">
												<input id="company-display-settings-checkbox-position" type="text" name="position" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>联系电话</label>
											</div>
											<div class="  layui-col-lg10">
												<input id="company-display-settings-checkbox-tel" type="text" name="tel" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>移动电话</label>
											</div>
											<div class="  layui-col-lg10">
												<input id="company-display-settings-checkbox-phone" type="text" name="phone" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label" style="width: 100%;border-right: 0;"><i>*</i>传真</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-fax" name="fax" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>邮箱</label>
											</div>
											<div class="  layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-email" name="email" autocomplete="off" class="layui-input textss">
												<!--lay-verify="required|username"-->
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0;"><i>*</i>网址</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-website" placeholder="如:http:www.baidu.com"
													name="website" autocomplete="off" class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>公司地址</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-address" name="address" autocomplete="off"
													class="layui-input textss">
											</div>
										</div>
										<div class="layui-form-item layui-row layui-inline ">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>产品类型</label>
											</div>
											<div class="  layui-col-lg9" style="width: 45%;float:left;">
												<input type="text" name="productType" id="company-display-settings-checkbox-productType" class="layui-input textss"
													autocomplete="off">
											</div>
											<div class="layui-col-lg1" style="width: 15%;float: left;">
												<input type="button" value="选择" class="layui-input" style="color:#009E94 ;padding:0;">
											</div>
										</div>
										<!-- <div class="layui-form-item layui-row layui-inline">
											<div class="layui-col-lg2">
												<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>供需智能匹配标签</label>
											</div>
											<div class="layui-col-lg10">
												<input type="text" id="company-display-settings-checkbox-supplyAndDemand" name="supplyAndDemand"
													autocomplete="off" placeholder="不超过30个字,如:香蕉，牛奶" class="layui-input textss">
											</div>
										</div> -->
										<div class="hk_add2"></div>
									</div>
									<div class="layui-form-item layui-row layui-inline">
										<div class="layui-col-lg2">
											<label class="layui-form-label " style="width: 100%;border-right: 0px;"><i>*</i>供需智能匹配标签</label>
										</div>
										<div class="layui-col-lg10">
											<input type="text" id="company-display-settings-checkbox-supplyAndDemand" name="supplyAndDemand"
												autocomplete="off" placeholder="不超过30个字,如:香蕉，牛奶" class="layui-input textss">
										</div>
									</div>
									<div class="hk_company">
										<div class="layui-form-item layui-form-text long-text">
											<label class="layui-form-label" style="text-align: center;"><i>*</i>公司简介</label>
											<div class="layui-input-block">
												<textarea id="company-display-settings-checkbox-profile" placeholder="请输入内容" class="layui-textarea company-profile textss"
													name="profile"></textarea>
											</div>
										</div>
									</div>
								</form>
								<!-- 产品信息 -->
								<div class="hk_product">
									<h6 class="hk-titles main-title">
										产品信息
										<!-- <span style="font-size: 14px;color: #999;margin-left: 10px;" id="product_message"> -->
										<span style="font-size: 14px;color: #999;margin-left: 10px;">
											(最多上传<em id="maxUploadProductCount" style="font-style: normal;color: red;">0</em>个产品)</span>
									</h6>
									<button type="button" class="layui-btn flex-center-w" id="importProd" onclick="importProductInfo()" style="">
										<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20" fill="white"><defs><style>.b{clip-path:url(#a);}.c{fill:none;}</style><clipPath id="a"><rect class="a" width="14" height="17" transform="translate(0 -0.359)"/></clipPath></defs><g transform="translate(-287 -237)"><g class="b" transform="translate(290 239.359)"><path class="a" d="M9,11.719l-4-2H4v2H0v2H4v2H5l4-3a.557.557,0,0,0,0-1m5-7-5-4c-.148-.15.209-1,0-1H2a.893.893,0,0,0-1,1v7H3v-6H8v3a1.194,1.194,0,0,0,1,1h3v9H9v2h4a.893.893,0,0,0,1-1v-10c0-.211.147-.851,0-1" transform="translate(-0.003 -0.078)" /></g><rect class="c" width="20" height="20" transform="translate(287 237)"/></g></svg>
										&nbsp;&nbsp;
										<span>一键导入产品信息</span>
									</button>
									<div class="set-bottom-bot-img">
										<div class="get-product-img" id="choisePic">
											<img src="../../../img/invitation/img.png">
											<p>上传产品图</p>
										</div>
									</div>
									<div class="all_product long-text">
										<div class="layui-row">
											<table class="layui-table" id="producttable" lay-filter="producttable" lay-data="{id:'producttable',toolbar: '#toolbarDemo',totalRow:true}">
												<thead>
													<tr id="product-table-tr">
														<th lay-data="{type: 'radio' ,width:60}"></th>
														<!-- <th lay-data="{type:'numbers',width:100,align:'center'}">
															序号
														</th> -->
														<th lay-data="{field:'showOrder',width:120,align:'center', sort: true,event: 'showOrder', }">
															显示顺序
														</th>
														<th lay-data="{field:'productName',align:'left',sort: true,}">
															产品名称
														</th>
														<th lay-data="{field:'productSpec',align:'center',}">
															产品规格
														</th>
														<th lay-data="{field:'productTypeBaseName',align:'center',sort: true,}">
															产品类型
														</th>
														<th lay-data="{field:'pic',align:'center',templet: productTablePic}">
															产品图片
														</th>
														<th lay-data="{width: 180,align:'center',toolbar: '#productTableToolBar'}">
															顺序
														</th>
													</tr>
												</thead>
											</table>
										</div>
										<div class="layui-fluid" id="productdiv" hidden="hidden" style="margin-top: 20px;min-width: 870px;">
											<div class="" style="z-index: 999;">
												<div class="layui-collapse">
													<div class="layui-colla-item">
														<h2 class="layui-colla-title">产品信息</h2>
														<div class="layui-colla-content layui-show">
															<div class="layui-row" id="product-information"  style="display: flex;flex-wrap: wrap;">
																<div class="layui-col-xs6" id="product-display-productName">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2" onclick="AllData()"><i>*</i>产品名称</label>
																		<div class="layui-col-lg10">
																			<input type="hidden" id="productId">
																			<input type="text" lay-verify="title" autocomplete="off" name="productName" id="productName"
																				class="layui-input texts">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-productNameEn">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>产品名称(英文)</label>
																		<div class="layui-col-lg10">
																			<!-- <input type="hidden" id="productId"> -->
																			<input type="text" lay-verify="title" autocomplete="off" name="productNameEn" id="productNameEn"
																				class="layui-input texts">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-showOrder">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2">显示顺序</label>
																		<div class="layui-col-lg10">
																			<!-- <input type="hidden" id="productId"> -->
																			<input type="text" lay-verify="title" autocomplete="off" name="showOrder" id="showOrder" value="99"
																				class="layui-input">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-productSpec">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>产品规格</label>
																		<div class="layui-col-lg10">
																			<input type="text" lay-verify="title" autocomplete="off" name="productSpec" id="productSpec"
																				class="layui-input texts">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-links">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>订购链接</label>
																		<div class="layui-col-lg10">
																			<input type="text" lay-verify="links" autocomplete="off" name="links" id="links" class="layui-input texts">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-tradeName">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2">行业分类</label>
																		<div class="layui-col-lg10">
																			<input type="text" readonly="readonly" name='tradeName' id="tradeName" class="layui-input">
																			<input type="hidden" readonly="readonly" name="tradeId" id="tradeNameIds" class="layui-input"
																				autocomplete="off">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-tradeNameEn">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2 ">行业分类(英文)</label>
																		<div class="layui-col-lg10 layui-input-block" style="margin-left: 0;">
																			<input type="text" readonly="readonly" name="tradeNameEn" id="tradeNameEn"  class="layui-input">
																			<input type="hidden" readonly="readonly" name="tradeIdEn" id="tradeNameEns" class="layui-input"
																				autocomplete="off">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-productType">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>展品类型</label>
																		<div class="layui-col-lg9"  style="width: 45%;float:left;">

																			<input type="text" readonly="readonly" name="productTypeBaseName" id="productType"  class="layui-input">
																			<input type="hidden" readonly="readonly" name="productTypeBase" id="productTypeNameID" class="layui-input"
																				autocomplete="off">
																		</div>
																		<div class=" layui-col-lg1 show" style="width: 15%;float: left;">
																			<input type="button" value="选择" class="layui-input theme-color" onclick="getProType(); " style="padding:0;">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-productTypeEn">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>展品类型(英文)</label>
																		<div class="layui-col-lg10">
																			<input type="text" readonly="readonly" name="productTypeBaseEnName" id="productTypeEn"  class="layui-input">
																			<input type="hidden" readonly="readonly" name="productTypeBaseEn" id="productTypeEnID" class="layui-input">
																		</div>
																	</div>
																</div>

																<div class="layui-col-xs6" id="product-display-section">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>展区</label>
																		<div class="layui-col-lg10">
																			<input type="text" lay-verify="section" autocomplete="off" name="section" id="section" class="layui-input texts">
																		</div>
																	</div>
																</div>
																<div class="layui-col-xs6" id="product-display-boothNum">
																	<div class="layui-form-item wth98">
																		<label class="layui-form-label b_g_none layui-col-lg2"><i>*</i>展位号</label>
																		<div class="layui-col-lg10">
																			<input type="text" lay-verify="boothNum" autocomplete="off" name="boothNum" id="boothNum" class="layui-input texts">
																		</div>
																	</div>
																</div>
															</div>
															<div class="layui-row" id="product-display-productData">
																<div class="layui-col-xs12">
																	<div class="layui-form-item wth100">
																		<label class="layui-form-label b_g_none"><i>*</i>产品简介</label>
																		<div class="layui-col-lg10" style="width: 80%;">
																			<textarea placeholder="请输入内容" class="layui-textarea product_profile_count texts" id="productData"
																				style="height: 100px;"></textarea>
																		</div>
																	</div>
																</div>
															</div>
															<div class="layui-row" id="product-display-productDataEn">
																<div class="layui-col-xs12">
																	<div class="layui-form-item wth100">
																		<label class="layui-form-label b_g_none"><i>*</i>产品简介(英文)</label>
																		<div class="layui-col-lg10" style="width: 80%;">
																			<textarea placeholder="请输入内容" class="layui-textarea  product_profile_count_en texts" id="productDataEn"
																				style="height: 100px;"></textarea>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>

													<div class="layui-colla-item">
														<h2 class="layui-colla-title">图片列表</h2>
														<div class="layui-colla-content layui-show">
															<div class="layui-row">
																<div class="layui-upload">
																	<!-- <button type="button" class="layui-btn layui-btn-normal" >上传图片
																	</button> -->
																	<input type="file" id="addPic" style="display:none;" onchange="selecttype(this)">
																	<div class="layui-upload-list">
																		<table class="layui-table" id="pictable" lay-filter="pictable"
																			lay-data="{page:false, limit: 20,totalRow:true}">
																			<thead>
																				<tr>
																					<!-- <th lay-data="{type: 'checkbox' ,width:60}"></th> -->
																					<!-- <th lay-data="{type:'numbers',width:100}">
																						序号
																					</th> -->
																					<th lay-data="{field:'showOrder',sort: true,width:120,align:'center'}">
																						显示顺序
																					</th>
																					<th lay-data="{field:'pic',align:'center',templet:'#producttlimg'}">
																						产品图片
																					</th>
																					<th lay-data="{field:'isMain',align:'center',templet:'#mainpic'}">
																						是否主图片
																					</th>
																					<!-- <th lay-data="{field:'serveProductPicId',width:260,align:'center',templet:'#isSave'}">状态</th> -->
																					<th lay-data="{width:240,align:'center',toolbar: '#barDemo'}">
																						操作
																					</th>
																					<th lay-data="{width: 180,align:'center',toolbar: '#productPicTableToolBar'}">
																						顺序
																					</th>
																				</tr>
																			</thead>
																		</table>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<!-- 录入框结束 -->
									</div>
								</div>
								<!-- 附件信息 -->
								<div class="isUploadAnnex" style="margin-bottom: 60px;">
									<h2 class="main-title">
										附件上传：<b style="margin: 10px 0;" id="upload_inst"></b>
									</h2>
									<div class="standard-exhibition">
										<div class="layui-upload">
											<button type="button" style="margin-top:0;display:inline-block !important"
												class="layui-btn layui-btn-normal " id="testList">选择文件</button>
											<div class="layui-upload-list">
												<table class="layui-table" id="accTable" lay-filter="accTable">
													<thead>
														<tr>
															<th lay-data="{field:'x', width:100, sort: true, type:'numbers'}">序号</th>
															<th lay-data="{field:'f_doc_name', templet: '#docName'}">文件名</th>
															<th lay-data="{field:'f_name', width:150, toolbar: '#barFormatbtn'}">操作</th>
														</tr>
													</thead>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-fluid menber-botton-fiexd">
			<div class="layui-row">
				<div class="layui-col-lg12">
					<div class="layui-btn-group" style="float: right;margin: 11px 50px 11px 0;">
						<button class="layui-btn layui-hide" id="clibtn" onclick="submit()"></button>
						<button class="layui-btn" id="KeepBtn">保存</button>
						<button class="layui-btn" id="ComitBtn">提交</button>
						<button class="layui-btn" style="display: none" id="revokeBtn">撤销提交</button>
						<button class="layui-btn" style="display: none" id="cancelBtn">撤销审核</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 产品信息顶部 操作栏 -->
	<script type="text/html" id="toolbarDemo">
		<div class="layui-btn-container">
			<div class="layui-inline" lay-event="add">
				<i class="layui-icon layui-icon-add-1"></i>
			</div>
			<div class="layui-inline" lay-event="delete">
				<i class="layui-icon layui-icon-delete"></i>
			</div>
			<div class="layui-inline" lay-event="update">
				<i class="layui-icon layui-icon-edit"></i>
			</div>
		</div>
	</script>
	<!-- 产品信息表格 操作栏 -->
	<script type="text/html" id="productTableToolBar">
		<!-- <a onclick="app.productSort({{ d.serveProductId }},{{ !!d.showOrder }});return false;" href="#" class="layui-btn">
			调整
		</a> -->
		<a onclick="app.productSort({{ d.serveProductId }},{{ !!d.showOrder }},true);return false;" href="#" class="btn-up">
			上移
		</a>&nbsp;&nbsp;&nbsp;
		<a onclick="app.productSort({{ d.serveProductId }},{{ !!d.showOrder }},false);return false;" href="#" class="btn-down">
			下移
		</a>
	</script>
	<script type="text/html" id="productPicTableToolBar">
		<a onclick="app.productPicSort({{ d.serveProductId }},{{ d.serveProductPicId }},{{ d.showOrder }},true);return false;" href="#" class="btn-up">
			上移
		</a>&nbsp;&nbsp;&nbsp;
		<a onclick="app.productPicSort({{ d.serveProductId }},{{ d.serveProductPicId }},{{ d.showOrder }},false);return false;" href="#" class="btn-down">
			下移
		</a>
	</script>
	<script type="text/html" id="productTablePic">
		<div style="position: relative;width: 125px;height: 80px;">
			{{datass(d.pic) }}
			{{# if (d.serveProductPiclist && d.serveProductPiclist.length) { }}
			<center class="productPicNum">x{{ d.serveProductPiclist.length }}</center>
			{{# } }}
		</div>
	</script>
	<!-- 产品信息 编辑/添加 表格 操作栏 -->
	<script type="text/html" id="barDemo">
		<a class="layui-btn  layui-btn-xs" lay-event="updateMain">设为主图片</a>
		&nbsp;&nbsp;&nbsp;
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
	</script>
	<script type="text/html" id="producttlimg">
		{{#
		var pics =d.pic;
		var pictype = d.pic.substring(d.pic.lastIndexOf("."));
			if(pictype == ".bmp" || pictype == ".png" || pictype == ".gif" || pictype == ".jpg" || pictype == ".jpeg") {
			return '<img src="'+ getImgUrl(pics)+'" style="width:124px;height:80px"/>';
				}
			return '<video src="'+ getImgUrl(pics)+'" width="124px" height="80px" controls x5-video-player-type="h5"></video>';
			}}
	</script>
	<script type="text/html" id="isSave">
		{{# if(d.serveProductPicId == null||d.serveProductPicId == ''||d.serveProductPicId == '0') return '
			<span style="color: red;">未保存</span>'; return '已保存'; }}
	</script>
	<script type="text/html" id="mainpic">
		{{# if(d.isMain == null||d.isMain == ''||d.isMain == '0') return '否'; return '
			<span style="color: red;">是</span>'; }}
	</script>
	<!-- 附件部分 -->
	<script type="text/html" id="docName">
		<a href="javascript:void(0);" onclick="downAttch(this)"
			fileName="{{d.f_doc_name}}"
			filePath="{{d.f_doc_file}}">{{d.f_doc_name}}{{d.f_doc_suffix}}</a>
	</script>
	<script type="text/html" id="barFormatbtn">
		<a href="javascript:void(0);" class="theme-color-hover" onclick="delAcc({{d.accId}})" ">删除</a>
	</script>
	<!-- end -->

<div class="layui-fluid" id="getProType" hidden="hidden" style="overflow: hidden;">
	<div id="test2" class="demo-tree-more leftTreeStyle" style="overflow-y: auto; height: 400px"></div>
	<div class="layui-fluid rightTreeStyle">
		<table class="layui-table" lay-data="{id:'proType',height:332}" lay-filter="test">
			<thead>
				<tr>
					<th lay-data="{field:'0', width:100,align:'center',type: 'radio' }"></th>
					<th lay-data="{width:100,align:'center',type:'numbers'}">序号</th>
					<th lay-data="{field:'f_type_name',width:200,align:'center'}">类型名称</th>
					<th lay-data="{field:'f_type_code',width:215,align:'center'}">类型名称(英文)</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
<div class="layui-fluid" id="getIndustryType" hidden="hidden" style="overflow: hidden;">
	<div id="test3" class="demo-tree-more leftTreeStyle"></div>
</div>

<!-- 公司产品导入选择 -->
<div class="layui-fluid" id="selectImportProducts" hidden="hidden" style="overflow: hidden;">
	<!-- <div id="test4" class="demo-tree-more leftTreeStyle" style="overflow-y: auto; height: 400px"></div> -->
	<div class="layui-fluid rightTreeStyle">
		<table class="layui-table " id="selectImportProductsTable" lay-data="{id:'selectImportProductsTable'}"
			lay-filter="selectImportProductsTable">
			<thead>
				<tr>
					<th lay-data="{field:'0',  type: 'checkbox' ,width:60}"></th>
					<!-- <th lay-data="{type:'numbers',width:100,align:'center'}">序号</th> -->
					<th lay-data="{field:'productName',align:'center'}">产品名称</th>
					<th lay-data="{field:'productSpec',align:'center'}">产品规格</th>
					<th lay-data="{field:'productTypeName',align:'center'}">产品类型</th>
					<th lay-data="{field:'pic',align:'center',templet:productTablePic}">产品图片
					</th>
				</tr>
			</thead>
		</table>
	</div>
</div>

<!-- 裁剪 start -->
<div class="tailoring-container ImgShow" style="display: none;">
	<div class="black-cloth" onclick="closeTailor(this)"></div>
	<div class="tailoring-content">
		<div class="tailoring-content-one">
			<label title="上传图片" for="chooseImg" class="l-btn choose-btn">
				<input type="file" name="file" id="chooseImg" onchange="selectImg(this)" class="hidden" accept="image/*">
				选择图片
			</label>
			<div class="close-tailoring" onclick="closeTailor(this)">×</div>
		</div>
		<div class="tailoring-content-two">
			<div class="tailoring-box-parcel">
				<img id="tailoringImg">
			</div>
			<div class="preview-box-parcel">
				<p>图片预览：</p>
				<div class="square previewImg"></div>
				<div class="circular previewImg"></div>
			</div>
		</div>
		<div class="tailoring-content-three">
			<button class="l-btn cropper-reset-btn">复位</button>
			<button class="l-btn cropper-rotate-btn">旋转</button>
			<button class="l-btn cropper-scaleX-btn">换向</button>
			<button class="l-btn sureCut" id="sureCut">确定</button>
		</div>
	</div>
</div>
<script>
	/**
	 * 裁剪 确定
	 */
	$("#sureCut").on("click", function() {
		if ($("#tailoringImg").attr("src") == null) return false;
		var cas = $('#tailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
		var base64url = cas.toDataURL()
		var timestamp = new Date().getTime()
		var file = dataURLtoFile(base64url, timestamp + ".jpg");
		uploadImg(file, true);
		// 关闭裁剪框
		$(".ImgShow").hide();
		$(".tailoring-box-parcel").hide();
		$(".preview-box-parcel").hide();
	});
	// if ($("#tailoringImg").attr("src") == null)
	function uploadImg(file, isAfterCropper = false) {
		if(!isAfterCropper) checkFile(file);
		const idx = layer.load(3, {
			shade: [0.2],
			// time: 60000,
		});
		let idxTimer = setTimeout(() => {
			layer.close(idx);
			layer.msg('上传超时',{icon: 2});
		}, 60000);
		var formData = new FormData();
		formData.append("file", file); //data
		$.ajax({
			url: variableSponsor + '/upload/uploadFileOss',
			dataType: "json", //返回数据类型
			type: 'post',
			data: formData,
			//cache : false,
			processData: false,
			contentType: false,
			async: true,
			success: function(res) {
				layer.close(idx);clearTimeout(idxTimer);
				type = +type
				if (type === 1) {
					$('#logoImg').attr('src', res.result.path).css('background-color', '#E4E4E4')
					$("#logo").val(res.result.path);
					// submit()
				} else if (type === 2) {
					if (res.state == 0) {
						return layer.msg('上传失败');
					}
					var data = allPicTable;
					data.push({
						serveProductPicId: "0",
						serveProductId: "0",
						pic: res.result.path,
						isMain: "0",
					})
					loadPicTable(data);
				} else if (type === 3) {
					$('#companyPicImg').attr('src', res.result.path).css('background-color', '#E4E4E4')
					$("#companyPic").val(res.result.path);
					// $('#companyPicUploader').val('')
					// submit()
				} else if (type === 4) {
					$('#companyAuthImg').attr('src', res.result.path).css('background-color', '#E4E4E4')
					$("#companyAuth").val(res.result.path);
					// $('#companyPicUploader').val('')
					// submit()
				}


			},
			error: function(data) {
				layer.close(idx);clearTimeout(idxTimer);
				alert('上传失败');
			}
		})
	}
	//关闭裁剪框
	function closeTailor() {
		$(".tailoring-container").toggle();
	}
	var gfile;
	//弹出框水平垂直居中
	(window.onresize = function() {
		var win_height = $(window).height();
		var win_width = $(window).width();
		if (win_width <= 768) {
			$(".tailoring-content").css({
				"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
				"left": 0
			});
		} else {
			$(".tailoring-content").css({
				"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
				"left": (win_width - $(".tailoring-content").outerWidth()) / 2
			});
		}
	})();

	//图像上传
	function selectImg(file) {
		var max_size = 10240;
		var img_size = 1024;
		var fileData = file.files[0];
		try{
			file.value = '';
			checkFile(fileData)
		}catch(e){
			$(file).val('');
			throw e
		}
		var size = fileData.size;
		$(".tailoring-box-parcel").show();
		$(".preview-box-parcel").show();
		if (size > img_size * 1024) {
			let fileObj = fileData; // document.getElementById('chooseImg').files[0];
			//alert(fileObj)
			var reader = new FileReader();
			reader.readAsDataURL(fileObj)
			reader.onload = function(e) {
				let image = new Image() //新建一个img标签（还没嵌入DOM节点)
				image.src = e.target.result
				image.onload = function() {
					let canvas = document.createElement('canvas'),
						context = canvas.getContext('2d'),
						imageWidth = image.width / 5, //压缩后图片的大小
						imageHeight = image.height / 5,
						data = ''
					canvas.width = imageWidth
					canvas.height = imageHeight
					context.drawImage(image, 0, 0, imageWidth, imageHeight)
					data = canvas.toDataURL('image/jpeg')
					// console.log(data)
					//压缩完成
					// document.getElementById('img').src = data
					$('#tailoringImg').cropper('replace', data, false)
				}
			}
		} else {
			var reader = new FileReader();
			let fileObj = fileData; // document.getElementById('chooseImg').files[0];
			reader.readAsDataURL(fileObj)
			reader.onload = function(evt) {
				var replaceSrc = evt.target.result;
				//更换cropper的图片
				$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
				$('#replaceImg').cropper('replace', replaceSrc, false);
			}


		}
	}
	var companysizes = 4 / 3 //100 / 64.7;
	//var logosize = 25 /25;
	//cropper图片裁剪
	$('#tailoringImg').cropper({
		aspectRatio: companysizes, //默认比例
		preview: '.previewImg', //预览视图
		guides: false, //裁剪框的虚线(九宫格)
		autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
		dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
		movable: true, //是否允许移动剪裁框
		resizable: true, //是否允许改变裁剪框的大小
		zoomable: true, //是否允许缩放图片大小
		mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
		touchDragZoom: true, //是否允许通过触摸移动来缩放图片
		rotatable: true, //是否允许旋转图片
		crop: function(e) {
			// 输出结果数据裁剪图像。
		}
	});
	//旋转
	$(".cropper-rotate-btn").on("click", function() {
		$('#tailoringImg').cropper("rotate", 45);
	});
	//复位
	$(".cropper-reset-btn").on("click", function() {
		$('#tailoringImg').cropper("reset");
	});
	//换向
	var flagX = true;
	$(".cropper-scaleX-btn").on("click", function() {
		if (flagX) {
			$('#tailoringImg').cropper("scaleX", -1);
			flagX = false;
		} else {
			$('#tailoringImg').cropper("scaleX", 1);
			flagX = true;
		}
		flagX != flagX;
	});

	function dataURLtoFile(dataurl, filename) { //将base64转换为文件
		var arr = dataurl.split(','),
			mime = arr[0].match(/:(.*?);/)[1],
			bstr = atob(arr[1]),
			n = bstr.length,
			u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8arr], filename, {
			type: mime
		});
	}
	// 产品图片
	function selecttype(e) {
		let file = e.files[0];
		try{
			e.value = '';
			checkFile(file)
		}catch(e){
			$(e).val('');
			throw e;
		}
		var file_id = file.type || '';
		let fileObj = file; //document.getElementById('addPic').files[0];
		// file_id = file_id.substring(file_id.indexOf(".")).toLocaleLowerCase();
		if(file_id.startsWith('image/')) {
		// if (file_id == ".bmp" || file_id == ".png" || file_id == ".gif" || file_id == ".jpg" || file_id == ".jpeg") {
			var size = file.size;
			if(file.type.endsWith('png')) {
				uploadImg(file, true);
				return;
			}
			var max_size = 10240;
			var img_size = 1024;
			$(".ImgShow").toggle();
			if (size > img_size * 1024) {
				var reader = new FileReader();
				reader.readAsDataURL(fileObj)
				reader.onload = function(e) {
					let image = new Image() //新建一个img标签（还没嵌入DOM节点)
					image.src = e.target.result
					image.onload = function() {
						let canvas = document.createElement('canvas'),
							context = canvas.getContext('2d'),
							imageWidth = image.width / 5, //压缩后图片的大小
							imageHeight = image.height / 5,
							data = ''
						canvas.width = imageWidth
						canvas.height = imageHeight
						context.drawImage(image, 0, 0, imageWidth, imageHeight)
						data = canvas.toDataURL('image/jpeg')
						//压缩完成
						// document.getElementById('img').src = data
						$('#tailoringImg').cropper('replace', data, false)
					}
				}

			} else {
				var reader = new FileReader();
				reader.onload = function(evt) {
					var replaceSrc = evt.target.result;
					//更换cropper的图片
					$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
					$('#replaceImg').cropper('replace', replaceSrc, false);
					//图像上传
					//alert('图像上传');
				}
				reader.readAsDataURL(fileObj)
			}
			$(".tailoring-box-parcel").show();
		} else if( file_id.startsWith('video/') ){
			// var max_vidoesize = 10240;
			// var img_vidoesize = 1024;
			// var vidoesize = file.size;
			// if (vidoesize > max_vidoesize * 1024) {
			// 	alert("视频大小不能超过10M");
			// 	return;
			// }
			uploadImg(file, true)
		} else {
			alert("只能上传图片或视频 !");
			return;
		}
	}
</script>

<script>
var cid = uinfo.cid || +_url.get('cid') || '';
var pid = +_url.get('pid');
var task_dtl_id = _url.get("task_dtl_id");
var exhibitCode = _url.get("exhibitCode");
var f_confirm = 0;
var state = '';
var taskName = '商家发布信息填报';
$('.taskName').html(taskName);
var f_task_id = _url.get("f_task_id"); //任务id
</script>

<!-- 上传 附件 下载 : 相关 -->
<script>
	//最多上传产品数量
	var maxUploadProductCount;
	//最多上传产品图片数量
	var maxUploadProductPicCount;
	//公司简介最大字数
	var companyProfileCount;
	//公司简介英文最大字数
	var companyProfileEnCount;
	//产品简介最大字数
	var productProfileCount;
	//产品简介英文最大字数
	var productProfileEnCount;
	var isReload = false;
	var type = "";
	var serveCompanyId = "";
	var layertable;
	/**
	 * 上传产品图片
	 */
	layui.use('upload', function() {
		var upload = layui.upload;
		//执行实例
		var uploadInst = upload.render({
			elem: '#choisePic',
			url: variableSponsor + '/serveCompany/upload',
			done: function(res) {
				//上传完毕回调
				if (res.code === 0) {
					var html = '<div class="product-img" id="' + res.data.src + '">\n' +
						'  <img src="' + res.data.src + '" alt="" class="layui-upload-img cata-product-img" >\n' +
						'  <div class="product-set">\n' +
						'  <a href="javascript:void(0)"  style="text-align: center" onclick="delPic(this)">删除</a>\n' +
						' </div>' +
						' </div>';
					$("#choisePic").before(html);
				} else {
					layer.msg('上传失败');
				}
			},
			error: function() {
				// 请求异常回调
			}
		});
	});

	function showtrim(obj) {
		ifSumbitError()
		obj = +obj
		if (obj == 1) { // 1公司图片: logo  2产品图片: pic   3公司logo  4营业执照
			type = obj
			$(".ImgShow").show();
			$("#chooseImg").click();
		} else if (obj == 2) {
			type = obj
			//$(".ImgShow").show();
			$("#addPic").click()
		} else if ([3,4].includes(obj)) {
			type = obj
			$("#addPic").click()
			// $('#companyPicUploader').click()
			// $(".ImgShow").show();
			// $("#chooseImg").click();
		}
	}

	// 附件上传 删除
	layui.use(['upload','layer','table'], function() {
		var upload = layui.upload
		,table = layui.table
		,layer = layui.layer;
		//执行实例
		var uploadInst = upload.render({
			elem: '#upload-attachment',
			accept: 'file',
			size: 10240,
			url: variableSponsor + '/upload/uploadFile',
			done: function(res) {
				//上传完毕回调
				if (res.state === 1) {
					var html = '<div class="allFile"><div class="fileText">' + res.result.fileName +
						'</div><span class="allFilespan"><img src="../../../img/close.png" ></span></div>'
					$("#upload-fujian").append(html);
				} else {
					layer.msg('上传失败');
				}
			},
			error: function() {
				//请求异常回调
			}
		});

		//多文件列表示例
		var uploadListIns = upload.render({
				elem: '#testList',
				url: variableSponsor + '/accessory/save',
				accept: 'file',
				multiple: false
					//,auto: false
					,
				size: 50*1024,
				auto: true
					/* ,bindAction: '#testListAction' */
					,
				data: {
					tableName: 't_task_detail',
					recordCode: task_dtl_id,
					projectId: pid
				},
				choose: function(obj) {
					ifSumbitError()
				},
				done: function(res, index, upload) {
					if (res.state == 1) { //上传成功
						layer.msg('操作成功', {
							icon: 1
						})
						loadAcc();
					} else {
						layer.msg('操作失败', {
							icon: 2
						})
					}

				},
				error: function(index, upload) {
					layer.msg('操作失败', {
						icon: 2
					})

				}
			});
		window.loadAcc = function() {
			$.ajax({
				url: variableSponsor + '/accessory/selectList',
				data: {
					tableName: 't_task_detail',
					recordCode: task_dtl_id,
					projectId: pid
				},
				type: "post",
				async: true,
				xhrFields: {
					withCredentials: true
				},
				success: function(data) {
					if (data.state == 1) {
						table.reload('accTable', {
							data: data.rows
						});
					} else {
						table.reload('accTable', {
							data: []
						});
					}

					//console.log(data.result)
				},
				error: function(data) {
					layer.msg(data);
					return false;
				}
			});
		}
		window.delAcc = function(id) {
			ifSumbitError()
			$.ajax({
				url: variableSponsor + '/accessory/delAccById',
				data: {
					accId: id
				},
				type: "post",
				async: true,
				xhrFields: {
					withCredentials: true
				},
				success: function(data) {
					layer.msg('操作成功', {
						icon: 1
					})
					loadAcc();
				},
				error: function(data) {
					//layer.msg(data);
					return false;
				}
			});
		}
		//多文件列表示例结束
	});

	function delPic(obj) {
		$(obj).parent().parent().remove();
	}
	/**
	 * 下载附件
	 * @param obj
	 */
	function downAttch(obj) {
		let jQuery = $(obj).attr("filePath");
		if (jQuery.indexOf("http") == -1)
			jQuery = variablePic + "/image/" + jQuery;
		var a = document.createElement('a');
		a.href = jQuery;
		a.download = $(obj).attr("fileName");
		a.click();
	}

	function delAcc(id) {
		ifSumbitError()
		$.ajax({
			url: variableSponsor + '/accessory/delAccById',
			data: {
				accId: id
			},
			type: "post",
			async: true,
			xhrFields: {
				withCredentials: true
			},
			success: function(data) {
				layer.msg('操作成功', {
					icon: 1
				})
				loadAcc();
			},
			error: function(data) {
				//layer.msg(data);
				return false;
			}
		});
	}

	function downloaddoc() {
		window.location.href = "http://" + variable + "/file/download?fileType=human";
		return false;
	}
	function delPic(id) {
		var flag = 0;
		$.ajax({
			url: variableBus + '/serveProduct/deletePic',
			type: "post",
			datatype: 'json',
			async: false,
			data: {
				serveProductPicId: id
			},
			xhrFields: {
				withCredentials: true
			},
			success(data) {
				if (!data) goLogin();
				//var res = JSON.parse(data)
				if (JSON.parse(data).state > 0) {
					//alert(data)
					layer.msg('删除数据成功', {
						icon: 1
					})
					flag = 1;
					return false;
				} else {
					layer.msg('删除数据失败', {
						icon: 1
					})
					flag = 0;
					return false;
				}
			},
			error(data) {
				layer.msg('数据发送失败', {
					icon: 2
				})
				flag = 0;
				return false;
			}
		});
		return flag;
	}
</script>

<script>
	var layer = layui.layer;
	var _loading;
	var _loadingTimer;
	layer.ready(function() {
		_loading = layer.load(3, {
			shade: [0.2],
			time: 0,
		});
		_loadingTimer = setTimeout(() => {
			layer.msg('请求超时, 请稍后重试!', {icon: 2,zIndex: 20220802,anim: 6})
			layer.close(_loading);
		}, 30000);
		window.closeLoading = () => {
			setTimeout(() => {
				layer.close(_loading);
				clearTimeout(_loadingTimer);
			}, 500);
		}
	});
	var app;
	var laydate = layui.laydate;
	var tree = layui.tree;
	// var util = layui.util;
	var element = layui.element;
	var form = layui.form;
	var table = layui.table;
	var	layedit = layui.layedit;
	var	upload = layui.upload;
	var index;
	var showCompanyImg = false
	var showLogo = false;
	var companyIsUpdate = true;
	var productIsUpdate = true;
	var companyDisplay = true;
	var cataDisplay = true;
	var cataCancel  = true; // 是否可撤销审核
	var length;

	function initRender(){
		element.render('breadcrumb');
		table.init('producttable', {page: false,limit: 99,});
		table.init('accTable', {limit: 20,page: false,});
		table.init('pictable', {page: false,limit: 99,});
		table.init('selectImportProductsTable', {page: false,limit: 99,});
		// 监听事件
		table.on('toolbar(producttable)', function(obj) {
			var checkStatus = table.checkStatus(obj.config.id);
			switch (obj.event) {
				case 'add':
					ifSumbitError()
					if (!productIsUpdate)  util2.alert('展品不可新增');
					if (checkRowcount(obj)) util2.alert('展品数量已达到最大值');
					openProductData(1)
					break;
				case 'delete':
					ifSumbitError()
					if (!productIsUpdate) util2.alert('展品不可删除');
					var size = checkStatus.data.length;
					if (size == 0) util2.alert('请勾选要删除的数据');
					layer.confirm('确认删除当前选中' + size + '行么', function(index) {
						delbatch();
						// table.reload("demo", table.cache.demo)
						layer.close(index);
					});
					break;
				case 'update':
					openProductData(2);
					break;
			}
		});
		//监听行单击事件（双击事件为：rowDouble）
		table.on('rowDouble(producttable)', function(obj) {
			ifSumbitError();
			//标注选中样式
			openProductData(2);
		});
		table.on('row(producttable)', function(obj) {
			//选中行样式
			obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
			//选中radio样式
			obj.tr.find('i[class="layui-anim layui-icon"]').trigger("click");

			// table.reload("producttable",table.cache)
		});
		// table.on('tool(showOrder)', function(obj) {
		// 	// 顺序
		// });
		// 单元格编辑了
		// table.on('edit(test)', function(obj){
		// 	console.log(obj.value); //得到修改后的值
		// 	console.log(obj.field); //当前编辑的字段名
		// 	console.log(obj.data); //所在行的所有相关数据
		// });

		//监听行工具事件
		table.on('tool(pictable)', function(obj) {
			ifSumbitError()
			var data = obj.data;
			//console.log(obj)
			if (obj.event === 'del') {
				var tableData = allPicTable;
				layer.confirm('真的删除么', function(index) {
					for (var i = 0; i < allPicTable.length; i++) {
						if (allPicTable[i].pic === data.pic) {
							allPicTable.splice(i, 1);
						}
					}
					obj.del();
					layer.close(index);
					loadPicTable(tableData);
				});
			} else if (obj.event === 'updateMain') {
				var tableData = allPicTable;
				for (var i = 0; i < tableData.length; i++) {
					if (tableData[i].isMain == 1) {
						if (tableData[i].pic == data.pic) {
							util2.alert('已经是主图片');
						} else {
							tableData[i].isMain = 0;
						}
					} else {
						if (tableData[i].pic == data.pic) {
							tableData[i].isMain = 1;
						}
					}
				}
				loadPicTable(tableData);
			}
		});

		//常规用法
		laydate.render({
			elem: '#test1'
		});
		table.init('demo', {
			limit: 10 //注意：请务必确保 limit 参数（默认：10）是与你服务端限定的数据条数一致
				,
			page: true,
			toolbar: 'default'
			//支持所有基础参数
		});
		table.on('toolbar(test)', function(obj) {
			var checkStatus = table.checkStatus(obj.config.id);
			switch (obj.event) {
				case 'getCheckData':
					var data = checkStatus.data;
					layer.alert(JSON.stringify(data));
					break;
				case 'getCheckLength':
					var data = checkStatus.data;
					layer.msg('选中了：' + data.length + ' 个');
					break;
				case 'isAll':
					layer.msg(checkStatus.isAll ? '全选' : '未全选');
					break;
			}
		});

		//富文本框里的图片上传
		layedit.set({
			uploadImage: {
				url: variableSponsor + '/serveCompany/upload' //接口url
					,
				type: 'post' //默认post
			}
		});

		index = layedit.build('profile', {
			tool: [
				'strong' //加粗
				, 'italic' //斜体
				, 'underline' //下划线
				, 'del' //删除线
				//   ,'|' //分割线
				, 'left' //左对齐
				, 'center' //居中对齐
				, 'right' //右对齐
				//  ,'link' //超链接
				//   ,'unlink' //清除链接
				// ,'face' //表情
				, 'image' //插入图片
				//   ,'help' //帮助
			],
			height: 500
		}); //建立编辑器
	}

	window.buildFormArr = function(id) {
		const obj = {}
		let $i
		$(`#${id} input,#${id} textarea`).each((idx,i) => {
			$i = $(i)
			if($i.attr('name')){
				obj[$i.attr('name')] = $i.val()
			}
		})
		return obj
	}
	window.getCompanyData = function(){
		var formData = {};
		// var id = $('#serveCompanyId').val();
		// if (id !== '' && id > 0) {
		// 	formData['editMode'] = 'Mod';
		// } else {
		// 	formData['editMode'] = 'Add';
		// }
		formData['clientId'] = cid;
		formData['projectId'] = pid;
		formData['taskDtlId'] = task_dtl_id;
		var customFieldJson = {};
		//会刊自定义字段
		$(".company").each(function(index, element) {
			var fieldname = $(element).attr("fieldname");
			customFieldJson[fieldname] = $(element).val();
		});
		$(".customFieldJson").val(JSON.stringify(customFieldJson));

		// var t = $('#companyForm').serializeArray();
		//$.each(t, function() {
		//	formData[this.name] = this.value;
		//});
		formData = Object.assign(formData,buildFormArr('companyForm') || {})

		if (cataDisplay) {
			if (!companyDisplay) {
				formData['companyName'] = $("#catalogue-settings-checkbox-f_company_name").val();
			}
		}
		var content = $("#company-display-settings-checkbox-profile").val();
		formData['profile'] = content;
		formData['serveCompanyId'] = serveCompanyId;
		return formData;
	}
	// /** 保存公司显示项 **/
	// window.save = function() {
	// 	var formData = {};
	// 	// var id = $('#serveCompanyId').val();
	// 	// if (id !== '' && id > 0) {
	// 	// 	formData['editMode'] = 'Mod';
	// 	// } else {
	// 	// 	formData['editMode'] = 'Add';
	// 	// }
	// 	formData['clientId'] = cid;
	// 	formData['projectId'] = pid;
	// 	formData['taskDtlId'] = task_dtl_id;
	// 	var customFieldJson = {};
	// 	//会刊自定义字段
	// 	$(".company").each(function(index, element) {
	// 		var fieldname = $(element).attr("fieldname");
	// 		customFieldJson[fieldname] = $(element).val();
	// 	});
	// 	$(".customFieldJson").val(JSON.stringify(customFieldJson));

	// 	var t = $('#companyForm').serializeArray();
	// 	$.each(t, function() {
	// 		formData[this.name] = this.value;
	// 	});
	// 	if (cataDisplay) {
	// 		if (!companyDisplay) {
	// 			formData['companyName'] = $("#catalogue-settings-checkbox-f_company_name").val();
	// 		}
	// 	}
	// 	var content = $("#company-display-settings-checkbox-profile").val();
	// 	formData['profile'] = content;
	// 	formData['serveCompanyId'] = serveCompanyId;
	// 	$.ajax({
	// 		url: variableSponsor + '/serveCompany/save', //请求服务器url地址.
	// 		type: "post",
	// 		async: false,
	// 		data: formData,
	// 		success: function(data) {
	// 			//var res = JSON.parse(data);
	// 			if (parseInt(data.state) > 0) {
	// 				$('#serveCompanyId').val(data.state);
	// 				serveCompanyId = data.state
	// 				// layer.msg('操作成功，即将返回主页');
	// 			} else {
	// 				layer.msg('保存失败', {
	// 					icon: 2
	// 				});
	// 			}
	// 		}
	// 	});
	// }

	window.loadPicTable = function(data) {
		length = data.length;
		allPicTable = data;
		layui.table.reload("pictable", {
			data,
		});
	}

	window.loadProductTable = function () {
		isHaveData = 0;
		$.ajax({
			url: variableBus + '/serveProduct/getList', //请求服务器url地址.   +cid+"&pid="+pid
			type: "post",
			data: {
				clientId: cid,
				projectId: pid
			},
			async: false, //同步，防止重复提交
			xhrFields: {
				withCredentials: true
			},
			beforeSend: function() {},
			success: function(data) {
				if (!data) goLogin()
				var str = JSON.parse(data)
				isHaveData = +str.state
				productTableReload(isHaveData === 1 ? str.rows : []);
				return false;
			},
			complete: function() {
				// $("#load").hide();
				return false;
			},
			error: function(data) {
				productTableReload([]);
				return false;
			}
		});
	}

	// 貌似无用
	function addProductData() {
		clearFrom();
		//页面层
		layertable = layer.open({
			type: 1,
			title: '展具选择',
			skin: 'layui-layer-demo-1', //加上边框
			area: ['1200px', '800px', '上传图片'], //宽高
			content: $('#productdiv'),
			btn: ['保存', '关闭'],
			yes: function(index, layero) {
				//按钮【按钮一】的回调
				//	    commitCert();
				saveProduct();
			},
			btn2: function(index, layero) {
				//按钮【按钮二】的回调
				//return false 开启该代码可禁止点击该按钮关闭
				//console.log(1)
			},
			cancel: function() {
				//右上角关闭回调
				var confim = layer.confirm('确认退出编辑？', {
					btn: ['确认', '取消'] //按钮
				}, function() {
					layer.close(layertable);
					layer.close(confim);
				}, function() {
					layer.close(confim);
				});
				return false
			},
			end: function() {
				//console.log("colse")
			}
		});
	}

	function openProductData(flag) {
		if (flag == 2) {
			var checkStatus = table.checkStatus('producttable');
			var len = checkStatus.data.length;
			if (len <= 0) {
				layer.msg('您未选择数据！', {
					icon: 0
				});
				return;
			} else if (len > 1) {
				layer.msg('您选择了多条数据！', {
					icon: 0
				});
				return;
			}
		}
		clearFrom();
		//页面层
		layertable = layer.open({
			type: 1,
			title: '上传产品',
			skin: 'layui-layer-demo-1 layer-product', //加上边框
			area: ['80%', '60%'], //宽高
			content: $('#productdiv'),
			btn: ['保存', '关闭', "上传图片视频"],
			yes: function(index, layero) {
				console.log('上传产品保存按钮')
				//加载产品信息数量禁用添加功能
				ifSumbitError()
				var length1 = 0
				var length2 = 0
				if ($("#productData").length == 1) {
					length1 = $("#productData").val().length;
				}
				if ($("#productDataEn").length == 1) {
					length2 = $("#productDataEn").val().length;
				}
				var text2 = $(".texts");
				for (var i = 0; i < text2.length; i++) {
					if (text2[i].value === "" || text2[i].value == null) {
						var title = $(".texts").eq(i).parent().prev().text()
						layer.msg(title + '为必填项!', {
							icon: 2
						});
						return false;
					}
				}
				if (length1 > productProfileCount) {
					layer.msg('产品简介字符数已超过最大值' + productProfileCount + '！', {
						icon: 2
					});
					return false;
				} else if (length2 > productProfileEnCount) {
					layer.msg('产品简介英文字符数已超过最大值' + productProfileEnCount + '！', {
						icon: 2
					});
					return false;
				} else {
					//按钮【按钮一】的回调
					saveProduct();
				}
			},
			btn2: function(index, layero) {
				//return false 开启该代码可禁止点击该按钮关闭
				console.log(1)
			},
			btn3: function(index, layero) {
				ifSumbitError()
				if (length >= maxUploadProductPicCount) {
					layer.msg('单个产品最多上传' + maxUploadProductPicCount + '图片', {
						icon: 2
					});
					return false;
				}
				// $("#addPic").click();
				showtrim(2)
				return false //开启该代码可禁止点击该按钮关闭
			},
			success: function(obj,idx) {
				$('#layui-layer'+idx).find('.layui-layer-btn2').after('<span>推荐上传400*300像素图片   支持.JPG .JPEG 大小不超过2M，.png格式大小不超过300K，视频上传不得超过10M</span>')
			},
			cancel: function() {
				//右上角关闭回调
				//return false 开启该代码可禁止点击该按钮关闭
				var confim = layer.confirm('确认退出编辑？', {
					btn: ['确认', '取消'] //按钮
				}, function() {
					layer.close(layertable);
					layer.close(confim);
				}, function() {
					layer.close(confim);
				});
				return false
			},
			end: function() {
				//console.log("colse")
			}
		});
		if (flag == 2) {
			loadProductData();
		}else{
			var name = $('#catalogue-settings-checkbox-tradeName2').val()
			if(name!='' && name!=null && name!=undefined){
				$('#tradeName').val(name)
				$('#tradeNameIds').val($('#catalogue-settings-checkbox-tradeName').val())
				$('#tradeNameEn').val($('#catalogue-settings-checkbox-tradeNameEn2').val())
				$('#tradeNameEns').val($('#catalogue-settings-checkbox-tradeNameEn').val())
			}
		}
	}

	function saveProductByData(product) {
		console.log('product',product);
		$.ajax({
			url: variableBus + '/serveProduct/save', //请求服务器url地址.   +cid+"&pid="+pid
			type: "post",
			dataType: "json",
			contentType: "application/json;charset=utf-8",
			data: JSON.stringify(product),
			async: false, //同步，防止重复提交
			xhrFields: {
				withCredentials: true
			},
			beforeSend: function() {
				$("#load").show();
			},
			success: function(data) {
				if (!data) goLogin()
				//alert(data.state)
				if (data.state > 0) {
					layer.msg('保存成功', {
						icon: 1
					})
					layertable && layer.close(layertable)
					loadProductTable();
				} else {
					layer.msg('保存失败', {
						icon: 2
					})
				}
				return false;
			},
			complete: function() {
				$("#load").hide();
				return false;
			},
			error: function(data) {
				layer.msg('数据发送失败', {
					icon: 2
				})
				return false;
			}
		});
	}
	function saveProduct() {
		var productId = $("#productId").val();
		var productName = $("#productName").val();
		var productNameEn = $("#productNameEn").val();
		var productSpec = $("#productSpec").val();

		var productData = $("#productData").val();
		var productDataEn = $("#productDataEn").val();
		var boothNum = $('#boothNum').val();
		var showOrder = $('#showOrder').val();
		var section = $('#section').val();
		var links = $('#links').val();
		var tradeId = $('#tradeNameIds').val();
		var tradeIdEn = $('#tradeNameIds').val();

		var productTypeBase = $('#productTypeEnID').val() || $('#productTypeNameID').val()
		var productTypeBaseEn = $('#productTypeEnID').val() || $('#productTypeNameID').val()
		// var productTypeBase = $('#productTypeNameID').val()
		// var productTypeBaseEn = $('#productTypeEnID').val()
		console.log(1622, productTypeBase, productTypeBaseEn)
		if (productName === '' && !$("#product-display-productName").hasClass("hide1")) {
			layer.msg('请输入产品名称', {});
			return;
		}
		if (productNameEn === '' && !$("#product-display-productNameEn").hasClass("hide1")) {
			layer.msg('请输入产品名称(英文)', {});
			return;
		}
		if (productData === '' && !$("#product-display-productData").hasClass("hide1")) {
			layer.msg('请输入产品简介', {});
			return;
		}
		if (productDataEn === '' && !$("#product-display-productDataEn").hasClass("hide1")) {
			layer.msg('请输入产品简介(英文)', {});
			return;
		}
		if (productSpec === '' && !$("#product-display-productSpec").hasClass("hide1")) {
			layer.msg('请输入产品规格', {});
			return;
		}

		var tableData = allPicTable;
		var arr = [];
		var isMainFlag = 0;
		for (var i = 0; i < tableData.length; i++) {
			if (tableData[i].isMain === 1) {
				isMainFlag = 1;
			}
			var strjson = {
				serveProductPicId: tableData[i].serveProductPicId,
				serveProductId: tableData[i].serveProductId,
				pic: tableData[i].pic,
				showOrder: tableData[i].showOrder || 0,
				isMain: tableData[i].isMain
			};
			arr.push(strjson);
		}
		if (isMainFlag === 0) {
			layer.msg('选择产品图片', {});
			arr[0].isMain = 1;
		}
		var customFieldJson = {};
		//会刊自定义字段
		$(".product").each(function(index, element) {
			var fieldname = $(element).attr("fieldname");
			customFieldJson[fieldname] = $(element).val();
		});
		var product = {};
		product.customFieldJson = JSON.stringify(customFieldJson);
		product.clientId = cid;
		product.projectId = pid;
		product.taskDtlId = task_dtl_id;
		product.showOrder = showOrder;
		product.serveProductId = productId;
		product.productName = productName;
		product.productNameEn = productNameEn;
		// product.productType = productType;
		// product.productTypeEn = productTypeEn;
		product.productSpec = productSpec;
		product.productData = productData;
		product.productDataEn = productDataEn;
		product.serveProductPiclist = arr;
		product.boothNum = boothNum;
		product.section = section;
		product.links = links;
		product.tradeId = tradeId;
		product.tradeIdEn = tradeIdEn;
		product.productTypeBase = productTypeBase;
		product.productTypeBaseEn = productTypeBaseEn;
		//alert(JSON.stringify(product))
		//return;
		saveProductByData(product)
	}

	function clearFrom() {
		$("#productId").val('');
		$("#productName").val("");
		$("#productShowOrder").val("");
		$("#productNameEn").val('');
		$("#productSpec").val("");
		$("#tradeName").val("");
		$("#tradeNameIds").val('');
		$("#tradeNameEn").val("");
		$("#tradeNameEns").val('');
		$("#productType").val('');
		$("#productTypeNameID").val("");
		$("#productTypeEn").val('');
		$("#productTypeEnID").val("");
		$("#links").val('');
		$("#section").val("");
		$("#showOrder").val("");
		$("#boothNum").val('');
		$("#productData").val("");
		$("#productDataEn").val("");
		loadPicTable([]);
	}

	function delbatch() {
		var checkStatus = table.checkStatus('producttable'); //idTest 即为基础参数 id 对应的值
		console.log(checkStatus.data) //获取选中行的数据
		var arr = [];
		for (var i = 0; i < checkStatus.data.length; i++) {
			var strjson = {
				serveProductId: checkStatus.data[i].serveProductId
			};
			arr.push(strjson);
		}
		$.ajax({
			url: variableBus + '/serveProduct/deleteBatch',
			type: "post",
			datatype: 'json',
			contentType: "application/json",
			data: JSON.stringify(arr),
			xhrFields: {
				withCredentials: true
			},
			success: function(data) {
				if (!data) goLogin();
				if (JSON.parse(data).state > 0) {
					util2.alert('删除数据成功', 'suc' , false)
					loadProductTable();
				} else {
					util2.alert('删除数据失败')
				}
			},
			error: function(data) {
				util2.alert('数据发送失败')
			}
		});
	}

	function loadProductData() {
		var checkStatus = table.checkStatus('producttable');
		$.ajax({
			url: variableBus + '/serveProduct/get',
			type: "post",
			datatype: 'json',
			data: {
				id: checkStatus.data[0].serveProductId
			},
			xhrFields: {
				withCredentials: true
			},
			success: function(data) {
				if (!data) goLogin()
				//var res = JSON.parse(data)
				if (JSON.parse(data).state > 0) {
					var res = JSON.parse(data).rows;
					console.log('res',res)
					$('#productId').val(res.serveProductId);
					$('#productName').val(res.productName);
					$('#productNameEn').val(res.productNameEn);
					$('#productSpec').val(res.productSpec);
					$('#productData').val(res.productData);
					$('#productDataEn').val(res.productDataEn);
					$('#boothNum').val(res.boothNum);
					$('#section').val(res.section);
					$('#links').val(res.links);
					$('#showOrder').val(res.showOrder || '99');

					$('#tradeName').val(res.tradeName);
					$('#tradeNameIds').val(res.tradeId);
					$('#tradeNameEn').val(res.tradeNameEn);
					$('#tradeNameEns').val(res.tradeIdEn);
					$('#productType').val(res.productTypeBaseName);
					$('#productTypeNameID').val(res.productTypeBase);
					$('#productTypeEn').val(res.productTypeBaseEnName);
					$('#productTypeEnID').val(res.productTypeBaseEn);


					layui.form.render('select'); //需要渲染一下
					loadPicTable(res.serveProductPiclist);
				} else {
					layer.msg('加载数据失败', {
						icon: 1
					})
				}
			},
			error: function(data) {
				layer.msg('数据发送失败', {
					icon: 2
				})
				console.log("error");
			}
		});
	}

	// function temporary() {
	// 	$(".layui-input").change(function(e) {
	// 		//获取input输入的值
	// 		state = 2;
	// 		$("#clibtn").click();
	// 	});
	// 	$(".layui-textarea").change(function(e) {
	// 		//获取input输入的值
	// 		console.log(e.currentTarget.value);
	// 		state = 2;
	// 		$("#clibtn").click();
	// 	});

	// 	/* if ($("#KeepBtn").hasClass('layui-btn-disabled')) {
	// 	     layer.msg('已提交，无法操作', {
	// 	         icon: 2
	// 	     });
	// 	     return false;
	// 	 } else {
	// 	     state = 2;
	// 	     $("#clibtn").click();
	// 	     return false;
	// 	 }*/
	// }

	$(function() {

		$(".panel-loading").hide();
		initRender();
		app = new Vue({
			el: '#app',
			data: {
				confirm: f_confirm,
				taskName,
				confirm_memo: '',
				completion_inst: '', // 填写提示
				upload_inst: '', // 附件提示
				explain_attachment: false, // 填写提示文件
			},
			computed: {
				confirmTip() {
					return ['未开启','未填报','存为草稿','已提交','已审核'
					,'','','','','已驳回'][this.confirm] || '';
				},
				isTip() {
					return this.completion_inst || this.explain_attachment
				},
			},
			methods: {
				init() {
					var timer = setInterval(_ => {
						if(window.closeLoading) {
							clearInterval(timer);
							window.closeLoading();
						}
					},100)
				},
				bulid_changeState(confirm) {
					this.confirm = +confirm;
					this.confirm_memo = '';
					if (this.confirm === 3) {
						$("#ComitBtn,#KeepBtn,#testList,#importCorp,#importProd").addClass('layui-btn-disabled');
						$('#testList').off().data('havaEvents', false);
					} else if(this.confirm === 4) {
						$(".sub-state").html('<span>状态：已审核</span>');
						$("#ComitBtn,#KeepBtn,#testList,#importCorp,#importProd").addClass('layui-btn-disabled');
						$('#testList').off().data('havaEvents', false);
					} else if(this.confirm === 9) {
						this.queryTaskDetail();
					}
				},
				// 查询驳回原因
				queryTaskDetail() {
					const that = this
					$.ajax({
						url: 'http://' + variable + '/taskDetail/selectById',
						data: {
							f_task_dtl_id: task_dtl_id
						},
						type: "post",
						async: true,
						xhrFields: {
							withCredentials: true
						},
						success(data) {
							var str = JSON.parse(data);
							var memo = '';
							if (str.f_confirm_memo != null && str.f_confirm_memo !== "null") {
								memo = str.f_confirm_memo;
							}
							that.confirm_memo = '驳回原因：' + memo
						},
					});
				},
				productPicSort(prodId,picId,order,isUp=false) {
					let data = allPicTable
					const prodIdx = data.findIndex(it => it.serveProductPicId == picId)
					if(prodIdx >-1 ) {
						if(isUp) { // 上移
							if(prodIdx) {
								data.forEach((it,idx) => {
									if(idx === prodIdx) {
										it.showOrder = prodIdx - 1 +1
									} else if(idx === prodIdx - 1) {
										it.showOrder = prodIdx + 1
									} else {
										it.showOrder = idx + 1
									}
								})
							}
						} else { // 下移
							if(prodIdx < data.length) {
								data.forEach((it,idx) => {
									if(idx === prodIdx) {
										it.showOrder = prodIdx + 1 + 1
									} else if(idx === prodIdx + 1) {
										it.showOrder = prodIdx + 1
									} else {
										it.showOrder = idx + 1
									}
								})
							}
						}
						data.sort((a,b)=> a.showOrder - b.showOrder)
						loadPicTable(data);
					}
				},
				productSort(prodId,order,isUp=false) {
					let data = util2.dataCata.productList
					const prodIdx = data.findIndex(it => it.serveProductId == prodId)
					if(prodIdx >-1 ) {
						if(isUp) { // 上移
							if(prodIdx) {
								data.forEach((it,idx) => {
									if(idx === prodIdx) {
										it.showOrder = prodIdx - 1 +1
									} else if(idx === prodIdx - 1) {
										it.showOrder = prodIdx + 1
									} else {
										it.showOrder = idx + 1
									}
								})
							}
						} else { // 下移
							if(prodIdx < data.length) {
								data.forEach((it,idx) => {
									if(idx === prodIdx) {
										it.showOrder = prodIdx + 1 + 1
									} else if(idx === prodIdx + 1) {
										it.showOrder = prodIdx + 1
									} else {
										it.showOrder = idx + 1
									}
								})
							}
						}
						data.sort((a,b)=> a.showOrder - b.showOrder)
						productTableReload();
					}
					// layer.prompt({
					// 	formType: 0,
					// 	value: order || '',
					// 	title: '请输入排序数值 (>=0,越小越靠前,不填为0)',
					// 	area: ['800px', '350px'] //自定义文本域宽高
					// }, function(value, index, elem){
					// 	// console.log(value, index, elem); //得到value
					// 	const product = util2.dataCata.productList.filter(it => it.serveProductId == prodId)[0]
					// 	product.showOrder = Math.max(0,+value);
					// 	saveProductByData(product);
					// 	layer.close(index);
					// });
				},
			},
		});

		// window.util.init(); // 加载行业分类
		// loadclient();
		getTaskDelStates();
		// loadename();
		// 同步加载字段
		loadInformation("CATA"); //会刊
		loadInformation("COMPANY"); //公司
		loadInformation("PRODUCT"); //产品
		loadInformation("UNIVERSAL");
		getCustomizedField(); //加载自定义字段
		// f_org_num
		loadExProperty();
		// 产品信息添加*号
		isNullAdd();
		// 查询附件
		loadAcc();
		// temporary() //自动暂存
		// loadCompanyData();
		loadCatalog();
		loadServeBoothData();
		// $('#companyPicUploader').change(function(){
		//	const file = this.files[0]
		//	if (!file) return
		//	uploadImg(file)
		// });
		app.init();
	});

	function getTaskDelStates() {
		$("#revokeBtn,#cancelBtn").hide();
		$.ajax({
			url: 'http://' + variable + '/taskDetail/selectById?f_task_dtl_id=' + task_dtl_id,
			dataType: "json",
			type: "post",
			async: false,
			success: function(data) {
				f_confirm = data.f_confirm;
				if (f_confirm != 1 && f_confirm != 2 && f_confirm != 9) {
					$(".layui-input").attr("readonly", "readonly");
					$(".layui-textarea").attr("readonly", "readonly");
					// $("#ComitBtn").hide();
					$(".non-operational").hide();
				}
				if (f_confirm == 3) {
					$("#revokeBtn").show()
					$(".non-operational").show();
				} else if (f_confirm == 4) {
					if(cataCancel) $("#cancelBtn").show()
					$(".non-operational").show();
				}
				app.bulid_changeState(f_confirm);
			}
		})
	}
	// 公司名称
	function loadServeBoothData() {
		// 查询展商信息
		$.ajax({
			url: variableSponsor + '/serveBooth/queryByMapOne',
			data: {
				projectId: pid,
				clientId: cid
			},
			dataType: "json", //返回数据类型
			type: "post",
			success: function(data) {
				console.log('queryByMapOne --- ', data);
				const catalogueId = (util2.dataCata.catalogueList && util2.dataCata.catalogueList.length) ? +util2.dataCata.catalogueList[0].f_serve_catalogue_id : 0;
				if(data){
					if(!catalogueId && rb.isNotEmpty(data.clientName)){
						companyName = data.clientName
						$("#catalogue-settings-checkbox-f_company_name").val(companyName);
						// $("#boothNum").val(data.boothNum);
						// companyName = data.clientName;
					}
				}else{
					rb.msg("加载展商信息失败")
				}
			}
		});
	}
	/*** 加载是否勾选 */
	function loadInformation(kindCode) {
		$.ajax({
			url: variableSponsor + '/exhibition/selectExhibitSetting',
			data: {
				exhibitCode: exhibitCode, //展会编号
				taskKindCode: kindCode, //CATA表示会刊
				taskId: f_task_id,
			},
			type: "post",
			async: false,
		}).done(function(data) {
			if (!data) goLogin();
			var res = JSON.parse(data);
			if (res.length == 0) {
				$(".set-bottom-bot-img").addClass("layui-hide");
			}
			for (var i = 0; i < res.length; i++) {
					if (kindCode === 'CATA') { //会刊
						if (res[i].paramKind === 'show') {
							if (res[i].value === '0') {
								$("#catalogue-settings-checkbox-" + res[i].param).parent().parent(".layui-form-item").remove();
								$("#catalogue-settings-checkbox-" + res[i].param).parent().parent(".layui-form-item").remove();
								$("#" + res[i].param).parent().parent().remove();
								if('f_company_name_en' === res[i].param){
									$("#exhibitor-huikan-f_company_name-show").addClass("hide");
								}
							} else {
								if (!res[i].isNull) {
									$("#catalogue-settings-checkbox-" + res[i].param).removeClass("textss");
									$("#catalogue-settings-checkbox-" + res[i].param).parent().prev().find("i").remove();
									if (res[i].param === 'f_type_product' || res[i].param === 'f_type_product_en') {
										$("#" + res[i].param).removeClass("textss");
										$("#" + res[i].param).parent().prev().find("i").remove();
									}
								}
							}
						}
					} else if (kindCode === 'COMPANY') { //公司
						if (res[i].paramKind === 'show') {
							if (res[i].value === '0') {
								$("#company-display-settings-checkbox-" + res[i].param).parent().parent().remove();
							} else {
								if (!res[i].isNull) {
									$("#company-display-settings-checkbox-" + res[i].param).removeClass("textss");
									$("#company-display-settings-checkbox-" + res[i].param).parent().prev().find("i").remove();
								}
							}
						}
					} else if (kindCode === 'PRODUCT') { //产品信息
						if (res[i].param === 'isPic') {
							if (res[i].value === '0') {
								$(".set-bottom-bot-img").addClass("layui-hide");
							} else {
								$(".all_product").addClass("layui-hide");
							}
						} else {
							//是否必填
							if (res[i].value === '0') {
								if(res[i].param == 'productTypeEn'){
									$("#tradeNameEn").parent().parent(".layui-form-item").remove();

								}else if(res[i].param == 'productType'){
									$("#tradeName").parent().parent(".layui-form-item").remove();
								}
								$("#" + res[i].param).parent().parent(".layui-form-item").remove();
								$("#" + res[i].param).parent().parent().remove();

							} else {
								if (!res[i].isNull) {
									$("#" + res[i].param).removeClass("texts");
									$("#" + res[i].param).parent().prev().find("i").remove();
									$("#product-display-" + res[i].param).addClass("hide1");
								}
							}
						}
					} else if (kindCode === 'UNIVERSAL') { //通用设置
						//显示附件和logo
						if (res[i].paramKind === 'show') {
							if (res[i].value !== '1') {
								if(res[i].param == 'isUploadCompanyImg' || res[i].param == 'isUploadLogo') {
									$("." + res[i].param).parent().parent().remove();
								}else{
									$("." + res[i].param).addClass("layui-hide");
								}
								if (res[i].param === 'companyDisplay') {
									// $("#companyForm").remove();
									companyDisplay = false;
									$(".hk_company").remove()
								}
								if (res[i].param === 'isUploadAnnex') {
									$(".isUploadAnnex").remove();
								}
								if (res[i].param === 'productDisplay') {
									$(".hk_product").remove();
								}
								if (res[i].param === 'cataDisplay') {
									$("#formTest").remove();
									cataDisplay = false;
								}
								if (res[i].param === 'supplyAndDemand') {
									$("#company-display-settings-checkbox-supplyAndDemand").parent().parent().remove();
								}
								//隐藏公司和产品页面
								if (res[i].param === 'companyDisplay') {
									// $("#companyForm").remove();
									$(".hk_company").remove()
								}
								if (res[i].param === 'productDisplay') {
									$(".hk_product").remove();
								}
								if (res[i].param === 'cataDisplay') {
									$("#formTest").remove();
								}
								//公司信息不能修改
								if (res[i].param === 'companyIsUpdate') {
									companyIsUpdate = false;
									// 禁用修改
									$('#catalogue-settings-checkbox-f_company_name').attr('disabled','disabled')
									$('#company-display-settings-checkbox-companyName').attr('disabled','disabled')
								}
								//产品信息不能修改
								if (res[i].param === 'productIsUpdate') {
									productIsUpdate = false;
								}
								// 撤回审核
								if (res[i].param === 'cancelEnable') {
									cataCancel = false;
								}
							} else if (res[i].value === '1') {
								$("." + res[i].param).addClass("show");
								if (res[i].param == 'isUploadCompanyImg') {
									showCompanyImg = true
								}
								if (res[i].param == 'isUploadLogo') {
									showLogo = true
								}
							}
						}
						//字数控制
						if (res[i].paramKind === 'value') {
							if (res[i].param === 'uploadLogoClaim') { //logo上传要求
								$("#uploadLogoClaim").html(res[i].value)
							} else if (res[i].param === 'companyProfileCount') { //公司简介最多显示
								// console.log(res[i])
								companyProfileCount = res[i].value;
								$(".company-profile").prop("placeholder", "最多输入" + res[i].value + "字 ")
							} else if(res[i].param === 'companyProfileEnCount'){//公司简介(英文)最多显示
								// console.log(res[i])
								companyProfileEnCount = res[i].value;
								$(".company_profile_en").prop("placeholder", "最多输入" + res[i].value + "字 ")
							} else if (res[i].param === 'productProfileCount') { //产品简介最多显示
								if (res[i].value != "" && res[i].value != null) {
									productProfileCount = res[i].value;
									$(".product_profile_count").prop("placeholder", "最多输入" + res[i].value + "字 ")
								} else {
									productProfileCount = 100;
									$(".product_profile_count").prop("placeholder", "最多输入100字 ")
								}
							} else if (res[i].param === 'productProfileEnCount') { //产品简介(英文)最多显示
								if (res[i].value != "" && res[i].value != null) {
									productProfileEnCount = res[i].value;
									$(".product_profile_count_en").prop("placeholder", "最多输入" + res[i].value + "字 ")
								} else {
									productProfileEnCount = 100;
									$(".product_profile_count_en").prop("placeholder", "最多输入100字 ")
								}
							} else if (res[i].param === 'maxUploadProductCount') { //最多上传多少个产品
								if (res[i].value != "" && res[i].value != null) {
									maxUploadProductCount = res[i].value;
									$("#maxUploadProductCount").html(res[i].value == '' ? 10 : res[i].value);
								} else {
									maxUploadProductCount = 9;
									$("#maxUploadProductCount").html(res[i].value == '' ? 10 : res[i].value);
								}
								// console.log($("#maxUploadProductCount").html());;
							} else if (res[i].param === 'maxUploadProductPicCount') { //单个产品图片数量
								if (res[i].value != "" && res[i].value != null) {
									maxUploadProductPicCount = res[i].value;
								} else {
									maxUploadProductPicCount = 3;
								}
							} else if (res[i].param == 'attachment') {
								if (res[i].value != null && res[i].value != '') {
									var attach = JSON.parse(res[i].value);
									$.each(attach, function(key, val) {
										var html =
										'<a  href="javascript:void(0);" class="layui-btn" ' +
										'fileName="' + key + '" filePath="' + val + '" onclick="downAttch(this)" >下载填写说明</a>'
										$("#explain-attachment").html(html);
										app.explain_attachment = true
										return false; //跳出循环
									});
								}
							} else if (res[i].param === 'completion_inst') { // 填写提示
								app.completion_inst = res[i].value || '';
							} else if (res[i].param === 'upload_inst') { // 附件说明
								app.upload_inst = res[i].value || ''
								$('#upload_inst').html(res[i].value || '');
							}
						}
					}
				}
		}).fail(data => {
			console.error(data)
			layer.msg('获取任务设置失败');
		});
	}
	function loadclient() {
		// $.ajax({
		// 	// url:  http://'+variable+'/proj_controct/getProj_controct_id?f_project_id=139&f_client_id=155769
		// 	url: 'http://' + variable + '/contract/loadContractrBypidCid',
		// 	data: {
		// 		Pid: pid,
		// 		Cid: cid
		// 	},
		// 	type: "post",
		// 	async: false,
		// 	xhrFields: {
		// 		withCredentials: true
		// 	},
		// }).done(function(data) {
		// 	if (!data) goLogin();
		// 	var res = JSON.parse(data);
		// 	var rows = res.rows;
		// 	$("#CompanyName").html(rows.f_client_name);
		// 	$("#BoothCode").html(rows.f_booth_num);
		// 	$(".BoothCode").html(rows.f_booth_num);
		// 	$(".BoothType").html(rows.f_booth_type_code);
		// 	$(".CompanyName").html(rows.f_company_name);
		// 	app.bulid_changeState(f_confirm)
		// 	boothType = rows.f_booth_type_code;
		// }).fail(function(data) {
		// 	console.error(data);
		// 	layer.msg('获取客户信息失败');
		// });
	}
	function loadename() {
		// $.ajax({
		// 	url: 'http://' + variable + '/proj_controct/getEname', //请求服务器url地址.   http://'+variable+'/proj_controct/getProj_controct_id?f_project_id=139&f_client_id=155769
		// 	data: {
		// 		f_project_id: pid
		// 	},
		// 	type: "post",
		// 	async: false, //同步，防止重复提交
		// 	xhrFields: {
		// 		withCredentials: true
		// 	},
		// }).done(data => {
		// 	if (!data) {
		// 		layer.msg('登录超时，即将返回登录页面', {
		// 			icon: 2,
		// 			end() {
		// 				goLogin()
		// 			}
		// 		});
		// 	}
		// 	var res = JSON.parse(data);
		// 	var rows = res.data;
		// 	$("#exhibitName").html(rows.f_exihibit_name);
		// 	ecode = rows.f_exhibit_code;
		// 	return false;
		// }).fail(data => {
		// 	console.error(data)
		// 	// layui.layer.msg(data);
		// })
	}
	function loadCatalog() {
		$.ajax({
			// url: 'http://' + variable + '/catalog/select', //http://'+variable+'/proj_controct/getProj_controct_id?f_project_id=139&f_client_id=155769,
			// data: {
			// 	pid: pid,
			// 	cid: cid
			// },
			url: 'http://' + variable + '/admin/catalogCompanyProduct/getOne',
			data: {
				projectId: pid,
				clientId: cid,
				taskDtlId: task_dtl_id,
			},
			type: "post",
			dataType: 'json',
			async: false, //同步，防止重复提交
			xhrFields: {
				withCredentials: true
			}
		}).done(function(data) {
			// // console.log(data)
			// //session过期，服务端不返回数据，得重新登录一次
			// if ((data == null) || (data === "")) {
			// 	layer.msg('登录超时，即将返回登录页面', {
			// 		icon: 2,
			// 		end() {
			// 			goLogin()
			// 		}
			// 	});
			// }
			// var array = eval('(' + data + ')');
			// // console.log(data)
			// console.log(array);
			// var res = array.result;
			util2.checkReturn(data);
			util2.dataCata = data.data;
			if (+data.state === 1) {
				var catalog = util2.dataCata.catalogueList[0] || {};
				// console.log("catalogueselect: " + catalog)
				//设置表单输入框隐藏id 为数据库数据 的id f_serve_catalogue_id 用以后台辨别更新或者是添加
				$("#id_f_serve_catalogue_id").val(catalog.f_serve_catalogue_id)
				//自定义字段赋值
				if (catalog.f_custom_field_json != null) {
					var customField = JSON.parse(catalog.f_custom_field_json);
					for (var i in customField) {
						$("#" + i).val(customField[i]);
					}
				}
				// 产品图片回显
				if (catalog.f_cata_product_img != null && catalog.f_cata_product_img !== '') {
					var arr = catalog.f_cata_product_img.split(',');
					for (j = 0, len = arr.length; j < len; j++) {
						if (arr[j] === '') continue;
						var html = '<div class="product-img" id="' + arr[j] + '">' +
							'  <img src="' + variablePic + arr[j] + '" alt="" class="layui-upload-img cata-product-img" >\n' +
							'  <div class="product-set">\n' +
							'  <a href="javascript:void(0)"  style="text-align: center" onclick="delPic(this)">删除</a>\n' +
							' </div>' +
							' </div>';
						$("#choisePic").before(html);
					}
				}
				//附件回显
				if (catalog.f_cata_attach != null && catalog.f_cata_attach !== '') {
					var f_cata_attach = JSON.parse(catalog.f_cata_attach);
					$.each(f_cata_attach, function(key, val) {
						var html = '<div class="allFile"><div class="fileText">' + key +
							'</div><span class="allFilespan"><img src="../../../img/close.png" ></span></div>'
						$("#upload-fujian").append(html);
						loadform(catalog);
						return false;
					});
				}
				// 会刊信息
				loadform(catalog);
				// 公司
				var res = util2.dataCata.companyList[0] || '';
				if (res) {
					//自定义字段赋值
					if (res.customFieldJson) {
						// console.log(res.customFieldJson)
						var customField = JSON.parse(res.customFieldJson);
						for (var i in customField) {
							$("#" + i).val(customField[i]);
						}
					}
					if(parseInt(f_confirm) === 1){
						// rb.msg('会刊未填报')
						// 设置展商公司名
						if(res && res.companyName){
							let iptPre = '#catalogue-settings-checkbox-'
							$(iptPre + 'f_company_name').val(res.companyName);
						}
					}
					loadCompanyform(res);
				}
				// 产品
				productTableReload()
				// 附件
				// table.reload('accTable', {
				// 	data: util2.dataCata.docLists || [],
				// });
			}
		}).fail(data => {
			console.error(data)
			layer.msg('获取任务信息失败');
		})
	}
	function loadCompanyform(data) {
		var companyName = stroage.getItem('companyName');
		$.each(data, function(key, value) {
			if (data[key] == "null") {
				data[key] = ""
			}
		})
		layui.form.val("companyForm", {
			"serveCompanyId": data.serveCompanyId,
			"companyEngName": data.companyEngName,
			"linkman": data.linkman,
			"position": data.position,
			"fax": data.fax,
			"email": data.email,
			"tel": data.tel,
			"phone": data.phone,
			"website": data.website,
			"links": data.links,
			"liveLinks": data.liveLinks,
			"supplyAndDemand": data.supplyAndDemand,
			"address": data.address,
			"logo": data.logo,
			"pic": data.pic,
			"businessLicense": data.businessLicense,
			"productType": data.productType,
			"companyAbbr": data.companyAbbr,
			"profile": data.profile
		});
		serveCompanyId = data.serveCompanyId
		$('#logoImg').attr('src', data.logo);
		// $("#exhibitor-huikan-product-supplyAndDemand").textbox("setValue",data.supplyAndDemand)
		$('#companyPicImg').attr('src', data.pic);
		$('#companyAuthImg').attr('src', data.businessLicense);
		$('#profile').html(data.profile);
		if (data.companyName != "" && data.companyName != null) {
			$("#company-display-settings-checkbox-companyName").val(data.companyName)
		} else {
			$("#company-display-settings-checkbox-companyName").val(companyName)
		}
		layui.layedit.setContent(index, data.profile, false); //给富文本框赋值
	}

	window.loadform = function (data, isImport = false) {
		var companyName = stroage.getItem('companyName');
		const formVals = {
			"f_company_name_en": data.f_company_name_en,
			"f_company_name": data.f_company_name,
			"f_contacts_name": data.f_contacts_name,
			"f_contacts_en": data.f_contacts_en,
			"f_tel": data.f_tel,
			"f_fax": data.f_fax,
			"f_email": data.f_email,
			"f_site": data.f_site,
			"f_type_product": data.f_type_product,
			"f_type_product_en": data.f_type_product_en,
			"f_company_profile": data.f_company_profile,
			"f_company_profile_en": data.f_company_profile_en,
			"check[write]": true,
			"open": false,
			"f_memo": data.f_memo,
			"f_post": data.f_post,
			"f_post_en": data.f_post_en,
			// "f_serve_catalogue_id": data.f_serve_catalogue_id,
			"f_task_id": task_dtl_id,
			"f_company_address": data.f_company_address,
			"f_company_address_en": data.f_company_address_en,
			"f_store_link": data.f_store_link,
			"f_live_link": data.f_live_link,
			"tradeId":data.tradeId,
			"tradeIdEn":data.tradeIdEn,
			"tradeName":data.tradeName,
			"tradeNameEn":data.tradeNameEn,
		}
		if(!isImport){ // 导入时不替换会刊id
			formVals["f_serve_catalogue_id"] = data.f_serve_catalogue_id
		}
		layui.form.val("formTest", formVals );
	}
	// 保存事件
	window.submit = function() {
		$(".f_client_id").val(cid);
		$(".f_project_id").val(pid);
		$(".f_task_id").val(task_dtl_id);
		var customFieldJson = {};
		//会刊自定义字段
		$(".cata").each(function(index, element) {
			var fieldname = $(element).attr("fieldname");
			customFieldJson[fieldname] = $(element).val();
		});
		$(".f_custom_field_json").val(JSON.stringify(customFieldJson));

		var f_cata_product_img = '';
		$(".cata-product-img").each(function() {
			f_cata_product_img += $(this).attr("src").replace(variablePic, "") + ",";
		});
		$(".f_cata_product_img").val(f_cata_product_img);
		//附件上传
		var obj = {};
		$(".f_cata_attach_file").each(function() {
			obj[$(this).attr("attrname")] = $(this).attr("attrsrc");
		});
		$(".f_cata_attach").val(JSON.stringify(obj));
		//
		let catalogue = (util2.dataCata.catalogueList && util2.dataCata.catalogueList.length) ? util2.dataCata.catalogueList[0] : {}
		let serveProducts = util2.dataCata.productList || []
		let serveCompany = getCompanyData();
		let formData2 = {
			editMode: catalogue.f_serve_catalogue_id ? 'Modify' : 'Add',
			clientId: cid,
			projectId: pid,
			taskDtlId: task_dtl_id,
			// serveCompany,
			// catalogue,
			// serveProducts,
		}

		// catalogue = $('#formTest').serializeArray();
		catalogue = buildFormArr('formTest') || {}
		//

		$.each(catalogue,(name,value) =>{
			formData2['catalogue.' + name] = value
		})
		// Object.keys(catalogue).forEach(k =>{
		// 	formData2['catalogue.'+k] = catalogue[k]
		// })
		Object.keys(serveCompany).forEach(k =>{
			formData2['serveCompany.'+k] = serveCompany[k]
		})
		serveProducts.forEach((prod, i) => {
		 	Object.keys(prod).forEach(k =>{
		 		if(k !== 'serveProductFavorites'){
		 			if(k === 'serveProductPiclist'){
		 				prod[k].forEach((prodPic, ii) =>{
		 					Object.keys(prodPic).forEach(kk =>{
		 						formData2[`serveProducts[${i}].serveProductPiclist[${ii}].${kk}`] = prodPic[kk]
		 					})
		 				})
		 			}else{
						if(!['createTime','updateTime'].includes(k)){
							formData2[`serveProducts[${i}].${k}`] = prod[k]
						}
		 			}
		 		}
		 	})
		})
		let idx = window.loading('',function() {
			$("#ComitBtn,#KeepBtn").addClass('layui-btn-disabled').prop('disabled', true);
		},function() {
			$("#ComitBtn,#KeepBtn").removeClass('layui-btn-disabled').prop('disabled', false);
		});
		setTimeout( _ => {
			$.ajax({
				url: `http://${variable}/admin/catalogCompanyProduct/save`,
				data: formData2,
				type: "post",
				// beforeSend: function() {
				// 	$("#load").show();
				// },
				success: function(data) {
					util2.checkReturn(data, data.msg || (state == 3 ? '提交审核失败,请稍后再试!' : '保存失败'));
					ChangeConfirm(true);
					return false;
				},
				complete: function() {
					window.loaded(idx, function() {
						$("#ComitBtn,#KeepBtn").removeClass('layui-btn-disabled').prop('disabled', false);
					})
					// $("#load").hide();
					return false;
				}
			});
		}, 500);
	};
	// 暂存
	$("#KeepBtn").click(function() {
		if ($("#KeepBtn").hasClass('layui-btn-disabled')) {
			layer.msg('已提交，无法操作', {
				icon: 2
			});
			return false;
		} else {
			state = 2;
			$("#clibtn").click();
			return false;
		}
	})
	// 提交审核
	function ChangeConfirm(isReload, isCancel) {
		$.ajax({
			url: 'http://' + variable + '/taskDetail/ChangeConfirm',
			data: {
				f_task_dtl_id: task_dtl_id,
				f_confirm: state
			},
			type: "post",
			success: function(data) {
				getTaskDelStates();
				if (state === 3) {
					layer.msg('操作成功，即将返回主页', {
						icon: 1,
						success: function(layero, index){
							setTimeout(() => {
								history.go(-1)
							}, 2000);
						},
						// end: function() {
						// 	history.go(-1)
						// }
					});
				} else {
					if(isReload)
					layer.msg(isCancel ? '商家发布信息 撤销成功!' : '商家发布信息 保存成功!', {
						icon: 1,
						success: function(layero, index){
							if (isReload) {
								setTimeout(() => {
									location.reload()
								}, 1000);
							}
						},
					})
				}
			}
		});
	}
	// 撤销提交
	$("#revokeBtn,#cancelBtn").click(function() {
		isReload = true;
		state = 2;
		$(".non-operational").hide();
		ChangeConfirm(true, true)
	})

	// 提交检查 + 提交
	$("#ComitBtn").click(function() {
		console.log('tijiao')
		const $textssList = $(".textss");
		try{
			$textssList.each(function(){
				const $this = $(this)
				const currentID = $this.attr('id')
				const currentErrorMessage = $this.parent().prev().text() + '为必填项!'
				if(!this.value){
					layer.msg(currentErrorMessage, {icon: 2})
					throw Error('invalid')
				}

				if(currentID === 'catalogue-settings-checkbox-tradeName2' &&
						$('#catalogue-settings-checkbox-tradeName').val() == 0){
					layer.msg(currentErrorMessage, {icon: 2})
					throw Error('invalid')
				}

				if(currentID === 'catalogue-settings-checkbox-tradeNameEn2' &&
						$('#catalogue-settings-checkbox-tradeNameEn').val() == 0){
					layer.msg(currentErrorMessage, {icon: 2})
					throw Error('invalid')
				}
			})
		}catch(e){
			if(e.message === 'invalid')return false
		}

		if (showLogo == true) {
			var companyPic = $("#companyPic").val()
			if (companyPic == "" || companyPic == null || companyPic == undefined) {
				layer.msg('公司Logo为必填项!', {
					icon: 2
				});
				return false;
			}
		}
		if ( showCompanyImg == true) {
			var logo = $("#logo").val()
			if (logo == "" || logo == null || logo == undefined) {
				layer.msg('公司图片为必填项!', {
					icon: 2
				});
				return false;
			}
		}
		//公司简介最大值
		var profile = $(".company-profile");
		console.log(profile);
		console.log(companyProfileEnCount);
		for (var i = 0; i < profile.length; i++) {
			var proFileVal = profile[i].value;
			if (proFileVal.length > companyProfileCount) {
				layer.msg('公司简介字数最大字数' + companyProfileCount + '字', {
					icon: 2
				});
				return false;
			}
		}
		var profileEn = $(".company_profile_en");
		for( var i = 0; i < profileEn.length; i++){
			var proFileValEn = profileEn[i].value;
			if (proFileValEn.length > companyProfileEnCount) {
				layer.msg('公司简介(英文)字数最大字数' + companyProfileEnCount + '字', {
					icon: 2
				});
				return false;
			}
		}
		if (!$(".all_product").hasClass("layui-hide")) {
			var $hkProduct = $(".hk_product")[0];
			//检查表格行数是否超过最大值
			if ($hkProduct != undefined && table.cache.producttable.length === 0) {
				layer.msg('产品未上传!', {
					icon: 2
				});
				return false;
			}
		}
		if ($("#KeepBtn").hasClass("layui-btn-disabled")) {
			$("#ComitBtn").addClass("layui-btn-disabled");
			//alert("不可提交")
			layer.msg('已提交，无法操作', {
				icon: 2
			});
			return false;
		} else {
			layer.confirm('请谨慎提交，主办方审核通过后将不可修改，是否确定提交!', function(index) {
				console.log('确认提交');
				layer.close(index);
				$("#ComitBtn").removeClass("layui-btn-disabled");
				state = 3;
				$(".non-operational").show();
				$("#clibtn").click();
			});
			return false;
		}
	})

	// 分类
	var tradeNameId
	var tradeName
	var tradeNameEn
	window.getProType = function() {
		ifSumbitError()
		tradeNameId = ""

		tradeName = ""
		tradeNameEn = ""
		table.reload('proType', {
			data: []
		});
		//页面层
		layertabless = layer.open({
			type: 1,
			title: '产品类型',
			skin: 'layui-layer-demo', //加上边框
			area: ['920', '500px'], //宽高
			content: $('#getProType'),
			btn: ['保存', '关闭'],
			yes: function(index, layero) {
				//按钮【按钮一】的回调
				var type = eval(layui.table.checkStatus('proType').data);
				console.log(type)
				console.log('产品类型保存')
				$('#tradeName').val(tradeName)
				$('#tradeNameEn').val(tradeNameEn)
				$('#tradeNameEns').val(tradeNameId)
				$('#tradeNameIds').val(tradeNameId)
				$('#productType').val(type[0].f_type_name)
				$('#productTypeNameID').val(type[0].f_product_type_id)
				$('#productTypeEn').val(type[0].f_type_code)
				$('#productTypeEnID').val(type[0].f_product_type_id)
				layer.close(index);
			},
			btn2: function(index, layero) {
			},
			cancel: function() {
				// var confim = layer.confirm('确认退出编辑？', {
				// 	btn: ['确认', '取消'] //按钮
				// }, function() {
				// 	layer.close(layertabless);
				// 	layer.close(confim);
				// }, function() {
				// 	layer.close(confim);
				// });
				// return false
			},
			end: function() {
				console.log("colse")
			}
		});
		getIndustry()

	}
	window.getIndustryDiv = function() {
		ifSumbitError()
		//页面层
		layertables = layer.open({
			type: 1,
			title: '行业分类',
			skin: 'layui-layer-demo', //加上边框
			area: ['300px', '400px'], //宽高
			content: $('#getIndustryType'),
			cancel: function() {
				var confim = layer.confirm('确认退出编辑？', {
					btn: ['确认', '取消'] //按钮
				}, function() {
					layer.close(layertables);
					layer.close(confim);
				}, function() {
					layer.close(confim);
				});
				return false
			},
			end: function() {
				// console.log("colse")
			}
		});
		getIndustry()
	}
	// 树菜单
	// var datass
	window.getIndustry = function() {
		console.log('打开树菜单');
		$.ajax({
			url: variableSponsor + '/tradeClass/selectTradeTree',
			data: {
				projectId: pid
			},
			type: "post",
			async: false,
			xhrFields: {
				withCredentials: true
			},
			beforeSend: function() {},
			success: function(data1) {
				console.log(data1);
				loadProductType($('#catalogue-settings-checkbox-tradeName').val())
				tradeNameId = $('#catalogue-settings-checkbox-tradeName').val()
				tradeName = $('#catalogue-settings-checkbox-tradeName2').val()
				tradeNameEn =  $('#catalogue-settings-checkbox-tradeNameEn2').val()
				tree.render({//产品类型里的行业树菜单
					elem: '#test2',
					data: data1.data,
					onlyIconControl: true, //是否仅允许节点左侧图标控制展开收缩
					checked:true,
					id:'tradeId',
					spread:true,
					click: function(obj) {
						console.log('产品类型里的行业树菜单',obj);
						loadProductType(obj.data.tradeId)
						tradeNameId = obj.data.tradeId
						tradeName = obj.data.tradeName
						tradeNameEn = obj.data.tradeCode
					},
				});
				// tree.setChecked('tradeId',$('#catalogue-settings-checkbox-tradeName').val())
				// console.log($('#catalogue-settings-checkbox-tradeName').val())
				tree.render({//外层行业树菜单
					elem: '#test3',
					data: data1.data,
					onlyIconControl: true, //是否仅允许节点左侧图标控制展开收缩
					accordion:true,
					checked:true,
					id:'tradeId',
					click: function(obj) {
						console.log('外层行业树菜单',obj)
						$('#catalogue-settings-checkbox-tradeName').val(obj.data.tradeId)
						$('#catalogue-settings-checkbox-tradeName2').val(obj.data.tradeName)
						$('#catalogue-settings-checkbox-tradeNameEn').val(obj.data.tradeId)
						$('#catalogue-settings-checkbox-tradeNameEn2').val(obj.data.tradeCode)
						layer.close(layertables);
					}
				});
				return false;
			},
			complete: function() {
				return false;
			},
			error: function(data) {
				return false;
			}
		});

	}
	window.f_org_num = 0;
	function loadExProperty() {
		$.ajax({
			url: variableSponsor + '/exhibition/selectByExhibitCode',
			data: {
				exhibitCode: exhibitCode
			},
			type: "post",
			dataType: "json",
			success: function(data) {
				f_org_num = data.f_org_num
			}
		});
	}
	function loadProductType(id) {
		$.ajax({
			url: variableSponsor + '/productTypeBase/getList', //请求服务器url地址.   +cid+"&pid="+pid
			type: "post",
			data: {
				parentTradeId: id,
				f_org_num: f_org_num
			},
			async: false,
			xhrFields: {
				withCredentials: true
			},
			beforeSend: function() {},
			success: function(data) {
				var str = JSON.parse(data)
				table.reload('proType', {
					data: str.rows,
					page: true,
					limit: '10'
				});
			},
			error: function(data) {
				table.reload('proType', {
					data: []
				});
			}
		});
	}

	/**
	 * 检查产品信息总条数
	 * @returns {boolean}
	 */
	function checkRowcount(obj) {
		//当前表格总数
		var length = obj.config.data.length;
		// $("#product_message").html("<em  style='color: red'> 产品数量已达到最大值</em>");
		return maxUploadProductCount <= length
	}
	function loadProType() {
		if (pid == null || pid === '') {
			layer.msg('产品类型数据加载失败,请刷新重试', {});
			return;
		}
		$.ajax({
			url: variableSponsor + '/productType/getByMap ', //请求服务器url地址.   +cid+"&pid="+pid
			type: "post",
			data: {
				projectId: pid
			},
			async: false,
			xhrFields: {
				withCredentials: true
			},
			success: function(data) {
				//alert(data)
				var str = JSON.parse(data)
				table.reload('proType', {
					data: str,
					page: true,
					limit: '10'
				});
			},
			error: function(data) {
				table.reload('proType', {
					data: []
				});
			}
		});
	}

	//生成自定义控件
	function getCustomizedField() {
		$.ajax({
			url: 'http://' + sponsorURL + '/customizedField/getList',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				// f_project_id: GetQueryString("pid"),
				f_task_id: f_task_id
			}, //发送数据   f_form_name: g_f_form_name
			async: false,
			success: function(data) {
				$.each(data, function(i, n) {
					var isNotNull = (data[i].f_is_null) ? 'textss ' : '';
					var productIsNotNull = (data[i].f_is_null) ? 'isNull ' : '';
					var required = (data[i].f_is_null) ? '<i>*</i> ' : '';

					var html = '<div class="layui-form-item layui-row layui-inline "> <div class="layui-col-lg2">' +
						'<label title="' + data[i].f_field_label +
						'"class="layui-form-label " style="width: 100%;border-right: 0px;display :inline-block ;overflow: hidden;textOverflow: ellipsis;whiteSpace: nowrap">' +
						required + data[i].f_field_label + '</label>' +
						'</div> <div class="  layui-col-lg10">' +
						'<input class="layui-input ' + isNotNull + data[i].f_form_name + '" fromName="' + data[i].f_form_name +
						'" id="' + data[i].f_field_name + '" fieldName="' + data[i].f_field_name +
						'" type="text" autocomplete="off" class="layui-input textss">' +
						'</div> </div>';
					if (data[i].f_form_name === 'cata') { //会刊信息
						$(".hk_add1").before(html);
					}
					if (data[i].f_form_name === 'company') { //公司信息
						$(".hk_add2").before(html);
					}
					if (data[i].f_form_name === 'product') { //产品信息
						html = '<div class="layui-col-xs6 ' + productIsNotNull + '" >' +
							' <div class="layui-form-item wth98"> <label class="layui-form-label b_g_none layui-col-lg2" title="' +
							data[i].f_field_label + '">' + data[i].f_field_label + '</label>' +
							' <div class="layui-col-lg10">' +
							'<input type="text" lay-verify="title" autocomplete="off" fromName="' + data[i].f_form_name +
							'" fieldName="' + data[i].f_field_name + '"  id="' + data[i].f_field_name + '" class="layui-input ' +
							data[i].f_form_name + '">' +
							'   </div> </div> </div>';
						$("#product-information").append(html);
						html = "<th lay-data=\"{field:'" + data[i].f_field_name + "',align:'center'}\">" + data[i].f_field_label +
							"</th>";
						$("#product-table-tr").append(html);
					}

				});
			},
			error: function() {
				$.messager.alert('提示', '加载自定义控件失败！', 'error');
			}
		});
	}

	function productTableReload(data) {
		if(!data){
			data = util2.dataCata.productList || []
		} else {
			util2.dataCata.productList = data
		}
		layui.table.reload('producttable', {
			data,
			// initSort: {
			// 	field: 'showOrder',
			// 	type: 'asc',
			// },
		});
	}

	/**添加*号 **/
	function isNullAdd() {
		$(".isNull").each(function() {
			var jQuery = $(this).find(".b_g_none").html();
			$(this).find(".b_g_none").html("<en style='color: red'>*</en>" + jQuery)
		});
	}
	/** 检查产品的必填项是否为空 **/
	function checkProductIsNull() {
		var count = 0;
		$(".isNull").each(function() {
			if (!$(this).hasClass("hide")) {
				var jQuery = $(this).find(".layui-input").val();
				if (jQuery == null) {
					jQuery = $(this).find(".layui-textarea").val()
				}
				if (jQuery == null || jQuery === '' || jQuery === 'undefined') {
					count++;
				}
			}
		});
		return count > 0;
	}
</script>
</body>
</html>