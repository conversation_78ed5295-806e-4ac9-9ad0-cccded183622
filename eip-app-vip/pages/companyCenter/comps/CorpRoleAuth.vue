<template>
<div class="project-score-setting">
  <!-- 权限角色设置 -->
  <custom-dialog
    :show="show"
    @before-close="close()"
    :title="L('corp.权限角色设置')"
    width="800px">
    <el-container v-loading="loading">
      <el-header class="dialog-header" height="40px">
        <div class="dialog-header-left">
          <span>{{ L('基本信息') }}</span>
        </div>
        <div class="dialog-header-right">
        </div>
      </el-header>
      <el-main>
        <div style="padding-bottom: 20px;" class="fix-el-icon">
          <el-link icon="el-icon-circle-plus-outline" :underline="false"
           @click="add()"
          > {{ L('新增')}}</el-link>
            &nbsp;&nbsp;&nbsp;
          <el-link icon="el-icon-delete" :underline="false"
           @click="del()"
          > {{ L('删除') }}
          </el-link>
        </div>
        <el-table ref="ruleTable"
          size="mini"
          height="200px"
          class="table-noline fix-table"
          :data="list"
          :element-loading-text="L('数据加载中')"
          element-loading-spinner="el-icon-loading"
          @row-click="r=>$refs['ruleTable'].toggleRowSelection(r)"
          v-loading="loading">
          <el-table-column
              type="selection"
              fixed="left"
              width="45">
          </el-table-column>
          <!-- <el-table-column
              fixed="left"
              type="index"
              :index="i=>(page - 1) * size + i + 1"
              width="60">
            <template slot="header">{{ L('序号') }}</template>
          </el-table-column> -->
          <el-table-column v-for="it in fields" :key="it.prop" resizable
              show-overflow-tooltip
              :prop="it.prop"
              :label="it.label"
              :width="it.width || ''"
            >
            <template slot-scope="{row}" >
              <div v-if="it.prop === 'integralSendStartTime'">
              {{ fmtTime(row.integralSendStartTime) || '~' }} - {{ fmtTime(row.integralSendEndTime) || '~' }}
              </div>
              <div v-else-if="it.prop === 'roleName'" >
                {{ row[it.prop] || '' }}
              </div>
              <div v-else >
                <el-link
                  :underline="false"
                  type="primary"
                  @click.stop="editAuth(row, it.prop)"
                >{{ L('设置') }}</el-link>
              </div>
            </template>
          </el-table-column>
          <el-table-column
              :label="L('操作')"
            >
            <template slot-scope="{row}">
              <!-- icon="el-icon-edit" -->
              <el-link
                :underline="false"
                type="primary"
                @click.stop="edit(row)"
              >{{ L('编辑') }}</el-link>
              <span style="color:#e9e9e9;padding:0 5px"></span>
              <!-- <span style="color:#e9e9e9;padding:0 5px">|</span> -->
              <!-- icon="el-icon-delete" -->
              <el-link
                  :underline="false"
                  type="primary"
                  @click.stop="del(row.roleId)"
              >{{ L('删除') }}</el-link>
            </template>
          </el-table-column>
        </el-table>
        <center  style="padding-top: 20px;">
          <el-pagination
            :style="{width : 'max-content'}"
            @size-change="sizeChange"
            @current-change="pageChange"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="size"
            :current-page.sync="page"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </center>
      </el-main>
    </el-container>
  </custom-dialog>

  <custom-dialog
    :show="set.show"
    @before-close="close('set')"
    :title="set.title"
    width="414px" >
      <!-- <el-main slot="body" class="dialog-body"  v-loading="loading"> -->
      <el-main class="dialog-body" >
        <el-header class="dialog-header" height="40px">
          <div style="color: #2E82E4;">
            <span>{{ L('基本信息') }}</span>
          </div>
          <div class="dialog-header-right" style="flex: none">
            <el-button size="mini" type="primary" :disabled="!set.form.roleName" @click="setSave()">{{ L('确定') }}</el-button>
          </div>
        </el-header>
        <!-- <div style="padding: 20px 20px 0;"></div> -->
        <el-container style="padding: 14px 40px 33px 0px;background-color: white;" >
          <el-form :model="set.form" class="mainfrom"
            label-width="120px"
            :style="{width: '100%'}"
            label-position="right"
            :rules="set.formRules"
            size="mini"
            ref="form" >
            <div style="display: grid;grid-template-columns: 1fr;grid-gap: 15px 0;" class="setBaseInfo">
              <el-form-item prop="roleName"  :label="L('corp.权限角色')">
                <el-input type="text" v-model="set.form.roleName" clearable placeholder="" />
              </el-form-item>
              <el-form-item prop="order"  :label="L('corp.显示序号')">
                <el-input type="text" v-model="set.form.order" @change="set.form.order = set.form.order === '' ? '' : (parseInt(set.form.order) || 0)" clearable placeholder="" />
              </el-form-item>
            </div>
          </el-form>
        </el-container>
      </el-main>
  </custom-dialog>

  <custom-dialog
    :show="auth.show"
    @before-close="close('auth')"
    :title="L('corp.权限调整')"
    width="414px" >
      <!-- <el-main slot="body" class="dialog-body"  v-loading="loading"> -->
      <el-main class="dialog-body" v-loading="auth.loading">
        <el-header class="dialog-header" height="40px">
          <div style="color: #2E82E4;">
            <!-- <span>基本信息</span> -->
          </div>
          <div class="dialog-header-right" style="flex: none">
            <el-button size="mini" type="primary" @click="authSave()">{{ L('确定') }}</el-button>
          </div>
        </el-header>
        <!-- <div style="padding: 20px 20px 0;"></div> -->
        <el-container style="padding: 14px 40px 33px 10px;background-color: white;" >
          <div style="display: grid;grid-template-columns: 1fr;padding: 10px 20px" class="auth-list">
            <!-- <el-checkbox :indeterminate="auth.isIndeterminate" v-model="auth.checkAll" @change="authAllChange">全选</el-checkbox>
            <el-checkbox-group v-model="auth.form.auths" @change="authChange">
              <el-checkbox v-for="it in authData" :key="it.id" :label="it.id">{{ it.name }}</el-checkbox>
            </el-checkbox-group> -->
            <!-- :default-expanded-keys="[2, 3]" -->
            <div style="padding:0 0 5px 23px;">
              <el-link :underline="false" type="primary" @click="authAllChange(1)">{{L('全选') }}</el-link> &nbsp;
              <el-link :underline="false" type="primary" @click="authAllChange()">{{ L('取消') }}</el-link>
            </div>
            <el-tree
              :data="auth.data"
              v-show="auth.showType == 'task'"
              show-checkbox
              ref="taskTree"
              node-key="taskKindCodeExt"
              default-expand-all
              :default-checked-keys="auth.form.initKeys"
              :props="{
                children: 'childList',
                label: 'taskKindName'
              }">
            </el-tree>
            <el-tree
              :data="auth.data"
              v-show="auth.showType != 'task'"
              ref="tree"
              show-checkbox
              node-key="functionCode"
              default-expand-all
              :default-checked-keys="auth.form.initKeys"
              :props="{
                children: 'childList',
                label: 'menuName'
              }">
            </el-tree>
          </div>
        </el-container>
      </el-main>
  </custom-dialog>
</div>
</template>

<script>
export default {
 name: "CorpRoleAuth",
  components: {
    CustomDialog: importC('CustomDialog'),
  },
  mixins: [ZzMixin],
  data() {
    return {
      tableHeight: '200px',
      fields: [
        {label: L('corp.权限角色'), prop: 'roleName',width: '150px',},
        // {label: '个人会员中心权限', prop: 'user',width: '200px', },
        {label: L('corp.企业会员中心权限'), prop: 'corp',width: '200px',},
        {label: L('corp.参展信息填报权限'), prop: 'task',width: '200px',},
      ],
      auth: {
        show: false,
        showType: '',
        loading: false,
        // checkAll: false,
        // isIndeterminate: true,
        data: [],
        // [{ menuId,menuName,menuNameEn,parentId,treePath,childList,functionCode}]
        // [{taskKindName,taskKindCode,taskKindNameEn,taskCategory,taskKindCodeExt}]
        form: {
          roleId: '',
          initKeys: [],
        },
        formInit: '',
      },
      set: {
        show: false,
        loading: false,
        title: '',
        form: {
          roleId: '',
          enterpriseId: '',
          roleName: '',
          order: '',
        },
        formInit: '',
      },
    }
  },
  computed: {
    isSetAdd() {
      return this.set.form.roleId;
    },
    authKeysAll(){
      let tmp = [];
      if(this.auth.showType == 'task') {
        tmp = this.auth.data.map(it=> it.taskKindCodeExt)
      } else {
        this.auth.data.map(it=> {
          (it.childList || []).map(itt => {
            tmp.push(itt.functionCode)
          })
          tmp.push(it.functionCode)
        })
      }
      return tmp
    },
    moduleCode() {
      return this.auth.showType == 'task' ? 'FILL_EXHIBITOR_INFO' : 'ENTERPRISE_MEMBER_CENTER'
    },
  },
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  created () {
    this.set.formInit = JSON.stringify(this.set.form)
    this.auth.formInit = JSON.stringify(this.auth.form)
  },
  methods:{
    // errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
    //   return this.$alert(msg, title, {type})
    // },
    error(msg, throws = true) { //  用element的 $alert
      msg = msg || '请求失败';
      if(this.loading) this.loading = false;
      if(this.set.loading) this.set.loading = false;
      if(this.auth.loading) this.auth.loading = false;
      if(window.fullloading) window.fullloading(true);
      const p = this.errorMsg(msg, '提醒', 'warning');
      if(throws) throw new Error(msg)
      return p;
    },
    // ok(message) {
    //   message = message || '操作成功!';
    //   return this.$message({
    //     message,
    //     type: 'success'
    //   });
    // },
    fullloading(close = false, ops = {}) {
      // window.app = this;
      if(close) {
        if(window.$loading) {
          setTimeout(() => {
            window.$loading.close();
          }, 300);
        }
      } else {
        window.$loading = this.$loading({
          lock: true,
          text: '操作中 ...',
          // body: false,
          // fullscreen: true,
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
          ...ops
        });
      }
    },
    close(module=''){
      if(module) this[module].show = false
      else this.show = false
    },
    fmtTime(val) {
      if(val) {
        try{
          return val.replaceAll('-', '.').substr(0,16)
        }catch(e) {
          return val + '';
        }
      }
      return '';
    },
    async initSet() {
      this.set.form = JSON.parse(this.set.formInit)
      // this.setSelectCerts = [];
      // this.checkPoints = [];
      // await Promise.all([
      //   this.getInviteResults(),
      //   this.getScoreChannels(),
      // ])
      // bug
      this.$refs['setForm'] && this.$refs['setForm'].clearValidate()
    },
    async add() {
      await this.initSet()
      this.set.title = L('添加') + ' ' + L('corp.权限角色')
      this.set.show = true
    },
    async edit(row) {
      await this.initSet()
      Object.keys(this.set.form).forEach(k => {
        if(row.hasOwnProperty(k)) {
          this.set.form[k] = row[k] === null ?  '' : row[k];
        }
      })
      this.set.show = true
      this.set.title = L('编辑') + ' ' + L('corp.权限角色')
    },
    async editAuth(it, prop) {
      this.auth.showType = prop || ''; // user/corp/task
      this.auth.form.roleId = it.roleId;
      this.auth.form.initKeys = [];
      // this.auth.loading = true
      this.auth.data = [];
      await Promise.all([
        this.getRoleAuth(),
        this.getDataAuth()
      ])
      this.auth.show = true
    },
    async getRoleAuth() {
      const {data} = await Axios.post('/enterpriseRoleFunction/getRoleFunction', this.obj2fd({
          roleId: this.auth.form.roleId,
          moduleCode: this.moduleCode
        })).catch(_ => this.error());
      this.checkRet(data);
      this.auth.form.initKeys = (data.data || []).map(it=> (it.taskCategory || it.functionCode || '').trim()).filter(it => it);
    },
    async getDataAuth() {
      // .post(this.auth.showType == 'user' ? '/menu/getPersonalCenterMenus': '/menu/getEnterpriseCenterMenus')
      const {data} = await (this.auth.showType == 'task' ?
        Axios.post('/task/getOrgNumSetTask', this.obj2fd({
          sysType: 'SERVE'
        })) :
        Axios.post('/menu/getEnterpriseCenterMenus')
      ).catch(_ => this.error());
      // this.auth.loading = false;
      this.checkRet(data);
      this.auth.data = (data.data || []).map(it=> {
        it.childList = it.childList || [];
        if(it.taskKindCode) it.taskKindCodeExt = it.taskCategory || it.taskKindCode
        return it;
      });
    },
    authAllChange(val) {
      this.auth.form.initKeys = val ? this.authKeysAll : [];
      // this.auth.isIndeterminate = false;
      this.$refs[this.auth.showType == 'task' ? 'taskTree' : 'tree'].setCheckedKeys(this.auth.form.initKeys)
    },
    // authChange(value) {
    //   let checkedCount = value.length;
    //   this.auth.checkAll = checkedCount === this.authData.length;
    //   this.auth.isIndeterminate = checkedCount > 0 && checkedCount < this.authData.length;
    // },
    async authSave() {
      if(this.auth.loading) return;
      this.auth.loading = true;
      const {data} = await Axios
        .post('/enterpriseRoleFunction/saveRoleFunction',this.obj2fd({
            roleId: this.auth.form.roleId,
            moduleCode: this.moduleCode,
            functionCodes: [...new Set(this.$refs[this.auth.showType == 'task' ? 'taskTree': 'tree'].getCheckedKeys())],
        }))
        .catch(_ => this.error())
      this.auth.loading = false;
      this.checkRet(data);
      this.close('auth');
    },
    async setSave() {
      if(this.set.loading) return;
      this.set.loading = true;
      let id = this.set.form.roleId;
      let params = {
        roleName: this.set.form.roleName,
        order: this.set.form.order,
        ...(id ? {
          roleId: id,
        }: {
          enterpriseId: window.cpid,
        })
      };
      const {data} = await Axios
        .post('/enterpriseRoleFunction/saveRole',this.obj2fd(params))
        .catch(_ => this.error())
      this.set.loading = false;
      this.checkRet(data);
      this.getPageData();
      this.close('set');
    },
    async del(id,multi=false) {
      if(!id) {
        const ref = 'ruleTable';
        const rows = this.$refs[ref] ? this.$refs[ref].selection : [];
        if(!rows || rows.length<1) this.error('至少选择一行');
        if(!multi && rows.length>1) this.error('暂不支持多选');
        id = rows[0].roleId;
      }
      await this.confirm('是否确认删除?');
      // await this.$confirm('是否确认删除这<font color="red">' + 1 + '</font>条规则?', '提示', {
      //   type: 'warning',
      //   dangerouslyUseHTMLString: true,
      // });
      const {data} = await Axios
        .post('/enterpriseRoleFunction/deleteRole', this.obj2fd({roleId: id}))
        .catch(_ => this.error())
      this.checkRet(data);
      this.getPageData();
      this.close('set');
    },
    getPageData() {
      this.$emit('finish')
      //   const {data} = await Axios.post('applySpecialProject/selectRuleDetail',this.obj2fd({
      //     projectId: +this.projectData.projectId,
      //     page: this.page,
      //     rows: this.size,
      //   })).catch(_ => this.error())
      //   this.checkRet(data)
      //   this.data = data.rows || []
      //   this.total = +data.total  || 0;
    },
    async open(){
      this.loading = true;
      this.show = true
      this.$nextTick(_ => {
        this.loading = false;
        this.$refs['ruleTable'].doLayout();
      })
    },
  },
}
</script>

<style scoped>
.project-score-setting .custom-dialog .dialog-header {
  background-color: #f5f5f5;
  box-shadow: 0 0 2px #ccc;
  height: 36px;
}
.project-score-setting .el-container {
  background-color: white;
}
.project-score-setting .el-table__header tr
,.project-score-setting .el-table__header th{
  background-color: #fafafa;
}
.project-score-setting .el-form-item {
   margin-bottom: 0;
 }
.project-score-setting .btn-hover{
  background: #FFF;
  border-color: #409EFF;
  color: #409EFF;
}
.fix-el-icon i {
  color: #409EFF;
}
.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 20px;
    box-shadow: 0 0 4px #ccc;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
}

.dialog-header .dialog-header-left {
    color: #63A9FB;
}

.dialog-header .dialog-header-left > span {
    padding: 0 30px 0 5px;
    line-height: 29px;
}

.dialog-header .dialog-header-right i.el-icon-plus {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}
.dialog-body {
    overflow: auto;
    padding: 0;
    /* height: 440px; */
    position: relative;
  }
  .dialog-body i {
    color: inherit !important;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-input, .el-input,  .el-select, .el-select {
    width: 100%;
  }
  .text-right{
    text-align: right;
  }
  body .el-popper[x-placement^=bottom] {
    margin-top: 0;
  }
  body .el-dropdown-menu{
    margin: 0;padding: 0;border-radius: 0;min-width: 120px;
  }
  body .el-dropdown-menu .popper__arrow {
    display: none;
  }
  a.el-link {
    display: inline-block;
    cursor: pointer;
  }
  .el-input.el-input-fixWidth {
    width: 205px;
  }
  .auth-list .el-checkbox {
    display: block;line-height: 30px;
  }
</style>