<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="close"
    :show-header="false"
    theme="confirm"
    :title="L('corp.新建子账号')"
    width="600px">
    <el-main class="dialog-body" ref="body">
      <el-form
        class="form"
        ref="form"
        :model="form"
        label-width="130px"
        size="mini">
        <template v-if="step === 1">
          <el-form-item
            prop="operatorType"
            :rules="rulesRequired"
            :label="L('corp.创建方式')">
            <div class="flex" style="padding-top: 8px;">
              <el-radio v-model="form.operatorType" label="1">{{ L('corp.为公司人员创建会员') }}</el-radio>
              <el-radio v-model="form.operatorType" label="2">{{ L('corp.指定手机或邮箱关联') }}</el-radio>
            </div>
          </el-form-item>
          <el-form-item v-if="form.operatorType == '1'" prop="enterprisePersonId" :label="L('corp.联系人')">
            <el-select
              v-model="form.enterprisePersonId"
              clearable
              filterable
              default-first-option
              :placeholder="L('corp.联系人')">
              <el-option
                v-for="item in list"
                :key="item.enterprisePersonId"
                :label="item.fullName + ' ' + (item.mobile || item.email)"
                :value="item.enterprisePersonId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-else prop="username" :label="L('corp.手机或邮箱')"
            :error="isEmailOrPhone ? '' : L('corp.手机或邮箱格式错误!')">
            <el-input v-model="form.username" :placeholder="L('corp.手机或邮箱')" clearable></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <!--姓名和初始密码-->
          <el-form-item prop="fullName" :label="L('corp.姓名')">
            <el-input v-model="form.fullName" :placeholder="L('corp.姓名')" clearable></el-input>
          </el-form-item>
          <el-form-item prop="password" :label="L('corp.初始密码')">
            <el-input show-password v-model="form.password" :placeholder="L('corp.初始密码')" clearable></el-input>
          </el-form-item>
        </template>
      </el-form>
      <zzy-user-selector ref="selector"></zzy-user-selector>
    </el-main>
    <el-footer slot="footer">
      <template v-if="step === 1">
        <el-button size="small" @click="close">{{ L('取消') }}</el-button>
        <el-button size="small" type="primary" @click="finish">
          {{ form.operatorType == 1 ? L('corp.创建会员') : '下一步' }}
        </el-button>
      </template>
      <template v-else>
        <el-button size="small" @click="step = 1">{{ L('corp.返回') }}</el-button>
        <el-button size="small" type="primary" @click="finishStep2">{{ L('corp.创建会员') }}</el-button>
      </template>
    </el-footer>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')


function getPassLevel(pass) {
  if(typeof pass !== 'string' || !pass) return 0;
  var level = 0;
  // pass = String(pass).toLocaleLowerCase();
  // 小写字母，级别+1
  if (/[a-z]/.test(pass)) level++;
  if (/[A-Z]/.test(pass)) level++;
  // 数字+1
  if (/[0-9]/.test(pass)) level++;
  // 其他+1
  if (/[\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass)) level++;
  return level;
}

export default {
  name: "CorpUserAdd",
  data() {
    return {
      form: {
        operatorType: '1',
        enterprisePersonId: '',
        enterpriseId: '',
        username: '',
        fullName: '',
        password: '',
      },
      step: 1,
    }
  },
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  computed: {
    isEmailOrPhone() {
      if (!this.form.username) return true;
      return this.regEmail.test(this.form.username) || this.regPhone.test(this.form.username);
    }
  },
  mixins: [ZzMixin],
  methods: {
    async open(paras) {
      this.show = true
      this.initForm(paras);
    },
    close() {
      this.show = false
    },
    async checkZzyUserByName() {
      const {data} = await Axios.post('/zzyUser/existZzyUser', this.obj2fd({
        username: this.form.username,
      })).catch(_ => this.error())
      if (Array.isArray(data.data) && data.data.length) {
        const selectedZzyUserId = await this.$refs.selector.open(this.form.username, data.data)
        if (!selectedZzyUserId) return
        await this.handleRequest({
          operatorType: 2,
          zzyUserId: selectedZzyUserId,
          enterpriseId: this.form.enterpriseId,
          username: this.form.username,
        })
      } else {
        this.step = 2
      }
    },
    async handleStep2() {
      if (!this.form.username) return this.$message.warning({
        message: '请输入手机或邮箱',
        showClose: true
      })
      await this.checkZzyUserByName()
    },
    finishStep2() {
      this.handleRequest({
        ...this.form,
        pwdGrade: getPassLevel(this.form.password),
        password: hex_md5(this.form.password).toUpperCase()
      })
    },
    async handleRequest(payload) {
      this.loading = true
      const {data} = await Axios.post('/zzyUser/createEnterpriseAccount', this.obj2fd(payload)).catch(_ => this.error())
      this.checkRet(data)
      this.show = false
      this.step = 1
      this.$message.success({
        message: '操作成功',
        showClose: true
      })
      this.$emit('finish')
    },
    async finish() {
      if (+this.form.operatorType !== 1) return this.handleStep2();
      await this.handleRequest({
        operatorType: 1,
        enterprisePersonId: this.form.enterprisePersonId
      })
    },
  },
  components: {
    CustomDialog,
    ZzyUserSelector: importC('ZzyUserSelector.vue'),
  }
}
</script>

<style scoped>
.el-input, .el-select {
  width: 400px;
}

.form {
  padding: 20px 20px 10px 0;
}

.dialog-body {
  overflow: auto;
  padding: 0 30px 0 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.fix-el-tooltip {
  border-radius: 2px;
  line-height: 1.5em;
}
</style>