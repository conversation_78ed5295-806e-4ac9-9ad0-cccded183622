<template>
  <custom-dialog
    append2body
    :show="show"
    @before-close="show=false"
    @closed="close()"
    theme="confirm"
    :show-header="false"
    title="提醒"
    width="400px">
    <div style="font-size: 14px;color: rgba(21,27,53,1);line-height: 23px;padding: 20px;">
      <div style="color: rgba(0,0,0,0.85);font-size: 17px;" v-if="childName">
        您输入的{{ childNameExt }} {{ childName }}<br/>
        存在{{ tip.data.length }}个相关会员
      </div>
      <div style="color: rgba(0,0,0,0.85);font-size: 17px;" v-else>
        发现{{ tip.data.length > 1 ? '多个' : '' }}可指定会员信息
      </div>
      <div style="color: rgba(198,204,215,0.85);">请选择下方需要指定的会员账户</div>
      <div style="color: rgba(89,89,89,1);padding: 20px 0 12px;width: 100%;box-sizing: border-box;
            min-height: 220px;overflow-y: auto;max-height: 300px;">
        <div
          v-for="it in tip.data" :key="it.f_zzyuser_id"
          :class="'unlinkUserWrap'"
        >
          <div
            :class="['unlinkUser','cursor',it.f_zzyuser_id === tip.zzyUserId ? 'hover' : '']"
            style="position: relative;"
            @click="_ => $set(tip, 'zzyUserId', it.f_zzyuser_id)"
          >
            <div style="padding: 10px 0;">
            </div>
            <div style="position: absolute;top: 23px;right: 40px;">
              <el-tooltip v-if="it.mobileCertified || it.emailCertified" class="item" effect="dark"
                          :visible-arrow="false" content="已认证会员" placement="top">
                <img src="/eip-app-vip/img/auth.png" alt="">
              </el-tooltip>
            </div>
            <center>
              <el-avatar :size="55" :src="it.headImage">
                <img src="/eip-app-vip/img/avatar2.png"/>
              </el-avatar>
              <div class="text-over-2" style="line-height: 1.25em;margin-top: 0.5em;">{{ it.contacts }}</div>
              <div class="text-over-2" style="line-height: 1.25em;margin-top: 0.5em;">{{ it.companyName }}</div>
              <div class="text-over-2" style="line-height: 1.25em;margin-top: 0.5em;">{{ it.f_user_name }}</div>
            </center>
          </div>
        </div>
      </div>
      <el-button @click="close(true)" :disabled="!tip.zzyUserId" type="primary" class="regBtn">
        {{ tip.zzyUserId ? '选择此会员' : '请先选择会员' }}
      </el-button>
      <div style="margin: 30px 0 0;border-top: 1px solid #f3f6f9;"></div>
    </div>
  </custom-dialog>
</template>

<script>
const CustomDialog = importC('CustomDialog')

export default {
  name: "ZzyUserSelector",
  components: {
    CustomDialog,
  },
  computed: {
    childNameExt() {
      const value = this.childName || '';
      if (!value) return '会员信息'
      else if (/^1\d{10}$/.test(value)) return '手机号'
      else if (/^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i.test(value)) return '邮箱'
      else return '会员信息'
    }
  },
  data() {
    return {
      show: false,
      childName: '',
      resolve: null,
      tip: {
        data: [],
        zzyUserId: '',
      }
    }
  },
  methods: {
    async open(account, list) {
      return new Promise(resolve => {
        this.resolve = resolve
        this.show = true
        this.childName = account
        this.tip.data = list
      })
    },
    close(confirm) {
      this.show = false
      this.resolve(confirm ? this.tip.zzyUserId: null)
    },
  },
}
</script>

<style scoped>
.selector-body {
  width: 100%;
  background: white;
  box-sizing: border-box;
  padding: 10px 18px 30px;
  min-height: 200px;
}

.fix-checkbox-group {
  margin-top: 10px;
  margin-bottom: 15px;
}

.select-item {
  background-color: #FAFAFA;
  padding: 16px 25px;
  margin-bottom: 10px;
  align-items: center;
}

.regBtn {
  border-radius: 0;
  width: 100%;
}

.cursor {
  cursor: pointer;
}

.unlinkUser.hover,
.unlinkUser:hover {
  border: 1px solid #aeb8c6;
  background: #f8fafc;
}

.unlinkUserWrap {
  display: inline-block;
  margin-left: 15px;
  margin-bottom: 15px;
}

.unlinkUser center:hover {
  color: #1890FF;
}

.unlinkUser {
  border-radius: 8px;
  border: 1px solid transparent;
  font-size: 12px;
  color: #595959;
  display: inline-block;
  width: 135px;
  padding: 0 6px 20px;
  box-sizing: border-box;
  transition: all .2s;
  position: relative;
}

.text-over-2 {
  overflow: hidden;
  white-space: unset;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.hide {
  display: none;
}
</style>