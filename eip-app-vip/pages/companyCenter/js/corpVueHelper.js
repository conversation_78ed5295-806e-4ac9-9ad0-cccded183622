window.url = (new URL(window.location.href)).searchParams

function getCookie(name) {
  var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
  if (arr = document.cookie.match(reg)) {
    return decodeURI(arr[2]);
  } else {
    return null;
  }
}

Vue.prototype.$errorMsg = function (msg, title = '错误', type = 'error') {
  return this.$alert(msg, title, {type})
}

function getQueryString(key) {
  return url.get(key) || ''
}

function JSON2FormData(obj) {
  if (typeof obj !== 'object') return;
  const formData = new FormData();
  Object.keys(obj).forEach(key => formData.append(key, obj[key]));
  return formData;
}

if (window.axios) {
  window.Axios = axios.create({
    baseURL: '/eip-web-vip'
  });
}
window.ZzMixin = {
  data() {
    return {
      tableHeight: 0,
      show: false,
      loading: true,
      total: 0,
      size: 20,
      page: 1,
      data: [],
      form: {},
      searchForm: {},
      searchFormInit: '',
      formChange: false,
      formInit: '',
      rulesMoney: [{
        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输入正确额格式,可保留两位小数'
      }],
      rulesRequired: [{
        required: true, message: '此为必输项 !',
      },],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 7);
            picker.$emit('pick', start);
          }
        }, {
          text: '最近一个月', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 30);
            picker.$emit('pick', start);
          }
        }, {
          text: '最近三个月', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 90);
            picker.$emit('pick', start);
          }
        }]
      },
    }
  },
  created() {
    this.formInit = JSON.stringify(this.form);
    this.searchFormInit = JSON.stringify(this.searchForm);
    this.loading = true;
  },
  watch: {
    form: {
      handler(n, o) {
        this.formChange = JSON.stringify(this.form) === this.formInit;
      }, deep: true,
    },
    // searchForm: {
    //   handler(n,o) {
    //     console.log('dd',n,o)
    //     if(n.opType == 'score') {
    //       this.searchForm.goodsId = ''
    //     } else if(n.opType == 'goods') {
    //       this.searchForm.channelId = ''
    //       this.searchForm.operatorTypeId = ''
    //     } else {
    //       this.searchForm.goodsId = ''
    //       this.searchForm.channelId = ''
    //       this.searchForm.operatorTypeId = ''
    //     }
    //   },
    //   deep: true,
    // },
  },
  methods: {
    obj2fd(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      return this.$alert(msg, title, {type})
    },
    err(msg, throws = true) {
      this.error(msg, throws)
    },
    error(msg, throws = true) {
      msg = msg || '请求失败';
      if (this.loading) this.loading = false;
      const p = this.errorMsg(msg, '提醒', 'warning');
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message) {
      message = message || '操作成功!';
      return this.$message({
        message, type: 'success'
      });
    },
    checkRet(obj, errMsg = '', okMsg = '') {
      const {state, msg, data} = obj
      if (state !== 1) this.error(errMsg || msg)
      if (okMsg) this.ok(okMsg === true ? '' : okMsg);
    },
    initForm(args, clearValid = true) {
      this.form = JSON.parse(this.formInit);
      // 可以放 open 里 , 如右
      if (args) {
        Object.keys(args).forEach(k => {
          if (this.form.hasOwnProperty(k)) {
            // if(k == 'deductionMoney') {
            //   this.form[k] = +args[k] || 0;
            // } else if(k == 'applyFunction') {
            //   this.form[k] = args[k] ? args[k].split(',') : [];
            // } else {
            this.form[k] = args[k] === null ? '' : args[k];
            // }
          }
        })
      }
      if (clearValid) {
        this.$nextTick(_ => {
          this.$refs['form'] && this.$refs['form'].clearValidate();
        })
      }
      this.formChange = false;
    },
    sizeChange(size) {
      this.page = 1;
      this.size = size;
      this.getPageData();
    },
    pageChange(page) {
      this.page = page
      this.getPageData()
    },
    fmt(prop, row) {
      // TODO
      return row[prop] === 0 ? 0 : (row[prop] || '');
    },
    search() {
      // TODO
    },
    getPageData() {
      // TODO
    },
    confirm(msg, title, ops) {
      return this.$confirm(msg || '是否确认 ?', title || '提醒', {
        type: 'warning', confirmButtonText: '确认', cancelButtonText: '取消', ...(ops || {})
      })
    },
    fixTableHeight(w = ' - 160px') {
      if (!this.$refs['table']) return;
      this.$nextTick(_ => {
        this.tableHeight = 'calc( 100vh ' + w + ' )';
        setTimeout(_ => {
          this.$refs['table'] && this.$refs['table'].doLayout();
        }, 0);
      })
    },
    async close(force = true) {
      if (force) {
        this.show = true
      } else {
        if (this.formChange) {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            type: 'warning',
          }).catch(e => {
            throw new Error('取消关闭')
          })
          this.show = false
        }
      }
    },
  },
};