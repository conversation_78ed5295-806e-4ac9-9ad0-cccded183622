window.url = (new URL(window.location.href)).searchParams

function getCookie(name) {
  var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
  if (arr = document.cookie.match(reg)) {
    return decodeURI(arr[2]);
  } else {
    return null;
  }
}

top._comps = top._comps || {};
if (!window.importC) {
  window.importC = (path, name = '') => {
    // public
    if (path == 'CustomDialog') path = '/eip-app-vip/pages/components/CustomDialog.vue';
    //
    if (path.split('.').length < 2) {
      name = name || path
      path = '/eip-app-vip/pages/companyCenter/comps/' + path + '.vue'
    } else if (path.split('/').length < 2) {
      path = '/eip-app-vip/pages/companyCenter/comps/' + path
      name = name || parseComponentURL(path).name
    } else {
      name = name || parseComponentURL(path).name
    }
    function parseComponentURL(url) {
      var comp = url.match(/(.*?)([^/]+?)\/?(\.vue)?(\?.*|#.*|$)/);
      return {
        name: comp[2],
        url: comp[1] + comp[2] + (comp[3] === undefined ? "/index.vue" : comp[3]) + comp[4]
      }
    }
    if (path.endsWith('.vue')) path += '?v=' + ~~(Date.now() / 86400000)
    return httpVueLoader(path, name);
    // if (!top._comps[name]) top._comps[name] = httpVueLoader(path, name);
    // return top._comps[name]
  }
}
try {
  httpVueLoader.httpRequest = async function (url) {
    if (top._comps[url]) return top._comps[url];
    const resp = await fetch(url)
    const data = await resp.text()
    top._comps[url] = data.replace('export default', 'module.exports = ')
    return top._comps[url]
  }
} catch (e) {
  console.error(e);
}
Vue.prototype.$errorMsg = function (msg, title = '错误', type = 'error') {
  return this.$alert(msg, title, { type })
}

function getQueryString(key) {
  return url.get(key) || ''
}

function JSON2FormData(obj) {
  if (typeof obj !== 'object') return;
  const formData = new FormData();
  Object.keys(obj).forEach(key => formData.append(key, obj[key]));
  return formData;
}

if (window.axios) {
  window.Axios = axios.create({
    baseURL: '/eip-web-vip'
  });
}
if(!window.L) window.L = str => str;
window.ZzMixin = {
  data() {
    return {
      tableHeight: 0,
      show: false,
      loading: true,
      total: 0,
      size: 20,
      page: 1,
      data: [],
      form: {},
      searchForm: {},
      searchFormInit: '',
      formChange: false,
      formInit: '',
      rulesMoney: [{
        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输入正确额格式,可保留两位小数'
      }],
      regEmail: /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i,
      regPhone: /^1\d{10}$/,
      rulesRequired: [{
        required: true, message: '此为必输项 !',
      },],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 7);
            picker.$emit('pick', start);
          }
        }, {
          text: '最近一个月', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 30);
            picker.$emit('pick', start);
          }
        }, {
          text: '最近三个月', onClick(picker) {
            const start = new Date();
            start.setTime(start.getTime() - 86400000 * 90);
            picker.$emit('pick', start);
          }
        }]
      },
    }
  },
  created() {
    this.formInit = JSON.stringify(this.form);
    this.searchFormInit = JSON.stringify(this.searchForm);
    this.loading = true;
  },
  watch: {
    form: {
      handler(n, o) {
        this.formChange = JSON.stringify(this.form) === this.formInit;
      }, deep: true,
    },
    // searchForm: {
    //   handler(n,o) {
    //     console.log('dd',n,o)
    //     if(n.opType == 'score') {
    //       this.searchForm.goodsId = ''
    //     } else if(n.opType == 'goods') {
    //       this.searchForm.channelId = ''
    //       this.searchForm.operatorTypeId = ''
    //     } else {
    //       this.searchForm.goodsId = ''
    //       this.searchForm.channelId = ''
    //       this.searchForm.operatorTypeId = ''
    //     }
    //   },
    //   deep: true,
    // },
  },
  methods: {
    obj2fd(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '', title, type = 'error') {
      return this.$alert(L(msg || '请求失败'), L(title || '错误'), {
        type,
        confirmButtonText: L('确认'),
        // cancelButtonText: L('取消'),
      })
    },
    err(msg, title,throws = true) {
      return this.error(msg, title, throws)
    },
    error(msg, title, throws = true) {
      msg = msg || '';
      if (this.loading) this.loading = false;
      const p = this.errorMsg(msg, title, 'warning');
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message) {
      message = L(message || '操作成功');
      return this.$message({
        message, type: 'success'
      });
    },
    checkRet(obj, errMsg = '', okMsg = '') {
      const { state, msg, data } = obj
      if (state !== 1) this.error(errMsg || msg)
      if (okMsg) this.ok(okMsg === true ? '' : okMsg);
    },
    initForm(args, clearValid = true) {
      this.form = JSON.parse(this.formInit);
      // 可以放 open 里 , 如右
      if (args) {
        Object.keys(args).forEach(k => {
          if (this.form.hasOwnProperty(k)) {
            // if(k == 'deductionMoney') {
            //   this.form[k] = +args[k] || 0;
            // } else if(k == 'applyFunction') {
            //   this.form[k] = args[k] ? args[k].split(',') : [];
            // } else {
            this.form[k] = args[k] === null ? '' : args[k];
            // }
          }
        })
      }
      if (clearValid) {
        this.$nextTick(_ => {
          this.$refs['form'] && this.$refs['form'].clearValidate();
        })
      }
      this.formChange = false;
    },
    sizeChange(size) {
      this.page = 1;
      this.size = size;
      this.getPageData();
    },
    pageChange(page) {
      this.page = page
      this.getPageData()
    },
    fmt(prop, row) {
      // TODO
      return row[prop] === 0 ? 0 : (row[prop] || '');
    },
    search() {
      // TODO
    },
    getPageData() {
      // TODO
      // const {data} = await Axios.post('/enterprise/getEnterpriseAccountPage', this.obj2fd({
      //   enterpriseId: cpid,
      //   page: this.page,
      //   rows: this.size,
      // })).catch(_ => this.err())
      // this.checkRet(data);
      // this.data  = data.rows || [];
      // this.total = +data.total || 0;
    },
    confirm(msg, title, ops) {
      return this.$confirm(L(msg || '是否确认 ?'), title || L('提醒'), {
        type: 'warning',
        confirmButtonText: L('确认'),
        cancelButtonText: L('取消'),
        ...(ops || {})
      })
    },
    fixTableHeight(w = ' - 160px') {
      if (!this.$refs['table']) return;
      this.$nextTick(_ => {
        this.tableHeight = 'calc( 100vh ' + w + ' )';
        setTimeout(_ => {
          this.$refs['table'] && this.$refs['table'].doLayout();
        }, 0);
      })
    },
    async close(force = true) {
      if (force) {
        this.show = true
      } else {
        if (this.formChange) {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', L('提示'), {
            type: 'warning',
          }).catch(e => {
            throw new Error('取消关闭')
          })
          this.show = false
        }
      }
    },
  },
};