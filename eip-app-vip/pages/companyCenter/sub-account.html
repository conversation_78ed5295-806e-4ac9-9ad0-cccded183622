<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title> ... </title>
  <link rel="stylesheet" href="/eip-app-vip/css/corpReset.css"/>
  <script src="../../js/jquery-1.11.1.min.js"></script>
  <link href="../vue/element-ui/index.css" rel="stylesheet">
  <!--<script src="../vue/vue.js"></script>-->
  <script src="../vue/vue2.dev.js"></script>
  <script src="../vue/dialogDrag.js"></script>
  <script src="../vue/element-ui/index.js"></script>
  <script src="../vue/axions.js"></script>

  <script src="../../js/common/fixAjax.js?v=240105"></script>
  <script src="/eip-app-vip/js/L.js?v=250418"></script>
  <script src="/eip-app-vip/js/md5/md5.js"></script>

  <script src="/eip-app-vip/pages/vue/httpVueLoader.js"></script>
  <script src="/eip-app-vip/pages/companyCenter/js/corpVueHelperSrc.js?v=250418"></script>
  <!-- <script src="/eip-app-vip/pages/vue/httpVueLoaderEx.js"></script>
  <script src="./js/comps/CorpRoleAuth.vue" data-name="RoleAuth" type="text/x-vue"></script>
  <script src="./js/comps/ExhibitorManageCreateVip.vue" data-name="CreateVip" type="text/x-vue"></script>
  <script>
    function GetComponentPath(name) {
      return `../components/${name}.vue`
    }
    Vue.use(top == window ? httpVueLoaderEx : top.httpVueLoaderExFactory(window, document, Function))
  </script>
  <script src="/eip-app-vip/pages/companyCenter/js/corpVueHelper.js"></script> -->

  <script src="../../js/common/_vipUtilCorp.js"></script>
  <script>
    var uinfo = util.checkLogin();
    var token = uinfo.token;
		var cpid   = util.get('cpid');
    // var cpinfo = util.getCpinfo(cpid, true);
    document.title = L('menuv.子账号管理')
  </script>
  <style>
    body {
      background-color: #f0f2f5;
      padding: 30px 30px 0;
    }

    .table-noline.el-table thead tr, .table-noline.el-table thead th {
      background-color: #fafafa;
    }

    body .el-button {
      border-radius: 2px;
    }
  </style>
</head>
<body>
<div id="app" v-cloak style="background-color: white;min-height: calc(100vh - 30px);">
  <div style="display: grid;grid-template-columns: 1fr;">
    <!-- <div class="search page-header">
    </div> -->
    <section style="display: flex;flex-direction: column;font-size: 14px;height: 100%;overflow: hidden;">
      <el-container class="flex-h" :style="{flex: 1}">
        <el-header class="flex-between page-header" :style="{alignItems: 'center',height: '56px'}">
          <div style="color: #606266;font-size: 14px;">
            <!-- 剩余子账号数量: 5 -->
            <!-- <span class="key">当前检索：
            closable @close="searchClear(it.k)" -- >
            <el-tag v-for="it in searchDescs" :key="it.k" size="mini"
              plain type="info"
             >{{ it.v }}</el-tag>
            </span> -->
          </div>
          <div>
            <el-button type="primary" size="small" :disabled="loading" @click="add()">{{L('corp.新建子账号')}}
            </el-button>
            <el-button size="small" :disabled="loading" @click="auth()">{{ L('corp.通用权限设置') }}
            </el-button>
            <!--<el-badge :value="apply.total" :hidden="apply.total < 1" :max="99" :style="{marginLeft: '10px'}">
            <el-button size="small" :disabled="loading" @click="applyOpen()">
              {{ L('corp.子账号申请') }}
            </el-button></el-badge> &nbsp;-->
          </div>
        </el-header>
        <el-main class="page-main" :style="{padding: '0 24px 5px'}">
          <div>
            <!-- height="400" -->
            <!-- border -->
            <el-table
              size="mini"
              :height="tableHeight"
              class="table-noline fix-table"
              :data="data"
              ref="table"
              v-loading.fullscreen.lock="loading"
              :element-loading-text="L('数据加载中')"
              element-loading-spinner="el-icon-loading"
            >
              <el-table-column v-for="it in fields" :key="it.prop" resizable
                               show-overflow-tooltip
                               :header-align="it.headerAlign || it.align || ''"
                               :prop="it.prop"
                               :label="it.label"
                               :align="it.align"
                               :width="it.width || ''"
              >
                <template slot-scope="{row}">
                  <!--<div v-if="it.prop=='zzyUserName'" >
                     <el-link type="primary" :underline="false" @click.stop.prevent="goUserDetail(row)">{{ fmt(it.prop,row) }}</el-link> -->
                  <div v-if="it.prop=='roleId'">
                    <el-link type="primary" :underline="false" @click.stop.prevent="setRole(row)">{{ row.roleName || L('设置')}}</el-link>
                  </div>
                  <div v-else>
                    {{ fmt(it.prop, row) }}
                  </div>
                </template>
              </el-table-column>
              <!-- fixed="right" -->
              <el-table-column width="220" :label="L('操作')">
                <template slot-scope="{row}">
                  <el-link
                    :underline="false"
                    type="primary"
                    @click.stop="del(row)"
                  >{{ L('删除') }}
                  </el-link>
                  <!-- &nbsp;&nbsp;
                  <el-link
                    :underline="false"
                    type="primary"
                    @click.stop="auth(row)"
                  >子账号权限设置
                  </el-link> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
        <el-footer class="page-footer" height="40px">
          <el-pagination
            @size-change="sizeChange"
            @current-change="pageChange"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="size"
            :current-page.sync="page"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
        </el-footer>
      </el-container>
    </section>
  </div>
  <user-set-role ref="userSetRole" @finish="setRoleFinish" :list="roles"></user-set-role>
  <user-add ref="userAdd" @finish="addFinish" :list="persons"></user-add>
  <user-apply ref="userApply" :list="apply.data"></user-apply>
  <role-auth ref="roleAuth" :list="roles" @finish="authFinish"></role-auth>
</div>
<script>
  const app = new Vue({
    el: '#app',
    data() {
      return {
        cpid,
        fields: [
          {label: L('corp.关联账号'), prop: 'userName',},
          {label: L('corp.个人姓名'), prop: 'fullName',},
          {label: L('corp.关联时间'), prop: 'bindTime',},
          {label: L('corp.关联来源'), prop: 'focusSource',},
          {label: L('corp.子账号状态'), prop: 'roleId', align: 'center',},
        ],
        roles: [],
        persons: [],
        apply: {
          total: 0,
          data: [],
        }
      }
    },
    components: {
      RoleAuth: importC('CorpRoleAuth'),
      UserAdd: importC('CorpUserAdd'),
      UserApply: importC('CorpUserApply'),
      UserSetRole: importC('CorpUserSetRole'),
    },
    mixins: [ZzMixin],
    methods: {
      async setRole(item) {
        // await this.confirm('是否确认选取该会员成为本公司子账号 ?');
        this.$refs['userSetRole'].open({
          roleId: item.roleId || '',
          enterpriseAccountId: item.focuseId || '',
        })
      },
      authFinish() {
        this.getRoles();
      },
      setRoleFinish() {
        this.getPageData();
      },
      applyOpen() {
        if(this.apply.total < 1) this.err('corp.暂无申请数据');
        this.$refs['userApply'].open()
      },
      applyFinish() {
        this.getPageData();
      },
      add() {
        this.$refs['userAdd'].open({
          enterpriseId: this.cpid,
        })
      },
      addFinish() {
        this.getPageData();
      },
      auth(item) {
        this.$refs['roleAuth'].open()
      },
      async del(item) {
        await this.confirm('是否确认删除?');
        const {data} = await Axios.post('/enterprise/deleteEnterpriseSubAccount', this.obj2fd({
          focuseId: item.focuseId
        })).catch(_ => this.err());
        this.checkRet(data);
        this.getPageData();
      },
      async getPageData() {
        const {data} = await Axios.post('/enterprise/getEnterpriseAccountPage', this.obj2fd({
          enterpriseId: this.cpid,
          page: this.page,
          rows: this.size,
        })).catch(_ => this.err())
        this.checkRet(data);
        this.data  = data.rows || [];
        this.total = +data.total || 0;
      },
      async getRoles() {
        const {data} = await Axios.post('/enterpriseRoleFunction/getRoleListPage', this.obj2fd({
          enterpriseId: this.cpid,
          page: 1,
          rows: 99,
        })).catch(_ => this.err())
        this.checkRet(data);
        this.roles = data.rows || [];
      },
      async getPersons() {
        const {data} = await Axios.post('/enterprisePerson/getEnterprisePersonList', this.obj2fd({
          enterpriseId: this.cpid,
          filterMemberFlag: true,
          page: 1,
          rows: 99,
        })).catch(_ => this.err())
        this.checkRet(data);
        this.persons =  data.rows || [];
      },
      async getApplyData() {
        const {data} = await Axios.post('/subAccountApply/getPage', this.obj2fd({
          enterpriseId: this.cpid,
          page: 1,
          rows: 20,
        })).catch(_ => this.err())
        this.checkRet(data);
        // this.apply.data  =  [
        //     {
        //         "focuseId": null,
        //         "zzyUserId": 9671,
        //         "enterpriseId": "3600297437475903357",
        //         "state": null,
        //         "bindTime": null,
        //         "unbindTime": null,
        //         "isMain": null,
        //         "userName": "***********",
        //         "fullName": "蔡舒琛",
        //         "accountSource": null,
        //         "orgNum": 1002
        //     }
        //   ];
        // this.apply.total = 1;
        this.apply.data  = data.rows || [];
        this.apply.total = +data.total || 0;
      },
      // getFmtNumber(num ,df='') {
      //   num = +num || 0;
      //   return num > 0 ? ('+'+num) : (num<0 ? num : df);
      // }
    },
    async mounted() {
      // this.initForm();
      // this.search();
      this.getPageData();
      this.getApplyData();
      this.getPersons();
      this.getRoles();
      this.$nextTick(_ => {
        this.fixTableHeight(' - 140px ');
        setTimeout(() => {
          this.loading = false;
        }, 300);
      })
    },
  })
</script>
</body>
</html>