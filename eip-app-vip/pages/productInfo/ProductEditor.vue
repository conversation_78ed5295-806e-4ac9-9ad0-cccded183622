<template>
  <custom-dialog
    :show="show"
    show-header
    @before-close="close"
    :title="title"
    width="750px"
  >
    <template #header-right>
      <el-button :loading="loading" size="mini" type="primary" @click="save()">保存
        <template v-if="loading">中...</template>
      </el-button>
    </template>
    <el-container v-loading="loading">
      <el-main style="max-height: 70vh;overflow: auto">
        <el-form
          :model="form"
          class="form-prod"
          label-width="130px"
          label-position="right"
          size="small"
          :rules="rules"
          ref="form">
          <div style="display: flex;padding-bottom: 10px;flex-wrap: wrap;justify-content: space-between;">
            <el-form-item label="产品名称" prop="productName">
              <el-input
                clearable
                v-model="form.productName"
                placeholder="产品名称"></el-input>
            </el-form-item>
            <el-form-item label="产品名称(英文)" prop="productNameEn">
              <el-input
                clearable
                v-model="form.productNameEn"
                placeholder="产品名称(英文)"></el-input>
            </el-form-item>
            <el-form-item label="产品规格" prop="productSpec">
              <el-input
                clearable
                v-model="form.productSpec"
                placeholder="产品规格"></el-input>
            </el-form-item>
            <el-form-item label="行业及产品分类" prop="tradeAndProductType">
              <el-select
                clearable
                multiple
                class="fixed-arrow trade-product-select"
                popper-class="disallow-popper"
                @remove-tag="onTradeAndProductTypeChange(form.tradeAndProductTypeList)"
                @click.native.stop="openTradeAndProductTypeSelector"
                v-model="form.tradeAndProductTypeList"
                :title="selectedTradeAndProductTypeList.map(item => item.text).toString()"
                placeholder="行业分类 - 产品分类">
                <el-option
                  v-for="cr in selectedTradeAndProductTypeList"
                  :label="cr.text"
                  :value="cr.idx"
                  :key="cr.idx">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="应用领域" prop="applyAreaList">
              <el-select
                multiple
                clearable
                class="fixed-arrow"
                :collapse-tags="form.applyAreaList.length > 3"
                popper-class="disallow-popper"
                @click.native.stop="openApplyAreaSelector"
                @remove-tag="onApplyAreaListChange(form.applyAreaList)"
                v-model="form.applyAreaList"
                :title="selectedApplyAreaList.map(item => item.name).toString()"
                placeholder="应用领域">
                <el-option
                  v-for="cr in selectedApplyAreaList"
                  :label="cr.name"
                  :value="cr.id"
                  :key="cr.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="客户群体" prop="customerGroupList">
              <el-select
                multiple
                clearable
                class="fixed-arrow"
                :collapse-tags="form.customerGroupList.length > 3"
                popper-class="disallow-popper"
                @click.native.stop="openCustomerGroupSelector"
                @remove-tag="onCustomerGroupChange(form.customerGroupList)"
                v-model="form.customerGroupList"
                :title="selectedCustomerGroupList.map(item => item.name).toString()"
                placeholder="客户群体">
                <el-option
                  v-for="cr in selectedCustomerGroupList"
                  :label="cr.name"
                  :value="cr.id"
                  :key="cr.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="full" label="产品简介" prop="productProfile">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 9}"
                clearable
                v-model="form.productProfile"
                placeholder="产品简介"
              ></el-input>
            </el-form-item>
            <el-form-item class="full" label="产品简介(英文)" prop="productProfileEn">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 9}"
                clearable
                v-model="form.productProfileEn"
                placeholder="产品简介(英文)"
              ></el-input>
            </el-form-item>
            <el-form-item class="full" label="产品图片" prop="picList">
              <alex-table
                border
                max-height="600px"
                mode="none"
                class="alex-table-ant"
                :show-pagination="false"
                :span-method="picListSpanMethod"
                :fields="picListFields"
                :index="false"
                :data="form.picList">
                <template slot-scope="{row}" slot="showOrder">
                  <div v-if="row.productPicId === OPERATOR_FLAG" style="text-align:left;">
                    <el-link
                      style="text-align:left;padding-left: 10px;"
                      @click="handleProductPic('add')"
                      icon="el-icon-plus"
                      type="primary"
                      :underline="false">添加产品图片
                    </el-link>
                  </div>
                  <el-input-number
                    v-else
                    style="width: 60px;"
                    size="mini"
                    :min="0"
                    :max="65535"
                    v-model="row.showOrder"
                    :precision="0"
                    :controls="false"></el-input-number>
                </template>
                <template slot-scope="{row}" slot="isMain">
                  {{row.isMain ? '是': '否'}}
                </template>
                <template slot-scope="{row}" slot="picUrl">
                  <template v-if="row.picUrl">
                    <div class="prod-img-warp">
                      <el-image style="height: 100px" :src="row.picUrl" :preview-src-list="[row.picUrl]">
                        <img slot="error" src="/eip-app-vip/img/blank.jpg" alt="" style="width: 100%; height: 100%;">
                      </el-image>
                      <div class="delete-img-wrap">
                        <i class="el-icon-circle-close" @click.stop="handleProductPic('edit', {...row, picUrl: ''})"></i>
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <el-link
                      icon="el-icon-upload"
                      :underline="false"
                      type="primary"
                      @click="handleProductPic('startUpload', row)">点击上传</el-link>
                  </template>
                </template>
                <template slot-scope="{row}" slot="alex-operation">
                  <div style="display:inline-flex;gap: 12px;margin: auto;">
                    <el-link :underline="false" type="primary" @click="handleProductPic('togglePrimary', row)">
                      {{ row.isMain ? '设为副图片' : '设为主图片' }}
                    </el-link>
                    <el-link :underline="false" type="danger" @click="handleProductPic('del', row)">删除</el-link>
                  </div>
                </template>
              </alex-table>
              <el-upload
                style="display: none"
                ref="upload"
                action="/eip-web-vip/upload/uploadFileOss"
                :before-upload="beforeUploadNew"
                :on-error="uploadError"
                :headers="authHeaders"
                :on-success="uploadSuccess"
                :show-file-list="false"
              >
                <template #trigger>
                  <input ref="uploadTrigger" type="button" />
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </el-main>
      <item-tree-selector ref="itc"></item-tree-selector>
      <trade-prod-tree-selector :multiple="false" ref="tpts"></trade-prod-tree-selector>
    </el-container>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue";
import TradeProdTreeSelector from "../components/TradeProdTreeSelector.vue";
import ItemTreeSelector from "../components/ItemTreeSelector.vue";
import AlexTable from "../components/AlexTable.vue";

const ZzMixin = {
  data() {
    return {
      show: false,
      loading: false,
      formChange: false,
      formInit: {},
    }
  },
  created() {
    this.formInit = JSON.parse(JSON.stringify(this.form));
  },
  watch: {
    form: {
      handler(n, o) {
        if (!this.formChange) {
          this.formChange = true;
        }
      },
      deep: true,
    },
  },
  methods: {
    obj2fd(obj) {
      return this.obj2FormData(obj);
    },
    obj2FormData(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      return this.$alert(msg, title, {type})
    },
    error(msg, throws = true) {
      msg = msg || '请求失败';
      const p = this.errorMsg(msg, '提醒', 'warning');
      if (this.loading) this.loading = false;
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message = '操作成功!') {
      return this.$message({
        message,
        type: 'success',
        showClose: true
      });
    },
    checkRet(obj) {
      const {state, msg, data} = obj
      if (state !== 1) this.error(msg)
    },
    initForm() {
      this.form = JSON.parse(JSON.stringify(this.formInit));
    },
    async close(force) {
      if (force === true) this.formChange = false
      if (this.formChange) {
        try {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return
        }
      }
      this.$refs.form.clearValidate()
      this.show = false
      return true
    },
  },
};

const OPERATOR_FLAG = '#operator'
const OPERATOR_DATA = {
  productPicId: OPERATOR_FLAG,
  picUrl: '',
  isMain: '',
  showOrder: void 0,
}
export default {
  name: "ProductEditor",
  components: {AlexTable, ItemTreeSelector, TradeProdTreeSelector, CustomDialog},
  mixins: [ZzMixin, Mixins.setValidateRules(), Mixins.itemKindCode(), Mixins.clearAndInitForm('form')],
  data() {
    return {
      OPERATOR_FLAG,
      form: {
        productId: '', //产品id
        enterpriseId: '', //企业id
        productName: '', //产品名称
        productNameEn: '', //产品英文名
        productSpec: '', //产品规格
        productProfile: '', //产品简介
        productProfileEn: '', //产品简介英文
        tradeId: '', //行业id
        productTypeId: '', //产品类别id
        tradeName: '', //行业名称
        tradeNameEn: '', //行业名称 英文
        productTypeName: '', //产品类型名称
        productTypeNameEn: '', //产品类型名称 英文
        picUrl: '',
        customerGroupList: [], //客户群体
        applyAreaList: [], //产品应用领域
        picList: [{...OPERATOR_DATA}], //图片列表 picUrl isMain showOrder
        tradeAndProductTypeList: [],
      },
      rules: {
        productName: [{required: true, message: '请输入产品名称', trigger: ['blur', 'change']}],
        productNameEn: [{required: true, message: '请输入产品名称(英文)', trigger: ['blur', 'change']}],
        productSpec: [{required: true, message: '请输入产品规格', trigger: ['blur', 'change']}],
      },
      picListFields: [
        {
          label: '显示顺序',
          prop: 'showOrder',
          sortable: false,
          width: 90,
          fixedWidth: true,
          'show-overflow-tooltip': false,
        },
        {
          label: '产品图片',
          prop: 'picUrl',
          sortable: false,
          minWidth: 120,
          fixedWidth: true,
        },
        {
          label: '是否主图',
          prop: 'isMain',
          sortable: false,
          width: 90,
          fixedWidth: true,
        },
      ],
      customerGroup: [],
      selectedCustomerGroupList: [],
      selectedApplyAreaList: [],
      selectedTradeAndProductTypeList: [],
      countries: [],
      provinces: [],
      city: [],
      district: [],
      showPCD: true,
      uploadingRow: null
    }
  },
  computed: {
    title() {
      return this.form.productId ? '编辑产品信息' : '添加产品'
    },
    authHeaders() {
      return {
        Authorization: Axios.defaults.headers.common['Authorization'],
        Appid: Axios.defaults.headers.common['Appid']
      }
    }
  },
  props: {
    autoClose: {
      type: Boolean,
      default: true
    },
  },
  methods: {
    async beforeUploadNew(file) {
      if (!window.imageHelper) return
      if (!imageHelper.isImageFile(file)) {
        this.error('请选择图片文件', false)
        await Promise.reject()
      }
      const result = await imageHelper.checkCompressTip(file, this)
      if (!result) await Promise.reject()
      window.__TMP_VUE_LOADING = this.$loading({
        lock: true,
        text: '上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.3)'
      })
      if (result === 'compress') {
        file = await imageHelper.imageCompress(file)
      }
      return file
    },
    uploadError(err, file, fileList) {
      this.$message.error('上传失败!');
      if (window.__TMP_VUE_LOADING && typeof window.__TMP_VUE_LOADING.close === 'function') {
        window.__TMP_VUE_LOADING.close()
      }
    },
    async uploadSuccess(resp, file, fileList) {
      if (window.__TMP_VUE_LOADING && typeof window.__TMP_VUE_LOADING.close === 'function') {
        window.__TMP_VUE_LOADING.close()
      }
      if (resp.state !== 1) this.error(resp.msg || resp.message || '上传失败');
      this.handleProductPic('edit', {...this.uploadingRow, picUrl: resp.result.path})
      this.uploadingRow = null
    },
    open(data = {}) {
      this.show = true
      this.initForm();
      Object.keys(this.form).map(k => {
        if (data.hasOwnProperty(k)) {
          let v = (!data[k] && data[k] !== 0) ? '' : data[k];
          if (v) {
            switch (k) {
              case 'applyAreaList':
                this.selectedApplyAreaList = v.map(item => {
                  return {
                    ...item,
                    name: item.name,
                    id: item.value,
                    oldId: item.id
                  }
                })
                v = this.selectedApplyAreaList.map(it => it.value)
                break
              case 'customerGroupList':
                this.selectedCustomerGroupList = v.map(item => {
                  return {
                    ...item,
                    name: item.name,
                    id: item.value,
                    oldId: item.id
                  }
                })
                v = this.selectedCustomerGroupList.map(it => it.value)
                break
              case 'tradeId':
                this.selectedTradeAndProductTypeList = [
                  {
                    id: Math.random().toString(36).slice(2),
                    value: data.tradeId,
                    type: "trade",
                    name: data.tradeName,
                    nameEn: data.tradeNameEn,
                  },
                  {
                    id: Math.random().toString(36).slice(2),
                    value: data.productTypeId,
                    type: "product_type",
                    name: data.productTypeName,
                    nameEn: data.productTypeNameEn,
                  },
                ].map(item => {
                  if (!item.value) return
                  return {
                    ...item,
                    text: item.name,
                    idx: [item.type, item.value].join('-')
                  }
                }).filter(Boolean)
                this.form.tradeAndProductTypeList = this.selectedTradeAndProductTypeList.map(it => [it.type, it.value].join('-'))
                break
              case 'picList':
                v && v.push({...OPERATOR_DATA})
                break
            }
          }
          this.form[k] = v
        }
      });
      this.form.customerGroupList = this.form.customerGroupList || []
      setTimeout(() => {
        this.formChange = false
        this.$refs.form && this.$refs.form.clearValidate()
      }, 10)
      this.loading = false;
    },
    async save() {
      try {
        await this.validateForm('form')
      } catch (e) {
        if (!this.handleErrors(e)) console.warn(e)
        return
      }
      this.loading = true;
      const payload = {...this.form}
      payload.customerGroupList = payload.customerGroupList.map(value => ({value}))
      payload.applyAreaList = payload.applyAreaList.map(value => ({value}))
      payload.picList = payload.picList.map(item => {
        if (item.productPicId === OPERATOR_FLAG) return
        const {isMain, picUrl, showOrder} = item
        return {isMain: Boolean(isMain), picUrl, showOrder}
      }).filter(Boolean)
      const trade = this.selectedTradeAndProductTypeList .find(item => item.type === 'trade') || {}
      const productType = this.selectedTradeAndProductTypeList .find(item => item.type === 'product_type') || {}
      payload.tradeId = trade.value || trade.id || ''
      payload.productTypeId = productType.value || productType.id || ''
      delete payload.tradeAndProductTypeList
      const {data} = await Axios
        .post('/enterpriseProduct/save', payload)
        .catch(e => this.error())
      this.checkRet(data);
      this.ok()
      this.loading = false;
      this.$emit('finish')
      if (this.autoClose) {
        this.formChange = false
        this.close()
      }
    },
    async openCustomerGroupSelector() {
      try {
        this.selectedCustomerGroupList = await this.$refs.itc.open('customer_group', this.form.customerGroupList)
        this.form.customerGroupList = this.selectedCustomerGroupList.map(it => it.id)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
      }
    },
    async openApplyAreaSelector() {
      try {
        this.selectedApplyAreaList = await this.$refs.itc.open('product_apply_area', this.form.applyAreaList)
        console.log(this.selectedApplyAreaList)
        this.form.applyAreaList = this.selectedApplyAreaList.map(it => it.id)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
      }
    },
    onCustomerGroupChange(value) {
      this.selectedCustomerGroupList = this.selectedCustomerGroupList.filter(it => value.includes(it.id))
    },
    onApplyAreaListChange(value) {
      this.selectedApplyAreaList = this.selectedApplyAreaList.filter(it => value.includes(it.id))
    },
    async openTradeAndProductTypeSelector() {
      try {
        this.selectedTradeAndProductTypeList = await this.$refs.tpts.open(this.form.tradeAndProductTypeList)
        this.form.tradeAndProductTypeList = this.selectedTradeAndProductTypeList.map(it => it.idx)
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        console.warn(e)
      }
    },
    onTradeAndProductTypeChange(value) {
      this.selectedTradeAndProductTypeList = this.selectedTradeAndProductTypeList.filter(it => value.includes(it.idx))
    },
    picListSpanMethod({row, column, rowIndex, columnIndex}) {
      if (row.productPicId === OPERATOR_FLAG) {
        return columnIndex === 0 ? [1, this.picListFields.length + 1] : [0, 0]
      }
    },
    /**
     * @param {'add'|'edit'|'togglePrimary'|'startUpload'|'del'} act
     * @param {object} [row]
     * */
    handleProductPic(act, row) {
      const footer = this.form.picList.pop()
      const picList = [...this.form.picList]
      const add = () => {
        const newId = 'prod-pic-' + Math.random().toString(36).slice(2)
        picList.push({
          productPicId: newId,
          picUrl: '',
          isMain: '',
          showOrder: picList.length + 1,
        })
        this.form.picList = [...picList, footer]
      }
      const togglePrimary = () => {
        const isMain = row.isMain
        picList.forEach(item => item.isMain = item.productPicId === row.productPicId ? !isMain : false)
        this.form.picList = [...picList, footer]
      }
      const edit = () => {
        this.form.picList = [...picList.map(item => {
          if (item.productPicId === row.productPicId) {
            item = {...row}
          }
          return item
        }), footer]
      }
      const del = async () => {
        try {
          if (row.picUrl) {
            await this.$confirm('确认删除该产品图片吗？', '提示', {type: 'warning'})
          }
          this.form.picList = [...picList.filter(item => item.productPicId !== row.productPicId), footer]
        } catch (e) {
          if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
          console.warn(e)
        }
      }

      const startUpload = () => {
        this.uploadingRow = row
        this.$refs.uploadTrigger.click()
        this.form.picList = [...picList, footer]
      }

      return {add, edit, togglePrimary, startUpload, del}[act].call(this)
    }
  }
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 0;
}

.el-form-item.full {
  flex-basis: 100%;
}

.el-input, .el-input, .el-select, .el-select {
  width: 100%;
}

.text-right {
  text-align: right;
}

.el-upload {
  width: 188px;
  height: 134px;
  /* margin-top: 10px;
  padding: 10px; */
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.el-upload img {
  width: 188px;
  height: 134px;
}

.el-upload .el-upload__text {
  line-height: 49px;
  font-size: 20px;
  color: #ccc;
}

.el-upload i {
  font-size: 22px;
  /* font-size: 40px; */
  color: white;
  font-weight: bold;
  padding-top: 20px;
}

.el-input__inner, .el-textarea__inner, .el-upload {
  border: 1px solid #e3e3e3;
  border-radius: 2px;
}

.el-button {
  border-radius: 0;
}

body .el-popper[x-placement^=bottom] {
  margin-top: 0;
}

body .el-dropdown-menu {
  margin: 0;
  padding: 0;
  border-radius: 0;
  min-width: 120px;
}

body .el-dropdown-menu .popper__arrow {
  display: none;
}

.el-input.el-input-fixWidth {
  width: 205px;
}

.cursor {
  cursor: pointer;
}

.form-prod .upload-tip:after {
  content: '+';
  position: absolute;
  top: 35px;
  left: 0;
  width: 100%;
  transform: scale(3);
  text-align: center;
  font-size: 25px;
}

.form-prod .upload-tip {
  position: relative;
  overflow: hidden;
  color: #cccccc;
  font-size: 18px;
  padding-top: 90px;
  line-height: 20px;
  text-align: center;
}

.form-prod .el-form-item {
  margin-bottom: 15px;
}

.box-uploader .upload-img {
  position: relative;
}

.box-uploader .upload-img:after {
  transition: all .3s;
  content: '';
  padding-top: 50px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* border-radius: 50%; */
}

/* .box-uploader .upload-img:after { */
.box-uploader:hover .upload-img:after {
  /* content: '修 改'; */
  font-size: 30px;
  background-color: #33333355;
}

.flex-el-link {
  transition: all .3s;
}

.flex-el-link.hover,
.flex-el-link:hover {
  background-color: #33333355;
}

.flex-el-link .el-link:hover i {
  color: white !important;
}

.flex-el-link .el-link i {
  font-size: 20px;
  color: #ffffffab !important;
}

.flex-el-link .el-link {
  display: none;
}

.flex-el-link.hover .el-link,
.flex-el-link:hover .el-link {
  display: inline-block;
}

.el-image-viewer__btn i {
  color: white !important;
}

.prod-img-warp {
  position: relative;
  width: fit-content;
}

.prod-img-warp:hover .delete-img-wrap {
  top: 0;
}

.delete-img-wrap {
  position: absolute;
  top: -100%;
  transition: top 0.2s;
  width: 100%;
  padding: 5px;
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1) 70%, transparent);
  color: #fff;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.delete-img-wrap i {
  cursor: pointer;
  transition: transform 0.2s;
}

.delete-img-wrap i:hover {
  transform: scale(1.1);
}

.trade-product-select .el-tag.el-tag--mini {
  max-width: 100%;
  display: inline-flex;
}

.trade-product-select .el-select__tags-text {
  width: calc(100% - 12px);
  overflow: hidden;
  text-overflow: ellipsis;
}

.trade-product-select .el-tag__close.el-icon-close {
  top: 3px;
}
</style>