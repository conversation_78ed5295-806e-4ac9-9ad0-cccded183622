<template>
  <el-container class="product-management">
    <el-main>
      <el-header height="auto">
        <el-button
          style="width: 70px;margin-left: auto;"
          icon="el-icon-plus"
          size="small"
          type="primary"
          @click="handleProduct('add')">新增
        </el-button>
      </el-header>
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="calc(100vh - 130px)"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        :index="false"
        :operation-width="100"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="alex-operation">
          <div style="display:flex;gap:10px;margin: auto;width: fit-content">
            <el-link type="primary" :underline="false" @click.stop="handleProduct('edit', row)">修改</el-link>
            <el-link type="danger" :underline="false" @click.stop="handleProduct('del', row)">删除</el-link>
          </div>
        </template>
        <template slot-scope="{row}" slot="picUrl">
          <el-image style="height: 100px" :src="row.picUrl || '/eip-app-vip/img/blank.jpg'" :preview-src-list="[row.picUrl || '/eip-app-vip/img/blank.jpg']">
            <img slot="error" src="/eip-app-vip/img/blank.jpg" alt="" style="width: 100%; height: 100%;">
          </el-image>
        </template>
        <template slot-scope="{row}" slot="applyAreaList">
          {{(row.applyAreaList || []).map(item => item.name).join('、')}}
        </template>
        <template slot-scope="{row}" slot="customerGroupList">
          {{(row.customerGroupList || []).map(item => item.name).join('、')}}
        </template>
        <template slot-scope="{row}" slot="tradeName">
          {{[row.tradeName, row.productTypeName].filter(Boolean).join('-')}}
        </template>
      </alex-table>
    </el-main>
    <product-editor ref="editor" @finish="refresh"></product-editor>
  </el-container>
</template>

<script>
import AlexTable from "../components/AlexTable.vue";
import ProductEditor from "./ProductEditor.vue";

export default {
  name: "ProductInfo",
  components: {ProductEditor, AlexTable},
  mixins: [Mixins.loadingWrap(), Mixins.table()],
  data() {
    return {
      tableData: [],
      total: 0,
      loading: false,
      fields: [
        {
          label: '产品图片',
          prop: 'picUrl',
          sortable: false,
          minWidth: 120,
          fixedWidth: true,
        },
        {
          label: '产品名称',
          prop: 'productName',
          sortable: false,
          minWidth: 120,
          fixedWidth: true,
        },
        {
          label: '产品规则',
          prop: 'productSpec',
          sortable: false,
          minWidth: 120,
          fixedWidth: true,
        },
        {
          label: '行业及产品分类',
          prop: 'tradeName',
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
        {
          label: '应用领域',
          prop: 'applyAreaList',
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
        {
          label: '客户群体',
          prop: 'customerGroupList',
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
      ]
    }
  },
  methods: {
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        if (!queryData.enterpriseId) {
          queryData.enterpriseId = cpid
        }
        const {data} = await Axios.post('/enterpriseProduct/getEnterpriseProduct', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')

        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = +data.total
        // 确保 API 返回的数据中 row.certificateOrderList 存在且是数组
        this.tableData = data.rows.map(row => ({
          ...row,
          customerGroupList: Array.isArray(row.customerGroupList) ? row.customerGroupList : []
        }));
        await this.$nextTick();
        if (tableRef) {
          await tableRef.computedWidth();
        }
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    search() {
      this.requestTableData({
        page: 1,
        rows: 20
      })
    },
    orderStateFormatter(row) {
      const stateMap = {
        '0': '待支付',
        '1': '已支付',
        '2': '已取消',
        '3': '已退款'
      };
      return stateMap[row.orderPayState] || row.orderPayState || '未知';
    },
    // 新增：格式化商品名称的方法 (修正)
    formatCertificateNames(row) {
      if (row.certificateOrderList && row.certificateOrderList.length > 0) {
        return row.certificateOrderList.map(item => item.certificateName).join(', '); // 使用逗号加空格分隔
      }
      return '-'; // 如果没有商品或列表为空，显示占位符
    },
    /**
     * @param {'add'|'edit'|'del'} act
     * @param {object} [row]
     * */
    handleProduct(act, row) {
      const editor = this.$refs.editor
      const add = () => {
        editor.open({enterpriseId: cpid})
      }
      const edit =  () => {
        this.loadingWrap(async () => {
          const {data} = await Axios.post('/enterpriseProduct/selectById',JSON2FormData({productId: row.productId}))
          if (data.state !== 1) await Promise.reject(data.msg)
          editor.open(data.data)
        })
      }
      const del = () => {
        this.loadingWrap(async () => {
          await this.$confirm('确定删除该产品吗?', '提示', {type: 'warning'})
          const {data} = await Axios.post('/enterpriseProduct/delete',JSON2FormData({productId: row.productId}))
          if (data.state !== 1) await Promise.reject(data.msg)
          this.refresh()
        })
      }
      return {add, edit, del}[act].call(this)
    },
  },
  mounted() {
    this.refresh()
  }
}
</script>

<style scoped>
.product-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-management > .el-main > .el-header {
  padding: 0 0 20px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.product-management > .el-header .el-form-item {
  margin-bottom: 0;
}

.product-management > .el-main {
  padding: 20px 20px 0;
  margin: 20px 20px 0;
  flex-grow: 1;
  display: flex;
  overflow: hidden;
  background-color: #fff;
  flex-direction: column;
}

.product-management .alex-table-ant {
  padding: 0;
  background: #fff;
}

.product-management .el-table {
  padding: 0;
  background: #fff;
}
</style>