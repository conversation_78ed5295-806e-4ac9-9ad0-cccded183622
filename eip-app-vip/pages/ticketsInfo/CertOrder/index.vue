<template>
  <el-container class="ticket-order-management">
    <el-header height="auto">
      <el-form
        @submit.native.prevent="search"
        :model="searchForm"
        inline
        label-width="fit-content"
        size="small"
      >
        <el-form-item label="项目检索" prop="projectName">
          <el-input v-model="searchForm.projectName" placeholder="项目检索" clearable></el-input>
        </el-form-item>
      </el-form>
      <el-button style="width: 70px;margin-left: auto;" size="small" type="primary" plain @click="search">检索</el-button>
    </el-header>
    <el-main>
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="calc(100vh - 150px)"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        index-label="序号"
        index-padding
        :operation-width="100"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="alex-operation">
          <el-link type="primary" :underline="false" @click.stop="readDetails(row)">查看</el-link>
        </template>
        <!-- 商品名称列插槽 (修正) -->
        <template slot-scope="{row}" slot="certificateNames">
          {{ formatCertificateNames(row) }}
        </template>
        <!-- 订单状态列插槽 -->
        <template slot-scope="{row}" slot="orderPayState">
          {{ orderStateFormatter(row) }}
        </template>
      </alex-table>
    </el-main>
  </el-container>
</template>

<script>
import AlexTable from "../../components/AlexTable.vue";

export default {
  name: "TicketOrder",
  components: { AlexTable },
  mixins: [Mixins.table()],
  data() {
    return {
      tableData: [],
      total: 0,
      loading: false,
      fields: [
        {
          label: '订单编号',
          prop: 'orderTradeId',
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
        {
          label: '项目',
          prop: 'projectName',
          sortable: false,
          minWidth: 150,
          fixedWidth: true,
        },
        {
          label: '生成时间',
          prop: 'createTime',
          sortable: false,
          minWidth: 160,
          fixedWidth: true,
        },
        {
          label: '商品名称', // 使用插槽自定义显示
          prop: 'certificateNames', // prop 改为插槽名称
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
        {
          label: '价格',
          prop: 'totalFree',
          sortable: false,
          minWidth: 100,
          fixedWidth: true,
        },
        {
          label: '订单状态',
          prop: 'orderPayState', // 使用插槽自定义显示
          sortable: false,
          minWidth: 100,
          fixedWidth: true,
        }
      ],
      searchForm: {
        projectName: ''
      }
    }
  },
  methods: {
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        const { data } = await AxiosSponsor.post('/order/getListForVip', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')

        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = +data.total
        // 确保 API 返回的数据中 row.certificateOrderList 存在且是数组
        this.tableData = data.rows.map(row => ({
          ...row,
          certificateOrderList: Array.isArray(row.certificateOrderList) ? row.certificateOrderList : []
        }));
        await this.$nextTick();
        if (tableRef) {
          await tableRef.computedWidth();
        }
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    search() {
      this.requestTableData({
        ...this.searchForm,
        page: 1,
        rows: 20
      })
    },
    orderStateFormatter(row) {
      const stateMap = {
        '0': '待支付',
        '1': '已支付',
        '2': '已取消',
        '3': '已退款'
      };
      return stateMap[row.orderPayState] || row.orderPayState || '未知';
    },
    readDetails({exhibitCode, projectId, }) {
      const zzyuserId = uinfo.zzyUserId
      const url = `/eip-app-business/pages/cert-flows/cert-order.html?eid=${exhibitCode}&pid=${projectId}&bid=&isEn=${_lang == 'zh' ? 'true': 'false'}&uid=${zzyuserId}&ref=vip`
      window.open(url, 'cert-order')
    },
    // 新增：格式化商品名称的方法 (修正)
    formatCertificateNames(row) {
      if (row.certificateOrderList && row.certificateOrderList.length > 0) {
        return row.certificateOrderList.map(item => item.certificateName).join(', '); // 使用逗号加空格分隔
      }
      return '-'; // 如果没有商品或列表为空，显示占位符
    }
  },
  mounted() {
    this.refresh();
  }
}
</script>

<style scoped>
.ticket-order-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ticket-order-management > .el-header {
  padding: 20px;
  background: #fff;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.ticket-order-management > .el-header .el-form-item {
  margin-bottom: 0;
}

.ticket-order-management > .el-main {
  padding: 20px 20px 0;
  margin: 20px 20px 0;
  flex-grow: 1;
  display: flex;
  overflow: hidden;
  background-color: #fff;
}

.ticket-order-management .alex-table-ant {
  padding: 0;
  background: #fff;
}

.ticket-order-management .el-table {
  padding: 0;
  background: #fff;
}
</style>