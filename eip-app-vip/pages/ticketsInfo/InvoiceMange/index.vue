<template>
  <el-container class="invoice-management">
    <el-header height="auto">
      <el-form
        @submit.native.prevent="search"
        :model="searchForm"
        inline
        label-width="fit-content"
        size="small"
      >
        <el-form-item label="项目检索" prop="projectName">
          <!-- @load-memory 使用 v-model -->
          <el-input v-model="searchForm.projectName" placeholder="项目名称" clearable></el-input>
        </el-form-item>
      </el-form>
      <el-button style="width: 70px;margin-left: auto;" size="small" type="primary" plain @click="search">检索</el-button>
    </el-header>
    <el-main>
      <!-- @load-memory 使用 AlexTable, 配置与 TicketOrder 一致 -->
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="calc(100vh - 150px)"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        index-label="序号"
        index-padding
        :operation-width="100"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <!-- 操作列插槽 -->
        <template slot-scope="{row}" slot="alex-operation">
          <el-link type="primary" :underline="false" @click.stop="readDetails(row)">查看</el-link>
        </template>
        <!-- 发票状态列插槽 -->
        <template slot-scope="{row}" slot="invoiceState">
          <!-- @load-memory 使用插槽配合 formatter 函数 -->
          {{ invoiceStateFormatter(row) }}
        </template>
        <!-- 是否寄出列插槽 -->
        <template slot-scope="{row}" slot="isSend">
          <!-- @load-memory 使用插槽配合 formatter 函数 -->
          {{ isSendFormatter(row) }}
        </template>
      </alex-table>
    </el-main>
  </el-container>
</template>

<script>
// @load-memory 引入 AlexTable
import AlexTable from "../../components/AlexTable.vue";

export default {
  // @load-memory 组件命名风格
  name: "InvoiceMange",
  // @load-memory 引入组件
  components: { AlexTable },
  // @load-memory 使用 Mixin
  mixins: [Mixins.table()],
  data() {
    return {
      tableData: [],
      total: 0,
      loading: false,
      // @load-memory 定义表格列 (根据确认)
      fields: [
        { label: '项目名称', prop: 'projectName', minWidth: 150 },
        { label: '申请时间', prop: 'applyTime', minWidth: 160 },
        { label: '开票金额', prop: 'amount', minWidth: 100 },
        { label: '发票状态', prop: 'invoiceState', minWidth: 100 }, // 使用插槽
        { label: '是否寄出', prop: 'isSend', minWidth: 100 },       // 使用插槽
        { label: '申请人', prop: 'applicantName', minWidth: 120 },
        { label: '开票单位', prop: 'cltName', minWidth: 200 },
        { label: '发票号码', prop: 'invoiceCode', minWidth: 150 },
        { label: '开票内容', prop: 'billingContents', minWidth: 180 },
      ].map(field => ({ ...field, sortable: false, fixedWidth: true })), // 统一设置 sortable 和 fixedWidth
      // @load-memory 定义搜索表单模型
      searchForm: {
        projectName: ''
      }
      // requestHistory 会由 Mixin 或 requestTableData 管理
    }
  },
  methods: {
    // @load-memory 遵循 requestTableData 标准流程
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        // @load-memory 使用 Axios 实例和对应 endpoint，使用 JSON2FormData
        const { data } = await Axios.post('/invoiceController/getListForVip', JSON2FormData(queryData))
        // @load-memory 检查响应状态
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')

        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        // @load-memory total 转数字，rows 直接赋值
        this.total = +data.total
        this.tableData = data.rows
        // @load-memory 使用 $nextTick 和 computedWidth
        await this.$nextTick();
        if (tableRef) {
          await tableRef.computedWidth();
        }
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        // @load-memory 错误处理
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    // @load-memory 定义 search 方法 (与 TicketOrder 类似)
    search() {
      this.requestTableData({
        ...this.searchForm,
        page: 1,
        rows: 20
      })
    },
    // @load-memory 定义数据格式化函数
    invoiceStateFormatter(row) {
      const stateMap = {
        1: '申请中',
        2: '开票中',
        3: '已开票'
      };
      return stateMap[row.invoiceState] || row.invoiceState || '未知';
    },
    // @load-memory 定义数据格式化函数
    isSendFormatter(row) {
      return row.isSend ? '已寄出' : row.isSend === false ? '未寄出' : '-';
    },
    // @load-memory 定义操作方法 (根据确认)
    readDetails({projectId, invoiceId}) {
      const url =  `/eip-web-business/pages/boothinfo/exhibitor_member.html?p=${projectId}&token=${uinfo.token}&invoiceId=${invoiceId}&target=invoicePc&typem=`;
      window.open(url, 'invoice')
    },
    // handlePagination 和 refresh 预期由 Mixin 提供
  },
  mounted() {
    // @load-memory mounted 调用 refresh
    this.refresh();
  }
}
</script>

<style scoped>
/* @load-memory 复制 TicketOrder 的样式 */
.invoice-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.invoice-management > .el-header {
  padding: 20px;
  background: #fff;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.invoice-management > .el-header .el-form-item {
  margin-bottom: 0;
}

.invoice-management > .el-main {
  padding: 20px 20px 0; /* 保持与 TicketOrder 一致 */
  margin: 20px 20px 0; /* 保持与 TicketOrder 一致 */
  flex-grow: 1;
  display: flex;
  overflow: hidden;
  background-color: #fff; /* 保持与 TicketOrder 一致 */
}

.invoice-management .alex-table-ant {
  padding: 0;
  background: #fff;
}

.invoice-management .el-table {
  padding: 0;
  background: #fff;
}
</style>