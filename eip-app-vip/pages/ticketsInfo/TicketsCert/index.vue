<template>
  <el-container class="tickets-cert">
    <el-header height="auto">
      <el-form
        @submit.native.prevent="search"
        :model="searchForm"
        inline
        label-width="fit-content"
        size="small"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="searchForm.projectName" placeholder="项目名称" clearable></el-input>
        </el-form-item>
      </el-form>
      <el-button style="width: 70px;margin-left: auto;" size="small" type="primary" plain @click="search">检索
      </el-button>
    </el-header>
    <el-main class="content-main">
      <div class="custom-tab">
        <div
          class="custom-tab-item"
          :class="{active: currentTab===v}"
          @click="currentTab=v"
          v-for="(v, k) in ETabs"
          :key="k">
          <span>{{ v }}</span>
        </div>
      </div>
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="calc(100vh - 195px)"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        index-label="序号"
        index-padding
        :operation-width="100"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="alex-operation">
          <el-link type="primary" :underline="false" @click.stop="readDetails(row)">查看</el-link>
        </template>
        <template slot-scope="{row}" slot="state">
          {{ stateFormatter(row) }}
        </template>
      </alex-table>
    </el-main>
  </el-container>
</template>

<script>
import AlexTable from "../../components/AlexTable.vue";
const ETabs = {
  used: '已使用',
  unused: '未使用'
}

export default {
  name: "TicketsCert",
  components: {AlexTable},
  mixins: [Mixins.table()],
  data() {
    return {
      tableData: [],
      ETabs,
      total: 0,
      loading: false,
      searchForm: {
        projectName: ''
      },
      currentTab: ETabs.used
    }
  },
  methods: {
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        const {data} = await AxiosSponsor.post(this.tableURI, JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject('数据加载失败')
        // 绑定表格分页器
        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = +data.total
        this.tableData = data.rows
        await tableRef.computedWidth()
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    search() {
      this.requestTableData({
        ...this.searchForm,
        page: 1,
        rows: 20
      })
    },
    stateFormatter(row) {
      const checkText = row.isCheck ? '已核销' : '未核销'
      const convertibleText = row.isConvertible ? '已兑换' : ''
      const stateText = row.state === 0 ? '' : '已停用'
      return [checkText, convertibleText, stateText].filter(Boolean).join('-')
    },
    readDetails({exhibitCode, buyerCertificateId, projectId}) {
      const zzyUserId = uinfo.zzyUserId
      let url = `/eip-app-business/pages/cert-flows/cert-profile.html?eid=${exhibitCode}&pid=${projectId}&bid=&isEn=${_lang == 'zh' ? 'true' : 'false'}&uid=${zzyUserId}&bcid=${buyerCertificateId}&ref=vip#self`
      if (this.currentTab === '未使用') {
        url = `/eip-app-business/pages/cert-flows/cert-profile.html?eid=${exhibitCode}&pid=${projectId}&bid=&isEn=${_lang == 'zh' ? 'true' : 'false'}&uid=${zzyUserId}&ref=vip#unused`
      }
      window.open(url, 'cert-profile')
    }
  },
  mounted() {
    this.refresh()
  },
  computed: {
    fields() {
      switch (this.currentTab) {
        case ETabs.used:
          return [
            {
              label: '项目名称',
              prop: 'projectName',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            },
            {
              label: '证件名称',
              prop: 'certificateName',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            },
            {
              label: '胸卡号',
              prop: 'buyerCertificateCode',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            },
            {
              label: '状态',
              prop: 'state',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            }
          ]
        case ETabs.unused:
          return [
            {
              label: '项目名称',
              prop: 'projectName',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            },
            {
              label: '证件名称',
              prop: 'certificateName',
              sortable: false,
              minWidth: 300,
              fixedWidth: true,
            },
            {
              label: '剩余数量',
              prop: 'residueNumber',
              sortable: false,
              minWidth: 200,
              fixedWidth: true,
            }
          ]
      }
    },
    tableURI() {
      switch (this.currentTab) {
        case ETabs.used:
          return '/buyerCertificate/getListForVip'
        case ETabs.unused:
          return '/order/getUnReceiveCertificateForVip'
      }
    }
  },
  watch: {
    currentTab() {
      this.refresh()
    }
  }
}
</script>

<style scoped>
.tickets-cert {
  height: 100vh;
}

.tickets-cert > .el-header {
  padding: 20px;
  background: #fff;
  display: flex;
  align-items: center;
}

.tickets-cert > .el-header .el-form-item {
  margin-bottom: 0;
}

.tickets-cert .alex-table-ant {
  padding: 0;
}

.tickets-cert .el-table {
  padding: 0;
  background: #fff;
}

.content-main {
  padding: 12px 20px 0;
  background-color: #fff;
  margin: 20px 20px 0;
}

.custom-tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  margin-bottom: 12px;
  width: 140px;
}

.custom-tab-item {
  padding: 0 4px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: border 0.5s;
}

.custom-tab-item:hover {
  color: #2e82e4;
}

.custom-tab-item.active {
  color: #2e82e4;
  border-bottom-color: #2e82e4;
}
</style>
