<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>我的票证</title>
  <link rel="stylesheet" href="/eip-app-vip/css/corpReset.css"/>
  <script src="../../js/jquery-1.11.1.min.js"></script>
  <link href="../vue/element-ui/index.css" rel="stylesheet">
  <script src="../vue/vue.js"></script>
  <script src="../vue/dialogDrag.js"></script>
  <script src="../vue/mixins.js"></script>
  <script src="../vue/element-ui/index.js"></script>
  <script src="../vue/axions.js"></script>
  <script src="../../js/common/fixAjax.js?v=240105"></script>
  <script src="/eip-app-vip/js/L.js"></script>
  <script src="../../js/common/_vipUtilCorp.js"></script>
  <script src="./App.vue" type="text/x-vue"></script>
  <style>
    #app {
      min-height: 100vh;
      background-color: #f2f2f2;
      box-sizing: border-box;
    }
  </style>
  <script>
    var uinfo = util.checkLogin();
    var token = uinfo.token;
    var page = util.url.get('page')
    Vue.use(top.httpVueLoaderExFactory(window, document, Function))
  </script>
</head>
<body>
<div id="app" v-cloak>
  <App></App>
</div>
<script>
  new Vue().$mount('#app')
</script>
</body>
</html>