<template>
  <el-container class="person-management">
    <el-main>
      <el-header height="auto">
        <el-button
          style="margin-left: auto;"
          size="small"
          type="text"
          @click="handleHistoryOpen()">查看历史人员
        </el-button>
        <el-button
          style="width: 70px;"
          icon="el-icon-plus"
          size="small"
          type="primary"
          @click="handlePerson('add')">新增
        </el-button>
        <el-button
          size="small"
          @click="refreshEx">刷新
        </el-button>
        <el-badge v-if="apply.total" :value="apply.total" :hidden="apply.total < 1" :max="99"
                  :style="{marginLeft: '10px'}">
          <el-button size="small" :disabled="loading" @click="handleApplyOpen()">
            加入申请
          </el-button>
        </el-badge>
      </el-header>
      <alex-table
        ref="table"
        :data="tableData"
        style="flex-grow: 1"
        height="calc(100vh - 130px)"
        border
        size="small"
        class="alex-table-ant"
        mode="none"
        :fields="fields"
        :index="false"
        :operation-width="100"
        :handle-pagination="handlePagination"
        :total="total"
        :loading="loading">
        <template slot-scope="{row}" slot="alex-operation">
          <div style="display:flex;gap:10px;margin: auto;width: fit-content">
            <el-link type="primary" :underline="false" @click.stop="handlePerson('edit', row)">修改</el-link>
            <el-link type="primary" :underline="false" @click.stop="handlePerson('del', row)">删除</el-link>
          </div>
        </template>
      </alex-table>
      <person-editor ref="editor" @finish="refreshEx"></person-editor>
      <person-apply ref="apply" @handled="onApplyHandled"></person-apply>
      <person-history ref="history"></person-history>
    </el-main>
  </el-container>
</template>

<script>
import AlexTable from "../components/AlexTable.vue";
import PersonEditor from "./PersonEditor.vue";
import PersonApply from "./PersonApply.vue";
import PersonHistory from "./PersonHistory.vue";

export default {
  name: "PersonInfo",
  components: {PersonHistory, PersonApply, PersonEditor, AlexTable},
  mixins: [Mixins.loadingWrap(), Mixins.table()],
  data() {
    return {
      tableData: [],
      total: 0,
      loading: false,
      apply: {
        total: 0,
        list: []
      },
      fields: [
        {
          label: '姓名',
          prop: 'fullName',
          sortable: false,
          minWidth: 150,
          fixedWidth: true,
        },
        {
          label: '性别',
          prop: 'sex',
          sortable: false,
          minWidth: 160,
          fixedWidth: true,
        },
        {
          label: '手机',
          prop: 'mobile',
          sortable: false,
          minWidth: 200,
          fixedWidth: true,
        },
        {
          label: '邮箱',
          prop: 'email',
          sortable: false,
          minWidth: 100,
          fixedWidth: true,
        },
      ]
    }
  },
  methods: {
    async requestTableData(queryData) {
      this.loading = true
      this.requestHistory = queryData
      try {
        if (!queryData.enterpriseId) {
          queryData.enterpriseId = cpid
        }
        const {data} = await Axios.post('/enterprisePerson/getEnterprisePersonList', JSON2FormData(queryData))
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')

        const tableRef = this.$refs.table
        if (tableRef && ('rows' in queryData || 'page' in queryData)) {
          tableRef.setPager(queryData.page, queryData.rows)
        }
        this.total = +data.total
        this.tableData = data.rows
        await this.$nextTick();
        if (tableRef) {
          await tableRef.computedWidth();
        }
        return [tableRef, data.rows]
      } catch (e) {
        console.warn(e)
        typeof e === 'string' && this.$errorMsg(e)
      } finally {
        this.loading = false
      }
    },
    search() {
      this.requestTableData({
        page: 1,
        rows: 20
      })
    },
    readDetails(row) {
      console.log("查看:", row);
    },
    /**
     * @param {'add'|'edit'|'del'} act
     * @param {object} [row]
     * */
    handlePerson(act, row) {
      console.log(arguments)
      const editor = this.$refs.editor
      const add = () => {
        editor.open({enterpriseId: cpid})
      }
      const edit = () => {
        editor.open(row)
      }
      const del = () => {
        this.loadingWrap(async () => {
          await this.$confirm('确定删除该人员吗?', '提示', {type: 'warning'})
          const {data} = await Axios.post('/enterprisePerson/exitEnterprise', JSON2FormData({enterprisePersonId: row.enterprisePersonId}))
          if (data.state !== 1) await Promise.reject(data.msg)
          await this.refreshEx()
        })
      }
      return {add, edit, del}[act].call(this)
    },
    async getApplyData() {
      try {
        const {data} = await Axios.post('/subAccountApply/getPage', JSON2FormData({
          enterpriseId: cpid,
          page: 1,
          rows: 10000,
        }))
        if (data.state !== 1) await Promise.reject(data.msg || '数据加载失败')
        this.apply.total = +data.total
        this.apply.list = data.rows
      } catch (e) {
        if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
        this.$errorMsg(e || '数据加载失败')
      }
    },
    handleApplyOpen() {
      this.$refs.apply.open(this.apply.list)
    },
    handleHistoryOpen() {
      this.$refs.history.open()
    },
    async onApplyHandled() {
      await this.refreshEx()
      if (!this.apply.list.length) return this.$refs.apply.close()
      this.$refs.apply.setList(this.apply.list)
    },
    async refreshEx() {
      this.refresh()
      await this.getApplyData()
    }
  },
  mounted() {
    this.refreshEx()
  }
}
</script>

<style scoped>
.person-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.person-management > .el-main > .el-header {
  padding: 0 0 20px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.person-management > .el-header .el-form-item {
  margin-bottom: 0;
}

.person-management > .el-main {
  padding: 20px 20px 0;
  margin: 20px 20px 0;
  flex-grow: 1;
  display: flex;
  overflow: hidden;
  background-color: #fff;
  flex-direction: column;
}

.person-management .alex-table-ant {
  padding: 0;
  background: #fff;
}

.person-management .el-table {
  padding: 0;
  background: #fff;
}
</style>