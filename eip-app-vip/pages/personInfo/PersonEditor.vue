<template>
  <custom-dialog
    :show="show"
    @before-close="close"
    :title="title"
    show-header
    width="670px">
    <template #header-left>
      <span>基本信息</span>
    </template>
    <template #header-right>
      <el-button size="mini" type="primary" @click="save()">确定</el-button>
    </template>
    <el-main class="dialog-body" v-loading="loading">
      <el-form
        :model="form"
        class="formSelf"
        label-width="130px"
        label-position="right"
        size="mini"
        ref="form">
        <div style="display: grid;grid-template-columns: 310px 310px;padding: 20px 0;grid-gap: 15px 0;">
          <el-form-item
            :style="it.full && {'grid-column': 'span 2'}"
            v-for="it in cFormFields"
            :key="it.field"
            :label="it.fieldName"
            :prop="it.field"
            :rules="it.required ? [{required: true, message: '此为必输项'},...(it.rules || [])] : (it.rules || [])"
          >
            <el-select
              clearable
              filterable
              v-if="it.type=='select'"
              v-model="form[it.field]"
              :placeholder="it.fieldName" clearable
            >
              <el-option
                v-for="item in it.typeData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-date-picker
              clearable
              v-else-if="it.type=='date'"
              v-model="form[it.field]"
              align="right"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
            <el-input-number
              v-else-if="it.type==='number'"
              v-model="form[it.field]"
              :controls="false"
              style="width: 100%;"
              :min="0"
              :max="99999"></el-input-number>
            <el-input
              v-else :type="it.type || 'text'"
              filterable
              clearable
              default-first-option
              v-model.trim="form[it.field]"
              :placeholder="it.fieldName"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue";

const PATTERN_EMAIL_LOOSE = /^((?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))([,;，；、](?![_\-.][a-zA-Z0-9_\-.]*[_\-.])[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-.]+\.([a-zA-Z]{2,}))*$/i
const ZzMixin = {
  data() {
    return {
      show: false,
      loading: false,
      formChange: false,
      formInit: {},
    }
  },
  created() {
    this.formInit = JSON.parse(JSON.stringify(this.form));
  },
  watch: {
    form: {
      handler(n, o) {
        if (!this.formChange) {
          this.formChange = true;
        }
      },
      deep: true,
    },
  },
  methods: {
    obj2fd(obj) {
      return this.obj2FormData(obj);
    },
    obj2FormData(obj) {
      if (typeof obj !== 'object') return;
      const formData = new FormData();
      Object.keys(obj).forEach(key => formData.append(key, obj[key]));
      return formData;
    },
    errorMsg(msg = '数据加载失败！', title = '错误', type = 'error') {
      return this.$alert(msg, title, {type})
    },
    error(msg, throws = true) {
      msg = msg || '请求失败';
      const p = this.errorMsg(msg, '提醒', 'warning');
      if (this.loading) this.loading = false;
      if (throws) throw new Error(msg)
      return p;
    },
    ok(message = '操作成功!') {
      return this.$message({
        message,
        type: 'success',
        showClose: true
      });
    },
    checkRet(obj) {
      const {state, msg, data} = obj
      if (state !== 1) this.error(msg)
    },
    initForm() {
      this.form = JSON.parse(JSON.stringify(this.formInit));
    },
    async close(force) {
      if (force === true) this.formChange = false
      if (this.formChange) {
        try {
          await this.$confirm('检测到您的数据有改动且未保存，确定直接关闭吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch (e) {
          return
        }
      }
      this.$refs.form.clearValidate()
      this.show = false
      return true
    },
  },
};
export default {
  name: "PersonEditor",
  components: {
    CustomDialog,
  },
  mixins: [ZzMixin, Mixins.loadingWrap()],
  data() {
    const isMobile = (rule, value, callback) => {
      if (!value || /^1[3-9][0-9]\d{8}$/.test(value)) {
        callback()
      } else {
        callback(new Error('手机号码格式如:138xxxx8754'))
      }
    }
    const isEmail = (rule, value, callback) => {
      if (!value || PATTERN_EMAIL_LOOSE.test(value)) {
        callback()
      } else {
        callback(new Error('电子邮箱格式错误!'))
      }
    }
    return {
      form: {
        enterpriseId: '',
        enterprisePersonId: '',
        fullName: '',
        position: '',
        mobile: '',
        email: '',
        age: '',
        sex: '',
        wechat: '',
        qq: '',
        birthday: '',
        occupation: '',
        hobby: '',
        idCard: '',
        address: '',
      },
      formRules: [
        {
          field: 'fullName',
          need: true,
          required: true,
          fieldName: '姓名',
        },
        {
          field: 'position',
          need: true,
          required: false,
          fieldName: '职务',
        },
        {
          field: 'mobile',
          need: true,
          required: false,
          fieldName: '手机',
          rules: [{
            validator: isMobile,
            trigger: 'blur'
          }]
        },
        {
          field: 'email',
          need: true,
          required: false,
          fieldName: '邮箱',
          rules: [{
            validator: isEmail,
            trigger: 'blur'
          }]
        },
        {
          field: 'age',
          need: true,
          required: false,
          fieldName: '年龄',
          type: 'number',
        },
        {
          field: 'sex',
          need: true,
          required: false,
          fieldName: '性别',
          type: 'select',
          typeData: [
            {
              label: '男',
              value: '男',
            }, {
              label: '女',
              value: '女',
            }
          ],
        },
        {
          field: 'wechat',
          need: true,
          required: false,
          fieldName: '微信号',
        },
        {
          field: 'qq',
          need: true,
          required: false,
          fieldName: 'QQ',
        },
        {
          field: 'birthday',
          need: true,
          required: false,
          fieldName: '出生日期',
          type: 'date',
        },
        {
          field: 'occupation',
          need: true,
          required: false,
          fieldName: '工作领域',
        },
        {
          field: 'hobby',
          need: true,
          required: false,
          fieldName: '兴趣领域',
        },
        {
          field: 'idCard',
          need: true,
          required: false,
          fieldName: '身份证号',
        },
        {
          field: 'address',
          need: true,
          required: false,
          fieldName: '地址',
          full: true,
          type: 'textarea'
        },
      ],
    }
  },
  computed: {
    title() {
      return !this.form.enterprisePersonId ? '添加人员信息' : '修改人员信息'
    },
    cFormFields() {
      return this.formRules.filter(it => it.need);
    },
  },
  props: {
    autoClose: {
      type: Boolean,
      default: true
    },
  },
  watch: {
    'form.age': {
      handler(n, o) {
        if (!n) this.form.age = undefined;
      },
    }
  },
  methods: {
    open(data = {}) {
      this.show = true
      this.initForm();
      Object.keys(this.form).map(k => {
        if (data.hasOwnProperty(k)) {
          this.form[k] = (!data[k] && data[k] !== 0) ? '' : data[k];
        }
      });
      this.loading = false;
      setTimeout(() => {
        this.$refs.form.clearValidate()
        this.formChange = false
      })
    },
    save() {
      this.loadingWrap(async () => {
        await this.$refs['form'].validate();
        this.loading = true;
        this.form.age = this.form.age || '';
        const {data} = await Axios
          .post('memberInfo/saveFromEnterprise', {...this.form})
          .catch(e => this.error())
        this.checkRet(data);
        this.ok()
        this.loading = false;
        this.$emit('finish')
        if (this.autoClose) this.close(true)
      })

    },
  }
}
</script>

<style scoped>
.dialog-body {
  overflow: auto;
  padding: 0;
  /* height: 440px; */
  position: relative;
}

.dialog-body i {
  color: inherit !important;
}

.el-form-item {
  margin-bottom: 0;
}

.el-input, .el-input, .el-select, .el-select {
  width: 100%;
}

.text-right {
  text-align: right;
}

.el-upload {
  width: 188px;
  height: 134px;
  /* margin-top: 10px;
  padding: 10px; */
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.el-upload img {
  width: 188px;
  height: 134px;
}

.el-upload .el-upload__text {
  line-height: 49px;
  font-size: 20px;
  color: #ccc;
}

.el-upload i {
  font-size: 40px;
  color: #ccc;
  font-weight: bold;
  padding-top: 20px;
}

.el-input__inner, .el-textarea__inner, .el-upload {
  border: 1px solid #e3e3e3;
  border-radius: 2px;
}

.el-button {
  border-radius: 0;
}

body .el-popper[x-placement^=bottom] {
  margin-top: 0;
}

body .el-dropdown-menu {
  margin: 0;
  padding: 0;
  border-radius: 0;
  min-width: 120px;
}

body .el-dropdown-menu .popper__arrow {
  display: none;
}

.el-input.el-input-fixWidth {
  width: 205px;
}

.cursor {
  cursor: pointer;
}

.formSelf .upload-tip:after {
  content: '+';
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  transform: scale(3);
  text-align: center;
  font-size: 30px;
}

.formSelf .upload-tip {
  position: relative;
  overflow: hidden;
  color: #cccccc;
  font-size: 18px;
  padding-top: 120px;
  line-height: 20px;
  text-align: center;
}

.box-uploader .upload-img {
  position: relative;
}

.box-uploader .upload-img:after {
  transition: all .3s;
  content: '';
  padding-top: 75px;
  box-sizing: border-box;
  text-align: center;
  color: white;
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* border-radius: 50%; */
}

/* .box-uploader .upload-img:after { */
.box-uploader:hover .upload-img:after {
  content: '修 改';
  font-size: 35px;
  background-color: #33333355;
}
</style>