<template>
  <custom-dialog
    :show="show"
    @before-close="show=false"
    @closed="close"
    theme="confirm"
    :show-header="false"
    title="公司人员加入申请"
    width="414px">
    <el-main class="dialog-body" ref="body">
      <div style="padding: 20px 20px 0 20px;">
        <div v-for="it in list" :key="it.applyId" class="flex-between flex-center-w" style="padding-bottom: 20px;">
          <div class="flex-center-w">
            <el-avatar icon="el-icon-user-solid" :size="50" :src="it.headImage"></el-avatar>
            <div style="flex: 1;padding-left: 15px;">
              <div class="text-over-1">{{ it.fullName || it.userName || '' }} {{ it.position || '' }}</div>
              <div class="text-over-1">{{ it.mobile || '' }}</div>
              <div class="text-over-1">{{ it.email || '' }}</div>
            </div>
          </div>
          <div>
            <el-button type="primary" round @click="handleItem(it,1)" size="small">添加</el-button>
            <el-button round size="small" @click="handleItem(it,2)" :style="{marginLeft: '2px'}">拒绝</el-button>
          </div>
        </div>
      </div>
    </el-main>
  </custom-dialog>
</template>

<script>
import CustomDialog from "../components/CustomDialog.vue";

export default {
  name: "PersonApply",
  mixins: [Mixins.loadingWrap()],
  emits: ['handled'],
  components: {
    CustomDialog,
  },
  data() {
    return {
      show: false,
      list: []
    }
  },
  methods: {
    async open(list) {
      this.show = true
      this.list = list
    },
    close() {
      this.show = false
    },
    async handleItem(item, applyState) {
      this.loadingWrap(async () => {
        const {data} = await Axios.post('/subAccountApply/handleSubAccountApply', JSON2FormData({
          applyId: item.applyId,
          applyState
        }))
        if (data.state !== 1) await Promise.reject(data.msg || '操作失败')
        this.$emit('handled')
      })
    },
    setList(list) {
      this.list = list
    }

  },
}
</script>

<style scoped>
.dialog-body {
  overflow: auto;
  padding: 0;
  position: relative;
  max-height: 65vh;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  box-shadow: 0 0 4px #ccc;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
</style>