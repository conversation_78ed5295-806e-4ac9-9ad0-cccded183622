<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title></title>
  <link rel="stylesheet" href="/eip-app-vip/css/corpReset.css"/>
  <script src="../../js/jquery-1.11.1.min.js"></script>
  <link href="../vue/element-ui/index.css" rel="stylesheet">
  <script src="../vue/vue.js"></script>
  <!--<script src="../vue/vue2.dev.js"></script>-->
  <script src="../vue/dialogDrag.js"></script>
  <script src="../vue/mixins.js"></script>
  <script src="../vue/element-ui/index.js"></script>
  <script src="../vue/axions.js"></script>
  <script src="../../js/common/fixAjax.js?v=240105"></script>
  <script src="/eip-app-vip/js/L.js"></script>
  <script src="../../js/common/_vipUtilCorp.js"></script>
  <script src="../../js/compressor.min.js"></script>
  <script src="../../js/image-helper.js?v=250206"></script>
  <script src="./App.vue" type="text/x-vue"></script>
  <style>
    #app {
      background-color: #f2f2f2;
      box-sizing: border-box;
      padding: 1px;
    }
    .el-message-box__wrapper input {
      transition: all 0.3s;
    }
    .el-message-box__wrapper.error input:not(:checked) {
      transform: scale(1.02);
      box-shadow: 0 0 3px red;
    }
  </style>
  <script>
    var uinfo = util.checkLogin();
    var token = uinfo.token;
    var cpid = util.get('cpid')
    Vue.use(top.httpVueLoaderExFactory(window, document, Function))
  </script>
</head>
<body>
<div id="app" v-cloak>
  <App></App>
</div>
<script>
  new Vue().$mount('#app')
</script>
</body>
</html>