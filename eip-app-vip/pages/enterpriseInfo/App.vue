<template>
  <div class="container">
    <company-profile
      :field-rules="fieldConfig"
      :company="enterprise"
      @delete="handleDeleteEnterprise"
      @apply-certification="$refs.certAD && $refs.certAD.open(enterprise)"
      @edit="handleEditEnterprise"></company-profile>
    <company-editor @finish="getEnterprise" ref="editor"></company-editor>
    <certification-apply-dialog ref="certAD" @finish="getEnterprise"></certification-apply-dialog>
  </div>
</template>

<script>
import CompanyProfile from "./CompanyProfile.vue";
import CompanyEditor from "../components/CompanyEditor.vue";
import CertificationApplyDialog from "../components/CertificationApplyDialog.vue";

export default {
  components: {CertificationApplyDialog, CompanyEditor, CompanyProfile},
  mixins: [Mixins.loadingWrap()],
  data() {
    return {
      enterprise: {},
      fieldConfig: []
    }
  },
  mounted() {
    this.getEnterprise()
    this.getFieldSet()
  },
  methods: {
    async getEnterprise() {
      this.loadingWrap(async () => {
        if (!cpid) await Promise.reject('参数异常，请刷新重试')
        const {data} = await Axios.post('/enterprise/selectById', JSON2FormData({enterpriseId: cpid}))
        if (data.state !== 1) await Promise.reject(data.msg)
        this.enterprise = Object.freeze(data.data)
      })
    },
    async getFieldSet() {
      this.loadingWrap(async () => {
        const {data} = await Axios.post('/exhibitSetting/selectZzyUserInfoShowField')
        if (data.state !== 1) await Promise.reject(data.msg)
        this.fieldConfig = Object.freeze((data.data || [])
          .filter(item => item.taskKindCode === 'fo_zzyuser_company_info_field_set')
          .map(item => {
            return {
              ...item,
              need: +item.value === 1,
              required: !!item.isNull,
              field: item.param,
            }
          }))
      })
    },
    handleDeleteEnterprise(company) {
      const rdn = 'tmp__' + Math.random().toString(36).slice(2)
      this.loadingWrap(async () => {
        if (!company.enterpriseId) await Promise.reject('数据异常')
        await this.$confirm(
          `
            <p style="display: flex;flex-direction: column;font-size: 15px;">
              <span style="font-size: 16px;color: #666;margin-bottom: 10px;">公司下所有数据将被删除，确认要删除公司吗?</span>
              <label style="display: inline-flex;align-items:center;gap:5px"><input type="checkbox" name="${rdn}">确认此操作不可撤销</label>
              <label style="display: inline-flex;align-items:center;gap:5px"><input type="checkbox" name="${rdn}">确认删除公司信息</label>
              <label style="display: inline-flex;align-items:center;gap:5px"><input type="checkbox" name="${rdn}">确认删除公司人员信息</label>
              <label style="display: inline-flex;align-items:center;gap:5px"><input type="checkbox" name="${rdn}">确认删除公司产品信息</label>
            </p>
          `,
          '提醒',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true,
            beforeClose(action, instance, done) {
              if (action !== 'confirm') return done()
              if (document.querySelectorAll(`[name="${rdn}"]:checked`).length === 4) done()
              instance.$el.classList.add('error')
              setTimeout(() => {
                instance.$el.classList.remove('error')
              }, 300)
            }
          }
        )
        const {data} = await Axios.post('/enterprise/delete', JSON2FormData({enterpriseId: company.enterpriseId}))
        if (data.state !== 1) await Promise.reject(data.msg)
        this.$message.success({
          message: '删除成功',
          showClose: true,
        })
        top.app && top.app.goLogin(true)
      })
    },
    handleEditEnterprise() {
      this.loadingWrap(async () => {
        await this.getEnterprise()
        this.$refs.editor.open({
          data: this.enterprise,
          fields: this.fieldConfig
        })
      })
    },
  }
}
</script>
<style scoped>

.container {
  margin: 20px;
}
</style>