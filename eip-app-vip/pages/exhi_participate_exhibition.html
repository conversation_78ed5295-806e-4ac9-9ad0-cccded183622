<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title> ... </title>
	<link href="./vue/element-ui/index.css" rel="stylesheet" type="text/css"/>
	<script src="../js/common/variable.js"></script>
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="../js/dayjs.min.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<script src="./vue/element-ui/index.js"></script>
	<!-- <script src="./vue/wangEditor.min.js"></script> -->
	<link rel="stylesheet" href="../css/member-theme.css?v=20211103">
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="/eip-app-vip/js/L.js"></script>
	<script src="../js/common/_vipUtil.js?v=231113"></script>
	<script>
		var uinfo = util.checkLogin();
		var token = uinfo.token;
		document.title = L('menuv.我参与的展会');
	</script>
	<style type="text/css">
		body, html, #app {
			margin: 0;
			padding: 0;
			height: 100%;
			width: 100%;
		}
		.text-over-1{
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 100%;
		}
		.text-over-2 {
			overflow : hidden;
			text-overflow: ellipsis;
			display: -webkit-box !important;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}

		.ex_table_text_title {
			font-size: 16px;
			color: #333333;
			display: inline-block;
			line-height: 25px;
			width: 253px;
			height: 50px;
		}
		.ex_table_text_sub {
			/* display: inline-flex; */
			height: 20px;
			line-height: 20px;
			font-size: 14px;
			color: #000;
			padding-left: 20px;
			position: relative;
			width: 209px;
		}
		.ex_table_text_sub:nth-of-type(2)::before {
			position: absolute;
			top: 1px;
			left: 0;
			display: inline-block;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			content: url("../img/newpage/future_expo_time.png");
		}
		.ex_table_text_sub:last-of-type::before {
			position: absolute;
			top: 1px;
			left: 0;
			display: inline-block;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			content: url("../img/newpage/future_expo_addtr.png");
		}

		.ex-table-img {
			width: 285px;
			height: 170px;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: end;
		}

	  /*  .ex-table-img img {
			display: block;
			width: 100%;
			height: 100%;
			overflow: hidden;

		}*/

		.page_header {
			height: 106px;
			display: flex;
			flex-direction: column;
			align-items: start;
			justify-content: space-between;
			padding: 0 30px;
		}
		.page_title {
			align-self: center;
			font-size: 18px;
			color: #333333;
			margin-top: 18px;
		}

		.app_main, .app_foot {
			background-color: #f5f5f5;
		}
		.app_main {
			padding: 0;
		}
		.app_main_content {
			display: grid;
			grid-template-columns: repeat(auto-fill, 285px);
			grid-row-gap: 30px;
			grid-column-gap: 32px;
		}
		.app_main_content_item {
			background-color: #fff;
			border: solid 1px #eaeaea;
			cursor: pointer;
			height: 304px;
		}

		.app_main_content_item:hover {
			box-shadow: 3px 3px 9px 3px #ddd;
			/* outline: 1px solid #666; */
			/* cursor: pointer;*/
		}
		.app_main_content_item_info {
			font-size: 14px;
			height: 132px;
			color: #666;
			text-overflow: ellipsis;
			white-space: pre-line;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			/* align-items: start; */
			padding: 14px;
			box-sizing: border-box;
			width: 285px;
			text-align: left;
		}
		.app_main_content_item_info .tag {
			display: inline-block;
			padding: 0 5px;
			height: 20px;
			color: #fff;
			font-size: 12px;
			text-align: center;
			line-height: 20px;
			margin-left: 2px;
			border-radius: 0px 4px 0px 4px;
		}

		.tag_2 {
			background-color: #29a599; /*展商*/
		}

		.tag_4 {
			background-color: #ec6d36; /*观众*/
		}

		.tag_1 {
			background-color: #5888e0; /*主办方*/
		}

		.rang .el-form-item__label {
			padding-right: 0px;
		}
		.errorImg {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.errorImg .el-icon-picture-outline {
			font-size: 48px;
		}
		[v-cloak] {
			display: none;
		}
		.app_main_content_item {
			position: relative;
			height: 304px;
		}
		.app_main_content_item .item-hover{
			position: absolute;left: 0;top: 0;right: 0;bottom: 0;
			background-color: rgba(0,0,0,0.45);
			display: none;
		}
		.app_main_content_item.roles {
			outline: none !important;
		}
		.app_main_content_item.roles:hover .item-hover{
			display: block;
		}
		.tags>div.hover {
			color: white;
			background-color: var(--color-main);
			border-radius: 2px;
		}
		.tags {
			background-color: #f2f2f2;
			font-size: 10px;
			border-radius: 3px;
			line-height: 24px;
			margin-bottom: 5px;
		}
		.tags>div {
			color: #999;
			font-size: 12px;
			padding: 0 20px;
			margin: 0 1px;
			cursor: pointer;
		}
		.js-role+.js-role {
			margin-left: 10px;
		}
		.js-role {
			width: 80px;height: 80px;margin-bottom: 10px;cursor: pointer;
		}
	</style>
</head>

<body>
<el-container id="app" v-cloak>
	<el-header height="auto" class="page_header">
		<!-- <h1 class="page_title">{{ L('menuv.我参与的展会') }}</h1> -->
		<div style="padding: 20px 0 0;">
			<div style="display: flex;color: #606266;padding-bottom: 3px;">
				<div style="width: 68px;">举办时间</div>
				<div class="tags" style="display: flex;">
					<div :class="form.recentExhibitState == '' ? 'hover' : ''" @click="changeState('')">全部</div>
					|<div :class="form.recentExhibitState == '1' ? 'hover' : ''" @click="changeState('1')">近期开展的展会</div>
					<template v-for="i in years" :key="i">
						|<div :class="form.recentExhibitState == i ? 'hover' : ''" @click="changeState(i)">{{ i }}</div>
					</template>
				</div>
			</div>
			<el-form :inline="true" :model="form" size="mini">
				<el-form-item label="展会搜索">
					<el-select v-model="form.projRoleId" clearable placeholder="参与类别">
						<!-- <el-option label="所有" value=""></el-option> -->
						<!-- <el-option label="主办方" value="1"></el-option> -->
						<el-option label="展商" value="2"></el-option>
						<!-- <el-option label="服务商" value="3"></el-option> -->
						<el-option label="观众" value="4"></el-option>
					</el-select>
				</el-form-item> &nbsp;
				<el-form-item label="">
					<el-input v-model="form.projectName" placeholder="搜索展会名称" clearable @keyup.enter.native="search()" ></el-input>
				</el-form-item> &nbsp;
				<!-- value-format="yyyy-MM-dd" -->
				<el-form-item label="">
					<el-date-picker
							v-model="form.startTime"
							type="date"
							value-format="yyyyMMdd"
							placeholder="开始日期">
					</el-date-picker>
				</el-form-item>
				<el-form-item label="">
					<el-date-picker
							v-model="form.endTime"
							type="date"
							value-format="yyyyMMdd"
							placeholder="结束日期">
					</el-date-picker>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="search()">检索</el-button>
				</el-form-item>
			</el-form>
		</div>
	</el-header>
	<el-main class="app_main">
		<div class="app_main_content"
		style="overflow:auto;height: calc(100vh - 210px);padding: 20px 97px;"
		infinite-scroll-distance="65"
		v-infinite-scroll="load">
		<!-- v-loading="loading"> -->
		<!-- infinite-scroll-disabled="disabled" -->
			<div
				v-for="(item,i) in showData" :key="item.f_create_org_id"
				:class="'app_main_content_item ' + (item.roles.length > 1 ? 'roles' : '')"
				>
				<div @click="jump(item)" style="cursor: pointer;position: relative;">
					<div v-show="item.participateFlag" class="theme-bg-color-main" style="color: white;position: absolute;top: 0;left: 0;padding: 4px 10px;font-size: 12px;z-index: 1;">已参与</div>
					<div class="ex-table-img">
						<el-image :src='item.f_project_logo_fix' fit="contain" style="width:100%; height: 100%">
							<div slot="error" class="image-slot errorImg" >
								<i class="el-icon-picture-outline"></i>
							</div>
						</el-image>
					</div>
					<div class="app_main_content_item_info"> <!--1表示主办方 2表示展商 3表示服务商 4表示观众-->
						<span class="ex_table_text_title text-over-2" :title="item.f_project_name || ''">{{item.f_project_name || ''}}
	<!--						<span class="tag" :class="[{tag_1: item.f_proj_role_id ==1}, {tag_2: item.f_proj_role_id ==2}, {tag_4: item.f_proj_role_id ==4}]">{{item.f_proj_role_id | types}}</span>-->
						</span>
						<span class="ex_table_text_sub" v-if="item.f_start_date || item.f_end_date">{{item.f_start_date | timeFormat}} - {{item.f_end_date | timeFormat}}</span>
						<span class="ex_table_text_sub" v-else></span>
						<span class="ex_table_text_sub text-over-1" :title="item.f_exhibit_place || ''">{{item.f_exhibit_place || ''}}</span>
					</div>
				</div>
				<div class="item-hover" style="color: white;z-index: 2;">
					<center style="font-size: 20px;padding: 60px 0 40px;"> {{ L('作为以下身份参展') }} </center>
					<center style="">
						<img class="js-role"
							@click="jump(item, 4)" v-if="item.roles.includes('4')"
							src="/eip-app-vip/img/roles/role11-4.jpg" alt="观众">
						<img class="js-role"
							@click="jump(item, 2)" v-if="item.roles.includes('2')"
							src="/eip-app-vip/img/roles/role21-2.jpg" alt="展商">
						<!-- <img class="js-role"
							@click="jump(item, 0)" v-if="item.roles.includes('0')"
							src="/eip-app-vip/img/roles/role24-0.jpg" alt="其他"> -->
					</center>
				</div>
			</div>
		</div>
	</el-main>
	<el-footer class="app_foot"></el-footer>
</el-container>
<script src="../js/dayjs.min.js"></script>
<script>
	var Axios = axios.create({
		baseURL: variableSponsor
	});
	new Vue({
		el: "#app",
		data() {
			return {
				form: {
					projectName: '',
					startTime: dayjs().subtract(1, 'month').format('YYYYMMDD'),
					endTime: '',
					projRoleId: '',
					recentExhibitState: 1,
					// endExhibitTimeFlag: false,
				},
				page: 0,
				size: 20,
				total: 99,
				loading: false,
				years: [],
				showData: [],
				user_name: uinfo.usrname
			}
		},
		methods: {
			load(){
				this.page ++
				this.getData()
			},
			disabled () {
				// return false;// this.loading || this.page*this.size < this.total
				return this.loading || this.page*this.size < this.total
			},
			search() {
				this.loadErr = false
				this.page = 1
				this.getData()
			},
			jump({f_url, f_server_ip, f_server_port, f_proj_role_id, f_client_id, f_project_id, f_project_name, f_exhibit_code, f_proj_id,f_create_org_id,f_zzyuser_id},typem = '') {
				window.open(`${window.origin}/eip-web-business/pages/boothinfo/exhibitor_member.html?token=${util.buildAuth(token, f_project_id)}&p=${f_project_id}&typem=${typem}`);
				// let url = window.origin +'/'; // `//${f_server_ip}:${f_server_port}/`
				// if (f_proj_role_id == 1) { // 主办方
				// 	url += `eip-web-sponsor/backstage/indexA?m=${this.user_name}`
				// 	window.open(url)
				// } else if(f_proj_role_id == 2) { // 展商
				// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?token=${util.buildAuth(token, f_project_id)}&typem=2`;
				// 	window.open(url)
				// } else if(f_proj_role_id == 4) {
				// 	url += `eip-web-business/pages/boothinfo/exhibitor_member.html?token=${util.buildAuth(token, f_project_id)}&typem=4`;
				// 	window.open(url)
				// } else if(!f_proj_role_id){ // 未关联项目
				// 	// 此时没有zzyuserId 取登录用户的
				// 	f_zzyuser_id = uinfo.zzyUserId || ''
				// 	// 会员关联项目
				// 	const params = new FormData()
				// 	params.append('zzyuserId', f_zzyuser_id)
				// 	params.append('orgNum', f_create_org_id || '')//localStorage.getItem('orgNum')
				// 	params.append('projId', f_proj_id)
				// 	try{
				// 		Axios.post(API + "/tradeProject/queryRelationFocus", params).then(res => {
				// 			let {data: {data, state, msg}} = res;
				// 			if (state == 1) {
				// 				window.open(`${url}eip-web-business/pages/boothinfo/exhibitor_member.html?token=${util.buildAuth(token, f_project_id)}&typem=4`)
				// 			}else{
				// 				throw new Error(msg)
				// 			}
				// 		}).catch(e => {
				// 			this.$message.error('关联项目失败 : '+ e.message)
				// 		})
				// 	}catch(e){
				// 		this.$message.error('关联项目失败 : '+ e.message)
				// 		console.dir(e)
				// 	}
				// }else{
				// 	window.open(url)
				// }
			},
			async getYears() {
				let {data} = await Axios.post( API + "/tradeProject/getExhibitionYear").catch(e => console.error(e))
				this.years = data.data || []
			},
			changeState(year) {
				this.form.recentExhibitState = year;
				this.search();
			},
			fixLogoImg(src, it) {
				src = src || '';
				if(src.startsWith('http') || src.startsWith('//')) {
					src = src.replace('http:', '')
				} else {
					let port = it.f_server_port == '80' ? '' : (':' +it.f_server_port)
					src = '//'+it.f_server_ip + port + '/image/' + src;
				}
				return src;
			},
			async getData() {
				this.loading = true
				let fs = JSON.parse(JSON.stringify(this.form))
				fs.endExhibitTimeFlag = false
				if(this.years.length>0 && fs.recentExhibitState == this.years[this.years.length -1]) {
					fs.endExhibitTimeFlag = true
				}
				if(fs.recentExhibitState) fs.recentExhibitState =  parseInt(fs.recentExhibitState)
				let params = new FormData()
				let values = Object.entries(fs)
				for (let [k, v] of values) {
					if (v) params.set(k, v)
				}
				params.set('page', this.page)
				params.set('rows', this.size)
				try {
					let {data} = await Axios.post( API + "/tradeProject/getExhibitionList", params);
					(data.rows || []).map(it => {
						it.roles = (it.projRoleIds || '').split(',').filter(it=> it == '2' || it == '4');
						return it;
					})
					let showData = data.rows || []
					showData.map(it=> {
						it.f_project_logo_fix = this.fixLogoImg(it.f_project_logo, it)
						return it;
					})
					if(this.page < 2) this.showData = showData
					else this.showData = [...this.showData, ...showData]
					this.total 	  = +data.total || 0
				} catch (e) {
					console.log(e)
					this.loadErr = true;
				} finally{
					this.loading = false
				}
			}
		},
		mounted() {
			this.getYears();
			// this.search()
		},
		filters: {
			types: function (v) {
				let n = Number(v)
				switch (n) {
					case 1:
						return '主办方';
					case 2:
						return '展商';
					case 4:
						return  '观众'
					default:
						return  ''

				}
			},
			timeFormat: function(date) {
				const ymd = {
					year: "numeric",
					month: "2-digit",
					day: "2-digit"
				}
				return  date ? new Date(date).toLocaleDateString(ymd).replace(/\//g, '.') : ''
			}
		},
	})
</script>
</body>
</html>