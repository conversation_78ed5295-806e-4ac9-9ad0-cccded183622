<template>
  <div class="personal-card-wrapper">
    <div class="personal-card-container">
      <div class="personal-card-header">
        <h3 class="personal-card-title">个人名片</h3>
        <el-button type="primary" size="small" @click="handleAddCard">添加新名片</el-button>
      </div>
      <div class="card-scroll-container">
        <div class="card-scroll-wrapper">
          <div v-if="loading" class="loading-state">
            <i class="el-icon-loading"></i> 加载中...
          </div>
          <div v-else-if="!cardList || cardList.length === 0" class="empty-state">
            暂无名片数据
          </div>
          <div v-else class="card-list-items">
            <div class="card-item" v-for="card in cardList" :key="card.enterprisePersonId">
              <div class="card-item-inner">
                <el-image
                  :src="card.headImage"
                  fit="cover"
                  class="card-avatar"
                >
                  <div slot="error" class="image-slot">
                    <span class="fallback-text">{{ getFallbackAvatarText(card.fullName) }}</span>
                  </div>
                </el-image>
                <div class="card-info-main">
                  <div class="card-name-row">
                    <span class="card-full-name">{{ card.fullName }}</span>
                    <span v-if="card.mainCard" class="card-badge main-card-badge">主名片</span>
                  </div>
                  <div class="card-position">{{ card.position }}</div>
                </div>
                <el-button plain size="mini" class="card-edit-button" @click="handleEdit(card)">编辑资料</el-button>
              </div>
              <div class="card-company-row">
                <span class="card-company-name">{{ card.companyName || '无企业信息' }}</span>
                <span
                  :class="['card-badge', card.certificationState === 1 ? 'certified-badge' : 'uncertified-badge']">
                    {{ card.certificationState === 1 ? '已认证' : '未认证' }}
                  </span>
              </div>
              <div class="card-contact-details">
                <p v-if="card.mobile">
                  <span class="contact-dot phone-dot"></span>
                  {{ card.mobile }}
                </p>
                <p v-if="card.email">
                  <span class="contact-dot email-dot"></span>
                  {{ card.email }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <enterprise-person-editor @apply-handled="onApplyHandled" ref="epEditor"></enterprise-person-editor>
  </div>
</template>

<script>
import EnterprisePersonEditor from "../components/EnterprisePersonEditor.vue";

export default {
  name: 'PersonalCardSection',
  components: {EnterprisePersonEditor},
  data() {
    return {
      loading: false,
      cardList: []
    }
  },
  mounted() {
    this.getCardList()
  },
  methods: {
    async getCardList() {
      try {
        this.loading = true
        const {data} = await Axios.post('/enterprisePerson/getCard')
        if (data.state !== 1) {
          this.$errorMsg('名片数据加载失败')
          return
        }
        this.cardList = data.rows
      } catch (e) {
        console.warn('获取名片数据失败:', e)
      } finally {
        this.loading = false
      }
    },
    async handleEdit({enterpriseId, enterprisePersonId}) {
      await this.$refs.epEditor.open({
        enterpriseId,
        enterprisePersonId
      })
      await this.getCardList()
    },
    async handleAddCard() {
      await this.$refs.epEditor.open({
        enterpriseId: '',
        enterprisePersonId: '',
      })
      await this.getCardList()
    },
    getFallbackAvatarText(fullName) {
      return fullName ? String(fullName).charAt(0) : '?'
    },
    onApplyHandled() {
      top.openPage('fu_my_enterprise')
    }
  }
}
</script>

<style scoped>
/* 增加一个外层容器，用于控制整体的margin */
.personal-card-wrapper {
  margin: 15px;
}

.personal-card-container {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.personal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5; /* 添加分割线 */
}

.personal-card-title {
  font-size: 18px;
  color: #303133;
  font-weight: bold;
  margin: 0;
}

.card-scroll-wrapper {
  overflow-x: auto;
  white-space: nowrap;
}

.card-scroll-container {
  padding: 20px;
  position: relative;
}

/*渐变阴影，透明到白色*/
.card-scroll-container::after,
.card-scroll-container::before {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: 40px;
  z-index: 1;
  pointer-events: none;
}

.card-scroll-container::before {
  left: 0;
  background: linear-gradient(to right, white, rgba(255, 255, 255, 0));
}

.card-scroll-container::after {
  right: 0;
  background: linear-gradient(to left, white, rgba(255, 255, 255, 0));
}

.card-list-items {
  display: inline-flex; /* 使卡片横向排列 */
  gap: 20px; /* 卡片之间的间距 */
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
}

.card-item {
  width: 400px; /* 根据设计稿调整宽度 */
  border: 1px solid #ebeef5;
  border-radius: 12px;
  padding: 20px;
  box-sizing: border-box;
  display: inline-block; /* 配合 white-space: nowrap; */
  vertical-align: top; /* 防止底部对齐 */
  background-color: #fff;
  flex-shrink: 0; /* 防止卡片在小屏幕下缩小 */
}

.card-item:hover {
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.05);
}

.card-item:hover .card-edit-button {
  display: block;
}

.card-item-inner {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  position: relative; /* 用于定位编辑按钮 */
}

.card-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* 确保圆角 */
}

/* el-image fallback slot style */
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa; /* 默认背景色，可以根据需要调整 */
  color: #909399; /* 默认文字颜色 */
  font-size: 20px;
}

.fallback-text {
  text-transform: uppercase; /* 将首字母转换为大写 */
  font-weight: bold;
}


.card-info-main {
  flex-grow: 1;
}

.card-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.card-full-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-right: 8px;
}

.card-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1;
  white-space: nowrap;
}

.main-card-badge {
  color: rgb(208, 208, 208);
}

.card-position {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.card-company-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.card-company-name {
  font-size: 13px;
  color: #666;
  margin-right: 8px;
  white-space: normal; /* 允许换行 */
}

.certified-badge {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.uncertified-badge {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

.card-edit-button {
  position: absolute;
  top: -5px;
  right: -5px;
  color: #409eff;
  font-size: 12px;
  display: none;
}

.card-contact-details p {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
  line-height: 1.2;
}

.card-contact-details p:last-child {
  margin-bottom: 0;
}

.contact-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.phone-dot {
  background-color: #67c23a; /* 绿色点 */
}

.email-dot {
  background-color: #67c23a; /* 蓝色点 */
}
</style>