<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<title>展会添加/编辑</title>
	<link rel="stylesheet" href="/eip-web-vip/lib/layui2.6.13/css/layui.css" >
	<link rel="stylesheet" href="../css/vip.css" >
	<script src="../js/jquery-1.11.1.min.js"></script>
	<script src="/eip-web-vip/lib/layui2.6.13/layui.js"></script>
	<!-- <script src="../js/jquery.form.js"></script> -->
	<script src="../js/common/variableFix.js"></script>
	<script src="./vue/vue.js"></script>
	<script src="./vue/axions.js"></script>
	<link rel="stylesheet" href="./vue/element-ui/index.css" >
	<script src="./vue/element-ui/index.js"></script>
	<!-- <script src="./vue/wangEditor.min.js"></script> -->

	<link rel="stylesheet" type="text/css" href="../css/cropper.min.css" />
	<link rel="stylesheet" type="text/css" href="../css/ImgCropping.css" />
	<script src="../js/cropper.min.js"></script>

	<link rel="stylesheet" href="../css/member-theme.css" >
	<script src="../js/member-theme.js?v=230629"></script>
	<script src="../js/common/fixAjax.js"></script>
	<script src="../js/common/_vipUtil.js"></script>
	<script>
			var uinfo = util.checkLogin();
			var token = uinfo.token;
	</script>
</head>
<body class="exhiAdd flex">
<div class="layui-card flex1"  id="app">
	<div class="layui-card-header">新建展会</div>
	<div class="layui-card-body" v-cloak>
		<section class="layui-form layui-form-pane main-form">
			<!-- <input type="hidden" name="_notAutoSubmit" value=""> -->
			<div class="layui-row">
				<div class="layui-col-md6">
					<div class="layui-form-item" pane>
						<label class="layui-form-label"><i class="require">*</i>展会名称</label>
						<div class="layui-input-block">
							<el-input v-model.trim="form.name" name="jsf-name" />
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item flex" pane>
						<label class="layui-form-label">
							<i class="require">*</i>行业分类
						</label>
						<div class="layui-input-inline" style="margin-right: 0;">
							<el-input disabled v-model.trim="form.tradeName" name="jsf-tradeName" placeholder="点击右侧选择"/>
						</div>
						<button class="layui-btn layui-btn-primary" @click="selectTrade" style="margin-left: -1px;height: 40px;">选择</button>
					</div>
				</div>
			</div>
			<div>
				<div class="layui-form-item layui-row layui-inline" style="height: 180px;padding-top: 5px;">
					<input style="display:none" type="file" id="jsLogoUploader" accept="image/*" onchange="selecttype(this)">
					<div class="layui-col-lg12" style="display: flex;flex-flow:  row nowrap;align-items: center;">
					<!-- <div class="catalog-right" style="float: right;width: 20%; margin-bottom: 100px;margin-top: 40px;position: relative;"> -->
						<div class="set-bottom-right" id="jsLogoUploaderHelper" onclick="$('#jsLogoUploader').trigger('click')" style="cursor: pointer;margin-right: 20px;">
							<img v-if="form.logo" :src="form.logo" alt="" class="layui-upload-img" id="logoImg">
							<p>上传LOGO图片</p>
						</div>
						<!-- <div class="non-operational" style="display:none;"></div> -->
						<div class="upload-tip color-tip" >推荐上传400*300像素图片<br /> 支持.JPG .JPEG .PNG 格式，<br />大小不超过2M, PNG大小不超过300K</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>举办时间
						</label>
						<div class="layui-input-inline">
							<el-date-picker name="jsf-startTime"
								v-model.trim="form.startTime"
								value-format="yyyy-MM-dd"
								type="date"
								:picker-options="pickerOptions"
								clearable
								placeholder="选择日期"
							></el-date-picker>
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>截止时间
						</label>
						<div class="layui-input-inline">
							<el-date-picker name="jsf-endTime"
								v-model.trim="form.endTime"
								:picker-options="pickerOptions"
								value-format="yyyy-MM-dd"
								type="date"
								clearable
								:disabled="!form.startTime"
								:placeholder="form.startTime ? '选择截止时间' : '请先选择开始时间'"
							></el-date-picker>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>举办国家
						</label>
						<div class="layui-input-block" id="jsSelectCountry">
							<el-select v-model.trim="form.country"
								placeholder="请选择国家"
								@change="selectCountry"
								@clear="_ => { this.form.province='';this.form.city='' }"
								clearable
								filterable
							>
								<el-option
									v-for="it in countrys"
									:key="it.id"
									:label="it.nation"
									:value="it.nation">
								</el-option>
							</el-select>
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>举办城市
						</label>
						<div class="layui-input-block">
							<el-input v-model.trim="form.city"
								:placeholder="form.country ? '选择城市' : '请先选择国家'"
								@click.native="selectCity"
								:disabled="!form.country"
							></el-input>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md8">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>详细地址
						</label>
						<div class="layui-input-block">
							<el-input v-model.trim="form.address"
								name="jsf-address"
								placeholder="请输入详细地址" />
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md8">
					<div class="layui-form-item" pane>
						<label class="layui-form-label">
							<i class="">*</i>搜索关键词
						</label>
						<div class="layui-input-block">
							<el-input v-model.trim="form.keywords"
								name="jsf-keywords"
								placeholder="请输入关键词 多个用英文,隔开 以便更容易搜索到您的展会" />
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
	<div class="footer flex" style="flex-direction: row-reverse;">
		<button class="layui-btn" onclick="history.go(-1);">取消</button>
		<div style="width: 20px;"></div>
		<button class="layui-btn" :disabled="!form.tradeId || !form.name" @click="saveExhi">提交审核</button>
	</div>
</div>

<!-- 上传裁剪相关 -->
<div class="tailoring-container ImgShow" style="display: none;">
	<div class="black-cloth" onclick="closeTailor(this)"></div>
	<div class="tailoring-content">
		<div class="tailoring-content-one">
			<label title="上传图片" for="chooseImg" class="l-btn choose-btn">
				<input type="file" name="file" id="chooseImg" onchange="selectImg(this)" class="hidden" accept="image/*">
				选择图片
			</label>
			<div class="close-tailoring" onclick="closeTailor(this)">×</div>
		</div>
		<div class="tailoring-content-two">
			<div class="tailoring-box-parcel">
				<img id="tailoringImg">
			</div>
			<div class="preview-box-parcel">
				<p>图片预览：</p>
				<div class="square previewImg"></div>
				<div class="circular previewImg"></div>
			</div>
		</div>
		<div class="tailoring-content-three">
			<button class="l-btn cropper-reset-btn">复位</button>
			<button class="l-btn cropper-rotate-btn">旋转</button>
			<button class="l-btn cropper-scaleX-btn">换向</button>
			<button class="l-btn sureCut" id="sureCut">确定</button>
		</div>
	</div>
</div>
<!-- 图片上传 裁剪 -->
<script type="text/javascript">
var gfile;
//弹出框水平垂直居中
(window.onresize = function() {
	var win_height = $(window).height();
	var win_width = $(window).width();
	if (win_width <= 768) {
		$(".tailoring-content").css({
			"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
			"left": 0
		});
	} else {
		$(".tailoring-content").css({
			"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
			"left": (win_width - $(".tailoring-content").outerWidth()) / 2
		});
	}
})();

var companysizes = 4 / 3 //100 / 64.7;
//var logosize = 25 /25;
//cropper图片裁剪
$('#tailoringImg').cropper({
	aspectRatio: companysizes, //默认比例
	preview: '.previewImg', //预览视图
	guides: false, //裁剪框的虚线(九宫格)
	autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
	dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
	movable: true, //是否允许移动剪裁框
	resizable: true, //是否允许改变裁剪框的大小
	zoomable: true, //是否允许缩放图片大小
	mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
	touchDragZoom: true, //是否允许通过触摸移动来缩放图片
	rotatable: true, //是否允许旋转图片
	crop: function(e) {
		// 输出结果数据裁剪图像。
	}
});
//旋转
$(".cropper-rotate-btn").on("click", function() {
	$('#tailoringImg').cropper("rotate", 45);
});
//复位
$(".cropper-reset-btn").on("click", function() {
	$('#tailoringImg').cropper("reset");
});
//换向
var flagX = true;
$(".cropper-scaleX-btn").on("click", function() {
	if (flagX) {
		$('#tailoringImg').cropper("scaleX", -1);
		flagX = false;
	} else {
		$('#tailoringImg').cropper("scaleX", 1);
		flagX = true;
	}
	flagX != flagX;
});
//关闭裁剪框
function closeTailor() {
	$(".tailoring-container").toggle();
}
function dataURLtoFile(dataurl, filename) { //将base64转换为文件
	var arr = dataurl.split(','),
		mime = arr[0].match(/:(.*?);/)[1],
		bstr = atob(arr[1]),
		n = bstr.length,
		u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new File([u8arr], filename, {
		type: mime
	});
}


//图像上传
function selectImg(file) {
	var max_size = 10240;
	var img_size = 1024;
	var fileData = file.files[0];
	file.value = '';
	app.checkFile(fileData.false)
	var size = fileData.size;
	$(".tailoring-box-parcel").show();
	$(".preview-box-parcel").show();
	if (size > max_size * 1024) {
		app.layerMsg("图片大小不能超过10M")
	} else if (size > img_size * 1024) {
		var reader = new FileReader();
		reader.readAsDataURL(fileData)
		reader.onload = function(e) {
			let image = new Image() //新建一个img标签（还没嵌入DOM节点)
			image.src = e.target.result
			image.onload = function() {
				let canvas = document.createElement('canvas'),
					context = canvas.getContext('2d'),
					imageWidth = image.width / 5, //压缩后图片的大小
					imageHeight = image.height / 5,
					data = ''
				canvas.width = imageWidth
				canvas.height = imageHeight
				context.drawImage(image, 0, 0, imageWidth, imageHeight)
				data = canvas.toDataURL('image/jpeg')
				// console.log(data)
				//压缩完成
				// document.getElementById('img').src = data
				$('#tailoringImg').cropper('replace', data, false)
			}
		}
	} else {
		var reader = new FileReader();
		reader.readAsDataURL(fileData)
		reader.onload = function(evt) {
			var replaceSrc = evt.target.result;
			//更换cropper的图片
			$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
			$('#replaceImg').cropper('replace', replaceSrc, false);
		}
	}
}
// 产品图片
function selecttype(e) {
	let file = e.files[0];
	e.value = '';
	app.checkFile(file, false);
	var file_id = file.type || '';
	if(file_id.startsWith('image/')) {
		var max_size = 10240;
		var img_size = 1024;
		var size = file.size;
		$(".ImgShow").toggle();
		if (size > max_size * 1024) {
			app.layerMsg("图片大小不能超过10M");
		} else if (size > img_size * 1024) {
			var reader = new FileReader();
			reader.readAsDataURL(file)
			reader.onload = function(e) {
				let image = new Image() //新建一个img标签（还没嵌入DOM节点)
				image.src = e.target.result
				image.onload = function() {
					let canvas = document.createElement('canvas'),
						context = canvas.getContext('2d'),
						imageWidth = image.width / 5, //压缩后图片的大小
						imageHeight = image.height / 5,
						data = ''
					canvas.width = imageWidth
					canvas.height = imageHeight
					context.drawImage(image, 0, 0, imageWidth, imageHeight)
					data = canvas.toDataURL('image/jpeg')
					//压缩完成
					// document.getElementById('img').src = data
					$('#tailoringImg').cropper('replace', data, false)
				}
			}
		} else {
			var reader = new FileReader();
			reader.readAsDataURL(file)
			reader.onload = function(evt) {
				var replaceSrc = evt.target.result;
				//更换cropper的图片
				$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
				$('#replaceImg').cropper('replace', replaceSrc, false);
				//图像上传
				//alert('图像上传');
			}
		}
		$(".tailoring-box-parcel").show();
	} else if( file_id.startsWith('video/') ){
		app.layerMsg("只能上传图片 !");
		// app.uploadImgOss(file)
	} else {
		// app.layerMsg("只能上传图片或视频 !");
		app.layerMsg("只能上传图片 !");
	}
}
$("#sureCut").on("click", async function() {
	if ($("#tailoringImg").attr("src") == null) return false;
	var cas = $('#tailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
	var base64url = cas.toDataURL()
	var timestamp = new Date().getTime()
	var file = dataURLtoFile(base64url, timestamp + ".jpg");
	const data = await app.uploadImgOss(file, false);
	app.form.logo = data.path
	app.form.logoName = data.fileName
	console.log('upload', data);
	//关闭裁剪框
	$(".ImgShow").hide();
	$(".tailoring-box-parcel").hide();
	$(".preview-box-parcel").hide();
});
</script>

<script src="../js/common/_vipMixin.js?v=20220902"></script>
<script>
$(function(){
	// // dataObj : othis,elem,value
	// window.selectedCountry2 = async function (val) {
	// 	// const val = dataObj.value || ''
	// 	app.form.country = val || ''
	// 	if(val) {
	// 		const data = await app._getProvinces(val, false)
	// 		let html = '<option value="">选择省份</option>';
	// 		data.forEach(it => {
	// 			html += `<option value="${it.f_province}">${it.f_province}</option>`;
	// 		})
	// 		$('select[name=select-province').html(html)
	// 		layui.form.render('select','selectorArea');
	// 		layui.form.on('select(select-province)', function(data) {
	// 			selectedProvince(data.value)
	// 		});
	// 	}
	// }
	// dataObj : othis,elem,value
	window.selectedProvince = async function (val) {
		// const val = dataObj.value || ''
		app.form.province = val || ''
		if(val) {
			const data = await app._getAreas(val)
			let html = '<option value="">选择城市</option>';
			data.forEach(it => {
				html += `<option value="${it.f_city_name}">${it.f_city_name}</option>`;
			})
			$('select[name=select-city').html(html);
			layui.form.render('select','selectorArea');
			layui.form.on('select(select-city)', function(data) {
				app.form.city = data.value;
				layer.close(app.layerIdx);
			})
		}
	}
	window.selectIndustry = function() {
		var idx = layer.open({
			type: 1,
			title: '行业分类',
			skin: 'layui-layer-demo',
			area: ['300px', '400px'],
			shadeClose: true,
			content: '<div id="jsTradeTree" class="" style="padding-left: 20px;"></div>',
			cancel() {
				layer.close(idx);
				return false
			},
			success() {
				layui.tree.render({
					id:'tradeId', // 确保唯一
					elem: '#jsTradeTree',
					data: app.tradeTree,
					onlyIconControl: true, //是否仅允许节点左侧图标控制展开收缩
					accordion: true,
					checked: true,
					click: function(obj) {
						app.form.tradeId = obj.data.tradeId
						app.form.tradeName = obj.data.tradeName
						layer.close(idx);
					}
				});
			},
		});
	}
	window.app = new Vue({
		el: '#app',
		mixins: [_vipMixinCommon],
		data: {
			tradeTree: [],
			countrys: [],  	// 国家预设
			countrys2: [],  // 国家其他
			provinces: [],
			form: {
				name: '',
				tradeId: '',
				tradeName: '',
				logo: '',
				logName: '',
				startTime: '',
				endTime: '',
				country: '',
				province: '',
				city: '',
				address: '',
				keywords: '',
			},
			layerIdx: null,
		},
		// async mounted() {
		// },
		computed: {
			selectorCountry2() {
				let tmp = `<select name="select-country2" lay-filter="select-country2">
					<option value="">选择其他国家</option>`
				this.countrys2.forEach(it => {
					tmp += `<option value="${it.nation}">${it.nation}</option>`
				})
				return tmp + `</select>`;
			},
			selectorProvince() {
				let tmp = `<select name="select-province" lay-filter="select-province">
					<option value="">选择省份</option>`
				this.provinces.forEach(it => {
					tmp += `<option value="${it.f_province}">${it.f_province}</option>`
				})
				return tmp + `</select>`;
			}
		},
		methods: {
			async init() {
				this._loading()
				const [data1,data2] = await Promise.all([this._getTradeTree(),this._getCountry(true)])
				this.tradeTree = data1;
				let tmp = data2;
				tmp.push({
					id: -1,
					nation: '其他',
					continent: '',
					orgNum: '',
					nationEn: 'Other',
					areaCode: '-1',
				});
				this.countrys = tmp;
				if(this.pid) { // 编辑
					const data = await this._getProjectInfo(this.pid)
					this.form.address = data.f_address || ''
					this.form.country = data.f_country || ''
					this.form.province = data.f_province || ''
					this.form.city = data.f_city || ''
					this.form.tradeId = data.tradeId || ''
					this.form.tradeName = data.tradeName || ''
					this.form.keywords = data.searchKeys || ''
					this.form.logo = data.projectPicUrl || ''
					this.form.logoName = data.projectPicName || ''
					this.form.startTime = data.f_start_time || ''
					this.form.endTime = data.f_end_time || ''
					this.form.name = data.f_project_name || ''
					if(data.f_country) {
						this.provinces =  await this._getProvinces(data.f_country, true)
					}
				}
				this.$nextTick( _ => {
					this._loaded()
				})
			},
			async saveExhi() {
				const that = this
				if(!that.form.name)  that.alerts('请填写展会名称 !')
				if(!that.form.tradeId) that.alerts('请选择行业分类 !')
				const paras = {
					f_project_name: that.form.name || '',
					tradeId: that.form.tradeId || '',
					f_city:	that.form.city || '',
					f_province: that.form.province || '',
					f_country: that.form.country || '',
					f_start_time: that.form.startTime || '',
					f_end_time: that.form.endTime || '',
					f_address: that.form.address || '',
					searchKeys: that.form.keywords || '',
					projectPicUrl: that.form.logo || '',
					projectPicName: that.form.logoName || '',
				};
				if(that.pid) {
					paras.f_project_id = that.pid
				}
				const {data} = await _axios.post('project/save',that.obj2FormData(paras)).catch(e => { that.alerts(e.message) })
      	that.checkReturn(data,'提交审核失败!')
				layer.msg('提交成功, 即将跳转 !',{icon: 1, shade: 0.3})
				setTimeout( _ => {
					history.go(-1)
					// location.href = 'myCreateExhibition.html';
				}, 3000);
			},
			selectHelper(title, content, cb) {
				this.layerIdx = layer.open({
					type: 1,
					title,
					btn: 'yes',
					content,
					area: ['463px','500px'],
					skin: 'layer-theme-hide-footerBtn',
					closeBtn: 0,
					shadeClose: true,
					move: true,
					success(layero, index) {
						layui.form.render('select','selectorArea');
						cb && cb();
					},
				})
			},
			async selectCity() {
				// if(!city) return;
				if(!this.form.country) return;
				let html = `<section class="layui-form layui-form-pane" lay-filter="selectorArea">
					<div class="layui-row" style="margin-top: 20px;">
						<div class="layui-col-md-offset1 layui-col-md10">
							<div class="layui-form-item" pane>
								<label class="layui-form-label"><i class="">*</i>举办省份</label>
								<div class="layui-input-block">
									${this.selectorProvince}
								</div>
							</div>
						</div>
					</div>`;
				this.selectHelper('选择城市', html + `
					<div class="layui-row">
						<div class="layui-col-md-offset1 layui-col-md10">
							<div class="layui-form-item" pane>
								<label class="layui-form-label"><i class="">*</i>举办城市</label>
								<div class="layui-input-block">
									<select name="select-city" lay-filter="select-city">
										<option value="">先选择省份</option></select>
								</div>
							</div>
						</div>
					</div></section>`, _ =>{
						layui.form.on('select(select-province)', function(data){
							selectedProvince(data.value);
						});
					})
			},
			async selectCountry(nation) { // 选择其他国家
				this.provinces = await app._getProvinces(nation, nation !== '其他')
				if(nation !== '其他') return;
				const that = this;
				let html = `<section class="layui-form layui-form-pane" lay-filter="selectorArea">
					<div class="layui-row" style="margin-top: 20px;">
						<div class="layui-col-md-offset1 layui-col-md10">
							<div class="layui-form-item" pane>`;
				this.countrys2 = await this._getCountry(false);
				html += `<label class="layui-form-label"><i class="">*</i>举办国家</label>
							<div class="layui-input-block">
								${this.selectorCountry2}
							</div>
						</div>
					</div>
				</div>`;
				// <div class="layui-row">
				// 	<div class="layui-col-md-offset1 layui-col-md10">
				// 		<div class="layui-form-item" pane>
				// 			<label class="layui-form-label"><i class="">*</i>举办省份</label>
				// 			<div class="layui-input-block">
				// 				<select name="select-province" lay-filter="select-province">
				// 					<option value="">先选择国家</option></select>
				// 			</div>
				// 		</div>
				// 	</div>
				// </div>`;
				that.selectHelper('选择国家', html + `</section>`, _ => {
					layui.form.on('select(select-country2)', function(data){
						// selectedCountry2(data.value)
						that.form.country = data.value || '';
						that.form.province = '';
						that.form.city = '';
						layer.close(that.layerIdx);
					});
				})
			},
			selectTrade(){
				selectIndustry()
			},
		}
	});
	app.init();
});
</script>
</body>
</html>