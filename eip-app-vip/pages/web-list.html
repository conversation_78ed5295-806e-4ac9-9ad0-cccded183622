<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>跳转链接</title>
	</head>
	<style type="text/css">
		ul li {
			list-style: none;
		}

		a {
			text-decoration: none;
			color: #333;
		}
        .web-list {
			width: 1200px;
			overflow: hidden;
			margin: 50px auto;
		}
		.web-list ul li {
			width: 226px;
            height: 140px;
			border: 1px solid #ccc;
			margin: 20px;
			text-align: center;
            float: left;
			border-radius: 4px;
			line-height: 30px;
		}

		.web-list ul li a {
			display: block;
			width: 100%;
			height: 100%;
			color: #666;
			font-weight: bold;
			padding: 20% 0;
		}
        .web-list ul li a p:last-child {
			color: #999;
		}
		p {
			margin: 0;
		}
	</style>
	<body>
		<div class="web-list">
			<ul>
				<li>
					<a href="pc_officialWeb/index.html" target="_blank">
						<p>展之官网</p>
						<p>(pc端)</p>
					</a>
				</li>
				<li>
					<a href="mobile-officialweb/index.html" target="_blank">
						<p>展之官网</p>
						<p>(移动端)</p>
					</a>
				</li>
				<li>
					<a href="Mock-Sponsor/index.html" target="_blank">
						<p>模拟主办方官网</p>
						<p>(pc端)</p>
					</a>
				</li>
				<li>
					<a href="mobile-sponsor/index.html" target="_blank">
						<p>模拟主办方官网</p>
						<p>(移动端)</p>
					</a>
				</li>
				<li>
					<a href="mobile-vipcenter/index.html" target="_blank">
						<p>会员中心</p>
						<p>(移动端)</p>
					</a>
				</li>
				<li>
					<a href="mobile/login.html" target="_blank">
						<p>信息门户</p>
						<p>(移动端)</p>
					</a>
				</li>
				<li>
					<a href="industry-websites/index.html" target="_blank">
						<p>行业网站</p>
						<p>(pc端)</p>
					</a>
				</li>
			</ul>
		</div>
	</body>
</html>
