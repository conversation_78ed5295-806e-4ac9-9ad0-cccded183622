package com.eip.service.sponsor.biz;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.entity.OrderBS;
import com.eip.facade.sponsor.entity.OrderDtlBS;
import com.eip.facade.sponsor.entity.OrderDtlBSList;

import java.util.List;

public interface OrderBSBiz {

	int count(int key,Integer isChecked,OrderBS orderBS);
	
	List<OrderBS> select(PageParam pageParam, int key, Integer isChecked, OrderBS orderBS);

//	/**
//	 * 查询单条订单
//	 * @param orderBS
//	 * @return
//	 */
//	OrderBS selectOne(OrderBS orderBS);

	AjaxResponse save(OrderBS orderBS, OrderDtlBSList orderDtlBSList);


	int insert(OrderBS orderBS, List<OrderDtlBS> list);

	/**
	 * 插入订单
	 * @param orderBS
	 * @return
	 */
	int insertOrderBS(OrderBS orderBS);
	
	int update(OrderBS orderBS,List<OrderDtlBS> orderDtlBSList);
	
	int delete(Integer orderBSId);
	
	int deleteBatch(List<OrderBS> list);
	
	Boolean isExistOnly(int orderBSId, int projectId, int clientId,String orderAmountType);

	/**
	 *  审核展具时生成展具费用
	 * @param taskDtlId
	 * @param projectId
	 * @param clientId
	 * @param orgNum
	 * @return
	 */
	boolean saveGenerateToolCost(Integer taskDtlId, Integer projectId, Integer clientId, Integer orgNum);
	/**
	 *  审核展具时生成展具费用
	 * @param taskDtlId
	 * @param projectId
	 * @param clientId
	 * @param orgNum
	 * @return
	 */
	boolean saveBoothToolCost(Integer taskDtlId, Integer projectId, Integer clientId, Integer orgNum);

	int deleteBoothToolCost(Integer taskDtlId,Integer projectId, Integer clientId, Integer orgNum);

	/**
	 * 审核时生成费用
	 * 待办签证
	 * 邀请函
	 * 进馆证
	 * 翻译
	 * @param orderDtlBS
	 */
	int saveGenerateDtlCost(OrderDtlBS orderDtlBS);

	/**
	 * 审核时生成费用
	 * 待办签证
	 * 邀请函
	 * 进馆证
	 * 翻译
	 * @param orderDtlBS
	 */
	int saveGenerateTaskDtlCost(OrderDtlBS orderDtlBS);

	/**
	 * 弃审时删除费用
	 * 待办签证
	 * 邀请函
	 * 进馆证
	 * 翻译
	 * @param orderDtlBS
	 */
	int deleteGenerateTaskDtlCost(OrderDtlBS orderDtlBS);

	int generateVisaCost(OrderDtlBS orderDtlBS);

	int delVisaCost(OrderDtlBS orderDtlBS);

	/**
	 * 审核人员费用（人员费+单房费+签证费）
	 * @param orderDtlBS
	 * @return
	 */
	int saveGeneratePersonCost(OrderDtlBS orderDtlBS,Integer taskDetailId);

	/**
	 * 审核人员费用（人员费+单房费+签证费）
	 * @param orderDtlBS
	 * @return
	 */
	int savePersonCost(OrderDtlBS orderDtlBS);

	/**
	 * 人员费用弃审（人员费+单房费+签证费）
	 * @param orderDtlBS
	 * @return
	 */
	int deletePersonCost(OrderDtlBS orderDtlBS);

	/**
	 * 根据条件查询订单
	 * @param clientId
	 * @param projectId
	 * @param orderAmountType 订单费用类型(BOOTH、PERSON、OTHER)
	 * @return
	 */
	OrderBS selectByOrderAmountType(Integer clientId,Integer projectId, String orderAmountType);

	/**
	 * 判断人员费是否和订单表是否不一样
	 * @param orderDtlBS
	 */
	Boolean isPersonCostDifferent(OrderDtlBS orderDtlBS,String orderAmountType);

	/**
	 * 人员费更新
	 * @param orderDtlBS
	 * @return
	 */
	void updatePersonCost(OrderDtlBS orderDtlBS);

	/**
	 * 搭建方展具审核
	 * @param orderDtlBS
	 * @return
	 */
	int generateBuilderToolCost(OrderDtlBS orderDtlBS);

	/**
	 * 搭建方展具弃审
	 * @param orderDtlBS
	 * @return
	 */
	int delBuilderToolCost(OrderDtlBS orderDtlBS);

}
