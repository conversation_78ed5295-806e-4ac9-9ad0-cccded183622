package com.eip.async;

import com.eip.common.enums.ExhibitorTaskTypeEnum;
import com.eip.common.enums.ZzyUserTypeEnum;
import com.eip.common.util.DateUtil;
import com.eip.facade.sponsor.dto.OperatorDto;
import com.eip.facade.sponsor.entity.*;
import com.eip.facade.sponsor.vo.EnterpriseFocusMemberVo;
import com.eip.facade.sponsor.vo.EnterprisePersonVo;
import com.eip.service.sponsor.orgdao.*;
import com.eip.service.sponsor.zzysysmapper.ZzySysUserMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @USER: zwd
 * @DATE: 2025-06-10 11:06
 * @DESCRIPTION: 企业
 */
@Slf4j
@Component
public class EnterpriseAsyncService {
    @Autowired
    private EnterpriseMapper enterpriseMapper;
    @Autowired
    private EnterpriseFocusMemberMapper enterpriseFocusMemberMapper;
    @Autowired
    private EnterprisePersonMapper enterprisePersonMapper;
    @Autowired
    private EnterpriseProductMapper enterpriseProductMapper;
    @Autowired
    private EnterpriseProductPicMapper enterpriseProductPicMapper;
    @Autowired
    private NewIntegerIdDao newIntegerIdDao;
    @Autowired
    private ZzySysUserMapper zzySysUserMapper;
    @Autowired
    private ServeProductDao serveProductDao;
    @Autowired
    private ServeProductPicDao serveProductPicDao;
    @Autowired
    private ServeCertDao serveCertDao;
    @Autowired
    private ServeVisaDao serveVisaDao;
    @Autowired
    private ServeInviteDao serveInviteDao;
    @Autowired
    private ServePersonDao servePersonDao;
    @Autowired
    private MemberInfoMapper memberInfoMapper;


    /**
     * 查询企业Id
     * @param zzyUserId
     * @param orgNum
     * @return
     */
    public Map<String, Object> queryEnterpriseId(Integer zzyUserId, Integer orgNum) {
        ZzyUser zzyUser = zzySysUserMapper.selectByPrimaryKey(zzyUserId);
        if(Objects.isNull(zzyUser)) return null;
        Map<String,Object> resultMap = null;
        if(ZzyUserTypeEnum.ENTERPRISE.getCode().equals(zzyUser.getZzyuserType())){
            Long enterpriseId = queryOneEnterpriseId(zzyUserId, orgNum);
            if(enterpriseId == null) return resultMap;
            resultMap = Maps.newHashMapWithExpectedSize(2);
            resultMap.put("enterpriseId",enterpriseId);
        }else{
            List<Long> moreEnterpriseIdList = queryMoreEnterpriseId(zzyUserId, orgNum);
            if(CollectionUtils.isEmpty(moreEnterpriseIdList)) return resultMap;
            resultMap = Maps.newHashMapWithExpectedSize(2);
            resultMap.put("enterpriseIdList",moreEnterpriseIdList);
        }
        return resultMap;
    }

    /**
     * 查询单个企业
     * @param zzyUserId
     * @param orgNum
     * @return
     */
    public Long queryOneEnterpriseId(Integer zzyUserId,Integer orgNum){
        Long enterpriseId = null;
        Enterprise build = Enterprise.builder().enterpriseZzyuserId(zzyUserId).orgNum(orgNum).build();
        List<Enterprise> enterpriseList = enterpriseMapper.select(build);
        if(CollectionUtils.isNotEmpty(enterpriseList)){
            Enterprise enterprise = enterpriseList.get(0);
            enterpriseId = enterprise.getEnterpriseId();
        }
        return enterpriseId;
    }
    /**
     * 查询单个企业
     * @param zzyUserId
     * @return
     */
    public List<Long> queryMoreEnterpriseId(Integer zzyUserId,Integer orgNum){
        List<Long> enterpriseIdList = null;
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        paramMap.put("zzyUserId",zzyUserId);
        paramMap.put("enterpriseFocusState",1);
        paramMap.put("orgNum",orgNum);
        List<EnterpriseFocusMemberVo> enterpriseFocusMemberList = enterpriseFocusMemberMapper.getEnterpriseAccountList(paramMap);
        if(CollectionUtils.isNotEmpty(enterpriseFocusMemberList)){
            enterpriseIdList = enterpriseFocusMemberList.stream().filter(enterpriseFocusMember -> enterpriseFocusMember.getEnterpriseId()!=null).map(EnterpriseFocusMemberVo::getEnterpriseId).distinct().collect(Collectors.toList());
        }
        return enterpriseIdList;
    }


    /**
     * 同步会刊产品到企业产品
     * @param taskDtlId
     * @param operatorDto
     */
    @Async
    public void batchHandleServeProductToEnterprise(Integer taskDtlId, OperatorDto operatorDto) {
        if(taskDtlId == null) return;
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        paramMap.put("taskDtlId",taskDtlId);
        List<ServeProduct> serveProductList = serveProductDao.selectByMap(paramMap);
        if(CollectionUtils.isEmpty(serveProductList)) return;
        for(ServeProduct serveProduct : serveProductList){
            try {
                insertOrUpdateServeProductToEnterprise(serveProduct.getServeProductId());
            }catch (Exception e){
                log.error("审核会刊且同步会刊产品到企业产品时异常：{}",e);
            }
        }
    }



    public void insertOrUpdateServeProductToEnterprise(Integer serveProductId){
        if(serveProductId == null) return;
        ServeProduct serveProduct = serveProductDao.selectById(serveProductId);
        if(Objects.isNull(serveProduct)) return;
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        paramMap.put("serveProductId",serveProduct.getServeProductId());
        List<ServeProductPic> serveProductPicList = serveProductPicDao.selectByMap(paramMap);

        Long enterpriseProductId = serveProduct.getEnterpriseProductId();
        if(enterpriseProductId!=null){
            EnterpriseProduct enterpriseProduct = enterpriseProductMapper.selectByPrimaryKey(enterpriseProductId);
            if(Objects.nonNull(enterpriseProduct)) {
                buildCopyServeProductToEnterpriseProduct(serveProduct, enterpriseProduct);
                enterpriseProductMapper.updateEnterpriseProductFormServeProductById(enterpriseProduct);
                if(CollectionUtils.isNotEmpty(serveProductPicList)){
                    for(ServeProductPic serveProductPic : serveProductPicList){
                        EnterpriseProductPic enterpriseProductPicBuild = EnterpriseProductPic.builder()
                                .productId(enterpriseProductId)
                                .build();
                        String pic = serveProductPic.getPic();
                        if(StringUtils.isNotBlank(pic)){
                            enterpriseProductPicBuild.setPicUrl(pic);
                           int count =  enterpriseProductPicMapper.selectCount(enterpriseProductPicBuild);
                           if(count > 0) continue;
                        }
                        Long newEnterpriseProductPicId = newIntegerIdDao.snowNextId();
                        enterpriseProductPicBuild.setProductPicId(newEnterpriseProductPicId);
                        enterpriseProductPicBuild.setIsMain(serveProductPic.getIsMain()!=null ? (1 == serveProductPic.getIsMain() ? true : false) : false);
                        enterpriseProductPicBuild.setShowOrder(serveProductPic.getShowOrder());
                        enterpriseProductPicBuild.setPicUrl(pic);
                        enterpriseProductPicMapper.insert(enterpriseProductPicBuild);
                    }
                }
                return;
            }
        }
        Long enterpriseId = serveProduct.getEnterpriseId();
        if(enterpriseId == null) return;
        String productName = StringUtils.trim(serveProduct.getProductName());
        String productSpec = StringUtils.trim(serveProduct.getProductSpec());
        if(StringUtils.isNotBlank(productName)){
            String queryProductName = productName+productSpec;
            Example example = new Example(EnterpriseProduct.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("enterpriseId",enterpriseId);
            criteria.andCondition("(trim(f_product_name)||trim(f_product_spec)) = '"+queryProductName+"'");
            List<EnterpriseProduct> enterpriseProducts = enterpriseProductMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(enterpriseProducts)){
                for(EnterpriseProduct childEnterpriseProduct : enterpriseProducts){
                    buildCopyServeProductToEnterpriseProduct(serveProduct, childEnterpriseProduct);
                    enterpriseProductMapper.updateEnterpriseProductFormServeProductById(childEnterpriseProduct);
                    if(CollectionUtils.isNotEmpty(serveProductPicList)){
                        for(ServeProductPic serveProductPic : serveProductPicList){
                            EnterpriseProductPic enterpriseProductPicBuild = EnterpriseProductPic.builder()
                                    .productId(childEnterpriseProduct.getProductId())
                                    .build();
                            String pic = serveProductPic.getPic();
                            if(StringUtils.isNotBlank(pic)){
                                enterpriseProductPicBuild.setPicUrl(pic);
                                int count =  enterpriseProductPicMapper.selectCount(enterpriseProductPicBuild);
                                if(count > 0) continue;
                            }
                            Long newEnterpriseProductPicId = newIntegerIdDao.snowNextId();
                            enterpriseProductPicBuild.setProductPicId(newEnterpriseProductPicId);
                            enterpriseProductPicBuild.setIsMain(serveProductPic.getIsMain()!=null ? (1 == serveProductPic.getIsMain() ? true : false) : false);
                            enterpriseProductPicBuild.setShowOrder(serveProductPic.getShowOrder());
                            enterpriseProductPicBuild.setPicUrl(serveProductPic.getPic());
                            enterpriseProductPicMapper.insert(enterpriseProductPicBuild);
                        }
                    }
                }
            }else{
                Long newEnterpriseProductId = newIntegerIdDao.snowNextId();
                EnterpriseProduct build = EnterpriseProduct.builder()
                        .productId(newEnterpriseProductId)
                        .enterpriseId(enterpriseId)
                        .build();
                buildCopyServeProductToEnterpriseProduct(serveProduct,build);
                enterpriseProductMapper.insert(build);
                serveProductDao.updateBindEnterpriseProductIdById(serveProduct.getServeProductId(),newEnterpriseProductId);
                if(CollectionUtils.isNotEmpty(serveProductPicList)){
                    for(ServeProductPic serveProductPic : serveProductPicList){
                        Long newEnterpriseProductPicId = newIntegerIdDao.snowNextId();
                        EnterpriseProductPic enterpriseProductPicBuild = EnterpriseProductPic.builder()
                                .productPicId(newEnterpriseProductPicId)
                                .productId(newEnterpriseProductId)
                                .isMain(serveProductPic.getIsMain()!=null ? (1 == serveProductPic.getIsMain() ? true : false) : false)
                                .showOrder(serveProductPic.getShowOrder())
                                .picUrl(serveProductPic.getPic())
                                .build();
                        enterpriseProductPicMapper.insert(enterpriseProductPicBuild);
                    }
                }
            }
        }
    }


    /**
     * 复制会刊产品到企业产品
     */
    private void buildCopyServeProductToEnterpriseProduct(ServeProduct serveProduct, EnterpriseProduct enterpriseProduct){
        enterpriseProduct.setProductName(serveProduct.getProductName());
        enterpriseProduct.setProductNameEn(serveProduct.getProductNameEn());
        enterpriseProduct.setProductSpec(serveProduct.getProductSpec());
        enterpriseProduct.setProductProfile(serveProduct.getProductData());
        enterpriseProduct.setProductProfileEn(serveProduct.getProductDataEn());
        enterpriseProduct.setTradeId(serveProduct.getTradeId());
        enterpriseProduct.setProductTypeId(serveProduct.getProductTypeBase());
    }

    /**
     * 同步人员和签证、进馆证人员到企业人员
     * @param taskKindCode
     * @param taskDtlId
     * @param operatorDto
     */
    @Async
    public void batchHandlePersonInfoToEnterprisePerson(String taskKindCode, Integer taskDtlId, OperatorDto operatorDto) {
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(6);
        paramMap.put("taskDtlId",taskDtlId);
        paramMap.put("confirm",4);
        paramMap.put("enterprisePersonIdIsNull",true);
        if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
            List<ServeCert> serveCerts = serveCertDao.statisticsSelect(paramMap);
            if(CollectionUtils.isEmpty(serveCerts)) return;
            for(ServeCert serveCert : serveCerts){
                try {
                    insertOrUpdateExhibitorPersonToEnterprise(ExhibitorTaskTypeEnum.CERT.getTaskKindCode(),serveCert.getF_serve_cert_id());
                }catch (Exception e){
                    log.error("审核进馆证且同步人员信息到企业人员时异常：{}",e);
                }
            }
        }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
            List<ServePersonDtl> servePersonDtlList = servePersonDao.selectDtl(paramMap);
            if(CollectionUtils.isEmpty(servePersonDtlList)) return;
            for(ServePersonDtl personDtl : servePersonDtlList){
                try {
                    insertOrUpdateExhibitorPersonToEnterprise(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode(),personDtl.getF_person_dtl_id());
                }catch (Exception e){
                    log.error("审核人员和签证且同步人员信息到企业人员时异常：{}",e);
                }
            }
        }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
            List<ServeVisa> serveVisaList = serveVisaDao.selectByMap(paramMap);
            if(CollectionUtils.isEmpty(serveVisaList)) return;
            for(ServeVisa serveVisa : serveVisaList){
                try {
                    insertOrUpdateExhibitorPersonToEnterprise(ExhibitorTaskTypeEnum.VISA.getTaskKindCode(),serveVisa.getServeVisaId());
                }catch (Exception e){
                    log.error("审核待办签证且同步人员信息到企业人员时异常：{}",e);
                }
            }
        }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
            List<ServeInvite> serveInviteList = serveInviteDao.selectByMap(paramMap);
            if(CollectionUtils.isEmpty(serveInviteList)) return;
            for(ServeInvite serveInvite : serveInviteList){
                try {
                    insertOrUpdateExhibitorPersonToEnterprise(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode(),serveInvite.getF_serve_invite_id());
                }catch (Exception e){
                    log.error("审核邀请函且同步人员信息到企业人员时异常：{}",e);
                }
            }
        }
    }

    public void insertOrUpdateExhibitorPersonToEnterprise(String taskKindCode, Integer personId) {
        if(StringUtils.isBlank(taskKindCode) || personId == null) return;
        Date currentNow = DateUtil.getCurrentNow();
        Map<String,Object> paramMap = Maps.newHashMapWithExpectedSize(10);
        ServeCert serveCert = null;
        ServePersonDtl personDtl = null;
        ServeInvite serveInvite = null;
        ServeVisa serveVisa = null;
        Long enterpriseId = null;
        Long enterprisePersonId = null;
        String idCard = null;
        String sexName = null;
        String fullName = null;
        String mobile = null;
        String email = null;
        if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
            paramMap.put("serveCertId",personId);
            List<ServeCert> serveCertList = serveCertDao.select(paramMap);
            if(CollectionUtils.isEmpty(serveCertList)) return;
            serveCert = serveCertList.get(0);

            sexName = StringUtils.isNotBlank(serveCert.getF_sex()) ? ("1".equals(serveCert.getF_sex()) ? "男" : "女") : null;
            if(serveCert.getF_id_type()!=null && 1 == serveCert.getF_id_type()){
                idCard = StringUtils.trim(serveCert.getF_id_card());
            }

            enterprisePersonId = serveCert.getEnterprisePersonId();
            enterpriseId = serveCert.getEnterpriseId();
            fullName = StringUtils.trim(serveCert.getF_cert_name());
            mobile = StringUtils.trim(serveCert.getMobilePhoneNum());
            email = StringUtils.trim(serveCert.getF_email());
        }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
            paramMap.put("personDtlId",personId);
            List<ServePersonDtl> personDtlList = servePersonDao.selectDtl(paramMap);
            if(CollectionUtils.isEmpty(personDtlList)) return;
            personDtl = personDtlList.get(0);

            sexName = StringUtils.isNotBlank(personDtl.getF_sex_id()) ? ("1".equals(personDtl.getF_sex_id()) ? "男" : "女") : null;
            idCard = StringUtils.trim(personDtl.getF_id_number());

            enterprisePersonId = personDtl.getEnterprisePersonId();
            enterpriseId = personDtl.getEnterpriseId();
            fullName = StringUtils.trim(personDtl.getF_name());
            mobile = StringUtils.trim(personDtl.getF_mobile());
            email = StringUtils.trim(personDtl.getF_email());
        }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
            paramMap.put("serveVisaId",personId);
            List<ServeVisa> serveVisaList = serveVisaDao.selectByMap(paramMap);
            if(CollectionUtils.isEmpty(serveVisaList)) return;
            serveVisa = serveVisaList.get(0);

            sexName = StringUtils.isNotBlank(serveVisa.getSexId()) ? ("1".equals(serveVisa.getSexId()) ? "男" : "女") : null;
            idCard = StringUtils.trim(serveVisa.getIdNumber());

            enterprisePersonId = serveVisa.getEnterprisePersonId();
            enterpriseId = serveVisa.getEnterpriseId();
            fullName = StringUtils.trim(serveVisa.getName());
            mobile = StringUtils.trim(serveVisa.getMobile());
            email = StringUtils.trim(serveVisa.getEmail());
        }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
            paramMap.put("serveInviteId",personId);
            List<ServeInvite> serveInviteList = serveInviteDao.selectByMap(paramMap);
            if(CollectionUtils.isEmpty(serveInviteList)) return;
            serveInvite = serveInviteList.get(0);

            sexName = StringUtils.isNotBlank(serveInvite.getF_sex()) ? ("1".equals(serveInvite.getF_sex()) ? "男" : "女") : null;
            enterprisePersonId = serveInvite.getEnterprisePersonId();
            enterpriseId = serveInvite.getEnterpriseId();
            fullName = StringUtils.trim(serveInvite.getF_name_cn());
            mobile = StringUtils.trim(serveInvite.getF_mobile());
            email = StringUtils.trim(serveInvite.getF_email());
        }
        //当前已存在关联
        if(enterprisePersonId!=null){
            EnterprisePerson enterprisePerson = enterprisePersonMapper.selectByPrimaryKey(enterprisePersonId);
            if(Objects.nonNull(enterprisePerson)){
                Long memberInfoId = enterprisePerson.getMemberInfoId();
                if(memberInfoId != null){
                    MemberInfo memberInfo = memberInfoMapper.selectByPrimaryKey(memberInfoId);
                    if(Objects.nonNull(memberInfo)){
                        if(StringUtils.isNotBlank(fullName)) memberInfo.setFullName(fullName);
                        if(StringUtils.isNotBlank(sexName)) memberInfo.setSex(sexName);
                        if(StringUtils.isNotBlank(idCard)) memberInfo.setIdCard(idCard);
                        memberInfoMapper.updateByPrimaryKey(memberInfo);
                    }else{
                        Long newMemberInfoId = newIntegerIdDao.snowNextId();
                        MemberInfo memberInfoBuild = MemberInfo.builder()
                                .memberInfoId(newMemberInfoId)
                                .fullName(fullName)
                                .sex(sexName)
                                .idCard(idCard)
                                .createTime(currentNow)
                                .updateTime(currentNow)
                                .build();
                        memberInfoMapper.insert(memberInfoBuild);
                        enterprisePerson.setMemberInfoId(newMemberInfoId);
                    }
                }else{
                    Long newMemberInfoId = newIntegerIdDao.snowNextId();
                    MemberInfo memberInfoBuild = MemberInfo.builder()
                            .memberInfoId(newMemberInfoId)
                            .fullName(serveCert.getF_cert_name())
                            .sex(sexName)
                            .idCard(idCard)
                            .createTime(currentNow)
                            .updateTime(currentNow)
                            .build();
                    memberInfoMapper.insert(memberInfoBuild);
                    enterprisePerson.setMemberInfoId(newMemberInfoId);
                }
                if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.CERT.getTaskKindCode(),enterprisePerson,serveCert);
                }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode(),enterprisePerson,personDtl);
                }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.VISA.getTaskKindCode(),enterprisePerson,serveVisa);
                }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode(),enterprisePerson,serveInvite);
                }
                enterprisePersonMapper.updateEnterprisePersonFormExhibitorTaskById(enterprisePerson);
                return;
            }
        }
        //企业Id
        if(enterpriseId == null) return;
        //人员名称
        if(StringUtils.isBlank(fullName)) return;

        paramMap.clear();
        paramMap.put("enterpriseId",enterpriseId);
        paramMap.put("fullName",fullName);
        if(StringUtils.isNotBlank(idCard)){
            paramMap.put("idCard",idCard);
        }else if(StringUtils.isNotBlank(mobile)){
            paramMap.put("mobile",mobile);
        }else if(StringUtils.isNotBlank(email)){
            paramMap.put("email",email);
        }else{
            return;
        }
        List<EnterprisePersonVo> enterprisePersonVoList = enterprisePersonMapper.getList(paramMap);
        if(CollectionUtils.isNotEmpty(enterprisePersonVoList)){
            int personNum = enterprisePersonVoList.size();
            if(personNum <= 1){
                EnterprisePersonVo enterprisePersonVo = enterprisePersonVoList.get(0);
                EnterprisePerson enterprisePersonBuild = EnterprisePerson.builder()
                        .enterprisePersonId(enterprisePersonVo.getEnterprisePersonId())
                        .build();
                Long memberInfoId = enterprisePersonVo.getMemberInfoId();
                if(memberInfoId != null){
                    MemberInfo memberInfo = memberInfoMapper.selectByPrimaryKey(memberInfoId);
                    if(Objects.nonNull(memberInfo)){
                        if(StringUtils.isNotBlank(fullName)) memberInfo.setFullName(fullName);
                        if(StringUtils.isNotBlank(sexName)) memberInfo.setSex(sexName);
                        if(StringUtils.isNotBlank(memberInfo.getIdCard())) memberInfo.setIdCard(idCard);
                        memberInfoMapper.updateByPrimaryKey(memberInfo);
                    }else{
                        Long newMemberInfoId = newIntegerIdDao.snowNextId();
                        MemberInfo memberInfoBuild = MemberInfo.builder()
                                .memberInfoId(newMemberInfoId)
                                .fullName(fullName)
                                .sex(sexName)
                                .idCard(idCard)
                                .createTime(currentNow)
                                .updateTime(currentNow)
                                .build();
                        memberInfoMapper.insert(memberInfoBuild);
                        enterprisePersonBuild.setMemberInfoId(newMemberInfoId);
                    }
                }else{
                    Long newMemberInfoId = newIntegerIdDao.snowNextId();
                    MemberInfo memberInfoBuild = MemberInfo.builder()
                            .memberInfoId(newMemberInfoId)
                            .fullName(fullName)
                            .sex(sexName)
                            .idCard(idCard)
                            .createTime(currentNow)
                            .updateTime(currentNow)
                            .build();
                    memberInfoMapper.insert(memberInfoBuild);
                    enterprisePersonBuild.setMemberInfoId(newMemberInfoId);
                }
                if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.CERT.getTaskKindCode(),enterprisePersonBuild,serveCert);
                }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode(),enterprisePersonBuild,personDtl);
                }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.VISA.getTaskKindCode(),enterprisePersonBuild,serveVisa);
                }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                    buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode(),enterprisePersonBuild,serveInvite);
                }

                enterprisePersonMapper.updateEnterprisePersonFormExhibitorTaskById(enterprisePersonBuild);

                if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                    serveCertDao.updateEnterprisePersonIdById(serveCert.getF_serve_cert_id(),enterprisePersonBuild.getEnterprisePersonId());
                }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                    servePersonDao.updateEnterprisePersonIdById(personDtl.getF_person_dtl_id(),enterprisePersonBuild.getEnterprisePersonId());
                }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                    serveVisaDao.updateEnterprisePersonIdById(serveVisa.getServeVisaId(),enterprisePersonBuild.getEnterprisePersonId());
                }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                    serveInviteDao.updateEnterprisePersonIdById(serveInvite.getF_serve_invite_id(),enterprisePersonBuild.getEnterprisePersonId());
                }
            }else{
                for(EnterprisePersonVo enterprisePersonVo : enterprisePersonVoList){
                    EnterprisePerson enterprisePersonBuild = EnterprisePerson.builder()
                            .enterprisePersonId(enterprisePersonVo.getEnterprisePersonId())
                            .build();
                    Long memberInfoId = enterprisePersonVo.getMemberInfoId();
                    if(memberInfoId != null){
                        MemberInfo memberInfo = memberInfoMapper.selectByPrimaryKey(memberInfoId);
                        if(Objects.nonNull(memberInfo)){
                            if(StringUtils.isNotBlank(fullName)) memberInfo.setFullName(fullName);
                            if(StringUtils.isNotBlank(sexName)) memberInfo.setSex(sexName);
                            if(StringUtils.isNotBlank(memberInfo.getIdCard())) memberInfo.setIdCard(idCard);
                            memberInfoMapper.updateByPrimaryKey(memberInfo);
                        }else{
                            Long newMemberInfoId = newIntegerIdDao.snowNextId();
                            MemberInfo memberInfoBuild = MemberInfo.builder()
                                    .memberInfoId(newMemberInfoId)
                                    .fullName(fullName)
                                    .sex(sexName)
                                    .idCard(idCard)
                                    .createTime(currentNow)
                                    .updateTime(currentNow)
                                    .build();
                            memberInfoMapper.insert(memberInfoBuild);
                            enterprisePersonBuild.setMemberInfoId(newMemberInfoId);
                        }
                    }else{
                        Long newMemberInfoId = newIntegerIdDao.snowNextId();
                        MemberInfo memberInfoBuild = MemberInfo.builder()
                                .memberInfoId(newMemberInfoId)
                                .fullName(fullName)
                                .sex(sexName)
                                .idCard(idCard)
                                .createTime(currentNow)
                                .updateTime(currentNow)
                                .build();
                        memberInfoMapper.insert(memberInfoBuild);
                        enterprisePersonBuild.setMemberInfoId(newMemberInfoId);
                    }
                    if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                        buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.CERT.getTaskKindCode(),enterprisePersonBuild,serveCert);
                    }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                        buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode(),enterprisePersonBuild,personDtl);
                    }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                        buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.VISA.getTaskKindCode(),enterprisePersonBuild,serveVisa);
                    }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                        buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode(),enterprisePersonBuild,serveInvite);
                    }
                    enterprisePersonMapper.updateEnterprisePersonFormExhibitorTaskById(enterprisePersonBuild);
                }
            }
        }else{
            Long newMemberInfoId = newIntegerIdDao.snowNextId();
            MemberInfo memberInfoBuild = MemberInfo.builder()
                    .memberInfoId(newMemberInfoId)
                    .fullName(fullName)
                    .sex(sexName)
                    .idCard(idCard)
                    .createTime(currentNow)
                    .updateTime(currentNow)
                    .build();
            memberInfoMapper.insert(memberInfoBuild);
            Long newEnterprisePeronId = newIntegerIdDao.snowNextId();
            EnterprisePerson enterprisePersonBuild = EnterprisePerson.builder()
                    .enterprisePersonId(newEnterprisePeronId)
                    .enterpriseId(enterpriseId)
                    .memberInfoId(newMemberInfoId)
                    .mainCard(false)
                    .state(0)
                    .bindTime(currentNow)
                    .createTime(currentNow)
                    .updateTime(currentNow)
                    .build();
            if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.CERT.getTaskKindCode(),enterprisePersonBuild,serveCert);
            }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode(),enterprisePersonBuild,personDtl);
            }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.VISA.getTaskKindCode(),enterprisePersonBuild,serveVisa);
            }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                buildEnterprisePersonInfo(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode(),enterprisePersonBuild,serveInvite);
            }

            enterprisePersonMapper.insert(enterprisePersonBuild);

            if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
                serveCertDao.updateEnterprisePersonIdById(serveCert.getF_serve_cert_id(),newEnterprisePeronId);
            }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
                servePersonDao.updateEnterprisePersonIdById(personDtl.getF_person_dtl_id(),newEnterprisePeronId);
            }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
                serveVisaDao.updateEnterprisePersonIdById(serveVisa.getServeVisaId(),newEnterprisePeronId);
            }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
                serveInviteDao.updateEnterprisePersonIdById(serveInvite.getF_serve_invite_id(),newEnterprisePeronId);
            }
        }
    }




    private void buildEnterprisePersonInfo(String taskKindCode,EnterprisePerson enterprisePerson,Object objBean){
        String mobile = null;
        String email = null;
        String position = null;
        String address = null;
        if(ExhibitorTaskTypeEnum.CERT.getTaskKindCode().equals(taskKindCode)){
            ServeCert serveCert = (ServeCert) objBean;
            mobile = serveCert.getMobilePhoneNum();
            email = serveCert.getF_email();
            address = serveCert.getAddress();
            position = serveCert.getF_cert_position();
        }else if(ExhibitorTaskTypeEnum.PERSON.getTaskKindCode().equals(taskKindCode)){
            ServePersonDtl personDtl = (ServePersonDtl) objBean;
            mobile = personDtl.getF_mobile();
            email = personDtl.getF_email();
            address = null;
            position = personDtl.getF_position_id();
        }else if(ExhibitorTaskTypeEnum.VISA.getTaskKindCode().equals(taskKindCode)){
            ServeVisa serveVisa = (ServeVisa) objBean;
            mobile = serveVisa.getMobile();
            email = serveVisa.getEmail();
            address = serveVisa.getCompanyAddress();
            position = serveVisa.getPositionId();
        }else if(ExhibitorTaskTypeEnum.INVITE.getTaskKindCode().equals(taskKindCode)){
            ServeInvite serveInvite = (ServeInvite) objBean;
            mobile = serveInvite.getF_mobile();
            email = serveInvite.getF_email();
            address = serveInvite.getF_exhibitor_address();
            position = serveInvite.getF_position();
        }
        enterprisePerson.setMobile(mobile);
        enterprisePerson.setEmail(email);
        enterprisePerson.setPosition(position);
        enterprisePerson.setAddress(address);
    }
}
