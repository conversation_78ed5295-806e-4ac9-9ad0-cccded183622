package com.eip.async;

import com.eip.common.enums.EventKindEnum;
import com.eip.common.enums.EventOperateTypeEnum;
import com.eip.common.enums.OperateTypeEnum;
import com.eip.common.util.PublicUtil;
import com.eip.facade.sponsor.dto.OperatorDto;
import com.eip.facade.sponsor.entity.ServeCert;
import com.eip.facade.sponsor.entity.TaskDetail;
import com.eip.facade.sponsor.service.EventInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2025-05-24 10:54
 * @DESCRIPTION: 异步记录日志
 */
@Slf4j
@Component
public class OperatorEventInfoAsyncService {

    @Autowired
    private EventInfoService eventInfoService;

    /**
     * 构建日志主体
     * @param taskDetail
     * @return
     */
    public String buildObjName(TaskDetail taskDetail) {
        if(Objects.isNull(taskDetail)) return null;
        String objOperName = taskDetail.getTaskDtlId()+" ";
        if(StringUtils.isNotBlank(taskDetail.getTaskTopic())) objOperName+= taskDetail.getTaskTopic() + " ";
        if(StringUtils.isNotBlank(taskDetail.getCltName())) objOperName+=taskDetail.getCltName()+" ";
        if(StringUtils.isNotBlank(taskDetail.getProjectName())) objOperName+=taskDetail.getProjectName()+" ";
        return objOperName;
    }

    /**
     * 进馆证
     */
    @Async
    public void serveCertEventInfoAsync(String operatorType, OperatorDto operatorDto, TaskDetail taskDetail, ServeCert oldServe_cert, ServeCert newServe_cert){
        if(StringUtils.isBlank(operatorType) || Objects.isNull(operatorDto) || Objects.isNull(taskDetail)) return;
        operatorDto.setF_org_num(taskDetail.getOrgNum());
        Integer operatorId = operatorDto.getOperatorId();
        Integer eventKindId = EventKindEnum.getId(taskDetail.getTaskKindCode());
        if(operatorId!=null &&  operatorId > 0) eventKindId = EventKindEnum.SERVE_CERT_MANAGER.getId();
        String operateTypeName = OperateTypeEnum.MODIFY.getName();

        StringBuilder builder = new StringBuilder();

        if(EventOperateTypeEnum.Add.getCode().equals(operatorType) || EventOperateTypeEnum.Modify.getCode().equals(operatorType)){
            builder.append("工单:"+taskDetail.getTaskTopic()+"("+taskDetail.getTaskDtlId()+")").append("\r\n");
            String certName = StringUtils.isNotBlank(newServe_cert.getF_cert_name()) ? newServe_cert.getF_cert_name() : "空";
            String fullNamePinyin = StringUtils.isNotBlank(newServe_cert.getFullNamePinyin()) ? newServe_cert.getFullNamePinyin() : "空";
            String surnNamePinyin = StringUtils.isNotBlank(newServe_cert.getSurnamePinyin()) ? newServe_cert.getSurnamePinyin() : "空";
            String namePinyin = StringUtils.isNotBlank(newServe_cert.getNamePinyin()) ? newServe_cert.getNamePinyin() : "空";
            String position = StringUtils.isNotBlank(newServe_cert.getF_cert_position()) ? newServe_cert.getF_cert_position() : "空";
            String sex = StringUtils.isNotBlank(newServe_cert.getF_sex()) ? PublicUtil.getSexByState(Integer.valueOf(newServe_cert.getF_sex()),"") : "空";
            String country = StringUtils.isNotBlank(newServe_cert.getCountry()) ? newServe_cert.getCountry() : "空";
            String province = StringUtils.isNotBlank(newServe_cert.getProvince()) ? newServe_cert.getProvince() : "空";
            String city = StringUtils.isNotBlank(newServe_cert.getCity()) ? newServe_cert.getCity() : "空";
            String districtCounty = StringUtils.isNotBlank(newServe_cert.getDistrictCounty()) ? newServe_cert.getDistrictCounty() : "空";
            String address = StringUtils.isNotBlank(newServe_cert.getAddress()) ? newServe_cert.getAddress() : "空";
            String tel = StringUtils.isNotBlank(newServe_cert.getF_tel()) ? newServe_cert.getF_tel() : "空";
            String mobilePhoneNum = StringUtils.isNotBlank(newServe_cert.getMobilePhoneNum()) ? newServe_cert.getMobilePhoneNum() : "空";
            String email = StringUtils.isNotBlank(newServe_cert.getF_email()) ? newServe_cert.getF_email() : "空";
            String passNumber = StringUtils.isNotBlank(newServe_cert.getF_pass_number()) ? newServe_cert.getF_pass_number() : "空";
            String idTypeName = newServe_cert.getF_id_type_name();
            String idCard = StringUtils.isNotBlank(newServe_cert.getF_id_card()) ? newServe_cert.getF_id_card() : "空";

            if(Objects.isNull(oldServe_cert)){
                operateTypeName = OperateTypeEnum.ADD.getName();
                Integer f_serve_cert_id = newServe_cert.getF_serve_cert_id();
                builder.append("数据Id："+f_serve_cert_id).append("\r\n");
                builder.append("姓名："+certName).append("\r\n");builder.append("姓名全拼："+fullNamePinyin).append("\r\n");
                builder.append("姓氏全拼："+surnNamePinyin).append("\r\n");builder.append("名字全拼："+namePinyin).append("\r\n");
                builder.append("性别："+sex).append("\r\n");builder.append("职位："+position).append("\r\n");
                builder.append("国家："+country).append("\r\n");builder.append("省份："+province).append("\r\n");builder.append("城市："+city).append("\r\n");
                builder.append("区县："+districtCounty).append("\r\n");builder.append("地址："+address).append("\r\n");
                builder.append("电话："+tel).append("\r\n");
                builder.append("护照号："+passNumber).append("\r\n");
                builder.append("手机号："+mobilePhoneNum).append("\r\n");builder.append("邮箱："+email).append("\r\n");
                builder.append("证件类型："+idTypeName).append("\r\n");builder.append("证件号码："+idCard).append("\r\n");
            }else{
                Integer f_serve_cert_id = oldServe_cert.getF_serve_cert_id();
                String oldCertName = StringUtils.isNotBlank(oldServe_cert.getF_cert_name()) ? oldServe_cert.getF_cert_name() : "空";
                String oldFullNamePinyin = StringUtils.isNotBlank(oldServe_cert.getFullNamePinyin()) ? oldServe_cert.getFullNamePinyin() : "空";
                String oldSurnNamePinyin = StringUtils.isNotBlank(oldServe_cert.getSurnamePinyin()) ? oldServe_cert.getSurnamePinyin() : "空";
                String oldNamePinyin = StringUtils.isNotBlank(oldServe_cert.getNamePinyin()) ? oldServe_cert.getNamePinyin() : "空";
                String oldPosition = StringUtils.isNotBlank(oldServe_cert.getF_cert_position()) ? oldServe_cert.getF_cert_position() : "空";
                String oldSex = StringUtils.isNotBlank(oldServe_cert.getF_sex()) ? PublicUtil.getSexByState(Integer.valueOf(oldServe_cert.getF_sex()),"") : "空";
                String oldCountry = StringUtils.isNotBlank(oldServe_cert.getCountry()) ? oldServe_cert.getCountry() : "空";
                String oldProvince = StringUtils.isNotBlank(oldServe_cert.getProvince()) ? oldServe_cert.getProvince() : "空";
                String oldCity = StringUtils.isNotBlank(oldServe_cert.getCity()) ? oldServe_cert.getCity() : "空";
                String oldDistrictCounty = StringUtils.isNotBlank(oldServe_cert.getDistrictCounty()) ? oldServe_cert.getDistrictCounty() : "空";
                String oldAddress = StringUtils.isNotBlank(oldServe_cert.getAddress()) ? oldServe_cert.getAddress() : "空";
                String oldTel = StringUtils.isNotBlank(oldServe_cert.getF_tel()) ? oldServe_cert.getF_tel() : "空";
                String oldMobilePhoneNum = StringUtils.isNotBlank(oldServe_cert.getMobilePhoneNum()) ? oldServe_cert.getMobilePhoneNum() : "空";
                String oldEmail = StringUtils.isNotBlank(oldServe_cert.getF_email()) ? oldServe_cert.getF_email() : "空";
                String oldPassNumber = StringUtils.isNotBlank(oldServe_cert.getF_pass_number()) ? oldServe_cert.getF_pass_number() : "空";
                String oldIdTypeName = oldServe_cert.getF_id_type_name();
                String oldIdCard = StringUtils.isNotBlank(oldServe_cert.getF_id_card()) ? oldServe_cert.getF_id_card() : "空";
                builder.append("数据Id："+f_serve_cert_id).append("\r\n");
                if(!oldCertName.equals(certName)) builder.append("姓名由'"+oldCertName).append("'变成'").append(certName).append("'\r\n");
                if(!oldFullNamePinyin.equals(fullNamePinyin)) builder.append("姓名全拼由'"+oldFullNamePinyin).append("'变成'").append(fullNamePinyin).append("\r\n");
                if(!oldSurnNamePinyin.equals(surnNamePinyin)) builder.append("姓氏全拼由'"+oldSurnNamePinyin).append("'变成'").append(surnNamePinyin).append("\r\n");
                if(!oldNamePinyin.equals(namePinyin)) builder.append("名字全拼由'"+oldNamePinyin).append("'变成'").append(namePinyin).append("\r\n");
                if(!oldPosition.equals(position)) builder.append("职位由'"+oldPosition).append("'变成'").append(position).append("\r\n");
                if(!oldSex.equals(sex)) builder.append("性别由'"+oldSex).append("'变成'").append(sex).append("\r\n");
                if(!oldCountry.equals(country)) builder.append("国家由'"+oldCountry).append("'变成'").append(country).append("\r\n");
                if(!oldProvince.equals(province)) builder.append("省份由'"+oldProvince).append("'变成'").append(province).append("\r\n");
                if(!oldCity.equals(city)) builder.append("城市由'"+oldCity).append("'变成'").append(city).append("\r\n");
                if(!oldDistrictCounty.equals(districtCounty)) builder.append("区县由'"+oldDistrictCounty).append("'变成'").append(districtCounty).append("\r\n");
                if(!oldAddress.equals(address)) builder.append("地址由'"+oldAddress).append("'变成'").append(address).append("\r\n");
                if(!oldTel.equals(tel)) builder.append("电话由'"+oldTel).append("'变成'").append(tel).append("\r\n");
                if(!oldMobilePhoneNum.equals(mobilePhoneNum)) builder.append("手机号由'"+oldMobilePhoneNum).append("'变成'").append(mobilePhoneNum).append("\r\n");
                if(!oldEmail.equals(email)) builder.append("邮箱由'"+oldEmail).append("'变成'").append(email).append("\r\n");
                if(!oldPassNumber.equals(passNumber)) builder.append("护照号由'"+oldPassNumber).append("'变成'").append(passNumber).append("\r\n");
                if(!oldIdTypeName.equals(idTypeName)) builder.append("证件类型由'"+oldIdTypeName).append("'变成'").append(idTypeName).append("\r\n");
                if(!oldIdCard.equals(idCard)) builder.append("证件号码由'"+oldIdCard).append("'变成'").append(idCard).append("\r\n");
            }
        }else if(EventOperateTypeEnum.Delete.getCode().equals(operatorType)){
            String memo = "删除进馆证 "+oldServe_cert.getF_cert_name()+"("+oldServe_cert.getF_serve_cert_id()+")"+" 信息成功";
            builder.append(memo).append("\r\n");
            operateTypeName = OperateTypeEnum.DELETE.getName();
        }
        eventInfoService.saveEventOrEventChild(operatorDto,builder.toString(),eventKindId,
                operateTypeName, Objects.nonNull(taskDetail) ? String.valueOf(taskDetail.getTaskDtlId()) : null,buildObjName(taskDetail));

    }

}
