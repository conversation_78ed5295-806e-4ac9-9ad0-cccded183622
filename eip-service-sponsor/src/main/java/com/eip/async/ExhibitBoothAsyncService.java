package com.eip.async;

import com.eip.service.sponsor.orgdao.ExhibitBoothDao;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Future;

/**
 * @USER: zwd
 * @DATE: 2023-12-01 18:54
 * @DESCRIPTION:
 */
@Component
public class ExhibitBoothAsyncService {

    @Autowired
    private ExhibitBoothDao exhibitBoothDao;

    @Async
    public Future<Map<String,Object>> countExhibitBoothPrice(Map<String,Object> paramMap){
        Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(6);
        resultMap =  exhibitBoothDao.countExhibitBoothPrice(paramMap);
        return new AsyncResult<>(resultMap);
    }
}
