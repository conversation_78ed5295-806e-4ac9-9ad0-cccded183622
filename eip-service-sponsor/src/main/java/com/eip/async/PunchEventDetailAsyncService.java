package com.eip.async;

import com.eip.common.util.CollectionUtil;
import com.eip.common.util.ListUtils;
import com.eip.facade.sponsor.entity.ZzyUser;
import com.eip.facade.sponsor.vo.PunchEventDetailVo;
import com.eip.service.sponsor.zzysysmapper.ZzySysUserMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @USER: zwd
 * @DATE: 2023-02-11 14:02
 * @DESCRIPTION:
 */
@Component
public class PunchEventDetailAsyncService {

    @Autowired
    private ZzySysUserMapper zzySysUserMapper;

    /**
     * 查询会员信息
     * @param list
     * @return
     */
    @Async
    public Future<Boolean> queryZzyUser( List<PunchEventDetailVo> list) {
        if (CollectionUtil.isEmpty(list)) return new AsyncResult<>(true);
        int num = 1000;
        List<List<PunchEventDetailVo>> subList = Lists.newArrayList();
        if (list.size() > num) subList = ListUtils.fixedGrouping(list, num);
        else subList.add(list);
        for (List<PunchEventDetailVo> itemList : subList) {
            Set<Integer> collect = itemList.stream().filter(item -> item.getZzyUserId() != null)
                    .map(item -> item.getZzyUserId()).collect(Collectors.toSet());
            if (collect.size() == 0) continue;
            List<ZzyUser> users = zzySysUserMapper.selectUserNameByIds(collect);
            Map<Integer, Object> userMap = Maps.newHashMap();
            users.forEach(item -> userMap.put(item.getF_zzyuser_id(), item));
            itemList.forEach(item -> {
                if (item.getZzyUserId() != null){
                    Object objBean = userMap.get(item.getZzyUserId());
                    if(Objects.nonNull(objBean)){
                        ZzyUser objBean1 = (ZzyUser) objBean;
                        String f_user_name = objBean1.getF_user_name();
                        String f_mobile = objBean1.getF_mobile();
                        String f_email = objBean1.getF_email();
                        String zzyUserName = StringUtils.isNotBlank(f_mobile) ? f_mobile : StringUtils.isNotBlank(f_email) ? f_email : f_user_name;
                        item.setZzyUserName(zzyUserName);
//                        if(StringUtils.isBlank(item.getBuyerName())){
//                            item.setBuyerName(objBean1.getLinkman());
//                        }
//                        if(StringUtils.isBlank(item.getRegNum())){
//                            String regNum = StringUtils.isNotBlank(objBean1.getF_mobile()) ? objBean1.getF_mobile() : StringUtils.isNotBlank(objBean1.getF_email()) ? objBean1.getF_email() : "";
//                            item.setRegNum(regNum);
//                        }
                    }
                }
            });
        }
        return new AsyncResult<>(true);
    }

}
