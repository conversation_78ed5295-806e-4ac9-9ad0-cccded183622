package com.eip.async;

import com.alibaba.fastjson.JSON;
import com.eip.common.constant.CommonConstant;
import com.eip.common.enums.IntegralOperatorNumEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.http.HttpClientHelper;
import com.eip.facade.sponsor.entity.BuyerReg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2025-03-17 8:46
 * @DESCRIPTION: 会员异步发放积分
 */
@Slf4j
@Component
public class ZzyUserIntegralAsync {

    /**
     * 触发积分
     */
    @Async
    public void sendIntegral(Map<String,Object> paramMap, BuyerReg buyerReg){
        try {
            if(Objects.nonNull(buyerReg)){
                paramMap.put("buyerRegId",buyerReg.getBuyerId());
                paramMap.put("projectId",buyerReg.getProjectId());
                paramMap.put("zzyUserId",buyerReg.getZzyuserId());
            }
            String url = paramMap.get("requestIntegralUrl") + CommonConstant.SEND_INTEGRAL_URL;
            HttpClientHelper.sendIntegralHttpPost(url,paramMap);
        }catch (Exception e){
            Integer detailLogOperatorType = (Integer) paramMap.get("detailLogOperatorType");
            if(detailLogOperatorType!=null){
                if(1 == detailLogOperatorType){
                    if(IntegralOperatorNumEnum.RECEIVE_OF_CONFERENCE_CREDENTIALS.getCode().equals(paramMap.get("operatorTypeCode"))){
                        log.info("领用证件，触发积分规则异常:{}",e);
                    }else if(IntegralOperatorNumEnum.CONVERT_OF_CONFERENCE_CREDENTIALS.getCode().equals(paramMap.get("operatorTypeCode"))){
                        log.info("证件兑换，触发积分规则异常:{}",e);
                    }else if(IntegralOperatorNumEnum.PUNCH_EVENT.getCode().equals(paramMap.get("operatorTypeCode"))){
                        log.info("会员打卡点打卡或打卡结束兑奖，触发积分规则异常:{}",e);
                    }
                }else if(3 == detailLogOperatorType){
                    if(IntegralOperatorNumEnum.BUY_CONFERENCE_DOCUMENT.getCode().equals(paramMap.get("operatorTypeCode"))){
                        log.error("线上支付订单退款，回退积分异常：{}",e);
                    }else{
                        log.info("退领证件，触发积分规则异常:{}",e);
                    }
                }
            }else{
                log.info("证件领用或退领、证件兑换，触发积分规则异常:{}",e);
            }
        }
    }

    /**
     * 发放积分商品
     * @param paramMap
     */
    @Async
    public void sendIntegralGoods(Map<String,Object> paramMap){
        try {
            String url = paramMap.get("requestIntegralUrl") + CommonConstant.FREE_SEND_GOODS_URL;
            HttpClientHelper.sendIntegralHttpPost(url, paramMap);
        }catch (Exception e){
            e.printStackTrace();
            log.error("发放积分商品异常：{}",e);
        }
    }
    /**
     * 回退积分商品 同步
     * @param paramMap
     */
    public AjaxResponse syncRollBackIntegralGoods(Map<String,Object> paramMap){
        try {
            String url = paramMap.get("requestIntegralUrl") + CommonConstant.rollback_integral_GOODS_URL;
            String respMsg = HttpClientHelper.sendIntegralHttpPost(url, paramMap);
            if(StringUtils.isNotBlank(respMsg)){
                return JSON.parseObject(respMsg, AjaxResponse.class);
            }
        }catch (Exception e){
            log.error("回退积分商品异常：{}",e);
            return AjaxResponse.failure("回退积分商品失败");
        }
        return AjaxResponse.success();
    }

    /**
     * 回退积分商品 异步
     * @param paramMap
     */
    @Async
    public AjaxResponse rollBackIntegralGoods(Map<String,Object> paramMap){
        try {
            String url = paramMap.get("requestIntegralUrl") + CommonConstant.rollback_integral_GOODS_URL;
            String respMsg = HttpClientHelper.sendIntegralHttpPost(url, paramMap);
            if(StringUtils.isNotBlank(respMsg)){
                return JSON.parseObject(respMsg, AjaxResponse.class);
            }
        }catch (Exception e){
            log.error("回退积分商品异常：{}",e);
            return AjaxResponse.failure("回退积分商品失败");
        }
        return AjaxResponse.success();
    }


    /**
     * 重新计算积分商品创建核销记录
     * @param paramMap
     */
    @Async
    public void recalculateGoodsConfirmLog(Map<String,Object> paramMap){
        try {
            String url = paramMap.get("requestIntegralUrl") + CommonConstant.RECALCULATE_GOODS_CONFIRMLOG_CHECK_URL;
            HttpClientHelper.sendIntegralHttpPost(url, paramMap);
        }catch (Exception e){
            e.printStackTrace();
            log.error("重新计算证件所属积分商品并创建核销记录异常:{}",e);
        }
    }


}
