<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration >
    <classPathEntry  
        location="C:/postgresql-42.2.2.jar" /> 
	<context id="context1" >
  		<commentGenerator>  
  			<property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->  
        	<property name="suppressAllComments" value="true"/>  
    	</commentGenerator>
    	<!-- 数据库链接URL、用户名、密码 -->  
	    <!-- <jdbcConnection driverClass="com.microsoft.sqlserver.jdbc.SQLServerDriver" connectionURL="jdbc:sqlserver://*************:6976;DatabaseName=PMSDataSever" userId="sa" password="power" />
	     -->
	    <jdbcConnection driverClass="org.postgresql.Driver" connectionURL="**********************************************" userId="postgres" password="214216" />
	    <!-- 生成模型的包名和位置 -->
	    <javaModelGenerator targetPackage="com.eip.facade.sponsor.entity" targetProject="eip-facade-sponsor/src/main/java">
	    	<property name="enableSubPackages" value="true"/>    
            <property name="trimStrings" value="true"/>   
        </javaModelGenerator>
	    <!-- 生成的映射文件报名和位置 -->
	    <sqlMapGenerator targetPackage="mybatis.orgmapper" targetProject="eip-service-sponsor/src/main/resources">
	     	<property name="enableSubPackages" value="true"/>    
        </sqlMapGenerator> 
        
	    <!-- 生成DAO的包名和位置 -->
	    <javaClientGenerator type="XMLMAPPER" targetPackage="com.eip.service.sponsor.orgdao" targetProject="eip-service-sponsor/src/main/java">    
            <property name="enableSubPackages" value="true"/>    
        </javaClientGenerator>
	    <!-- 要生成的那些表(更改tableName 和domainObjectName 就可以了 columnOverride可以省略不写，这里是为了重新指定命名) --> 
	    <table tableName="t_proj_task_relation" domainObjectName="Proj_task_relation" enableCountByExample="false" enableUpdateByExample="false"
	     enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true" /> 
	    </table>
	</context>
</generatorConfiguration>