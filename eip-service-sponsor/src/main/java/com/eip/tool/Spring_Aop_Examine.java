package com.eip.tool;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.eip.facade.sponsor.entity.Examine;
import com.eip.service.sponsor.biz.CatalogBiz;
import com.eip.service.sponsor.biz.PersonBiz;
import com.eip.service.sponsor.orgdao.CatalogueMapper;
import com.eip.service.sponsor.orgdao.ExamineMapper;
import com.eip.service.sponsor.orgdao.NewIntegerIdDao;
@Component
@Aspect
public class Spring_Aop_Examine {
	
	 @Autowired
	    private ExamineMapper eMapper;
	    @Autowired
	    private  NewIntegerIdDao nIdDao;
	    @Autowired
	    private CatalogBiz catalogBiz;
	    @Autowired
	    private PersonBiz personBiz;
	    
	/*@Pointcut("execution(*   com.eip.facade.sponsor.service.ExamineServe.examine(Boolean,int,int,String )) && args(keyBool,f_project_id,f_client_id,serve_type) ) ")  //examine
	public void ExamineServe(Boolean keyBool,Integer f_project_id, Integer f_client_id,String serve_type) {
		
	};
	
	@AfterReturning("ExamineServe(keyBool,f_project_id,f_client_id,serve_type)")
	public void Star(Boolean keyBool,Integer f_project_id, Integer f_client_id,String serve_type) {
		System.err.println("Star...");
		System.out.println("1:"+keyBool +"2:"+f_project_id+"3:"+f_client_id+"4:"+serve_type);
		System.out.println(new Date().toString());
	}*/
	
	/*@Pointcut("execution(*  com.eip.service.sponsor.biz.ExamineBiz.examine(Boolean,int,int,String)) && args(keyBool,f_project_id,f_client_id,serve_type) ")  //examine
	public void Examine(Boolean keyBool,Integer f_project_id, Integer f_client_id,String serve_type) {
		
	};
	
	@AfterReturning("Examine(keyBool,f_project_id,f_client_id,serve_type)")
	public void testStar(Boolean keyBool,Integer f_project_id, Integer f_client_id,String serve_type) {
		System.out.println("1111111");
		System.out.println("2222222");
		System.out.println("3333333");
		System.out.println(f_client_id);
		System.out.println(f_project_id);
	}
	*/
	
   
    @AfterReturning(returning = "entity",value = "execution(* com.eip.service.sponsor.biz.ExamineBiz.examine(..))")
    public void Examine(JoinPoint joinPoint,Object entity){
        System.out.println("这里是目标方法执行完并成功返回结果 正常结束后才执行");
        System.out.println("方法的返回结果为"+entity);
        System.out.println("目标方法内的参数为"+ Arrays.asList(joinPoint.getArgs()));
        int res = 0;
        int eid=0;
        res = (Integer) entity;
        if (res==1) {
        	 List<Object> args  = Arrays.asList(joinPoint.getArgs());
        	 Boolean node = (Boolean) args.get(0);
        	 Integer pid =  (Integer) args.get(1);
             Integer sid= (Integer) args.get(2);
             String  type = (String) args.get(3);
              eid = nIdDao.getNewIntegerId("f_examine_id", false);
             System.out.println("["+pid+sid+node+type+"]");
             com.eip.facade.sponsor.entity.Examine examine = new  Examine();
             examine.setF_examine_id(eid);
             examine.setF_serve_id(sid);
             examine.setF_project_id(pid);
             if(node) {
            	if(type.equals("f_serve_catalogue_id")) {
            		examine.setF_serve_type("会刊服务");
                	examine.setF_operation_type("审批服务");
            	}else if(type.equals("f_serve_person_id")) {
            		examine.setF_serve_type("人员服务");
                	examine.setF_operation_type("审批服务");
            	}
            	
			}else{
				if(type.equals("f_serve_catalogue_id")) {
            		examine.setF_serve_type("会刊服务");
                	examine.setF_operation_type("撤审服务");
            	}else if(type.equals("f_serve_person_id")) {
            		examine.setF_serve_type("人员服务");
                	examine.setF_operation_type("撤审服务");
            	}
				
			}
             
             examine.setF_time(new Date());
             int resinsert =  eMapper.insertSelective(examine);
             if (resinsert>0) {
				nIdDao.getNewIntegerId("f_examine_id", true);
				if(type.equals("f_serve_catalogue_id")) {
					System.out.println("绑定"+eid+sid);
					catalogBiz.update_examine_id(eid, sid);
				}else if(type.equals("f_serve_person_id")) {
					System.out.println("绑定"+eid+sid);
					personBiz.update_examine_id(eid, sid);
				}
			}
             System.out.println("插入记录：" + resinsert);
        }
    }
    @AfterReturning(returning = "entity",value = "execution(* com.eip.service.sponsor.biz.ExamineBiz.examine_reject(..))")
    public void Examine_Reject(JoinPoint joinPoint,Object entity){
        System.out.println("这里是目标方法执行完并成功返回结果 正常结束后才执行  驳回申请");
        System.out.println("方法的返回结果为"+entity);
        System.out.println("目标方法内的参数为"+ Arrays.asList(joinPoint.getArgs()));
        int res = 0;
        int eid=0;
        res = (Integer) entity;
        if (res==1) {
        	 List<Object> args  = Arrays.asList(joinPoint.getArgs());
        	 Integer pid =  (Integer) args.get(0);
             Integer sid= (Integer) args.get(1);
             String reson = (String) args.get(2);
             String  type = (String) args.get(3);
              eid = nIdDao.getNewIntegerId("f_examine_id", false);
             System.out.println("["+pid+sid+reson+type+"]");
             com.eip.facade.sponsor.entity.Examine examine = new  Examine();
             examine.setF_examine_id(eid);
             examine.setF_serve_id(sid);
             examine.setF_project_id(pid);
             examine.setF_reason(reson);
             if(type.equals("f_serve_catalogue_id")) {
            	examine.setF_serve_type("会刊服务");
            	examine.setF_operation_type("驳回服务");
			 }else if(type.equals("f_serve_person_id")) {
	            examine.setF_serve_type("人员服务");
	            examine.setF_operation_type("驳回服务");
			 }
	             
             
             examine.setF_time(new Date());
             int resinsert =  eMapper.insertSelective(examine);
             if (resinsert>0) {
				nIdDao.getNewIntegerId("f_examine_id", true);
				if(type.equals("f_serve_catalogue_id")) {
					catalogBiz.update_examine_id(eid, sid);
				}else if(type.equals("f_serve_person_id")) {
					personBiz.update_examine_id(eid, sid);
				}
				
			}
             System.out.println("插入记录：" + resinsert);
        }
        
        
    }
    
	
}
