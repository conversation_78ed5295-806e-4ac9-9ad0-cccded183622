package com.eip.facade.sponsor.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.eip.common.enums.ZzyUserTypeEnum;
import com.eip.common.service.impl.BaseServiceImpl;
import com.eip.common.util.*;
import com.eip.common.util.onlyId.OnlyNumberUtil;
import com.eip.facade.sponsor.entity.*;
import com.eip.facade.sponsor.service.AwokeService;
import com.eip.facade.sponsor.service.ProjectService;
import com.eip.facade.sponsor.service.ServeBoothService;
import com.eip.facade.sponsor.vo.AwokeVo;
import com.eip.service.sponsor.orgdao.AwokeMapper;
import com.eip.service.sponsor.orgdao.OperatorDao;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-01-02 11:25
 */
@Service
public class AwokeServiceImpl extends BaseServiceImpl<Awoke> implements AwokeService {
    @Autowired
    private AwokeMapper awokeMapper;
    @Autowired
    private ServeBoothService serveBoothService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private OperatorDao operatorDao;

    @Override
    public PageInfo<AwokeVo> selectByAwokeTimeAfterNow(Integer operatorId, Integer page, Integer pageSize) {
        PageHelper.startPage(page, pageSize);
        List<AwokeVo> awokes = awokeMapper.selectByAwokeTimeAfterNow(operatorId.toString());
        if(CollectionUtils.isNotEmpty(awokes) && awokes.size() > 0){
            awokes.forEach(awoke -> {
                String tableName = awoke.getTableName();
                if (StringUtils.isNotBlank(tableName)) {
                    switch (tableName) {
                        //展商参展提醒
                        case "t_serve_booth":
                            ServeBooth serveBooth = serveBoothService.selectById(Integer.parseInt(awoke.getRecordCode()));
                            if(Objects.nonNull(serveBooth)){
                                Project project = projectService.selectByProjectId(serveBooth.getProjectId(), null);
                                if(Objects.nonNull(project)){
                                    awoke.setExhibitCode(project.getExhibitCode());
                                    awoke.setProjectId(project.getProjectId());
                                    break;
                                }
                            }
                        default:

                    }
                }
            });
        }
        return new PageInfo<>(awokes);
    }

    @Override
    public void saveByClientTouch(Integer touchId, Operator operator, Integer[] reminded, Awoke awoke) {
        //删除之前的提醒
        Awoke awoke1 = new Awoke();
        awoke1.setTableName("clientTouch");
        awoke1.setCreateUser(operator.getOperatorId().toString());
        awoke1.setAwokeType(2+"");
        awoke1.setRecordCode(touchId.toString());
        awokeMapper.delete(awoke1);
        if (reminded==null) return;
        for (Integer integer : reminded) {
            Long aLong = OnlyNumberUtil.nextId();
            awoke.setId(aLong);
            awoke.setAwokeId(aLong);
            awoke.setOrgNum(operator.getF_org_num());
            awoke.setAwokeType(2+"");
            awoke.setAwokeFrom("联系记录提醒");
            awoke.setFromId(operator.getOperatorId().toString());
            awoke.setCreateUser(operator.getOperatorId().toString());
            awoke.setCreateTime(new Date());
            awoke.setAwokeUser(integer.toString());
            awoke.setHaveSend(false);
            awoke.setHaveRead(false);
            awoke.setAwokeSummary("新增一条联系记录提醒");
            awoke.setAwokeDetail("新增一条联系记录提醒");
            awoke.setAwokeDone(false);
            awoke.setRecordCode(touchId.toString());
            awoke.setTableName("clientTouch");
            awokeMapper.insertSelective(awoke);
        }
    }

    /**
     *
     * @param boothPreorder 预订单信息
     * @param clientType 客户账号类型  OPERATOR 本公司账号
     * @param existOwnerFlag 是否存在业绩归属人  true 存在  false 不存在
     * @return
     */
    @Override
    public int sendBoothPreOrderAwoke(BoothPreorder boothPreorder,String clientType,Boolean existOwnerFlag) {
        Awoke awoke = packageCommonAwoke();
        awoke.setOrgNum(boothPreorder.getOrgNum());
        awoke.setAwokeType(String.valueOf(14));
        awoke.setAwokeFrom(PublicUtil.getPushWxMsgAttrByType(14));
        if(StringUtils.isNotBlank(clientType) && ZzyUserTypeEnum.OPERATOR.getCode().equals(clientType)){
            awoke.setFromId(String.valueOf(boothPreorder.getCreateOperId()));
            awoke.setCreateUser(String.valueOf(boothPreorder.getCreateOperId()));
        }
        awoke.setAwokeUser(String.valueOf(boothPreorder.getOwnerId()));
        awoke.setPushWxState(1);
        awoke.setSubUrl("/remind/details/"+awoke.getAwokeId()+"/"+14);
        awoke.setSubject(PublicUtil.getPushWxMsgAttrByType(14));
        awoke.setRecordCode(boothPreorder.getServeBoothId().toString());
        awoke.setTableName("t_serve_booth");
        String detail = null;

        String projectName = null;
        Project project = projectService.getById(boothPreorder.getProjectId());
        if(Objects.nonNull(project)){
            projectName = project.getProjectName();
        }
        if(StringUtils.isNotBlank(boothPreorder.getCompanyName())){
            if(existOwnerFlag){
//                detail=StringUtils.isBlank(projectName) ? "" : projectName + "，" + boothPreorder.getCompanyName() + " 展商已预订展位";
                detail=boothPreorder.getCompanyName()+"已订展";
            }else{
//                detail=StringUtils.isBlank(projectName) ? "" : projectName + "，" + boothPreorder.getCompanyName() + " 展商已预订展位,未指派业绩归属人";
                detail=boothPreorder.getCompanyName() + "已订展,未指派业绩归属人";
            }
        }else{
            if(existOwnerFlag){
                detail=StringUtils.isBlank(projectName) ? "" : "，"+projectName + " 展商已订展";
            }else{
                detail=StringUtils.isBlank(projectName) ? "" : "，"+projectName + " 展商已订展,未指派业绩归属人";
            }
        }
        awoke.setAwokeSummary(detail);
        awoke.setPushAwokeDetail(detail);

        StringBuilder awokeDetail = new StringBuilder();
        awokeDetail.append("<p>项目名称：").append(StringUtils.isBlank(projectName) ? "" : projectName).append("<br>")
                .append("公司名称：").append(StringUtils.isBlank(boothPreorder.getCompanyName()) ? "" : boothPreorder.getCompanyName()).append("<br>")
                .append("客户ID：").append(boothPreorder.getClientId()).append("<br>");
        if(StringUtils.isNotBlank(clientType) && ZzyUserTypeEnum.OPERATOR.getCode().equals(clientType)){
            String empName = operatorDao.getEmpNameByOperId(boothPreorder.getCreateOperId());
            if(StringUtils.isNotBlank(empName)){
                awokeDetail.append("输入员：").append(empName);
            }
        }
        awoke.setAwokeDetail(awokeDetail.toString());
        if(existOwnerFlag){
            awoke.setMessage(StringUtils.isBlank(boothPreorder.getCompanyName()) ? "" : boothPreorder.getCompanyName()+" 已订展，请您及时跟进");
        }else{
            awoke.setMessage(StringUtils.isBlank(boothPreorder.getCompanyName()) ? "" : boothPreorder.getCompanyName()+" 已订展，未指派业绩归属人，请及时跟进");
        }
        return  awokeMapper.insertSelective(awoke);
    }

    /**
     * 提取公共的提醒字段设置
     * @return
     */
    private Awoke packageCommonAwoke(){
        Awoke awoke = new Awoke();
        Calendar calendar = Calendar.getInstance();
        Long aLong = OnlyNumberUtil.nextId();
        awoke.setId(aLong);
        awoke.setAwokeId(aLong);
        awoke.setBeforeMinutes(0);
        awoke.setHaveSend(false);
        awoke.setHaveRead(false);
        awoke.setAwokeDone(false);
        awoke.setCreateTime(calendar.getTime());
        awoke.setAwokeTime(calendar.getTime());
        return awoke;
    }


}
