package com.eip.facade.sponsor.service.impl;

import com.eip.facade.sponsor.entity.CustomizedField;
import com.eip.facade.sponsor.service.OtherService;
import com.eip.service.sponsor.biz.OtherBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("otherService")
public class OtherServiceImpl implements OtherService{

	@Autowired
	private OtherBiz otherBiz;
	
	@Override
	public List<CustomizedField> getAll(String f_form_name, Integer f_project_id) {
		return otherBiz.getAll(f_form_name,f_project_id);
	}

	@Override
	public int insertCustomizedFieldList(Integer projectId,String formName,List<CustomizedField> list) {
		return otherBiz.insertCustomizedFieldList(projectId,formName,list);
	}

	@Override
	public String getSysSetting(String settingCode) {
		return otherBiz.getSysSetting(settingCode);
	}

	@Override
	public void insertCustomizedFieldListDelOld(Integer projectId, List<CustomizedField> list) {
	  	otherBiz.insertCustomizedFieldListDelOld(projectId,list);
	}

	@Override
	public List<CustomizedField> getList(CustomizedField customizedField) {
		return otherBiz.getList(customizedField);
	}

	@Override
	public void saveListByTask(Integer orgNum,Integer taskId,Integer taskKindOrgId, List<CustomizedField> customizedFields) {
		otherBiz.saveListByTask(orgNum,taskId,taskKindOrgId,customizedFields);
	}

	@Override
	public String getExhibitCodePid(Integer proId) {
		return otherBiz.getExhibitCodePid(proId);
	}

	@Override
	public List<CustomizedField> getProjectTaskCustomField(CustomizedField customizedField) {
		return otherBiz.getProjectTaskCustomField(customizedField);
	}
}
