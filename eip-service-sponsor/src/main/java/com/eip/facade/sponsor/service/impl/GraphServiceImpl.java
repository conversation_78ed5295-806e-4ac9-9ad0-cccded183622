package com.eip.facade.sponsor.service.impl;

import com.eip.common.exception.BusinessException;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.GraphDto;
import com.eip.facade.sponsor.dto.OperatorDto;
import com.eip.facade.sponsor.entity.Graph;
import com.eip.facade.sponsor.service.GraphService;
import com.eip.facade.sponsor.vo.GraphBoothBeanVo;
import com.eip.service.sponsor.biz.GraphBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("graphService")
public class GraphServiceImpl implements GraphService {

	@Autowired
	private GraphBiz graphBiz;
	
	@Override
	public List<Graph> selectBySectionMapId(Map<String,Object> hashMap) {
		return graphBiz.selectBySectionMapId(hashMap);
	}

	@Override
	public List<Graph> selectSponsorMapBooth(GraphBoothBeanVo graphBoothBeanVo) {
		return graphBiz.selectSponsorMapBooth(graphBoothBeanVo);
	}

	@Override
	public List<Graph> selectGraphHadCode(Map<String, Object> hashMap) {
		return graphBiz.selectGraphHadCode(hashMap);
	}

	@Override
	public Integer countGraphHadCodeByType(int sectionMapId, String type) {
		return graphBiz.countGraphHadCodeByType(sectionMapId,type);
	}

	@Override
	public List<Graph> selectSections(Integer sectionMapId,String exhibitCode) {
		return graphBiz.selectSections(sectionMapId,exhibitCode);
	}

	@Override
	public int insert(List<Graph> list) {
		return graphBiz.insert(list);
	}

	@Override
	public int update(Graph graph) {
		return graphBiz.update(graph);
	}

	@Override
	public AjaxResponse deleteByGraphId(GraphDto graphDto, OperatorDto operatorDto) {
		return graphBiz.deleteByGraphId(graphDto,operatorDto);
	}

	@Override
	public AjaxResponse batchDelete(List<GraphDto> graphDtos, OperatorDto operatorDto) throws BusinessException {
		return graphBiz.batchDelete(graphDtos,operatorDto);
	}

	@Override
	public AjaxResponse updateDealWithExhibitionGraphics(GraphDto graphDto, OperatorDto operatorDto) {
		return graphBiz.updateDealWithExhibitionGraphics(graphDto,operatorDto);
	}

	@Override
	public List<Graph> selectWithServeBooth(GraphBoothBeanVo graphBoothBeanVo) {
		return graphBiz.selectWithServeBooth(graphBoothBeanVo);
	}

	@Override
	public List<Graph> selectMoreBySectionMapId(GraphBoothBeanVo graphBoothBeanVo) {
		return graphBiz.selectMoreBySectionMapId(graphBoothBeanVo);
	}

	@Override
	public int selectCountBySectionMapId(Integer sectionMapId) {
		return graphBiz.selectCountBySectionMapId(sectionMapId);
	}

	@Override
	public AjaxResponse selectGraphHadCodeChange(GraphBoothBeanVo graphBoothBeanVo, OperatorDto operatorDto) {
		return graphBiz.selectGraphHadCodeChange(graphBoothBeanVo,operatorDto);
	}

	@Override
	public int batchDelGraph(List<Integer> graphIdList) {
		return graphBiz.batchDelGraph(graphIdList);
	}

	@Override
	public int addGraph(Graph newGraph) {
		return graphBiz.addGraph(newGraph);
	}


	@Override
	public AjaxResponse batchAddGraphics(List<Graph> graphs, OperatorDto operatorDto) {
		return graphBiz.batchAddGraphics(graphs,operatorDto);
	}

	@Override
	public int insertGraph(Graph graph, OperatorDto operatorDto) {
		return graphBiz.insertGraph(graph,operatorDto);
	}

	@Override
	public Graph selectGraphById(Integer graphId) {
		return graphBiz.selectGraphById(graphId);
	}

	@Override
	public AjaxResponse insertOrUpdate(Graph graph,OperatorDto operatorDto) throws BusinessException {
		return graphBiz.insertOrUpdate(graph,operatorDto);
	}
}
