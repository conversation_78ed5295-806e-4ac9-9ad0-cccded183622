package com.eip.facade.sponsor.service.impl;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.TaskDtlDto;
import com.eip.facade.sponsor.entity.Task;
import com.eip.facade.sponsor.entity.TaskBigKind;
import com.eip.facade.sponsor.entity.TaskCountA;
import com.eip.facade.sponsor.entity.TaskList;
import com.eip.facade.sponsor.service.TaskService;
import com.eip.service.sponsor.biz.TaskBiz;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("taskService")
public class TaskServiceImpl implements TaskService {

	@Autowired
	private TaskBiz taskBiz;
	
	@Override
	public int count(Boolean key, Integer createOperId, Integer taskState, String taskKindCode, Integer projectId, int f_org_num) {
		return taskBiz.count(key, createOperId, taskState,taskKindCode, projectId,  f_org_num);
	}

	@Override
	public int getSysId(String fieldCode, Boolean increaseMode) {
		return taskBiz.getSysId(fieldCode, increaseMode);
	}

	@Override
	public List<Task> select(PageParam pageParam, Boolean key, Integer createOperId, Integer taskState, String taskKindCode, Integer projectId, int f_org_num) {
		return taskBiz.select(pageParam, key, createOperId, taskState,taskKindCode, projectId,  f_org_num);
	}


	@Override
	public int insertTask(Task task) {
		return taskBiz.insertTask(task);
	}

	@Override
	public int insert(Task task) {
		return taskBiz.insert(task);
	}

	@Override
	public int update(Task task) {
		return taskBiz.update(task);
	}

	@Override
	public int delete(Integer taskId) {
		return taskBiz.delete(taskId);
	}

	@Override
	public int deleteBatch(List<Task> list) {
		return taskBiz.deleteBatch(list);
	}

	@Override
	public List<TaskCountA> countGroupByTaskKindCode(Integer projectId) {
		return taskBiz.countGroupByTaskKindCode(projectId);
	}

	@Override
	public List<Task> selectByProjectId(Integer projectId) {
		return taskBiz.selectByProjectId(projectId);
	}

	@Override
	public List<Task> selectByProjectIdOrderBy(Integer projectId) {
		return taskBiz.selectByProjectIdOrderBy(projectId);
	}

	@Override
	public Task selectByTaskId(Integer taskId) {
		return taskBiz.selectByTaskId(taskId);
	}

	@Override
	public int selectTaskIdByProjectIdAndTaskKindCode(int projectId, String taskKindCode) {
		return taskBiz.selectTaskIdByProjectIdAndTaskKindCode(projectId, taskKindCode);
	}

	@Override
	public int deleteByProjectId(int projectId) {
		return taskBiz.deleteByProjectId(projectId);
	}

	@Override
	public List<TaskBigKind> selectByProjectId2(Integer projectId) {
		return taskBiz.selectByProjectId2(projectId);
	}

	@Override
	public List<Task> selectByProjectId2AndTaskKindCode(Map<String, Object> paramMap) {
		return taskBiz.selectByProjectId2AndTaskKindCode(paramMap);
	}

	@Override
	public int updateBatch(List<Task> list) {
		return taskBiz.updateBatch(list);
	}

	@Override
	public int countCRM(Task task) {
		
		return taskBiz.countCRM(task);
	}

	@Override
	public List<Task> selectCRM(PageParam pageParam, Task task) {
		
		return taskBiz.selectCRM(pageParam, task);
	}

	@Override
	public int insertCRM(Task task) {
		
		return taskBiz.insertCRM(task);
	}

	@Override
	public int updateCRM(Task task) {
		
		return taskBiz.updateCRM(task);
	}

	@Override
	public int batchDeleteTask(TaskList taskList) {
		return taskBiz.batchDeleteTask(taskList);
	}

	@Override
	public int batchUpdateTask(TaskDtlDto taskDtlDto) {
		return taskBiz.batchUpdateTask(taskDtlDto);
	}

	@Override
	public int batchUpdateTaskDate(TaskDtlDto taskDtlDto) {
		return taskBiz.batchUpdateTaskDate(taskDtlDto);
	}

	@Override
	public AjaxResponse enableTaskCustomSet(TaskDtlDto taskDtlDto) {
		return taskBiz.enableTaskCustomSet(taskDtlDto);
	}

	@Override
	public List<Task> getTaskList(TaskDtlDto taskDtlDto) {
		return taskBiz.getTaskList(taskDtlDto);
	}

	@Override
	public PageInfo<Task> getTaskPage(PageParam pageParam, TaskDtlDto taskDtlDto) {
		return taskBiz.getTaskPage(pageParam,taskDtlDto);
	}

	@Override
	public int batchSaveTask(TaskDtlDto taskDtlDto) {
		return taskBiz.batchSaveTask(taskDtlDto);
	}

	@Override
	public int updateTaskById(Task task) {
		return taskBiz.updateTaskById(task);
	}
}
