package com.eip.util;

import com.eip.common.util.ApplicationContextHolder;
import com.eip.common.util.ReStrUtils;
import com.eip.facade.sponsor.entity.City;
import com.eip.facade.sponsor.entity.ClientKeyword;
import com.eip.service.sponsor.orgdao.CityDao;
import com.eip.service.sponsor.orgdao.ClientKeywordMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * 清除公司名称省市区
 *
 * @author: ZzzHhYyy
 * @Date: 2020-05-15 16:54
 */
public class ClearClientNameKeywordsUtils {
    private static final JdbcTemplate BEAN = ApplicationContextHolder.getBean(JdbcTemplate.class);
    private static final CityDao CITY_DAO = ApplicationContextHolder.getBean(CityDao.class);
    private static final ClientKeywordMapper Client_Keyword_Mapper = ApplicationContextHolder.getBean(ClientKeywordMapper.class);


    /**
     * 创建去模糊词汇函数
     *
     * @param clientName 公司名称  》》》浙江温州新特软件开发有限公司
     * @param fKeyLevel  l>=2是严格排重 l=1是模糊判别
     * @return
     */
    public static String clearClientNameKeywordsElse(String clientName, Integer fKeyLevel) {
    	if(StringUtils.isBlank(clientName))return clientName;
    	if(fKeyLevel==null)fKeyLevel = 1;
        Object[] args = new Object[]{fKeyLevel};
        List<String> fNames = BEAN.queryForList("select f_name from t_client_keyword where f_key_level>=?", args, String.class);
        for (String fName : fNames) {
            clientName = clientName.replace(fName, "");
        }
		if (fKeyLevel == 2) clientName = ReStrUtils.reParenthesis(clientName);
        return clientName;
    }

    /**
     * 创建去模糊词汇函数
     *
     * @param clientName 公司名称  》》》上海新特软件开发有限公司温州分公司
     * @return
     */
    public static City getAddressByClientName(String clientName) {
        //Preconditions.checkNotNull(clientName, "clientName 不能为空!");
        if(StringUtils.isBlank(clientName))return new City();
        // 取前头的地级市，防止取到地址中间的路名是别省市的
        String theKeyName = clearClientNameKeywordsElse(clientName, 2);
        String sql = "select f_province,f_city_name,'' As f_areaname,f_postcode,f_area_code,f_city_level from t_city " +
                "    where f_city_level=1  and ('" + clientName + "' like '%'||f_city_name||'分公司' or '" + theKeyName + "' like '%' ||f_city_name || '办事处')" +
                "  union all" +
                "  select f_province,f_city_name,'' As F_AreaName,f_postcode,f_area_code,f_city_level from t_city " +
                "    where f_city_level=1  and  '" + theKeyName + "' like f_city_name||'%' order by  f_area_code";
        List<City> cities = CITY_DAO.execute(sql);
        if (cities.size() >= 1) {// --地级市匹配，则判断地级市下面的区域
            City city = cities.get(0);
            sql = "  select f_city_name,f_postcode,f_area_code from t_city where" +
                    " coalesce(f_city_level,0)=0 and f_area_code||Left(f_postcode,2)=f_area_code||Left(f_postcode,2) and " +
                    "  '" + clientName + "' like '%'||f_city_name||'%' ";
            List<City> execute = CITY_DAO.execute(sql);
            if (!execute.isEmpty()&& StringUtils.isBlank(city.getF_postcode())) {
                city.setF_areaname(execute.get(0).getF_areaname());
                city.setF_postcode(execute.get(0).getF_postcode());
                city.setF_area_code(execute.get(0).getF_area_code());
            }
            return city;
        } else {
            sql = "select f_province,'' as f_city_name,f_city_name as f_area_code,f_postcode,f_area_code,f_city_level from t_city " +
                    "      where '" + clientName + "'  like '%'||f_province||f_city_name||'%' And coalesce(f_city_level,0)=0  order by f_postcode";
            List<City> execute = CITY_DAO.execute(sql);
            if (execute.size() >= 1) { //--有省县（市匹配），则反过来查地级市
                City city = execute.get(0);
                sql = " select f_city_name from t_city where coalesce(f_city_level,1)=1 and f_area_code||Left(f_postcode,2)=f_area_code||Left(f_postcode,2)";
                List<String> strings = BEAN.queryForList(sql, new Object[]{}, String.class);
                if (!strings.isEmpty()) {
                    city.setF_city_name(strings.get(0));
                }
            }
        }
        //假如都没有
        if (cities.isEmpty()){
            String province = clientName.substring(0, 2);
            sql = "select f_province,'' as f_city_name,f_city_name as f_area_code,f_postcode,f_area_code,f_city_level from t_city " +
                    "      where  f_province like '"+province+"%'";
            List<City> execute = CITY_DAO.execute(sql);
            City city = new City();
            if (execute!=null&&execute.size()>0) city.setF_province(execute.get(0).getF_province());
            return city;
        }
        return  cities.get(0);
    }

    public static City getAddressByAreaCode(String areaCode) {
        String sql="select f_province,f_city_name,f_area_code,f_postcode from t_city where f_area_code like '" +areaCode.trim()
                +"%' and f_city_level=1  order by f_postcode";
        List<City> execute = CITY_DAO.execute(sql);
        return execute.isEmpty()?new City():execute.get(0);
    }

    public static City getAddressByPostCode(String postCode) {
        String sql="select f_province,f_city_name,f_area_code,f_postcode from t_city where f_postcode like '" +postCode.trim()
                +"%'  order by f_postcode";
        List<City> execute = CITY_DAO.execute(sql);
        return execute.isEmpty()?new City():execute.get(0);
    }
    
    
    /**
     * 创建去模糊词汇函数
     *
     * @param clientName 公司名称  》》》浙江温州新特软件开发有限公司
     * @param fKeyLevel  2是严格排重  1是模糊判别
     * @return
     */
    public static String clearClientNameKeywords(String clientName, Integer fKeyLevel) {
    	if(StringUtils.isBlank(clientName))return clientName;
    	if(fKeyLevel==null)fKeyLevel = 1;
        String sql= "select f_name,f_key_level from t_client_keyword where f_key_level>= "+ fKeyLevel+" and '" + clientName.replace("'", "''") +"' like '%'|| f_name ||'%' ";
        List<ClientKeyword> list = Client_Keyword_Mapper.execute(sql);
        for(ClientKeyword keyword:list){
        	Integer keyLevel = keyword.getKeyLevel();
        	if(keyLevel==null)continue;
        	String name = keyword.getName()==null?"":keyword.getName();
        	switch (keyLevel) {
			case 3:
				clientName = clientName.replace(name, "");
				break;
			case 2:
				if(fKeyLevel==2){ //如果严格排重，名称中包含省份下面市县名字则去掉省份
					String sql2 = "select count(*) from t_city where f_province like '%"+name+"%' and '" + clientName.replace("'", "''").replace(name, "") + "' like '%'|| f_city_name ||'%' ";
					int count = CITY_DAO.executeCount(sql2);
					if(count>0){
						clientName = clientName.replace(name, "");
					}
				}else if(fKeyLevel==1){ //如果模糊判别，名称中包含省份就去掉省份
					clientName = clientName.replace(name, "");
				}
				break;	
			case 1:
				//如果城市为公司名称的最左边或最右边（已去了省份），则去掉
				if(clientName.endsWith(name)||clientName.startsWith(name))
					clientName = clientName.replace(name, "");
				break;		
			default:
				break;
			}
        }
		if (fKeyLevel == 2) clientName = ReStrUtils.reParenthesis(clientName);
        return clientName;
    }
    
  
}
