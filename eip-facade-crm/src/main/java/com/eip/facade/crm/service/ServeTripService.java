package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.OrderDtlBS;
import com.eip.facade.crm.entity.ServeTrip;

public interface ServeTripService {

	int insert(Integer staffId, ServeTrip serveTrip, List<OrderDtlBS> list);
	
	int update(ServeTrip serveTrip, List<OrderDtlBS> list);
	
	ServeTrip selectByServeTripId(Integer serveTripId);
	
	int count(int key, Integer projectId);
	
	List<ServeTrip> select(PageParam pageParam, int key, Integer projectId);
	
}
