package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.MemberInfo;

public interface MemberInfoService extends BaseService<MemberInfo> {

	MemberInfo getByZzyUserId(Integer zzyUserId);

	MemberInfo selectById(Long memberInfoId);

	AjaxResponse save(MemberInfo memberInfo, OperatorDto operatorDto);
}
