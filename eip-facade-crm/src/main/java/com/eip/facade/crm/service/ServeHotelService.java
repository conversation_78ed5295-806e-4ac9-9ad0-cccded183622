package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ServeHotel;

public interface ServeHotelService {

	int insert(Integer staffId, ServeHotel serveHotel);
	
	int update(ServeHotel serveHotel);
	
	ServeHotel selectByServeHotelId(Integer serveHotelId);

	int count(int key, Integer projectId);
	
	List<ServeHotel> select(PageParam pageParam, int key, Integer projectId);
	
}
