package com.eip.facade.crm.service;

import com.eip.common.entity.BaseEntity;
import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.IntegralFieldShowDto;
import com.eip.facade.crm.dto.ProductFieldShowDto;
import com.eip.facade.crm.dto.SaveClientAddFieldDto;
import com.eip.facade.crm.entity.DataGridSet;
import com.eip.facade.crm.entity.ExhibitSetting;
import com.eip.facade.crm.entity.Operator;
import com.eip.facade.crm.vo.IntegralTypeFieldShowVo;
import com.eip.facade.crm.vo.ProdKindAndProductFieldShowVo;

import java.util.List;
import java.util.Map;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-02-03 9:25
 */
public interface DataGridSetService extends BaseService<DataGridSet> {
    /**
     * 初始化操作员显示数据
     *
     * @param operId    操作员id
     * @param classZz   类的class对象
     * @param fGridName 初始化类型
     * @throws Exception
     */
    void initByOperatorData(Integer operId, Class<? extends BaseEntity> classZz, String fGridName) throws Exception;

	void initByOperator(DataGridSet dataGridSet, Class<?> classZz) throws Exception;

	void initByOperHistory(DataGridSet dataGridSet);

	List<DataGridSet> checkNewField(List<DataGridSet> list, DataGridSet dataGridSet);

	List<DataGridSet> checkNewFieldBuyer(List<DataGridSet> list, DataGridSet dataGridSet);

	List<DataGridSet> getWorkbench(DataGridSet dataGridSet);

	void saveWorkbench(List<DataGridSet> list, Operator operator,String fGridName);

	List<DataGridSet> getWorkbenchM(DataGridSet dataGridSet);

	List<ExhibitSetting> getClientAddM(DataGridSet dataGridSet);

	void saveClientAddM(SaveClientAddFieldDto saveClientAddFieldDto, Operator operator);

	List<ProdKindAndProductFieldShowVo> getProductByProject(ProductFieldShowDto productFieldShowDto);

	void saveProductByProject(ProductFieldShowDto productFieldShowDto);

	Map<String,Object> getByProject(ProductFieldShowDto productFieldShowDto);

	/**
	 * 获取积分台账报表表头
	 * @param integralFieldShowDto
	 * @return
	 */
	Map<String, Object> getIntegralLedgerHeader(IntegralFieldShowDto integralFieldShowDto);

	/**
	 * 保存积分台账表头
	 * @param integralFieldShowDto
	 */
	void saveIntegralOrGoodsField(IntegralFieldShowDto integralFieldShowDto);

	List<IntegralTypeFieldShowVo> getIntegralTypeDynamicField(IntegralFieldShowDto integralFieldShowDto);

	AjaxResponse getIntegralGoodsDynamicField(IntegralFieldShowDto integralFieldShowDto);

	AjaxResponse getProjectServiceProcessField(Integer operatorId, Integer orgNum, String projectIds);

	List<DataGridSet> filterNoFieldRequired(List<DataGridSet> list, DataGridSet dataGridSet);
}

