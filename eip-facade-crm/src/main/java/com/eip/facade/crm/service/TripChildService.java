package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.TripChild;


public interface TripChildService {
	
	int insert(TripChild e);
	
	int update(TripChild e);
	
	TripChild selectById(Integer id);	
	
	int count(TripChild e);
	
	int delete(Integer id);
	
	int deleteBatch(List<TripChild> list);

	List<TripChild> select(PageParam pageParam, TripChild e);

	List<TripChild> getByMap(Map<String, Object> map);
}
