package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.common.entity.Statistics;
import com.eip.facade.crm.dto.ClientTouchDelDto;
import com.eip.facade.crm.dto.ClientTouchImportDto;
import com.eip.facade.crm.dto.ClientTouchListDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.*;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Repository;

public interface ClientTouchService {
	
	int count(ClientTouch e);

	List<ClientTouch> select(PageParam pageParam, ClientTouch e,String findName,String findNameVal);

	int insert(ClientTouch e,Operator operator);

	int update(ClientTouch e,Operator operator);
	
	int delete(Integer id);
	
	int deleteBatch(List<ClientTouch> list);
	
	ClientTouch selectById(Integer id);

	int updateDel(ClientTouch e);

	int updateDelBatch(List<ClientTouch> list);

	PageInfo<ClientTouch> selectPage(PageParam pageParam, ClientTouch clientTouch, String findName, String findNameVal);

    void updateClientToNewClient(Integer oldClientId, Integer newClientId);

	List<Statistics> statistics(ClientTouchListDto clientTouchListDto);

	int selectClientCountAsync(ClientTouch clientTouch, String findName, String findNameVal,String clientTypeCode);

	int batchAdd(List<ClientTouch> list);

	int importSave(List<ClientTouch> list, List<BatchDtl> batchDtlList, OperatorDto operatorDto);

	Map<String,Object> selectImportCountAsync(Long import_batchId);

    Map<String, Object> updateDelete(ClientTouchDelDto clientTouchDelDto);

    int bacthAddClientTouch(List<ClientTouch> clientTouchList, List<Accessory> accessoryList, List<Awoke> awokeList, List<BatchDtl> batchDtlList);

    Integer queryMaxId(ClientTouch clientTouch, String findName, String findNameVal);
}
