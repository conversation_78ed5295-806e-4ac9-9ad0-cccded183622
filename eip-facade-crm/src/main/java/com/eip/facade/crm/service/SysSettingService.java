package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.entity.SysSetting;

import java.util.List;
import java.util.Map;

public interface SysSettingService extends BaseService<SysSetting>{

    int countExistSetting(Map<String, Object> hashMap);

    List<SysSetting> getSysSettingList(List<String> asList);

    String getValue(String code);

    Map<String, Object> buildOssDataBase();

}
