package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.TaskKindOrg;

public interface TaskKindOrgService {

	int count(TaskKindOrg taskKindOrg);

	List<TaskKindOrg> select(PageParam pageParam, TaskKindOrg taskKindOrg);

	int deleteBatch(List<TaskKindOrg> list, OperatorDto operator);

	int insertBatch(List<TaskKindOrg> list, OperatorDto operator);

	int updateBatch(List<TaskKindOrg> list, OperatorDto operator);

	List<TaskKindOrg> select(TaskKindOrg taskKindOrg);
	
	
}
