package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.TraderAndEnterpriseDto;
import com.eip.facade.crm.dto.TraderRegDto;
import com.eip.facade.crm.entity.TraderReg;
import com.eip.facade.crm.entity.WorkTeam;
import com.eip.facade.crm.vo.TraderAndEnterpriseVo;

import java.util.HashMap;
import java.util.List;

public interface TraderRegService extends BaseService<TraderReg> {

	HashMap<String,Object> getPage(PageParam pageParam, TraderRegDto traderRegDto);

	TraderRegDto selectById(Integer f_trader_id);

	int save(TraderRegDto traderRegDto);

	int deleteRegAndQuestion(TraderReg bean);

	HashMap<String, Object> getPageAsync(PageParam pageParam, TraderRegDto traderRegDto);

	int delBatch(List<TraderReg> list, OperatorDto operator);

	int saveTrader(TraderRegDto traderRegDto, OperatorDto operator);

	AjaxResponse check(TraderRegDto traderRegDto, OperatorDto operator);

    List<WorkTeam> getSyncTeam(TraderRegDto traderRegDto);

    Boolean queryRegEnglish(Integer projectId, Integer targetType);

    Integer queryMaxId(TraderRegDto traderRegDto);

	int batchDelete(List<TraderReg> list, OperatorDto operator);

	String buildEventName(String projectName, String traderCompany, String regNum);

	String buildEventName(Integer projectId, String traderCompany, String regNum);

	AjaxResponse saveTraderAndEnterprise(TraderAndEnterpriseDto traderAndEnterpriseDto, OperatorDto operator);

	Integer queryTraderMaxId(TraderAndEnterpriseDto traderAndEnterpriseDto);

	HashMap<String, Object> getTraderPage(PageParam pageParam, TraderAndEnterpriseDto traderAndEnterpriseDto);

	HashMap<String, Object> getTraderPageAsync(PageParam pageParam, TraderAndEnterpriseDto traderAndEnterpriseDto);

	TraderAndEnterpriseVo selectTraderById(Integer f_trader_id, Integer f_org_num);

	AjaxResponse traderCheck(TraderAndEnterpriseDto traderAndEnterpriseDto, OperatorDto operator);

	TraderAndEnterpriseVo getClientOrEnterpriseByClientId(Integer clientId, Integer f_org_num);

	TraderAndEnterpriseVo getTraderBoothNeedByProjCltId(Integer projCltId, Integer f_org_num);
}
