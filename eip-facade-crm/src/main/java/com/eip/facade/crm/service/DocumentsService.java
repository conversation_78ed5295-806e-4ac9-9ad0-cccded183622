package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.ExportPdfDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.Documents;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;


public interface DocumentsService {
	
	int insert(Documents e);
	
	int update(Documents e);
	
	Documents selectById(Integer id);

	int count(Documents e);
	
	int delete(Integer f_doc_code);
	
	int deleteBatch(List<Documents> list);

	List<Documents> select(PageParam pageParam, Documents e);

	List<Documents> getListByMap(Map<String,Object> map);

	void saveDocumentByUploadAttach(Map<String, String> map, OperatorDto operatorDto, String recordCode, String tableName);

    AjaxResponse saveExhibitorNotice(ExportPdfDto exportPdfDto, OperatorDto operatorDto);

	Documents getExhibitorNoticeById(ExportPdfDto exportPdfDto);

	PageInfo<Documents> getExhibitorNoticePage(PageParam pageParam, ExportPdfDto exportPdfDto);
}
