package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ServeCompany;

public interface ServeCompanyService {

	int count(int key, ServeCompany serveCompany);

	List<ServeCompany> select(PageParam pageParam, int key, ServeCompany serveCompany);

	int insert(ServeCompany serveCompany);

	int update(ServeCompany serveCompany);

	ServeCompany getOne(ServeCompany serveCompany);

	List<ServeCompany> selectByMap(Map<String, Object> map);

	ServeCompany selectMoreById(int id);

	List<ServeCompany> selectMoreByMap(Map<String, Object> map);

	ServeCompany selectNext(ServeCompany serveCompany);

	List<ServeCompany> selectMorePage(PageParam pageParam, ServeCompany serveCompany);

	int countMore(ServeCompany serveCompany);

	ServeCompany selectByBoothNum(ServeCompany serveCompany);

	ServeCompany getOneMore(ServeCompany serveCompany);

}
