package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.Client;
import com.eip.facade.crm.entity.TaskDetail;
import com.eip.facade.crm.vo.TaskDetailVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface TaskDetailService {
	
	int count(TaskDetail e);

	List<TaskDetail> select(PageParam pageParam, TaskDetail e);

	int insert(TaskDetail e);

	int update(TaskDetail e);
	
	int delete(Integer id);
	
	int deleteBatch(List<TaskDetail> list,OperatorDto operatorDto);
	
	TaskDetail selectById(Integer id);

	int insertList(List<TaskDetail> list,OperatorDto operatorDto);

	List<TaskDetail> selectByMap(Map<String, Object> map);

	int updateConfirmAndMemo(TaskDetail taskDetail);

	PageInfo<TaskDetailVo> getList(PageParam pageParam, TaskDetailVo taskDetailVo);

	int batchUpdateTaskId(List<TaskDetail> list, OperatorDto operator);

	int addByClientAndTask(List<Client> list, TaskDetail taskDetail, OperatorDto operator);

	//void insertByClientQuery(ClientListDto clientListDto, TaskDetail taskDetail, Operator operator);

	int batchAdd(List<TaskDetail> taskDtlList);

	int batchUpdateTaskIdAsync(List<TaskDetail> subList, OperatorDto operator);

	int updateConfirm(TaskDetail taskDetail, OperatorDto operator);


	int getListCount(TaskDetailVo taskDetailVo);

	int getListCountAsync(TaskDetailVo taskDetailVo);

	int batchDelete(List<TaskDetailVo> list, OperatorDto operatorDto);
}
