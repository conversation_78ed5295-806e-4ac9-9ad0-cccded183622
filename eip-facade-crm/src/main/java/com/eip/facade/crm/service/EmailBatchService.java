package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.EmailBatchDto;
import com.eip.facade.crm.entity.EmailBatch;
import com.github.pagehelper.PageInfo;

public interface EmailBatchService extends BaseService<EmailBatch> {

    PageInfo<EmailBatch> getPage(PageParam pageParam, EmailBatchDto emailBatchDto);

    long sumSendNumberAsync(EmailBatchDto emailBatchDto);

    long sumSuccessNumberAsync(EmailBatchDto emailBatchDto);

    long sumFailedNumberAsync(EmailBatchDto emailBatchDto);
}
