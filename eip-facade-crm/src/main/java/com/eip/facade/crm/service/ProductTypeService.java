package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ExhibitKind;
import com.eip.facade.crm.entity.ProductType;

public interface ProductTypeService {

	int count(int key, ProductType productType);

	List<ProductType> select(PageParam pageParam, int key, ProductType productType);

	int insert(ProductType productType);

	int update(ProductType productType);

	int delete(int productTypeId);

	int deleteBatch(List<ProductType> productTypes);

	List<ProductType> selectByMap(Map<String, Object> map);

	

}
