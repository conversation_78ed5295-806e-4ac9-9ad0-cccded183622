package com.eip.facade.crm.service;

import com.eip.facade.crm.vo.PunchEventVo;

import java.util.List;
import java.util.Map;

/**
 * @USER: zwd
 * @DATE: 2023-12-12 16:47
 * @DESCRIPTION:
 */
public interface PunchEventService {

    /**
     * 查询打卡活动绑定的积分规则ID
     * @param punchEventMap
     * @return
     */
    String selectEnableIntegralPunEvent(Map<String, Object> punchEventMap);

    List<PunchEventVo> queryPunchEvent(Map<String, Object> paramMap);


}
