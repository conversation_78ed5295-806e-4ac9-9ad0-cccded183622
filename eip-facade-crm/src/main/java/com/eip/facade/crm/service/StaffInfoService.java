package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.StaffInfo;

public interface StaffInfoService {

	int count(int key, Integer projectId, Integer clientId, String staffName);
	
	List<StaffInfo> select(PageParam pageParam, int key, Integer projectId, Integer clientId, String staffName);
	
	int update(StaffInfo staffInfo);
	
}
