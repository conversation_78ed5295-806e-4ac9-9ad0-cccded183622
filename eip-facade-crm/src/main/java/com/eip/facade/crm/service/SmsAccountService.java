package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.entity.SmsChannel;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.SmsAccount;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface SmsAccountService extends BaseService<SmsAccount> {

    PageInfo<SmsAccount> getPage(PageParam pageParam, SmsAccount smsAccount);

    /**
     * 设为默认
     * @param smsAccountId
     * @param operatorDto
     * @return
     */
    int setDefault(Integer smsAccountId, OperatorDto operatorDto);

    /**
     * 批量删除
     * @param smsAccountIds
     * @param operatorDto
     * @return
     */
    int batchDelete(String smsAccountIds, OperatorDto operatorDto);

    int updateSmsAccountById(SmsAccount smsAccount);

	SmsAccount getDefaultVerificationAccount(Integer orgNum);

	List<SmsChannel> getAllSmsChannel();

	String getSmsChannelUrl(String channelCode);

	String getSmsChannelSecondUrl(String channelCode);

	String getSmsChannelName(String channelCode);

	SmsChannel getSmsChannel(String channelCode);

	SmsAccount selectById(Integer smsAccountId, Integer orgNum);
}
