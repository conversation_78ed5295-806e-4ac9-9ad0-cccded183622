package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.UserLoginDto;
import com.eip.facade.crm.entity.UserLogin;
import com.github.pagehelper.PageInfo;

public interface UserLoginService extends BaseService<UserLogin> {
    /**
     * 保存退出登录状态和时间
     * @param loginId
     * @return
     */
    int logout(Integer loginId);

    /**
     * 保存失效时间
     * @param sessionId
     */
    void invalidAsync(String sessionId);

    /**
     * 分页查询
     * @param pageParam
     * @param userLoginDto
     * @return
     */
    PageInfo<UserLogin> getPage(PageParam pageParam, UserLoginDto userLoginDto);
    /**
     * 分页查询 （异步）
     * @param pageParam
     * @param userLoginDto
     * @return
     */
    PageInfo<UserLogin> getPageAsync(PageParam pageParam, UserLoginDto userLoginDto);
}
