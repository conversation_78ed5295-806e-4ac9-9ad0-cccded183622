package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ProjClientDto;
import com.eip.facade.crm.entity.ProjClient;
import com.eip.facade.crm.entity.RivalClientInfo;
import com.eip.facade.crm.vo.ClientStateVo;
import com.eip.facade.crm.vo.ProjCltAmountVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;


public interface ProjClientService {

	int getSysId(String fieldCode, Boolean increaseMode);
	
	int insertTrader(ProjClient projClient);
	
	int updateTrader(ProjClient projClient);
	
	int delete(Integer projCltId);
	
	int deleteBatch(List<ProjClient> list);

	int count(ProjClient projClient);

	List<ProjClient> select(PageParam pageParam, ProjClient projClient);

	int insertList(List<ProjClient> projClients, OperatorDto operatorDto);

	PageInfo<ProjClient> getByClient(PageParam pageParam, ProjClientDto projClient);

    void updateClientToNewClient(Integer oldClientId, Integer newClientId);
    
    ProjClient selectById(Integer projCltId);

    Map<String,Object> deleteWithFuncode(int projCltId, OperatorDto operator);

    ProjCltAmountVo getProjCltAmount(Integer projCltId,Integer orgNum);

    ClientStateVo getProjClientStateByTask(Integer projectId, Integer clientId, String taskKindCode);

	/**
	 * 查询和新增项目客户
	 * @param projectId 项目id
	 * @param clientId  客户id
	 * @param actorCode 参与类型
	 * @param orgNum  机构号
	 * @param ownerId  归属人
	 * @param add  是否新增
	 * @return
	 */
	Integer queryOrAdd(Integer projectId, Integer clientId, String actorCode, Integer orgNum, Integer ownerId,Boolean add);

	/**
	 * 根据 项目和客户 获取所有已参与的参与类别actorCode，按照项目客户id正序 返回数据
	 * @param projectId
	 * @param clientId
	 * @return
	 */
    List<String> getAllActorCode(Integer projectId, Integer clientId);

	AjaxResponse updateRivalClient(RivalClientInfo rivalClientInfo, OperatorDto operatorDto);

	AjaxResponse existsExhibitorData(Integer clientId, Integer orgNum);

	AjaxResponse updateExhibitorCompanyName(ProjClientDto projClientDto, OperatorDto operatorDto);
}
