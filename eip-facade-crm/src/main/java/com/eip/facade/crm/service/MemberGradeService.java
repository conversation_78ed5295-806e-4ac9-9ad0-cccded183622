package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.MemberGradeDto;
import com.eip.facade.crm.entity.MemberGrade;
import com.eip.facade.crm.vo.EnterpriseMemberGradeVo;
import com.eip.facade.crm.vo.MemberGradeVo;

import java.util.List;

public interface MemberGradeService extends BaseService<MemberGrade> {

    List<MemberGradeVo> queryMemberGrade(MemberGradeDto memberGradeDto);

    List<EnterpriseMemberGradeVo> queryEnterpriseMemberGrade(MemberGradeDto memberGradeDto);

    List<MemberGrade> selectNewMemberGradeByOrgNum(Integer f_org_num);
}
