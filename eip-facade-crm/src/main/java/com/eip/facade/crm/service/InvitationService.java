package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.Invitation;

import java.util.List;
import java.util.Map;

public interface InvitationService {
	
	int insert(Invitation e);
	
	int update(Invitation e);
	
	List<Invitation> select(PageParam pageParam, Invitation historyPerson);

    List<Invitation> queryInvitation(Map<String, Object> paramMap);

	int updateResetInvitationServeBoothRef(Integer serveBoothId);

}
