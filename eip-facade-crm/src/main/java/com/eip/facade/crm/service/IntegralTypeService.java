package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.entity.IntegralType;
import com.eip.facade.crm.vo.IntegralTypeVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company ：杭州展之科技
 * @date ：Created in 2022-11-30 9:40
 * @description：
 */
public interface IntegralTypeService extends BaseService<IntegralType> {

    PageInfo<IntegralType> getPage(PageParam pageParam, IntegralType integralType);

    int save(IntegralType integralType);

    List<IntegralType> selectIntegralType(Map<String, Object> queryMap);

    List<IntegralTypeVo> queryIntegralType(IntegralType integralType);
}
