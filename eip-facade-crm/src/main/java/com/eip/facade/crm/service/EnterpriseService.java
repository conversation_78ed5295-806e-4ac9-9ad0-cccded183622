package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.EnterpriseDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ZzyUserDto;
import com.eip.facade.crm.entity.Enterprise;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface EnterpriseService extends BaseService<Enterprise> {

	List<Enterprise> getByZzyUserId(Integer zzyUserId, Integer orgNum);

	AjaxResponse save(Enterprise enterprise, OperatorDto operatorDto);

	AjaxResponse delete(Long enterpriseId, OperatorDto operatorDto);

	Enterprise getById(Long enterpriseId, Integer orgNum);

	List<Enterprise> getList(Map<String, Object> paramMap);

	int insertZzyUserAfterHandleMemberInfo(ZzyUserDto paramZzyUserDto);

	PageInfo<Enterprise> getCertificationList(EnterpriseDto enterpriseDto, PageParam pageParam);

	AjaxResponse certification(Enterprise enterprise, OperatorDto operator);

	int updateBindEnterpriseMemberId(Long enterpriseId, Integer zzyUserId);

	Map<String, Object> insertEnterpriseMemberForBatch(List<Enterprise> enterpriseList, EnterpriseDto enterpriseDto, OperatorDto operatorDto);

	AjaxResponse insertEnterpriseMember(EnterpriseDto enterpriseDto,OperatorDto operatorDto);

    AjaxResponse updateBindEnterprisePersonnelAccount(EnterpriseDto enterpriseDto,OperatorDto operatorDto);

	AjaxResponse insertEnterpriseSubAccount(EnterpriseDto enterpriseDto, OperatorDto operatorDto);

	Map<String, Object> updateEnterpriseZzyUserPassword(List<Enterprise> enterpriseList, EnterpriseDto enterpriseDto, OperatorDto operatorDto);

	Map<String, Object> updateEnterpriseMemberGrade(List<Enterprise> enterpriseList,EnterpriseDto enterpriseDto, OperatorDto operatorDto);
}
