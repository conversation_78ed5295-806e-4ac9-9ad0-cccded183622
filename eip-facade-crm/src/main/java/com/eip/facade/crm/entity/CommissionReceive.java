package com.eip.facade.crm.entity;

import com.eip.common.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 提成领取明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_commission_receive")
public class CommissionReceive extends BaseEntity {

    @Id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = "f_commission_receive_id")
    private Long commissionReceiveId;
    /**
     * 提成id
     */
    @Column(name = "f_commission_id")
    private Integer commissionId;
    /**
     * 领取日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "f_receive_date")
    private Date receiveDate;
    /**
     * 领取人id
     */
    @Column(name = "f_receiver_id")
    private Integer receiverId;
    /**
     * 领取金额
     */
    @Column(name = "f_receive_amount")
    private BigDecimal receiveAmount;
    /**
     * 机构号
     */
    @Column(name = "f_org_num")
    private Integer orgNum;
    /**
     * 领取人名称
     */
    @Transient
    private String receiverName;
}
