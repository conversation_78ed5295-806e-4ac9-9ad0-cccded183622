package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.ToolDto;
import com.eip.facade.crm.entity.DefaultTool;

import java.util.List;

public interface DefaultToolService {

	int count(ToolDto toolDto);

	List<DefaultTool> select(PageParam pageParam, ToolDto toolDto);

	AjaxResponse insert(ToolDto toolDto);

	AjaxResponse update(List<DefaultTool> defaultTools);

	AjaxResponse deleteBatch(ToolDto toolDto);

	DefaultTool selectOne(ToolDto toolDto);

	AjaxResponse save(List<DefaultTool> list, String boothTypeCode);

	int deleteAll(String boothTypeCode);
}
