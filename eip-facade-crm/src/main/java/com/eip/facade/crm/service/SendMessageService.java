package com.eip.facade.crm.service;

import com.eip.facade.crm.entity.Sms;
import com.eip.facade.crm.entity.SmsAccount;

import java.util.Map;

public interface SendMessageService {
	
	/**
	 * 发送短信 （签名和账号从数据库表t_sys_setting_org查）
	 * @param mobile 手机
	 * @param message 内容
	 * @param orgNum  机构号
	 * @param sms 短信发送日志
	 * @return 0 失败 1 成功  2 短信账号相关信息未设置
	 */
	int sendMessage(String mobile,String message,Integer orgNum,Sms sms);

	/**
	 * 发送短信 (账号从数据库表t_sys_setting_org查)
	 * @param mobile 手机
	 * @param message 内容
	 * @param orgNum  机构号
	 * @param sms 短信发送日志
	 * @param sign 签名
	 * @return 0 失败 1 成功  2 短信账号相关信息未设置
	 */
	int sendMessage(String mobile,String message,Integer orgNum,Sms sms,String sign);

	/**
	 * 发送短信 (账号从数据库表t_sms_account查)
	 * @param mobile  手机
	 * @param content 内容
	 * @param orgNum 机构号
	 * @param sms  短信发送日志
	 * @param sign  签名
	 * @param smsAccountId  短信账号id
	 * @return  0 失败 1 成功  2 短信账号相关信息未设置
	 */
	int sendMessage(String mobile, String content, Integer orgNum, Sms sms, String sign, Integer smsAccountId);

	/**
	 * 从数据库中查询  状态为0 的短信  然后发送并进行更新   适合群发的
	 * @param mobile  手机
	 * @param content 内容
	 * @param orgNum 机构号
	 * @param sms  短信发送日志
	 * @param sign  签名
	 * @param smsAccountId  短信账号id
	 * @return  0 失败 1 成功  2 短信账号相关信息未设置
	 */
	int sendMassTextingMessage(String mobile, String content, Integer orgNum, Sms sms, String sign, Integer smsAccountId);

	/**
	 * 查询各个短信通道的余额
	 * @return
	 */
	Map<String,Object> selectNoteChannelBalance(String channelCode,String account,String passWord,String smsType);
	/**
	 * 邮件发送
	 * @param orgNum 机构号
	 * @param receiver 邮箱
	 * @param subject 主题
	 * @param msg  内容
	 * @return 0 失败  1 成功 2 失败 账号未配置
	 */
	int sendEmail(Integer orgNum,String receiver, String subject, String msg);

	/**
	 * 发送短信 (账号取smsAccount参数)
	 * @param mobile
	 * @param message
	 * @param orgNum
	 * @param sms
	 * @param sign
	 * @param smsAccount
	 * @return 0 失败 1 成功  2 短信账号相关信息未设置
	 */
	int sendMessage(String mobile, String message, Integer orgNum, Sms sms, String sign, SmsAccount smsAccount);
}
