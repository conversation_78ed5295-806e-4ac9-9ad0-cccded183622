package com.eip.facade.crm.service;

import java.util.List;

import com.eip.facade.crm.entity.OrderBS;
import com.eip.facade.crm.entity.OrderDtlBS;

public interface OrderDtlBSService {

	List<OrderDtlBS> selectByOrderBSId(Integer orderBSId);
	
	List<OrderDtlBS> selectByServiceIdAndServiceType(int serviceId, String serviceType);

	List<OrderDtlBS> selectByTaskDtlId(Integer taskDtlId);
	
	int update(List<OrderDtlBS> list);
	
}
