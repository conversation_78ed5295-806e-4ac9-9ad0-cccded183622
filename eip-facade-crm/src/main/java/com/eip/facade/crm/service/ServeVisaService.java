package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ServeVisa;
import com.eip.facade.crm.entity.VisaDtl;

public interface ServeVisaService {

	int insert(Integer staffId, ServeVisa serveVisa, List<VisaDtl> list);
	
	int update(ServeVisa serveVisa, List<VisaDtl> list);
	
	ServeVisa selectByServeVisaId(Integer serveVisaId);

	int count(int key, ServeVisa serveVisa);

	List<ServeVisa> select(PageParam pageParam, int key, ServeVisa serveVisa);

	int deleteBatch(List<ServeVisa> serveVisaList);

	int insert(ServeVisa serveVisa);

	int update(ServeVisa serveVisa);


	ServeVisa selectPrice(Map<String, Object> map);

	int updateVisaStage(List<ServeVisa> list);

}
