package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.CommissionDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.Commission;
import com.eip.facade.crm.entity.CommissionDtl;
import com.eip.facade.crm.vo.CommissionClientVo;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface CommissionService extends BaseService<Commission> {

    PageInfo<Commission> getPage(PageParam pageParam, CommissionDto commissionDto);

    PageInfo<Commission> getPageAsync(PageParam pageParam, CommissionDto commissionDto);

    List<CommissionDtl> getCommissionDtlTotalAmountAsync(CommissionDto commissionDto);

    BigDecimal getCommissionReceiveTotalAmountAsync(CommissionDto commissionDto);

    int save(Commission commission, OperatorDto operatorDto);

    int batchDelete(String commissionIds, OperatorDto operatorDto);

    int batchDelete(List<Commission> list, OperatorDto operatorDto);

    AjaxResponse getReceivablesAndReceipt(CommissionDto commissionDto);

    Commission getCommissionAmountAsync(CommissionDto commissionDto);

    Commission getMoreById(Integer commissionId, Integer orgNum);

    int check(Integer commissionId, Boolean isCheck, OperatorDto operatorDto);

    PageInfo<CommissionClientVo> getClient(PageParam pageParam, CommissionDto commissionDto);

    AjaxResponse batchAdd(CommissionDto commissionDto, OperatorDto operatorDto);

    Commission query(Date accountPeriod, Integer projectId, Integer clientId, Integer salesmanId, Integer notCommissionId);
}
