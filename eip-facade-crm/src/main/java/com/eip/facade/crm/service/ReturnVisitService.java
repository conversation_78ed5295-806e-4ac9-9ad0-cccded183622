package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ReturnVisitQueryDto;
import com.eip.facade.crm.dto.ReturnVisitSaveDto;
import com.eip.facade.crm.entity.ReturnVisit;
import com.github.pagehelper.PageInfo;

public interface ReturnVisitService extends BaseService<ReturnVisit> {

    PageInfo<ReturnVisit> getPage(PageParam pageParam, ReturnVisitQueryDto returnVisitQueryDto);

    List<ReturnVisit> getList(PageParam pageParam, ReturnVisitQueryDto returnVisitQueryDto);

    Integer selectMaxId(ReturnVisitQueryDto returnVisitQueryDto);

    AjaxResponse save(ReturnVisitSaveDto returnVisitSaveDto, OperatorDto operatorDto);

    AjaxResponse delete(String returnVisitIds, OperatorDto operatorDto);

    ReturnVisit selectById(Integer returnVisitId, Integer orgNum);
}
