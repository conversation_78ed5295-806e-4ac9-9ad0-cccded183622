package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.CertificateDto;
import com.eip.facade.crm.entity.Certificate;
import com.eip.facade.crm.vo.CertificateBaseVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface CertificateService extends BaseService<Certificate> {

    PageInfo<CertificateBaseVo> queryCertificate(PageParam pageParam, CertificateDto certificateDto);

    List<CertificateBaseVo> getList(CertificateDto certificateDto);

    /**
     * 获取观众门票证件
     * @param projectId
     * @param targetType
     * @return
     */
    Certificate getCertificateA(Integer projectId, Integer targetType);
}
