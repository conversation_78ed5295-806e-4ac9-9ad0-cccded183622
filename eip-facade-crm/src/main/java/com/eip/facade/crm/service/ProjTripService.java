package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ProjTrip;
import com.github.pagehelper.PageInfo;

public interface ProjTripService {

    int count(int projectId);

    List<ProjTrip> select(PageParam pageParam, int projectId);

    int insert(ProjTrip projTrip);

    int update(ProjTrip projTrip);

    int delete(Integer projTripId);

    int deleteBatch(List<ProjTrip> list);

    int insertMore(ProjTrip projTrip);

    int updateMore(ProjTrip projTrip);

    List<ProjTrip> getByProjectId(int projectId);

    int countMap(Map<String, Object> map);

    PageInfo<ProjTrip> select(PageParam pageParam, ProjTrip projTrip);

}
