package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.entity.ChannelTarget;
import com.eip.facade.crm.vo.ChannelTargetVo;

import java.util.List;
import java.util.Map;

/**
 * @USER: zwd
 * @DATE: 2023-10-09 18:26
 * @DESCRIPTION:
 */
public interface ChannelTargetService extends BaseService<ChannelTarget> {

    List<ChannelTargetVo> getList(Map<String, Object> paramMap);
}
