package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.service.BaseService;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.ClientAuthStatusDto;
import com.eip.facade.crm.dto.ClientTransferDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ResetAuthDto;
import com.eip.facade.crm.entity.Client;
import com.eip.facade.crm.entity.Operator;
import com.eip.facade.crm.entity.TeamClient;
import com.eip.facade.crm.vo.TeamForMergeClientVo;
import org.springframework.stereotype.Repository;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-01-16 14:22
 */

public interface TeamClientService extends BaseService<TeamClient> {

    String getGroupNameByCilent(TeamClient teamClient);

	List<TeamClient> selectByMap(Map<String, Object> map);

	int deleteByClientId(Integer clientId);

    List<Long> getClientIdByTeamIdCondition(Integer inTeamId, Integer notInTeamId, Integer notJoinedTeam, String clientTypeCode);

    List<Long> selectClientIdByCondition(ClientAuthStatusDto clientAuthStatusDto);

	int checkIsExistAuth(List<Client> list, Integer add_fWorkteamId);

	Boolean queryOtherTeamProtect(Integer clientId, Integer query_workTeamId, Integer orgNum);

	List<Integer> queryNoOperAuth(List<Integer> clientIds, Integer fWorkteamId, List<Integer> salesmanIds);

    List<TeamClient> getByNameAndTeam(Map<String, Object> param);

    List<TeamClient> selectByClientIds(List<Integer> clientIds, Integer fWorkteamId);

    /**
     * 团队客户数据移交
     * @param teamClients
     * @param clientTransferDto
     * @param operator
     * @return
     */
    int transfer(List<TeamClient> teamClients, ClientTransferDto clientTransferDto, Operator operator);

    /**
     * 重置团队客户数据授权
     * @param resetAuthDto
     * @return
     */
    int resetAuth(ResetAuthDto resetAuthDto);

    int countByMap(Map<String, Object> map);

    /**
     * 根据客户和团队查询 团队客户id
     * @param clientIds
     * @param workTeamId
     * @return  key: clientId  value :teamCltId
     */
    Map<String, Long> selectTeamCltIds(List<Integer> clientIds, Integer workTeamId);

    /**
     * 独占客户（商机成交或合同生效）
     * @param workTeamId  团队id
     * @param clientId  客户id
     * @param salesmanId  业务员id
     * @param from  从何动作触发独占动作的标识
     */
    void exclusiveClient(Integer workTeamId, Integer clientId, Integer salesmanId, String from, OperatorDto operator);

    /**
     * 查询数据，带客户名称和团队名称
     * @param map
     * @return
     */
    TeamClient queryClientNameAndTeamName(Map<String, Object> map);

    /**
     * 独占客户（商机保护）
     * @param workTeamId  团队id
     * @param clientId  客户id
     * @param salesmanId  业务员id
     */
    int receiveClientFromOpp(Integer workTeamId, Integer clientId,Integer salesmanId, Operator operator);

	AjaxResponse queryAllowCompanyCount(Integer workTeamId, Integer salesmanId, Integer authStatus);

	int selectAuthCount(Integer workTeamId, String clientTypeCode, Integer salesmanId, Integer authStatus);

	TeamClient getParentCompanyTeamByLinkman(Integer clientId, Integer workTeamId);

	List<TeamForMergeClientVo> getTeamForMergeClient(String clientIds, Integer parentClientId);

	List<TeamClient> getAllLinkmanTeamByParentClientId(Integer clientId, List<Integer> workTeamIdList);

	void updateLinkmanTeamByParentCompany(Integer clientId, Integer workTeamId);

	boolean authEquals(TeamClient teamClient1, TeamClient teamClient2);

	void copyAuth(TeamClient source, TeamClient target);
}
