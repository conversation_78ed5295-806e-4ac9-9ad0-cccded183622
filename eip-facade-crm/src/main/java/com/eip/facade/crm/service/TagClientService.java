package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.TagClient;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-10 17:53
 */
public interface TagClientService extends BaseService<TagClient> {
    List<TagClient> selectMyAndShared(Long clientId, Integer operatorId, Integer teamId, Integer f_org_num, String clientTypeCode);

    int batchInsert(List<TagClient> tagClients);

    void save(List<Integer> clientIds, Long favoritesId, OperatorDto operator);
}
