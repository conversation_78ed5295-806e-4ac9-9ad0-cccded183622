package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.EventChild;

public interface EventChildService extends BaseService<EventChild> {
    /**
     * 保存日志
     * @param operator
     * @param operateMemo
     * @param eventKind
     * @param operateType
     * @param operObjCode
     * @param operObjName
     * @param eventId
     * @return
     */
    int save(OperatorDto operator, String operateMemo, Integer eventKind, String operateType, String operObjCode, String operObjName, Long eventId);
}
