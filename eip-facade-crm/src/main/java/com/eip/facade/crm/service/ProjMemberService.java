package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.Operator;
import com.eip.facade.crm.entity.ProjMember;

public interface ProjMemberService {

	List<ProjMember> selectByProjectId(Integer projectId);
	
	int delete(Integer id);
	
	int insert(ProjMember projMember);
	
	int update(ProjMember projMember);
	
	public int getSysId(String fieldCode, Boolean increaseMode);

	int batchSave(List<ProjMember> list, OperatorDto operator);

	/**
	 * 查询人员项目角色
	 * @param operatorId
	 * @param workTeamId
	 * @param projectIds
	 * @return
	 */
    Map<String, String> selectOperatorProjRoleAsync(Integer operatorId, Integer workTeamId, List<Integer> projectIds);

    List<ProjMember> selectByMap(Map<String, Object> map);

	int delete(Integer id, OperatorDto operator);

	int save(ProjMember projMember, OperatorDto operatorDto);
}
