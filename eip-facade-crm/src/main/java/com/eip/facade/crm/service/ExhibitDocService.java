package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.DocClass;
import com.eip.facade.crm.entity.ExhibitDoc;

import java.util.List;
import java.util.Map;

public interface ExhibitDocService {
	
	int insert(ExhibitDoc e);
	
	int update(ExhibitDoc e);
	
	ExhibitDoc selectById(Integer id);	
	
	int count(ExhibitDoc e);
	
	int delete(ExhibitDoc exhibitDoc);
	
	int deleteBatch(List<ExhibitDoc> list);

	List<ExhibitDoc> select(PageParam pageParam, ExhibitDoc e);

	List<ExhibitDoc> getListByMap(Map<String,Object> map);

	List<DocClass> getDocClass();

	int insertExhibitDocAndDocument(ExhibitDoc e);
}
