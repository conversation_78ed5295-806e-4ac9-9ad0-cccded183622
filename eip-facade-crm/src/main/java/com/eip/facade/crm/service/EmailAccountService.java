package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.entity.EmailAccount;

import java.util.List;

public interface EmailAccountService extends BaseService<EmailAccount> {
    /**
     * 新增或修改
     * @param emailAccount
     * @return
     */
    int save(EmailAccount emailAccount);

    /**
     * 根据id批量删除
     * @param list
     * @return
     */
    int delBatch(List<EmailAccount> list);

	EmailAccount getDefaultVerificationAccount(Integer orgNum);
}
