package com.eip.facade.crm.entity;

import com.eip.common.util.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Table(name = "t_workday")
public class HoliDayWorkDay implements Serializable {

	private static final long serialVersionUID = -356711357168730155L;

	@Id
	@Column(name = "f_id")
	private Integer id;

	@Column(name = "f_holiday_name")
	private String holidayName;//节日名称

	@Column(name = "f_is_holiday")
	private Boolean isHoliday;//是否节假日

	@Column(name = "f_holiday_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date holidayDate;//日期

	@Column(name = "f_year_date")
	private Integer yearDate;//年份

	@Column(name = "f_org_num")
	private Integer orgNum;//机构号


	/**
	 * 日期格式化时间
	 */
	@Transient
	private String formatHolidayDate;


	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getHolidayName() {
		return holidayName;
	}
	public void setHolidayName(String holidayName) {
		this.holidayName = holidayName;
	}
	public Boolean getIsHoliday() {
		return isHoliday;
	}
	public void setIsHoliday(Boolean isHoliday) {
		this.isHoliday = isHoliday;
	}
	public Date getHolidayDate() {
		return holidayDate;
	}
	public void setHolidayDate(Date holidayDate) {
		this.holidayDate = holidayDate;
	}
	public Integer getYearDate() {
		return yearDate;
	}
	public void setYearDate(Integer yearDate) {
		this.yearDate = yearDate;
	}

	public Integer getOrgNum() {
		return orgNum;
	}

	public void setOrgNum(Integer orgNum) {
		this.orgNum = orgNum;
	}

	public String getFormatHolidayDate() {
		if(holidayDate == null) return null;
		formatHolidayDate = DateUtil.date2Str(holidayDate,DateUtil.YYYY_MM_DD);
		return formatHolidayDate;
	}

	public void setFormatHolidayDate(String formatHolidayDate) {
		this.formatHolidayDate = formatHolidayDate;
	}
}
