package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.ClientBank;
import com.eip.facade.crm.entity.Operator;


public interface ClientBankService extends BaseService<ClientBank>{

	int save(ClientBank clientBank, OperatorDto operator);

    int saveInvoiceInfoByClientId(Integer clientId, String invoiceInfor);

}
