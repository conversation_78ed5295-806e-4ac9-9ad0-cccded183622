package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.Translate;


public interface TranslateService {
	
	int insert(Translate e);
	
	int update(Translate e);
	
	int delete(Integer id);
	
	int deleteBatch(List<Translate> list);
	
	List<Translate> select(PageParam pageParam, Translate e);
	
	int count(Translate e);
	
	Translate selectById(Integer id);
	
	Translate getByProjectId(int id);

}
