package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ProdCurrency;

public interface ProdCurrencyService {
	
	 	int insert(ProdCurrency e);
		
		int update(ProdCurrency e);
		
		int delete(Integer id);
		
		int deleteBatch(List<ProdCurrency> list);
		
		List<ProdCurrency> select(PageParam pageParam, ProdCurrency e);
		
		int count(ProdCurrency e);
		
		ProdCurrency selectById(Integer id);

		String selectOrInsert(PageParam pageParam, ProdCurrency prodCurrency);

		int updateBatch(List<ProdCurrency> prodCurrencyList);
		
		List<ProdCurrency> selectByMap(Map<String,Object> map);

		ProdCurrency selectCurrency(Integer projectId, String type);

	
}
