package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.entity.Statistics;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.*;
import com.eip.facade.crm.vo.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

public interface StatisticsService {

	List<StatisticsClientVo>  clientDataStatistics(StatisticsQueryDto statisticsQueryDto);

	List<StatisticsFieldVo> queryTouchStateField(Integer f_org_num, Integer workTeamId);

	List<StatisticsTouchVo> touchDataStatistics(StatisticsQueryDto statisticsQueryDto);

	List<StatisticsFieldVo> queryTouchSubjectField(Integer f_org_num);

	List<StatisticsTouchVo> touchSubjectStatistics(StatisticsQueryDto statisticsQueryDto);

	List<StatisticsFieldVo>  queryClientTradeFiled(Integer orgNum,Integer workTeamId);

	List<StatisticTradeVo> clientTradeStatistics(StatisticsQueryDto statisticsQueryDto);

	List<Statistics> orderStatistics(List<Integer> operatorIds,Integer workTeamId,Integer orgNum);

	List<Statistics> receiptStatistics(List<Integer> operatorIds,Integer workTeamId,Integer orgNum);

	Map<String,Object> clientTrade(StatisticsQueryDto statisticsQueryDto);

	Map<String, Object> invite(InviteQueryDto inviteQueryDto);

    AjaxResponse salesSummary(PageParam pageParam, StatisticsQueryDto statisticsQueryDto);

	AjaxResponse salesSummaryAsync(PageParam pageParam, StatisticsQueryDto statisticsQueryDto);

	AjaxResponse selectProjectReceivableSummary(PageParam pageParam, StatisticsQueryDto statisticsQueryDto);

	AjaxResponse projectReceivableSummaryAsync(PageParam pageParam1, StatisticsQueryDto statisticsQueryDto);

    Map<String, Object> getProjectSalesAccount(PageParam pageParam, ProjectSalesAccountDto projectSalesAccountDto);

    List<StatisticsInviteAndRegVo> getInviteAndRegAccount(StatisticsQueryDto statisticsQueryDto);

	AjaxResponse selectSalesPerformanceAccount(StatisticsQueryDto statisticsQueryDto);

    List<Statistics> contractProjClientStatistics(StatisticsQueryDto statisticsQueryDto);

	/**
	 * 导出尾款数据通知为PDF
	 * @param statisticsQueryDto
	 * @return
	 */
	List<ExportBalancePayMentVo> selectSalesSummary(StatisticsQueryDto statisticsQueryDto);
	/**
	 * 个人邀请任务完成情况报表
	 * 将以业务员为主体，统计业务员的关联了【邀请个人】联系记录的工单的完成情况。
	 * @param inviteQueryDto
	 * @return
	 */
    AjaxResponse inviteTask(InviteQueryDto inviteQueryDto);

	/**
	 * 项目邀请观众情况
	 * @param inviteQueryDto
	 * @return
	 */
	AjaxResponse inviteBuyerCircumstance(InviteQueryDto inviteQueryDto);

	/**
	 * 参展申请渠道统计
	 * @param statisticsQueryDto
	 * @return
	 */
	AjaxResponse traderRegChannel(StatisticsQueryDto statisticsQueryDto);

	AjaxResponse supplierSummary(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse supplierSummaryAsync(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectPurchaseSummary(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectPurchaseSummaryAsync(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectPurchaseLedger(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectPurchaseLedgerAsync(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectReceiptAndPayment(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectReceiptAndPaymentAsync(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projectServiceProcess(PageParam pageParam, ProjectServiceDto projectServiceDto);

	AjaxResponse projectServiceProcessAsync(PageParam pageParam, ProjectServiceDto projectServiceDto);

	AjaxResponse projectServiceTask(PageParam pageParam, ProjectServiceDto projectServiceDto);

	AjaxResponse projectServiceTaskAsync(PageParam pageParam, ProjectServiceDto projectServiceDto);

	AjaxResponse getProjectServiceTaskKind(Integer orgNum);

	AjaxResponse projClientServeBooth(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projClientServeBoothAsync(PageParam pageParam, SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse projClientOrderProduct(SupplierStatisticsDto supplierStatisticsDto);

	AjaxResponse clientOrderByServeBoothId(SupplierStatisticsDto supplierStatisticsDto);
}
