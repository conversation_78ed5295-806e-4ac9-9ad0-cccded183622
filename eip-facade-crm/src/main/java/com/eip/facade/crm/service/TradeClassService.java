package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.ProductTypeBase;
import com.eip.facade.crm.entity.TradeClass;
import com.eip.facade.crm.vo.TreeVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface TradeClassService {
	
	int count(TradeClass e);

	List<TradeClass> select(PageParam pageParam, TradeClass e);

	/*int insert(TradeClass e);

	int update(TradeClass e);
	
	int delete(Integer id);
	
	int deleteBatch(List<TradeClass> list);*/
	
	TradeClass selectById(Integer id);


	List<TradeClass> selectByMap(Map<String, Object> map);

	List<TradeClass> getTree(TradeClass tradeClass);

	List<TradeClass> selectFistNodeName(Integer orgNum,Integer workTeamId);

    Integer queryChildLevel(TradeClass tradeClass);

	AjaxResponse save(TradeClass tradeClass, OperatorDto operatorDto);

	AjaxResponse delete(TradeClass tradeClass, OperatorDto operatorDto);

	List<TradeClass> getAll(TradeClass tradeClass);

	List<TreeVo> getTradeAndProductType(Integer orgNum);

	AjaxResponse push(TradeClass tradeClass, OperatorDto operatorDto);

	List<TradeClass> getSubsetTradeClassTree(TradeClass tradeClass);

	PageInfo<ProductTypeBase> getProductTypePage(PageParam pageParam, ProductTypeBase productTypeBase);

}
