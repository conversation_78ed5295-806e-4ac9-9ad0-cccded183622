package com.eip.facade.crm.service;

import java.util.List;

import com.eip.facade.crm.entity.ExhibitTool;
import com.eip.facade.crm.entity.ToolDetial;

public interface ExhibitToolService {

	List<ExhibitTool> select();
	
	int insert(ExhibitTool ExhibitTool);
	
	int update(ExhibitTool ExhibitTool);
	
	int delete(Integer exhibitToolId);
	
	int deleteBatch(List<ExhibitTool> list);
	
	List<ToolDetial> init();
	
}
