package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.ExhibitSettingDto;
import com.eip.facade.crm.dto.ExhibitionParamDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.ExhibitSetting;
import com.eip.facade.crm.entity.Exhibition;
import com.eip.facade.crm.vo.AreaVo;
import com.eip.facade.crm.vo.TreeVo;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface ExhibitionService {
	
	/*int count(Boolean key, String exihibitName, String exhibitPlace, String yearMonth, int f_org_num);
	
	public int getSysId(String fieldCode, Boolean increaseMode);

	List<Exhibition> select(PageParam pageParam, Boolean key, String exihibitName, String exhibitPlace, String yearMonth, int f_org_num);

	int insert(Exhibition exhibition);
	
	int update(Exhibition exhibition);*/
	
	AjaxResponse delete(String  exhibitCode,OperatorDto operatorDto);

	Map<String,Object> checkExhibitionBusiness(String exhibitCode);

	AjaxResponse deleteExhibition(String exhibitCode, OperatorDto operatorDto);
	
	/*int deleteBatch(List<Exhibition> list);*/
	
	/*Boolean isExistExhibitCode(int id, String exhibitCode);
	
	int countByState(Boolean key, String exihibitName, String exhibitPlace, String yearMonth, Integer exhibitState, Integer f_org_num);
	
	List<Exhibition> selectByState(PageParam pageParam, Boolean key, String exihibitName, String exhibitPlace, String yearMonth, Integer exhibitState, Integer f_org_num);

	int updateState(Integer id, Integer exhibitState);
	
	int approve(Integer id);*/
	
	Exhibition selectByExhibitCode(String exhibitCode, Map<String, Object> paramMap);
	/*
	int updateLevel(int exhibitLevel, String exhibitCode);
	
	int openMap(Integer id);
	
	int closeMap(Integer id);
	
	int insertTools(List<ExhibitHaveTools> list);

	int deleteBatchTools(List<Tool> list, String exhibitCode);
	
	List<Exhibition> GetSelectOne(Map<String, Object> paramMap);*/
	
	List<Exhibition> selectAll(Map<String, Object> paramMap);
	
	/*int release(String exhibitCode);
	
	int unrelease(String exhibitCode);*/

	List<Exhibition> selectByMap(Map<String, Object> map);

	List<ExhibitSetting> selectExhibitSetting(Map<String, Object> paramMap);

	int saveExhibitSetting(List<ExhibitSetting> exhibitSetting);

	/*int updateTools(List<ExhibitHaveTools> list);*/

    void insertExhibitSetting(ExhibitSetting es);

	void updateExhibitSetting(ExhibitSetting es);

	void deleteByTaskKindCode(String taskKindCode, Integer exhibitCode, Integer teamId);

	int selectExhibitSettingCount(Map<String, Object> paramMap);

    String getExhibitNameByProjectId(Integer projectId);

	Exhibition getByProjectId(Integer projectId);

	PageInfo<Exhibition>  getPage(PageParam pageParam, ExhibitionParamDto exhibitionParamDto);

	List<TreeVo> getAllYear(Map<String, Object> map);

	List<AreaVo> getArea(Map<String, Object> map);

	int create(Exhibition exhibition, OperatorDto operatorDto);

    List<Exhibition> selectByExhibitName(String exihibitName, Integer orgNum);

    List<ExhibitSetting> queryExhibitSetting(ExhibitSettingDto exhibitSettingDto);

	Exhibition selectExhibitByCode(String exhibitCode);
}
