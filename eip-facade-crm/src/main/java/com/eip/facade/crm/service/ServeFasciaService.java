package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ServeFascia;

public interface ServeFasciaService {
	
	int insert(Integer boothInfoId, ServeFascia serveFascia);
	
	int update(ServeFascia serveFascia);
	
	ServeFascia selectByServeFasciaId(Integer serveFasciaId);
	
	int count(int key, Integer projectId);
	
	List<ServeFascia> select(PageParam pageParam, int key, Integer projectId);

	List<ServeFascia> selectByMap(Map<String, Object> map);

	int saveOrUpdate(ServeFascia serveFascia);

	int count(ServeFascia serveFascia);
	
	List<ServeFascia> select(PageParam pageParam, ServeFascia serveFascia);
	
}
