package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.*;
import com.eip.facade.crm.entity.*;
import com.eip.facade.crm.vo.*;
import com.github.pagehelper.PageInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ServeBoothService {

	int insert(ServeBooth serverBooth);
	/*
	int update(ServeBooth serverBooth);
	
	List<ServeBooth> selectByMap(Map<String, Object> map);

	int saveServeBooth(ServeBooth serveBooth);

	int count(int key, Map<String, Object> map);

	List<ServeBooth> select(PageParam pageParam, int key, Map<String, Object> map);

	*/
	int delete(int serveBoothId);

	AjaxResponse deleteBatch(List<ServeBooth> serveBooths,OperatorDto operatorDto);
	/*
	PageInfo<ServeBooth> selectByInformationSummary(Integer page, Integer rows, Integer projectId, Integer clientId, String boothNum);

	List<ServeBoothDtl> selectDtlAndTrip(Integer serveBoothId);

	int existExhibitor(ServeBooth serveBooth);
	*/
	ServeBooth selectById(Integer serveBoothId);

	HashMap<String,Object> getPage(PageParam pageParam, ServeBoothDto serveBoothDto);

	ServeBooth getMoreById(Integer serveBoothId);

	AjaxResponse save(ServeBooth serveBooth,OperatorDto operatorDto);

	Map<String,Object> confirm(ServeBooth serveBooth);

	int batchEnableBookRule(ServeBoothDto serveBoothDto);

	HashMap<String,Object> getPageAsync(PageParam pageParam, ServeBoothDto serveBoothDto);

	int updateServeBooth(ServeBoothDto serveBoothDto);

	int countExistServeBooth(Map<String, Object> map);

	int importData(ServeBoothImportParamDto importParamDto, ServeBoothImportDto importDto);

	/**
	 * 导入合同时处理展位信息
	 * @param contract
	 * @param importParamDto
	 * @param batchDtlList
	 * @return
	 */
	int importContractHandleBooth(Contract contract,ImportParamDto importParamDto,List<BatchDtl> batchDtlList);

	List<String> selectExhibitProduct(ServeBoothDto serveBoothDto);

	List<ServeBooth> selectByClientAndProject(Map<String, Object> hashMap);

	int updateServeBoothById(ServeBooth serveBooth);

	int updateServeBoothSaleState(ServeBooth serveBooth);

	int sp_del_task_and_serve(Integer serveBoothId);

	int updateServeBoothByContractInvalid(Integer serveBoothId);

	int resetContractId(List<Contract> contractList);

	int updateBoothNumById(Map<String, Object> parammap);

	AjaxResponse queryExhibitorInfo(ServeBoothDto serveBoothDto);

	/**
	 * 根据合同更新参展记录
	 * @param contract
	 * @param serveBooth
	 * @return
	 */
	int updateByContract(Contract contract, ServeBooth serveBooth);

	/**
	 * 合同生效时 重新更新展位相关信息
	 * @param newUpdateContract
	 * @param serveBooth
	 * @return
	 */
	int updateBoothInfo(Contract newUpdateContract, ServeBooth serveBooth);
	/**
	 * 为serveBooth 数据里赋值 客户相关信息
	 * @param serveBooth
	 * @param client
	 */
	void buildCompanyData(ServeBooth serveBooth, Client client);
	/**
	 * 为serveBooth 数据里赋值 公司、联系人和
	 * @param serveBooth
	 * @param contract
	 */
	void buildClientData(Contract contract, ServeBooth serveBooth);
	/**
	 * 为serveBooth 数据里赋值 展位相关信息
	 * @param serveBooth
	 * @param contract
	 */
	void buildBoothData(Contract contract, ServeBooth serveBooth);

    AjaxResponse batchHandOver(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	ServeBooth selectServeBooth(ServeBoothDto serveBoothDto);

	int updateSaleStateById(ServeBooth serveBooth);


	List<ServeBoothVo> queryServeBooth(Map<String, Object> paramMap);

	/**
	 * 批量启动服务
	 * @param serveBoothVos
	 * @param operatorDto
	 * @return
	 */
    int batchGenerateTaskDetail(List<ServeBoothVo> serveBoothVos, OperatorDto operatorDto,List<TaskDtlDto> taskDtlDtoList);

	/**
	 * 根据选择的任务Id启动对应的项目服务任务
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse generateTaskDetail(ServeBoothDto serveBoothDto,OperatorDto operatorDto);


	/**
	 * 确认参展
	 * @param serveBooth
	 * @return
	 */
	AjaxResponse confirmExhibitorInfo(ServeBooth serveBooth,OperatorDto operatorDto);

	/**
	 * 获取行程
	 * @param serveBoothId
	 * @return
	 */
    List<ServeBoothDtl> selectDtlAndTrip(Integer serveBoothId);

	/**
	 * 参展人员需求
	 * @param serveBoothDto
	 * @return
	 */
	int saveDtlAndTrip(ServeBoothDto serveBoothDto);
	/**
	 * 参展需求申请
	 * @param serveBoothDto
	 * @return
	 */
	int saveBoothNeed(ServeBoothDto serveBoothDto);

	/**
	 * 查询确认参展的展商
	 * @param apiClientQueryDto
	 * @return
	 */
	List<PromoterInfoVo> getConfirmExhibitor(ApiClientQueryDto apiClientQueryDto);

	/**
	 * 参展详情添加展位
	 * @param serveBoothDto
	 * @return
	 */
    AjaxResponse saveBoothToServeBoothDtl(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 参展记录详情批量更新展位状态
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse updateExhibitorBoothSaleState(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 根据参展记录中的所属项目ID查询符合的项目任务数据
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse selectProjectServeTask(ServeBoothDto serveBoothDto);

	/**
	 * 选择确认的展位
	 * @param serveBoothDto
	 * @return
	 */
	List<ExhibitBoothVo> selectConfirmBoothList(ServeBoothDto serveBoothDto);

	/**
	 * 退展
	 * @param serveBoothDto
	 * @return
	 */
    AjaxResponse chargeBackBooth(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 处理展商参展中关联的项目任务服务工单(中止或作废或不处理)
	 * @param taskDtlDtoList
	 * @return
	 */
	int dealWithTaskState(List<TaskDtlDto> taskDtlDtoList,OperatorDto operatorDto);

	/**
	 * 批量确认展位
	 * @param map
	 * @return
	 */
	int batchConfirmBooth(Map<String, Object> map);

	/**
	 * 删除展商详情中展位列表无业务逻辑的展位
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse deleteFocusBooth(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 解绑会员
	 * @param serveBoothDto
	 * @return
	 */
	int unbindServeBoothZzyUser(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 查询单个展商所有联系人
	 * @param serveBoothDto
	 * @return
	 */
	List<Map<String, Object>> selectClientLinkmanInfo(ServeBoothDto serveBoothDto);

	/**
	 * 指定会员到展商参展记录
	 * @param serveBoothDto
	 * @return
	 */
    AjaxResponse updateAppointServeBoothZzyUser(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

    AjaxResponse autoBuildExhibitorTask(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 查询公司合并后是否存在重复的参展记录
	 * @param serveBoothDto
	 * @return
	 */
	List<ServeBoothRepeatVo> queryExistRepeatServeBooth(ServeBoothDto serveBoothDto);

	/**
	 * 合并重复的参展记录
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse mergingRepeatServeBooth(ServeBoothDto serveBoothDto,OperatorDto operatorDto);

	/**
	 * 查询展商是否启动了项目任务服务
	 * @param serveBoothDto
	 * @return
	 */
	List<TaskDetailVo> selectTaskServiceStatus(ServeBoothDto serveBoothDto);

    int sp_merge_replace_zzyuser_id(Integer orgNum,Integer mainZzyUserId, String mergerZzyUserIds);

	int updateServeBoothClientInfoById(Map<String, Object> paramMap);

	AjaxResponse batchInitZzyUserPassWord(List<ServeBoothVo> serveBoothVoList, OperatorDto operatorDto, String initPassWord);

	Map<String,Object> updateZzyuserMemberGrade(List<ServeBoothVo> serveBoothVos, ServeBoothDto serveBoothDto, OperatorDto operatorDto);

	AjaxResponse cancelCustomerService(Integer serveBoothId, OperatorDto operatorDto);

	AjaxResponse batchCancelCustomerService(List<ServeBoothVo> serveBoothVos, ServeBoothDto serveBoothDto, OperatorDto operatorDto);

	/**
	 * 根据参展记录ID重置合同关联
	 * @param serveBoothId
	 * @return
	 */
	int updateServeBoothContractRefById(Integer serveBoothId);

	/**
	 * 合同更新工单关联展位
	 * @param serveBoothId 参展记录Id
	 * @param beforeContractBoothList 保存前展位
	 * @param endContractBoothList 保存后展位
	 * @return
	 */
	int updateTaskDtlRefBoothFromContract(Integer serveBoothId,Boolean isPayment,List<ExhibitBooth> beforeContractBoothList, List<ExhibitBooth> endContractBoothList);

	/**
	 * 检查参展记录是否已存在会员
	 * @param serveBoothDto
	 * @return
	 */
	AjaxResponse checkServeBoothExistZzyUser(ServeBoothDto serveBoothDto);

	/**
	 * 新增合同、销售订单、收款单时判断是否存在参展记录
	 * @param businessType 1 合同 2 销售订单 3 收款单
	 * @param lockKeyId 主键Id 用来加锁
	 * @param businessObj 根据 业务类型区分实体
	 * @param operatorDto 操作实体
	 * @return
	 */
    Map<String,Object> insertOrUpdateServerBooth(Integer businessType,Integer lockKeyId, Object businessObj, OperatorDto operatorDto);

	/**
	 * 查询当前机构设置的项目任务
	 * @param serveBoothDto
	 * @return
	 */
	List<Map<String,Object>> getProjectTaskCombinedQuery(ServeBoothDto serveBoothDto);

	/**
	 * 创建企业会员（同时绑定在线企业）
	 * @param serveBoothDto
	 * @param operatorDto
	 * @return
	 */
    AjaxResponse insertEnterpriseMember(ServeBoothDto serveBoothDto, OperatorDto operatorDto);

	AjaxResponse handlerServeBoothEnterpriseRelation(ServeBoothDto serveBoothDto,ServeBooth serveBooth, OperatorDto operatorDto);

	AjaxResponse handlerServeBoothEnterpriseMemberRelation(ServeBoothDto serveBoothDto,ServeBooth serveBooth, OperatorDto operatorDto);

	/**
	 * 批量创建企业个人会员
	 * @param serveBoothDto
	 * @param operatorDto
	 * @return
	 */
	AjaxResponse insertEnterprisePersonalMemberForBatch(ServeBoothDto serveBoothDto, OperatorDto operatorDto);

	/**
	 * 创建企业个人会员
	 * @param serveBoothDto
	 * @param operatorDto
	 * @return
	 */
	AjaxResponse insertEnterprisePersonalMember(ServeBoothDto serveBoothDto, OperatorDto operatorDto);

	int updateEnterprisePersonalMemberById(Integer serveBoothId, Integer zzyUserId);

	PageInfo<ServeBoothVo> getExhibitor(PageParam pageParam, ServeBoothDto serveBoothDto);

	List<PreorderFocusBooth> findAllExhibitBooth(Integer serveBoothId);

    AjaxResponse checkForDuplicateData(Integer serveBoothId);

    AjaxResponse selectCollectExhibitorOrderDtl(Integer serveBoothId);
}
