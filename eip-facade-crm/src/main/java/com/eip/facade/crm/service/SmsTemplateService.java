package com.eip.facade.crm.service;

import com.eip.facade.crm.entity.SmsTemplate;

public interface SmsTemplateService {
    /**
     * 查询短信模板
     * @param smsTemplate
     * @return
     */
    SmsTemplate selectSmsTemplate(SmsTemplate smsTemplate);

    /**
     * 插入一个短信模板
     * @param smsTemplate
     * @return
     */
    int insertSmsTemplate(SmsTemplate smsTemplate);

    /**
     * 更新短信模板
     * @param smsTemplate
     * @return
     */
    int updateByTemplateId(SmsTemplate smsTemplate);
}
