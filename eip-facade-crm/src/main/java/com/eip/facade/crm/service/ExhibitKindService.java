package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ExhibitKind;

public interface ExhibitKindService {

	List<ExhibitKind> selectAll();

	int count(int key,  ExhibitKind exhibitKind);

	List<ExhibitKind> select(PageParam pageParam, int key,  ExhibitKind exhibitKind);

	int delete(int exhibitKindId);

	int insert(ExhibitKind exhibitKind);

	int update(ExhibitKind exhibitKind);

	int deleteBatch(List<ExhibitKind> exhibitKinds);

	List<ExhibitKind> selectByMap(Map<String, Object> map);
	


	
}
