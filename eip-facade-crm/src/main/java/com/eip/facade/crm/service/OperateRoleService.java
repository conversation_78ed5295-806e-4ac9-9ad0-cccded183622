package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.OperateRole;
import com.eip.facade.crm.entity.RoleFunction;
import com.github.pagehelper.PageInfo;

public interface OperateRoleService {

	int count(Boolean key, String keyword);
	
	List<OperateRole> select(PageParam pageParam, Boolean key, String keyword);
	
	List<OperateRole> selectBasicRole();
	
	public int getSysId(String fieldCode, Boolean increaseMode);
	
	int insert(OperateRole operateRole, List<RoleFunction> list);
	
	int update(OperateRole operateRole, List<RoleFunction> list);
	
	int delete(Integer operRoleId);
	
	int deleteBatch(List<OperateRole> list);
	
	List<OperateRole> selectByOperatorId(Integer operatorId);
	
	List<OperateRole> selectByKeyword(String keyword);

	PageInfo<OperateRole> getPage(PageParam pageParam, OperateRole operateRole);

    String getNameById(Integer operRoleId);
}
