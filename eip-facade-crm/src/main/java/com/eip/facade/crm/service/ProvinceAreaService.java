package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.service.BaseService;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.dto.ProvinceAreaDto;
import com.eip.facade.crm.entity.ProvinceArea;
import com.github.pagehelper.PageInfo;

public interface ProvinceAreaService extends BaseService<ProvinceArea> {

    PageInfo<ProvinceArea> getList(PageParam pageParam, ProvinceAreaDto provinceAreaDto);

    int save(ProvinceAreaDto provinceAreaDto, OperatorDto operatorDto);

    ProvinceArea getByProvince(String province, Integer f_org_num);
}
