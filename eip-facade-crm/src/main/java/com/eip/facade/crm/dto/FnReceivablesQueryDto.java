package com.eip.facade.crm.dto;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.eip.facade.crm.entity.FnReceivables;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
public class FnReceivablesQueryDto extends FnReceivables{
	

	private static final long serialVersionUID = -8285456461056967591L;
	
	/**
     * 记账日期 起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bookDateStart;
    /**
     * 记账日期 止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bookDateEnd;

    /**
     * 团队客户id
     */
    private Integer workTeamId;

    /**
     * 当前操作员id
     */
    private Integer operatorId;
    /**
     * 项目id，多个用英文逗号隔开
     */
    private String projectIds;
    /**
     * 操作员ids
     */
    private List<Integer> operatorIds;
    /**
     * 收款单id
     */
    private Integer receiptId;
}
