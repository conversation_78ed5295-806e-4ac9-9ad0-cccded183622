package com.eip.facade.crm.service;

import java.util.List;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ProjVisaData;


public interface ProjVisaDataService {
	
	int insert(ProjVisaData e);
	
	int update(ProjVisaData e);
	
	int delete(Integer id);
	
	int deleteBatch(List<ProjVisaData> list);
	
	List<ProjVisaData> select(PageParam pageParam, ProjVisaData e);
	
	int count(ProjVisaData e);
	
	ProjVisaData selectById(Integer id);

	int insertBatch(List<ProjVisaData> list);

	int updateBatch(List<ProjVisaData> list);
	
	
}
