package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.crm.dto.DialCallDto;

/**
 * <AUTHOR>
 * @company ：杭州展之科技
 * @date ：Created in 2022-03-11 16:12
 * @description：电话呼出和呼入统计
 */
public interface DialCallLogService {
    AjaxResponse getPage(PageParam pageParam, DialCallDto dialCallDto);

    AjaxResponse getPageAsync(PageParam page_param, DialCallDto dialCallDto);
}
