package com.eip.facade.crm.service;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.dto.FnPayablesQueryDto;
import com.eip.facade.crm.dto.OperatorDto;
import com.eip.facade.crm.entity.FnPayables;
import com.eip.facade.crm.vo.AmountVo;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;

public interface FnPayablesService {
    /**
     * 分页查询
     * @param pageParam
     * @param fnPayablesQueryDto
     * @return
     */
    PageInfo<FnPayables> getPage(PageParam pageParam, FnPayablesQueryDto fnPayablesQueryDto);
    /**
     * 分页查询 (dubbo 异步调用)
     * @param pageParam
     * @param fnPayablesQueryDto
     * @return
     */
    PageInfo<FnPayables> getPageAsync(PageParam pageParam, FnPayablesQueryDto fnPayablesQueryDto);

    /**
     * 统计总金额
     * @param fnPayablesQueryDto
     * @return
     */
    BigDecimal sumAmountRmbAsync(FnPayablesQueryDto fnPayablesQueryDto);

    /**
     * 根据id查询
     * @param payablesId
     * @return
     */
    FnPayables getById(Integer payablesId);

    /**
     * 根据id删除
     * @param payablesId
     * @return
     */
    int delete(Integer payablesId);

    /**
     * 修改（其他应付）
     * @param fnPayables
     * @return
     */
    int updateOther(FnPayables fnPayables, OperatorDto operator);

    /**
     * 新增
     * @param fnPayables
     * @return
     */
    int insert(FnPayables fnPayables,OperatorDto operator);

    /**
     * 外币总额
     * @param fnPayablesQueryDto
     * @return
     */
    List<AmountVo> sumAmountForeignAsync(FnPayablesQueryDto fnPayablesQueryDto);
}
