package com.eip.facade.crm.service;

import java.util.List;
import java.util.Map;

import com.eip.common.entity.PageParam;
import com.eip.facade.crm.entity.ExhibitHaveTools;
import com.eip.facade.crm.entity.ProjTrip;
import com.eip.facade.crm.entity.ServePerson;
import com.eip.facade.crm.entity.ServePersonDtl;
import com.eip.facade.crm.entity.ServeTool;
import com.eip.facade.crm.entity.ServeToolDtl;
import com.eip.facade.crm.entity.VisaDtlList;
public interface ServePersonService {

	int insert(Integer staffId, ServePerson servePerson);
	
	int update(ServePerson servePerson);
	
	ServePerson selectByServePersonId(Integer servePersonId);
	
	int getSysId(String fieldCode, Boolean increaseMode);
	
	int count(int key, Integer projectId, Integer clientId, String name);
	int countDtl(int key, Integer projectId, Integer clientId, String name);
	
	List<ServePerson> select(PageParam pageParam, int key, Integer projectId, Integer clientId, String name);
	List<ServePersonDtl> selectDtl(PageParam pageParam, int key, Integer projectId, Integer clientId, String name);
	
	int countToolDtlList(int key, Integer projectId, Integer clientId, String name);
	List<ServeToolDtl> selectToolDtlList(PageParam pageParam, int key, Integer projectId, Integer clientId, String name);

	int getToolsCountNotInClient(Integer projectId, Integer clientId, String name);

	List<ExhibitHaveTools> getToolsNotInClient(PageParam pageParam, Integer projectId, Integer clientId, String name);

	int getToolsCountByProjectId(Integer projectId, String name);

	List<ExhibitHaveTools> getToolsByProjectId(PageParam pageParam, Integer projectId, String name);

	int deleteBatch(List<ServePersonDtl> servePersonDtlList);

	int insertDtl(ServePersonDtl servePersonDtl);

	int updateDtl(ServePersonDtl servePersonDtl);

	List<ServePersonDtl> selectDtlNotEqualId(ServePersonDtl servePersonDtl);

	int countHotel(ServePersonDtl servePersonDtl);

	List<ServePersonDtl> selectHotel(PageParam pageParam, ServePersonDtl servePersonDtl);

	//ServePersonDtl selectAllPrice(Map<String, Object> map);
	
	List<ServePersonDtl> selectAllPrice(Map<String, Object> map);

	List<ProjTrip> selectTrip(Integer projectId, Integer clientId);



	int IsAllowTrip(ServePersonDtl servePersonDtl);

	int countDtl(ServePersonDtl servePersonDtl);

	List<ServePersonDtl> selectDtl(PageParam pageParam, ServePersonDtl servePersonDtl);

	int insertDtl(ServePersonDtl servePersonDtl, VisaDtlList visaDtlList);

	int updateDtl(ServePersonDtl servePersonDtl, VisaDtlList visaDtlList);

	
}
