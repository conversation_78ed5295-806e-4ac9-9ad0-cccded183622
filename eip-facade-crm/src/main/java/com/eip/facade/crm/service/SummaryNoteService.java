package com.eip.facade.crm.service;

import com.eip.common.service.BaseService;
import com.eip.facade.crm.entity.SummaryNote;

import java.util.List;
import java.util.Map;

/**
 * @description: 日月周总结批复
 * @author： hfl
 * @date：2021-11-05 9:08
 * @File: SummaryNoteService
 */
public interface SummaryNoteService extends BaseService<SummaryNote> {

    int editeSummaryNote(SummaryNote summaryNote);

    List<SummaryNote> selectSummaryNoteById(Map<String, Object> paramMap);

    int addSummaryNote(SummaryNote summaryNote);
}
