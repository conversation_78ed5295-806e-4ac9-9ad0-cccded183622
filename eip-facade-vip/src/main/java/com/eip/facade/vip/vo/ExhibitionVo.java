package com.eip.facade.vip.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;


@Data
public class ExhibitionVo implements Serializable {

    /**
     * 展会代号
     */
    private String exhibitCode;
    /**
     * 展会名称
     */
    private String exhibitName;
    /**
     * 展会简称
     */
    private String exhibitAbbr;

    /**
     * 机构号
     */
    private Integer orgNum;
    /**
     * 主项目id
     */
    private Integer projectId;
    /**
     * 默认短信账号id
     */
    private Integer smsAccountId;
    /**
     * 默认短信签名
     */
    private String smsSign;
    /**
     * 默认邮箱账号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emailAccountId;
}
