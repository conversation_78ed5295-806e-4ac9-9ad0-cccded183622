/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-07-01 10:13
 * @description：
 */
package com.eip.facade.vip.vo;

import com.eip.facade.vip.entity.ProjectPic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-07-01 10:13
 * @description：
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectVo implements Serializable {
    private static final long serialVersionUID = 9192030827441751978L;

    /**
     * 项目ID
     */
    private Integer projectId;

    private String exhibitCode;

    private String exhibitName;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类别
     */
    private String projKindCode;

    /**
     * 项目类别
     */
    private String projKindCodeName;

    /**
     * 创建者
     * 行业网站  会员ID
     * 内部系统 业务员Id
     */
    private Integer createId;

    /**
     * 项目状态  立项
     */
    private Integer projectState;

    /**
     * 年份
     */
    private String yearMonth;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private  Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private  Date endTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 机构号
     */
    private Integer orgNum;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;

    /**
     * 地点
     */
    private String address;
    /**
     * 地点
     */
    private String addressEn;

    /**
     * 项目类别名
     */
    private String projKindName;
    /**
     * 项目英文名称
     */
    private String projectNameEn;
    /**
     * 项目简介
     */
    private String briefIntroduction;
    /**
     * 项目简介英文
     */
    private String briefIntroductionEn;
    /**
     * 项目图片名称
     */
    private String projectPicName;
    /**
     * 项目图片路径
     */
    private String projectPicUrl;

    /**
     * 项目背景图片名称
     */
    private String projectBgImgName;

    /**
     * 项目背景图片路径
     */
    private String projectBgImgUrl;


    /**
     * 父项目ID
     */
    private Integer parentId;
    /**
     * 行业ID
     */
    private Integer tradeId;

    /**
     * 行业名称
     */
    private String tradeName;

    /**
     * 审核
     * true 审核
     * false 未审核
     */
    private Boolean checkFlag;

    /**
     * 发布
     * true 发布
     * false 未发布
     */
    private Boolean publishFlag;

    /**
     * 展会开始标识 true  开展中  false 即将开展  取字段即将开展50天
     */
    private Boolean startExhibitFlag;

    /**
     * 即将开展还剩几天
     */
    private Long startDay;
    /**
     * 展会剩余天数 距开展12天  开展中 展会已结束
     */
    private String exhibitMsg;

    /**
     * 展会开展状态： 1 开展中  2 展会已结束  3 距开展50天  0 不显示
     */
    private Integer exhibitMsgType;

    /**
     * 行业网站展会创建者
     */
    private Integer createZzyuserId;

    /**
     * 商家信息 会刊
     */
    private MerchantVo merchantInfo;

    /**
     * 搜索关键字
     */
    private String searchKeys;

    /**
     * 项目介绍
     */
    private String projectIntroduce;


    /**
     * 参展范围
     */
    private String exhibitionScope;

    /**
     * 展馆信息
     */
    private String exhibitSectionInfo;
    /**
     * 项目创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 活动(项目)起止时间（开始）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityTimeStart;
    /**
     * 活动(项目)起止时间（结束）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityTimeEnd;

    /**
     * 项目图片
     */
    private List<ProjectPic> projectPics;

    /**
     * 项目嵌套
     */
    private List<ProjectVo> projectVos;

}
