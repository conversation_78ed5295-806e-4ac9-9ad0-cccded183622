package com.eip.facade.vip.vo.api.brandWebSiteVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @USER: zwd
 * @DATE: 2023-05-18 11:40
 * @DESCRIPTION: 展馆展区
 */
@Data
public class ExhibitSectionVo implements Serializable {
    /**
     * 展区code
     */
    private String sectionCode;
    /**
     * 展区名称
     */
    private String sectionName;

    /**
     * 展区英文名
     */
    private String sectionNameEn;

    /**
     * 父级Code
     */
    private String parentCode;

    /**
     * 展馆预览图名称
     */
    private String planeGraphName;

    /**
     * 展馆预览图链接
     */
    private String planeGraphUrl;

    /**
     * 展区平面图上传时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planeGraphUploadTime;

    /**
     * 展馆预览图名称 英文
     */
    private String planeGraphNameEn;

    /**
     * 展馆预览图链接 英文
     */
    private String planeGraphUrlEn;

    /**
     * 展区平面图上传时间 英文
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planeGraphUploadTimeEn;
}
