package com.eip.facade.vip.vo;

import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;

/**
 * 展商名录表
 *
 */
@Data
public class MerchantCataVo implements Serializable {

    private static final long serialVersionUID = 3980962802005279424L;
    /**
     * 会刊服务序号
     */
    @Id
    private Integer serveCatalogueId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司名称
     */
    private String companyNameEn;
    /**
     * 公司地址
     */
    private String companyAddress;
    /**
     * 公司地址英文
     */
    private String companyAddressEn;
    /**
     * 联系人
     */
    private String contactsName;
    /**
     * 联系人英文
     */
    private String contactsEn;
    /**
     * 职务
     */
    private String post;
    /**
     * 职务英文
     */
    private String postEn;
    /**
     * 产品类型
     */
    private String typeProduct;
    /**
     * 产品类型英文
     */
    private String typeProductEn;
    /**
     * 公司简介
     */
    private String companyProfile;
    /**
     * 公司简介英文
     */
    private String companyProfileEn;
    /**
     * 网址
     */
    private String site;
    /**
     * 店铺地址
     */
    private String storeLink;
    /**
     * 直播地址
     */
    private String liveLink;

    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 参展记录ID
     */
    private Integer serveBoothId;
    /**
     * 项目id
     */
    private Integer projectId;
    /**
     * 客户id
     */
    private Integer clientId;
    /**
     * 联系电话
     */
    private String tel;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 自定义字段键值对json字符串对象
     */
    private String customFieldJson;
    /**
     * 展位号
     */
    private String boothNum;
    /**
     * 阅读量基数
     */
    private Integer readNumInit;
    /**
     * 收藏量基数
     */
    private Integer collectNumInit;
    /**
     * logo
     */
    private String logo;
    /**
     * 是否推荐
     */
    private Integer isRecommend;
    /**
     * 展馆名
     */
    private String sectionName;
    /**
     * 展馆代码
     */
    private String sectionCode;
    private Integer productCount;
    /**
     * 公司收藏数
     */
    private Integer collectionsCount;
    /**
     * 展品收藏数量
     */
    private Integer allCollectionsCount;
    /**
     * 展会代码
     */
    private String exhibitCode;
    /**
     * 是否审核
     */
    private Integer confirm;
    /**
     * 搜索关键字
     */
    private String keyWord;
    /**
     * 会员ID
     */
    private Integer zzyUserId;
    private String pic;
    /**
     * 供需匹配关键字 逗号分隔
     */
    private String supplyAndDemand;
    /**
     * 阅读量
     */
    private Integer readNum;
    private Integer serveCompanyId;

    /**
     * 行业ID
     */
    private Integer tradeId;
    /**
     * 行业ID英文
     */
    private Integer tradeIdEn;
    /**
     * 产品类型（用于线上会展）
     */
    private Integer productTypeBase;
    /**
     * 产品类型英文（用于线上会展）
     */
    private Integer productTypeBaseEn;

    /**
     * 子项目id
     */
    private Integer childProjectId;
}
