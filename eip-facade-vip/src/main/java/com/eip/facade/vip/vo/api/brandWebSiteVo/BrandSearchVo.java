package com.eip.facade.vip.vo.api.brandWebSiteVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @USER: zwd
 * @DATE: 2024-07-24 9:30
 * @DESCRIPTION: 搜索结果
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BrandSearchVo implements Serializable {

    private static final long serialVersionUID = 3062692920831843111L;

    //==================================新闻=========================
    /**
     * 新闻ID
     */
    private Integer newsId;

    /**
     * 新闻标题
     */
    private String newsTitleName;

    /**
     * 新闻摘要
     */
    private String newsBrief;

    /**
     * 新闻内容
     */
    private String newsContent;

    /**
     * 新闻logo
     */
    private String newsLogo;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    //==================================文件下载=========================

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 文件上传时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    /**
     * 文件后缀
     */
    private String fileSuffix;
}
