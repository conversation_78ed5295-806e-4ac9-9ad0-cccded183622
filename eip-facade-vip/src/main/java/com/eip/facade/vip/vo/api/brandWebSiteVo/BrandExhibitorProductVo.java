package com.eip.facade.vip.vo.api.brandWebSiteVo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @USER: zwd
 * @DATE: 2024-07-24 16:40
 * @DESCRIPTION:
 */
@Data
public class BrandExhibitorProductVo implements Serializable {

    private static final long serialVersionUID = -9003693929970276727L;

    /**
     * 产品ID
     */
    private Integer serveProductId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品名称英文
     */
    private String productNameEn;
    /**
     * 产品规格
     */
    private String productSpec;
    /**
     * 产品简介
     */
    private String productData;
    /**
     * 产品简介英文
     */
    private String productDataEn;

    /**
     * 产品主图片
     */
    private String productMainPic;

    /**
     * 展品的图片
     */
    private List<String> productPics;
}
