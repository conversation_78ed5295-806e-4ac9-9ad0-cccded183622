/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-07-06 11:46
 * @description：
 */
package com.eip.facade.vip.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-07-06 11:46
 * @description：
 */
@Data
public class UserCompanyVo implements Serializable {
    private static final long serialVersionUID = 7577573031525728646L;


    private Integer userComId;

    /**
     * 客户Id
     */
    private Integer clientId;

    /**
     * 机构号
     */
    private Integer orgNum;

    /**
     * 会员id
     */
    private Integer zzyuserId;

    /**
     * 任务Id
     */
    private Integer cataTaskDtlId;
    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 展会code
     */
    private String exhibitCode;

    //=================公司中文信息========================
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司简称
     */
    private String companyAbbr;
    /**
     * 公司英文
     */
    private String companyNameEn;
    /**
     * 公司简称英文
     */
    private String companyAbbrEn;
    /**
     * 地址
     */
    private String address;
    /**
     * 地址英文
     */
    private String addressEn;
    /**
     * 公司简介英文
     */
    private String comIntroduceEn;
    /**
     * 公司电话
     */
    private String tel;
    /**
     * 公司邮箱
     */
    private String companyEmail;
    /**
     * 公司传真
     */
    private String fax;
    /**
     * 网址
     */
    private String site;
    /**
     * 公司简介
     */
    private String comIntroduce;

    /**
     * 统一社会信用代码
     */
    private String socialReditCode;


    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 公司logo
     */
    private String companyLogo;

    /**
     * 公司图片
     */
    private String companyPic;
    /**
     * 产品类型
     * 改成
     * 展品范围
     */
    private String typeProduct;
    /**
     * 行业分类Id
     */
    private Integer tradeId;
    /**
     * 行业分类Id英文
     */
    private Integer tradeIdEn;

    /**
     * 展商客服中心审核会刊时，同步到会员中心的行业分类Id
     */
    private Integer cataTradeId;

    //======================联系人信息=======================
    /**
     * 联系人姓名
     */
    private String contacts;
    /**
     * 联系人职务
     */
    private String position;
    /**
     * 联系人移动电话
     */
    private String mobile;
    /**
     * 联系人email
     */
    private String email;
    /**
     * 联系人英文
     */
    private String contactsEn;
    /**
     * 联系人职务英文
     */
    private String positionEn;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别
     */
    private String sex;
    /**
     * 微信
     */
    private String wechat;
    /**
     * QQ
     */
    private String qq;
    /**
     * 出生日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 爱好
     */
    private String hobby;
    /**
     * 身份证
     */
    private String idCard;

    //=============================================
    /**
     * 产品类型英文
     * 改成
     * 展品范围英文
     */
    private String typeProductEn;
    /**
     * 产品介绍
     */
    private String productIntroduce;
    /**
     * 是否作为展商关联过客户
     */
    private Boolean everFocus;
    /**
     * 是否是外商
     * true 是
     * false null  不是
     */
    private Boolean isForeign;

}
