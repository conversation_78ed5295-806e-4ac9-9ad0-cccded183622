package com.eip.facade.vip.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @USER: zwd
 * @DATE: 2023-04-14 9:48
 * @DESCRIPTION:
 */
@Data
public class ZzyuserAwokeVo implements Serializable {

    private static final long serialVersionUID = -7151379706857568540L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long zzyuserAwokeId;
    /**
     * 提醒类别
     * 1 展商参展提醒
     */
    private Integer awokeType;
    /**
     * 提醒来源
     */
    private String awokeFrom;
    /**
     * 创建人
     */
    private String senderUserId;
    /**
     * 创建发送提醒时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;
    /**
     * 被提醒人
     */
    private String receiverUserId;
    /**
     * 提醒时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reminderTime;
    /**
     * 提醒摘要
     */
    private String awokeSummary;

    /**
     * 提醒详细
     */
    private String awokeDetail;

    private Integer orgNum;

    /**
     * 主题
     */
    private String subject;
    /**
     * 留言
     */
    private String message;
    /**
     * 提醒来源或关联项目(项目ID）
     */
    private Integer awokeProjectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 提醒天数
     */
    private String awokeDayStr;
}
