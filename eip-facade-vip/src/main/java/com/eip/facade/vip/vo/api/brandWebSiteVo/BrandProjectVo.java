package com.eip.facade.vip.vo.api.brandWebSiteVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @USER: zwd
 * @DATE: 2024-07-19 14:33
 * @DESCRIPTION:
 */
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Builder
@Data
public class BrandProjectVo implements Serializable {
    private static final long serialVersionUID = 862336578062210747L;

    private String projectName;

    private String projectNameEn;

    private Integer projectId;

    private String exhibitCode;

    private String projKindCode;

    private Integer orgNum;

    private Integer parentId;

    private Boolean enableBrandExternalLinks;

    private String brandExternalLinks;

    /**
     * 项目背景图
     */
    private String projectBgImgName;
    /**
     * 项目背景图
     */
    private String projectBgImgUrl;
    /**
     * 项目图片
     */
    private String projectPicName;
    /**
     * 项目图片
     */
    private String projectPicUrl;

    /**
     * 项目简介
     */
    private String briefIntroduction;

    /**
     * 项目简介 英文
     */
    private String briefIntroductionEn;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    private String address;

    private String addressEn;

    /**
     * 品牌所属子集
     */
    private List<BrandProjectVo> brandProjectVos;

    /**
     * 品牌即开展所属展会
     */
    private Map<String,Object> startProjectInfo;

    /**
     * 展商申请信息
     */
    private ProjRegSetVo exhibitorApplyLink;

    /**
     * 观众申请链接
     */
    private ProjRegSetVo buyerRegApplyLink;

    /**
     * 展馆展区
     */
    private List<ExhibitSectionVo> planeGraphs;

    /**
     * 展馆价格说明
     */
    private String sectionPriceExplain;

    /**
     * 品牌默认项目Id
     */
    private Integer brandSelectProjectId;

    /**
     * 品牌关联项目时效  展会开始多少天前
     */
    private Integer brandProjectFilterRuleBeforeNow;

    /**
     * 品牌关联项目时效  展会结束多少天后
     */
    private Integer brandProjectFilterRuleAfterNow;

    /**
     * 品牌所属项目汇总
     */
    private List<Map<String,Object>> brandOwnerProjectCollect;

    /**
     * 所有的品牌项目显示
     */
    private List<BrandProjectVo> horizontalDisplayItems;
}
