package com.eip.facade.vip.vo.api.webSiteVo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-07-13 8:36
 */
@Data
public class ApiServeCatalogueVo implements Serializable {


    private static final long serialVersionUID = -5693533145143949473L;
    /**
     * 会刊服务序号a
     */
    private Integer serveCatalogueId;

    /**
     * 任务工单Id
     */
    private Integer taskDtlId;
    /**
     * 公司名称a
     */
    private String companyName;
    /**
     * 公司英文名称a
     */
    private String companyNameEn;
    /**
     * 公司简称
     */
    private String companyAbbr;
    /**
     * 公司地址a
     */
    private String companyAddress;
    /**
     * 公司地址英文a
     */
    private String companyAddressEn;
    /**
     * 联系电话a
     */
    private String tel;
    /**
     * 图片a
     */
    private String pic;
    /**
     * logo a
     */
    private String logo;

    /**
     * 公司logo
     */
    private String companyLogo;

    /**
     * 公司图片
     */
    private String companyPic;
    /**
     * 是否推荐a
     */
    private Integer isRecommend;
    /**
     * 公司收藏数 a
     */
    private Integer collectionsCount;
    /**
     * 展品收藏数量 a
     */
    private Integer allCollectionsCount;
    /**
     * 阅读量a
     */
    private Integer readNum;
    /**
     * 产品数量
     */
    private Integer productCount;

    /**
     * 展位号
     */
    private String boothNum;

    /**
     * 展馆
     */
    private String sectionName;

    /**
     * 展区英文
     */
    private String sectionNameEn;

    private Integer tradeId;

    private Integer tradeIdEn;

    /**
     * 行业名称
     */
    private String tradeName;

    /**
     * 行业名称英文
     */
    private String tradeNameEn;

    private Integer readNumInit;

    private Integer collectNumInit;


}
