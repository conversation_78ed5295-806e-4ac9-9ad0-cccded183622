package com.eip.facade.vip.vo.api.brandWebSiteVo;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @USER: zwd
 * @DATE: 2024-07-20 17:12
 * @DESCRIPTION:
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BrandExhibitorVo implements Serializable {
    private static final long serialVersionUID = 6656235632177573649L;

    /**
     * 展商Id
     */
    private String clientId;

    /**
     * 会刊
     */
    private Integer serveCatalogueId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司名称 英文
     */
    private String companyNameEn;

    /**
     * 公司logo
     */
    private String companyLogo;

    /**
     * 公司图片
     */
    private String companyPic;

    /**
     * 公司简介
     */
    private String companyProfile;

    /**
     * 公司简介英文
     */
    private String companyProfileEn;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 联系人名称 英文
     */
    private String linkmanNameEn;

    /**
     * 电话
     */
    private String tel;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 网址
     */
    private String site;

    /**
     * 职务
     */
    private String post;

    /**
     * 职务英文
     */
    private String postEn;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司地址英文
     */
    private String companyAddressEn;

    /**
     * 公司阅读量
     */
    private Integer companyPageView;

    /**
     * 展位号
     */
    private String boothNum;

    /**
     * 会刊关联展位
     */
    private String boothFocusIds;

    /**
     * 是否需要拦截登录
     */
    private Boolean needToLogin = false;

    /**
     * 展品
     */
    private List<BrandExhibitorProductVo> products;
}
