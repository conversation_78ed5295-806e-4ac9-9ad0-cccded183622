/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-12-10 13:35
 * @description：
 */
package com.eip.facade.vip.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-12-10 13:35
 * @description： 兑换和核销的商品结果类
 */
@Data
public class IntegralGoodsVo implements Serializable {

    /**
     * 商品Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 核销说明
     */
    private String checkOffMemo;

    /**
     * 会员ID
     */
    private Integer zzyUserId;

    /**
     * 机构号
     */
    private Integer orgNum;

    /**
     * 库存量
     */
    private Integer stock;

    /**
     * 商品有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validTime;

    /**
     * 适用功能
     * 使用编码进行
     *  BUYERREG_PAY_ORDER 观众登记支付订单
     *  BOOTH_PREORDER 展位订单
     *  CONFERENCE_PAPERS_ORDER 会议证件订单
     */
    private String applyFunction;

    /**
     * 0 未上架  1 上架
     */
    private Integer goodsState;

    /**
     * 剩余量
     */
    private Integer surplusNum;

    /**
     * 已兑换数量
     */
    private Integer exchangeNum;

    /**
     * 消耗积分
     */
    private BigDecimal consumeNum;

    /**
     * 积分类型Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long integralTypeId;

    /**
     * 积分类型名称
     */
    private String integralTypeName;

    /**
     * 限制兑换数量
     */
    private Integer limitNum;

    /**
     * 积分商品适用项目
     */
    private String limitProjectId;

    /**
     * 积分商品适用项目名称
     */
    private String limitProjectName;

    /**
     * 商品图片链接
     */
    private String goodsImgUrl;
    /**
     * 商品图片名称
     */
    private String goodsImgName;
    /**
     * 商品详情
     */
    private String goodsDetail;
    /**
     * 商品库存为零隐藏
     */
    private Boolean stockZeroHidden = Boolean.FALSE;
    /**
     * 商品有效期逾期隐藏
     */
    private Boolean goodsOverdueHidden = Boolean.FALSE;
    /**
     * 会员兑换商品总数
     */
    private Integer zzyUserPurchaseGoodsNum;
    /**
     * 会员商品已核销数量
     */
    private Integer zzyUserConfirmGoodsNum;

    /**
     * 排序号
     */
    private Integer sortNum;
}
