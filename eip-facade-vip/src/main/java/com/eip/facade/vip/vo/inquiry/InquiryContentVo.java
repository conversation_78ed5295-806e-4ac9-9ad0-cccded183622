package com.eip.facade.vip.vo.inquiry;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @USER: zwd
 * @DATE: 2025-01-15 13:48
 * @DESCRIPTION:
 */
@Data
public class InquiryContentVo implements Serializable {

    private static final long serialVersionUID = -1560841194669725611L;

    /**
     * 询盘消息ID
     */
    private Integer inquiryMsgId;

    /**
     * 询盘产品ID
     */
    private Integer commonId;

    /**
     * 发送方会员ID
     */
    private Integer senderId;

    /**
     * 消息状态（0 代表未读 1已读 3删除）
     */
    private Integer msgState;

    /**
     * 消息内容
     */
    @NotNull(message = "询盘内容不能为空")
    private String msgContent;


    /**
     * 发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 展会编号
     */
    private String exhibitCode;

    /**
     * 询盘类型
     * INQUIRY
     * CLIENT
     */
    private String inquiryType;

    /**
     * 询盘中间表Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long serveInquiryJoinId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品名称
     */
    private String productNameEn;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司名称
     */
    private String companyNameEn;

    /**
     * 询盘人名字
     */
    private String inquiryPeople;

    /**
     * 机构号
     */
    private Integer orgNum;
}
