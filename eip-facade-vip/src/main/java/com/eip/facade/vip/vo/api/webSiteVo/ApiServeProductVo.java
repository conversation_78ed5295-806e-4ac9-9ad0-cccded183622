package com.eip.facade.vip.vo.api.webSiteVo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-13 8:36
 * 产品服务Vo
 */
@Data
public class ApiServeProductVo implements Serializable {

    private static final long serialVersionUID = -4354400948054572072L;
    /**
     * 产品服务idv
     */
    private Integer serveProductId;

    /**
     * 任务工单Id
     */
    private Integer taskDtlId;
    /**
     * 产品名称v
     */
    private String productName;
    /**
     * 产品名称英文v
     */
    private String productNameEn;
    /**
     * 产品类型（用于线上会展）v
     */
    private Integer productTypeBase;
    /**
     * 产品类型英文（用于线上会展）v
     */
    private Integer productTypeBaseEn;
    /**
     * 产品规格v
     */
    private String productSpec;
    /**
     * 产品简介v
     */
    private String productData;
    /**
     * 产品简介英文v
     */
    private String productDataEn;
    /**
     * 是否推荐v
     */
    private Integer isRecommend;
    /**
     * 阅读量v
     */
    private Integer readNum;
    /**
     * 第三方链接v
     */
    private String links;
    /**
     * 阅读量基数v
     */
    private Integer readNumInit;
    /**
     * 收藏量基数v
     */
    private Integer collectNumInit;
    /**
     * 产品类型v
     */
    private String productTypeBaseName;
    /**
     * 产品类型英文v
     */
    private String productTypeBaseEnName;
    /**
     * 行业名称v
     */
    private String tradeName;
    /**
     * 行业名称英文v
     */
    private String tradeNameEn;

    /**
     * 公司名v
     */
    private String companyName;
    /**
     * 公司logov
     */
    private String companyLogo;
    /**
     * 公司pic v
     */
    private String companyPic;
    /**
     * 公司英文名v
     */
    private String companyNameEn;

    /**
     * 图片v
     * 多个以英文逗号隔开
     */
    private String pic;

    /**
     * 展品图片集合
     */
    private List<String> productPics;

    /**
     * 产品主图片
     */
    private String productMainPic;

    /**
     * 展位号
     */
    private String boothNum;

    /**
     * 展区名称
     */
    private String sectionName;


    /**
     * 展区名称 英文
     */
    private String sectionNameEn;


}
