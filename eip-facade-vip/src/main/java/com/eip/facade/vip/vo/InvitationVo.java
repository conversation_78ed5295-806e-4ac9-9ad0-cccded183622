package com.eip.facade.vip.vo;

import com.eip.facade.vip.entity.InvitationProduct;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @USER: zwd
 * @DATE: 2024-05-15 13:56
 * @DESCRIPTION: 邀约观众实体
 */
@Data
public class InvitationVo implements Serializable {

    private static final long serialVersionUID = -1231719116985416494L;

    /**
     * 邀请函Id
     */
    private Integer invitationId;

    /**
     * 邀请函所属客户Id
     */
    private Integer inviteClientId;
    /**
     * 邀请函所属项目Id
     */
    private Integer inviteProjectId;

    /**
     * 展会编号
     */
    private String exhibitCode;

    /**
     * 展会名称 英文
     */
    private String exhibitNameEn;

    /**
     * 展会名称
     */
    private String exhibitName;

    /**
     * 主展会项目ID
     */
    private Integer mainProjectId;

    /**
     * 机构号
     */
    private Integer orgNum;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司简称
     */
    private String companyNameAbbr;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 公司网址
     */
    private String website;

    /**
     * 公司简介
     */
    private String companyProfile;

    /**
     * logo
     */
    private String logo;

    /**
     * 语种类型 1 中文  2 英文
     */
    private Integer version;

    /**
     * 邀请函标题
     */
    private String invitationTitle;

    /**
     * 公司地址 add、
     */
    private String address;

    /**
     * 阅读量
     */
    private Integer readNum;

    /**
     * 主办方邀请函 邀请人名称
     */
    private String inviter;

    /**
     * 邀请函模板ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /**
     * 邀请函模板名称
     */
    private String templateName;

    /**
     * 参展记录Id
     */
    private Integer serveBoothId;

    /**
     * 主办方邀请函 工号
     */
    private Integer jobNumber;

    /**
     *  主办方邀请函  介绍内容
     */
    private String recomContent;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 产品列表
     */
    private List<InvitationProduct> serveProducts;

    /**
     * 展品数量
     */
    private Integer productNum;

    /**
     * 点赞量
     */
    private Integer likeCount;

    /**
     * 评论量
     */
    private Integer commCount;

    /**
     *  展位号
     */
    private String boothNum;
    /**
     * 展馆
     */
    private String sectionName;

    /**
     * 展馆 英文
     */
    private String sectionNameEn;

    /**
     * 邀请的观众数据
     */
    private Integer buyerCount;
    /**
     * 邀请函顶部背景图
     */
    private String templateTopImgUrl;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否可修改公司名称
     */
    private Boolean memberModInvitationCompanyName;

}
