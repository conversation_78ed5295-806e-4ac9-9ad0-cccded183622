package com.eip.facade.vip.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class CountPreorderVo implements Serializable {

    private static final long serialVersionUID = -7672946878030302664L;
    /**
     * 展位数量
     */
    private Integer countBoothNum;
    /**
     * 展位总面积
     */
    private BigDecimal sumBoothArea;
    /**
     * 展位订单金额
     */
    private BigDecimal sumSalePrice;
    /**
     * 线上支付订单金额
     */
    private BigDecimal sumPayMoney;

}
