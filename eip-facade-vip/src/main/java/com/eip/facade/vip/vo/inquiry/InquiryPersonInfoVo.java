package com.eip.facade.vip.vo.inquiry;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * @USER: zwd
 * @DATE: 2025-01-15 12:00
 * @DESCRIPTION: 询盘人信息
 */
@Data
public class InquiryPersonInfoVo implements Serializable {

    private static final long serialVersionUID = 4997176283846290006L;

    /**
     * 询盘人Id
     */
    private Integer serveInquiryId;

    /**
     * 询盘中间表Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long serveInquiryJoinId;

    /**
     * 会员Id
     */
    private Integer zzyuserId;

    /**
     * 名字
     */
    private String inquiryPeople;

    /**
     * 手机号
     */
    private String inquiryPhone;

    /**
     * 邮箱
     */
    private String inquiryEmail;

    /**
     * 备注
     */
    private String inquiryDescription;

    /**
     * 产品Id
     */
    private Integer serveProductId;


    /**
     * 展品图片
     */
    private String pic;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品名称 英文
     */
    private String productNameEn;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司名称 英文
     */
    private String companyNameEn;
}
