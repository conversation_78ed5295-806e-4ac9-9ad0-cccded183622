package com.eip.web.sponsor.controller.api;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.entity.ServeProduct;
import com.eip.facade.sponsor.entity.ServeProductPic;
import com.eip.facade.sponsor.service.ServeProductService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @USER: zwd
 * @DATE: 2023-05-22 16:34
 * @DESCRIPTION:线上网站产品
 */
@RestController
@RequestMapping(value = "/api/product")
public class ApiProductController extends BaseController {

    @Autowired
    private ServeProductService serveProductService;

    /**
     * 根据行业分类或产品类别查询产品
     * @param pageParam
     * @param serveProduct
     * @return
     */
    @RequestMapping(value = "/getListByType", method = RequestMethod.POST)
    public AjaxResponse getListByType(PageParam pageParam, ServeProduct serveProduct) throws ParseException {
        if(serveProduct.getProjectId()==null) return AjaxResponse.failure("项目ID不能为空");
        PageInfo<ServeProduct> list = serveProductService.selectByType(pageParam, serveProduct);
        if(CollectionUtils.isNotEmpty(list.getList())){
            for(ServeProduct product:list.getList()){
                List<ServeProductPic> serveProductPics = serveProductService.selectPicByProductId(product.getServeProductId());
                String collect = serveProductPics.stream().map(ServeProductPic::getPic).collect(Collectors.joining(","));
                product.setPic(collect);
            }
        }
        return AjaxResponse.pageSuccess(list.getTotal(), list.getList());
    }


    @RequestMapping(value = "/getProductById", method = RequestMethod.POST)
    public AjaxResponse getOneFocusCata(@RequestParam("productId") Integer productId) {
        if(productId == null) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        ServeProduct serveProduct = serveProductService.selectOneByIdFocusCata(productId);
        if(Objects.isNull(serveProduct)) return AjaxResponse.success();
        if(Objects.nonNull(serveProduct)){
            serveProductService.updateReadNumUpOne(serveProduct.getServeProductId());
        }
        return AjaxResponse.success(serveProduct);
    }

    /**
     * 根据关键词搜索产品(默认搜索中文)
     * @param pageParam
     * @param serveProduct
     * @param languageType CN中文版 EN英文版
     * @return
     */
    @RequestMapping(value = "/selectByKeyword", method = RequestMethod.POST)
    public AjaxResponse selectByKeyword(PageParam pageParam, ServeProduct serveProduct,@RequestParam("languageType") String languageType) throws ParseException {
        if(serveProduct.getProjectId()==null) return AjaxResponse.failure("项目ID不能为空");
        PageInfo<ServeProduct> list = serveProductService.selectBykeyword(pageParam, serveProduct,languageType);
        if(CollectionUtils.isNotEmpty(list.getList())){
            for(ServeProduct product:list.getList()){
                List<ServeProductPic> serveProductPics = serveProductService.selectPicByProductId(product.getServeProductId());
                String collect = serveProductPics.stream().map(ServeProductPic::getPic).collect(Collectors.joining(","));
                product.setPic(collect);
            }
        }
        return AjaxResponse.pageSuccess(list.getTotal(), list.getList());
    }

    /**
     * 查询单个展商名下的展品
     * @param param
     * @param cataId
     * @return
     */
    @RequestMapping(value = "/getByServeCatalogueId", method = RequestMethod.POST)
    public AjaxResponse getByServeCatalogueId(PageParam param, Integer cataId) {
        if(cataId == null) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        List<ServeProduct> list = serveProductService.getByServeCatalogueId(param, cataId);
        if (list!=null)  return AjaxResponse.pageSuccess(list.size(), list);
        else return AjaxResponse.pageSuccess(0, list);
    }

}
