package com.eip.web.sponsor.controller.api;

import com.eip.common.controller.BaseController;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.BuyerRegDto;
import com.eip.facade.sponsor.service.BuyerRegService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @USER: zwd
 * @DATE: 2023-01-30 9:00
 * @DESCRIPTION:
 */
@RestController
@RequestMapping(value = "/api/buyerReg")
public class ApiBuyerRegController extends BaseController {

    @Autowired
    private BuyerRegService regService;

    /**
     * 根据不同类型查询组装所属发票实体
     * @param buyerRegDto
     * @return
     */
    @RequestMapping(value = "/queryBuyerRegInfo",method = RequestMethod.POST)
    public AjaxResponse queryBuyerRegInfo(BuyerRegDto buyerRegDto){
        return regService.queryBuyerRegInfo(buyerRegDto);
    }

    /**
     * 根据观众ID查询对应展商渠道是否开启了自动使用邀请码
     * @param buyerRegDto
     * @return
     */
    @RequestMapping(value = "/getAutoUseInviteCode",method = RequestMethod.POST)
    public AjaxResponse getBuyerRegInviteCode(BuyerRegDto buyerRegDto){
        if(buyerRegDto.getBuyerId() == null) return AjaxResponse.failure("观众Id为空");
        return regService.getBuyerRegInviteCode(buyerRegDto);
    }

}
