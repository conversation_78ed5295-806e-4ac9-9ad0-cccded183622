/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-11-02 17:50
 * @description：
 */
package com.eip.web.sponsor.controller.api;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.enums.EventKindEnum;
import com.eip.common.enums.OperateTypeEnum;
import com.eip.common.util.RegexUtils;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.ExhibitBoothDto;
import com.eip.facade.sponsor.dto.OperatorDto;
import com.eip.facade.sponsor.entity.*;
import com.eip.facade.sponsor.service.*;
import com.eip.facade.sponsor.vo.ExhibitBoothOrderVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-11-02 17:50
 * @description：
 */
@RestController
@RequestMapping(value = "/api/exhibitBooth")
public class ApiExhibitBoothController extends BaseController {

    @Resource
    private ExhibitBoothService exhibitBoothService;
    @Resource
    private ExhibitionService exhibitionService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProductTypeService productTypeService;
    @Resource
    private BoothTypeService boothTypeService;
    @Autowired
    private SalestateColorService saleStateColorService;
    @Autowired
    private ExhibitSectionService exhibitSectionService;
    @Autowired
    private ServeBoothService serveBoothService;
    @Autowired
    private EventInfoService eventInfoService;


    /**
     * CRM展商参展详情中的确认展位合同弹框中的可添加销售展位
     * @param pageParam
     * @param exhibitBooth
     * @return
     */
    @RequestMapping(value = "/getListExceptOrder", method = RequestMethod.POST)
    public AjaxResponse getListExceptOrder(PageParam pageParam, ExhibitBooth exhibitBooth){
        if(StringUtils.isBlank(exhibitBooth.getExhibitCode())){return AjaxResponse.failure("展会Code不能为空");}
        if(StringUtils.isNotBlank(exhibitBooth.getSectionCode())){
            exhibitBooth.setCrmSectionCode(exhibitBooth.getSectionCode());
            exhibitBooth.setSectionCode(null);
        }
        PageInfo<ExhibitBooth> list = exhibitBoothService.selectExceptOrder(pageParam,exhibitBooth);
        return AjaxResponse.pageSuccess(list.getTotal(),list.getList());
    }


    /**
     * CRM展商参展详情中的确认展位合同弹框中的可添加销售展位
     * @param exhibitBooth
     * @return
     */
    @RequestMapping(value = "/saveExhibitBooth", method = RequestMethod.POST)
    public AjaxResponse saveExhibitBooth(ExhibitBooth exhibitBooth){
        if(StringUtils.isBlank(exhibitBooth.getExhibitCode())
                || StringUtils.isBlank(exhibitBooth.getBoothCode()))return AjaxResponse.failure("参数缺失");
        if(RegexUtils.validateRegex(exhibitBooth.getBoothCode(),RegexUtils.BOOTH_REGEX))
            return AjaxResponse.failure("展位号不能包含分隔符(逗号或分号)");
        Exhibition exhibition = exhibitionService.selectByExhibitCode(exhibitBooth.getExhibitCode());
        if(Objects.isNull(exhibition)) return AjaxResponse.failure("项目未开通信息门户,不允许新增展位");
        exhibitBooth.setOrgNum(exhibition.getF_org_num());
        int state = exhibitBoothService.insertOrUpdate(exhibitBooth);
        if(state == -1) return AjaxResponse.create(state,"同展会下不允许存在相同展位号");
        return AjaxResponse.success(state);
    }

    /**
     * CRM展商参展详情中的添加展位并获取展品范围
     * @param exhibition
     * @return
     */
    @RequestMapping(value = "/getBoothProductType", method = RequestMethod.POST)
    public AjaxResponse getBoothProductType(Exhibition exhibition){
        if(StringUtils.isBlank(exhibition.getExhibitCode()))return AjaxResponse.failure("参数缺失");
        exhibition = exhibitionService.selectByExhibitCode(exhibition.getExhibitCode());
        if(Objects.isNull(exhibition)) return AjaxResponse.failure("项目未开通信息门户,不允许新增展位");
        Map<String,Object> map =new HashMap<>();
        map.put("exhibitCode", exhibition.getExhibitCode());
        map.put("projectId", exhibition.getProjectId());
        return AjaxResponse.success( productTypeService.selectByMap(map));
    }

    /**
     * CRM展商参展详情中的添加展位并获取展位类型
     * @param typeCode
     * @return
     */
    @RequestMapping(value = "/getBoothType", method = RequestMethod.POST)
    public AjaxResponse getBoothType(String typeCode){
        if(StringUtils.isBlank(typeCode))return AjaxResponse.failure("参数缺失");
        return AjaxResponse.success(boothTypeService.selectByTypeCode(typeCode,null));
    }

    /**
     * CRM展商参展详情中的合并展位，逻辑上判断计算是否可以合并
     * @param exhibitBoothDto
     * @return
     */
    @RequestMapping(value = "/mergeExhibitBooth", method = RequestMethod.POST)
    public AjaxResponse mergeExhibitBooth(ExhibitBoothDto exhibitBoothDto){
//        if(exhibitBoothDto.getClientId() == null)  return AjaxResponse.failure();
        if(exhibitBoothDto.getProjectId() == null) return AjaxResponse.failure("项目Id不能为空");
        if(exhibitBoothDto.getServeBoothId() == null)  return AjaxResponse.failure("参展记录Id不能为空");
        if(exhibitBoothDto.getMergeType() == null)  return AjaxResponse.failure("操作类型参数为空");
        if(CollectionUtils.isEmpty(exhibitBoothDto.getExhibitVos()))  return AjaxResponse.failure("待合并展位集合为空");
        Project project = projectService.getById(exhibitBoothDto.getProjectId());
        if(Objects.isNull(project)) return AjaxResponse.failure("项目不存在");
        if(StringUtils.isBlank(project.getExhibitCode())) return AjaxResponse.failure("项目未开通信息门户,不允许合并展位");
        exhibitBoothDto.setOrgNum(project.getF_org_num());
        return exhibitBoothService.mergeExhibitBooth(exhibitBoothDto);
    }

    /**
     * crm展商参展详情中合并展位时，返回确认保存合并展位
     * @param exhibitBoothDto
     * @return
     */
    @RequestMapping(value = "/saveMergeExhibitBooth", method = RequestMethod.POST)
    public AjaxResponse saveMergeExhibitBooth(@RequestBody ExhibitBoothDto exhibitBoothDto){
        ExhibitBoothOrderVo exhibitBoothVo = exhibitBoothDto.getExhibitBoothVo();
        if(Objects.isNull(exhibitBoothVo)) return AjaxResponse.failure("展位信息为空");
        String mergeFocusPreOrderIds = exhibitBoothVo.getMergeFocusPreOrderIds();
        if(StringUtils.isBlank(mergeFocusPreOrderIds)) return AjaxResponse.failure("展位关联ID为空");
        return exhibitBoothService.saveMergeExhibitBooth(exhibitBoothDto);
    }

    /**
     * 获取展位颜色
     * @return
     */
    @RequestMapping(value = "/selectBoothColor",method = RequestMethod.POST)
    public AjaxResponse selectBoothColor(){
        List<SalestateColor> salestateColorList = saleStateColorService.select();
        return AjaxResponse.success(salestateColorList);
    }


    @RequestMapping(value = "/queryExhibitorOrSectionIsPublish", method = RequestMethod.POST)
    public AjaxResponse queryIsLock(String sectionCode,String exhibitCode){
        if(StringUtils.isBlank(exhibitCode)) return AjaxResponse.failure("展会code不能为空");
        if(StringUtils.isBlank(sectionCode)) return AjaxResponse.failure("展区code不能为空");
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("exhibitCode",exhibitCode);
        if("-1".equals(sectionCode)){
            paramMap.put("queryTopOne",true);
        }else{
            paramMap.put("sectionCode",sectionCode);
        }
        ExhibitSection exhibitSection = exhibitSectionService.queryExhibitSection(paramMap);
        return AjaxResponse.success(exhibitSection);
    }

    /**
     * crm合同快速新增展位
     * @param exhibitBoothOrderVo
     * @return
     */
    @RequestMapping(value = "/fastInsertExhibitBooth", method = RequestMethod.POST)
    public AjaxResponse fastInsertExhibitBooth(ExhibitBoothOrderVo exhibitBoothOrderVo){
        if(StringUtils.isBlank(exhibitBoothOrderVo.getExhibitCode())) return AjaxResponse.failure("展会Code不能为空");
        if(StringUtils.isBlank(exhibitBoothOrderVo.getSectionCode())) return AjaxResponse.failure("展区不能为空");
        if(StringUtils.isBlank(exhibitBoothOrderVo.getTypeCode())) return AjaxResponse.failure("展位类型不能为空");
//        if(StringUtils.isBlank(exhibitBoothOrderVo.getBoothCodes())) return AjaxResponse.failure("展位号不能为空");
        Exhibition exhibition = exhibitionService.selectByExhibitCode(exhibitBoothOrderVo.getExhibitCode());
        if(Objects.isNull(exhibition)) return AjaxResponse.create(CommonResponseStateEnum.EXHIBITION_NOT_EXIST);
        exhibitBoothOrderVo.setExhibitName(exhibition.getExihibitName());
        Integer f_org_num = exhibition.getF_org_num();
		if(f_org_num == null) return AjaxResponse.failure("展会未关联机构");
		exhibitBoothOrderVo.setOrgNum(f_org_num);
        OperatorDto operatorDto = null;
        if(exhibitBoothOrderVo.getOperatorId()!=null){
            operatorDto = new OperatorDto();
            operatorDto.setF_org_num(f_org_num);
            operatorDto.setOperatorId(exhibitBoothOrderVo.getOperatorId());
            operatorDto.setOperEntrance(exhibitBoothOrderVo.getOperEntrance());
            if(exhibitBoothOrderVo.getBoothId()==null && StringUtils.isNotBlank(exhibitBoothOrderVo.getBoothCodes())){
                batchLog(RegexUtils.validateIsBooth(exhibitBoothOrderVo.getBoothCodes()),exhibition,operatorDto, OperateTypeEnum.BATCH_ADD.getName(), null);//批量日志
            }
        }
        return exhibitBoothService.fastInsertExhibitBooth(exhibitBoothOrderVo,operatorDto);
    }

    /**
     * 批量日志
     * @param operatorDto
     * @param operateType
     * @param memo
     */
    public void batchLog(String boothCodes, Exhibition exhibition,OperatorDto operatorDto, String operateType, String memo) {
        StringBuffer sbf = new StringBuffer();
        if(StringUtils.isNotBlank(boothCodes)){
            List<String> boothCodeList = Lists.newArrayList(Arrays.asList(StringUtils.split(boothCodes, ",")));
            if(CollectionUtils.isNotEmpty(boothCodeList)){
                if(boothCodeList.size() > 1){
                    sbf.append(exhibition.getExihibitName()).append(" ").append(boothCodeList.get(0)).append("、").append(boothCodeList.get(1));
                    sbf.append("等").append(boothCodeList.size()).append("项");
                }else{
                    sbf.append(boothCodeList.get(0)).append(" ").append(exhibition.getExihibitName());
                }
            }
        }
        EventInfo eventInfo = eventInfoService.buildEventByOperator(operatorDto, true, memo,
                    EventKindEnum.EXHIBIT_BOOTH_INFO_SET.getId(), operateType, exhibition.getExhibitCode(),String.valueOf(sbf), true, null);
        eventInfoService.add(eventInfo);
        operatorDto.setEventId(eventInfo.getId());
    }


}
