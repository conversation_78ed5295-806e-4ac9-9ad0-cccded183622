//package com.eip.web.sponsor.job;

/**
 * @description: 定时任务  查询订单支付状态
 * @author： hfl
 * @date：2021-08-27 14:26
 * @File: OrderTradeStateJob
 */
//@Slf4j
//@Component
//public class OrderTradeStateJob {
//
//    @Autowired
//    private OrderTradeService orderTradeService;
//
//    @Autowired
//    private PayService payService;
//
//    /**
//     * 微信订单状态定时查询
//     */
//	@Scheduled(cron ="0/30 * * * * ?")
//    @Async(value = "annotationExecutor")
//    public void updateOrderTradeState(){
//        Map<String, Object> map = Maps.newHashMap();
//        try {
////            log.info("--------订单状态查询定时任务启动--------");
//            int count = 60;
//            int timeMin = 30;//当前时间(服务器) - 30分钟 <= f_create_time  //没有设置失效时间的使用这个过滤
////            map.put("countState",count);
////            map.put("countTime",timeMin);
////            map.put("payType","0");
////            map.put("filterExpireTime",true);//过滤存在过期时间的订单查询
//            map.put("orderPayState","0");
//            List<OrderTrade> tradeList = orderTradeService.selectOrderTradeByMap(map);
//            if(CollectionUtils.isNotEmpty(tradeList) && tradeList.size() > 0){
////                log.info("未支付订单数据总计：{}--------",tradeList.size());
//                for(OrderTrade orderTrade : tradeList){
////                    log.info("--------订单号：{}--------",orderTrade.getOrderTradeId());
//                    Date expireTime = orderTrade.getExpireTime();
//                    if(Objects.nonNull(expireTime) && expireTime.before(DateUtil.getCurrentNow())){//说明过期时间已经超过了当前时间，调取关闭订单接口
//                        String orderPayState = orderTrade.getOrderPayState();
//                        if(StringUtils.isBlank(orderPayState)) continue;
//                        if(Integer.valueOf(orderPayState).intValue() > 0) continue;//说明支付成功-1或已退款-3或订单关闭-2
//                        OrderTradeDto orderTradeDto1 = new OrderTradeDto();
//                        orderTradeDto1.setOrderTradeId(String.valueOf(orderTrade.getOrderTradeId()));
//                        orderTradeDto1.setOrgNum(orderTrade.getOrgNum());
//                        Map<String, String> wxOrderCloseMap = payService.wxOrderClose(orderTradeDto1);
//                        if(wxOrderCloseMap.get("return_code")!=null && wxOrderCloseMap.get("return_code").equalsIgnoreCase(WxConstant.RETURN_SUCCESS)){
//                            if(wxOrderCloseMap.get("result_code")!=null && wxOrderCloseMap.get("result_code").equalsIgnoreCase(WxConstant.RETURN_SUCCESS)){
//                                orderTrade.setOrderPayState("2");
//                                orderTrade.setOperatType(2);
//                                orderTrade.setUpdateTime(new Date());
//                                orderTrade.setOrderTradeId(orderTrade.getOrderTradeId());
//                                orderTrade.setCurrentOrderState("orderClose");//该字段只是标志 orderTradeService.updateOrderTrade接口中不处理关闭订单以外的其它操作
//                                orderTradeService.updateOrderTrade(orderTrade);
//                            }
//                        }
//                        continue;
//                    }
//                    OrderTradeDto orderTradeDto = new OrderTradeDto();
//                    orderTradeDto.setOrderTradeId(String.valueOf(orderTrade.getOrderTradeId()));
//                    orderTradeDto.setOrgNum(orderTrade.getOrgNum());
//                    Map<String, String> wxOrderQuery = payService.wxOrderQuery(orderTradeDto);
//
//                    String return_code = wxOrderQuery.get("return_code");
//                    String result_code = wxOrderQuery.get("result_code");
//                    String trade_state = wxOrderQuery.get("trade_state");
//
//                    if(return_code.equalsIgnoreCase(WxConstant.RETURN_SUCCESS)
//                            && result_code.equalsIgnoreCase(WxConstant.RETURN_SUCCESS)
//                            && trade_state.equalsIgnoreCase(WxConstant.RETURN_SUCCESS)) {
//                        orderTrade.setOrderPayState("1");
//                        Date date = null;
//                        try {
//                            date = DateUtil.parseStringTo2Date(wxOrderQuery.get("time_end"), "yyyyMMddhhmmss");
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                        //更新订单状态和支付时间
//                        orderTrade.setTimeEnd(date);
//                        orderTrade.setPayType("0");
//                        orderTrade.setTradeType("WX_"+wxOrderQuery.get("trade_type"));
//                        String trade_type = wxOrderQuery.get("trade_type");
//                        switch (trade_type){
//                            case "JSAPI":
//                                orderTrade.setFromType("1");
//                                break;
//                            case "MWEB":
//                                orderTrade.setFromType("2");
//                            default:
//                                break;
//                        }
//                        orderTrade.setUpdateTime(new Date());
//                        orderTrade.setOperatType(1);
//                        orderTrade.setOpenId(wxOrderQuery.get("openid"));
//                        orderTrade.setNotifyMsg(wxOrderQuery.toString());
//                        orderTrade.setTransactionId(wxOrderQuery.get("transaction_id"));
//                        orderTrade.setPayType("0");
//                        orderTradeService.updateOrderTrade(orderTrade);
//                    }else{
//                        if(orderTrade.getPaySource() != null && orderTrade.getPaySource() == 1 &&
//                                orderTrade.getBuyerId()!=null && orderTrade.getBuyerId().intValue() > 0){
//                            Integer countType = orderTrade.getCountState();
//                            countType++;
//                            orderTrade.setCountState(countType);
//                            orderTrade.setCurrentOrderState("buyerReg");//该字段设值无意义，只是为了防止更新逻辑走了其它的判断
//                            orderTradeService.updateOrderTrade(orderTrade);
//                            if(countType == count){
//                                //最后一次查询未成功，调用接口关闭订单
//                                OrderTradeDto orderTradeDto1 = new OrderTradeDto();
//                                orderTradeDto1.setOrderTradeId(String.valueOf(orderTrade.getOrderTradeId()));
//                                orderTradeDto1.setOrgNum(orderTrade.getOrgNum());
//                                Map<String, String> wxOrderCloseMap = payService.wxOrderClose(orderTradeDto1);
//                                if(wxOrderCloseMap.get("return_code")!=null && wxOrderCloseMap.get("return_code").equalsIgnoreCase(WxConstant.RETURN_SUCCESS)){
//                                    if(wxOrderCloseMap.get("result_code")!=null && wxOrderCloseMap.get("result_code").equalsIgnoreCase(WxConstant.RETURN_SUCCESS)){
//                                        orderTrade.setOrderPayState("2");
//                                        orderTrade.setOperatType(2);
//                                        orderTrade.setUpdateTime(new Date());
//                                        orderTrade.setOrderTradeId(Long.parseLong(orderTradeDto.getOrderTradeId()));
//                                        orderTrade.setCurrentOrderState("orderClose");//该字段只是标志 orderTradeService.updateOrderTrade接口中不处理关闭订单以外的其它操作
//                                        orderTradeService.updateOrderTrade(orderTrade);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("订单状态查询定时任务结束:{}",e);
//        }
////        log.info("--------订单状态查询定时任务结束--------");
//    }
//}
