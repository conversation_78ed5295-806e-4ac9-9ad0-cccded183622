package com.eip.web.sponsor.common;

import com.beust.jcommander.internal.Lists;
import com.eip.common.util.CollectionUtil;
import com.eip.common.util.bean.BeanUtils;
import com.eip.facade.sponsor.dto.OperatorDto;
import com.eip.facade.sponsor.entity.Operator;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

public class CommonUtils {

    public static final String NOT_LOGIN_MSG = "登录失效";

    /**
     * 判断是否有登录
     * @param request
     * @return
     */
    public static Boolean isLogin(HttpServletRequest request) {
        HttpSession session = request.getSession();
        return isLogin(session);
    }
    /**
     * 判断是否有登录
     * @param session
     * @return
     */
    public static Boolean isLogin(HttpSession session) {
        if (getOperator(session) == null) return false;
        return true;
    }

    /**
     * 根据session获取operator
     * @param session
     * @return
     */
    public static Operator getOperator(HttpSession session) {
        if (session == null) return null;
        //return (Operator) session.getAttribute("operator");
        String operatorJson = (String) session.getAttribute("operatorJson");
        if (StringUtils.isBlank(operatorJson)) return null;
        Gson gson = new Gson();
        return gson.fromJson(operatorJson, Operator.class);
    }
    /**
     * 复制operator 得到 operateDto
     * @param operator
     * @return
     */
    public static OperatorDto  getOperatorDto(Operator operator){
        if(operator == null) return null;
        OperatorDto bean = new OperatorDto();
        BeanUtils.copyProperties(operator,bean);
        return bean;
    }
    /**
     * 从session里 得到 operateDto
     * @param session
     * @return
     */
    public static OperatorDto  getOperatorDto(HttpSession session){
        if(session == null) return null;
        Operator operator = getOperator(session);
        return getOperatorDto(operator);
    }
    /**
     * 从session里 得到 operateDto
     * @param session
     * @return
     */
    public static OperatorDto getOperatorDto(HttpSession session, String operEntrance) {
        if (session == null) return null;
        OperatorDto operatorDto = getOperatorDto(session);
        if (operatorDto != null) operatorDto.setOperEntrance(operEntrance);
        return operatorDto;
    }

    /**
     * 从session里 得到 操作员，没有操作员信息 虚拟一个 -1的操作员
     * @param session
     * @param operEntrance
     * @return
     */
    public static OperatorDto getOperatorDtoIfNullVirtual(HttpSession session, String operEntrance) {
        if (session == null) return null;
        OperatorDto operatorDto = getOperatorDto(session);
        if (operatorDto == null) {
            operatorDto = new OperatorDto();
            operatorDto.setOperatorId(-1);
        }
        operatorDto.setOperEntrance(operEntrance);
        return operatorDto;
    }

    /**
     * 获取操作员全部权限
     * @param session
     * @return
     */
    public static List<String> getFunCodes(HttpSession session) {
        List<String> funCodes = (List<String>) session.getAttribute("funCodes");
        return CollectionUtil.isEmpty(funCodes) ? Lists.newArrayList() : funCodes;
    }

    /**
     * 获取CRM权限
     * @param session
     * @return
     */
    public static List<String> getCrmFunCodes(HttpSession session) {
        List<String> funCodes = (List<String>) session.getAttribute("crm_funCodes");
        return CollectionUtil.isEmpty(funCodes) ? Lists.newArrayList() : funCodes;
    }

    /**
     * 是否存在某个权限
     * @param session
     * @param funCode
     * @return
     */
    public static boolean isExistFun(HttpSession session, String funCode) {
        List<String> funCodes = getFunCodes(session);
        //return funCodes.stream().anyMatch(item -> item.equals(funCode));
        boolean match = funCodes.stream().anyMatch(item -> item.equals(funCode));
        if (!match) {
            List<String> crmFunCodes = getCrmFunCodes(session);
            match = crmFunCodes.stream().anyMatch(item -> item.equals(funCode));
        }
        return match;
    }
}
