package com.eip.web.sponsor.async;

import com.eip.facade.sponsor.service.ServeCertService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Future;

/**
 * @USER: zwd
 * @DATE: 2025-05-15 10:20
 * @DESCRIPTION:
 */
@Slf4j
@Component
public class ServeCertAsync {

    @Autowired
    private ServeCertService serveCertService;


    /**
     * 统计未推送和推送失败的数据
     * @return
     */
    @Async
    public Future<Map<String,Object>> queryFailedOrNotPushedDataAsync(Map<String, Object> paramMap){
        paramMap.remove("selectPushData");
        paramMap.remove("companyNameJoinState");
        paramMap.remove("offset");
        paramMap.remove("rows");
        int notPushAndPushFailedNum = 0;
        Map<String,Object> resultMap = Maps.newHashMapWithExpectedSize(6);
        //推送状态  -1 未推送  0 推送失败  1 已推送
        Object dataPushObj = paramMap.get("dataPush");
        if(dataPushObj!=null && (Integer) dataPushObj!=1){//直接count即可
            notPushAndPushFailedNum = serveCertService.statisticsCount(paramMap);
        }else{
            //1、未推送
            paramMap.put("notPushCountFlag",true);
            int notPushCount = serveCertService.statisticsCount(paramMap);
            //2、推送失败
            paramMap.remove("notPushCountFlag");
            paramMap.put("pushFailedFlag",true);
            int pushFailedCount = serveCertService.statisticsCount(paramMap);
            notPushAndPushFailedNum = notPushCount + pushFailedCount;
        }
        resultMap.put("notPushAndPushFailedNum",notPushAndPushFailedNum);
        return new AsyncResult<>(resultMap);
    }
}
