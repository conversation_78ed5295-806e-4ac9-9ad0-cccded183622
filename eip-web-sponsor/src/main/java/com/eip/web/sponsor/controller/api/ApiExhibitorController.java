package com.eip.web.sponsor.controller.api;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.ProjectDto;
import com.eip.facade.sponsor.dto.ServeCatalogueDto;
import com.eip.facade.sponsor.entity.ExhibitHaveTools;
import com.eip.facade.sponsor.entity.ServeCompany;
import com.eip.facade.sponsor.service.ServeCatalogueService;
import com.eip.facade.sponsor.service.ServeCompanyService;
import com.eip.facade.sponsor.service.ServePersonService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-05-22 16:33
 * @DESCRIPTION: 线上网站商家
 */
@RestController
@RequestMapping(value = "/api/exhibitor")
public class ApiExhibitorController extends BaseController {

    @Autowired
    private ServeCompanyService serveCompanyService;
    @Autowired
    private ServeCatalogueService serveCatalogueService;
    @Autowired
    private ServePersonService servePersonService;

    /**
     * 推荐商家
     * @param pageParam
     * @param serveCompany
     * @return
     */
    @RequestMapping(value = "/selectRecommendExhibitor",method = RequestMethod.POST)
    public AjaxResponse selectRecommendExhibitor(PageParam pageParam, ServeCompany serveCompany){
        if(StringUtils.isBlank(serveCompany.getExhibitCode())) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        PageInfo<ServeCompany> list = serveCompanyService.selectMorePage(pageParam, serveCompany);
        return AjaxResponse.pageSuccess(list.getTotal(), list.getList());
    }

    /**
     * 获取展商列表
     * @param pageParam
     * @param serveCatalogueDto
     * @return
     */
    @RequestMapping(value = "/getMorePageFocusCata", method = RequestMethod.POST)
    public AjaxResponse getMorePage(PageParam pageParam, ServeCatalogueDto serveCatalogueDto) {
        if(StringUtils.isBlank(serveCatalogueDto.getExhibitCode())) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        PageInfo<ServeCatalogueDto> list = serveCatalogueService.selectMorePageFocusCata(pageParam, serveCatalogueDto);
        return AjaxResponse.pageSuccess(list.getTotal(), list.getList());
    }


    /**
     * 根据id获取展商详情
     * @param cataId
     */
    @RequestMapping(value = "/getMoreByIdFocusCata", method = RequestMethod.POST)
    public AjaxResponse getMoreByIdFocusCata(@RequestParam("cataId") Integer cataId){
        if(cataId == null) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        ServeCatalogueDto serveCatalogueDto = serveCatalogueService.selectMoreByIdFocusCata(cataId);
        if(Objects.nonNull(serveCatalogueDto)){
            serveCompanyService.updateReadNumUpOne(serveCatalogueDto.getServeCompanyId());
        }
        return AjaxResponse.success(serveCatalogueDto);
    }

    /**
     * 根据展会Code查询展会所有的展具配置
     * @param pageParam
     * @return
     */
    @RequestMapping(value = "/getToolsByProjectId", method = RequestMethod.POST)
    public AjaxResponse getToolsByProjectId(PageParam pageParam, ProjectDto projectDto){
        if(StringUtils.isBlank(projectDto.getExhibitCode())){//默认展会code
            if(projectDto.getProjectId() == null) return AjaxResponse.failure("项目ID不能为空");
        }
        List<ExhibitHaveTools> list = Lists.newArrayList();
        int total = servePersonService.getToolsCountByProjectId(projectDto);
        if(total > 0){
            list = servePersonService.getToolsByProjectId(pageParam, projectDto);
        }
        return AjaxResponse.pageSuccess(total,list);
    }

}
