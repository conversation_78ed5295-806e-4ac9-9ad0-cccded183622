/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-01-06 13:57
 * @description：展位预订单自动对应合同
 */
package com.eip.web.sponsor.controller.api;

import com.eip.common.controller.BaseController;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.dto.BoothPreOrderDto;
import com.eip.facade.sponsor.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-01-06 13:57
 * @description：展位预订单自动对应合同
 */
@Controller
@RequestMapping(value = "/api/boothOrderContract")
public class ApiContractController extends BaseController {

    @Autowired
    private ExhibitBoothService exhibitBoothService;


    /**
     * 展商定展管理
     * @param boothPreOrderDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/selectPreOrderInfo", method = RequestMethod.POST)
    public AjaxResponse selectPreOrderInfo(BoothPreOrderDto boothPreOrderDto) {
        if(boothPreOrderDto.getProjectId()==null) return AjaxResponse.failure("项目Id为空");
        if(boothPreOrderDto.getClientId()==null) return AjaxResponse.failure("公司Id为空");
        return exhibitBoothService.selectPreOrderInfo(boothPreOrderDto);
    }


    /**
     * 展商定展管理
     * @param boothPreOrderDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/synUpdateContractBoothFree", method = RequestMethod.POST)
    public AjaxResponse updateSynContractBoothFree(BoothPreOrderDto boothPreOrderDto) {
        if(boothPreOrderDto.getContractId() == null) return AjaxResponse.failure("合同Id不能为空");
        return exhibitBoothService.updateSynContractBoothFree(boothPreOrderDto);
    }

}
