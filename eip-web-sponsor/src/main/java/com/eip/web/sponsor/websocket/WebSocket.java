package com.eip.web.sponsor.websocket;

import com.alibaba.fastjson.JSONObject;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 服务器
 * @ClassName: WebSocket 
 * @Description: TODO
 * <AUTHOR> @Date  
 *
 */
//@ServerEndpoint("/webSocketByTomcat/{username}/{operatorId}")
public class WebSocket {  
    private static int onlineCount = 0;  
    private static Map<String, WebSocket> clients = new ConcurrentHashMap<String, WebSocket>();  
    private Session session;  
    private String username;
    private String operatorId;
      
    @OnOpen  
    public void onOpen(@PathParam("username") String username,@PathParam("operatorId") String operatorId, Session session) throws IOException {
  
        this.username = username;  
        this.session = session;
        this.operatorId = operatorId;
          
        addOnlineCount();  
        clients.put(operatorId, this);
        System.out.println(operatorId+"已连接");

        //给所有用户发送在线用户信息
        JSONObject sendMessage = new JSONObject();
        String msg = "";
        for (WebSocket item : clients.values()) {
            if(msg == ""){
                msg = (item.operatorId.toString());
            }else{
                msg = msg + ',' + (item.operatorId.toString());
            }
        }
        msg = "当前在线："+msg;
        sendMessage.put("msg",msg);
        sendMessage.put("type","alive");
        sendMessageAll(JSONObject.toJSONString(sendMessage));
    }  
  
    @OnClose  
    public void onClose() throws IOException {  
        clients.remove(operatorId);
        System.out.println(operatorId+"已关闭");
        subOnlineCount();

        //给所有用户发送在线用户信息
        JSONObject sendMessage = new JSONObject();
        String msg = "";
        for (WebSocket item : clients.values()) {
            if(msg == ""){
                msg = (item.operatorId.toString());
            }else{
                msg = msg + ',' + (item.operatorId.toString());
            }
        }
        msg = "当前在线："+msg;
        sendMessage.put("msg",msg);
        sendMessage.put("type","alive");
        sendMessageAll(JSONObject.toJSONString(sendMessage));
    }  
  
    @OnMessage  
    public void onMessage(String message) throws IOException {  
    	System.out.println("收到消息："+message);
        JSONObject jsonTo = JSONObject.parseObject(message);
        System.out.println(jsonTo.getString("to") +":"+ jsonTo.getString("msg"));
        //
        JSONObject sendMessage = new JSONObject();
        sendMessage.put("msg",jsonTo.getString("msg"));
        sendMessage.put("type","content");

        if (!jsonTo.getString("to").toLowerCase().equals("all")){
            sendMessageTo(JSONObject.toJSONString(sendMessage), jsonTo.getString("to"));
        }else{
            sendMessageAll(JSONObject.toJSONString(sendMessage));
        }
    }  
  
    @OnError  
    public void onError(Session session, Throwable error) {  
        error.printStackTrace();  
    }  
  
    public void sendMessageTo(String message, String To) throws IOException {  
        // session.getBasicRemote().sendText(message);  
        //session.getAsyncRemote().sendText(message);  
        for (WebSocket item : clients.values()) {  
            if (item.operatorId.equals(To) )
                item.session.getAsyncRemote().sendText(message);  
        }  
    }  
      
    public void sendMessageAll(String message) throws IOException {  
        for (WebSocket item : clients.values()) {  
            item.session.getAsyncRemote().sendText(message);  
        }  
    }  
      
      
  
    public static synchronized int getOnlineCount() {  
        return onlineCount;  
    }  
  
    public static synchronized void addOnlineCount() {  
        WebSocket.onlineCount++;  
    }  
  
    public static synchronized void subOnlineCount() {  
        WebSocket.onlineCount--;  
    }  
  
    public static synchronized Map<String, WebSocket> getClients() {  
        return clients;  
    }  
}