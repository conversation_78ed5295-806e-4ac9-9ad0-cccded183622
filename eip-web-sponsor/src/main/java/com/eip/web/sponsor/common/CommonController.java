package com.eip.web.sponsor.common;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eip.common.controller.BaseController;
import com.eip.common.util.RedisUtil;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.sponsor.vo.AuthLoginRespVo;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @USER: zwd
 * @DATE: 2023-06-25 11:59
 * @DESCRIPTION: 多一层继承，继承基础web请求，解析token
 */
public abstract class CommonController extends BaseController {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取token
     * @return
     */
    public AuthLoginRespVo getUserInfo(){
        AjaxResponse resp = redisUtil.getMemberLoginTokenFromRedis();
        if(resp.getState()!=1) return null;
        AjaxResponse memberInfoObjResp = redisUtil.validToken(String.valueOf(resp.getData()));
        if(memberInfoObjResp.getState()!=1) return null;
        JSONObject jsonObject = JSONUtil.parseObj(memberInfoObjResp.getData());
        AuthLoginRespVo authLoginRespVo = JSONUtil.toBean(jsonObject, AuthLoginRespVo.class);
        return authLoginRespVo;
    }
}
