<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<script src="../js/variable.js"></script>
<!-- 图片裁切的css及js -->
<link rel="stylesheet" type="text/css" href="../css/cropping/cropper.min.css" />
<link rel="stylesheet" type="text/css" href="../css/cropping/ImgCropping.css" />

<!-- <script src="../../js/cropping/cropper.min.js"></script> -->
<%@ include file="common/include_base.jsp" %>
<%@ page import="com.eip.common.entity.MenuList" %>
<%@ page import="com.eip.common.entity.Menu" %>
<%@ page import="java.util.List" %>
<%
	MenuList menuList = (MenuList)session.getAttribute("menuList");
	List<Menu> menus = menuList.getMenus();
%>
<%@ page import="java.util.List" %>
<%
	boolean fu_show_exhibit_map = false;
	boolean fu_menu_member = false;
	boolean fu_menu_mock_sponsor = false;
	boolean fu_menu_mobile_mock = false;
	boolean fu_menu_new_crm = false;
	boolean fu_menu_message = false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_show_exhibit_map"))fu_show_exhibit_map = true;
		if(s.equals("fu_menu_member"))fu_menu_member = true;
		if(s.equals("fu_menu_mock_sponsor"))fu_menu_mock_sponsor = true;
		if(s.equals("fu_menu_mobile_mock"))fu_menu_mobile_mock = true;
		if(s.equals("fu_menu_new_crm"))fu_menu_new_crm = true;
		if(s.equals("fu_menu_message"))fu_menu_message = true;


	}
%>
<style type="text/css">
a,
a:link { color:#000; text-decoration:none; outline:none; }
a:hover,
a:focus { text-decoration:none; color:#39f; }

.pd3 { padding:3px; }
.pd5 { padding:5px; }
.pd10 { padding:10px; }

.my-text,
.my-textarea { padding:3px; border:1px #95b8e7 solid; width:260px; }
.my-text { height:14px; line-height:14px; }
.my-checkbox { height:16px; width:16px; }

/* .my-header { height:51px; position:relative; z-index:0; overflow:hidden; border-bottom:1px #95b8e7 solid; background:#1e90fa bottom repeat-x; } */
.my-header { height:51px; position:relative; z-index:0; overflow:hidden; border-bottom:1px #4c72fe solid; background:#4c72fe bottom repeat-x; }
 .my-header-left { position:absolute; z-index:1; left:15px; top:0; }
 .my-header-left h1 { font:20px/20px Verdana; color:#fff; margin: 0; }
.my-header-right { position:absolute; z-index:1; right:5px; top:4px; color:#fff; text-align:right; }
.my-header-right p {margin-bottom: 0;margin-top: 6px;}
.my-header-right a { color:#fff; margin:0 5px; }

.my-sidebar { width:160px; }
.my-side-tree .tree-node { padding:3px 0px; }
.my-side-tree a { display:block; }
.my-side-tree a div { width:100px; }
.my-side-tree .tree-node-selected { padding:2px 0; border:1px #fade23 solid; }

.my-footer { height:25px; padding:5px; text-align:center; overflow:hidden; background:#e0ecff; }
.my-message-div{
	padding-left: 17px;
    margin-left: 26px;
    background: url(../jquery-easyui-1.5.5.4/themes/icons/mess.png) no-repeat left center;
    float:left;
    text-align:left; }
.message-count{}

.my-toolbar-button,
.my-toolbar-search { padding:3px 5px; }

.panel-body-noborder{ overflow-y: hidden;}
/* 消息框 */
.newsWindow{
	position: fixed;
	top: 24%;
	left: 25%;
	box-sizing: border-box;
	background-color: #fafcff;
	overflow: hidden;
	display: none;
	box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
	border: solid 1px #eaeaea;
}
.news_windowstop{
	height: 38px;
	overflow: hidden;
}
.news_windowstop h1{
	float: left;
	width: 250px;
	line-height: 38px;
	box-sizing: border-box;
	padding-left: 30px;
	background-color: #2e82e4;
	font-size: 14px;
	color: #ffffff;
	font-weight: normal;
}
.news_windowstop input{
	width: 70px;
	height: 26px;
	box-sizing: border-box;
	padding-left: 24px;
	border-radius: 2px;
	outline: none;
	border: none;
	font-size: 14px;
	color: #666666;
	float: left;
	cursor: pointer;
	float: left;
	margin-left: 20px;
	margin-top: 6px;
}
.news_windowstop input:hover{
	background-color: #dce0e6;
}
.refreshs{
	background: url(img/Refresh.png) no-repeat 8px center;
	background-size: 14px;
}
.dels{
	background: url(img/recycleBin/5.png) no-repeat 9px center;
	background-size: 14px;
}
.news_windowsbotton{
	width: 100%;
	overflow: hidden;
	margin-bottom: 18px;
}
.news_windowleft{
	float: left;
	width: 250px;
	height: 530px;
	overflow-y: auto;
}
.news_windowleft ul li{
	width: 100%;
	height: 64px;
	overflow: hidden;
	cursor: pointer;
}
.news_windowleft ul li:hover{
	background-color: #ebedf2;
}
.news_head{
	float: left;
	width: 41px;
	height: 41px;
	background-color: #2e82e4;
	margin: 10px;
	border-radius: 50%;
}
.news_head img{
	display: block;
	overflow: hidden;
}
.news_text{
	float: left;
	height: 64px;
	width: 189px;
	overflow: hidden;
}
.news_text h1{
	width: 100%;
	font-size: 16px;
	color: #4d4d4d;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	margin: 10px 0 4px;
	font-weight: normal;
}
.news_text h1 i{
	width: 8px;
	height: 8px;
	background-color: #f05555;
	border-radius: 50%;
	display: inline-block;
	margin-right: 4px;
}
.news_text p{
	width: 100%;
	font-size: 14px;
	color: #999999;
	overflow: hidden;
}
.news_text p span:last-child{
	float: right;
	margin-right: 16px;
}
.news_text p span:first-child{
	float: left;
	width: 126px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.news_windowright{
	float: right;
	width: 694px;
	margin: 0 15px;
}
.news_windowright h1{
	font-size: 18px !important;
	color: #333333;
	box-sizing: border-box;
	padding: 15px 0 14px 10px;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.news_windowcontent{
	width: 694px;
	background-color: #ffffff;
	border-radius: 6px;
	border: solid 1px #e6e6e6;
	overflow: hidden;
}
.content_top{
	box-sizing: border-box;
	margin: 0 20px;
	border-bottom: dashed 1px #dfe1e6;
}
.content_top p,.content_top p span{
	font-size: 12px;
	color: #909399;
}
.content_top p:first-child{
	margin-top: 16px;
}
.content_top p:last-child{
	margin-bottom: 8px;
}
.content_button{
	position: relative;
	padding: 12px 20px 0;
	color: #333333;
}
.content_buttoninp{
	text-align: center;
	margin: 58px 0;
}
.content_buttoninp input{
	width: 140px;
	height: 34px;
	background-color: #53a0f9;
	box-shadow: 0px 4px 6px 0px
	rgba(83, 160, 249, 0.2);
	border-radius: 3px;
	outline: lavender;
	border: none;
	color: #ffffff;
	cursor: pointer;
}
.content_text{
	height: 236px;
	overflow-y: auto;
}
/* end */
</style>
<script>

	//window.location.href = "/eip-web-sponsor/exhibit/index?exhibitCode=E0000000048&projectId=425";

var websocket = null;
//关闭WebSocket连接
function closeWebSocket() {
    websocket.close();

}
//将消息显示在网页上
function setMessageInnerHTML(innerHTML) {
    document.getElementById('message').innerHTML += innerHTML + '<br/>';
    /*    $('#11').bind('click',function(){

        });
        $('.my-side-tree a').bind("click", function(){
            var title = $(this).text();

        });*/
    $(function(){
        //alert('22');
	});

}
//将在线会员序号显示在网页上
function setAliveInnerHTML(innerHTML) {
    document.getElementById('alive').innerHTML = innerHTML + '<br/>';

}
//发送消息
function send() {
    console.log("向服务器发送消息.");
    //alert('22');
    var message = document.getElementById('text').value;
	var messageObj = {};
    messageObj['to'] = 'all';
    messageObj['msg'] = message;
    messageJson = JSON.stringify(messageObj);
    websocket.send(messageJson);
}
/*$(function(){
	const protocol = location.origin.startsWith('https') ? 'wss://' : 'ws://'
    //var wsServer = 'wss://127.0.0.1:9501';
    // var wsServer = window.location.hostname+":"+window.location.port+"/eip-web-sponsor";
     var wsServer = window.location.hostname+":8079"+"/msgnotify";///msgnotify
    if('WebSocket' in window) {
        websocket = new WebSocket(protocol+wsServer+"/webSocketByTomcat/"+document.getElementById('sessionId').value+"/"+operatorId);
    } else if('MozWebSocket' in window) {
        websocket = new MozWebSocket(protocol+wsServer+"/webSocketByTomcat/"+document.getElementById('sessionId').value);
    } else {
        websocket = new SockJS(wsServer+"/webSocketByTomcat/"+document.getElementById('sessionId').value);
    }
    //var websocket = new WebSocket(wsServer);
    //连接成功建立的回调方法
    websocket.onopen = function (evt) {
        console.log("Connected to WebSocket server.");
    };

    websocket.onclose = function (evt) {
    	setMessageInnerHTML("WebSocket连接关闭");
        console.log("Disconnected");
    };
    //closeWebSocket
  	//监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        closeWebSocket();
    }

  	//接收到消息的回调方法
    websocket.onmessage = function (evt) {
        //$('#content').append(evt.data+"<br>");
		//解析收到的消息是什么类型
        var obj = JSON.parse(evt.data)
		if(obj['type']=='content'){
            // setMessageInnerHTML(obj['msg']);
			var queryParams = $('#exhibitor-cost-datagrid-contract').datagrid('options').queryParams;
			$('#exhibitor-cost-datagrid-contract').datagrid('options').queryParams=queryParams;
			$("#exhibitor-cost-datagrid-contract").datagrid('reload');
		}else if (obj['type']=='alive'){
            setAliveInnerHTML(obj['msg']);
		}
        // document.getElementById('div').style.background = evt.data;
        console.log('Retrieved data from server: ' + evt.data);
    };
  	//连接发生错误的回调方法
    websocket.onerror = function (evt, e) {
    	setMessageInnerHTML("WebSocket连接发生错误");
        console.log('Error occured: ' + evt.data);
    };
    function ab(){
        var zhi = $('#text').val();
        websocket.send(zhi);
        $('#text').val('');

    }

 });*/

</script>
<script type="text/javascript">
	/*$(function(){
		$('.my-side-tree a').bind("click", function(){
			var title = $(this).text();
			var url = $(this).attr('data-link');
			var iconCls = $(this).attr('data-icon');
			var iframe = $(this).attr('iframe') == 1 ? true : false;
			addTab(title, url, iconCls, iframe);
		});
	});*/
	 function exhibitMainAddTab(my){
	     //alert(my);

		 var title = my.attr('title');
		 var iconCls = null;
		 var url = my.attr('data-link');
		 var iframe = my.attr('iframe') == 1 ? true : false;
		 addTab(title, url, iconCls, iframe, 1);
         console.log($('#divAccountLogin'));
	 }

	function bindMenuTreeClick(){
		$('.my-side-tree a').bind("click", function(){
			var title = $(this).text();
			var url = $(this).attr('data-link');
			var iconCls = $(this).attr('data-icon');
			var iframe = $(this).attr('iframe') == 1 ? true : false;
			addTab(title, url, iconCls, iframe);
		});
	}

	function closeTab(title){
		var tabPanel = $('#index-tabs');
		if(tabPanel.tabs('exists', title)){
			tabPanel.tabs('close', title);
		}
	}
	// +第5个参数: 是否可关闭
	// close: boolean 默认false
	function addTab(title, href, iconCls, iframe,close=false){
        //alert(href);
		var tabPanel = $('#index-tabs');
		if(!tabPanel.tabs('exists', title)){
			var content = '<iframe scrolling="auto" frameborder="0"  src="' + href + '" style="width:100%;height:100%;"></iframe>';
			if(iframe){
				tabPanel.tabs('add', {
					title: title,
					content: content,
					//iconCls: iconCls,
					fit: true,
					cls: 'pd3',
					closable: close
				});
			}else{
				tabPanel.tabs('add', {
					title: title,
					href: href,
					//iconCls: iconCls,
					fit: true,
					cls: 'pd3',
					closable: close
				});
			}
		}else{
			tabPanel.tabs('select', title);
			if(title=="观众登记"){
				RefreshTabTrue(href)
			}
		}
	}


	//刷新当前标签Tabs
    function RefreshTabTrue(url) {
    	var currentTab = $('#index-tabs').tabs('getSelected');
        	$('#index-tabs').tabs('update', {
            tab: currentTab,
            options: {
                href: url
            }
        });
        currentTab.panel('refresh');
  }

	var version = '${sessionScope.version}';
	var lastPage = '${sessionScope.lastPage}';
	function logout(){
		$.messager.confirm('提示', '确定退出吗？', function(r){
			if (r){
				/* window.location.href = eippath + "/backstage/logout"; */
				window.location.href = eippath + "/backstage/logout?version="+ version+"&lastPage="+lastPage;
			}
		});
	}

	//刷新当前标签Tabs
    function RefreshTab(currentTab) {
        var url = $(currentTab.panel('options')).attr('href');
         $('#tabs').tabs('update', {
            tab: currentTab,
            options: {
                href: url
            }
        });
        currentTab.panel('refresh');
  }
    function reflush(){
    	var currentTab = $('#index-tabs').tabs('getSelected');
  		 RefreshTab(currentTab);
  	}

	//postData赋值
	function setPostData(postData, ul){
		$(ul).eq(0).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox2"){
				var v = $($(this).find("input")[0]).combobox("getText")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});
	}
	//设置编辑页面内容清空
	function setEditInfoClear(ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
	}
	//设置编辑页面内容初始化
	function setEditInfoInit(row, ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
	}
	//设置编辑页面可编辑属性
	function setEditInfoReadOnly(flag, ul){
		var readonly = true, able = 'disable';
		if(flag == 1){
			readonly = false;
			able = 'enable';
		}
		$(ul).find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
			else if(control == "checkbox"){
				if(flag == 1)$($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
	}
	//按钮有效 flag 1 编辑 2 查看
	function setButtonEditMode(flag, div){
		var able = 'enable', dable = 'disable';
		if(flag == 1){
			able = 'disable';
			dable = 'enable';
		}
		$(div).find("a").each(function(){
			var hint = $(this).attr("hint");
			if(hint == "add")$(this).linkbutton(able);
			if(hint == "mod2")$(this).linkbutton(able);
			if(hint == "del")$(this).linkbutton(able);
			if(hint == "save")$(this).linkbutton(dable);
			if(hint == "cancel")$(this).linkbutton(dable);
		});
	}

	var operatorId='${sessionScope.operatorId}';
	var operName='${sessionScope.operName}';
	var f_org_num = '${sessionScope.f_org_num}';
	// function showMessage(){
	// 	$('#messager-window').window('open');
	// }
	function showMessage(){
		$('#messager-window1').window('open');
	}
	 function updatePassword(){
		  $('#update-password-textbox-operName').textbox("setValue",operName);
		  $('#update-password-textbox-password').textbox("setValue","");
		  $('#update-password-textbox-newPassword').textbox("setValue","");
		  $('#update-password-textbox-newPasswordAgain').textbox("setValue","");
		  $('#update-password-window').window('open');
	 }
	 function updatePassword_cancel(){
		  $('#update-password-textbox-password').textbox("setValue","");
		  $('#update-password-textbox-newPassword').textbox("setValue","");
		  $('#update-password-textbox-newPasswordAgain').textbox("setValue","");
		  $('#update-password-window').window('close', true);
	 }

	function getPassLevel(pass) {
		if(typeof pass !== 'string' || !pass) return 0;
		var level = 0;
		// pass = String(pass).toLocaleLowerCase();
		// 小写字母，级别+1
		if (/[a-z]/.test(pass)) level++;
		if (/[A-Z]/.test(pass)) level++;
		// 数字+1
		if (/[0-9]/.test(pass)) level++;
		// 其他+1
		if (/[\`\~\!\@\#\$\%\^\&\*\(\)\_\+\-\=\\\|\[\]\{\}\;\'\:\"\,\.\/\<\>\?]/.test(pass)) level++;
		return level;
	}

	 function updatePassword_save(){
		  var password = $('#update-password-textbox-password').textbox("getValue");
		  if (password == undefined || password == '') {
	            $.messager.alert('提示','请输入原密码！','warning');
	            return;
	        }
		  var newPassword = $('#update-password-textbox-newPassword').textbox("getValue");
		  if (newPassword == undefined || newPassword == '') {
	            $.messager.alert('提示','请输入新密码！','warning');
	            return;
	        }
		  var newPasswordAgain = $('#update-password-textbox-newPasswordAgain').textbox("getValue");
		  if (newPasswordAgain == undefined || newPasswordAgain == '') {
	            $.messager.alert('提示','请输入确认密码！','warning');
	            return;
	        }
		  if(newPassword!=newPasswordAgain){
			  $.messager.alert('提示','新密码和确认密码不一致！','warning');
	            return;
		  }
			const passwordBak = newPassword
		  password = hex_md5(password).toUpperCase();
  		  newPassword = hex_md5(newPassword).toUpperCase();
		  $.ajax({
				url: "/eip-web-crm/operator/updatePassword",
				dataType: "json",	//返回数据类型
				type: "post",
				data: {
					pwdGrade: getPassLevel(passwordBak),
					operatorId,
					password,
					newPassword
				},	//发送数据
				async: true,
				success: function(data){

					if(data.state == 1){
						$.messager.alert('提示','您输入的原密码有误！','error');
					}else if(data.state == 2){
						$.messager.alert('提示','修改成功！','info');
						$('#update-password-window').window('close', true);
					}else{
						$.messager.alert('提示','数据发送失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		 // $('#update-password-window').window('close', true);
	 }

</script>
</head>
<body class="easyui-layout">

<!-- begin of header -->
<div class="my-header" data-options="region:'north',border:false,split:false">
	<div class="my-header-left" >

			<div style="display:inline-block;padding:12px 20px 3px 40px;"><img alt="logo"   src="${eippath}/img/logo.png"></div>
			<div class="Ti"  style="display:inline-block;margin-top:0px;"><h1 style="padding:0px;">信息门户后台管理</h1></div>

		  <%-- <img alt="logo"  style="padding:12px 20px 3px 40px;"  src="${eippath}/img/logo.png"> --%>
		  <!-- <h1 style="display: inline-block;margin-top:0px;" >信息门户后台管理</h1>  -->
		  <!-- <h1 style="display: inline-block;padding:0px;" >信息门户后台管理</h1>  -->
	</div>
	<div class="my-header-right">
        <%
			if(fu_menu_member)out.println("<a  target=\"_blank\" href=\"javascript:void(0);\"  id = \"jumpA\" >电脑版会员中心</a>");
			out.println("<a  target=\"_blank\" href=\"javascript:void(0);\"  id = \"vip_mobile_center\" >手机版会员中心</a>");
			if(fu_menu_mock_sponsor)out.println("<a  target=\"_blank\" href=\"/eip-web-business/pages/Mock-Sponsor/index.html\"  id = \"jumpA\" >主办方官网</a>");
			if(fu_menu_mobile_mock)out.println("<a  target=\"_blank\" href=\"/eip-web-business/pages/mobile-sponsor/index.html\"  id = \"jumpA\" >官网(手机版)</a>");
			if(fu_menu_new_crm)out.println("<a  target=\"_blank\" href=\"/eip-web-crm/pages/crm/newCRM.html\"  id = \"jumpA\" >新版crm</a>");
			if(fu_menu_message)out.println("<a  target=\"_blank\" href=\"\"  id = \"jumpA2\" >消息服务器状态</a>");
        %>

<!--       	<a  target="_blank" href="http://www.expo2c.com:8080/RegSever/RegPage/pages/login.html"  id = "jumpA" >观众登记</a> -->



		<!-- <a  target="_blank" href="/eip-web-business/pages/bscrm/newCRM.html"  id = "jumpA" >新版crm框架草图</a> -->

		<!-- <a  target="_blank" href="/eip-web-sponsor/pages/crm/newCRM.html"  id = "jumpA" >新版crm</a>
		<a  target="_blank" href=""  id = "jumpA2" >消息服务器状态</a> -->
        <span style="margin-left:60px;" id="index_versionNum"></span>

 		<p>欢迎您，${sessionScope.employeeName}！&nbsp;<a href="javascript:void(0);" onclick="updatePassword()">修改密码</a>&nbsp;<a href="javascript:void(0);" onclick="logout()">退出</a></p>
	</div>
</div>
<!-- end of header -->
<!-- begin of sidebar -->
<div class="my-sidebar" data-options="region:'west',split:true,border:true,title:'功能菜单'">
	<!-- <div class="easyui-accordion" data-options="border:false,fit:true,selected:false"> -->
	<div class="easyui-accordion" id="js-mainMenu" data-options="border:false,fit:true">
		<%@ page import="java.util.List" %>
		<%
			/*for(Menu m : menus){
				if(m.getParentId() == -1){
					out.println("<div title=\"" + m.getMenuName() + "\" data-options=\"iconCls:'" + m.getMenuNote() + "'\" style=\"padding:5px;\">" +
						"<ul class=\"easyui-tree my-side-tree\" data-options=\"url:'" + request.getContextPath() +
						"/backstage/getMenuTree?parentId=" + m.getMenuId() +
						"',method:'post',onLoadSuccess:function(node, data){bindMenuTreeClick();}\"></ul></div>");
				}
			} */
			for(Menu m : menus){
				if(m.getParentId() == -1){
					out.println("<div title=\"" + m.getMenuName() + "\" data-options=\"iconCls:'" + m.getMenuNote() + "'\" style=\"padding:5px;\">" );
					//获取子节点
					List<Menu> menusTemp = menuList.getChildMenus(m.getMenuId());
					if(menusTemp.isEmpty() == false){
						for(Menu m1 : menusTemp){
							out.println("<div class=\"exhibit-main-div2\" style=\"margin:20px auto;text-align: center; \" ><a href=\"javascript:void(0);\" data-link=\"/eip-web-sponsor"+m1.getBplName()+"\" iframe=\"0\" title=\""+m1.getMenuName()+"\" style=\"display: block;\">");
							out.println("<image src=\"/eip-web-sponsor"+m1.getImageUrl()+"\" />");// /img/exhibit_main2.png
							out.println("<p style=\"text-align:center;color:#333;font-family: microsoft yahei;line-height: 0px;\">"+m1.getMenuName()+"</p></a>");
							out.println("</div>");
						}

					}
					out.println("</div>");
				}
			}
		%>


	</div>
</div>
<!-- end of sidebar -->
<!-- begin of main -->
 <div data-options="region:'center'">
	<div id="index-tabs" class="easyui-tabs" data-options="border:false,fit:true">
<!-- 		<div title="首页" data-options="href:'${eippath}/backstage/main/main',closable:false,cls:'pd3'"></div> -->
	</div>
</div>
<!-- end of main -->
<!-- begin of easyui-window -->
 <div id="update-password-window" class="easyui-window"
	toolbar="#update-password-window-toolbar" data-options="closed:true,title:'修改密码',modal:true,maximizable: false,resizable: false,minimizable: false" style="width:572px;">

	<div id="update-password-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="update-password-save" class="easyui-linkbutton" iconCls="icon-save" onclick="updatePassword_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="update-password-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="updatePassword_cancel()" plain="true">取消</a>
	    </div>
    </div>

    <div id="update-password-panel-1" class="easyui-panel" title="基本信息" style="width:558px;height:150px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<label>用户名</label>&#12288;&nbsp;&nbsp;
        		<input id="update-password-textbox-operName" class="easyui-textbox" readonly style="width:170px;height:25px">
        	</li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<label>原密码</label>&#12288;&nbsp;&nbsp;
        		<input id="update-password-textbox-password" class="easyui-textbox" type="password" style="width:170px;height:25px">
        	</li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<label>新密码</label>&#12288;&nbsp;&nbsp;
        		<input id="update-password-textbox-newPassword" class="easyui-textbox" type="password" style="width:170px;height:25px">
        	</li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<label>确认密码</label>&nbsp;&nbsp;
        		<input id="update-password-textbox-newPasswordAgain" class="easyui-textbox" type="password" style="width:170px;height:25px">
        	</li>
        </ul>
    </div>

</div >

<!-- begin of easyui-window -->
<div id="messager-window" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar" data-options="closed:true,title:'系统消息',modal:false,maximizable: true,resizable: true,minimizable: true" style="width:572px;">
	<div style="display:none">
        <span>sessionId:</span>
        <%
            HttpSession s= request.getSession();
            out.println(s.getId());
        %>
    </div>
	<div id="alive"></div>
    <input id="sessionId" type="hidden" value="<%=session.getId() %>" />
    <input id="text" type="text" />
    <button onclick="send()">发送消息</button>
    <hr />
    <!-- <button onclick="closeWebSocket()">关闭WebSocket连接</button> -->
	<!--<button onclick="closeWebSocket()">服务器发送消息给客户端</button>-->
    <div id="message"></div>
	<div data-options="region:'center'" style="height:500px;">
		<table id="exhibitor-cost-datagrid-contract" class="easyui-datagrid"
	       	data-options="rownumbers:true,singleSelect:true,multiSort:true,fitColumns:true,fit:true,method:'post'
	       	,selectOnCheck:true,">
	       	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'awokeSummary',width:40">消息标题</th>
					<th data-options="field:'operName',width:30" >发送人</th>
					<th data-options="field:'awokeDetail',width:100">内容</th>
					<th data-options="field:'createTime',width:40" >发送时间</th>
					<th data-options="field:'f_price',width:30" >不再提醒</th>
				</tr>
			</thead>
	       </table>
	</div>
</div>
<!-- end of easyui-window  -->

<!-- begin of footer -->
<div class="my-footer" data-options="region:'south',border:true,split:false">
	<div class="my-message-div"><a href="javascript:void(0);" onclick="showMessage()">系统消息<!-- <span class="message-count">5</span>条 --></a></div>
	技术支持： 杭州展之信息技术有限公司
</div>

<!-- 通知消息 -->
<div id="messager-window1" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar" data-options="closed:true,title:'系统消息',modal:false,maximizable: true,resizable: true,minimizable: true" style="width:100%;height: 100%">

	<!-- 通知消息 -->
	<div class="newsWindow" id="newsWindow">
		<div class="news_windowstop" style="height: 2px !important;position: absolute;top: 0; width: 100%; display: block; z-index: 11;">
			<h1 style="height: 2px !important;"></h1>
		</div>
		<div class="news_windowstop" id="dv">
			<h1>我的通知消息</h1>
			<input type="button" class="refreshs" name="" id="" value="刷新" />
			<input type="button" class="dels" name="" id="" value="删除" />
			<a href="javascript:void(0);" class="shutDown" onclick="shutDown()" ><img src="img/shutdown.png" ></a>
		</div>
		<div class="news_windowsbotton">
			<div class="news_windowleft">
				<ul>
					<li>
						<div class="news_head">
							<img src="">
						</div>
						<div class="news_text">
							<h1><i></i>客服主管-李飞</h1>
							<p><span>我是任务主题</span><span>12:46</span></p>
						</div>
					</li>
					<li>
						<div class="news_head">
							<img src="">
						</div>
						<div class="news_text">
							<h1><i></i>客服主管-李飞</h1>
							<p><span>我是任务主题</span><span>10-24</span></p>
						</div>
					</li>
				</ul>
			</div>
			<div class="news_windowright">
				<h1>消息通知的主题</h1>
				<div class="news_windowcontent">
					<div class="content_top">
						<p>通知类型：<span>尾款通知</span></p>
						<p>消息来自：<span>客服主管-李飞</span></p>
						<p>提醒时间：<span>2019.12.28 14:31</span></p>
					</div>
					<div class="content_button">
						<div class="content_text">我是这次通知的详细内容我是这次通知的详细内容，我是这次通知的详细内容我是这次通知的详细内容，我是这次通知的详细内容
							我是这次通知的详细内容</div>
						<div class="content_buttoninp">
							<input type="button" name="" id="" value="立即跟进" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>


</div>
<!-- end of footer -->
<script>
	var openMenu = sessionStorage.getItem('openMenu');
	if(openMenu) sessionStorage.removeItem('openMenu');
	/**
	* Name 添加菜单选项
	* Param title 名称
	* Param href 链接
	* Param iconCls 图标样式
	* Param iframe 链接跳转方式（true为iframe，false为href）
	*/
	$(function(){
		//var url = window.location.href;
		//var prof
		//alert(window.location.protocol);
		$('#jumpA2').attr('href',window.location.protocol+'//'+window.location.hostname+':8079/msgnotify/static/pages/hello.html');
		$('#index-tabs').tabs({
			onSelect: function(title,index){
				//alert(title);
			},
			onLoad: function(panel){
					if($('#divAccountLogin').length > 0){
							top.window.location.href = "${pageContext.request.contextPath}/backstage/indexA";
					}
			}
		});
		setTimeout(function(){
			if(<% if(fu_show_exhibit_map)out.println("true");else out.println("false"); %>){
				addTab("展会中心", "${eippath}/backstage/project/show_exhibit_map", 0, true);
			} else {
				// 打开第一个子菜单
				$('.my-sidebar .exhibit-main-div2:eq(0)>a').trigger('click')
			}
			var $a;
			if(openMenu == 'task_kind_org') {
				$a = $('a[data-link="/eip-web-sponsor/backstage/system/task_kind_org"]');
			} else if(openMenu == 'visa_data') {
				$a = $('a[data-link="/eip-web-sponsor/backstage/system/visa_data"]');
			} else if(openMenu == 'exhibit_tool') {
				$a = $('a[data-link="/eip-web-sponsor/backstage/system/exhibit_tool"]');
			} else if(openMenu == 'channel_type_set') {
				$a = $('a[data-link="/eip-web-sponsor/backstage/system/channel_type_set"]');
			} else if(openMenu == 'visa_data') {
				$a = $('a[data-link="/eip-web-sponsor/backstage/system/visa_data"]');
			}
			if($a && $a.length){
				$a.click();
				$('#js-mainMenu').accordion('select',$a.closest('.panel-htop').index())
			}
		}, 0);

		$("#jumpA").attr("href", window.origin+"/eip-web-vip/pages/frontend/zzy-denglu.html?orgNum=" +  f_org_num);/* exhibitor/pages/common/login.html?__hbt=1552873306073 */
		$('#vip_mobile_center').attr('href',window.origin + '/eip-web-vip/mobile-vipcenter/index.html?orgNum=' + f_org_num)
		//  let jump = document.querySelector('#vip_mobile_center')
		// 	jump.onclick=function() {
		// 		window.open(window.origin + '/eip-web-vip/mobile-vipcenter/index.html?orgNum=' + f_org_num)
		// 	}
		$('.exhibit-main-div2 a').bind("click", function(){
			exhibitMainAddTab($(this))
		});
		$("#exhibitor-cost-datagrid-contract").datagrid({
			url:"${eippath}/awoke/getAwokeList",
			singleSelect:true,//单选模式
			fit:true, //平铺
			pagination : true,
            onDblClickRow: function (rowIndex, rowData) {
                console.log(rowData);
                //展商参展提醒
                if (rowData.awokeType===1){
                    var url='/eip-web-sponsor/backstage/exhibit/index?exhibitCode='
                        +rowData.exhibitCode+'&projectId='+rowData.projectId+'&recordCode='+rowData.recordCode
                        +'&awokeType='+rowData.awokeType;
					window.open(url,'target','');
                }
            }
        });
        //alert('开始跳转');
        //window.location.href = "/eip-web-sponsor/backstage/exhibit/index?exhibitCode=E0000000040&projectId=411";
        queryVersion();
    })

    function queryVersion(){
    	$.ajax({
			url: eippath + '/sysSetting/getVersionNum',
			dataType: "json",	//返回数据类型
			type: "post",
			async: true,
			success: function(data){
				if(data.state>0){
					$("#index_versionNum").html("版本号："+data.data);
				}
			},
			error: function(){

			}
		});
    }

</script>

<%--<script type="text/javascript">--%>
<%--	//窗口移动--%>
<%--	$("#dv").mousedown(function(event) {--%>
<%--		var isMove = true;--%>
<%--		var abs_x = event.pageX - $('#newsWindow').offset().left;--%>
<%--		var abs_y = event.pageY - $('#newsWindow').offset().top;--%>
<%--		$(document).mousemove(function(event) {--%>
<%--			if (isMove) {--%>
<%--				var obj = $('#newsWindow');--%>
<%--				var left_x = event.pageX - abs_x;--%>
<%--				var top_y = event.pageY - abs_y;--%>
<%--				obj.css({--%>
<%--					'left': left_x,--%>
<%--					'top': top_y,--%>
<%--				});--%>
<%--				$("#dv").css("cursor","move")--%>
<%--			}--%>
<%--		}).mouseup(function() {--%>
<%--			isMove = false;--%>
<%--		});--%>
<%--	});--%>

<%--</script>--%>
</body>
</html>

