<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<%@ include file="common/include_base.jsp" %>
<script type="text/javascript">
	$(function(){
		
	});
	function exportE(){
		jQuery('<form action="${eippath}/excel/exportExcel" method="post"></form>')
            .appendTo('body').submit().remove();
	};
	function importE(){
		//$("#fff").submit();
		var formData = new FormData();
		var name = $("#excelFile").val();
		formData.append("excelFile", $("#excelFile")[0].files[0]);
		//formData.append("name", name);
		$.ajax({
			url: "${eippath}/excel/importExcel",
			dataType: "json",	//返回数据类型
			type: "post",
			data: formData,	//发送数据
			//告诉jQuery不要去处理发送的数据
        	processData: false,
        	//告诉jQuery不要去设置Content-Type请求头
        	contentType: false,			
			async: true,
			success: function(data){
				if(data.state == 1)$.messager.alert('提示','导入成功！','info');
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
/*
 	jQuery.download = function (url, data, method) {
        // 获得url和data
        if (url && data) {
            // data 是 string 或者 array/object
            data = typeof data == 'string' ? data : jQuery.param(data);
            // 把参数组装成 form的  input
            var inputs = '';
            jQuery.each(data.split('&'), function () {
                var pair = this.split('=');
                inputs += '<input type="hidden" name="' + pair[0] + '" value="' + pair[1] + '" />';
            });
            // request发送请求
            jQuery('<form action="' + url + '" method="' + (method || 'post') + '">' + inputs + '</form>')
            .appendTo('body').submit().remove();
        };*/
</script>
</head>
<body>
<button onclick="exportE()">导出</button>
<br />
<!-- 
<form id="fff" method="POST" enctype="multipart/form-data" action="${eippath}/excel/importExcel">
  	导入: <input type="file" name="excelFile"><br/>
</form>
 -->
 
 导入: <input type="file" id="excelFile" name="excelFile"><br/>
<button onclick="importE()">提交</button>
</body>
</html>