<!--展馆展区图形设置 -->
<%@ page contentType="text/html;charset=UTF-8" %>
 <!DOCTYPE html>
<!--  <!DOCTYPE html PUBLIC "-//WAPFORUM//DTD XHTML Mobile 1.0//EN" "http://www.wapforum.org/DTD/xhtml-mobile10.dtd">   -->
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no">
<!-- <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no" /> -->
<!--  <meta name="viewport" content="user-scalable=0" />  -->
<title>动态展位图：${exhibition.exihibitName}</title>

<%@ include file="../common/include_base_draw.jsp" %>

<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
	fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
	fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
	fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
	fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
	fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	//List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	//for(String s : funCodes){
	//	if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
	//	else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
	//	else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
	//	else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
	//	else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
	//	else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
	//	else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
	//	else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
	//	else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
	//	else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
	//	else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
	//	else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
	//}
%>
<style type="text/css">

a,
a:link { color:#333333; text-decoration:none; outline:none; }
a:hover,
a:focus { text-decoration:none; color:#39f; }

.pd3 { padding:3px; }
.pd5 { padding:5px; }
.pd10 { padding:10px; }

.my-text,
.my-textarea { padding:3px; border:1px #95b8e7 solid; width:260px; }
.my-text { height:14px; line-height:14px; }
.my-checkbox { height:16px; width:16px; }
.my-header { height:71px; position:relative; z-index:0; overflow:hidden; border-bottom:1px #4c72fe solid; background:#4c72fe bottom repeat-x; }
.my-header-left { position:absolute; z-index:1; left:30px; top:0; }
.my-header-left h1 { font:30px/30px Verdana; color:#fff; }
.my-header-right { position:absolute; z-index:1; right:5px; top:4px; color:#fff; text-align:right; }
.my-header-right p { line-height:0.7em; }
.my-header-right a { color:#fff; margin:0 5px; }

.my-sidebar { width:150px; }
.my-side-tree .tree-node { padding:3px 0px; }
.my-side-tree a { display:block; }
.my-side-tree a div { width:100px; }
.my-side-tree .tree-node-selected { padding:2px 0; border:1px #fade23 solid; }

.my-footer { height:80px;line-height: 80px; text-align:center; overflow:hidden; background:#fff; }

.my-toolbar-button,
.my-toolbar-search { padding:2px 5px; }

 input{outline:none;}
 textarea{outline:none;}

#map-svg{
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
}

.saved-shape {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill-opacity: 0.5;
	stroke-opacity: 0.5;
}
.saved-section-shape {
	stroke: #31b0d5;
	stroke-width: 1px;
 	fill: #E0ECFF;
	fill-opacity: 0.5;
	stroke-opacity: 0.5;
}
.opacity {
	fill-opacity: 0.5 !important;
	stroke-opacity: 0.5 !important;
}
/* 农博会 */
.common-top {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 3;
}
.per100 {
    width: 100%;
}
.oh {
    overflow: hidden;
}
.back {
    width: .88rem;
    height: .86rem;
    display: block;
    position: absolute;
    top: 0rem;
    left: 0rem;
    background: url(../../img/back.png) no-repeat center center;
    background-size: .2rem;
}
.tops {
    width: 100%;
    position: relative;
    background-color: #28ae39;
    height: .88rem;
}
.tops>input{
	width: 6.5rem;
    height: .66rem;
    border-radius: .1rem;
    border: solid .01rem #e5e5e5;
    display: inherit;
    margin: .1rem .16rem .1rem .88rem;
    float: left;
    box-sizing: border-box;
    padding-left: .58rem;
    font-size: .3rem;
    color: #333;
}
.tops>input:first-child{
    background: url(../../img/search1.png) no-repeat .18rem center #f5f5f5;
	background-size: .28rem;
}
.tops>input:placeholder{
	    color: #b3b3b3;
}
.top-op5 {
    background-color: #fff;
}
.pavilion-map-text {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
	z-index: 4;
}
.pavilion-map-search {
    width: 4.02rem;
    height: .8rem;
    background-color: #ffffff;
    box-shadow: 0px 0px 0.2rem 0px rgba(0, 0, 0, 0.3);
    border-radius: .4rem;
    margin: 0 0 .3rem .24rem;
    overflow: hidden;
}
.pavilion-map-search input:first-child {
    outline: none;
    border: none;
    width: 100%;
    height: 100%;
    font-size: .3rem;
    box-sizing: border-box;
    padding-left: .65rem;
    background: url(../../img/search1.png) no-repeat .27rem center;
    background-size: .28rem;
    float: left;
    display: inline-block;
}
.map-search-line {
    display: inline-block;
    width: .02rem;
    height: .5rem;
    background-color: #f5f5f5;
    float: left;
    margin: .18rem 0;
}
.map-search-button {
    width: 1.21rem !important;
    color: #06a21a !important;
    font-size: .3rem !important;
    float: right !important;
    background-color: #fff !important;
    border: none !important;
    padding-left: 0 !important;
    margin: 0 !important;
    margin-top: .1rem !important;
	box-shadow: none;
}
.pavilion-map-info {
    width: 100%;
    height: 1.44rem;
    background-color: #ffffff;
    box-shadow: 0px 0px 0.31rem 0px rgba(0, 0, 0, 0.4);
    border-radius: .24rem .24rem 0px 0px;
    overflow: hidden;
	position: relative;
}
.pavilion-map-left {
    float: left;
}
.pavilion-map-left h6 {
    font-size: .34rem;
    font-weight: bold;
    color: #333333;
    margin: .2rem 0 0 .28rem;
    max-width: 5.14rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.pavilion-map-left span {
    font-size: .32rem;
    color: #666666;
    display: block;
    margin: .1rem 0 0 .3rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 74%;
}
.pavilion-map-right {
/* 	position: absolute;
	right: .1rem;
    margin: .4rem .34rem 0 0; */
    overflow: hidden;
    width: 100%;
	margin-top: 1.2rem;
	margin-bottom: .6rem;
}
.pavilion-map-right a {
    width: 2.14rem;
    height: .7rem;
	box-shadow: none;
    border-radius: .35rem;
    display: block;
    font-size: .28rem;
    color: #fff;
    line-height: .7rem;
    text-align: center;
	float: left;
	margin: 0 .24rem 0;
}
.go_exhibitors{
    background-image: linear-gradient(90deg,
		#18ab3b 0%,
		#2fcc54 100%);
	box-shadow: 0px 10px 10px 0px
				rgba(40, 174, 57, 0.13);
	margin-left: .24rem !important;
}
.go_exhibits{
    background-image: linear-gradient(87deg,
		#f09e26 0%,
		#f8b520 100%);
	box-shadow: 0px 10px 10px 0px
				rgba(255, 152, 0, 0.15);
}
.exhibits_location{
	border: solid 1px #28ae39;
	color: #28ae39 !important;
}
.zoom-buttons{
	position: fixed;
	top:2.69rem;
	right: .26rem;
}
.zoom-buttons input{
    width: .7rem;
    height: .7rem;
    background-color: #ffffff;
    border-radius: .35rem;
    border: none;
    outline: none;
    padding: 0;
    display: block;
    margin-top: .26rem;
    opacity: .8;
    box-shadow: 0px 0px 0.1rem 0px rgba(0, 0, 0, 0.25);
}
/* .zoom-buttons input:hover{
background-color: #f5f5f5;
} */
.magnify-button{
	background-image: url(../../img/magnify.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: .34rem;
}
.shrink-button{
	background-image: url(../../img/shrink.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: .34rem;
}
.go-upper-story{
    background-image: url(../../img/bank.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: .34rem;
}
.avtives{
	background-color: #28ae39 !important;
	box-shadow: 0 0.1rem 0.1rem 0 rgba(40, 174, 57, 0.2) !important;
}

.all_company_info{
	position: fixed;
	bottom: 1rem;
	width: 100%;
	background-color: #ffffff;
	box-shadow: 0px 0px .31rem 0px
		rgba(0, 0, 0, 0.1);
	border-radius: 24px 24px 0px 0px;
	max-height: 80%;
	overflow: hidden;
	overflow-y: auto;
	/* height: 4.4rem; */
}
.indicat_arrow{
    height: .4rem;
    text-align: center;
    width: 100%;
	box-sizing: border-box;
	padding-top: 0.18rem;
	background-color: #ffffff;
	position: fixed;
	z-index: 1;
}
.indicat_arrow_img_xz{
	transform: rotate(-180deg);
}
.indicat_arrow img{
   width: .52rem;
   	height: .12rem;
	display: block;
	margin: 0 auto;
}
.company_title{
	overflow: hidden;
}
.company_logo{
	float: left;
	width: 1rem;
    height: 1rem;
    background-color: #000000;
    border-radius: .06rem;
    border: solid .1rem #e5e5e5;
    margin: 0 .18rem .24rem .24rem;
}
.company_logo img{
	width: 100%;
	height: 100%;
	display: block;
}
.company_name{
	float: left;
	box-sizing: border-box;
	padding-left: .24rem;
    width: 90%;
	margin-top: .4rem;
}
.company_name h6{
	font-size: .32rem;
	font-weight: bold;
	color: #333333;
	margin: 0;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.company_name span{
	font-size: .26rem;
	color: #333;
	display: block;
	margin-top: .09rem;
	word-break: break-all;
	word-wrap: break-word;
	width: 100%;
}
.company_texts{
	position: relative;
    margin-bottom: .7rem;
}
.company_texts span{
    width: 1rem;
    height: .4rem;
    font-size: .24rem;
    color: #06a21a;
    position: absolute;
    right: 0;
    top: .3rem;
    line-height: .4rem;
    background-color: #fff;
    box-sizing: border-box;
    padding-left: .16rem;
}

.company_text,
.remark{
    box-sizing: border-box;
    padding: 0 .24rem;
	margin-top: .24rem;
    height: .62rem;
    overflow: hidden;
	font-size: .24rem;
	color: #333333;
}
.company_text p,
.remark p{
	font-size: .24rem;
	color: #4d4d4d;
	margin: 0;
	line-height: .34rem;
}
.collect_button_sc{
	position: fixed;
	bottom: 0;
	width: 100%;
	height: 1rem;
	background-color: #fafafa;
}
.company_product{
	overflow: hidden;
    margin-top: .24rem;
    width: 100%;
    box-sizing: border-box;
    padding-left: 0.22rem;
    padding-bottom: .26rem;
}
.company_product ul li{
	float: left;
	margin-right: .24rem;
}
.company_product ul li:nth-of-type(3n){
	margin-right: 0;
}
.company_product_img{
    width: 2.2rem;
	height: 1.42rem;
	background-color: #ffffff;
	border-radius: .04rem;
}
.company_product_img img{
    width: 100%;
	height: 100%;
	display: block;
}
.company_product ul li h6{
	font-size: .26rem;
	color: #333333;
	list-style: none;
    text-align: center;
    margin: .14rem auto;
    width: 2.20rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}
ul,li{
	list-style: none;
	margin: 0;
	padding: 0;
}
.collect_button_sc input{
	width: 100%;
	height: 100%;
	outline: none;
	border: none;
	font-size: .32rem;
    color: #4d4d4d;
	text-align: center;
	background: url(../../img/agritech_img/collect.png) no-repeat 2.9rem center #fafafa;
	background-size: .4rem;
	box-sizing: border-box;
	padding-left: 0.7rem;
	display: block;
}
.collect_button_sc input:hover{
	background-color: #f5f5f5;
}
.company_info_close{
    position: fixed;
    /* top: 0rem; */
    width: .6rem;
    height: .6rem;
    box-sizing: border-box;
    padding-top: .22rem;
    right: .1rem;
	z-index: 9;
}
.company_info_close img{
    display: block;
    margin: 0 auto;
     width: .21rem;
    height: .21rem;
}
.all_company_info_active{
	height: 80%;
	overflow-y: auto !important;
}
.xz{
	transform: rotate(0deg);
}
.auto_height{
	height: auto !important;
}
/* 询盘内容 */
.product_all_info{
	position: fixed;
	top:10%;
	left: 0;
	width: 100%;
	background-color: #ffffff;
	box-shadow: 0px 0px 0.3rem 0px rgba(0, 0, 0, 0.4);
	border-radius: .1rem;
	z-index: 4;
}
.product_all_info_title{
	overflow: hidden;
	box-sizing: border-box;
	padding: .3rem 0 0rem .24rem;

}
.product_all_info_img{
	float: left;
	width: 1.55rem;
    height: 1rem;
    border-radius: .06rem;
	margin-right: .18rem;
    margin-bottom: .3rem;
}
.product_all_info_img img{
	width: 100%;
	height: 100%;
	display: block;
}
.product_all_info_name{
	float: left;
	width: 5.4rem;
}
.product_all_info_name h6{
    margin: 0;
    padding: 0;
    font-size: .32rem;
    font-weight: bold;
    color: #333333;
    margin-top: .08rem;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.product_all_info_name span{
    font-size: .26rem;
    color: #666666;
    display: block;
    margin-top: .1rem;
}
.product_all_info_text{
    box-sizing: border-box;
    padding: 0 .24rem;
    padding-bottom: .3rem;
    max-height: 7rem;
    overflow-y: auto;
}
.product_all_info_text p{
	font-size: .24rem;
    line-height: .34rem;
    color: #4d4d4d;
	margin: 0;
}
.commodity-botton {
    overflow: hidden;
    bottom: 0;
    width: 100%;
}

.commodity-botton ul li {
	width: 50%;
	float: left;
	background-color: #f5f5f5;
	height: 1rem;
	line-height: 1rem;
	text-align: center;
	font-size: .32rem;
	color: #4d4d4d;
}

.commodity-botton ul li p {
	position: relative;
	box-sizing: border-box;
	padding-left: .5rem;
	margin: 0;
}

.commodity-botton ul li:hover {
	background-color: #eaeaea;
}

.collect {
	background: url(../../img/agritech_img/collect.png) no-repeat center;
	background-size: .4rem;
	width: .4rem;
	height: .35rem;
	display: inline-block;
	margin-right: .13rem;
	position: absolute;
	top: .32rem;
	left: 1.2rem;
}

.inquiry {
	background: url(../../img/agritech_img/inquiry.png) no-repeat center;
	background-size: .38rem;
	width: .38rem;
	height: .37rem;
	display: inline-block;
	margin-right: .13rem;
	position: absolute;
	top: .32rem;
	left: 1.32rem;
}

/* .top-op5 {
	background-color: #383838;
	opacity: 0.5;
} */

.commodity-botton-list2:hover .inquiry {
		background-size: .38rem;
}
em{
	font-style: normal;
}
.inquiry-window {
	width: 7.02rem;
	/* height: 7.5rem; */
	background-color: #ffffff;
	box-shadow: 0px 0px 0.6rem 0px rgba(0, 0, 0, 0.2);
	border-radius: .2rem;
	margin: 0 auto;
	position: fixed;
	z-index: 9;
	top: 16%;
	left: 50%;
	margin-left: -3.51rem;
	box-sizing: border-box;
	padding: 0 .44rem;
}

.inquiry-window p {
	font-size: .26rem;
	color: #666666;
	margin-top: .12rem;
}

.inquiry-window p span {
	width: 1.6rem;
	display: inline-block;
}

.inquiry-window h6 {
	font-size: .36rem;
	color: #333333;
	font-weight: bold;
	text-align: center;
	margin: .54rem 0 .3rem;
}

.inquiry-window ul li {
	overflow: hidden;
	border-bottom: .02rem solid #cccccc;
	height: .96rem;
	box-sizing: border-box;
	padding-top: 0.32rem;
}

.inquiry-window ul li label {
	font-size: .32rem;
	color: #999999;
	float: left;
	width: 1.6rem;
}

.inquiry-window ul li input {
	display: inline-block;
	float: left;
	color: #333333;
	font-size: .32rem;
	width: 4.5rem;
	/* overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap; */
	border: none;
}

.inquiry-window-button {
	overflow: hidden;
	margin-top: .8rem;
	margin-bottom: .8rem;
}

.inquiry-window-button input {
	width: 2rem;
	height: .88rem;
	border-radius: .44rem;
	font-size: .3rem;
}
.adds_products_button {
	color: #fff;
	border: none;
	background-color: #28ae39;
	box-shadow: 0px 0.1rem 0.1rem 0px rgba(40, 174, 57, 0.2);
}

.removes_products_button {
	box-sizing: border-box;
	background-color: #fff !important;
	color: #666666;
	border: solid .02rem #999999 !important;

	box-shadow: none;
}
.commodity-botton-list2:hover .inquiry{
	background: url(../../img/agritech_img/inquiry1.png) no-repeat center;
	background-size: .38rem;
}
.adds_products_button:hover {
	background-color: #06a21a;
}

.removes_products_button:hover {
	border: solid .02rem #06a21a !important;
	color: #06a21a !important;
}
.shadow{
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: .2;
    z-index: 3;
}
/* .exhibition-area{

}
.exhibition-area h6{

} */
.exhibition-area-img{
	width: 100%;
	box-sizing: border-box;
	padding: .24rem .22rem;
}
.exhibition-area-img img{
	width: 7.02rem;
}
/* end */
<c:forEach var="item" items="${salestateColors}">
.shape-salestate-${item.saleState} {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: ${item.stateColor};
    fill-opacity: 0;
    stroke-opacity: 0.5;
	/*fill-opacity: 0.5;*/
	/*stroke-opacity: 0.5;*/
}

</c:forEach>

.new-booth {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: #8d85de;
    fill-opacity: 0;
    stroke-opacity: 0.5;
	/*fill-opacity: 0.5;*/
	/*stroke-opacity: 0.5;*/
}
.new-exhibition {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: #74b3e4;
    fill-opacity: 0;
    stroke-opacity: 0.5;
    /*fill-opacity: 0.5;*/
	/*stroke-opacity: 0.5;*/
}
.new-meeting {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: #c76235;
	fill-opacity: 0.5;
	stroke-opacity: 0.5;
}
.new-advertising {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: #ab7a34;
	fill-opacity: 0.5;
	stroke-opacity: 0.5;
}
.new-other {
	stroke: #31b0d5;
	stroke-width: 1px;
	fill: #9e9e9e;
	fill-opacity: 0.5;
	stroke-opacity: 0.5;
}


.panel-loading {
    			background: url(../../img/loading1.gif) no-repeat 10px 10px;
    			margin-top: 10px;
    			margin-left: 10px;
    			color: #5567a5;
    			font-size: 14px;
    			padding: 11px 0px 10px 30px;
    			/* z-index:999; */
			}
	text,tspan {
		pointer-events: none;
	}
</style>
<script src="${eippath}/js/rem.js" type="text/javascript" charset="utf-8"></script>
</head>
<body class="easyui-layout">
<div class="panel-loading">初始化中...</div>
<div data-options="region:'north',border:true" style="<!-- height:108px; -->display: none;">
	<div class="my-header" style="display: none;">
		<div class="my-header-left" >
			<h1>当前展会：${exhibition.exihibitName}</h1>

		</div>

	</div >

	<!-- begin of toolbar -->
       <div>
       <!-- style="background-color:#f2f0f1;height:60px" -->
           <div class="my-toolbar-button" style="display: none;">

			<!-- 	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-menu1" onclick="fresh()" plain="true">刷新</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-select9" onclick="close_window()" plain="true">关闭</a> -->

				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add3" title="放大" onclick="size_up()" plain="true"
           				style="margin-left: 15px;"><font style="font-size: 28px">放大</font></a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add2" title="缩小" onclick="size_down()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 28px">缩小</font></a>
        		<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add1" title="复原" onclick="size_reset()" plain="true"
        				style="margin-left: 15px;"><font style="font-size: 28px">复原</font></a>
				<a href="javascript:void(0);" id="up-level" class="easyui-linkbutton" iconCls="icon-menu5" onclick="up_level()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 28px">上一层</font></a>
				<a href="javascript:void(0);" id="down-level" class="easyui-linkbutton" iconCls="icon-menu6" onclick="down_level()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 28px">下一层</font></a> -->

			<!-- 	<span id="level-root" style="margin-left: 5px;">&nbsp;&nbsp;展会总览</span> -->
				<!-- <span id="lockInfo" style="color:red;"></span> -->
            	<div style="float:left;">


        			<div id="shape_type_0"
        				style="width:90px;height:28px;opacity:0.5;
        				background-color:#8d85de;float:left;margin-right:10px;
        				color:#ffffff;font-weight:bold;border:1px solid transparent;
        				text-align:center;line-height:28px;font-size: 20px;">展位</div>
        			<div id="shape_type_1"
        				style="width:90px;height:28px;opacity:0.5;
        				background-color:#74b3e4;float:left;margin-right:10px;
        				color:#ffffff;font-weight:bold;border:1px solid transparent;
        				text-align:center;line-height:28px;font-size: 20px;">展区</div>
        			<div id="shape_type_2"
        				style="width:90px;height:28px;opacity:0.5;
        				background-color:#c76235;float:left;margin-right:10px;
        				color:#ffffff;font-weight:bold;border:1px solid transparent;
        				text-align:center;line-height:28px;font-size: 20px;">会议室</div>
        			<div id="shape_type_3"
        				style="width:90px;height:28px;opacity:0.5;
        				background-color:#ab7a34;float:left;margin-right:10px;
        				color:#ffffff;font-weight:bold;border:1px solid transparent;
        				text-align:center;line-height:28px;font-size: 20px;">广告位</div>
        			<div id="shape_type_9"
        				style="width:90px;height:28px;opacity:0.5;
        				background-color:#9e9e9e;float:left;margin-right:10px;
        				color:#ffffff;font-weight:bold;border:1px solid transparent;
        				text-align:center;line-height:28px;font-size: 20px;">其它设施</div>

        		</div>



           		</div>

           </div>

  </div>

<!-- end of toolbar -->
<div data-options="region:'center'" style="display: none;" id="map-wrapper">
	<div id="map" style="z-index:0; position:relative;"  class="pinch-zoom">
		<img id="map-img" src="" style="z-index:-1; position:absolute;" />
		<svg onclick="" preserveAspectRatio="xMinYMin meet" version="1.1" style="border:transparent solid 1px; z-index:1; padding:0; margin:0; height:1000; width:2000;"
			id="map-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2000 1000" ondragstart="return false;">
		</svg>
	</div>
</div>
<div style="float:right;display: none;">
	   	<input id="size-slider" class="easyui-slider" value="100" style="width:1100px;height:60px;" data-options="
				showTip: true,
				min: 25,
				max: 500,
				rule: [25,'|',50,'|',75,'|',100,'|',125,'|',150,'|',175,'|',200,'|',
					  225,'|',250,'|',275,'|',300,'|',325,'|',350,'|',375,'|',400,'|',
					  425,'|',450,'|',475,'|',500
					],
				tipFormatter: function(value){
					return value + '%';
				},
				onChange: function(value){
					change_size_action(value);
				}">
	</div>
<!-- <div class="my-footer" data-options="region:'south',border:true,split:false" style="display: none;margin-bottom: 5px;">
 	<div style="float:right;display: none;">
	   	<input id="size-slider" class="easyui-slider" value="100" style="width:1100px;height:60px;" data-options="
				showTip: true,
				min: 25,
				max: 500,
				rule: [25,'|',50,'|',75,'|',100,'|',125,'|',150,'|',175,'|',200,'|',
					  225,'|',250,'|',275,'|',300,'|',325,'|',350,'|',375,'|',400,'|',
					  425,'|',450,'|',475,'|',500
					],
				tipFormatter: function(value){
					return value + '%';
				},
				onChange: function(value){
					change_size_action(value);
				}">
	</div>
	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add3" title="放大" onclick="size_up()" plain="true"
           				style="margin-left: 15px;"><font style="font-size: 42px;  line-height: 80px;">放大</font></a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add2" title="缩小" onclick="size_down()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 42px; line-height: 80px;">缩小</font></a>
        		<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add1" title="复原" onclick="size_reset()" plain="true"
        				style="margin-left: 15px;"><font style="font-size: 42px; line-height: 80px;">复原</font></a>
				<a href="javascript:void(0);" id="up-level" class="easyui-linkbutton" iconCls="icon-menu5" onclick="up_level()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 42px; line-height: 80px;">上一层</font></a>
				<a href="javascript:void(0);" id="down-level" class="easyui-linkbutton" iconCls="icon-menu6" onclick="down_level()" plain="true"
						style="margin-left: 15px;"><font style="font-size: 42px; line-height: 80px;">下一层</font></a>
</div> -->



<div id="now-shape-info-float" style="display:none;position:absolute;display: none;">
	<div id="now-shape-info" class="easyui-panel" title="当前图形信息" style="padding:5px;width:250px;height:180px;" >
        <ul id="now-info-section" style="list-style:none;margin:0;padding:0;display:none;">

        	<!-- <li style="float:left;margin-right:5px;margin-bottom:5px;">
        		<div style="width:display:inline-block;"><label class="info-label">备注</label></div>&nbsp;
        		<textarea id="now-info-section-memo" readonly style="border:0px;font-size:14px;color:#444;float:right;" ></textarea>
        	</li> -->
        	<li id="now-info-section-name-li" style="float:left;">
        		<!-- <div style="display:inline-block;"><label class="info-label"></label></div> -->
        		<input id="now-info-section-name" readonly style="border:0px;font-size: 20px;">
        	</li>
        	<li id="now-info-section-name-li" style="float:left;">
        		<!-- <div style="display:inline-block;"><label class="info-label"></label></div> -->
        		<input id="now-info-section-" readonly style="border:0px;font-size: 20px;">
        	</li>
        	<li id="now-info-section-into-li" style="float:right;margin-top: 40px" >
				<!-- onclick="down_level()" -->
        		<a  class="easyui-linkbutton" class="VRhref" iconCls="icon-view"
        		      plain="true"><font style="font-size: 20px">VR全景</font></a>
        	</li>
        </ul>
        <ul id="now-info-booth" style="list-style:none;margin:0;padding:0;display:none;">


        	<li id="now-info-booth-code-li" style="float:left;margin-right:5px;margin-bottom:5px;display:none;">
        		<div style="display:inline-block;"><label class="info-label">代号</label></div>
        		<input id="now-info-booth-code" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-typeName-li" style="float:left;margin-right:5px;margin-bottom:5px;display:none;">
        		<div style="display:inline-block;"><label class="info-label">类型</label></div>
        		<input id="now-info-booth-typeName" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-boothSpec-li" style="float:left;margin-right:5px;margin-bottom:5px;">
        		<div style="display:inline-block;"><label class="info-label">规格</label></div>&nbsp;
        		<input id="now-info-booth-boothSpec" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-boothArea-li" style="float:left;margin-right:5px;margin-bottom:5px;display:none;">
        		<div style="display:inline-block;"><label class="info-label">面积</label></div>
        		<input id="now-info-booth-boothArea" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-salePrice-li" style="float:left;margin-right:5px;margin-bottom:5px;">
        		<div style="display:inline-block;"><label class="info-label">价格</label></div>
        		<input id="now-info-booth-salePrice" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-saleState-li" style="float:left;margin-right:5px;margin-bottom:5px;">
        		<div style="display:inline-block;"><label class="info-label">状态</label></div>
        		<input id="now-info-booth-saleState" readonly style="border:0px;">
        	</li>
        	<li id="now-info-booth-companyName-li" style="float:left;">
        		<!-- <div style="display:inline-block;"><label class="info-label"></label></div> -->
        		<input id="now-info-booth-companyName" readonly style="border:0px;font-size: 20px">
        	</li>
        	<li id="now-info-booth-time-li" style="float:left;">
        		<!-- <div style="display:inline-block;"><label class="info-label"></label></div> -->
        		<input id="now-info-booth-time" readonly style="border:0px;font-size: 20px">
        	</li>

        	<li id="now-info-booth-queryInfo-li" style="float:right;margin-top: 40px" >
        		<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view"
        		     onclick="dblclick()" plain="true"><font style="font-size: 20px">查看详情</font></a>
        	</li>
        </ul>
	</div>

</div>


<!-- 农博会 -->
<div class="common-top oh per100">
	<div class="tops top-op5">
		<input type="text" name="" id="searchForPavilionText" placeholder="请输入关键字检索" />
		<input type="button" name="" style="display:none;" class="map-search-button" onclick="searchForPavilion()" value="搜 索" />
		<a onclick="javascript:history.back(-1);" class="back"></a>
	</div>
</div>
<div class="pavilion-map-text">
	<!-- <div class="pavilion-map-search">
		<input type="text" name="" id="searchForPavilionText" value="" class="map-search-text" onclick="input_change()" placeholder="请输入关键词搜索" />
		<span class="map-search-line" style="display:none;"></span>
		<input type="button" name="" style="display:none;" class="map-search-button" onclick="searchForPavilion()" value="搜 索" />
	</div> -->

	<!-- <div class="pavilion-map-info" style="display: none">
		<div class="pavilion-map-left" >
			<h6 id="company_information"></h6><%--公司名称--%>
			<span id="exhibition_booth"> </span><%--展区+展位号--%>
		</div>
		<div class="pavilion-map-right">
			<a id="vrLink" class="avtives">公司详情</a>
		</div>
	</div> -->
	<div class="all_company_info" style="display: none">
		<div class="indicat_arrow" onclick="all_company_info_up()">
			<img src="../../img/agritech_img/down.png" class="indicat_arrow_img_xz">
		</div>
		<div class="company_info_close" onclick="close_company_info()">
			<img src="../../img/agritech_img/del.png" >
		</div>
		<div class="company_title">
			<div class="company_logo" style="display: none;">
				<img src="" >
			</div>
			<div class="company_name">
				<h6 id="company_information">杭州展之信息技术有限公司</h6>
				<span class="company_num" id="exhibition_booth" style="display: none;">3号馆b55展位</span>
			</div>
			<div class="pavilion-map-right">

				<a id="go_exhibitors" class="go_exhibitors">网上展厅</a>
				<a id="go_exhibits" class="go_exhibits">品种展示</a>

				<%--<a id="vrLink" class="exhibits_location VRhref">VR全景</a>--%>
				<a id="nextLayer" onclick="down_level()" class="exhibits_location">进入展区</a>
			    <!-- <a id="vrLink" class="avtives">公司详情</a> -->
		</div>
		</div>

		<div class="pavilion">
		<div class="company_texts">
			<!-- 展区备注 -->
			<div class="remark"></div>
			<!-- 公司简介 -->
			<div class="company_text no_company">
				<p>杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研杭州展之信息技术有限公司，前身为温州新特软件，成立于2000年，是一家专业从事商贸、生产、服务等领域信息化研</p>
			</div>
			<span onclick="mores()" id="mores">更多</span>
		</div>
		<div class="company_product no_company">
			<ul id="company_product_ul">

				<!-- <li onclick="show_product_info()">
					<div class="company_product_img">
						<img src="" >
					</div>
					<h6>草莓品种GF156</h6>
				</li> -->

			</ul>
		</div>
        </div>
		<div class="exhibition-area-img" style="display: none;"></div>
	</div>
	<div class="collect_button_sc " style="display: none; background-color:#fff">
		<%--<input type="button" class="commodity-botton-list no_company" name="" onclick="favoriteProduct(1)"  value="收藏公司" />--%>
	</div>
</div>
<div class="zoom-buttons">
	<input type="button" name="" id="" class="magnify-button" onclick="size_up()" value="" />
	<input type="button" name="" id="" class="shrink-button" onclick="size_down()" value="" />
	<input type="button" name="" id="up_level" class="go-upper-story" onclick="up_level()" value="" />
</div>
<!-- 询盘 -->
<div class="product_all_info" style="display: none;">
	<div class="product_all_info_title">
	<div class="product_all_info_img">
<%--		<img src="" id="product_all_info_img_images" >--%>
	</div>
	<div class="product_all_info_name">
		<h6 class="productName">葡萄新品种T30</h6>
		<span id="productTypeName">展品类别</span>
	</div>
	</div>
	<div class="product_all_info_text">
		<!-- <p>公司办、招相关服务展位图、会员中心等。</p> -->
	</div>
	<div class="commodity-botton">
	    <ul>
	        <li class="commodity-botton-list1">
	            <p onclick="favoriteProduct()" style="padding-left: .8rem;"><i class="collect"></i><span class="collect_p">收藏展品</span></p>
	        </li>
	        <li class="commodity-botton-list2" onclick="inquiry_window()">
	            <p><i class="inquiry"></i>询盘</p>
	        </li>
	    </ul>
	</div>
	<div class="company_info_close" style="position: absolute;top: 0;" onclick="close_company_info2()">
		<img src="../../img/agritech_img/del.png" >
	</div>
</div>
<div class="inquiry-window" style="display: none;">
    <form id="inquiry-window-form" action="goods-details.html">
        <h6>询盘</h6>
        <p><span>公司名称:</span><em class="companyName">李晓飞</em></p>
        <p><span>展位号:</span><em class="boothNum"><EMAIL></em></p>
        <p><span>展品名称:</span><em class="productName">18339333302</em></p>
        <ul>
            <li>
                <input type="hidden" name="id" id="inquiryId" value=""/>
                <input type="hidden" name="serveProductId" id="serveProductId" value=""/>
                <label>询盘描述：</label>
                <input type="text" required name="inquiryDescription" id="inquiryDescription" value=""/>
            </li>
            <li>
                <label>询盘人：</label>
                <input type="text" required name="inquiryPeople" id="inquiryPeople" value=""/>
            </li>
            <li>
                <label>联系电话：</label>
                <input type="text" required name="inquiryPhone" id="inquiryPhone" value=""/>
            </li>
            <li>
                <label>联系邮箱：</label>
                <input type="text" required name="inquiryEmail" id="inquiryEmail" value=""/>
            </li>
        </ul>
        <div class="inquiry-window-button">
            <input type="button" name="" class="adds_products_button" style="margin: 0 1rem 0 .55rem;" value="确认"
                   onclick="inquiry_affirm()"/>
            <input type="button" name="" class="removes_products_button" value="取消" onclick="inquiry_cancel()"/>
        </div>
    </form>
</div>
<div class="shadow" style="display: none;"></div>

<!-- end -->
</body>
</html>
<script type="text/javascript">
	var variableAttachment = window.location.origin;
	var variablePic = window.location.origin + "/image/"; //图片Url
	var variableSite = window.location.origin + "/eip-web-site";
	function getImageURL(str){
		// try {
		// 	return new URL(str).toString()
		// } catch (e){
		// 	console.warn(e)
		// 	return new URL('/image/' + str, location.origin).toString()
		// }
		str = str || ''
		if(str.startsWith('http') || str.startsWith('//')) {
			return str
		} else {
			return location.origin +'/image/' + str;
		}
	}
	// 获取隐藏参数
    var gomap = request("gomap");
	// end
	// 跳转演示
	// var p = "http://"+ window.location.hostname+":"+window.location.port+"/eip-web-site/pages/mobile-farm-project/subpages/pavilion-exhibition/company-infor.html?id=13&productType=&sectionCode="
	// $(".pavilion-map-right a").attr('href',p);
	// end
	function close_window(){
		$.messager.confirm('提示', '确定关闭吗？', function(r){
			if (r){
				close();
			}
		});
	}
	function close_win(){
		close();
	}

	var project_boothType_edit_mode;
	var project_boothType_button_edit_mode;
	var project_boothType_alocate_id = -1;
	var project_boothType_window_close_set = false;
	var project_boothType_save_id;


	/*  var el = document.querySelector('div.pinch-zoom');
      new PinchZoom.default(el, {}); */


	$(function(){

		/*  $('div.pinch-zoom').each(function () {
             new RTP.PinchZoom($(this), {});
         });
		 */


		//监听鼠标点击，获取点击元素的id
		 $(document).click(function(e) { // 在页面任意位置点击而触发此事件
			var id= $(e.target).attr("id");       // e.target表示被点击的目标
			//alert(id)
			//判断是否点击的是模块图  以id判断 shape   map-svg

			if(id=="map-svg"){
				//隐藏掉详细信息
				//$("#now-shape-info-float1").css("display", "none");
				$("#now-shape-info-float").css("display", "none");
				if(g_last_select_shape_num!=null){
					 //还原边框
		 	 		$('#shape' + g_last_select_shape_num).css("stroke", "#31b0d5");
		 			$('#shape' + g_last_select_shape_num).css("stroke-width", "1");
					$('#shape' + g_last_select_shape_num).css("fill-opacity", "0");
		 			g_last_select_shape_num=null;
		 			init_tool();
				}
			}

		 })
		 //解决 详细信息弹框关闭时,点击被详细信息覆盖的模块时 不弹出这个模块的详细信息的Bug
		 $("#now-shape-info").panel({
			 	onClose: function () {
	                //alert("Close");
	            	$("#now-shape-info-float").css("display", "none");
	            }
	        });

		/*  touch.on('#map','pinchout',function(ev){

			alert("a")
			}) */


	/* 	 var hammertime = new Hammer(document.getElementById("map-svg"));

		 //为该dom元素指定触屏移动事件
		// hammertime.add(new Hammer.Pinch());

		 hammertime.get('pinch').set({ enable: true });

		 //添加事件
		 hammertime.on("pinchin pinchout", function (e) {
			// document.getElementById("result").innerHTML += "捏合初触发<br />";
			// alert(e)
			 if(e.type == "pinchin"){
                 size_down();
             }else if(e.type == "pinchout"){
            	size_up();
             }

		 }); */
	});

 // 是否隐藏画的图形
 //解析url传过来的参数
 function request(paras) {
 	var url = location.href;
 	var paraString = url.substring(url.indexOf('?') + 1, url.length).split('&');
 	var paraObj = {};
 	for (i = 0; j = paraString[i]; i++) {
 		paraObj[j.substring(0, j.indexOf('=')).toLowerCase()] = j.substring(j.indexOf('=') + 1, j.length);
 	}
 	var returnValue = paraObj[paras.toLowerCase()];
 	if (typeof(returnValue) == 'undefined') {
 		return '';
 	} else {
 		return returnValue;
 	}
 }

function hideGraph(){
	if(gomap=="0"){
		$("#vrLink").hide();
		$("rect").css("opacity","0")
		$("polygon").css("opacity","0")
		$("text").css("opacity","0")
	}else{
		// $("#nextLayer").hide();
	}
}
// end



	function project_boothType_formatIfStandard(val, row){
		if(val == false)return '否';
		else return '是';
	}
	function project_boothType_formatPriceWay(val, row){
		if(val == 0)return '按个';
		else if(val == 1)return '按面积';
	}
	function project_boothType_window_close(){
		$('#project-boothType-window').window({
			onBeforeClose: function(){
				if(project_boothType_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#project-boothType-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				project_boothType_fresh();
			}
		});
	}











	function show_float(id) {
        var objDiv = $("#" + id + "");
        $(objDiv).css("display", "block");
       //  $(objDiv).css("left", event.clientX + 10 > 1400 ? 1400 : event.clientX + 10);
        $(objDiv).css("left", event.clientX + 10);
        $(objDiv).css("top", event.clientY + 10 > 500 ? 500 : event.clientY + 10);
    }
    function hide_float(id) {
        var objDiv = $("#" + id + "");
        $(objDiv).css("display", "none");
    }
	window.onload = function(){
		if(navigator.userAgent.indexOf("Trident") > 0) {
			alert("请使用非IE内核的浏览器，例如：Google Chrome");
		}
	}
	var svgns = "http://www.w3.org/2000/svg";
	var xlinkns = "http://www.w3.org/1999/xlink";
	var ATTR_MAP = {
	  "className": "class",
	  "svgHref": "href"
	};
	var NS_MAP = {
	    "svgHref": xlinkns
	};

	var operatorId='${sessionScope.operatorId}';
	var operName='${sessionScope.operName}';
	var f_org_num = '${sessionScope.f_org_num}'
	//alert(operatorId+"--"+operName);
	var g_exhibitCode = '${exhibition.exhibitCode}';
	var g_exhibitName = '${exhibition.exihibitName}';
	var g_release = '${exhibition.release}';
	var g_width = 2000;	//原始背景图宽
	var g_height = 1000;	//原始背景图高
	var g_size = 1;	//缩放比率
	var g_shape_num = 0;	//图形号
	var g_last_select_shape_num = null;	//上次选择的图形号
	var g_parent_map_id = -1;
	var g_section_map_id = -1;	//分区图层ID
	var g_parent_code = '-1';
	var g_section_code = '${param.sectionCode}';
	var g_tree_path = '';
	var g_section_name = '';
	var g_memo = '';
	var g_level_arr = new Array();
	var g_level_now = -1;
	var g_level_max = -1;
	var g_preorderReserveDays = 0;	//展位预订保留天数
	var g_contractReserveDays = 0;	//展位合同保留天数
	var g_locked = false ;//是否锁定，默认false
	var g_lockOperId = -1 ;//锁定人Id
	var g_lockOperName='';
	function makeSVG(tag, attributes) {
	    var elem = document.createElementNS(svgns, tag);
	    for (var attribute in attributes) {
	        var name = (attribute in ATTR_MAP ? ATTR_MAP[attribute] : attribute);
	        var value = attributes[attribute];
	        if (attribute in NS_MAP){
	        	 elem.setAttributeNS(NS_MAP[attribute], name, value);
	        } else{
	        	 elem.setAttribute(name, value);
	        }
	    }
	    return elem;
	}
	function getTodayFormatDate() {
    	//获取当前日期
		var myDate = new Date();

		var lw = new Date(myDate.getTime());
		var lastY = lw.getFullYear();
		var lastM = lw.getMonth() + 1;
		var lastD = lw.getDate();
		var lastH = lw.getHours();
		var lastM2 = lw.getMinutes();
		var t = lastY + "-" + (lastM<10 ? "0" + lastM : lastM) + "-" + (lastD<10 ? "0"+ lastD : lastD) +
			" " + (lastH<10 ? "0"+ lastH : lastH) + ":" +  (lastM2<10 ? "0"+ lastM2 : lastM2) + ":00";
		return t;
    }
/* 	function get_reserve_time(t, g){
		var lw = new Date(t.substring(0, 10));
		lw.setDate(lw.getDate() + g);
		var lastY = lw.getFullYear();
		var lastM = lw.getMonth() + 1;
		var lastD = lw.getDate();
		var endDate = lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);
		//return endDate + t.substring(10);
		var time=" 23:59:59";
		return endDate + time;
	} */
	function get_reserve_time(t, g){
		//alert(t)
		var lw = "" ;
		$.ajax({
			url: eippath + '/dateTime/addWorkDay',
			//dataType: "json",	//返回数据类型
			type: "post",
			data: {startDate: t,num:g},	//发送数据
			async: false,
			success: function(data){

				if(data!=null&&data!=''){
					lw = data.substring(0, 10)+" 23:59:59";

				}
			},
			error: function(){
				$.messager.alert('提示','获取数据失败,请刷新重试','error');
			}
		});
		return lw;
	}
	$(function(){

		init();
		$.ajax({
			url: eippath + '/reserveDay/selectByExhibitCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {exhibitCode: g_exhibitCode},	//发送数据
			async: true,
			success: function(data){

				if(data!=null&&data.preorderReserveDays != null && data.preorderReserveDays > 0){
					g_preorderReserveDays = data.preorderReserveDays;
				}
				if(data!=null&&data.contractReserveDays != null && data.contractReserveDays > 0){
					g_contractReserveDays = data.contractReserveDays;
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	/* 	$("#preoderTime").datetimebox({
			onChange: function(n, o){
				if(n != ''){
					$("#preoderReserveTime").textbox('setValue', get_reserve_time(n, g_preorderReserveDays));
				}
			}
		});
		$("#contractTime").datetimebox({
			onChange: function(n, o){
				if(n != ''){
					$("#contractReserveTime").textbox('setValue', get_reserve_time(n, g_contractReserveDays));
				}
			}
		}); */

	/* 	$('#sponsor-combobox-projectId').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: 'auto',
			url: eippath + '/project/selectByExhibitCode?exhibitCode=' + g_exhibitCode
		}); */


		//drawImage(image,width/2 -pointx,height/2 -pointy,TOP|LEFT)
		//width,height是屏幕尺寸
		//pointx,pointy是你希望的点在图片中的坐标
	/* 	var pointx =1046.00;
		var pointy = 401.00;
		var height = $(window).height();
		var width = $(window).width();
		alert(width)
		alert(height)
		var c=document.getElementById("map");
		var ctx=c.getContext("2d");

		var img=document.getElementById("map-img");
		ctx.drawImage(img,width/2 -pointx,height/2 -pointy,TOP|LEFT); */
	});



	function init(){
		if(g_section_code == '-1'){
			//alert(g_exhibitCode)
			$.ajax({
				url: eippath + '/exhibitSection/selectTopOne',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {exhibitCode: g_exhibitCode},	//发送数据
				async: true,
				success: function(data){
					//alert(data)
					if(data == null){
						//alert(data);
						//init_window_show();
						alert("展位图功能暂未开启")
					}else{
						g_parent_map_id = data.parentMapId;
						g_section_map_id = data.sectionMapId;
						g_parent_code = data.parentCode;
						g_section_code = data.sectionCode;
						g_tree_path = data.treePath;
						g_section_name = data.sectionName;
						g_memo = data.memo;
						$("#draw").attr("href", "${eippath}/backstage/map/draw?exhibitCode=" + g_exhibitCode + "&sectionMapId=" + g_section_map_id + "&sectionCode=" + g_section_code);
						//updateLockStyle(data.locked,data.lockOperId,data.lockOperName);
						init_tool();
						init_by_sectionMapId();

					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}else{
			fresh();
		}
	}
	//工具栏初始化
	var localfontsize = 0.12;
	var initfontsize = ''
	function init_tool(){

		if(g_parent_code == '-1')$("#up-level").linkbutton('disable');
		else $("#up-level").linkbutton('enable');
		if(g_level_now == g_level_max)$("#down-level").linkbutton('disable');
		else $("#down-level").linkbutton('enable');
	}
	function init_by_sectionMapId(){
		$.ajax({
			url: eippath + '/sectionMap/selectBySectionMapId',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {sectionMapId: g_section_map_id},	//发送数据
			async: true,
			success: function(data){
				$("#map-img").remove();
				$('#map').prepend('<img id="map-img" src="" style="z-index:-1; position:absolute;" />');
				var map_img = document.getElementById('map-img');
				map_img.onload = function(){
					g_width = map_img.width;
					g_height = map_img.height;
					var w = (g_width * g_size).toFixed(2);
					var h = (g_height * g_size).toFixed(2);
					map_img.width = w;
					map_img.height = h;
					var map_svg = document.getElementById("map-svg");
					map_svg.style.width = w;
					map_svg.style.height = h;
					map_svg.setAttribute('viewBox', '0 0 ' + w + ' ' + h);
					init_shape();
				}
				if(data!=null){
					//$("#map-img").attr("src", "http://" + location.hostname+":"+location.port + '/image/' + data.sectionPicFile);
					var imgUrl = getImageURL(data.sectionPicFile) //"http://" + location.hostname+":"+location.port + '/image/' + data.sectionPicFile;
					try{
						if(!validateImage(imgUrl)){
							imgUrl = eippath+"/img/map-default-white.jpg"
						}
					}catch(e) {
						console.dir(e)
					}
					localfontsize = (data.fontSize || 12) / 100;
					$("#map-img").attr("src", imgUrl);
				}

			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	//图形初始化
	function init_shape(){
		util.init();
		var map_svg_dom = document.getElementById("map-svg");
		while(map_svg_dom.hasChildNodes()){
			map_svg_dom.removeChild(map_svg_dom.firstChild);
		}
		g_last_select_shape_num = null;
		g_shape_num = 0;
		$.ajax({
			url: eippath + '/api/graph/selectOnlineBoothMap',
			// url: eippath + '/graph/selectMoreBySectionMapId',
			//url: eippath + '/graph/selectWithServeBooth',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				exhibitCode: g_exhibitCode,
				sectionMapId: g_section_map_id
			},
			async: true,
			success: function(resp){
				var data = resp.data || [];
				// var map_svg = document.getElementById('map-svg');
				var map_svg = document.createDocumentFragment();
				// var map_svg_dom = document.getElementById('map-svg');
				var map_wrapper = document.getElementById('map-wrapper');
				var showSectionName = util.setting.showSectionName
				var showBoothCode = util.setting.showBoothCode
				var graphType,isBooth,isSection;
				for(var i = 0; i < data.length; i++){
					graphType = data[i].buildingType
					isBooth = graphType === 0
					isSection = graphType === 1
					if(data[i].graphKind == 0){  //矩形
						var code = '';
						if(data[i].code != null) code = data[i].code;
						var temp = (data[i].graphData).split(',');
						var t0 = temp[0] * g_size;
						var t1 = temp[1] * g_size;
						var t2 = temp[2] * g_size;
						var t3 = temp[3] * g_size;
						var v_class = getClass(data[i].buildingType);

						var shape = makeSVG('rect', {
							x: t0,
							y: t1,
							width: t2,
							height: t3,
							'class': (data[i].saleState == null ? v_class : 'shape-salestate-' + data[i].saleState),
							id: 'shape' + g_shape_num,
							//onclick: 'select_shape(' + g_shape_num + ')',
							/* onclick: 'select_shape2(' + g_shape_num + ')',
							ondblclick: 'dblclick()',
							onmouseover:'select_shape1(' + g_shape_num + ')',
							onmouseout:'hide_info(' + g_shape_num + ')', */
							onclick: 'select_shape1(' + g_shape_num+ ',' + data[i].saleState + ','+ data[i].companyShow  + ',"'+ (data[i].companyAbbr ||'')  + '","'+ (data[i].companyAbbrEn ||'') +'")',
							f_graph_id: data[i].graphId,

							f_code: code,
							f_length: +data[i].length,
							f_booth_id: data[i].boothId || '',
							f_width: +data[i].width,
							f_spec: data[i].spec,
							f_area: +data[i].area,
							f_building_type: data[i].buildingType,
							f_sale_state: (data[i].saleState == null ? -10 : data[i].saleState),
							is_new: 0
						});
						map_svg.appendChild(shape);
						var text = makeSVG('text', {
							x: +t0 + t2/2,
							y: +t1 + t3/2,
							id: 'shape' + g_shape_num + 'text',
							// //onclick: 'select_shape(' + g_shape_num + ')',
							// /* onclick: 'select_shape2(' + g_shape_num + ')',
							// ondblclick: 'dblclick()',
							// onmouseover:'select_shape1(' + g_shape_num + ')',
							// onmouseout:'hide_info(' + g_shape_num + ')', */
							// onclick: 'select_shape1(' + g_shape_num + ',' + data[i].saleState + ','+ data[i].companyShow  + ',"'+ data[i].companyAbbr  + '","'+ data[i].companyAbbrEn +'")',
							'font-size': localfontsize + 'rem',
							'text-anchor': 'middle',
							'alignment-baseline': 'central'
						});
						if(isBooth){
							// $(text).html(code);
							if(data[i].companyShow){
							// if(data[i].ifStandard===false && data[i].companyShow){
							// if([3].includes(data[i].saleState) && data[i].ifStandard===false && data[i].companyShow){
								$(text).html(showBoothCode ?
									util.buildShapeText(t0,t1,t2,t3,code,data[i].companyAbbr || data[i].companyName || '',data[i].companyAbbrEn || '',data[i].ifStandard) :
									util.buildShapeTextNoCode(t0,t1,t2,t3,data[i].companyAbbr || data[i].companyName || '',data[i].companyAbbrEn || '',data[i].ifStandard));
							}else{
								showBoothCode && $(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
							}
						}else if(isSection){
							if(showSectionName){
								$(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
							}
						}else{
							$(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
						}
						map_svg.appendChild(text);
						g_shape_num++;
					}else if(data[i].graphKind == 1){ //多边形
						var code = '';
						if(data[i].code != null) code = data[i].code;
						var xy = (data[i].graphData).split(' ');
						var temp2 = xy[0].split(',');
						var x = (temp2[0]* g_size).toFixed(2);
						var y = (temp2[1]* g_size).toFixed(2);
						let xMax = +x,xMin = +x,yMax= +y,yMin=+y;
						var p = x + ',' + y;
						for(var j = 1; j < xy.length; j++){
							temp2 = xy[j].split(',');
							x = (temp2[0]* g_size).toFixed(2);
							xMax = Math.max(xMax,+x);
							xMin = Math.min(xMin,+x);
							y = (temp2[1]* g_size).toFixed(2);
							yMax = Math.max(yMax,+y);
							yMin = Math.min(yMin,+y);
							p += ' ' + x + ',' + y;
						}
						var t0 = +xMin;
						var t1 = +yMin;
						var t2 = +xMax - (+xMin);
						var t3 = +yMax - (+yMin);

						var v_class = getClass(data[i].buildingType);
						var shape = makeSVG('polygon', {
							// 自动放缩也需要动态计算
							x: t0, // 无实际意义, 定位用
							y: t1, // 无实际意义, 定位用
							width: t2, // 无实际意义, 定位用
							height: t3,// 无实际意义, 定位用
							points: p,
							'class': (data[i].saleState == null ? v_class : 'shape-salestate-' + data[i].saleState),
							id: 'shape' + g_shape_num,
							//onclick: 'select_shape(' + g_shape_num + ')',
							/* onclick: 'select_shape2(' + g_shape_num + ')',
							ondblclick: 'dblclick()',
							onmouseover:'select_shape1(' + g_shape_num + ')',
							onmouseout:'hide_info(' + g_shape_num + ')', */
							onclick: 'select_shape1(' + g_shape_num+ ',' + data[i].saleState + ','+ data[i].companyShow + ',"'+ (data[i].companyAbbr ||'')  + '","'+ (data[i].companyAbbrEn ||'')  + '")',
							f_graph_id: data[i].graphId,
							f_booth_id: data[i].boothId || '',
							f_code: code,
							f_area: data[i].area == null ? '' : +data[i].area,
							f_building_type: data[i].buildingType,
							f_sale_state: (data[i].saleState == null ? -10 : data[i].saleState),
							is_new: 0
						});
						map_svg.appendChild(shape);
						var text = makeSVG('text', {
							x: +t0 + t2 / 2,
							y: +t1 + t3 / 2,
							id: 'shape' + g_shape_num + 'text',
					// 		//onclick: 'select_shape(' + g_shape_num + ')',
					// /* 		onclick: 'select_shape2(' + g_shape_num + ')',
					// 		ondblclick: 'dblclick()',
					// 		onmouseover:'select_shape1(' + g_shape_num + ')',
					// 		onmouseout:'hide_info(' + g_shape_num + ')', */
					// 		onclick: 'select_shape1(' + g_shape_num+ ',' + data[i].saleState + ','+ data[i].companyShow  + ',"'+ data[i].companyAbbr  + '","'+ data[i].companyAbbrEn  +'")',
							'font-size': localfontsize + 'rem',
							'text-anchor': 'middle',
							'alignment-baseline': 'central'
						});
						if(isBooth){
							// $(text).html(code);
							if(data[i].companyShow){
							// if(data[i].ifStandard===false && data[i].companyShow){
							// if([3].includes(data[i].saleState) && data[i].ifStandard===false && data[i].companyShow){
								$(text).html(showBoothCode ?
									util.buildShapeText(t0,t1,t2,t3,code,data[i].companyAbbr || data[i].companyName || '',data[i].companyAbbrEn || '',data[i].ifStandard) :
									util.buildShapeTextNoCode(t0,t1,t2,t3,data[i].companyAbbr || data[i].companyName || '',data[i].companyAbbrEn || '',data[i].ifStandard));
							}else{
								showBoothCode && $(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
							}
						}else if(isSection){
							if(showSectionName){
								$(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
							}
						}else{
							$(text).html(util.buildShapeTextOne(t0 + t2/2, t1 + t3/2, code))
						}
						map_svg.appendChild(text);
						g_shape_num++;
					}
				}
				map_svg_dom.appendChild(map_svg);
				util.fixTextSize();
				const width = Number(map_svg_dom.style.width.replace('px',''))
				const height = Number(map_svg_dom.style.height.replace('px',''))
				if(!isNaN(width) && !isNaN(height)){
					const offsetX = (width - map_wrapper.offsetWidth) / 2
					const offsetY = (height - map_wrapper.offsetHeight) / 2
					map_wrapper.scrollTo(offsetX, offsetY)
					$("#map-wrapper").css({
						'marginTop': '0.66rem',
						'borderColor': 'transparent'
					}).scrollTop(0)
				}
				hideGraph();
				let params = (new URL(document.location)).searchParams;
				params.set('sectionCode', g_section_code)
				let url = (new URL(document.location))
				history.pushState({id: g_section_code},null,  window.origin + url.pathname +'?'+params.toString())
				//document.location = url
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}



	function fresh(){
		$.ajax({
			url: eippath + '/exhibitSection/selectBySectionCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {sectionCode: g_section_code},	//发送数据
			async: true,
			success: function(data){
				g_parent_map_id = data.parentMapId;
				g_section_map_id = data.sectionMapId;
				g_parent_code = data.parentCode;
				g_section_code = data.sectionCode;
				g_tree_path = data.treePath;
				g_section_name = data.sectionName;
				g_memo = data.memo;
				$("#draw").attr("href", "${eippath}/backstage/map/draw?exhibitCode=" + g_exhibitCode + "&sectionMapId=" + g_section_map_id + "&sectionCode=" + g_section_code);
				//updateLockStyle(data.locked,data.lockOperId,data.lockOperName);
				init_tool();
				init_by_sectionMapId();


			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}

	//上一层级按钮点击
	function up_level(){

		$("#now-shape-info-float").css("display", "none");
        $(".all_company_info").hide();
        $(".pavilion-map-right").show();
		$(".remark").show();
		$.ajax({
			url: eippath + '/exhibitSection/selectBySectionCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {sectionCode: g_parent_code},	//发送数据
			async: true,
			success: function(data){
				if(g_level_now >= 0)g_level_now--;
				g_parent_map_id = data.parentMapId;
				g_section_map_id = data.sectionMapId;
				g_parent_code = data.parentCode;
				g_section_code = data.sectionCode;
				g_tree_path = data.treePath;
				g_section_name = data.sectionName;
				g_memo = data.memo;
				var str = $("#level-root").text();
				$("#level-root").text((str).substring(0, str.lastIndexOf("-->")));
				if($("#level-root").text() == "展会总览")$("#publish").show();
				size_reset();
				$("#draw").attr("href", "${eippath}/backstage/map/draw?exhibitCode=" + g_exhibitCode + "&sectionMapId=" + g_section_map_id + "&sectionCode=" + g_section_code);
				//updateLockStyle(data.locked,data.lockOperId,data.lockOperName);
				init_tool();
				init_by_sectionMapId();

			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}

	function dblclick(){

		//queryIsLock();//判断是否被锁定

		var buildingType = 0;
		if(g_last_select_shape_num != null){
			buildingType = $('#shape' + g_last_select_shape_num).attr('f_building_type')
		}else{
			$.messager.alert('提示','请选区域！','info');
			return;
		};

		//如果详细信息有打开  那么隐藏掉
		//隐藏掉详细信息
		$("#now-shape-info-float").css("display", "none");
		//alert(buildingType)
		if(buildingType == 1){
			down_level();

		}else if(buildingType == 0){
			var boothNum = $('#shape' + g_last_select_shape_num).attr('f_code')
			if(!boothNum) return;
			$.ajax({
					url: eippath + '/serveCompany/selectByBoothNum',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {exhibitCode:g_exhibitCode,boothNum},	//发送数据
					success: function(data){
						// TODO 查询模板
						// TODO 请求参 + pid
						// if(data!=null&&data.serveCompanyId!=null&&data.serveCompanyId>0){
						// 	var href = '/eip-web-business/pages/mobile-sponsor/subpages/exhibitors-service/company-infor.html?id='
						// 			+data.serveCompanyId+'&exhibitCode='+g_exhibitCode+'&productType='+'&sectionCode=';
						// 	window.location.href=href;
						// 	//window.open(href,'_blank');
						// }else{
						// 	$.messager.alert('提示','暂无详细信息！','info');
						// }
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
		}
	}
	//下一层级按钮点击
	function down_level(){
		$("#now-shape-info-float").hide();
		$('.all_company_info').hide()
		var graphId = null;
		var buildingType = 0;
		if(g_last_select_shape_num != null)buildingType = $('#shape' + g_last_select_shape_num).attr('f_building_type');
		if(buildingType == 1){
			graphId = $('#shape' + g_last_select_shape_num).attr('f_graph_id');
		}else{
			graphId = g_level_arr[g_level_now + 1];
		}
		//alert(graphId)
		$.ajax({
			url: eippath + '/exhibitSection/selectByGraphId',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {graphId: graphId},	//发送数据
			async: true,
			success: function(data){
				if(data == null){
					//init2_window_show();
				}else{
					if(buildingType == 1){
						g_level_now++;
						g_level_max = g_level_now;
						g_level_arr[g_level_now] = $('#shape' + g_last_select_shape_num).attr('f_graph_id');
					}else{
						g_level_now++;
					}
					g_parent_map_id = data.parentMapId;
					g_section_map_id = data.sectionMapId;
					g_parent_code = data.parentCode;
					g_section_code = data.sectionCode;
					g_tree_path = data.treePath;
					g_section_name = data.sectionName;
					g_memo = data.memo;
					$("#level-root").text($("#level-root").text() + "-->" + g_section_name);

					size_reset();
					//alert("${eippath}/backstage/map/draw?exhibitCode=" + g_exhibitCode + "&sectionMapId=" + g_section_map_id + "&sectionCode=" + g_section_code)
					$("#draw").attr("href", "${eippath}/backstage/map/draw?exhibitCode=" + g_exhibitCode + "&sectionMapId=" + g_section_map_id + "&sectionCode=" + g_section_code);
					//updateLockStyle(data.locked,data.lockOperId,data.lockOperName);

					init_tool();
					init_by_sectionMapId();


				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	var g_preorder_clientId;
	var g_preorder_projCltId;
	var g_preorder_projectId;
	var g_preorder_boothId;
	var g_preorder_salePrice;
	var g_preorder_foreignPrice;
	var g_preorder_foreignCurrency;
	var g_preorder_currencyName;



	var g_contract_currencyName;
	var g_contract_boothId;

	var g_pay_boothId;




	var boothdata_edit_mode;
	var boothdata_save_id;
	var boothdata_foreignCurrency;
	var boothdata_priceWay;
	var boothdata_price;
	var boothdata_openPrice;
	var boothdata_foreignPrice;
	var boothdata_foreignOpenPrice;




	var g_boothdata_boothId;






	//选择图形1   鼠标单击事件
	var g_building_type = -1;
	function select_shape1(shape_num, saleState, companyShow, companyAbbr,companyAbbrEn){

		//如果单击选中的图形不是这个图形
		if(g_last_select_shape_num!=shape_num){
			$('#shape' + shape_num).css("stroke", "FFFFFF");
			$('#shape' + shape_num).css("stroke-width", "1");
		}
		showmore(1);
		select_shape2(shape_num);
	    /* $('#now-shape-info').panel('open');
		show_float("now-shape-info-float");   */
		//默认隐藏浮动窗内的信息
		$("#now-info-booth-typeName-li").hide();
		$("#now-info-booth-boothSpec-li").hide();
		$("#now-info-booth-boothArea-li").hide();
		$("#now-info-booth-salePrice-li").hide();
		$("#now-info-booth-saleState-li").hide();
		$("#now-info-booth-companyName-li").hide();
		$("#now-info-booth-time-li").hide();
		$(".remark").hide()
		//$("#now-info-booth-queryInfo-li").hide();
		var buildingType = $('#shape' + shape_num).attr('f_building_type');
		var f_graph_id = $('#shape' + shape_num).attr('f_graph_id');


		var building_type_str = "";
		g_building_type = Number(buildingType)
		var boothId = $('#shape' + shape_num).attr('f_booth_id') || '';
		building_type_str = ['展位','展区','会议室','广告位'][g_building_type] || '其他设施'
		var f_code = $('#shape' + shape_num).attr('f_code');
		if(buildingType == 1){
			$(".all_company_info").show();
			$("#now-info-booth").hide();
			//$("#now-shape-info-float").css("display", "none");
			$('#now-shape-info').panel('open');
			// show_float("now-shape-info-float");
			$("#now-shape-info").panel('setTitle',building_type_str+f_code);
			$("#now-info-section-name").val(building_type_str+f_code);
			$("#now-info-section").show();
			$("#down-level").linkbutton('enable');
			$("#company_information").html(f_code);
			$("#exhibition_booth").html(f_code);
			$(".all_company_info").css("bottom","0rem");
			$(".company_num").hide();
			//$(".pavilion").hide();
			$(".no_company").hide();
			$(".collect_button_sc").hide();
			$(".pavilion-map-right").show();
			$('#go_exhibitors,#go_exhibits').hide()
			$.ajax({
				url: eippath + '/exhibitSection/getSectionMapByGraphId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {graphId: $('#shape' + shape_num).attr('f_graph_id')},	//发送数据
				async: true,
				success: function(data){
					var sectionPicFile = data.data.sectionPicFile;
					var sectionCode = data.data.sectionCode;
					var memo = data.data.memo;
					var sectionMapId= data.data.sectionMapId
					if (memo) {
						$(".remark").html(memo);
						$(".remark").show()
					} else {
						$(".remark").hide()
					};
					showmore(1);
					// if(gomap=="0"){
					// 	$("#go_exhibitors").html("展商名录");
					// 	$("#go_exhibits").html("展品一览");
					// 	$("#go_exhibitors").attr("href",variableSite + "/pages/mobile-farm-project/subpages/exhibitors-service/exhibitor-list-hrb.html?sectionCode=" + sectionCode )
					// 	$("#go_exhibits").attr("href",variableSite + "/pages/mobile-farm-project/subpages/spectator-services/exh-show-hrb.html?sectionCode=" + sectionCode )
					// }else{
					// 	var vrLink = data.data.vrLink
					// 	if(vrLink!=""&&vrLink!=null){
					// 		$("#vrLink").attr("href",data.data.vrLink)
					// 	}else{
					// 		$("#vrLink").attr("href",variableSite + "/pages/mobile-farm-project/vip-subpages/showVR.html")
					// 	}
					//     $("#go_exhibitors").attr("href",variableSite + "/pages/mobile-farm-project/subpages/exhibitors-service/exhibitor-list.html?sectionCode=" + sectionCode )
					//     $("#go_exhibits").attr("href",variableSite + "/pages/mobile-farm-project/subpages/spectator-services/exh-show.html?sectionCode=" + sectionCode )
					// }
					// if (!sectionPicFile){
					// 	$(".exhibition-area-img").html('<img src="" alt="">');
					// } else if(sectionPicFile.indexOf("\\") > -1) {
					// 	  sectionPicFile = variableAttachment + sectionPicFile
					// } else {
					// 	sectionPicFile = getImageURL(sectionPicFile) //variablePic +sectionPicFile;
					// }
					// $(".exhibition-area-img").html('<img src="'+sectionPicFile+'" alt="">');
					$.ajax({
						url: eippath + '/graph/selectCountBySectionMapId',
						dataType: "json",	//返回数据类型
						type: "post",
						data: {sectionMapId: sectionMapId},	//发送数据
						async: true,
						success: function(data){
							var n = data.data > 0

							if (n) {
								$("#nextLayer").show()
								// $('#go_exhibitors,#go_exhibits').hide()
							}else {
								$("#nextLayer").hide()
								// $('#go_exhibitors,#go_exhibits').show()
							}

						},
					});
					// alert(sectionPicFile)
				},
			});
			// $(".pavilion-map-info").show();
			// $(".all_company_info").show();
			 // $("#vrLink").html("进入展区")
		       $("#vrLink").click(function(){
			 	 $(".all_company_info").hide();
			 	// $("#vrLink).hide();
			    })

			/* 	$("#now-shape-info").panel('setTitle',building_type_str+f_code);

                $("#now-info-section").show();


                $.ajax({
                    url: eippath + '/exhibitSection/selectByGraphId',
                    dataType: "json",	//返回数据类型
                    type: "post",
                    data: {graphId: $('#shape' + shape_num).attr('f_graph_id')},	//发送数据
                    async: true,
                    success: function(data){
                        if(data == null){
                            $("#now-info-section-code").val("");
                            $("#now-info-section-name").val("");
                            $("#now-info-section-memo").val("");

                        }else{
                            $("#now-info-section-code").val(data.sectionCode);
                            $("#now-info-section-name").val(data.sectionName);
                            $("#now-info-section-memo").val(data.memo);

                        }
                    },
                    error: function(){
                        $.messager.alert('提示','数据发送失败！','error');
                    }
                }); */
		}else if(buildingType == 0){
			if(!(companyShow && util.setting.mouseMove)){
			// if(!(companyShow && [3].includes(saleState) && util.setting.mouseMove)){
				if(g_building_type===0)  close_company_info()
				return;
			}
			$("#down-level").linkbutton('disable');
			$('#now-shape-info').panel('open');
			// show_float("now-shape-info-float");

			$("#now-shape-info").panel('setTitle',building_type_str+f_code);
			$("#now-info-section").hide();
			$("#now-info-booth-companyName-li").show();
			$("#now-info-booth-time-li").show();
			$(".collect_button_sc").show();
			$(".company_num").show();
			$(".exhibition-area-img").hide();
			$(".all_company_info").css("bottom","1rem");
			//$(".pavilion").show();
			$(".remark").hide();
			$(".pavilion-map-right").hide();

			//根据展位号到serveBooth表中查询展商信息
			var boothNum = $('#shape' + shape_num).attr('f_code')
			if(boothNum) {
				$.ajax({
					url: eippath + '/serveCompany/selectByBoothNum',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {exhibitCode:g_exhibitCode,boothNum,boothId},	//发送数据
					success: function(data){
						if(data!=null){
							//$("#now-info-booth-queryInfo-li").show();
							//公司简称
							// $("#now-info-booth-companyName").val(data.companyAbbr);
							// $("#now-info-booth-time").val(data.productType);
							$("#company_information").html(util.getHoverTipHtml(data.companyName,companyAbbr,companyAbbrEn));
							// $("#company_information").html(data.companyName);
							$(".company_text").html(data.profile);
							//$("#exhibition_booth").html(data.sectionName+" "+data.boothNum);
							$("#exhibition_booth").html(data.boothNum);
							// loadProduct(88)
							// isFavorite(88,1)
							// serveCompanyId=88;
							serveCompanyId=data.serveCompanyId;
							loadProduct(data.serveCompanyId)
							isFavorite(data.serveCompanyId,1)
							$(".no_company").show();
							$(".all_company_info").show();
						}else{
							/* $("#now-info-booth-companyName").val("");
							$("#now-info-booth-time").val(""); */

							$("#company_information").html("暂未绑定展商");
							$(".company_text").html("");
							$("#exhibition_booth").html("");
							$(".no_company").hide();
							$(".all_company_info").show();
						}
						showmore(2);
					},
				});
			}
			$("#now-info-booth").show();

		}
	}
	function loadProduct(id) {
		$("#company_product_ul").html("")
		$.ajax({
			url: eippath + '/serveProduct/getByServeCompanyId',
			type: "post",
			data: { serveCompanyId: id, page: 1, rows: 100},
			success: function(data) {
				var result = data;
				if (result.state == 1 && result.rows) {
					for (var i = 0; i < result.rows.length; i++) {
						var pic=result.rows[i].pic || '';
						// if (pic.indexOf("\\") > -1)  pic = variableAttachment + pic;
						// else pic = variablePic +"/"+pic;
						var  end = pic.substring(pic.lastIndexOf(".") + 1).toLowerCase();
						if(end == "mp4" || end == "m2v" || end == "mkv" || end == "avi" || end == "mov" || end == "rmvb" || end ==
							"rmvb" || end == "flv" || end == "3gp"){
						 	pic = '../../img/agritech_img/video_images.jpg';
						}
						var html='<li onclick="show_product_info('+result.rows[i].serveProductId+')">\n' +
							'<div class="company_product_img">\n' +
							'<img src="'+pic+'" >\n' +
							'</div>\n' +
							'<h6>'+ result.rows[i].productName +'</h6>\n' +
							'</li>';
						$("#company_product_ul").append(html)
					}
				}
			},
		});
	}

	//选择图形
	function select_shape2(shape_num){
		//alert("as");
		//queryIsLock();
	<%-- 	if(g_locked){
			<%
			if(fu_section_graph_set_draw){out.println("$(\"#queryData\").linkbutton(\'enable\');");}
			else{out.println("$(\"#queryData\").linkbutton(\'disable\');");}
			%>
		}else{
			$("#queryData").linkbutton('enable');
		} --%>
		//$("#queryData").linkbutton('enable');
		if(g_last_select_shape_num != null && g_last_select_shape_num != shape_num){
			$('#shape' + g_last_select_shape_num).css("stroke", "#31b0d5");
			$('#shape' + g_last_select_shape_num).css("stroke-width", "1");
			$('#shape' + g_last_select_shape_num).css("fill-opacity", "0");
		}
		$('#shape' + shape_num).css("stroke", "red");
		$('#shape' + shape_num).css("stroke-width", "3");
		$('#shape' + shape_num).css("fill-opacity", "0.5");
		g_last_select_shape_num = shape_num;

	/* 	return;  */


	}



   // 文字缩放
	// function textscal() {
	// 	$("#map-svg").find("text").each(function(){
	// 		$(this).attr("x", ($(this).attr("x") / old_size * g_size).toFixed(2));
	// 		$(this).attr("y", ($(this).attr("y") / old_size * g_size).toFixed(2));
	// 		$(this).attr("font-size", base_size + 'rem');
	// 	});
	// }

	//放大与缩小效果
	var base_scale = 1
	function change_size_action(value){
		console.log(value)
		var old_size = g_size;
		g_size = (value / 100).toFixed(2);
		base_scale = 1 * g_size
		var w = (g_width * g_size).toFixed(2);
		var h = (g_height * g_size).toFixed(2);
		var map_img = document.getElementById('map-img');
		map_img.width = w;
		map_img.height = h;
		var map_svg = document.getElementById("map-svg");
		map_svg.style.width = w;
		map_svg.style.height = h;
		map_svg.setAttribute('viewBox', '0 0 ' + w + ' ' + h);
		let tmpSize = g_size / old_size
		$("#map-svg").find("rect").each(function(){
			var $this = $(this)
			$this.attr("x", ($this.attr("x") * tmpSize).toFixed(4))
					.attr("y", ($this.attr("y") * tmpSize).toFixed(4))
					.attr("width", ($this.attr("width") * tmpSize).toFixed(4))
					.attr("height", ($this.attr("height") * tmpSize).toFixed(4));
		});
		$("#map-svg").find("polygon").each(function(){
			var $this = $(this);
			$this
				.attr("x", ($this.attr("x") * tmpSize).toFixed(4))
				.attr("y", ($this.attr("y") * tmpSize).toFixed(4))
				.attr("width", ($this.attr("width") * tmpSize).toFixed(4))
				.attr("height", ($this.attr("height") * tmpSize).toFixed(4));
			var p = $this.attr('points');
			var xy = p.split(' ');
			var temp = xy[0].split(',');
			var x = (temp[0] * tmpSize).toFixed(4);
			var y = (temp[1] * tmpSize).toFixed(4);
			p = x + ',' + y;
			for(var i = 1; i < xy.length; i++){
				temp = xy[i].split(',');
				x = (temp[0] * tmpSize).toFixed(4);
				y = (temp[1] * tmpSize).toFixed(4);
				p += ' ' + x + ',' + y;
			}
			$this.attr('points', p);
		});
		$("#map-svg").find("tspan").each(function(){
			var $this = $(this);
			let fs = parseFloat($this.attr('font-size') || localfontsize);
			$this
				.attr("x", ($this.attr("x") * tmpSize).toFixed(4))
				.attr("y", ($this.attr("y") * tmpSize).toFixed(4))
				.attr("font-size", (fs * tmpSize).toFixed(4) + 'rem');
		});
	}
	let count = 1
	let g_value; // 记下放缩
	function size_up(val){
		var value = g_size * 1.25;
		if(val) value = val
		if(value > 5)value = 5;
		g_value = value
		$('#size-slider').slider('setValue', Math.round(value * 100));
	}
	function size_down(val){
		var value = g_size / 1.25;
		if(val) value = val
		if(value < 0.25)value = 0.25;
		g_value = value
		$('#size-slider').slider('setValue', Math.round(value * 100));
	}
	function size_reset(){
		$('#size-slider').slider('setValue', 100);
	}

	//设置编辑页面内容清空
	function setEditInfoClear(ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
	}
	//设置编辑页面内容初始化
	function setEditInfoInit(row, ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
	}
	//设置编辑页面可编辑属性
	function setEditInfoReadOnly(flag, ul){
		var readonly = true, able = 'disable';
		if(flag == 1){
			readonly = false;
			able = 'enable';
		}
		$(ul).find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
			else if(control == "checkbox"){
				if(flag == 1)$($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
	}

	//修改锁定样式
	function updateLockStyle(bool,lockOperId,lockOperName){
		if(bool){
			$('#lock').linkbutton({text: '解锁'});
			$('#lock').linkbutton({iconCls: 'icon-tool-unlock1'});
			$("#lockInfo").text("(展区已锁定)");
			g_locked = true;
			if(lockOperId!=null&&lockOperId>0){
				g_lockOperId=lockOperId;
				g_lockOperName=lockOperName;
				if(g_lockOperId!=operatorId){
					//如果当前登录人不是锁定人  置灰锁定图标
					$("#lock").linkbutton('disable');
				}
			}else{
				g_lockOperId=-1;
				g_lockOperName='';
				$("#lock").linkbutton('enable');
			}
		}else{
			$('#lock').linkbutton({text: '锁定'});
			$('#lock').linkbutton({iconCls: 'icon-tool-lock'});
			$("#lockInfo").text("");
			g_locked = false;
			g_lockOperId= -1;
			g_lockOperName='';
			$("#lock").linkbutton('enable');
		}
	}

	function queryIsLock(){
		$.ajax({
			url: eippath + '/exhibitSection/queryIsLock',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {sectionCode: g_section_code},	//发送数据
			async: false,
			success: function(data){
				if(data!=null){
					//alert(data.locked+data.lockOperId+data.lockOperName)
					 updateLockStyle(data.locked,data.lockOperId,data.lockOperName);
				}else{
					$.messager.alert('提示','操作失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
 	}

 	//获取图层的颜色
 	function  getClass(buildingType){
 		v_class='';
		if(buildingType == 0){
			v_class = 'new-booth';
		}else if(buildingType == 1){
			v_class = 'new-exhibition';
		}else if(buildingType == 2){
			v_class = 'new-meeting';
		}else if(buildingType == 3){
			v_class = 'new-advertising';
		}else{
			v_class = 'new-other';
		}
		return v_class;
 	}

 	//状态
 	function getStateName(state){
 		var name="";
 		if(state==0){
 			name="可销售";
 		}else if(state==1){
 			name="已预订";
 		}else if(state==2){
 			name="已签合同";
 		}else if(state==3){
 			name="已付款";
 		}else if(state==-10){
 			name="未设置";
 		}
 		return name;
 	}
	 //隐藏浮窗信息
	function hide_info(shape_num){
		 $("#now-shape-info-float").hide();
		// $("#now-shape-info-float1").hide();
			//如果没有单击被选中
		 if(g_last_select_shape_num!=shape_num){
			 //还原边框
			$('#shape' + shape_num).css("stroke", "#31b0d5");
			$('#shape' + shape_num).css("stroke-width", "1");
		 }
	 }
	 //关键字搜索
	function searchForPavilion() {
		var exhibitCode = localStorage.getItem("exhibitCode");
		var cid = localStorage.getItem("cid");
		var pid = localStorage.getItem("pid");

		var val = $("#searchForPavilionText").val();
		if(val=='') {
			alert('请输入文字!');
			return false;
		}
		$.ajax({
			 url: eippath + '/serveBooth/selectBoothNumByKeyword',
			 dataType: "json",	//返回数据类型
			 type: "post",
			 data: {exhibitCode: exhibitCode,clientId: cid, projectId: pid,keyWord:val},
			 success: function(data){
				 if(data.result.length>0){
				 	var flag=false;
					 for (var x in data.result) {
						 var b = location_booth(data.result[x]);
						 flag=b
						 if (b) break;
					 }
					 if (!flag) alert('该展位/展商在本展区搜索不到，请到另外另外展馆检索');
				 }else{
					alert("未搜索到记录！")
					 /** $.messager.alert('提示','未搜索到记录！','error'); */				 }
			 }
		 });
		 $(".map-search-button").hide()
		 $(".tops input").eq(0).css({"width":"6.5rem"})
	 }

	function location_booth(val){
		/**
		 behavior 可选
		 定义动画过渡效果， "auto"或 "smooth" 之一。默认为 "auto"。
		 block 可选
		 定义垂直方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "start"。
		 inline 可选
		 定义水平方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "nearest"。
		 */
		val = val.trim();
		if(val==""){
			return;
		}
		if($("rect[f_code="+ val +"]")[0] != undefined ){
			$("rect[f_code="+ val +"]")[0].scrollIntoView({behavior: "auto", block: "center", inline: "center"});
			var id = $("rect[f_code="+ val +"]")[0].id;
			$("#"+ id).click();
		}else if($("polygon[f_code="+ val +"]")[0] != undefined ){
			$("polygon[f_code="+ val +"]")[0].scrollIntoView({behavior: "auto", block: "center", inline: "center"});
			var id = $("polygon[f_code="+ val +"]")[0].id;
			$("#"+ id).click();
		}else{
			return false;
		}
		return true;
	}
 	//农博会
	 function input_change(){
		$(".map-search-line").show();
		$(".map-search-button").show();
		$(".pavilion-map-search").css({"width":"7.02rem"})
		$(".map-search-text").css({"padding-left":".3rem","width":"5.78rem","background":"none"})
	 }
	 // $(".map-search-text").on("blur",function(){
		//  $(".map-search-line").hide();
		//  $(".map-search-button").hide();
		//  $(".pavilion-map-search").css({"width":"4.02rem"})
		//  $(".map-search-text").css({"padding-left":".65rem","width":"100%","background":"url(../../img/search1.png) no-repeat .27rem center","background-size":".28rem"})
	 // })
	 $("#searchForPavilionText").keydown(function (e) {//当按下按键时
	     if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
	        searchForPavilion()
	     }
	 })
	 $(".zoom-buttons input").each(function(i){
		 $(".zoom-buttons input").eq(i).on("touchstart",function(){
			 $(".zoom-buttons input").eq(i).css({"background-color":"#f5f5f5"})
		 })
		 $(".zoom-buttons input").eq(i).on("touchend",function(){
			 $(".zoom-buttons input").eq(i).css({"background-color":"#fff"})
		 })
	 })
	 $(".commodity-botton-list").on("click", function () {
		var text = $(".commodity-botton-list").val();
		if (text == "收藏公司") {
			$(".commodity-botton-list").val("已收藏")
			$(".commodity-botton-list").css({
				"background": "url(../../img/agritech_img/collect1.png) no-repeat 3rem center #f5f5f5",
				"background-size": ".4rem",
				"padding-left": ".42rem"
			})

		} else {
			$(".commodity-botton-list").val("收藏公司")
			$(".commodity-botton-list").css({
				"background": "url(../../img/agritech_img/collect.png) no-repeat 2.9rem center #f5f5f5",
				"background-size": ".4rem",
				"padding-left": ".7rem"
			})
		}
	});
	function close_company_info(){
		$(".all_company_info").hide();
		$(".collect_button_sc").hide();
		$(".shadow").hide();
		$(".product_all_info").hide();
		$(".inquiry-window").hide();
		$(".all_company_info").removeClass("all_company_info_active");
		$(".indicat_arrow_img_xz").removeClass("xz");
		$(".company_texts span").show();
	}
	function all_company_info_up(){
		$(".all_company_info").toggleClass("all_company_info_active")
		$(".indicat_arrow_img_xz").toggleClass("xz")
	}
	function mores(){
		$(".all_company_info").addClass("all_company_info_active")
		$(".company_text").addClass("auto_height");
		$(".company_texts span").hide();
		$(".indicat_arrow_img_xz").addClass("xz")
	}
	$(".tops input").eq(0).on("click",function(){
		$(this).css("width","5.25rem");
		$(".map-search-button").show()
	})
	$(".commodity-botton-list1").on("click", function () {
		var text = $(".collect_p").text();
		if (text == "收藏展品") {
			$(".collect_p").html("已收藏")
			$(".collect").css({
				"background": "url(../../img/agritech_img/collect1.png) no-repeat center",
				"background-size": ".4rem",
				"left": "1.1rem"
			})
			$(".commodity-botton-list1 p").css("padding-left", ".4rem")
		} else {
			$(".collect_p").html("收藏展品")
			$(".commodity-botton-list1 p").css("padding-left", ".8rem")
			$(".collect").css({
				"background": "url(../../img/agritech_img/collect.png) no-repeat center",
				"background-size": ".4rem",
				"left": "1.12rem"
			})
		}
	});
	var serve_product_id=0;
	var serveCompanyId=0;

	function show_product_info(id){
		// serve_product_id=id;
		// $(".shadow").show();
		// loadProductById(id)
		// isFavorite(id)
		// isInquiry()
		// $(".product_all_info").show()
	}
	/**
	 * 收藏商品
	 */
	function favoriteProduct(type) {
		// //未登录
		// if (localStorage.getItem('usrname') == null) {
		// 	localStorage.setItem("loginBefore", window.location.href);
		// 	window.location.href = '/eip-web-site/pages/mobile-farm-project/login1.html';
		// } else {//收藏
		// 	var id;
		// 	if (type==1) id=serveCompanyId
		// 	else id=serve_product_id
		// 	if (id==1) return false;
		// 	$.ajax({
		// 		url: variableSite + '/serveProductFavorite/addProductFavorite',
		// 		dataType: "json",	//返回数据类型
		// 		type: "post",
		// 		data: {serveProductId:id ,type:type},
		// 		success: function (data) {
		// 			if (data.message == "未登录") {
		// 				localStorage.removeItem('usrname')
		// 				localStorage.setItem("loginBefore", window.location.href);
		// 				window.location.href = '/eip-web-site/pages/mobile-farm-project/login1.html';
		// 			}
		// 		}
		// 	});
		// }
	}
	/**
	 * 是否收藏
	 */
	function isFavorite(id,type) {
		// $.ajax({
		// 	url: variableSite + '/serveProductFavorite/isFavorite/' + id,
		// 	dataType: "json",	//返回数据类型
		// 	type: "post",
		// 	data: {'type': type},
		// 	success: function (data) {
		// 		if (data.result == "已收藏") {
		// 			if (type ==1){
		// 				$(".commodity-botton-list").val("已收藏")
		// 				$(".commodity-botton-list").css({
		// 					"background": "url(../../img/agritech_img/collect1.png) no-repeat 3rem center #f5f5f5",
		// 					"background-size": ".4rem",
		// 					"padding-left": ".42rem"
		// 				})
		// 			}else {
		// 				$(".collect_p").html("已收藏")
		// 				$(".collect").css({
		// 					"background": "url(../../img/agritech_img/collect1.png) no-repeat center",
		// 					"background-size": ".4rem",
		// 					"left": "1.1rem"
		// 				});
		// 				$(".commodity-botton-list1 p").css("padding-left", ".4rem")
		// 			}
		// 		} else {
		// 			if (type ==1){
		// 				$(".commodity-botton-list").val("收藏公司")
		// 				 $(".commodity-botton-list").css({
		// 					"background": "url(../../img/agritech_img/collect.png) no-repeat 2.9rem center #f5f5f5",
		// 					"background-size": ".4rem",
		// 					"padding-left": ".8rem"
		// 				})
		// 			}else {
		// 				$(".collect_p").html("收藏展品")
		// 				$(".commodity-botton-list1 p").css("padding-left", ".8rem")
		// 				$(".collect").css({
		// 					"background": "url(../../img/agritech_img/collect.png) no-repeat center",
		// 					"background-size": ".4rem",
		// 					"left": "1.12rem"
		// 				})
		// 			}
		// 		}
		// 	}
		// });
	}
	function loadProductById(id) {
		$.ajax({
			url: eippath + '/serveProduct/getMore', //请求服务器url地址.
			type: "post",
			data: {id: id},
			success: function (data) {
				var result = data;
				$('.productName').html(result.rows.productName);
				$('#productTypeName').html(result.rows.productTypeName);
				$('#productSpec').html(result.rows.productSpec);
				$('.companyName').html(result.rows.companyName);
				$('#website').html(result.rows.website);
				$('#sectionName').html(result.rows.sectionName);
				$('.boothNum').html(result.rows.boothNum);

				var text = '<p>' + result.rows.productData.replace(/[\n\r]/g, '</p><p>') + '</p>';
				$('.product_all_info_text').html(text);
				var pic = result.rows.pic || '';
				if(pic.startsWith('http')){ }
				else if (pic.indexOf("\\") > -1)  pic = variableAttachment + pic;
				else pic = variablePic +"/"+pic;
				$('.product_all_info_img').html('<img src="' + pic + '" alt="" />');
				return false;
			},
			error: function (data) {
				return false;
			}
		});
	}

	function inquiry_window(){
		 $(".inquiry-window").show();
		}
	$(".shadow").on("click",function(){
		$(".shadow").hide();
		$(".product_all_info").hide()
		 $(".inquiry-window").hide();
		 $(".all_company_info").hide();
		 $(".collect_button_sc").hide();
	})
	function close_product_info(){
		$(".product_all_info").hide();
	}
	function inquiry_affirm(){
		//未登录
		if (localStorage.getItem('usrname') == null) {
			localStorage.setItem("loginBefore", window.location.href);
			window.location.href = '/eip-web-site/pages/mobile-farm-project/login1.html';
		}
		// 初始化 序列化对象的方法
		initSerializeObject();
		if(!$("#inquiry-window-form")[0].checkValidity()) {
			$("#inquiry-window-form")[0].reportValidity();
			return false;
		}
		$(".inquiry-window").hide();
		//获取 from表单的数据
		var formData = $("#inquiry-window-form").serializeObject();
		$.ajax({
			type: "POST",
			dataType: "json",
			url: variableSite+"/serveProductInquiry/addInquiry",
			contentType: "application/json; charset=utf-8",
			data:JSON.stringify(formData),
			success: function (result) {
				alert("保存成功")
			},
			error : function() {
				alert("异常！");
			}
		});
	}
	function inquiry_cancel(){
		$(".inquiry-window").hide();
	}
	/**
	 * 初始化  serializeObject
	 */
	function initSerializeObject() {
		$.fn.serializeObject = function () {
			var o = {};
			var a = this.serializeArray();
			$.each(a, function () {
				if (o[this.name]) {
					if (!o[this.name].push) {
						o[this.name] = [o[this.name]];
					}
					o[this.name].push(this.value || '');
				} else {
					o[this.name] = this.value || '';
				}
			});
			return o;
		};
	}
	/**
	 * 是否询盘
	 */
	function isInquiry() {
		// $.ajax({
		// 	url: variableSite + '/serveProductInquiry/isInquiry/' + serve_product_id,
		// 	dataType: "json",	//返回数据类型
		// 	type: "post",
		// 	success: function (data) {
		// 		//已经询盘过了
		// 		var result = data.result;
		// 		$("#inquiryPeople").val(result.inquiryPeople);
		// 		$("#serveProductId").val(serve_product_id);
		// 		$("#inquiryId").val(result.id);
		// 		$("#inquiryDescription").val(result.inquiryDescription);
		// 		$("#inquiryPhone").val(result.inquiryPhone);
		// 		$("#inquiryEmail").val(result.inquiryEmail);
		// 	}
		// });
	}
	//end
	//是否显示更多
	function showmore(obj){
		if (obj == 1) {
			var length1 =$(".remark").html().length;
			if (length1 >52) {
				$("#mores").show();
			} else{
				$("#mores").hide();
			}
		} else if(obj == 2){
			var length2 =$(".company_text").html().length;
			if (length2 >52) {
				$("#mores").show();
			} else{
				$("#mores").hide();
			}
		}
	}



	function close_company_info2(){
		$(".product_all_info").hide();
	}


var util = {
  exhibitCode: g_exhibitCode,
	lastSetting: '',
  setting: {
		mouseMove: true,
		boothMapShow: true,
		rawSpaceShowAbbrEn: true,
		boothShowOriPrice: true,
		showBoothCode:true,
    showSectionName: true,
	},   // 显示设置数据
  init() {
		this.getShowSetting()
		// setInterval(() => {
		// 	this.getShowSetting(true)
		// }, 5000);
	},
	getHoverTipHtml(name,abbr, abbrEn){
		abbr = abbr || '';
		abbrEn = abbrEn || '';
		return (name || '') + (abbr ? '(<small>'+abbr+'</small>)' : '')+ (abbrEn ? '(<small>'+abbrEn+'</small>)' : '');
	},
	freshShapeText(data,shape_num){
		var showBoothCode = this.setting.showBoothCode
    // if(data.ifStandard===false){
    // // if(data.ifStandard===false && data.companyShow){
      var $s = $('#shape' + shape_num);
      $('#shape' + shape_num + 'text').html(showBoothCode ?
				this.buildShapeText($s.attr('x'),$s.attr('y'),$s.attr('width'),$s.attr('height'),data.boothCode,data.companyAbbr || data.companyName || '',data.companyAbbrEn || '')
				:
				this.buildShapeTextNoCode($s.attr('x'),$s.attr('y'),$s.attr('width'),$s.attr('height'),data.companyAbbr || data.companyName || '',data.companyAbbrEn || '')
				);
			if($s[0].tagName == 'rect') this.fixTextSizeOne($('#shape' + shape_num + 'text'));
    // }else{
		// 	$('#shape' + shape_num + 'text').html(data.boothCode);
    // }
  },
	fixTextSize() {
		setTimeout(() => {
			$('svg text').each((idx , el) => this.fixTextSizeOne($(el)))
			// this.tmp = new Date();
		}, 0);
	},
	fixTextSizeOne($text) {
		let ts  = $text.find('tspan');
		if(ts.length < 1) return;
		let pre  = $text.prev();
		if(pre[0].tagName !== 'rect') return;
		let fsSrc = parseFloat($text.attr('font-size')) || 0.12;
		let size = pre[0].getBBox();
		// let sizeW = size.width - 2/g_size;
		// let sizeH = (size.height - 2/g_size) / ts.length;
		let sizeW = size.width;
		let sizeH = size.height / ts.length;
		//
		ts.each((idx, el) => {
			let fs = fsSrc;
			let size2 = el.getBBox();
			let size2W = size2.width;
			let size2H = size2.height / ts.length;
			// console.log(size, size2);
			if(size2H > sizeH || size2W > sizeW) {
				if(size2H > sizeH ) {
					fs = fs * sizeH / size2H
				}
				if(size2W > sizeW) {
					fs = fs * sizeW / size2W
				}
				$(el).attr('font-size', (fs).toFixed(2) + 'rem');
			}
		})
	},
	buildShapeTextOne(tx,ty,text='', w, h) {
		if(!text && text !== 0) return '';
		return `<tspan text-anchor="middle" alignment-baseline="central" x="\${tx}" y="\${ty}">\${text}</tspan>`;
	},
  buildShapeTextNoCode(x,y,w,h,text1,text2){
		const showCn = this.setting.boothMapShow
    const showEn = this.setting.rawSpaceShowAbbrEn
		x = +x;y = +y;w = +w;h = +h;
		var tx = x + w/2;
		if(showCn && showEn){
			return this.buildShapeTextOne(tx, y + h/3,text1, w, h/2)
					+  this.buildShapeTextOne(tx, y + 2*h/3,text2, w, h/2);
		}else if(showCn){
			return this.buildShapeTextOne(tx, y + h/2,text1, w, h);
		}else if(showEn){
			return this.buildShapeTextOne(tx, y + h/2,text2, w, h);
		}else{
			return ''
		}
	},
  buildShapeText(x,y,w,h,text1,text2,text3){
		const showCn = this.setting.boothMapShow
    const showEn = this.setting.rawSpaceShowAbbrEn
		x = +x;y = +y;w = +w;h = +h;
		const tx = +x + w/2
		if(showCn && showEn){
			return this.buildShapeTextOne(tx, y + h/4,text1,w, h/3)
              +  this.buildShapeTextOne(tx, y + h/2,text2,w, h/3)
              +  this.buildShapeTextOne(tx, y + 3*h/4,text3,w, h/3);
		}else if(showCn){
			return this.buildShapeTextOne(tx, y + h/3,text1,w, h/2)
              +  this.buildShapeTextOne(tx, y + 2*h/3,text2,w, h/2);
		}else if(showEn){
			return this.buildShapeTextOne(tx, y + h/3, text1,w, h/2)
              +  this.buildShapeTextOne(tx, y + 2*h/3, text3,w, h/2);
		}else{
			return this.buildShapeTextOne(tx, y + h/2, text1,w, h);
		}
  },
	fullLoading(close=false,msg="正在更新展位图，请稍候。。。"){
		if(close){
			$('.datagrid-mask,.datagrid-mask-msg').hide();
		}else{
			$("<div class=\"datagrid-mask\"></div>").css({display:"block",width:"100%",height:$(window).height()}).appendTo("body");
			$("<div class=\"datagrid-mask-msg\"></div>").html(msg || '').appendTo("body")
				.css({display:"block",fontSize: '.32rem', left: "10%",right: "10%", backgroundColor: '#ffffffaa'});
		}
	},
	getShowSetting( async = false){
		const that = this
		// that.data = {}
		that.post('boothMapShow/getBoothMapShowSetList',{
			exhibitCode: that.exhibitCode
		},{ async },function( rsp ){
			that.checkReturn(rsp,'', 'info', true)
			if(rsp.data && rsp.data.length){ rsp.data.forEach(it => {
				if(it.showInterface=== 'onLineBoothMapPage' && Object.keys(that.setting).includes(it.showMotion)) {
					that.setting[it.showMotion] = it.showFlag
				}
			}); }
			if(!that.setting.mouseMove && g_building_type===0) close_company_info();
			if(async && (that.lastSetting!= JSON.stringify(that.setting)) ){ // 更新 且变动了
				that.fullLoading()
				// 更新展位图
				init_by_sectionMapId()
				if(+g_value > +g_size){
					size_up(g_value)
				}else{
					size_down(g_value)
				}
				setTimeout(() => {
					that.fullLoading(true)
				}, 1000);
			}
			that.lastSetting = JSON.stringify(that.setting)
		})
	},
  alert: function (msg = "", icon, throws = false) {
    if (msg) {
      $.messager.alert("提示", msg, icon ? icon : "warning");
      if (throws) throw new Error("error: " + msg);
    }
  },
  checkReturn: function (obj, msg = "", icon = "", throws = false) {
    icon = icon || "warning";
    if (obj && obj.state === 1) {
        return true;
    } else {
      this.alert(msg || obj.msg || obj.message, icon, throws);
      return false;
    }
  },
  post (url, data, options, cb, cbErr) {
    const df = {
      // async:true,  // 默认,异步
      type: "post",
      url : eippath + '/'+ url,
      data,
      dataType: "json",
      success: cb || function (rsp) {
        console.log("rsp", rsp);
      },
      error: cbErr || function (data,type, err) {
        console.log("error", data , type , err);
      },
    };
    return $.ajax(options ? Object.assign(df, options) : df);
  },
};
</script>