<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="format-detection" content="telephone=no"/>
  <meta name="viewport" content="user-scalable=no,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1"/>
  <title>确认订单</title>
  <link rel="stylesheet" href="/eip-web-sponsor/css/style.css">
  <script src="/eip-web-sponsor/js/order_detail/variable.js?v=240821"></script>
  <script type="text/javascript" src="/eip-web-sponsor/js/rem.js"></script>
  <script type="text/javascript" src="/eip-web-sponsor/js/jquery.min.js"></script>
  <%--<script src="/eip-web-sponsor/js/style.js" type="text/javascript" charset="utf-8"></script>--%>
  <link rel="stylesheet" type="text/css" href="/eip-web-sponsor/vue/vant/index.css"/>
  <script src="/eip-web-sponsor/vue/vue.js"></script>
  <script src="/eip-web-sponsor/vue/vant/vant.min.js"></script>
  <script src="/eip-web-sponsor/vue/axions.js"></script>
  <%--<script src="/eip-web-sponsor/common/variable.js"></script>--%>
  <script>
    if(!Date.prototype.format){
      Date.prototype.format = function (fmt) { //author: meizz
        //yyyy/MM/dd hh:mm:ss
        var o = {
          "M+": this.getMonth() + 1, //月份
          "d+": this.getDate(), //日
          "h+": this.getHours(), //小时
          "m+": this.getMinutes(), //分
          "s+": this.getSeconds(), //秒
          "q+": Math.floor((this.getMonth() + 3) / 3), //季度
          "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
      };}
  </script>
  <style>
      html,
      body,
      #app {
          margin: 0;
          padding: 0;
          display: flex;
          flex-direction: column;
      }

      .empty-order-list {
          height: calc(100vh - 1.89rem);
          display: flex;
          align-content: center;
          justify-content: center;
          align-items: center;
          background: #fff;
          color: #999;
          font-size: 0.3rem;
      }

      .main_content {
          /* height: calc(100% - 1.88rem); */
          display: flex;
          flex-direction: column;
          align-items: center;
          box-sizing: border-box;
      }


      .marMenu_main {
          flex: 1;
          overflow-y: auto;
          height: 100%;
          width: 100%;
      }


      .card_main {
          display: flex;
          padding: 0 0.1rem 0rem;
          align-items: center;
          margin-bottom: .1rem;
      }

      .card_main_content {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          flex: 1;
          background-color: #f5f5f5;
          padding: .14rem 0.24rem .1rem;
          border-radius: 4px;
      }

      .card_main_content_text {
          color: #333;
          font-size: 0.32rem;
      }

      .card_main_content_subtext {
          color: #666;
          font-size: 0.24rem;
          /* display: flex;
          justify-content: space-between;
          align-items: center; */
      }

      .card_main_content_price {
          color: #ff4444;
          font-size: 0.28rem;
      }

      [v-cloak] {
          display: none;
      }

      .orderNum {
          font-size: .24rem;
          line-height: 0.6rem;
          color: #4c4c4c;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.34rem;
          box-sizing: border-box;
      }
      .orderNum-company-name{
          font-weight: 700;
          font-size: 0.3rem;
          color: #333;
          line-height: 0.45rem;
          max-width: 70vw;
          overflow: hidden;
          display: inline-block;
          text-overflow: ellipsis;
      }

      .orderNumList {
          background-color: #fff;
          margin-bottom: .2rem;
      }

      .order-top-info {
          width: 7.02rem;
          background-color: #ffffff;
          box-shadow: 0px 0px 0.1rem 0px rgba(0, 0, 0, 0.1);
          border-radius: .1rem;
          margin: .24rem;
          box-sizing: border-box;
          padding: .24rem;
          position: relative;
      }

      .order-top-info > p {
          font-size: .24rem;
          color: #666666;
          overflow: hidden;
          line-height: .42rem;
      }

      .order-time {
          width: 100%;
          /* height: 3.52rem; */
          background-color: #ffffff;
          /* padding-bottom: 1rem;           */
          margin-bottom: 0.22rem;
          padding-bottom: .44rem;
      }

      .order-time > h6 {
          font-size: .28rem;
          font-weight: bold;
          color: #808080;
          padding: .3rem 0 .2rem .34rem;
      }

      .order-time-text {
          width: 7.02rem;
          background-color: #f5f5f5;
          border-radius: .08rem;
          margin-left: .24rem;
          box-sizing: border-box;
          padding: .22rem .26rem;
      }

      .order-time-text > p {
          font-size: .26rem;
          line-height: .46rem;
          color: #808080;
      }

      .order-time-text > p > span {
          margin-left: 1.04rem;
          font-size: .24rem;
      }
      .order-pay{
				width: 100%;
				/* height: 3.52rem; */
				background-color: #ffffff;
				margin-top: .22rem;
				padding-bottom: .33rem;
			}
			.order-pay>h6{
				font-size: .28rem;
			    font-weight: bold;
			    color: #808080;
			    padding: .3rem 0 .2rem .34rem;
			}
			.order-pay-text{
				width: 7.02rem;
			    background-color: #f5f5f5;
			    border-radius: .08rem;
			    margin-left: .24rem;
			    box-sizing: border-box;
			    padding: .22rem .26rem;
			}
			.order-pay-text>p{
			    font-size: .26rem;
			    line-height: .46rem;
			   color: #808080;
			}
			.order-pay-text>p>span{
			    margin-left: 1.04rem;
				font-size: .24rem;
			}
      .remark-span {
          float: left;
          width: 5.6rem;
          white-space: break-spaces;
      }

      .numberArea {
          font-size: .24rem;
          color: #999;
          margin-left: .15rem;
          margin-right: .24rem;
      }

      .orderTotal {
          line-height: .74rem;
          border-bottom: .02rem solid #f0f0f0;
      }

      .orderOperate {
          height: .9rem;
          line-height: .9rem;
          text-align: right;
      }

      .orderOperate > span {
          display: inline-block;
          height: .5rem;
          background-color: #ffffff;
          border-radius: .25rem;
          border: solid .01rem #cccccc;
          line-height: .5rem;
          padding: 0 .21rem;
          font-size: .24rem;
          color: #808080;
          margin-right: .24rem;
      }

      .boothDel {
          height: .42rem;
          background-color: #ffffff;
          border-radius: .21rem;
          border: solid .01rem #cccccc;
          color: #808080;
          font-size: .2rem;
          padding: 0 .19rem;
          display: inline-block;
          line-height: .42rem;
          float: right;
      }

      .orderCompile {
          position: absolute;
          top: 0;
          right: 0;
      }
      .orderTotal {
        font-size: 0.28rem;
      }
  </style>
  <style>
      #app .van-nav-bar {
          line-height: 0.44rem;
      }
      #app .van-nav-bar .van-nav-bar__title,
      #app .van-nav-bar .van-icon {
          color: #333;
          font-size: .32rem;
      }
      #app .van-nav-bar .van-nav-bar__content {
          height: 0.92rem;
      }
  </style>
  <style>
    [v-cloak]{display: none}
    .page {
        overflow: hidden;
        /* position: absolute; */
        right:0;left: 0;height: 100vh;
        /* top: 0;bottom: 0;z-index: 999; */
        display: flex;
        flex-direction: column;
        background-color: #f5f5fa;
      }
    .flex-between{
      display: flex;
      flex-flow:  row nowrap;
      justify-content: space-between;
    }
    .exchangeList .li span{
      color: #888888;
      line-height: 5.3333vw;
      font-size: 2.9333vw;
    }
    .exchangeList .title+div{
      padding-top: 1.0667vw;
    }
    .exchangeList .liTop+div{
      border-top: 1px dashed #d5d5d5
    }
    .exchangeList .liTop{
      /* display: inline-block; */
      color: #14171a;
      line-height: 7.2vw;
      font-size: 4vw;
      padding: 3.2vw 0 1.6vw;
    }
    .exchangeList .li{
      display: block;
      padding: 0 3.2vw 0;
      margin: 0 4.2667vw 2.6667vw;
      background: #ffffff;
      border-radius: 2.6667vw;
      box-shadow: 0px 1.5px 3.2vw 0px rgba(0,0,0,0.08);
      color: var(--primary-color) !important;
    }
    .page .used {
      opacity: 0.5;
    }
    .page .van-nav-bar .van-icon,.van-nav-bar .van-nav-bar__text {
      color: black;
    }
    /* .page .van-nav-bar {
        height: 2.875rem !important;
    } */
    .page .couponNav .van-icon,.couponNav .van-nav-bar__text {
				color: black;
    }
    .page .couponNav {
        height: 0.88rem !important;
    }
    .van-stepper--round .van-stepper__minus {
      color: rgb(0, 141, 211) !important;
      border-color: rgb(0, 141, 211) !important;
    }
    .van-stepper--round .van-stepper__plus {
      background-color: rgb(0, 141, 211) !important;
      border-color: rgb(0, 141, 211) !important;
    }
    .van-stepper__input {
      border: 1px solid #8888883b !important;
      border-radius: 5px !important;
    }
    .submit-button {
      background: linear-gradient(90deg,#2fcc54, #2fcc54 1%, #18ab3b) !important;
      border-radius: 5px !important;
    }
    #app .cart-wrapper {
      margin-bottom: 1rem !important;
    }
  </style>
</head>
<body style="background-color: #f5f5f5;height: 100%;">
<div id="app" v-cloak>
  <van-nav-bar :title="title" left-arrow @click-left="goBack" fixed placeholder></van-nav-bar>
  <form action="/" class="search-form">
    <van-search
        v-if="isParticulars === 1"
        v-model="searchArgs.companyName"
        placeholder="公司名称搜索"
        autofocus
        @search="loadPreorderList(!0)"
    ></van-search>
  </form>
  <div class="reserve_page" v-if="isParticulars==1" style="margin-top: 0.24rem;">
    <div class="main_content">
      <div class="marMenu_main">
        <van-list
            ref="list"
            v-model="loading"
            :finished="finished"
            @load="loadPreorderList()"
            finished-text="没有更多了"
        >
          <div class='orderNumList' v-for="(item, index) in orderList" :key="index">
            <div class="orderNum" @click="particulars(item,index)">
              <p style="line-height: 1;width: 60vw;">
                <span class="orderNum-company-name">{{item.boothPreorder.companyName}}</span><br/>
                <span>订单编号：{{item.boothPreorder.preorderId}}</span>
              </p>
              <span v-if="item.boothPreorder.saleState == '0'" style="color: #4c4c4c;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else-if="isFullProcess" style="color: #5284f6;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else-if="item.boothPreorder.saleState == '1'" style="color: #5284f6;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else-if="item.boothPreorder.saleState == '2'" style="color: #ff801a;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else-if="item.boothPreorder.saleState == '3'" style="color: #33b744;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else-if="item.boothPreorder.saleState == '4'" style="color: #33b744;">{{item.boothPreorder.saleState | saleStateName}}</span>
              <span v-else>{{item.boothPreorder.saleState | saleStateName}}</span>
            </div>
            <div class="card_main" v-for="(items, index2) in item.exhibitBooths" @click="particulars(item,index)" :key="index2">
              <div class="card_main_content">
                <p class="card_main_content_text">
                  <span>{{items.sectionName}}-</span><span>{{items.typeName}}-</span><span>{{items.boothCode}}</span><span
                    v-if="items.openInfoName!=null && items.openInfoName!=' ' ">（{{items.openInfoName}}）</span></p>
                <div class="card_main_content_subtext"><span>规格：{{items.boothSpec}}</span> <span
                    style="margin-left: .48rem">{{items.boothArea}} 平米</span><span style="float: right;">价格: {{finalPrice(items)}}</span>
                </div>
              </div>
            </div>
            <div class="orderTotal" @click="particulars(item,index)">
              <p style="text-align: right;"><span class="card_main_content_price">
                {{ item.boothPreorder.saleState | payPriceTitle(item) }}
                {{finalPrice({...item.exhibitBooths[0], transactionPrice: Math.max(0,
                  (item.boothPreorder.salePrice - (+item.boothPreorder.discountPrice)).toFixed(2)
                  ) })}}
                  <!-- 合计：{{finalPrice({...item.exhibitBooths[0], transactionPrice: item.boothPreorder.salePrice})}} -->
                </span><span class="numberArea">
                <!-- ({{item.exhibitBooths | pcs}}个展位，{{item.exhibitBooths | allArea}}平米) -->
              </span></p>
            </div>
            <div class="orderOperate"  style="
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            align-items: center;padding-left: 0.24rem;">
            <div style="color: #666;font-size: 0.24rem;letter-spacing: 1px;">
              {{ getPayDesc(item.boothPreorder) || ''}}
            </div>
            <span v-if="item.boothPreorder.saleState == 0" @click="orderDel(item.boothPreorder.preorderId)">删除</span>
            <span v-else @click="cancelOrder(item.boothPreorder.preorderId,2)">取消订单</span>
            </div>
          </div>
          <div class="empty-order-list" v-if="orderList.length === 0">暂无订单</div>
        </van-list>
      </div>
    </div>
  </div>
  <div class="reserve_page" v-if="isParticulars==2">
    <div class="order-top-info" v-for="(item, index) in orderParticulars">
      <p>公司名称：{{item.boothPreorder.companyName}}</p>
      <p>联系人：{{item.boothPreorder.linkman}} <template v-if="item.boothPreorder.position">/</template> {{item.boothPreorder.position}}</p>
      <p>会员等级：{{item.boothPreorder.memberGradeName}}</p>
      <p>电话：{{item.boothPreorder.telephone}}</p>
      <p>业绩归属人：{{item.boothPreorder.ownerName}}</p>
      <p class="remark">
        <span style="float: left;">备注：</span>
        <span class="remark-span">{{item.boothPreorder.memo}}</span>
      </p>
    </div>
    <div class="orders">
      <div class='orderNumList' v-for="(item, index) in orderParticulars" :key="index">
        <div class="orderNum">
          <p><span>订单编号：</span>{{item.boothPreorder.preorderId}}</p>
          <span v-if="item.boothPreorder.saleState == '0'" style="color: #4c4c4c;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else-if="isFullProcess" style="color: #5284f6;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else-if="item.boothPreorder.saleState == '1'" style="color: #5284f6;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else-if="item.boothPreorder.saleState == '2'" style="color: #ff801a;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else-if="item.boothPreorder.saleState == '3'" style="color: #33b744;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else-if="item.boothPreorder.saleState == '4'" style="color: #33b744;">{{item.boothPreorder.saleState | saleStateName}}</span>
          <span v-else>{{item.boothPreorder.saleState | saleStateName}}</span>
        </div>
        <div class="card_main" v-for="(items, index) in item.exhibitBooths">
          <div class="card_main_content" style="margin-bottom: .1rem;">
            <p class="card_main_content_text"><span>{{items.sectionName}}-</span><span>{{items.typeName}}-</span><span>{{items.boothCode}}</span>
              <span v-if="items.openInfoName!=null && items.openInfoName!=' '">（{{items.openInfoName}}）</span>
              <%--<span class="boothDel" v-if="item.boothPreorder.saleState == '1'" @click="boothDel(item.boothPreorder.preorderId,items.boothId)">删除展位</span>--%>
            </p>
            <div class="card_main_content_subtext" style="margin-top: .08rem;"><span>规格：{{items.boothSpec}}</span>
              <span style="margin-left: .48rem">{{items.boothArea}} 平米</span><span style="float: right;">价格: {{finalPrice(items)}}</span>
            </div>
          </div>
        </div>
        <div class="orderTotal flex-between" style="border: none;padding: 0 0.24rem; " v-if="item.boothPreorder.salePrice != salePriceTotal.transactionPrice">
          <span>
            展位原价：
          </span>
          <span class="card_main_content_price" style="text-decoration: line-through;color: #676767">
            {{ finalPrice(salePriceTotal) }}
            <!-- {{finalPrice({
              ...item.exhibitBooths[0],
              transactionPrice: Math.max(0, item.boothPreorder.salePrice),
            })}} -->
          </span>
        </div>
        <div class="orderTotal flex-between"  style="border: none;padding: 0 0.24rem;">
          <span>
            订单金额：
          </span>
          <span class="card_main_content_price" style="color: #676767">
            {{finalPrice({
              ...item.exhibitBooths[0],
              transactionPrice: Math.max(0, item.boothPreorder.salePrice),
            })}}
          </span>
        </div>
        <div class="orderTotal order__coupon flex-between" style="border: none;padding: 0 0.24rem;">
          <span>展位代金券</span>
          <div v-if="ifCanChangeCoupon" style="display: inline-flex;align-items: center;" @click="selectCoupon">
            <span style="color: #666" v-html="couponsTip">
              <!-- {{ item.boothPreorder.discountPrice ? '已抵扣¥'+(+item.boothPreorder.discountPrice || 0) : '可用代金券('+couponsNum+')' }} -->
            </span>
            <i class="van-icon van-icon-arrow van-cell__right-icon" v-if="!useCoupon || couponsNum"></i>
          </div>
          <div v-else style="display: inline-flex;align-items: center;">
            <span style="color: #666" v-html="couponsTip">
              <!-- {{ '已抵扣¥'+ (+item.boothPreorder.discountPrice || 0) }} -->
            </span>
          </div>
        </div>
        <div class="orderTotal " v-if="item.boothPreorder.saleState" style="border: none;padding: 0 0.24rem 0.2rem;">
          <p style="" class="flex-between">
            <span>{{ item.boothPreorder.saleState | payPriceTitle(item) }}</span>
            <span class="card_main_content_price">
              {{finalPrice({
                ...item.exhibitBooths[0],
                transactionPrice: Math.max(0,
                (item.boothPreorder.salePrice - (+item.boothPreorder.discountPrice)).toFixed(2)
                ),
              })}}
            </span>
            <!-- <span class="numberArea">
              < !-- ({{item.exhibitBooths | pcs}}个展位，{{item.exhibitBooths | allArea}}平米) -- >
            </span> -->
          </p>
        </div>
      </div>
    </div>
    <div class="order-time">
      <h6>订单详细信息</h6>
      <div class="order-time-text" v-for="(item, index) in orderParticulars">
        <p v-if="item.boothPreorder.preoderTime != null ">预定时间：<span>{{item.boothPreorder.preoderTime}}</span></p>
        <!-- <p v-if="item.boothPreorder.preoderReserveTime != null">预定保留时间：<span style="margin-left: .5rem;">{{item.boothPreorder.preoderReserveTime}}</span>
        </p> -->
        <p v-if="!isFullProcess && item.boothPreorder.contractTime != null">合同时间：<span>{{item.contractTime}}</span>
        </p>
        <p v-if="!isFullProcess && item.boothPreorder.contractReserveTime != null">合同保留时间：<span
            style="margin-left: .5rem;">{{item.boothPreorder.contractReserveTime}}</span></p>
        <p v-if="!isFullProcess && item.boothPreorder.payTime != null">
          付款时间：<span>{{item.boothPreorder.payTime}}</span></p>
        <p v-if="item.boothPreorder.cancelTime != null ">取消时间：<span>{{item.boothPreorder.cancelTime}}</span></p>
      </div>
    </div>
    <div class="order-pay" v-if="detailPayInfoShow">
      <h6>{{ getPayTypeDesc(detailPayInfo.payType) }}支付信息</h6>
      <div class="order-time-text">
        <p>{{ getPayTypeDesc(detailPayInfo.payType) }}支付ID：<span>{{detailPayInfo.orderTradeId || ''}}</span></p>
        <p>支付状态：<span>{{ getPayStateDesc(detailPayInfo.finalPayOrderState) }}</span></p>
        <p>交易完成时间：<span>{{detailPayInfo.paySuccessTime || ''}}</span></p>
        <p v-if="detailPayInfo.finalPayOrderState && detailPayInfo.finalPayOrderState > 1">退款时间：<span>{{detailPayInfo.refundTime || ''}}</span></p>
      </div>
    </div>
  </div>

  <!-- 代金券选择 -->
  <van-popup v-model="showSelectCoupon" position="right" class="page">
    <!-- title="代金券选择" -->
    <!-- right-text="积分商城" -->
    <!-- @click-right="goScoreMall" -->
    <van-nav-bar
      left-text=" 选择代金券"
      left-arrow
      class="couponNav"
      fixed
      placeholder
    @click-left="selectCouponBack"
    ></van-nav-bar>
    <!-- <van-sticky>
    <div  style="height: 10vw;line-height: 10vw;width: 100%;
    background-color: #f9f9f9;color: #000;position: fixed;top: 0;font-size: 4.2667vw;align-items: center;" class="flex-between">
      <div @click="page='main'" style="width: auto">
        <van-icon name="arrow-left" :style="{width: '10vw',textAlign:'center'}" ></van-icon>选择代金券
      </div>
    </div>
    </van-sticky> -->
    <div class="flex-between" style="margin: 4vw 4.2667vw 2.6667vw;font-size: 3.7333vw;">
      <span>代金券</span>
      <span style="color: #888;">本次抵扣金额 ¥{{ disAmount }}</span>
    </div>
    <div style="flex: 1;overflow-y: auto;">
      <van-checkbox-group class="exchangeList" v-model="selectIds">
        <div
          v-for="(it,idx) in couponsEx" :key="idx"
          :class="['li',isValid(it) ? '': 'used']">
          <div class="liTop" @click="selectToggle(it)">
            <div class="title flex-between">
              <div>
                {{ it.goodsName || '' }}
              </div>
              <van-checkbox :name="it.goodsId" :label-disabled="!isValid(it)"></van-checkbox>
            </div>
            <div>
              <span>有效期至: {{ it.validTime || '~'}}</span>
            </div>
          </div>
          <div class="flex-between" style="align-items: center;font-size: 3.2vw;margin: 1vw 0;">
            <a style="color:#52A3FC;display: flex;padding:4.6667vw 0;align-items: center;" >
              <div style="height: 4vw;">剩余{{ it.surplusNum || 0 }}张</div>&nbsp;&nbsp;
              <div style="height: 4vw;">面值 ¥{{ it.deductionMoney || 0 }}</div>&nbsp;&nbsp;
            </a>
            <div>
              <!-- disable-input -->
              <!-- async-change -->
              <!-- :disabled="!selectIds.includes(it.goodsId)" -->
              <van-stepper v-model="it.selectNum" theme="round" button-size="22"
                :max="it.useLimit ? Math.min(+it.useLimit || 0, +it.surplusNum || 0) : (+it.surplusNum || 0)" min="0"
                input-width="5em"
                integer
                :long-press="false"
                @change="changeCouponNum(it)"
              />
            </div>
            <!-- <div style="border: 1px solid #d5d5d5;display: inline-block;
              border-radius: 16.5px; padding: 1.8667vw 2.6667vw;font-size: 3.2vw;">
              打开凭证</div> -->
          </div>
      </div>
      </van-checkbox-group>
    </div>
    <div style="padding: 10px;">
      <van-button color="#008dd3" type="info" size="large"
        @click="selectedCoupon">{{ selectIds.length ? '确认使用' : '不使用优惠券' }}</van-button>
    </div>
  </van-popup>
</div>
<script>
  const Axios = axios.create({baseURL: '/eip-web-sponsor'})
  const searchParams = new URL(location.href).searchParams
  const exhibitCode = searchParams.get('exhibitCode')
  const orgNum = searchParams.get('orgNum')
  const pid = searchParams.get('pid')

  // 优惠券选择
  const mixinCoupon = {
    data() {
      return {
        showSelectCoupon: false,
        projectId: pid,
        disAmount: 0,  // 抵扣金额
        useCoupon: false, // 是否使用优惠券
        selectIds: [], // 优惠券ID
        selectNum: [], // 优惠券数量
        coupons: [],
        // zzyuserId: '',
      }
    },
    computed: {
      couponsEx() {
        // this.coupons.map(it=>{
        //   it.selectNum = 0
        // })
        return this.coupons;
      },
      couponsTip() {
        const disMax = Math.min(this.disAmount, this.transactionPriceTotal.transactionPrice);
        return this.useCoupon ? '已抵扣<span style="color: #ff4444">¥' + this.disAmount  + '</span>':
        '可用<span style="color: #ff4444">'+this.couponsNum+'</span>代金券';
      },
      couponsNum() {
        let num = 0;
        this.coupons.map(it=>{
          num += +it.surplusNum || 0;
        })
        return num;
      },
      // payAmount() {
      //   const p = +this.transactionPriceTotal.transactionPrice || 0;
      //   return Math.max(0, p - (+this.disAmount || 0)).toFixed(2);
      // },
    },
    methods: {
      goScoreMall() {
        // 前往积分商城
        location.href = '../../vip-subpages/my-score.html?p=' + this.projectId + '&refer='+ encodeURIComponent(location.href)
      },
      changeCouponNum(it) {
        const max = it.useLimit ? Math.min(+it.useLimit || 0, +it.surplusNum || 0) : (+it.surplusNum || 0);
        if(it.selectNum > max) {
          it.selectNum = max;
          // this.error('最多使用 ' + max + '张');
        }
        this.buildDisAmount();
        event.stopPropagation();
      },
      buildDisAmount() { // 没时间了, 就这样
        let sum = 0,tmp;
        this.selectIds.map(it => {
          tmp = this.couponsEx.find(itt=> itt.goodsId === it) || {};
          sum += (tmp.deductionMoney || 0)*(tmp.selectNum || 0)
        })
        this.disAmount = (+sum).toFixed(2);
      },
      buildPayPara() {
        // const tmp = [];
        const tmp = {};
        this.selectIds.map((it,idx) => {
          const tmp2 = this.couponsEx.find(itt=> itt.goodsId === it) || '';
          if(tmp2) {
            tmp[`useCoupons[${idx}].goodsId`] = tmp2.goodsId;
            tmp[`useCoupons[${idx}].userNum`] = tmp2.selectNum;
            // tmp.push({
            //   goodsId: tmp2.goodsId,
            //   userNum: tmp2.selectNum,
            // })
          }
        })
        return tmp;
      },
      obj2fd(obj) {
        if (typeof obj !== 'object') return;
        const formData = new FormData();
        Object.keys(obj).forEach(key => formData.append(key, obj[key]));
        return formData;
      },
      error(msg='请求失败', ops={type: 'toast'}, throws = true) {
        this.$toast.fail(msg)
        if(throws) throw new Error(msg)
      },
      ok(msg="操作成功"){
        this.$toast.success(msg)
      },
      isValid(row) {
        if(row.validTime && row.validTime < new Date().format('yyyy-MM-dd hh:mm:ss')){
          return false
        } else if(row.surplusNum < 1) {
          return false
        }
        return true
      },
      async getUserCoupon(){
        const {data} = await Axios
          .post('/eip-web-crm/api/zzyUserIntegral/getZzyUserCoupon',this.obj2fd({
            orderTradeType: 2,
            projectId: this.projectId,
            zzyUserId: this.orderParticulars[0].boothPreorder.zzyuserId ||'',
          }),{baseURL: '/'})
          .catch(_=> this.error('查询优惠券失败')).finally(_ =>{ })
        if(data.state !==1) this.error(data.msg || data.message || '查询优惠券失败!')
        this.coupons = data.data || []
        this.coupons.map(it=> {
          it.selectNum = 0;
          it.isValid = !!this.isValid(it);
        })
        // 设置选中
        // this.coupons.map(it=> {
        //   if(this.selectIds.includes(it.goodsId)) {
        //     it.selectNum = this.selectNum[this.selectIds.findIndex(itt=> itt == it.goodsId)];
        //   } else {
        //     it.selectNum = 0;
        //   }
        // })
      },
      getCouponMax(it) {
        if(it) {
          return it.useLimit ? Math.min(+it.useLimit || 0, +it.surplusNum || 0) : (+it.surplusNum || 0)
        }
        return 0;
      },
      async selectCoupon() {
        // if(this.ifDisableCoupon) return;
        // if(this.isMultiCert) {
        //   this.$dialog.alert({
        //     title: '无法使用代金券',
        //     message: '当前订单中存在多种证件<br>请分批次购买不同证件以使用代金券',
        //     confirmButtonColor: '#000',
        //   }).then(() => {
        //     // on close
        //   });
        //   return;
        // }
        this.useCoupon = false;
        this.showSelectCoupon = true;
        this.selectIds = [];
        this.selectNum = [];
        const loading = this.$loading();
        try{
          await this.getUserCoupon();
          this.autoSetFirstCoupon();
          this.buildDisAmount();
        } catch(e) { throw e
        } finally {
          loading && loading.clear();
        }
      },
      autoSetFirstCoupon() {
        // 订单金额
        const orderPrice = +this.orderParticulars[0].boothPreorder.salePrice || 0;
        // 第一个可使用的优惠券
        const firstValidCoupon = this.coupons.find(it => it.isValid) || '';
        if(!firstValidCoupon || !orderPrice) return;
        // 可用最大张 // 订单金额/优惠券面值 向上取整
        const selectNum = Math.max(0,
          Math.min(this.getCouponMax(firstValidCoupon),Math.ceil(orderPrice/(firstValidCoupon.deductionMoney)))
          );
        this.selectIds = [firstValidCoupon.goodsId];
        firstValidCoupon.selectNum = selectNum;
      },
      selectToggle(it) {
        const {goodsId} = it
        if(this.isValid(it)) {
          if(this.selectIds.includes(goodsId)) {
            this.selectIds = this.selectIds.filter(it => it !== goodsId)
          } else {
            this.selectIds.push(goodsId);
            if(!it.selectNum) it.selectNum = 1
          }
        }
        this.buildDisAmount();
      },
      async selectedCoupon() {
        this.useCoupon = true;
        // if(this.selectIds.length) {
        // } else {
        // 	this.selectIds = [];
        // }
        // this.showSelectCoupon = false;
        const orderPrice = +this.orderParticulars[0].boothPreorder.salePrice || 0;
        if(+this.disAmount > +orderPrice) {
          await this.$dialog.confirm({
            title: '提醒',
            message: '当前使用代金券金额已大于订单金额,是否确认使用?',
            confirmButtonText: '确认',
            confirmButtonColor: '#108EE9',
            cancelButtonText: '关闭',
          })
          this.disAmount = orderPrice;
        }
        const params = {
          preOrderId: this.orderParticulars[0].boothPreorder.preorderId || '',
          useCoupons: [],
        };
        if(this.selectIds && this.selectIds.length) {
          this.selectIds.map((it,idx) => {
            const tmp = this.coupons.find(itt => itt.goodsId == it) || {};
            params.useCoupons.push({
              goodsId: it,
              userNum: tmp.selectNum,
            })
          })
        }
        const {data} = await Axios
          .post('/api/boothPreOrder/changeCoupon', params)
          .catch(_ => this.error());
        if(data.state !== 1) this.error(data.msg || data.message || '优惠券使用失败');
        alert('优惠券使用成功');
        location.reload();
      },
      selectCouponBack() {
        this.useCoupon = false;
        this.selectIds = [];
        this.selectNum = [];
        // if(this.selectIds.length) {
        //   this.selectIds.map((it,idx)=> {
        //     this.selectNum[idx] = (this.coupons.find(itt => itt.goodsId == it) || {}).selectNum || 0
        //   })
        // } else {
        //   this.selectNum = [];
        // }
        this.showSelectCoupon = false
      },
    }
  }
  const app = new Vue({
    el: "#app",
    data() {
      return {
        preorderId: '',
        isParticulars: 1,
        orderParticulars: [],
        orderID: 0,
        isFullProcess: false, // 是否全流程
        loading: false,
        finished: true,
        freshTarget: -1, // TODO 判断更新详情后分页刷新那一页的
        orderList: [],
        searchArgs: {
          projectFlow: '',
          saleState: -1,
          exhibitCode,
          page: 1,
          rows: 20,
          companyName: '',
          ifOperator: true,
        },
      }
    },
    mixins: [mixinCoupon],
    methods: {
      getPayDesc(item) {
        if(item.saleState == 3) {
          if(item.ifOpenPay) {
            return '线上支付-' + this.getPayTypeDesc(item.payType) + '  ¥' + item.payMoney || ''
          }
          return '线下支付-收款单';
        }
      },
      getPayTypeDesc(paytype) {
        return ['微信','支付宝','网银'][paytype] || ''
      },
      getPayStateDesc(paystate) {
        return ['未支付','支付成功','订单关闭'][paystate] || ''
      },
      $loading(msg) {
        return this.$toast.loading({
          message: '加载中...' || msg,
          forbidClick: true,
          loadingType: 'spinner',
        })
      },
      obj2FormData (obj) {
        if (typeof obj !== 'object') return;
        const formData = new FormData();
        Object.keys(obj).forEach(key => formData.append(key, obj[key]));
        return formData;
      },
      errorMessage(message, option) {
        message = message || '数据加载失败！'
        option = option || {}
        const toastType = option['toastType']
        if(option.type === 'toast'){
          return this.$toast({message, ...option, type: toastType})
        }else if(option.type === 'confirm'){
          return this.$dialog.confirm({message, ...option})
        }
        return this.$dialog.alert({message, ...option})
      },

      cancelOrder(preorderId, type) {
        let url = variableSponsor + '/boothPreorder/sponsorCancelPreorder'
        let params = new FormData()
        params.set('preorderId', preorderId)
        params.set('operEntrance', '手机端主办方订单管理')
        let that = this
        this.$dialog.confirm({
          title: '提示',
          message: '确认取消订单吗',
        })
          .then(() => {
            Axios.post(url, params).then(function (response) {
              if (response.data.state == 1) {
                if (type == 1) {
                  that.preorderId = preorderId
                }
                that.loadPreorderList(!0)
                that.errorMessage('取消成功', {type: 'toast', toastType: 'success'})
              } else {
                that.errorMessage('取消订单失败', {type: 'toast', toastType: 'fail'})
              }
              that.loadPreorderList(!0)
            });
          })
          .catch(() => {
            console.log("2")
          });
      },
      orderDel(preorderId) {
        let url = variableSponsor + '/boothPreorder/delPreorder'
        let params = new FormData()
        params.set('preorderId', preorderId)
        params.set('operEntrance', '手机端主办方订单管理')
        let that = this
        this.$dialog.confirm({
          title: '提示',
          message: '确认删除订单吗',
        })
          .then(() => {
            Axios.post(url, params).then(function (response) {
              if (response.data.state == 1) {
                that.errorMessage('删除成功', {type: 'toast', toastType: 'success'})
              } else {
                that.errorMessage('删除失败', {type: 'toast', toastType: 'fail'})
              }
              that.loadPreorderList(!0)
            });
          })
          .catch(() => {
            console.log("2")
          });
      },
      async getOrderDetailByPreorderId(preorderId) {
        let params = new FormData()
        params.set('preorderId', preorderId)
        const {data} = await Axios
          .post(variableSponsor + '/boothPreorder/selectOnePreorder', params)
          .catch(_ => this.error())
        if(data.state !== 1) this.error(data.message || data.msg);
        let rs = data.result ||{};
        let isForeign = (rs.boothPreorder ||{}).isForeign;
        rs.exhibitBooths.map(itt=> {
          itt.salePriceExt = isForeign ? itt.foreignPrice : itt.salePrice;
          return itt;
        })
        this.orderParticulars.push(rs); // 用0赋值不触发 computed
      },
      async particulars(datas,index) {
        // 查询订单详情
        // this.orderParticulars.push(datas)
        await this.getOrderDetailByPreorderId(datas.boothPreorder.preorderId);
        this.freshTarget = index;
        //
        this.isParticulars = 2
        this.orderID = this.orderParticulars[0].boothPreorder.preorderId
        this.preorderId = this.orderParticulars[0].boothPreorder.preorderId
      },

      back2() {
        this.isParticulars = 1
        this.orderParticulars = []
      },
      goBack(){
        if (this.isParticulars > 1)
          return this.back2()
        history.back()
      },

      async getFullProcess() {
        this.isFullProcess = true;// data === 'fullProcess'
        // try {
        //   const {data: {data}} = await axios.post('/eip-web-site/sysSettingOrg/selectProjectFlow', this.obj2FormData({orgNum}))
        //   this.isFullProcess = data === 'fullProcess'
        //   this.$set(this.searchArgs, 'projectFlow', data || 'simpleProcess')
        // } catch (e) {
        //   console.warn(e)
        // }
      },
      async loadPreorderList(clean){
        if (clean) {
          this.searchArgs.page = 1
          this.orderList = []
          this.finished = false
          return this.$nextTick(() => {
            this.$refs.list && this.$refs.list.check()
          })
        }
        try {
          const {data: {state, rows, msg}} = await Axios.post('/boothPreorder/getPreorderList?key=1', this.obj2FormData(this.searchArgs))
          if (state !== 1) await Promise.reject(msg)
          if (!rows.length) return (this.finished = true)
          let isForeign = false;
          rows.map(it=> {
            isForeign = it.boothPreorder.isForeign;
            it.exhibitBooths.map(itt=> {
              itt.salePriceExt = isForeign ? itt.foreignPrice : itt.salePrice;
              return itt;
            })
            return it;
          })
          this.orderList.push(...rows)
          this.$set(this.searchArgs, 'page', this.searchArgs.page + 1)
        }catch (e) {
          if (e === 'cancel') return (this.finished = true)
          e && console.warn(e)
          this.errorMessage(e || void 0)
          this.finished = false
        }finally {
          this.loading = false
        }
      }
    },
    async mounted() {
      if (!exhibitCode || !pid || !orgNum) return this.errorMessage('参数异常！').finally(this.goBack)
      const loading = this.$loading()
      try {
        await this.getFullProcess()
        await this.loadPreorderList(!0)
      }catch (e) {
        e && e !== 'cancel' && console.warn(e)
      }finally {
        loading && loading.clear()
      }
    },
    computed: {
      ifCanChangeCoupon() {
        const d = (this.orderParticulars[0] || {}).boothPreorder || {}
        return d.changeCoupon && d.saleState==1
      },
      detailPayInfo() {
        return (this.orderParticulars[0] || {}).boothPreorder || {}
      },
      detailPayInfoShow() {
        const d = (this.orderParticulars[0] || {}).boothPreorder || {}
        return d.saleState == 3 && !!d.ifOpenPay
      },
      onlinePayShow() {
        const d = (this.orderParticulars[0] || {}).boothPreorder || {}
        return !!d.ifOpenPay && !(+d.orderPayState)
      },
      couponsTip() {
        const d = (this.orderParticulars[0] || {}).boothPreorder || {}
        const disMax = Math.min(+d.discountPrice || 0, d.salePrice);
        if(this.ifCanChangeCoupon) {
          return d.discountPrice ? '已抵扣'+'<span style="color: #ff4444">¥'+(+disMax || 0)+ '</span>' : '可用<span style="color: #ff4444">'+this.couponsNum+'</span>代金券'
        } else {
          return '已抵扣' + '<span style="color: #ff4444">¥'+ (+disMax || 0) + '</span>';
        }
      },
      salePriceTotal() { // 原价
        const data = (this.orderParticulars[0] || {}).exhibitBooths || {}
        if (data.length > 0) {
          const {currencyName, currencySymbol, foreignCurrencySettlementCode, foreignCurrencySettlementSymbol} = data[0]
          return {
            currencyName,
            currencySymbol,
            foreignCurrencySettlementCode,
            foreignCurrencySettlementSymbol,
            transactionPrice: data.map(item => Number(item.salePriceExt)).reduce((a, b) => a + b)
          }
        }
        return {
          transactionPrice: 0
        }
      },
      finalPrice() {
        return ({transactionPrice, currencyName, currencySymbol, foreignCurrencySettlementCode, foreignCurrencySettlementSymbol}) => {
          transactionPrice = transactionPrice || 0
          if (!foreignCurrencySettlementSymbol && foreignCurrencySettlementCode) return transactionPrice + foreignCurrencySettlementCode
          return (foreignCurrencySettlementSymbol ||'') + transactionPrice
          // if (!this.isForeign) return '￥' + transactionPrice
          // if (currencySymbol) return currencySymbol + transactionPrice
          // if (!currencySymbol && currencyName) return transactionPrice + currencyName
          // return '￥' + transactionPrice
        }
      },
      title(){
        switch (this.isParticulars){
          case 1: return '展位预定管理'
          case 2: return '展位订单'
        }
      },
    },
    filters: {
      payPriceTitle(saleState, item) {
        return (
                +(item.boothPreorder.saleState || '')>0 && 4===+(item.boothPreorder.contractSaleState || '')
        ) ? '实付款' :  '需支付';
      },
      saleStateName(name) {
        return booth_preOrder_getOrderStateText(name)
      },
      allTotal(datas) {
        var Total = 0
        $.each(datas, function (key, data) {
          Total = Total + data.salePrice
        })
        return Total
      },
      pcs(data) {
        return data.length
      },
      allArea(datas) {
        var Area = 0
        $.each(datas, function (key, data) {
          Area = Area + data.boothArea
        })
        return Area
      },
    }
  })
  function booth_preOrder_getOrderStateText(ss) {
    const ifFull = app.isFullProcess;
    const orderStates = [
      { // 简化流程
        '-1':"所有状态",
        '1':"已预订",
        // '2':"已签合同",
        // '3':"已到款",
        '3':"已签合同",
        '4':"已到款",
        '0':"订单取消",
      },
      { // 全流程
        '-1':"所有状态",
        '1':"订单生效-已预订",
        '2':"订单生效-已录合同",
        '3':"订单生效-已成交",
        '4':"订单生效",
        '0':"订单取消",
      },
    ];
    // 其他 ''
    return orderStates[ifFull ? 1 : 0][ss+''] || ''
  }
</script>
</body>
</html>
