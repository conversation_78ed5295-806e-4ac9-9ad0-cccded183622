<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<c:set var="v" value="250427" />
	<title>展会基本信息</title>
  <style>
    *{ margin: 0;padding: 0; }
    html,body{
      background-color: #F0F2F5;
      font-family: Microsoft YaHei, Microsoft YaHei-Regular;
      font-size: 16px;
    }
    [v-cloak]{
        display: none;
    }

    .flex{
        display: flex;
    }
    .flex-float{
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    .flex-w{
          flex-direction: row;
        }
        .flex-h{
          flex-direction: column;
        }
    .flex-center-w{
        display: flex;
        justify-content: center;
    }
      .flex-center-h{
          display: flex;
          align-items: center;
      }
    .flex-center{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .flex-between{
        display: flex;
        flex-flow:  row nowrap;
        justify-content: space-between;
    }
  </style>
  <style>
    .my-toolbar-button{
      background-color: #f5f5f5;
      padding: .1875rem .3125rem;}
      li{
      display: block;
      box-sizing: border-box;
      margin: 0 1.25rem .625rem 1.25rem;
      /* padding-bottom: .875rem; */
      overflow: hidden;}
    .red{
      color:#F74444;}
    .upFileDivButton {
        font-style: normal;
        margin: 0 20px 0 10px;
        display: inline-block;
        position: relative;}
    .upFileImgDiv > img, .upFileImgDiv2 > img {
      width: 100%;
      height: 100%;
      display: block;
      overflow: hidden; }
    .upFileDivP {
      color: #666; }
    .my-toolbar-button {
      background-color: #f5f5f5;
      padding: 6px 5px; }
    .upFileDivButton {
      font-style: normal;
      margin: 0 20px 0 10px;
      display: inline-block;
      position: relative; }
    .upFileDivFileButton {
      width: 60px !important; }
    .upFileDivButton > span {
      opacity: 0;
      position: absolute;
      left: 0; }
    .upFileDivP {
      display: inline-block; }
    .upFileImgDiv {
      width: 80px;
      height: 80px;
      border: solid 1px #cccccc;
      margin-top: 10px; }
    .upFileDivButton > em {
      width: 64px;
      height: 30px;
      background-color: #ffffff;
      border-radius: 4px;
      border: solid 1px #b2b2b2;
      color: #333333;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      font-style: normal;
      display: inline-block;
      cursor: pointer; }
      .main-ul input:not([type=checkbox]){
        height: 1.875rem;
        width: 21.875rem;
      }
      .main-ul li>div>label{
        display: block;
        line-height: 2rem;
      }
      .main-ul li>div{
        padding-right: 4.6875rem;
      }
      .hide {
        display: none !important;
      }
  </style>
	<script>var eippath = '${eippath}'</script>
	<script src="${eippath}/js/view/exhibit_baseInfo.js?v=${v}"></script>
</head>
<body>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false">
		<div style="width:100%;height:100%;">
			<div style="position: sticky; top:0; z-index: 999">
				<div class="my-toolbar-button">
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="main.init()" plain="true">刷新</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="main.save()" plain="true">保存</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-import" onclick="main.import()" plain="true">导入历史展会数据</a>
				</div>
			</div>
      <div>
        <ul style="padding: 1.25rem 0;" id="jsf" class="main-ul">
          <li class="flex-float">
              <div>
                  <label><i class="red">* </i>展会名称</label>
                  <input id="jsf-exihibitName" ui="textbox" class="easyui-textbox" data-options="prompt: '请输入展会名称'" >
              </div>
              <div>
                  <label>展会简称</label>
                  <input id="jsf-exhibitAbbr" ui="textbox" class="easyui-textbox">
              </div>
          </li>
          <li class="flex-float enEnable hide">
              <div>
                  <label>展会名称(英文)</label>
                  <input id="jsf-exhibitNameEn" ui="textbox" class="easyui-textbox" data-options="prompt: '请输入展会名称(英文)'" >
              </div>
              <div>
                  <label>展会简称(英文)</label>
                  <input id="jsf-exhibitNameAbbrEn" ui="textbox" class="easyui-textbox">
              </div>
          </li>
          <li class="flex-float">
              <div>
                  <label>行业分类</label>
                  <input id="jsf-tradeId" ui="combotree" class="easyui-combotree" >
              </div>
              <div class="js-enableForeignCurrency hide">
                <label>默认外币</label>
                <input id="jsf-foreignCurrency" ui="combobox" class="easyui-combobox"
							 data-options="valueField:'id',textField:'text',panelHeight:'200px'">
              </div>
          </li>
          <li class="flex-float">
              <div>
                  <label>开始时间</label>
                  <input id="jsf-startDate" ui="datebox" editable="fasle" class="easyui-datebox" >
              </div>
              <div>
                  <label>截止时间</label>
                  <input id="jsf-endDate" ui="datebox" editable="fasle" class="easyui-datebox" >
              </div>
              <div>
                  <label>展会年份</label>
                  <input id="jsf-yearMonth" ui="textbox" class="easyui-textbox" >
              </div>
          </li>
          <li class="flex-float">
              <div>
                  <label>国家/地区</label>
                  <input id="jsf-country" ui="combobox"  class="easyui-combobox"
                    data-options="limitToList:true,valueField:'name',textField:'name',panelHeight:'300px',data:[{name:'中国'}],
                      onChange:function(newData,oldData){
                          loadProvinceSelect(newData);
                      }">
              </div>
              <div class="hide-other-country">
                  <label>省份</label>
                  <input id="jsf-province" ui="combobox" class="easyui-combobox"
                    data-options="limitToList:true,valueField:'f_province',textField:'f_province',panelHeight:'200px',
                      onChange:function(newData,oldData){
                          loadCitySelect(newData);
                      }">
              </div>
              <div class="hide-other-country">
                  <label>城市</label>
                  <input id="jsf-city" ui="combobox" class="easyui-combobox"
                    data-options="limitToList:true,valueField:'f_city_name',textField:'f_city_name',panelHeight:'200px',
                      onChange:function(newData,oldData){
                          //loadDistrictSelect(newData);
                      }">
              </div>
              <div style="display: none" class="show-other-country">
                <label>省份</label>
                <input id="jsf-provinceEx" ui="textbox" class="easyui-textbox" />
              </div>
              <div style="display: none" class="show-other-country">
                <label>城市</label>
                <input id="jsf-cityEx" ui="textbox" class="easyui-textbox" />
              </div>
          </li>
          <li class="flex-float">
              <div>
                  <label>展会地点</label>
                  <input id="jsf-exhibitPlace" ui="textbox" class="easyui-textbox"  style="width:780px;">
              </div>
          </li>
          <li class="flex-float enEnable hide">
              <div>
                  <label>展会地点(英文)</label>
                  <input id="jsf-exhibitPlaceEn" ui="textbox" class="easyui-textbox"  style="width:780px;">
              </div>
          </li>
          <li class="flex-float">
              <div>
                  <label>展会LOGO</label>
                  <div style="height: 30px;margin: 5px 0 10px 0;">
                    <input id="jsf-exhibitLogo" ui="textbox" class="easyui-textbox" data-options="editable:false">
                    <i class="upFileDivButton">
                      <em>上传</em>
                      <input id="upTempLogo" class="easyui-filebox upFileDivFileButton" name="upfile">
                    </i>
                    <i class="upFileDivButton" onclick="$('#jsf-exhibitLogoUrl').attr('src','');$('#jsf-exhibitLogo').textbox('clear');" style="cursor: pointer;">
                      <em>清除</em>
                    </i>
                    <p class="upFileDivP"><span>要求：</span><span style="margin-left: 24px;">要求: png或jpeg格式, 规格: 285*170, 大小不超过300k</span></p>
                  </div>
                  <div style="width:100%;">
                    <image id="jsf-exhibitLogoUrl" ui="image" src="" onerror="this.src = '/eip-web-sponsor/img/error.jpg'" style="width:140px;height:104px" />
                  </div>
              </div>
          </li>
          <li class="flex-float">
              <div>
                <label style="display: none !important;">
                  <input type="checkbox" id="jsf-enableMemberService" ui="checkbox">  已启用会员自助服务
                </label>
                <label>
                  <input type="checkbox" id="jsf-enableVerificationCode" ui="checkbox">  启用万能验证码
                  <span style="margin-left:10px;display: none" id="VerificationCodeWrapper">
                    <input
                        class="easyui-textbox"
                        ui="textbox"
                        id="jsf-verificationCode"
                        data-options="onChange(value, oldValue) {
                          if (!/^\d{4}$/.test(value)) {
                            $(this).textbox('setValue', (oldValue || '').slice(0, 4))
                          }
                        }"
                        style="width: 100px;height: 25px;">
                  </span>
                </label>
              </div>
          </li>
          <li class="flex-float">
            <div>
              <label>展会使用短信验证码通道账号</label>
              <input
                id="jsf-smsAccountId"
                ui="combobox"
                class="easyui-combobox"
                data-options="
                valueField:'id',
                textField:'text',
                panelHeight:'auto',
                panelMaxHeight:'200px',
                limitToList:true,
                url:'/eip-web-sponsor/smsAccount/getSmsAccount',
                loadFilter(data){return (window.BaseInfoSmsAccount = data.data)},
                onChange(value) {
                  main.smsAccountChange(value)
                },
                onLoadSuccess() {
                  main.smsAccountChange(main.data.smsAccountId)
                },
                method: 'post'">
            </div>
            <div>
              <label>短信默认签名</label>
              <input
                id="jsf-smsSign"
                ui="textbox"
                class="easyui-textbox"
                data-options="
                onChange(value) {
                   if (!value) return
                   $(this).textbox('setValue', '【' + value.replace(/[【】]/g, '') +'】')
                }">

            </div>
          </li>
          <li class="flex-float">
            <div>
              <label>展会使用验证码发送邮箱</label>
              <input
                  id="jsf-emailAccountId"
                  ui="combobox"
                  class="easyui-combobox"
                  data-options="
                    valueField:'emailAccountId',
                    textField:'sendEmail',
                    panelHeight:'auto',
                    panelMaxHeight:'200px',
                    limitToList:true,
                    url:'/eip-web-sponsor/emailAccount/getOrgEmailAccount',
                    loadFilter(data){return (window['OrgEmailAccount'] = data.data || [])},
                    method: 'post'">
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<!-- 导入历史展会信息 -->
<style>
  #importSetting-widnwow{
    display:none;
  }
  .importSetting-body li{
    margin: 0;
  }
  .importSetting-body label span.sp{
    height: 33px;
    background: #fafafa;
    padding-right: 20px;
    padding-left: 9px;
    border: 1px solid #cccccc;
    width: 100%;
    line-height: 33px;
    box-sizing: border-box;
    display: block;
  }
  .importSetting-body{
    line-height: 1.5625rem;
  }
  .importSetting-title{
    font-weight: bold;
    border: 1px solid #cccccc;
    line-height: 2rem;
    padding: 0 .5625rem;color: #666;
  }
  .block{
    display: block;
  }
</style>
<div id="importSetting-window" class="easyui-window" title="导入历史展会信息"
 data-options="closed:true,modal:true,width: 440,collapsible:true,minimizable:false,maximizable:false">
  <div style="padding: .1875rem;">
    <div style="height: 33px;">
      <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-save"
      onclick="main.importSettingSave()" plain="true">确定</a>
      <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel"
      onclick="main.close()" plain="true">取消</a>
    </div>
    <div class="importSetting-title">基本信息</div>
    <div class="importSetting-body">
      <ul>
        <li>
          <label style="margin: 10px 0 10px 9px;display: inline-block;">
            <span style="margin-right: 1.875rem;">导入历史展会</span>
            <input type="text" class="es-textbox" readonly style="width: 190px;" id="importSetting-historyCode" onclick="main.selectExhibit(this)">
          </label>
        </li>
      </ul>
    </div>
    <div class="importSetting-title">导入以下信息</div>
    <div class="importSetting-body">
      <ul>
        <li>
          <label>
            <span class="sp">展馆展区、展位相关数据</span>
            <div style="border: 1px solid #ccc">
              <table border="0" style="width: 80%;text-align: right;color: #333;font-size: 14px;line-height: 21px;padding: 7px 0;">
                <tr>
                  <td style="width: 50%">展馆展区设置</td>
                  <td style="width: 50%">展位类型设置</td>
                </tr>
                <tr>
                  <td>展位资料设置</td>
                  <td>展位图</td>
                </tr>
              </table>
            </div>
          </label>
        </li>
        <li style="margin-bottom: 1.25rem;">
          <label>
            <span class="sp">导入展位类型优惠信息
              <input type="checkbox" id="importSetting-importDiscount">
            </span>
            <div>
            </div>
          </label>
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- 选择展会 -->
<%@ include file="../common/select_exhibit.jsp" %>

<!-- 导入历史展会信息 -->
<style>
#importSetting-loading-window{
	display:none;
}
.importSetting-loading-window .progressbar-value{
  background-color: #ebeef5;
}
.importSetting-loading-window .progressbar{
  background-color: white;
  border-color: white;
}
</style>
<div id="importSetting-loading-window" class="easyui-window" title="导入历史展会内容"
 data-options="closed:true,modal:true,width: 500,collapsible:false,minimizable:false,maximizable:false">
  <div style="padding: 1.25rem .625rem;display: flex;align-items: center;flex-direction: column;">
    <div id="importSetting-loading" style="width: 100%;"></div>
    <div id="importSetting-loading-ok" style="width: 100%;height: 33px;display: flex;align-items: center;"> <img src="../../img/ok.svg" alt="" style="padding-right: 10px;"> 历史展会数据导入成功.</div>
    <div id="importSetting-loading-err" style="width: 100%;height: 33px;padding-right: 10px;">历史展会数据导入失败.</div>
  </div>
  <div class="dialog-button messager-button" style="width: 100%;box-sizing: border-box;">
    <a href="javascript:void(0);" class="easyui-linkbutton l-btn l-btn-small"
      onclick="main.close('#importSetting-loading-window')">确定</a>
  </div>
</div>
</body>
</html>