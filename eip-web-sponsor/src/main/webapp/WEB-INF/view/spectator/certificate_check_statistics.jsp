<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../common/select_exhibit_window.jsp" %>
<style>
  #certificate-check-toolbar-search {
      display:flex;
      height: 45px;
      align-items:center;
      flex-wrap: wrap;
  }
  #certificate-check-toolbar-search > div {
      margin-left: 10px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      margin-bottom: 12px;
  }
  #certificate-check-toolbar-search > div:first-of-type {
      margin-left: 0;
  }
  #certificate-check-toolbar-search > div > span {
      margin-right: 5px;
  }
</style>
<div class="datagrid-toolbar" id="certificate-check-statistics-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" plain="true"
       onclick="certificateCheckStatisticsExport()">导出</a>
  </div>
  <div id="certificate-check-toolbar-search" class="my-toolbar-search">
    <div>
      <span>统计口径</span>
      <select
        id="certificate-check-statistics-keyword-statisticsType"
        class="easyui-combobox"
        style="width:150px;height: 28px"
        data-options="panelHeight:'auto',valueField:'id',textField:'text'">
      </select>
    </div>
    <div class="certificate-check-ctrl certificate-check-ctrl4">
      <span>对比展会</span>
      <div style="position: relative;display: inline-block">
        <input readonly id="certificate-check-statistics-keyword-comparisonExhibitCode" class="my-text easyui-textbox" style="width:175px;height:28px">
        <a href="javascript:"
           class="textbox-icon icon-close"
           style="width: 26px; height: 28px;position: absolute;right: 6px;">
        </a>
      </div>
    </div>
    <div class="certificate-check-ctrl certificate-check-ctrl3">
      <span>展馆展区</span>
      <div style="position: relative;display: inline-block">
        <input
            id="certificate-check-statistics-keyword-sectionCode"
            class="easyui-combotree"
            data-options="panelHeight:'auto'"
            style="width:240px;height: 28px;"/>
        <a href="javascript:"
           class="textbox-icon icon-close"
           style="width: 26px; height: 28px;position: absolute;right: 28px;">
        </a>
      </div>
    </div>
    <div>
      <a href="javascript:void(0);" class="easyui-linkbutton" style="width:60px;height:28px" onclick="certificateCheckStatistics(certificateCheckCurrentTabIndex + 1)">查询</a>
    </div>
  </div>
  <div
      class="easyui-tabs"
      id="certificate-check-statistics-tabs"
      style="height:0;border: none;"
      data-options="tabPosition:'top',border: false, plain: true">
    <div title="展区统计"></div>
    <div title="会议区统计"></div>
    <div title="展会总计"></div>
  </div>
</div>
<table id="certificate-check-statistics-datagrid"></table>
<script>
  const certificateCheckStatisticsType = [
    { id: '1', text: '按证件统计'},
    { id: '2', text: '按主子项目统计'},
    { id: '3', text: '按核销点统计'},
    { id: '4', text: '按区域统计'},
  ]

  var certificateCheckCurrentTabIndex = 0

  $(function () {
    $('#certificate-check-statistics-tabs').tabs({
      onSelect(title, index) {
        certificateCheckCurrentTabIndex = index
        certificateCheckSectionCodeReload()
        setTimeout(() => certificateCheckStatistics(index + 1))
      }
    })
    certificateCheckInitEvent()
  })


  function certificateCheckSectionCodeReload(skipStatisticsType) {
    const index = certificateCheckCurrentTabIndex
    const sectionCodeLabel = $('#certificate-check-statistics-keyword-sectionCode').parent().prev()
    const statisticsType = $("#certificate-check-statistics-keyword-statisticsType")
    let text = ''
    let data = certificateCheckStatisticsType.filter(item => item.id !== '2')
    if (index === 0) {
      text = '展馆展区'
      certificateCheckLoadSectionCode()
    } else if (index === 1) {
      text = '会议室'
      certificateCheckLoadMeetingRoom()
    } else if (index === 2) {
      data = certificateCheckStatisticsType.filter(item => item.id === '2' || item.id === '4')
    }
    sectionCodeLabel.text(text)
    if (skipStatisticsType) return
    statisticsType.combobox({data})
    const current = data.find(item => item.id === statisticsType.combobox('getValue'))
    if (!current) {
      statisticsType.combobox('setValue', data[0].id)
    }
  }

  // 加载展馆展区
  function certificateCheckLoadSectionCode() {
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/exhibitSection/makeTreeJson',
      dataType: "json",
      async: false,
      data: {
        exhibitCode: exhibitCode_for_pass
      },
      success(data) {
        $('#certificate-check-statistics-keyword-sectionCode').combotree({
          data,
          valueField: 'sectionCode',
          textField: 'sectionName',
          panelHeight: 'auto',
          panelMaxHeight: "200px",
          limitToList: true
        })
      }
    })
  }

  // 加载会议室
  function certificateCheckLoadMeetingRoom() {
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/meetingRoom/getPage',
      dataType: "json",
      data: {
        exhibitCode: exhibitCode_for_pass
      },
      async: false,
      success(data) {
        const _data = data.rows.map(item => {
          const {meetingRoomCode, meetingRoomName} = item
          return {
            id: meetingRoomCode,
            text: meetingRoomName,
          }
        })
        $('#certificate-check-statistics-keyword-sectionCode').combotree({
          data: _data,
          valueField: 'sectionCode',
          textField: 'sectionName',
          panelHeight: 'auto',
          panelMaxHeight: "200px",
          limitToList: true
        })
      }
    })
  }

  function certificateCheckSelectExhibitCallback(result) {
    if (!result) return
    const {exhibitCode, exhibitName} = result
    $(this).textbox('setValue', exhibitName).data({exhibitCode})
  }

  function certificateCheckInitEvent() {
    const comparisonExhibitCode = $("#certificate-check-statistics-keyword-comparisonExhibitCode")
    const statisticsType = $("#certificate-check-statistics-keyword-statisticsType")
    const sectionCode = $("#certificate-check-statistics-keyword-sectionCode")
    comparisonExhibitCode.textbox({
      events: {click() {openSelectExhibit(certificateCheckSelectExhibitCallback.bind(comparisonExhibitCode))}}
    })
    comparisonExhibitCode.nextAll('a').click(() => comparisonExhibitCode.textbox('clear').removeData('exhibitCode'))
    sectionCode.nextAll('a').click(() => sectionCode.combotree('clear'))
    statisticsType.combobox({
      data: certificateCheckStatisticsType.filter(item => item.id !== '2'),
      onChange(value) {
        const baseClass = '.certificate-check-ctrl'
        $(baseClass).hide()
        $(baseClass + value).show()
        if (+value === 3) certificateCheckSectionCodeReload(true)
      }
    })
    // nextTick
    setTimeout(() => {
      statisticsType.combobox('setValue', '1')
    })
  }

  function certificateCheckStatistics(checkFrom) {
    checkFrom = checkFrom || 1
    const comparisonExhibitCode = $("#certificate-check-statistics-keyword-comparisonExhibitCode").data('exhibitCode')
    const statisticsType = $("#certificate-check-statistics-keyword-statisticsType").combobox('getValue')
    const sectionCode = $("#certificate-check-statistics-keyword-sectionCode").combobox('getValue')
    const data = {
      checkFrom,
      statisticsType,
      exhibitCode: exhibitCode_for_pass
    }
    if (checkFrom === 3 ) delete data.checkFrom
    switch (+statisticsType) {
      case 3:
        data.sectionCode = sectionCode
        break
      case 4:
        data.comparisonExhibitCode = comparisonExhibitCode
        break
    }
    const query = data
    $.messager.progress()
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/certificateCheck/statistics',
      dataType: "json",
      data,
      success({data, rows, state}) {
        if (state !== 1) return $.messager.alert('错误', '数据加载失败', 'error')
        certificateCheckStatisticsLoadTable(data || [], rows || [], query)
        $.messager.progress('close')
      },
      error(err) {
        console.warn(err)
        $.messager.progress('close')
        $.messager.alert('错误', '数据加载失败', 'error')
      }
    })
  }

  function certificateCheckStatisticsLoadTable(_data, rows, query) {
    const $table = $('#certificate-check-statistics-datagrid')
    const isOverview = k => k === '展会总览'
    const {comparisonExhibitCode, statisticsType} = query
    let newFirstExtCol
    const newSecondExtCol = []
    const newThirdExtCol = []
    const isAreaStatistics = +statisticsType === 4
    // 定义二级表头子列，如果是区域统计则为三级表头
    const childrenCols = 'buyerCount,newBuyerCount,checkCount'.split(',')
    const childrenTitles = '人数,新到人数,核销次数'.split(',')
    const makeCol = (date, col) => date + '-' + col

    // 区域统计特殊处理
    if (isAreaStatistics) {
      newFirstExtCol = _data.map(item => ({
        title: item.name,
        colspan:  (isOverview(item.name) ? 2 : 3) * (comparisonExhibitCode ? 2 : 1),
        width: 200,
        align: 'center'
      }))
      // 二级表头
      _data.forEach(item => {
        const {name, dateName, comparisonDateName} = item
        const col = {title: dateName, colspan: isOverview(name) ? 2 : 3, width: 200, align: 'center'}
        newSecondExtCol.push(col)
        if (!comparisonDateName) return
        const col2 = {title: comparisonDateName, colspan: isOverview(name) ? 2 : 3, width: 200, align: 'center'}
        newSecondExtCol.push(col2)
      })
      newSecondExtCol.forEach(({title: date, colspan}) => {
        const cols = colspan === 2 ? [childrenCols[0], childrenCols[2]] : childrenCols
        const titles = colspan === 2 ? [childrenTitles[0], childrenTitles[2]] : childrenTitles
        cols.forEach((col, index) => {
          const title = titles[index]
          const field = makeCol(date, col)
          newThirdExtCol.push({
            title,
            field,
            width: 80,
            align: 'center'
          })
        })
      })
    } else {
      newFirstExtCol = _data.map(title => ({title, colspan: isOverview(title) ? 2 : 3, width: 200, align: 'center'}))
      _data.forEach(date => {
        const cols = isOverview(date) ? [childrenCols[0], childrenCols[2]] : childrenCols
        const titles = isOverview(date) ? [childrenTitles[0], childrenTitles[2]] : childrenTitles
        cols.forEach((col, index) => {
          const title = titles[index]
          const field = makeCol(date, col)
          newSecondExtCol.push({
            title,
            field,
            width: 80,
            align: 'center'
          })
        })
      })
    }


    // 组装一级表头
    const rowspan = isAreaStatistics ? 3 : 2
    const firstCols = [
      {field: 'ck', checkbox: true, rowspan, width: 50},
      {
        field: 'certName',
        formatter(val, row) {
          return row.certificateDel === 1 ? `<del style="color: #A2A2A2">\${val}(已删除)</del>` : val
        },
        title: '类别',
        rowspan,
        width: 160,
        align: 'center'
      }
    ].concat(newFirstExtCol)
    const columns = [
      firstCols,
      newSecondExtCol,
    ]

    if (newThirdExtCol.length) {
      columns.push(newThirdExtCol)
    }
    // 转换数据格式
    // const _matchDateRegex = /[0-9]{4}-[0-9]{2}-[0-9]{2}/
    const inject2Temp = (tmp, val, key) => {
      val = val || {buyerCount: '', newBuyerCount: '', checkCount: ''}
      Object.entries(val).forEach(([k, v]) => tmp[makeCol(key, k)] = v)
    }
    const data = []
    const colsKeys = columns.slice(-2)[0].map(item => item.title)
    rows.forEach(item => {
      const temp = {}
      Object.keys(item).forEach(key => {
        if (colsKeys.indexOf(key) !== -1) {
          inject2Temp(temp, item[key], key)
        } else {
          temp[key] = item[key]
        }
      })
      data.push(temp)
    })


    // 添加合计行
    const summaryCols = []
    _data.forEach(date => childrenCols.forEach(col => summaryCols.push(makeCol(date, col))))
    const _safeNumber = n => +n ? +n : 0
    const summary = data.reduce((prev, curr) => {
      const temp = {}
      summaryCols.forEach(col => {
        temp[col] = _safeNumber(prev[col]) + _safeNumber(curr[col])
      })
      return temp
    }, {})
    //data.push(Object.assign({certName: '<strong>总计</strong>'}, summary))
    // 加载表格
    $table.datagrid({
      rownumbers: false,
      pagination: false,
      fitColumns: true,
      fit: true,
      scrollbarSize: 0,
      columns,
      data,
      toolbar: '#certificate-check-statistics-toolbar'
    })
  }

  function certificateCheckStatisticsExport() {
    const title = $('#certificate-check-statistics-tabs li.tabs-selected span.tabs-title').text()
    const uri = 'data:application/vnd.ms-excel;base64,'
    const template = 'PGh0bWwgeG1sbnM6bz0idXJuOnNjaGVtYXMtbWljcm9zb2Z0LWNvbTpvZmZpY2U6b2ZmaWNlIiB4bWxuczp4PSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOm9mZmljZTpleGNlbCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnL1RSL1JFQy1odG1sNDAiPjxtZXRhIGh0dHAtZXF1aXY9ImNvbnRlbnQtdHlwZSIgY29udGVudD0iYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsOyBjaGFyc2V0PVVURi04Ij48aGVhZD48IS0tW2lmIGd0ZSBtc28gOV0+PHhtbD48eDpFeGNlbFdvcmtib29rPjx4OkV4Y2VsV29ya3NoZWV0cz48eDpFeGNlbFdvcmtzaGVldD48eDpOYW1lPnt3b3Jrc2hlZXR9PC94Ok5hbWU+PHg6V29ya3NoZWV0T3B0aW9ucz48eDpEaXNwbGF5R3JpZGxpbmVzLz48L3g6V29ya3NoZWV0T3B0aW9ucz48L3g6RXhjZWxXb3Jrc2hlZXQ+PC94OkV4Y2VsV29ya3NoZWV0cz48L3g6RXhjZWxXb3JrYm9vaz48L3htbD48IVtlbmRpZl0tLT48L2hlYWQ+PGJvZHk+e3RhYmxlfTwvYm9keT48L2h0bWw+'
    const base64 = s => window.btoa(unescape(encodeURIComponent(s)))
    const format = (s, c) => s.replace(/{(\w+)}/g, (m, p) => c[p])
    const tableClone = $('#certificate-check-statistics-datagrid').prev().clone()
    tableClone.find('td[field="ck"]').remove()
    const ctx = {worksheet: title, table: tableClone.html()}
    const data = base64(format(atob(template), ctx))
    const alink = $('<a style="display:none"></a>').appendTo('body')
    alink[0].href = uri + data
    alink[0].download = '登录情况统计-' + title + '.xls'
    alink[0].click()
    alink.remove()
  }
</script>
