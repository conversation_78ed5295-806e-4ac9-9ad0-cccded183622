<!--进馆证管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
</style>
<table
  id="certificate-check-record-datagrid"
  class="easyui-datagrid"
  data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
          onClickRow: function (rowIndex, rowData) {
            var l = $(this).datagrid('getRows').length;
            for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
 				},"
       toolbar="#certificate-check-record-toolbar"
>
  <thead>
  <tr id="certificate-check-record-datagrid-tr">
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="certCode" width="220" align="center">胸卡号</th>
    <th field="checkFrom" width="150" align="center" formatter="v=>v===1?'展会进场':v===2?'会议签到':v===3?'积分商品核销':''">核销数据来源</th>
    <th field="buyerName" width="150" align="center">姓名</th>
    <th field="regNum" width="120" align="center">登记号码</th>
    <th field="projectName" align="center">项目</th>
    <th field="certName" width="200" align="center" formatter="certificate_check_record_certNameFormatter">证件名称</th>
    <th field="checkPointName" width="150" align="center">核销点</th>
    <th field="checkerName" width="150" align="center">核销人</th>
    <th field="checkTime" width="150" align="center">核销时间</th>
  </tr>
  </thead>

</table>
<div id="certificate-check-record-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:certificate_check_record_delete()" class="easyui-linkbutton" iconcls="icon-remove" plain="true">删除</a>
    <a href="javascript:$('#certificate-check-record-import').window('open')" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导入</a>
    <a href="javascript:certificate_check_record_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;margin-bottom: 18px">胸卡号</span>
    <input id="certificate-check-record-keyword-certCode" class="my-text easyui-textbox" style="width:175px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">核销数据来源</span>
    <select id="certificate-check-record-keyword-checkFrom" class="easyui-combobox" style="width:120px;"
            data-options="panelHeight:'auto'">
      <option selected value="">全部</option>
      <option value="1">展会进场</option>
      <option value="2">会议签到</option>
      <option value="3">积分商品核销</option>
    </select>&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block; ">证件姓名</span>
    <input id="certificate-check-record-keyword-buyerName" class="my-text easyui-textbox"
           style="width:175px;height:25px">&nbsp;&nbsp;
    <%--<span style="display:inline-block; ">登记号码</span>
    <input id="certificate-check-record-keyword-regNum" class="my-text easyui-textbox"
           style="width:175px;height:25px">&nbsp;&nbsp;--%>
    <%--登记号码改为手机和邮箱--%>
    <span style="display:inline-block; ">手机</span>
    <input id="certificate-check-record-keyword-mobile" class="my-text easyui-textbox"
           style="width:175px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block; ">邮箱</span>
    <input id="certificate-check-record-keyword-email" class="my-text easyui-textbox"
           style="width:175px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block; ">项目</span>
    <input
      class="easyui-combobox"
      style="width:175px;height:25px"
      id="certificate-check-record-keyword-projectId"
      data-options="
      valueField:'projectId',
      textField:'projectName',
      panelHeight:'auto',
      panelMaxHeight:'200px',
      limitToList:true,
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode: exhibitCode_for_pass}"/><br>
    <span style="display:inline-block;">证件名称：</span>
    <input
      class="easyui-combobox"
      id="certificate-check-record-keyword-certId"
      data-options="panelHeight:'auto',panelMaxHeight:'200px'"
      style="width:175px;height:25px"/>&nbsp;&nbsp;
    <span style="display:inline-block;">核销点：</span>
    <input
        class="easyui-combobox"
        id="certificate-check-record-keyword-checkPointId"
        style="width: 175px;height:25px"/>&nbsp;&nbsp;
    <span style="display:inline-block;">核销人：</span>
    <input
        class="easyui-combobox"
        id="certificate-check-record-keyword-checkerId"
        style="width: 144px;height:25px"
        data-options="
         valueField:'id',
         textField:'text',
         panelHeight:'200px',
         url:'${eippath}/operator/getAll',
         limitToList:true"/>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">核销时间：</span>
    <input id="certificate-check-record-keyword-checkTimeStart" class="easyui-datetimebox" style="width:175px;height: 25px;"
           data-options="panelHeight:'auto'"/>
    <span>-</span>
    <input id="certificate-check-record-keyword-checkTimeEnd" class="easyui-datetimebox" style="width:175px;height: 25px;"
           data-options="panelHeight:'auto'"/>&nbsp;&nbsp;
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="certificate_check_record_search()">查询</a>
  </div>
</div>
<style>
    .channelButtonStyle{
        color: #fff;
        display: block;
        width: 146px;
        height: 32px;
        background-color: #4fa0ff;
        border-radius: 6px;
        text-align: center;
        line-height: 32px;
        margin: 5px auto 15px;
        font-size: 14px;
        cursor: pointer;
    }
</style>
<div id="certificate-check-record-import" class="easyui-window"
     data-options="closed:true,title:'导入',modal:true,maximizable: false,resizable: false,minimizable: false,cls:'__check-record-window'" style="width:300px;">
  <div  class="easyui-panel"  style="width:100%;height:160px;padding:10px;">
    <span class="channelButtonStyle" style='margin-top: 30px;' onclick="window.open('/eip-web-sponsor/document/证件核销记录导入.xlsx', '_blank')">下载导入模板</span>
    <span class="channelButtonStyle" onclick="$('#certificate-check-record-file').click()">导入</span>
    <input type="file" id="certificate-check-record-file" style="display: none" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
  </div>
</div>

<script>
  var fu_sponsor_delete_query_data = top.checkFunCode('fu_sponsor_delete_query_data');
  $(function () {
    $('.__check-record-window').remove()
    function loadCertificate() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/certificate/getCertificateAndProject',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success({data, state}) {
          if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
          $('#certificate-check-record-keyword-certId').combobox({
            valueField: 'code',
            textField: 'text',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', data)
        }
      })
    }
    function loadCheckpoint() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/getPage',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success({rows, state}) {
          if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
          $('#certificate-check-record-keyword-checkPointId').combobox({
            valueField: 'checkPointId',
            textField: 'checkPointName',
            panelHeight: '260px',
            // panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', rows)
        }
      })
    }
    setTimeout(() => {
      let now = new Date().format('yyyy-MM-dd');
      $('#certificate-check-record-keyword-checkTimeStart').datetimebox("setValue", now + ' 00:00:00')
      // $('#certificate-check-record-keyword-checkTimeEnd').datetimebox("setValue", now + ' 23:59:59')
      certificate_check_record_setCid()
      loadCertificate()
      loadCheckpoint()
      $('#certificate-check-record-file').change(function () {
        if(!this.files.length) return
        certificate_check_record_import(this.files[0])
      })
    }, 0)
  });

  function certificate_check_record_setCid(cid) {
    const certId = cid || '${cid}'
    $('#certificate-check-record-keyword-certCode').textbox("setValue",certId)
    if (certId) {
      $('#certificate-check-record-keyword-checkTimeStart').datetimebox('clear')
      $('#certificate-check-record-keyword-checkTimeEnd').datetimebox('clear')
    }
    certificate_check_record_search()
  }

  function certificate_check_record_search(isExport) {
    const param = {
      exhibitCode: exhibitCode_for_pass,
      certCode: $('#certificate-check-record-keyword-certCode').textbox("getValue"),
      buyerName: $('#certificate-check-record-keyword-buyerName').textbox("getValue"),
      // regNum: $('#certificate-check-record-keyword-regNum').textbox("getValue"),
      // 改成 mobile 和 email
      mobile: $('#certificate-check-record-keyword-mobile').textbox("getValue"),
      email: $('#certificate-check-record-keyword-email').textbox("getValue"),
      projectId: $('#certificate-check-record-keyword-projectId').combobox("getValue"),
      checkPointId: $('#certificate-check-record-keyword-checkPointId').combobox("getValue"),
      checkerId: $('#certificate-check-record-keyword-checkerId').combobox("getValue"),
      checkFrom: $('#certificate-check-record-keyword-checkFrom').combobox("getValue"),
      checkTimeStart: $('#certificate-check-record-keyword-checkTimeStart').datetimebox("getValue"),
      checkTimeEnd: $('#certificate-check-record-keyword-checkTimeEnd').datetimebox("getValue"),
    }
    const certCode = $('#certificate-check-record-keyword-certId').combobox("getValue")
    if (certCode) {
      param.certType = certCode.charAt(0)
      param.certId = certCode.slice(1)
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (isExport){
      const target = new URL('/eip-web-sponsor/certificateCheck/exportData', location.origin)
      target.search = new URLSearchParams(param).toString()
      return window.open(target, '_blank')
    }
    $('#certificate-check-record-datagrid').datagrid({
      url: eippath + '/certificateCheck/getPage',
      queryParams: param,
    })
  }

  function certificate_check_record_import(file) {
    if (!file) return
    $.messager.progress()
    const fd = new FormData()
    fd.append('exhibitCode', exhibitCode_for_pass)
    fd.append('serialNum', Date.now() + '-' + Math.random())
    fd.append('file', file)
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/certificateCheck/importData',
      dataType: "json",
      contentType: false,
      processData: false,
      data: fd,
      success({state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
        certificate_check_record_search()
        $('#certificate-check-record-file').val('')
        $.messager.alert('提示', '操作成功', 'info')
        $.messager.progress('close')
        $('#certificate-check-record-import').window('close')
      },
      error(err) {
        console.warn(err)
        $.messager.alert('导入失败')
        $.messager.progress('close')
      }
    })
  }

  async function certificate_check_record_delete() {
    try {
      const $confirm = (msg, title) => new Promise((resolve, reject) => $.messager.confirm(title || '提醒', msg, f => f ? resolve() : reject('cancel')))
      const $alert = (msg, title, type) => $.messager.alert(title || '提醒', msg, type || 'warning')
      const table = $('#certificate-check-record-datagrid')
      const rows = table.datagrid('getSelections')
      const total = table.datagrid('getPager').data("pagination").options.total
      const lastQueryData = table.datagrid('options').queryParams
      const isAll = rows.length === 0
      if (isAll && !fu_sponsor_delete_query_data) {
        return $alert('未选中数据', '提醒')
      }
      const delSize = isAll ? total : rows.length
      if (delSize < 1) return $alert('没有可操作的数据', '提示')
      const post = {
        exhibitCode: exhibitCode_for_pass,
        operEntrance: isAll ? '批量删除全部票证核销记录' : '批量删除票证核销记录',
      }
      if (isAll) {
        const paramsLength = Object.values(lastQueryData).filter(item => item !== '' && item !== null && item !== void 0).length
        if (paramsLength <= 1) return $alert('请筛选或选中部分要删除的数据', '提醒')
        if (delSize > 5e4) return $alert('筛选结果超过5万条，不允许删除，请检查确认', '提醒')
        if (delSize >= 1e4) await $confirm('将删除的记录超过一万条，可能速度较慢，确认要删除么？', '提醒')
        await $confirm(
          `<span>未选中数据，\n即将对查询结果共 <b style="color: #00f">\${delSize}</b> 条数据全部删除,\n是否继续？</span>`,
          '提醒')
        Object.assign(post, lastQueryData)
        for (const key in post) {
          if(post[key] === null) delete post[key]
        }
      } else {
        post.certificateCheckIds = rows.map(item => item.certificateCheckId).toString()
      }
      await $confirm(`此操作不可逆!当前选择的数量为: \${delSize}，确定删除吗?`, '提醒')
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/certificateCheck/batchDelete',
        dataType: "json",
        data: post,
        success(data) {
          if (data.state !== 1) return $alert(data.msg || '删除失败', '提示', 'error')
          $alert('操作成功', '提示', 'info')
          certificate_check_record_search()
        },
        error(e) {
          console.warn(e)
          $alert('操作成功', '提示', 'error')
        }
      })
    } catch (e) {
      if ('exit,close,cancel'.split(',').indexOf(e) !== -1) return
      console.warn(e)
    }
  }

  function certificate_check_record_certNameFormatter(val, row) {
    return row.certificateDel === 1 ? `<del style="color: #A2A2A2">\${val}(已删除)</del>` : val
  }
</script>
