<!-- 观众财务汇总 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="231102"/>
<title>观众财务汇总</title>
  <link rel="stylesheet" href="../../css/view/buyer_finance_summary.css?v=${v}">
  <script>var eippath = '${eippath}';</script>
  <script src="../../js/datagrid-export.js"></script>
  <script src="../../js/view/buyer_finance_summary.js?v=${v}" async></script>
</head>
<body>

<!-- 主页面 -->
<style>
  #main{
    display: none;
  }
  #app-search label {
    padding-left: 5px;
    padding-right: 15px;
  }
  /* .app-datagrid {
      height: calc(100% - 40px) !important;
  } */
  .app-datagrid .datagrid-header-row .datagrid-cell>span {
      white-space: normal !important;
      word-wrap: normal !important;
  }
  .app-datagrid .datagrid-body .sumTable td{
      border-width: 1px 1px 0 0;
  }
  .app-datagrid .datagrid-body{
      position: relative;
  }
  .app-datagrid .datagrid-row-selected td{
      background-color: #ffe48d !important;
  }
</style>
<div class="easyui-layout app" data-options="fit:true">
  <div data-options="region:'center',border:false" id="main">
    <div style="height: 100%;width: 100%;display: flex;flex-direction: column;">
      <div id="app-toolbar" style="background: #F4F4F4;">
        <div class="my-toolbar-button">
          <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-reload"
          onclick="app.fresh()" plain="true">刷新</a>
          <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-view"
              onclick="app.export()"  plain="true">导出</a>
        </div>
        <form id="app-search" class="my-toolbar-search my-toolbar-search-p">
          <label>
            观众姓名&nbsp;
            <input id="app-search-name" class="easyui-textbox" style="width:170px;height:25px">
          </label>
          <label>
            会员/手机/邮箱&nbsp;
            <input id="app-search-userName" class="easyui-textbox" style="width:170px;height:25px">
          </label>
          <label>
            订单号&nbsp;
            <input id="app-search-orderId" class="easyui-textbox" style="width:170px;height:25px">
          </label>
          <label>
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
             onclick="app.search()">查询</a>
          </label>
        </form>
      </div>
      <div style="flex: 1">
        <table id="app-datagrid" class="easyui-datagrid"></table>
      </div>
      <div id="app-pager" class="easyui-pagination" style="background:#efefef;border:1px solid #ccc;"></div>
    </div>
  </div>
</div>
</body>
</html>