<!--进馆证管理  -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/project.jsp" %>
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
			fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
			fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
			fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
			fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
			fu_reg_pay_order_refund = false,
			fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
		else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
		else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
		else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
		else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
		else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
		else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
		else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
		else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
		else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
		else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
		else if(s.equals("fu_reg_pay_order_refund"))fu_reg_pay_order_refund = true;
	}
%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style scoped="scoped">
	.tb{
		width:100%;
		margin:0;
		padding:5px 4px;
		border:1px solid #ccc;
		box-sizing:border-box;
	}
</style>
<link rel="stylesheet" href="${eippath}/css/view/reg_pay_order.css">
<table id="reg-payment-datagrid" class="easyui-datagrid"
			 data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},"
			 toolbar="#reg-payment-toolbar"
>
	<thead>
	<tr id="reg-payment-datagrid-tr">
		<th data-options="field:'ck',checkbox:true"></th>
		<th field="projectName" width="220" align="center" >项目</th>
		<th field="buyerName" width="150" align="center">姓名</th>
		<th field="regNum" width="150" align="center">登记号码</th>
		<th field="barCode" width="150" align="center">胸卡号</th>
		<th field="orderTradeId" width="150" align="center" formatter="(v,r) => `<a style=color:#00f href=javascript:reg_payment_openOrderDetail('\${v}','\${r.projectId}')>\${v}</a>`">订单ID</th>
		<th field="totalFree" width="120" align="center">付款金额</th>
		<th field="orderPayState" width="120" align="center" formatter="v => reg_payment_orderPayStateFormat(v)">支付状态</th>
		<th field="createTime" width="150" align="center">订单创建时间</th>
		<th field="timeEnd" width="150" align="center">订单完成时间</th>
		<th field="transactionId"  align="center">微信订单ID</th>
	</tr>
	</thead>

</table>
<div id="reg-payment-toolbar">
	<div class="my-toolbar-button">
		<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
		<a href="javascript:void(0);" style="display: none" class="easyui-linkbutton fu_reg_pay_order_refund" iconCls="icon-refund" plain="true" onclick="reg_payment_refund_batch()">退款</a>
		<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download" plain="true" onclick="reg_payment_search(true)">导出</a>
	</div>
	<div class="my-toolbar-search">
		<div style="display: inline-block;">
			<style>
				#reg-payment-keyword-projectId+.combo{
					margin-right: 0 !important;
				}
			</style>
			<label>登记项目：</label>
			<div style="position: relative;display: inline-block;margin-right: 10px;">
				<select id="reg-payment-keyword-projectId" class="easyui-combotree" style="width:175px;height:25px;"></select>
				<a href="javascript:;" onclick="$('#reg-payment-keyword-projectId').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
			</div>
			<script>
				$(function(){
					$('#reg-payment-keyword-projectId').combotree({
						url: eippath + '/project/selectSubProject?parentId=' + project_for_pass,
						loadFilter(data) {
							const tmp = data.data;
							if(!tmp[0].children || !tmp[0].children.length){
								$('#reg-payment-keyword-projectId').parent().hide()
										.prev('label').hide();
							}
							return tmp
						},
						async: false,
						onLoadError(e) {
							$.messager.alert('子项目加载失败！')
						},
					});
				})
			</script>
		</div>&nbsp;&nbsp;
		<span style="display:inline-block;margin-bottom: 18px">姓名：</span>
		<input id="reg-payment-keyword-buyerName" class="my-text easyui-textbox" style="width:100px;height:25px">&nbsp;&nbsp;
		<span style="display:inline-block; ">登记号码：</span>
		<input id="reg-payment-keyword-regNum" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;
		<span style="display:inline-block; ">胸卡号：</span>
		<input id="reg-payment-keyword-barCode" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;
		<span style="display:inline-block; ">订单ID：</span>
		<input id="reg-payment-keyword-orderTradeIdLike" class="my-text easyui-textbox" style="width:120px;height:25px"><br>
		<span style="display:inline-block;">订单状态：</span>
		<select id="reg-payment-keyword-orderPayState" class="easyui-combobox" style="width:100px;" data-options="panelHeight:'auto'">
			<option value="">全部</option>
			<option value="0">未支付</option>
			<option value="1">已支付</option>
			<option value="2">取消支付</option>
			<option value="3">已退款</option>
		</select>&nbsp;&nbsp;
		<span style="display:inline-block;margin-bottom: 18px">订单创建时间：</span>
		<input id="reg-payment-keyword-orderCreateTimeStart" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>
		<span>-</span>
		<input id="reg-payment-keyword-orderCreateTimeEnd" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
		<span style="display:inline-block; ">支付完成时间：</span>
		<input id="reg-payment-keyword-orderTimeEndStart" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>
		<span>-</span>
		<input id="reg-payment-keyword-orderTimeEndEnd" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
		<span style="display:inline-block; ">微信订单ID：</span>
		<input id="reg-payment-keyword-transactionId" class="my-text easyui-textbox" style="width:120px;height:25px">
		<a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="reg_payment_search()">查询</a>
	</div>
</div>

<div id="reg-payment-details" class="easyui-dialog" title="观众支付订单" data-options="modal:true,shadow:false,closed:true"
		 style="width:840px">
	<div class="my-toolbar-button">
		<a href="javascript:void(0);"
			 class="easyui-linkbutton fu_reg_pay_order_refund"
			 iconCls="icon-refund"
			 style="display: none"
			 onclick="reg_payment_refund()"
			 plain="true"
			 style="margin-right: 6px;">退款</a>
		<a href="javascript:void(0);"
			 class="easyui-linkbutton"
			 iconCls="icon-wechat"
			 onclick="reg_payment_queryWxPayOrRefundState()"
			 plain="true"
			 style="margin-right: 6px;">查询微信支付状态</a>
	</div>
	<div id="reg-payment-details-order-panel" class="easyui-panel" style="width: 100%" title="订单信息">
		<div class="alex-form">
			<div class="alex-form-item full">
				<span class="form-item__label">项目</span>
				<span class="form-item__value" key="projectName"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">订单编号</span>
				<span class="form-item__value" key="orderTradeId"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">订单状态</span>
				<span class="form-item__value" key="orderPayState"></span>
			</div>
			<div class="alex-form-item full">
				<span class="form-item__label">订单创建时间</span>
				<span class="form-item__value" key="createTime"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">登记姓名</span>
				<span class="form-item__value" key="buyerName"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">登记号码</span>
				<span class="form-item__value" key="regNum"></span>
			</div>
			<div class="alex-form-item full">
				<span class="form-item__label">胸卡号</span>
				<span class="form-item__value" key="barCode"></span>
			</div>
			<div class="alex-form-item full">
				<span class="form-item__label">付款金额</span>
				<span class="form-item__value" key="totalFree"></span>
			</div>
		</div>
	</div>
	<div id="reg-payment-details-wechat-panel" class="easyui-panel" style="width: 100%;display: none" title="微信支付信息">
		<div class="alex-form">
			<div class="alex-form-item full">
				<span class="form-item__label">微信订单ID</span>
				<span class="form-item__value" key="orderId"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">交易状态</span>
				<span class="form-item__value" key="stateName"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">支付完成时间</span>
				<span class="form-item__value" key="payTime"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">退款时间</span>
				<span class="form-item__value" key="refundTime"></span>
			</div>
		</div>
	</div>
</div>
<script>
	$(function () {
		window.fu_reg_pay_order_refund = <%=fu_reg_pay_order_refund%>;
		setTimeout(() => {
			reg_payment_search()
			$('.fu_reg_pay_order_refund')[window.fu_reg_pay_order_refund ? 'show' : 'hide']()
		}, 1e2)
	});
	$("#reg-payment-keyword-buyerName").keypress(e => e.which == 13 && reg_payment_search())
	$("#reg-payment-keyword-regNum").keypress(e => e.which == 13 && reg_payment_search())
	$("#reg-payment-keyword-barCode").keypress(e => e.which == 13 && reg_payment_search())
	$("#reg-payment-keyword-orderTradeIdLike").keypress(e => e.which == 13 && reg_payment_search())
	$("#reg-payment-keyword-transactionId").keypress(e => e.which == 13 && reg_payment_search())

	function reg_payment_search(isExport){
		const param = {
			exhibitCode:  exhibitCode_for_pass,
			projectId:  $('#reg-payment-keyword-projectId').combotree('getValue'),
			buyerName:  $('#reg-payment-keyword-buyerName').textbox("getValue"),
			regNum:  $('#reg-payment-keyword-regNum').textbox("getValue"),
			barCode:  $('#reg-payment-keyword-barCode').textbox("getValue"),
			orderTradeIdLike:  $('#reg-payment-keyword-orderTradeIdLike').textbox("getValue"),
			orderPayState:  $('#reg-payment-keyword-orderPayState').combobox("getValue"),
			transactionId:  $('#reg-payment-keyword-transactionId').textbox("getValue"),
			orderCreateTimeStart:  $('#reg-payment-keyword-orderCreateTimeStart').datebox("getValue"),
			orderCreateTimeEnd:  $('#reg-payment-keyword-orderCreateTimeEnd').datebox("getValue"),
			orderTimeEndStart:  $('#reg-payment-keyword-orderTimeEndStart').datebox("getValue"),
			orderTimeEndEnd:  $('#reg-payment-keyword-orderTimeEndEnd').datebox("getValue"),
		}
		Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
		if (isExport) return window.open(eippath + '/order/exportBuyerOrder?' + new URLSearchParams(param).toString())
		$('#reg-payment-datagrid').datagrid({
			url: eippath +'/order/getBuyerOrderList',
			queryParams: param,
		})
	}
	function reg_payment_refund(orderTradeId, projectId){
		const $details = $('#reg-payment-details')
		orderTradeId = orderTradeId || $details.attr('orderTradeId')
		projectId = projectId || $details.attr('projectId')
		if (!orderTradeId || !projectId)
			return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
		const refund = (orderTradeId) => {
			$.messager.progress()
			$.ajax({
				url: eippath + '/refundOrder/refund',
				dataType: "json",
				type: "post",
				data: {projectId, orderTradeId},
				success(res){
					let message
					switch (res.state) {
						case 1:
							message = '操作成功！'
							break
						default:
							message = '数据加载失败！'
					}
					$.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
					$.messager.progress('close')
					reg_payment_search()
				},
				error(){
					$.messager.alert('提示','数据加载失败！','error')
					$.messager.progress('close')
				}
			})
		}
		refund(orderTradeId)
	}
	function reg_payment_refund_batch() {
		const rows = $('#reg-payment-datagrid').datagrid('getSelections')
		const refund = (orderTradeIds, projectIds) => {
			$.messager.progress()
			orderTradeIds = orderTradeIds || ''
			$.ajax({
				url: eippath + '/refundOrder/batchRefund',
				dataType: "json",
				type: "post",
				data: {projectIds, orderTradeIds},
				success(res){
					let message
					switch (res.state) {
						case 1:
							message = '操作成功！'
							break
						default:
							message = '数据加载失败！'
					}
					$.messager.alert('提示', message, res.state === 1 ? 'info' : 'error')
					$.messager.progress('close')
					reg_payment_search()
				},
				error(){
					$.messager.alert('提示','数据加载失败！','error')
					$.messager.progress('close')
				}
			})
		}
		if (!rows.length) return $.messager.alert('提示', '没有需要操作的数据！', 'warning')
		const ids = rows.map(row=>row['orderTradeId']).toString()
		const pids = [... new Set(rows.map(row=>row['projectId']))].toString()
		if (rows.some(({orderPayState}) => +orderPayState > 1 || !(+orderPayState))) {
			return $.messager.confirm('提示', '已选择项内包含 未支付、已退款 状态订单, 请确认是否对此类订单发送退款申请？', r => r && refund(ids, pids))
		}
		const total = rows.reduce((prev, next) => prev + next['totalFree'] ,0)
		$.messager.confirm('提示', `已选择了\${rows.length}个订单，即将退款￥\${total}，是否确认退款？`, r => r && refund(ids, pids))
	}
	function reg_payment_queryWxPayOrRefundState(orderTradeId){
		orderTradeId = orderTradeId || $('#reg-payment-details').attr('orderTradeId')
		if (!orderTradeId)
			return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
		$.messager.progress()
		$.ajax({
			url: eippath + '/weChat/pay/queryWxPayOrRefundState',
			dataType: "json",
			type: "post",
			data: {orderTradeId},
			success(res){
				let message
				switch (res.state) {
					case 1:
						message = '已更新微信支付信息！'
						break
					default:
						message = '数据加载失败！'
				}
				$.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
				$.messager.progress('close')
				const data = res.data
				if (!data) return
				const wrapper = $('#reg-payment-details-wechat-panel')
				Object.keys(data).forEach(key => {
					const el = wrapper.find(`[key="\${key}"]`)
					if(!el.length) return
					el.text(data[key] || '')
				})
				wrapper.panel({closed: false})
			},
			error(){
				$.messager.alert('提示','数据加载失败！','error')
				$.messager.progress('close')
			}
		})
	}
	function reg_payment_orderPayStateFormat(orderPayState){
		const OrderStatusMap =  {
			Unpaid: '<span style="color:#A2A2A2">未支付</span>',
			Paid: '<span style="color:#009900">已支付</span>',
			Cancelled: '<span style="color:#A2A2A2">超时取消</span>',
			Refunded: '<span style="color:#FF6C32">已退款</span>',
		}
		return Object.values(OrderStatusMap)[+orderPayState]
	}
	function reg_payment_openOrderDetail(orderTradeId, projectId) {
		if (!orderTradeId)
			return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
		const currentOrder = $('#reg-payment-datagrid').datagrid('getData').rows.find(({orderTradeId: id}) => id === orderTradeId)
		if(!currentOrder)return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
		Object.keys(currentOrder).forEach(key => {
			const el = $('#reg-payment-details-order-panel').find(`[key="\${key}"]`)
			if(!el.length) return
			const value = currentOrder[key]
			if(key === 'orderPayState')
				el.html(reg_payment_orderPayStateFormat(value))
			else el.text(value || '')
		})
		$('#reg-payment-details-wechat-panel').panel({closed: true})
		$('#reg-payment-details').window('open').attr({orderTradeId, projectId})
	}

</script>
