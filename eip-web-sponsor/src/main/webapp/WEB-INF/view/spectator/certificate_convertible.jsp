<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
</style>
<table
    id="certificate-convertible-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},"
    toolbar="#certificate-convertible-toolbar"
>
  <thead>
  <tr id="certificate-convertible-datagrid-tr">
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="personName" width="120" align="center">姓名</th>
    <th field="age" width="80" align="center">年龄</th>
    <th field="sex" width="80" align="center">性别</th>
    <th field="companyName" width="200" align="center">工作单位</th>
    <th field="jobTitle" width="120" align="center">职称</th>
    <th field="fromBasic" width="80" align="center" formatter="v=>v===true?'是':v===false?'否':''">来自基层</th>
    <th field="companyAddress" width="120" align="center">单位所在地</th>
    <th field="address" width="200" align="center">地址</th>
    <th field="phone" width="120" align="center">联系电话</th>
    <th field="email" width="200" align="center">邮箱</th>
    <th field="projectName" width="200" align="center">项目</th>
    <th field="buyerCertificateId" width="220" align="center"
        formatter="certificateConvertibleBuyerCertificateIdFormatter">证件号
    </th>
    <th field="certificateName" width="200" align="center" formatter="certificateConvertibleCertNameFormatter">证件名称</th>
    <th field="isConvertible" width="100" align="center" formatter="v=>v?'已兑换':'未兑换'">是否兑换</th>
    <th field="convertibleTime" width="150" align="center">兑换时间</th>
    <th field="buyerName" width="120" align="center">证件所属人</th>
    <th field="regNum" width="180" align="center">所属人登记号码</th>
  </tr>
  </thead>
</table>
<div id="certificate-convertible-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:certificate_convertible_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;margin-bottom: 18px;">姓名：</span>
    <input id="certificate-convertible-keyword-personName" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px;">工作单位：</span>
    <input id="certificate-convertible-keyword-companyName" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;">是否基层：</span>
    <select
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true"
        id="certificate-convertible-keyword-fromBasic"
        style="width: 120px;height:25px">
      <option value="" selected>全部</option>
      <option value="true">是</option>
      <option value="false">否</option>
    </select>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px;">电话：</span>
    <input id="certificate-convertible-keyword-phone" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;">是否兑换：</span>
    <select
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true"
        id="certificate-convertible-keyword-isConvertible"
        style="width: 120px;height:25px">
      <option value="" selected>全部</option>
      <option value="true">已兑换</option>
      <option value="false">未兑换</option>
    </select><br/>
    <span style="display:inline-block; ">项目：</span>
    <input
        class="easyui-combobox"
        style="width:175px;height:25px"
        id="certificate-convertible-keyword-projectId"
        data-options="
      valueField:'projectId',
      textField:'projectName',
      panelHeight:'auto',
      panelMaxHeight:'200px',
      limitToList:true,
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode: exhibitCode_for_pass}"/>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px;">证件名称：</span>
    <input class="easyui-combobox"
           id="certificate-convertible-keyword-certificateId"
           style="width:175px;height:25px"/>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">证件ID：</span>
    <input id="certificate-convertible-keyword-buyerCertificateIdStr" class="my-text easyui-textbox"
           style="width:150px;height:25px"><br/>
    <span style="display:inline-block;margin-bottom: 18px;">证件所属人：</span>
    <input id="certificate-convertible-keyword-buyerName" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <%--<span style="display:inline-block; ">所属人登记号码：</span>
    <input id="certificate-convertible-keyword-regNum" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;--%>
    <%--改成所属人手机和所属人邮箱--%>
    <span style="display:inline-block;margin-bottom: 18px">所属人手机：</span>
    <input id="certificate-convertible-keyword-buyerMobile" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">所属人邮箱：</span>
    <input id="certificate-convertible-keyword-buyerEmail" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">订单购买人：</span>
    <input id="certificate-convertible-keyword-orderBuyerName" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <%--span style="display:inline-block;margin-bottom: 18px">购买人登记号码：</span>
    <input id="certificate-convertible-keyword-orderRegNum" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;--%>
    <%--改购买人手机和购买人邮箱--%>
    <span style="display:inline-block;margin-bottom: 18px">购买人手机：</span>
    <input id="certificate-convertible-keyword-orderMobile" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">购买人邮箱：</span>
    <input id="certificate-convertible-keyword-orderEmail" class="my-text easyui-textbox"
            style="width: 150px;height: 25px;">&nbsp;&nbsp;
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="certificate_convertible_search()">查询</a>
  </div>
</div>

<script>
  $(function () {
    function loadCertificate() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/certificate/getCertificateAndProject',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass,
          onlyCertificate: true
        },
        success({data, state}) {
          if (state !== 1) return $.messager.alert('错误', '证件数据！', 'error')
          $('#certificate-convertible-keyword-certificateId').combobox({
            valueField: 'longId',
            textField: 'text',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', data)
          certificate_convertible_setProps()
        },
        error() {
          certificate_convertible_setProps()
        }
      })
    }

    function loadCheckpoint() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/getPage',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success({rows, state}) {
          if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
          $('#certificate-convertible-keyword-checkPointId').combobox({
            valueField: 'checkPointId',
            textField: 'checkPointName',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', rows)
        }
      })
    }

    setTimeout(() => {
      loadCertificate()
      loadCheckpoint()
    }, 1e2)
  });

  function certificate_convertible_setProps(orderTradeIdStr, certificateId) {
    $('#certificate-convertible-keyword-orderTradeIdStr').textbox("setValue", orderTradeIdStr || '${orderTradeIdStr}')
    $('#certificate-convertible-keyword-certificateId').combobox("setValue", certificateId || '${certificateId}')
    certificate_convertible_search()
  }

  function certificate_convertible_search(isExport) {
    const _getVal = (key, opt = 'textbox') => $('#certificate-convertible-keyword-' + key)[opt]('getValue')
    const queryParams = {
      exhibitCode: exhibitCode_for_pass,
      personName: _getVal('personName'),
      companyName: _getVal('companyName'),
      phone: _getVal('phone'),
      buyerCertificateIdStr: _getVal('buyerCertificateIdStr'),
      buyerName: _getVal('buyerName'),
      // regNum: _getVal('regNum'),
      buyerMobile: _getVal('buyerMobile'),
      buyerEmail: _getVal('buyerEmail'),
      orderMobile: _getVal('orderMobile'),
      orderEmail: _getVal('orderEmail'),
      orderBuyerName: _getVal('orderBuyerName'),
      // orderRegNum: _getVal('orderRegNum'),
      certificateId: _getVal('certificateId', 'combobox'),
      isConvertible: _getVal('isConvertible', 'combobox'),
      fromBasic: _getVal('fromBasic', 'combobox'),
      projectId: _getVal('projectId', 'combobox'),
    }
    Object.keys(queryParams).forEach(key => '' === queryParams[key] && (delete queryParams[key]))
    if (isExport) {
      const target = new URL('/eip-web-sponsor/certificateConvertible/exportData', location.origin)
      target.search = new URLSearchParams(queryParams).toString()
      return window.open(target, '_blank')
    }
    $('#certificate-convertible-datagrid').datagrid({
      url: '/eip-web-sponsor/certificateConvertible/getPage',
      queryParams
    })
  }


  function certificateConvertibleBuyerCertificateIdFormatter(val, row) {
    return `<a href="javascript:" style="color: #2E82E4;font-weight:bold;" onclick="certificateConvertibleDumpManage('\${row.buyerCertificateId}')">\${val}</a>`
  }

  function certificateConvertibleDumpManage(bcid) {
    const result = addTab('票证管理', '/eip-web-sponsor/backstage/spectator/certificate_management?bcid=' + bcid)
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_management_setProps(null, null, bcid)
  }

  function certificateConvertibleCertNameFormatter(val, row) {
    return row.certificateDel === 1 ? `<del style="color: #A2A2A2">\${val}(已删除)</del>` : val
  }
</script>
