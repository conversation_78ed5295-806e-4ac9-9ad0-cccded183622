<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
</style>
<table
    id="certificate-push-record-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
          onClickRow: function (rowIndex, rowData) {
            var l = $(this).datagrid('getRows').length;
            for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
 				},"
    toolbar="#certificate-push-record-toolbar"
>
  <thead>
  <tr id="certificate-push-record-datagrid-tr">
    <%--<th data-options="field:'ck',checkbox:true"></th>--%>
    <th field="projectName" width="140" align="center">项目</th>
    <th field="certType" width="140" align="center" formatter="v => certificate_push_certTypeNameMap[v]||''">推送类型</th>
    <th field="certName" width="140" align="center" formatter="certificate_push_certNameFormatter">推送证件</th>
    <th field="barCode" width="220" align="center">胸卡号</th>
    <th field="apiTypeName" width="150" align="center">第三方服务商</th>
    <th field="createTime" width="150" align="center">推送时间</th>
    <th field="pushState" width="120" align="center" formatter="v => v === 1 ? '推送成功': '推送失败'">推送结果</th>
    <th field="responseData" width="300" align="center">推送备注</th>
  </tr>
  </thead>

</table>
<div id="certificate-push-record-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:certificate_push_record_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;">项目</span>
    <input
        class="easyui-combobox"
        style="width:175px;height:25px"
        id="certificate-push-record-keyword-projectId"
        data-options="
      valueField:'projectId',
      textField:'projectName',
      panelHeight:'auto',
      panelMaxHeight:'200px',
      limitToList:true,
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode: exhibitCode_for_pass}"/>
    &nbsp;&nbsp;

    <span style="display:inline-block;margin-bottom: 18px">推送类型</span>
    <select id="certificate-push-record-keyword-certType" class="easyui-combobox" style="width: 120px;height:25px"
            data-options="panelHeight:'auto'">
      <option selected value="">全部</option>
      <option value="A">观众门票</option>
      <option value="C">会议证件</option>
      <option value="E">进馆证</option>
    </select>
    &nbsp;&nbsp;

    <span style="display:inline-block;margin-bottom: 18px">胸卡号</span>
    <input id="certificate-push-record-keyword-barCode" class="my-text easyui-textbox" style="width:175px;height:25px">&nbsp;&nbsp;
    <br/>

    <span style="display:inline-block;margin-bottom: 18px">第三方服务商</span>
    <input
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true,
          url: '/eip-web-sponsor/pushApi/getPushApiType',method: 'post',
          valueField:'code',textField:'text',
          panelMaxHeight:'200px',limitToList:true,
          queryParams:{exhibitCode:exhibitCode_for_pass, dataType: certificate_push_certType},
          loadFilter(data) {
            const ret = data.data
            ret.unshift({code:'any',text:'任意一个'})
            ret.unshift({code:'',text:'全部'})
            return ret
          }"
        id="certificate-push-record-keyword-apiType"
        style="width: 120px;height:25px"/>&nbsp;&nbsp;

    <span style="display:inline-block;margin-bottom: 18px">推送时间段：</span>
    <input id="certificate-push-record-keyword-pushTimeStart" class="easyui-datetimebox"
           style="width:175px;height: 25px;"
           data-options="panelHeight:'auto'"/>
    <span>-</span>
    <input id="certificate-push-record-keyword-pushTimeEnd" class="easyui-datetimebox"
           style="width:175px;height: 25px;"
           data-options="panelHeight:'auto'"/>&nbsp;&nbsp;

    <span style="display:inline-block;margin-bottom: 18px">推送结果</span>
    <select
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true"
        id="certificate-push-record-keyword-pushState"
        style="width: 150px;height:25px">
      <option value="" selected>全部</option>
      <%--<option value="-1">未推送</option>--%>
      <option value="0">推送失败</option>
      <option value="1">推送成功</option>
    </select>
    <input type="hidden" id="certificate-push-record-keyword-tableId">

    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="certificate_push_record_search()">查询</a>
  </div>
</div>

<script>
  var certificate_push_certTypeMap = {
    A: 'buyer',
    E: 'cert',
  }
  var certificate_push_certTypeNameMap = {
    A: '观众门票',
    C: '会议证件',
    E: '进馆证',
  }
  var certificate_push_certType = certificate_push_certTypeMap.A
  $(function () {
    $('.__check-record-window').remove()
    setTimeout(certificate_push_setProps)
  });

  function certificate_push_setProps() {
    const {apiType, tableId, certType} = top.__TEMP_CERT_PUSH_RECORD || {}
    delete top.__TEMP_CERT_PUSH_RECORD
    if (tableId) {
      $('#certificate-push-record-keyword-tableId').val(tableId)
    } else {
      $('#certificate-push-record-keyword-pushTimeStart').datetimebox("setValue", new Date().format('yyyy-MM-dd') + ' 00:00:00')
    }
    if (certType) {
      certificate_push_certType = certificate_push_certTypeMap[certType]
      $('#certificate-push-record-keyword-certType').combobox('setValue', certType)
    }
    if (apiType) {
      $('#certificate-push-record-keyword-apiType').combobox('setValue', apiType)
    }
    certificate_push_record_search()
  }

  function certificate_push_record_search(isExport) {
    const $tableId = $('#certificate-push-record-keyword-tableId')
    const param = {
      exhibitCode: exhibitCode_for_pass,
      projectId: $('#certificate-push-record-keyword-projectId').combobox("getValue"),
      certType: $('#certificate-push-record-keyword-certType').combobox("getValue"),
      barCode: $('#certificate-push-record-keyword-barCode').textbox("getValue"),
      apiType: $('#certificate-push-record-keyword-apiType').combobox("getValue"),
      pushTimeStart: $('#certificate-push-record-keyword-pushTimeStart').datetimebox("getValue"),
      pushTimeEnd: $('#certificate-push-record-keyword-pushTimeEnd').datetimebox("getValue"),
      pushState: $('#certificate-push-record-keyword-pushState').combobox("getValue"),
      tableId: $tableId.val(),
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (isExport) {
      const target = new URL('/eip-web-sponsor/pushLog/exportData', location.origin)
      target.search = new URLSearchParams(param).toString()
      return window.open(target, '_blank')
    }
    $('#certificate-push-record-datagrid').datagrid({
      url: eippath + '/pushLog/getPage',
      queryParams: param,
      onLoadSuccess() {
        $tableId.val('')
      }
    })
  }

  function certificate_push_certNameFormatter(val, row) {
    return row.certificateDel === 1 ? `<del style="color: #A2A2A2">\${val}(已删除)</del>` : val
  }

</script>
