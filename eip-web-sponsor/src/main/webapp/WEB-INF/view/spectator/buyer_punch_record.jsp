<!-- 打卡记录 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="250529"/>
<title>打卡记录</title>
  <link rel="stylesheet" href="../../css/view/clockRecord.css?v=${v}">
  <script>var eippath = '${eippath}';</script>
  <script src="../../js/view/clockRecord.js?v=${v}" async></script>
</head>
<body>

<!-- 主页面 -->
<style>
  #main{
    display: none;
  }
  #app-search label {
    padding-left: 5px;
    padding-right: 15px;
  }
</style>
  <div class="easyui-layout app" data-options="fit:true">
      <div data-options="region:'center',border:false" id="main">
          <div id="app-toolbar">
              <div class="my-toolbar-button">
                <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-reload"
                onclick="app.fresh()" plain="true">刷新</a>
                <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-view"
                    onclick="app.export()"  plain="true">导出</a>
              </div>
              <form id="app-search" class="my-toolbar-search my-toolbar-search-p">
                <label>
                  观众名&nbsp;
                  <input id="app-search-name" class="easyui-textbox" style="width:120px;height:25px">
                </label>
                <%--<label>
                  观众登记号码&nbsp;
                  <input id="app-search-regNum" class="easyui-textbox" style="width:120px;height:25px">
                </label>--%>
                <label>
                  手机
                  <input id="app-search-mobile" class="easyui-textbox" style="width:120px;height:25px">
                </label>
                <label>
                  邮箱
                  <input id="app-search-email" class="easyui-textbox" style="width:120px;height:25px">
                </label>
                <label>
                  会员号/手机/邮箱&nbsp;
                  <input id="app-search-userName" class="easyui-textbox" style="width:120px;height:25px">
                </label>
                <label>
                  活动名称&nbsp;
                  <select id="app-search-eventName" data-options="
                    panelHeight: 'auto',
                    panelMaxHeight: 200,
                  " class="easyui-combobox" style="width:120px;height:25px">
                  </select>
                </label>
                <label>
                  打卡点&nbsp;
                  <select id="app-search-clockId"  data-options="
                    panelHeight: 'auto',
                    panelMaxHeight: 200,
                  " class="easyui-combobox" style="width:120px;height:25px">
                    <option value="">请先选择活动名称</option>
                  </select>
                </label>
                <label>
                  打卡点类型&nbsp;
                  <select id="app-search-clockType" class="easyui-combobox" style="width:120px;height:25px" data-options="panelHeight: 'auto',panelMaxHeight: 200,panelHeight:'auto',limitToList: true,valueField: 'id',textField: 'text',data: [
                    {id: 1, text: '必打卡'},
                    {id: 2, text: '可选打卡'},
                    {id: 3, text: '独立打卡点'},
                  ]">
                  </select>
                </label>
                <label>
                  <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="app.search()">查询</a>
                </label>
              </form>
          </div>
          <table id="app-datagrid" class="easyui-datagrid" toolbar="#app-toolbar"></table>
      </div>
  </div>

</body>
</html>