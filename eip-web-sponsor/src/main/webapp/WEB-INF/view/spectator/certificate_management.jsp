<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
    .certificate-management-main .datagrid-view .datagrid-view1 {
        left: auto;
        right: 0;
        box-shadow: 3px 0 10px #aaa;
        z-index: 9;
    }
    .certificate-management-main .datagrid-view .datagrid-view2 {
        left: 0;
        right: auto;
    }
    .hide {
      display: none !important;
    }
    .certificate-management-import-btn{
      color: #fff;
      display: block;
      width: 146px;
      height: 32px;
      background-color: #4fa0ff;
      border-radius: 6px;
      text-align: center;
      line-height: 32px;
      margin: 5px auto 15px;
      font-size: 14px;
      cursor: pointer;
    }
    .certificate-management-main .tabs-header,
    .certificate-management-main .tabs-panels,
    .certificate-management-main .datagrid-wrap {
      border: none !important;
    }
</style>
<div id="certificate-management-toolbar" style="padding-bottom: 5px;background-color: #f4f4f4;">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:certificate_management_take()" class="easyui-linkbutton"
       iconCls="icon-add" plain="true">手动发放票证</a>
    <a href="javascript:certificate_management_import()" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导入票证</a>
    <a href="javascript:certificate_management_import('issued')" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导入换证数据</a>
    <a href="javascript:certificate_management_certIssuedStateOpen()" class="easyui-linkbutton"
       iconCls="icon-maker" plain="true">修改换证状态</a>
    <input type="file" onchange="certificate_management_importf(this)" id="certificate_management_choiseexcel" hidden="hidden" />
    <input type="file" onchange="certificate_management_importfIsIssued(this)" id="certificate_management_chooseIssuedExcel" hidden="hidden" />
    <a href="javascript:certificate_management_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
    <a href="javascript:certificate_management_unbind_wx()"
      class="easyui-linkbutton hide js-certType js-certType-onlyH"
       iconCls="icon-wechat" plain="true">解绑微信</a>
    <a id="certificate-management-push-btn" href="javascript:certificate_management_push()"
      class="easyui-linkbutton hide js-certType"
       iconCls="icon-redo" plain="true">推送</a>
  </div>
  <div class="my-toolbar-search">
    <div class="hide">
      <select id="certificate-management-keyword-certType"
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true,onChange: function(n,o) {
          certificate_management_switchTab(n,o)
        }">
        <!-- <option value="">全部</option> -->
        <option value="A" selected>门票证件</option>
        <option value="C">会议证件</option>
      </select>
    </div>
    <%--<span style="display:inline-block; ">领取人登记号码 </span>
    <input id="certificate-management-keyword-regNum" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;--%>
    <%--拆分为领取人手机和邮箱--%>
    <span style="display:inline-block;">领取人手机号 </span>
    <input id="certificate-management-keyword-mobile" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;">领取人邮箱 </span>
    <input id="certificate-management-keyword-email" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;">证件名称 </span>
    <input
      class="easyui-combobox"
      data-options="panelMaxHeight:'200px'"
      id="certificate-management-keyword-certificateId"
      style="width:175px;height:25px"/>&nbsp;&nbsp;
    <span style="display:inline-block;">证件状态 </span>
    <select
      class="easyui-combobox"
      data-options="panelHeight:'auto',limitToList:true"
      id="certificate-management-keyword-state"
      style="width: 120px;height:25px">
      <option value="" selected>全部</option>
      <option value="0">启用</option>
      <option value="1">停用</option>
    </select>&nbsp;&nbsp;

    <span class="push-control" style="display:inline-block;">推送状态 </span>
    <div class="push-control" style="display:inline-block;">
      <input
          class="easyui-combobox"
          data-options="panelHeight:'auto',limitToList:true,
          url: '/eip-web-sponsor/pushApi/getPushApiType',method: 'post',
          valueField:'code',textField:'text',
          panelMaxHeight:'200px',limitToList:true,
          queryParams:{exhibitCode:exhibitCode_for_pass, dataType: 'buyer'},
          loadFilter(data) {
            const ret = data.data
            ret.unshift({code:'any',text:'任意一个'})
            ret.unshift({code:'',text:'全部'})
            return ret
          }"
          id="certificate-management-keyword-apiType"
          style="width: 120px;height:25px"/>&nbsp;&nbsp;
    </div>
    <div class="push-control"  style="display:inline-block;">
      <select
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true"
        id="certificate-management-keyword-dataPush"
        style="width: 120px;height:25px">
        <option value="" selected>全部</option>
        <option value="-1">未推送</option>
        <option value="0">推送失败</option>
        <option value="1">已推送</option>
      </select>&nbsp;&nbsp;
    </div>
    <div class="hide">
    <span style="display:inline-block;margin-bottom: 18px;">领取人姓名：</span>
    <input id="certificate-management-keyword-buyerName" class="my-text easyui-textbox"
           style="width:120px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">会员/手机/邮箱</span>
    <input id="certificate-management-keyword-zzyuserName" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block; ">项目：</span>
    <input
      class="easyui-combobox"
      style="width:175px;height:25px"
      id="certificate-management-keyword-projectId"
      data-options="
      valueField:'projectId',
      textField:'projectName',
      panelHeight:'auto',
      panelMaxHeight:'200px',
      limitToList:true,
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode: exhibitCode_for_pass}"/>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">证件ID</span>
    <input id="certificate-management-keyword-buyerCertificateIdStr" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block;">证件来源：</span>
    <select
        class="easyui-combobox"
        data-options="panelHeight:'auto',limitToList:true"
        id="certificate-management-keyword-certificateFrom"
        style="width: 120px;height:25px">
      <option value="" selected>全部</option>
      <option value="1">自用</option>
      <option value="2">转赠</option>
    </select>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">关联订单：</span>
    <input id="certificate-management-keyword-orderTradeIdStr" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">订单提交姓名：</span>
    <input id="certificate-management-keyword-orderBuyerName" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">提交人手机：</span>
    <input id="certificate-management-keyword-orderRegNum" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;
    <span style="display:inline-block;">兑换状态：</span>
    <select
      class="easyui-combobox"
      data-options="panelHeight:'auto',limitToList:true"
      id="certificate-management-keyword-isConvertible"
        style="width: 120px;height:25px">
      <option value="" selected>全部</option>
      <option value="true">已兑换</option>
      <option value="false">未兑换</option>
    </select>&nbsp;&nbsp;
    </div>
    <a href="javascript:void(0);" class="easyui-linkbutton" style="height:25px"
      onclick="certificate_management_search()">查询</a>
    <a href="javascript:void(0);" class="easyui-linkbutton" style="height:25px"
      onclick="certificate_management_searchExt()">组合查询</a>
  </div>
</div>
<div class="easyui-panel certificate-management-main" style="width:100%;height:calc(100vh - 240px) !important;padding:0px;">
	<div class="easyui-tabs" style="width:100%;height:calc(100vh - 240px) !important;" id="certificate-management-tabs"
    data-options="
	    onSelect: function(title,idx) {
        $('#certificate-management-keyword-certType').combobox('setValue',['A','C'][idx] || 'A')
	  }">
  <div title="门票证件管理" style="width:99%;height:calc(100vh - 240px) !important;padding:0px;">
    <!-- toolbar="#certificate-management-toolbar" -->
    <table
      id="certificate-management-datagrid"
      style="height: calc( 100vh - 280px )"
      class="easyui-datagrid"
      data-options="rownumbers:false,fitColumns:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
              onClickRow: function (rowIndex, rowData) {
                var l = $(this).datagrid('getRows').length;
                for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
             },frozenColumns:[[
             {field:'buyerId',title:'操作',width:80,formatter:certificateManagementFormatter,align:'center'}
           ]]
            "
    >
      <thead>
      <tr id="certificate-management-datagrid-tr">
        <th class="datagrid-td-rownumber" data-options="
          field:'rowNo',
          width:30,
          align: 'center',
          formatter(value,row,index){
            const {pageNumber,pageSize} = $('#certificate-management-datagrid').datagrid('options')
            return index + 1 + (pageNumber - 1) * pageSize
          }"></th>
        <th data-options="field:'ck',checkbox:true"></th>
        <th field="buyerCertificateId" width="220" align="center">证件ID</th>
        <th field="certificateFrom" width="120" align="center" formatter="certificateManagementCertificateFromFormatter">证件来源</th>
        <!-- <th field="certType" width="120" align="center" formatter="v=>v==='A'?'观众门票证件':v==='C'?'会议证件':''">证件类型</th> -->
        <th field="userName" width="200" align="center">领取会员</th>
        <th field="buyerName" width="180" align="center">领取人姓名</th>
        <th field="regNum" width="180" align="center">领取人登记号码</th>
        <th field="projectName" align="center">项目</th>
        <th field="buyerCertificateCode" align="center">证件胸卡号</th>
        <th field="certificateName" width="200" align="center" formatter="certificateManagementCertificateNameFormatter">证件名称</th>
        <th field="checkNum" width="120" align="center" formatter="certificateManagementCheckNumFormatter">核销次数</th>
        <th field="state" width="150" align="center" formatter="v=>v===1?'停用':'启用'">证件状态</th>
        <th field="isConvertible" width="150" align="center" formatter="v=>v?'已兑换':'未兑换'">兑换学分</th>
        <th field="convertibleTime" width="150" align="center">兑换时间</th>
        <th field="isIssued" width="110" align="center" formatter="v=>v?'已换取':'未换取'">换取实物证件</th>
        <th field="issuedTime" width="150" align="center">换取时间</th>
        <th field="receiveType" width="150" align="center" formatter="v=>v===1?'是':'否'">后台发放</th>
        <th field="createTime" width="150" align="center">证件领用时间</th>
        <th field="orderTradeId" width="200" align="center">证件关联订单</th>
        <th field="payType" width="200" align="center" formatter="certificateManagementPayTypeFormatter">证件支付方式</th>
        <th field="orderUserName" width="150" align="center">提交会员</th>
        <th field="orderBuyerName" width="150" align="center">提交人姓名</th>
        <th field="orderRegNum" width="150" align="center">提交人登记号码</th>
        <th field="openId" width="150" align="center">微信ID</th>
      </tr>
      </thead>
    </table>
    <!-- </div> -->
  </div>
  <div title="会议证件管理" style="width:99%;height:calc(100vh - 240px) !important;padding:0px;">
      <!-- toolbar="#certificate-management-toolbar" -->
      <table
        id="certificate-management-datagrid2"
        style="height: calc( 100vh - 280px )"
        class="easyui-datagrid"
        data-options="rownumbers:false,fitColumns:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
                onClickRow: function (rowIndex, rowData) {
                  var l = $(this).datagrid('getRows').length;
                  for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
               },frozenColumns:[[
               {field:'buyerId',title:'操作',width:80,formatter:certificateManagementFormatter,align:'center'}
             ]]
              "
      >
        <thead>
        <tr id="certificate-management-datagrid2-tr">
          <th class="datagrid-td-rownumber" data-options="
            field:'rowNo',
            width:30,
            align: 'center',
            formatter(value,row,index){
              const {pageNumber,pageSize} = $('#certificate-management-datagrid2').datagrid('options')
              return index + 1 + (pageNumber - 1) * pageSize
            }"></th>
          <th data-options="field:'ck',checkbox:true"></th>
          <th field="buyerCertificateId" width="220" align="center">证件ID</th>
          <th field="certificateFrom" width="120" align="center" formatter="certificateManagementCertificateFromFormatter">证件来源</th>
          <!-- <th field="certType" width="120" align="center" formatter="v=>v==='A'?'观众门票证件':v==='C'?'会议证件':''">证件类型</th> -->
          <th field="userName" width="200" align="center">领取会员</th>
          <th field="buyerName" width="180" align="center">领取人姓名</th>
          <th field="regNum" width="180" align="center">领取人登记号码</th>
          <th field="projectName" align="center">项目</th>
          <th field="buyerCertificateCode" align="center">证件胸卡号</th>
          <th field="certificateName" width="200" align="center" formatter="certificateManagementCertificateNameFormatter">证件名称</th>
          <th field="checkNum" width="120" align="center" formatter="certificateManagementCheckNumFormatter">核销次数</th>
          <th field="state" width="150" align="center" formatter="v=>v===1?'停用':'启用'">证件状态</th>
          <th field="isConvertible" width="150" align="center" formatter="v=>v?'已兑换':'未兑换'">兑换学分</th>
          <th field="convertibleTime" width="150" align="center">兑换时间</th>
          <th field="isIssued" width="110" align="center" formatter="v=>v?'已换取':'未换取'">换取实物证件</th>
          <th field="issuedTime" width="150" align="center">换取时间</th>
          <th field="receiveType" width="150" align="center" formatter="v=>v===1?'是':'否'">后台发放</th>
          <th field="createTime" width="150" align="center">证件领用时间</th>
          <th field="orderTradeId" width="200" align="center">证件关联订单</th>
          <th field="payType" width="200" align="center" formatter="certificateManagementPayTypeFormatter">证件支付方式</th>
          <th field="orderUserName" width="150" align="center">提交会员</th>
          <th field="orderBuyerName" width="150" align="center">提交人姓名</th>
          <th field="orderRegNum" width="150" align="center">提交人登记号码</th>
          <th field="openId" width="150" align="center">微信ID</th>
        </tr>
        </thead>
      </table>
      <!-- </div> -->
  </div>
</div>
</div>
<!-- 推送弹窗 -->
<div id="certificate-management-push" class="easyui-dialog" title="确认推送"
     data-options="modal:true,shadow:false,closed:true"
     style="width:500px">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_management_push_submit()"
       plain="true"
       style="margin-right: 6px;">确定</a>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt"
       iconCls="icon-cancel"
       onclick="$('#certificate-management-push').window('close')"
       plain="true"
       style="margin-right: 6px;">取消</a>
  </div>
  <div class="easyui-panel" title="基本信息">
    <div style="padding: 20px">
      <span style="padding-left: 5px;display: inline-block;margin-bottom: 10px">请选择您所需要推送的数据</span>
      <div>
        <label style="display: inline-flex;align-items:center;margin-right: 20px;"><input type="radio" name="certificate-pushType" checked value="1"/>全部选中数据<span id="js-cert-manage-push-all"></span></label>
        <label style="display: inline-flex;align-items:center;"><input type="radio" name="certificate-pushType" value="2"/>未推送和推送失败的数据<span id="js-cert-manage-push-lost"></span></label>
      </div>
      <div>
        <span style="padding-left: 5px;display: inline-block;margin-bottom: 10px">请选择第三方推送接口</span>
        <input
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true,
          url: '/RegSever/pushApi/getList',method: 'post',
          valueField:'apiId',textField:'text',
          panelMaxHeight:'200px',limitToList:true,
          multiple:true,
          queryParams:{exhibitCode:exhibitCode_for_pass, dataType: 'buyer', isOpen: true},
          loadFilter(data) {
            // 会议证件不支持 yiZhiXing 的接口
            if($('#certificate-management-keyword-certType').combobox('getValue') === 'C') {
              data.data = data.data.filter(item=>item.apiType !=='yiZhiXing')
            }
            return data.data.map(item => {
              return {
                ...item,
                text: ((certList) =>{
                  return `【\${item.apiTypeName}】` + certList.map(it => it.certificateName).join('、')
                })(item.pushCertificateList)
              }
            })
          }"
            id="certificate-pushApiIds"
            style="width: 100%;height:25px"/>
      </div>
    </div>
  </div>
</div>


<style>
  #certificate-management-searchExt .panel-body span{
    text-align: right;
  }
</style>
<!-- 组合查询 -->
<div id="certificate-management-searchExt" class="easyui-dialog" title="组合查询"
     data-options="modal:true,shadow:false,closed:true"
     style="width: 750px">
  <div class="my-toolbar-button" style="display: flex;flex-direction: row-reverse">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_management_searchExtSure()"
       plain="true"
       style="margin-right: 6px;">查询</a>
  </div>
  <div class="easyui-panel" title="基本信息">
    <div style="padding: 20px 20px 10px;display: grid;grid-template-columns: 166px 150px 166px 150px;grid-gap: 10px;">
      <span>领取人姓名</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-buyerName" style="width:150px;height:25px">
      </div>
      <%--领取人登记号码、提交人登记号码 【改为领取人/提交人手机号、邮箱】--%>
      <%--<span>领取人登记号码</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-regNum" style="width:150px;height:25px">
      </div>--%>
      <span>领取人手机号</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-mobile" style="width:150px;height:25px">
      </div>
      <span>领取人邮箱</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-email" style="width:150px;height:25px">
      </div>
      <span>领取人渠道类型</span>
      <div>
        <input class="easyui-combobox" style="width:150px;height:25px"
            id="jsf-cert-channelType"
            data-options="valueField:'channelTypeId',textField:'channelTypeName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
      </div>
      <span>领取人渠道名称 </span>
      <div><input class="easyui-textbox" style="width:150px;height:25px" id="jsf-cert-channelName"/></div>
      <span>会员/绑定手机/绑定邮箱</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-zzyuserName" style="width:150px;height:25px">
      </div>
      <!-- <span>电话?</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-TODO" style="width:150px;height:25px">
      </div> -->
      <span>项目</span>
      <div>
        <input
          class="easyui-combobox"
          style="width:150px;height:25px"
          id="jsf-cert-projectId"
          data-options="
          valueField:'projectId',
          textField:'projectName',
          panelHeight:'auto',
          panelMaxHeight:'200px',
          limitToList:true,
          onChange: function(n) {
            $('#jsf-cert-targetType').combobox('clear');
            certificate_management_LoadRoles2(n);
            certificate_management_sync_certList();
          },
          url: '/eip-web-sponsor/project/getAllByExhibitCode',
          queryParams: {exhibitCode: exhibitCode_for_pass}"/>
      </div>
      <span>身份</span>
      <div>
        <select
          class="easyui-combobox"
          data-options="
            panelHeight:'auto',limitToList:true,
            onChange() {
              certificate_management_sync_certList();
            }
          "
          id="jsf-cert-targetType"
          style="width: 150px;height:25px">
          <option value="" selected>全部</option>
        </select>
      </div>

      <span>证件名称</span>
      <div>
        <input type="text" data-options="panelMaxHeight:'200px'" class="easyui-combobox" id="jsf-cert-certificateId" style="width:150px;height:25px">
      </div>

    </div>
  </div>
  <div class="easyui-panel" title="票证状态">
    <div style="padding: 20px 20px 0;display: grid;grid-template-columns: 166px 150px 166px 150px;grid-gap: 10px;">
      <span>证件状态</span>
      <div>
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-state"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="0">启用</option>
          <option value="1">停用</option>
        </select>
      </div>

      <span>证件ID</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-buyerCertificateIdStr" style="width:150px;height:25px">
      </div>

      <span>证件来源</span>
      <div>
        <input
            class="easyui-combobox"
            data-options="valueField:'id',textField:'text',panelHeight:'auto',limitToList:true,data:certificateManagementCertificateFrom"
            id="jsf-cert-certificateFrom"
            style="width: 150px;height:25px"/>
      </div>

      <span>胸卡号</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-buyerCertificateCode" style="width:150px;height:25px">
      </div>
    </div>
    <div style="padding:10px 20px 10px;display: grid;grid-template-columns: 166px 150px 166px 150px;grid-gap: 10px;">
      <span class="hide js-certType js-certType-onlyH" > 兑换状态</span>
      <div class="hide js-certType js-certType-onlyH" >
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-isConvertible"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="true">已兑换</option>
          <option value="false">未兑换</option>
        </select>
      </div>
    </div>
    <div style="padding:0 20px 10px;display: grid;grid-template-columns: 166px 237px 237px;grid-gap: 10px;">
      <span>领用时间段</span>
      <div>
        <input type="text" class="easyui-datetimebox" id="jsf-cert-createTimeStart"
               style="width: 237px;height:25px">
      </div>
      <div>
        <input type="text" class="easyui-datetimebox" id="jsf-cert-createTimeEnd"
               style="width: 237px;height:25px">
      </div>
    </div>
    <div class="push-control" style="padding: 0 20px 10px;display: grid;grid-template-columns: 166px 450px;grid-gap: 10px;">
      <span>推送状态</span>
      <div>
        <input
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true,
          url: '/eip-web-sponsor/pushApi/getPushApiType',method: 'post',
          valueField:'code',textField:'text',
          panelMaxHeight:'200px',limitToList:true,
          queryParams:{exhibitCode:exhibitCode_for_pass, dataType: 'buyer'},
          loadFilter(data) {
            const ret = data.data
            ret.unshift({code:'any',text:'任意一个'})
            ret.unshift({code:'',text:'全部'})
            return ret
          }"
            id="jsf-cert-apiType"
            style="width: 150px;height:25px"/>&nbsp;&nbsp;
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-dataPush"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="-1">未推送</option>
          <option value="0">推送失败</option>
          <option value="1">已推送</option>
        </select>
      </div>
      <div style="display: none;">
        <span>推送备注</span>
        <div>
          <input type="text" class="easyui-textbox" id="jsf-cert-dataPushMemo" style="width:150px;height:25px">
        </div>
      </div>
    </div>


    <div style="padding:0 20px 10px;display: grid;grid-template-columns: 166px 150px 166px 150px;grid-gap: 10px;">
      <span>换取实物证件</span>
      <div>
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-isIssued"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="true">已换取</option>
          <option value="false">未换取</option>
        </select>
      </div>
    </div>
    <div style="padding:0 20px 10px;display: grid;grid-template-columns: 166px 237px 237px;grid-gap: 10px;">
      <span>换取时间段</span>
      <div>
        <input type="text" class="easyui-datetimebox" id="jsf-cert-issuedTimeStart" style="width: 237px;height:25px">
      </div>
      <div>
        <input type="text" class="easyui-datetimebox" id="jsf-cert-issuedTimeEnd" style="width: 237px;height:25px">
      </div>
    </div>

  </div>
  <div class="easyui-panel" title="支付订单信息">
    <div style="padding: 0 20px 20px;display: grid;grid-template-columns: 166px 150px 166px 150px;grid-gap: 10px;">
      <span>关联订单</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-orderTradeIdStr" style="width:150px;height:25px">
      </div>
      <span>支付方式</span>
      <div style="grid-column: span 3">
        <select
            class="easyui-combobox"
            data-options="
              multiple:true,
              valueField:'id',
              textField:'text',
              panelHeight:'auto',
              limitToList:true,
              panelMaxHeight:'200px',
               onChange(values) {
                const vStr = values.toString()
                if (vStr !== '5') {
                  $('#jsf-cert-inviteCodeCompanyName').textbox('clear')
                  $('.only-show-invite-code').hide()
                }
                if (vStr !== '6') {
                  $('.only-hide-invite-code').hide()
                }
                if (vStr === '5') {
                  $('.only-show-invite-code').show()
                } else if (vStr === '6') {
                  $('.only-show-invite-code.discount-code').show()
                  $('.only-hide-invite-code').show()
                } else {
                  $('#jsf-cert-inviteCode').textbox('clear')
                }
              },
              data: certificateManagementPayTypeList"
            id="jsf-cert-payTypes"
            style="width: 150px;height:25px">
        </select>
        <label style="margin-left: 15px;">
          <input type="checkbox" id="jsf-cert-useCouponFlag">
          是否使用代金券
        </label>
      </div>

      <span class="only-show-invite-code discount-code">邀请码/优惠码</span>
      <div class="only-show-invite-code discount-code">
        <input type="text" class="easyui-textbox" id="jsf-cert-inviteCode" style="width:150px;height:25px">
      </div>
      <span class="only-show-invite-code">邀请码展商</span>
      <div class="only-show-invite-code">
        <input type="text" class="easyui-textbox" id="jsf-cert-inviteCodeCompanyName" style="width:150px;height:25px">
      </div>
      <div class="only-hide-invite-code" style="grid-column: span 4;display: none"></div>

      <span>订单提交姓名</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-orderBuyerName" style="width:150px;height:25px">
      </div>
      <%--<span>提交人登记号码</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-orderRegNum" style="width:150px;height:25px">
      </div>--%>
      <%--提交人登记号码拆成提交人手机号和提交人邮箱--%>
      <span>提交人手机号</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-orderMobile" style="width:150px;height:25px">
      </div>
      <span>提交人邮箱</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-orderEmail" style="width:150px;height:25px">
      </div>
    </div>
  </div>
</div>

<!-- 手动发放领用证件 -->
<div id="certificate-management-take" class="easyui-dialog" title="手动发放领用证件"
     data-options="modal:true,shadow:false,closed:true"
     style="width:400px">
  <div class="my-toolbar-button" style="background-color: #f4f4f4;">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_management_take_preview()"
       plain="true"
       style="margin-right: 6px;">保存</a>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt"
       iconCls="icon-cancel"
       onclick="$('#certificate-management-take').window('close')"
       plain="true"
       style="margin-right: 6px;">取消</a>
  </div>
  <!-- <div class="easyui-panel" title="基本信息"> -->
    <div style="padding: 20px;display: grid;grid-template-columns: 120px 200px;grid-gap: 10px;">
      <span style="text-align: right;"><span style="color: red">*</span> 项目</span>
      <div>
        <input type="text" class="easyui-combobox" id="jsf-certTake-projectId"
          style="width: 175px;height: 25px"
          data-options="
            valueField:'projectId',
            textField:'projectName',
            panelHeight:'auto',
            panelMaxHeight:'200px',
            limitToList:true,
            url: '/eip-web-sponsor/certificate/getProject',
            onChange: function(n,o) {
              $('#jsf-certTake-certificateId').combobox('clear');
              certificate_management_LoadRoles(n);
            },
            loadFilter(data) {
              return (data && data.state==1) ? (data.data || []) : [];
             },
            queryParams: {
              exhibitCode: exhibitCode_for_pass,
              isFree: true,
            }" >
      </div>
      <span style="text-align: right;"><span style="color: red">*</span> 身份</span>
      <div>
        <input type="text" class="easyui-combobox" id="jsf-certTake-targetType" style="width: 175px;height: 25px"
          data-options="
            panelHeight:'auto',
            panelMaxHeight:'200px',
            limitToList:true,
            onChange: function(n,o) {
              $('#jsf-certTake-certificateId').combobox('clear')
            },
          " >
      </div>
      <span style="text-align: right;"><span style="color: red">*</span> 登记号码</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-certTake-regNum" style="width: 175px;height: 25px">
      </div>
      <span class="hide-by-a-cert-type" style="text-align: right;"><span style="color: red">*</span> 发放领用证件</span>
      <div class="hide-by-a-cert-type">
        <input type="text" data-options="panelMaxHeight:'200px'" class="easyui-combobox" id="jsf-certTake-certificateId" style="width: 175px;height: 25px">
      </div>
    </div>
  <!-- </div> -->
</div>

<!-- 修改换证状态 -->
<div id="certificate-management-certIssuedState" class="easyui-dialog" title="修改换证状态"
     data-options="modal:true,shadow:false,closed:true"
     style="width:400px">
  <div class="my-toolbar-button" style="background-color: #f4f4f4;">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_management_certIssuedStateSave()"
       plain="true"
       style="margin-right: 6px;">保存</a>
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-cancel"
       onclick="$('#certificate-management-certIssuedState').window('close')"
       plain="true"
       style="margin-right: 6px;">取消</a>
  </div>
  <div style="padding: 20px;display: grid;grid-template-columns: 100px 200px;grid-gap: 10px;">
    <span style="text-align: right;"><span style="color: red">*</span> 换取实物证件</span>
    <div>
      <select
          class="easyui-combobox"
          data-options="
            panelHeight:'auto',
            limitToList:true,
            onChange(value) {
              const displayFn = value === 'true' ? 'show' : 'hide'
              $('#jsf-certIssuedState-save_issuedTime').parent().parent()[displayFn]().prev()[displayFn]()
            }
          "
          id="jsf-certIssuedState-save_isIssued"
          style="width: 180px;height:25px">
        <option value="true" selected>已换取</option>
        <option value="false">未换取</option>
      </select>
    </div>
    <span style="text-align: right"><span>&nbsp;</span> 换取时间</span>
    <div>
      <div>
        <input type="text" class="easyui-datetimebox" id="jsf-certIssuedState-save_issuedTime" style="width: 180px;height:25px">
      </div>
    </div>
  </div>
</div>

<!-- 导入 -->
<div id="certificate-management-import" data-options="closed:true,title:'导入',modal:true,maximizable: false,resizable: false,minimizable: false" style="width:300px;display: none;">
	<div  class="easyui-panel"  style="width:100%;height:160px;padding:10px;">
		<a class="certificate-management-import-btn" style='margin-top: 30px;color: white;' href="/eip-web-sponsor/document/会议证件导入模板.xlsx" download="会议证件导入模板">下载导入模板</a>
		<span class="certificate-management-import-btn" onclick='certificate_management_import2()'>导入</span>
	</div>
</div>

<script>
  function certificate_management_import(isIssued) {
    window.__CERTIFICATE_MANAGEMENT_IMPORT_IS_ISSUED = isIssued === 'issued'
    const tempMap = {
      A: '/eip-web-sponsor/document/门票导入模板.xlsx',
      C: '/eip-web-sponsor/document/会议证件导入模板.xlsx'
    }
    const url = window.__CERTIFICATE_MANAGEMENT_IMPORT_IS_ISSUED ?
      '/eip-web-sponsor/document/领取实物证件模板.xlsx' :
      tempMap[$('#certificate-management-keyword-certType').combobox('getValue')]
    $('.certificate-management-import-btn').attr('href', url).attr('download', url.split('/').slice(-1)[0].slice(0, -5))
    // document.getElementById('choiseexcel').click()
    $('#certificate-management-import').window({
			closed: false,
      cls: 'certificate-management-import',
      onBeforeOpen() {
        let w = $('.certificate-management-import')
        if(w.length>1){
          for (let i = 0; i < w.length-1; i++) {
            w[i].remove()
          }
        }
      },
		});
	}
  function certificate_management_import2() {
    window.__CERTIFICATE_MANAGEMENT_IMPORT_IS_ISSUED ?
      $('#certificate_management_chooseIssuedExcel').trigger('click') :
      $('#certificate_management_choiseexcel').trigger('click')
  }
  function certificate_management_importf(obj) {
		console.log('开始导入');
		if (!obj.files) {
			return;
		}
		var file = obj.files[0];
    const serialNum = "SP"+org_num_for_pass+"CERT:" + (new Date().toLocaleString()).replace(' ', '')+'' + (Math.random()*899+100);
		document.getElementById('certificate_management_choiseexcel').value = '';
		var formData = new window.FormData();
		formData.append('file', file);
		// formData.append('pid', getQueryString("projectId"));
		formData.append('serialNum', serialNum);
		formData.append('certType', $('#certificate-management-keyword-certType').combobox('getValue'));
		formData.append('exhibitCode', exhibitCode_for_pass);
		let tmpAlert ;
		$.ajax({
			url: eippath + "/buyerCertificate/importData",
			type: "post",
			data: formData,
			processData: false,  // 不处理数据
			contentType: false,   // 不设置内容类型
			beforeSend: function() {
				tmpAlert = $.messager.alert('提示', '导入中。。请稍候', 'info');
			},
			success: function(data) {
				tmpAlert.window({closed: true});
				if(data.state == 1){
					if(data.data) {
            let errHtml = '<ul style="font-size:12px;list-style-type: disc;margin-left: 17px;">';
            let noBuyer = (data.data.noBuyer || []).length;
            let noCert = (data.data.noCert || []).length;
            let exceptionData = (data.data.exceptionData || []).length;
            let noTarget = (data.data.noTarget || []).length;
            let noProject = (data.data.noProject || []).length;
            let haveCert = (data.data.haveCert || []).length;
            let noFree = (data.data.noFree || []).length;
            let limitReceiveTime = (data.data.limitReceiveTime || []).length;
            if(data.data.failed) {
              if(noBuyer) errHtml += '<li>无对应观众 导致发放失败 ' + noBuyer + '</li>';
              if(noCert) errHtml += '<li>无对应证件 导致发放失败 ' + noCert + '</li>';
              if(noTarget) errHtml += '<li>无对应身份 导致发放失败 ' + noTarget + '</li>';
              if(noProject) errHtml += '<li>无对应项目 导致发放失败 ' + noProject + '</li>';
              if(haveCert) errHtml += '<li>观众已领取证件 导致发放失败 ' + haveCert + '</li>';
              if(noFree) errHtml += '<li>证件不是免费证件 导致发放失败 ' + noFree + '</li>';
              if(limitReceiveTime) errHtml += '<li>证件已超过截止日期 导致发放失败 ' + limitReceiveTime + '</li>';
              if(exceptionData) errHtml += '<li>其他异常情况 导致发放失败 ' + exceptionData + '</li>';
            }
            errHtml += '</ul>';
						$.messager.alert('提示',`<div style="display: grid;">
              <div>操作成功,
              \${'<br>成功数量: ' + (+data.data.success || 0)}
              \${data.data.failed ? ('<br>失败数量及明细 <br> ' + errHtml) :''}
						  </div>
            </div>`,'warning').window({width: 380});
					} else {
						$.messager.alert('提示','导入成功！','info');
					}
					// $('#project-boothdata-window-b').window('close', true);
					certificate_management_search();
				}else{
					$.messager.alert('提示','导入失败！','error');
				}
			},
		});
	}
  function certificate_management_importfIsIssued(obj) {
    if (!obj.files) return;
    var file = obj.files[0];
    const serialNum = "SP"+org_num_for_pass+"CERT-ISSUED:" + (new Date().toLocaleString()).replace(' ', '')+'' + (Math.random()*899+100);
    document.getElementById('certificate_management_chooseIssuedExcel').value = '';
    var formData = new window.FormData();
    formData.append('file', file);
    formData.append('serialNum', serialNum);
    formData.append('exhibitCode', exhibitCode_for_pass);
    let tmpAlert ;
    $.ajax({
      url: eippath + "/buyerCertificate/importIssuedData",
      type: "post",
      data: formData,
      processData: false,  // 不处理数据
      contentType: false,   // 不设置内容类型
      beforeSend: function() {
        tmpAlert = $.messager.alert('提示', '导入中。。请稍候', 'info');
      },
      success: function(data) {
        tmpAlert.window({closed: true});
        if(data.state == 1){
          if(data.data) {
            let errHtml = '<ul style="font-size:12px;list-style-type: disc;margin-left: 17px;">';
            let noCertCode = (data.data.noCertCode || []).length;
            let noData = (data.data.noData || []).length;
            if(data.data.failed) {
              if(noCertCode) errHtml += '<li>无胸卡号 导致换取失败 ' + noCertCode + '</li>';
              if(noData) errHtml += '<li>无对应数据 导致换取失败 ' + noData + '</li>';
            }
            errHtml += '</ul>';
            $.messager.alert('提示',`<div style="display: grid;">
              <div>操作成功,
              \${'<br>成功数量: ' + (+data.data.success || 0)}
              \${data.data.failed ? ('<br>失败数量及明细 <br> ' + errHtml) :''}
						  </div>
            </div>`,'warning').window({width: 380});
          } else {
            $.messager.alert('提示','导入成功！','info');
          }
          certificate_management_search();
        }else{
          $.messager.alert('提示','导入失败！','error');
        }
      },
    });
  }
  function certificate_management_LoadRoles(regProjectId) {
    // multiple:true,
		$("#jsf-certTake-targetType").combobox({
      valueField:'targetId',textField:'targetName',
      panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {regProjectId},
			method: 'POST',
			loadFilter(data) {
				return data.state === 1 ? data.data: []
			},
		})
	}
  function certificate_management_LoadRoles2(regProjectId) {
    // multiple:true,
		$("#jsf-cert-targetType").combobox({
      valueField:'targetId',textField:'targetName',
      panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {regProjectId},
			method: 'POST',
			loadFilter(data) {
				return data.state === 1 ? data.data: []
			},
		})
	}
  function certificate_management_sync_certList() {
    const data = {
      certType: $('#certificate-management-keyword-certType').combobox('getValue'),
      exhibitCode: exhibitCode_for_pass,
      targetTypeOrNull: $('#jsf-cert-targetType').combobox('getValue'),
      projectId: $('#jsf-cert-projectId').combobox('getValue'),
    }
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/certificate/getCertificateAndProject',
      dataType: "json",
      data,
      success({data, state}) {
        if (state !== 1) return $.messager.alert('错误', '查询证件数据失败', 'error')
        $('#jsf-cert-certificateId').combobox({
          valueField: 'longId',
          textField: 'text',
          panelHeight: 'auto',
          panelMaxHeight:'200px',
          limitToList: true
        }).combobox('loadData', data)
      }
    })
  }
  function certificate_management_take_sure(para) {
    para = para || {
      projectId: $('#jsf-certTake-projectId').combobox('getValue'),
      targetType: $('#jsf-certTake-targetType').combobox('getValue'),
      regNum: $('#jsf-certTake-regNum').textbox('getValue'),
      certificateId: $('#jsf-certTake-certificateId').combobox('getValue'),
    };
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerCertificate/issueCertificate',
      dataType: "json",
      data: para,
      success({data, state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '领用失败！', 'error');
        $('#certificate-management-take').window('close');
        certificate_management_search();
      },
      error(){
        $.messager.alert('错误', '请求失败！', 'error')
      }
    })
  }
  function certificate_management_take_preview() {
    const para = {
      projectId: $('#jsf-certTake-projectId').combobox('getValue'),
      targetType: $('#jsf-certTake-targetType').combobox('getValue'),
      regNum: $('#jsf-certTake-regNum').textbox('getValue'),
    }
    const certificateId = $('#jsf-certTake-certificateId').combobox('getValue');
    let err = '', msgs = ['','选择项目' ,'选择身份' ,'登记号码', '选择证件'];
    para.certType = $('#certificate-management-keyword-certType').combobox('getValue')
    const isCCert = para.certType === 'C'
    if(!para.projectId) { err = '1'
    } else if(!para.targetType) { err = '2'
    } else if(!para.regNum) { err = '3'
    } else if(!certificateId && isCCert) { err = '4'
    }
    if(err) {
      $.messager.alert('错误', '需要' + msgs[err], 'error');
      throw new Error(msgs[err]);
    }
    const certName = ($('#jsf-certTake-certificateId').combobox('getData').find(it=> it.longId == certificateId) || {}).text || '';
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerCertificate/queryBuyer',
      dataType: "json",
      data: para,
      success({data, state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '发放失败！', 'error');
        if (!data) return $.messager.alert('错误', '未发现登记信息 ！', 'error');
        $.messager.confirm('提示', `<div style="display:grid;grid-gap: 5px;">
          <div style="padding-bottom: 10px">即将给以下观众发放 \${certName||'门票'}, 是否确认？</div>
          <div style="color: #797979;display:grid;grid-template-columns: 100px 250px;"><span>姓名:</span><span>\${data.buyerName ||''}</span></div>
          <div style="color: #797979;display:grid;grid-template-columns: 100px 250px;"><span>手机号:</span><span>\${data.mobile ||''}</span></div>
          <div style="color: #797979;display:grid;grid-template-columns: 100px 250px;"><span>邮箱:</span><span>\${data.email ||''}</span></div>
          <div style="color: #797979;display:grid;grid-template-columns: 100px 250px 100px 150px;">
            <span>登记时间:</span><span>\${data.regTime ||''}</span>
            <span>入场状态:</span><span>\${data.isEntrance ? '已入场' : '未入场'}</span>
          </div>
          <div style="color: #797979;">
            <div style="padding-bottom: 5px;">已拥有证件:</div>
            <div>\${(data.buyerCertificateList || []).map(it=> it.certificateName).join() || ''}</div>
          </div>
        </div>`, function(r){
          if (r) {
            certificate_management_take_sure({
              ...para,
              certificateId,
            });
          }
        }).window({width: 675})
      },
      error(){
        $.messager.alert('错误', '请求失败！', 'error')
      }
    })
  }
  function certificate_management_certIssuedStateOpen(){
    const openWindow = (params) => {
      $('#certificate-management-certIssuedState').window('open').data('params', params)
    }
    const certType = $('#certificate-management-keyword-certType').combobox("getValue");
    const datagrid = $('#certificate-management-datagrid' + (certType === 'A' ? '' : '2'))
    const rows = datagrid.datagrid('getSelections')
    if (!rows.length) {
      return $.messager.confirm('提示', '是否操作全部数据？', r => {
        if (!r) return
        openWindow(datagrid.datagrid('options').queryParams)
      })
    }
    const buyerCertificateIds = rows.map(item => item.buyerCertificateId).filter(Boolean).toString()
    openWindow({buyerCertificateIds})
  }
  function certificate_management_certIssuedStateSave(){
    const win = $('#certificate-management-certIssuedState')
    const data = win.data('params')
    if (!data.exhibitCode) data.exhibitCode = exhibitCode_for_pass
    data.save_isIssued = $('#jsf-certIssuedState-save_isIssued').combobox('getValue')
    if (data.save_isIssued) {
      data.save_issuedTime = $('#jsf-certIssuedState-save_issuedTime').datetimebox('getValue')
    }
    $.messager.progress()
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerCertificate/updateIssued',
      dataType: "json",
      data,
      success({state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
        $.messager.alert('提示', '操作成功', 'info')
        certificate_management_search()
        win.window('close').removeData('params')
      }
    }).done(() => $.messager.progress('close'))
  }
  var g_cert_take_first = true;
  var g_cert_search_history = null;
  var certificateManagementPayTypeList = [
    {id: '0', text: '微信支付'},
    {id: '1', text: '支付宝支付'},
    {id: '3', text: '线下支付'},
    {id: '4', text: '代金券全额抵扣'},
    {id: '5', text: '邀请码全额免费'},
    {id: '6', text: '优惠码全额免费'},
    {id: '10', text: '小程序支付'},
  ]
  var certificateManagementCertificateFrom = [
    {id: '1', text: '后台发放'},
    {id: '2', text: '免费证件领用'},
    {id: '3', text: '购买自用'},
    {id: '4', text: '他人转赠'},
  ]
  function certificate_management_take() {
    $('#jsf-certTake-regNum').textbox('clear');
    if(g_cert_take_first) {
      $('#jsf-certTake-projectId').combobox('setValue', project_for_pass);
      $('#jsf-certTake-targetType').combobox('setValue',1);
      $('#jsf-certTake-certificateId').combobox('clear');
    }
    g_cert_take_first = false;
    const isACert = $('#certificate-management-keyword-certType').combobox('getValue') === 'A'
    $('.hide-by-a-cert-type')[isACert ? 'hide' : 'show']()
    $('#certificate-management-take').window('open')
  }

  function certificate_management_searchExtSure() {
    const certType = $('#certificate-management-keyword-certType').combobox("getValue");
    let param = {
      exhibitCode: exhibitCode_for_pass,
      buyerName: $('#jsf-cert-buyerName').textbox("getValue"),
      // regNum: $('#jsf-cert-regNum').textbox("getValue"),
      mobile: $('#jsf-cert-mobile').textbox("getValue"),
      email: $('#jsf-cert-email').textbox("getValue"),


      zzyuserName: $('#jsf-cert-zzyuserName').textbox("getValue"),
      //
      projectId: $('#jsf-cert-projectId').combobox("getValue"),
      //
      certificateId: $('#jsf-cert-certificateId').combobox("getValue"),
      state: $('#jsf-cert-state').combobox("getValue"),
      buyerCertificateIdStr: $('#jsf-cert-buyerCertificateIdStr').textbox("getValue"),
      buyerCertificateCode: $('#jsf-cert-buyerCertificateCode').textbox("getValue"),
      certificateFrom: $('#jsf-cert-certificateFrom').combobox("getValue"),
      // isConvertible: $('#jsf-cert-isConvertible').combobox("getValue"),
      // dataPush: $('#jsf-cert-dataPush').combobox("getValue"),
      orderTradeIdStr: $('#jsf-cert-orderTradeIdStr').textbox("getValue"),
      orderBuyerName: $('#jsf-cert-orderBuyerName').textbox("getValue"),
      // orderRegNum: $('#jsf-cert-orderRegNum').textbox("getValue"),
      orderMobile: $('#jsf-cert-orderMobile').textbox("getValue"),
      orderEmail: $('#jsf-cert-orderEmail').textbox("getValue"),
      certType,
      createTimeStart: $('#jsf-cert-createTimeStart').datetimebox("getValue"),
      createTimeEnd: $('#jsf-cert-createTimeEnd').datetimebox("getValue"),
      channelType: $('#jsf-cert-channelType').combobox("getValue"),
      channelName: $('#jsf-cert-channelName').textbox("getValue"),
      payTypes: $('#jsf-cert-payTypes').combobox("getValues").join(','),
      useCouponFlag: $('#jsf-cert-useCouponFlag').prop('checked'),
      isIssued: $('#jsf-cert-isIssued').combobox("getValue"),
      issuedTimeStart: $('#jsf-cert-issuedTimeStart').datetimebox("getValue"),
      issuedTimeEnd: $('#jsf-cert-issuedTimeEnd').datetimebox("getValue"),
      // receiveType: $('#jsf-cert-receiveType').combobox("getValue"),
      inviteCode: $('#jsf-cert-inviteCode').textbox("getValue"),
      inviteCodeCompanyName: $('#jsf-cert-inviteCodeCompanyName').textbox("getValue"),
    }
    if(certType == 'A') {
      param.apiType = $('#jsf-cert-apiType').combobox("getValue")
      param.dataPush = $('#jsf-cert-dataPush').combobox("getValue")
      param.dataPushMemo = $('#jsf-cert-dataPushMemo').textbox("getValue")
    } else if(certType == 'C') {
      param.isConvertible = $('#jsf-cert-isConvertible').combobox("getValue")
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    $('#certificate-management-datagrid' + (certType == 'A' ? '' : '2')).datagrid({
      url: eippath + '/buyerCertificate/getPage',
      queryParams: param,
      onLoadSuccess(data) { // {data,rows,total,state}
        if(data.state == 1) {
          $('td[field="rowNo"]').addClass('datagrid-td-rownumber')
          g_cert_search_history = param
          cert_manage_data[certType == 'A' ? 'data1': 'data2'] = data;
        }
      }
    })
    $('#certificate-management-searchExt').window('close');
  }
  function certificate_management_searchExt() {
    $('#certificate-management-searchExt').window('open');
  }
  $(function () {
    window.certificate_management_switchTab = function (n,o) {
      $('.js-certType').addClass('hide');
      $('#certificate-management-push-btn').removeClass('hide');
      if(n == 'A') { // M
        $('#certificate-management-keyword-certType').combobox('setValue', 'A')
        $('.js-certType').removeClass('hide')
      } else if(n == 'C') { // H
        $('#certificate-management-keyword-certType').combobox('setValue', 'C')
        $('.js-certType-onlyH').removeClass('hide')
        if(window.certManagement.isFirst2) { // 初始化下 certificate-management-datagrid2
          window.certManagement.isFirst2 = false;
        }
      }
      loadCertificate()
    }
    function loadCertificate () { // init
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/certificate/getCertificateAndProject',
        dataType: "json",
        data: {
          certType: $('#certificate-management-keyword-certType').combobox('getValue'),
          exhibitCode: exhibitCode_for_pass
        },
        success({data, state}) {
          if (state !== 1) return $.messager.alert('错误', '证件数据！', 'error')
          $('#certificate-management-keyword-certificateId,#jsf-cert-certificateId,#jsf-certTake-certificateId').combobox({
            valueField: 'longId',
            textField: 'text',
            panelHeight: 'auto',
            panelMaxHeight:'200px',
            limitToList: true
          }).combobox('loadData', data)
          certificate_management_setProps()
        },
        error(){
          certificate_management_setProps()
        }
      })
    }
    function loadCheckpoint () {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/getPage',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success({rows, state}) {
          if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
          $('#certificate-management-keyword-checkPointId').combobox({
            valueField: 'checkPointId',
            textField: 'checkPointName',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', rows)
        }
      })
    }
    setTimeout(() => {
      window.certManagement = {isFirst2: true};
      certificate_management_switchTab('A', '');
      // window.certManagement.init()
      certificate_management_check_push();
      // $('#certificate-management-keyword-certType').combobox('setValue','A');
      loadCertificate() // certificate_management_setProps =>  certificate_management_search
      loadCheckpoint()
      $('#jsf-cert-channelType').combobox({
        url: eippath + '/channelType/getList',
        loadFilter(data) {
          data.unshift({
            channelTypeId: 4,
            channelTypeName: "扫码即到场"
          })
          data.unshift({
            channelTypeId: 1,
            channelTypeName: "主办方"
          })
          return data
        },
      })
    }, 0)
  });

  function certificate_management_setProps(orderTradeIdStr, certificateId, buyerCertificateId) {
    const isNotSetOrderProps = !top.__TEMP_CERT_MANAGEMENT || orderTradeIdStr || certificateId || buyerCertificateId
    if (isNotSetOrderProps) {
      $('#certificate-management-keyword-orderTradeIdStr').textbox("setValue", orderTradeIdStr || '${orderTradeIdStr}')
      $('#certificate-management-keyword-certificateId').combobox("setValue", certificateId || '${certificateId}')
      $('#certificate-management-keyword-buyerCertificateIdStr').textbox("setValue", buyerCertificateId || '${buyerCertificateId}')
    }
    certificate_management_search(void 0, void 0, !isNotSetOrderProps)
  }

  var cert_manage_data = {data1:'', data2: ''};
  function certificate_management_search(isExport, ct = '', isSetOrderProps) {
    if (!isSetOrderProps) delete top.__TEMP_CERT_MANAGEMENT
    const certType = ct || $('#certificate-management-keyword-certType').combobox("getValue");
    let param = {
      exhibitCode: exhibitCode_for_pass,
      buyerName: $('#certificate-management-keyword-buyerName').textbox("getValue"),
      // regNum: $('#certificate-management-keyword-regNum').textbox("getValue"),
      mobile: $('#certificate-management-keyword-mobile').textbox("getValue"),
      email: $('#certificate-management-keyword-email').textbox("getValue"),
      projectId: $('#certificate-management-keyword-projectId').combobox("getValue"),
      certificateId: $('#certificate-management-keyword-certificateId').combobox("getValue"),
      state: $('#certificate-management-keyword-state').combobox("getValue"),
      orderTradeIdStr: $('#certificate-management-keyword-orderTradeIdStr').textbox("getValue"),
      orderRegNum: $('#certificate-management-keyword-orderRegNum').textbox("getValue"),
      orderBuyerName: $('#certificate-management-keyword-orderBuyerName').textbox("getValue"),
      certificateFrom: $('#certificate-management-keyword-certificateFrom').combobox("getValue"),
      zzyuserName: $('#certificate-management-keyword-zzyuserName').textbox("getValue"),
      buyerCertificateIdStr: $('#certificate-management-keyword-buyerCertificateIdStr').textbox("getValue"),
      certType,
      // dataPush: $('#certificate-management-keyword-dataPush').combobox("getValue")
      // isConvertible: $('#certificate-management-keyword-isConvertible').combobox("getValue"),
    }
    param.apiType = $('#certificate-management-keyword-apiType').combobox("getValue")
    param.dataPush = $('#certificate-management-keyword-dataPush').combobox("getValue")
    if(certType === 'C') {
      param.isConvertible = $('#certificate-management-keyword-isConvertible').combobox("getValue")
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (isExport){
      const target = new URL('/eip-web-sponsor/buyerCertificate/exportData', location.origin)
      target.search = new URLSearchParams(g_cert_search_history || param).toString()
      return window.open(target, '_blank')
    }
    $('#certificate-management-datagrid' + (certType == 'A' ? '' : '2')).datagrid({
      url: eippath + '/buyerCertificate/getPage',
      queryParams: {...param, ...top.__TEMP_CERT_MANAGEMENT},
      onLoadSuccess(data) { // {data,rows,total,state}
        if(data.state == 1) {
          $('td[field="rowNo"]').addClass('datagrid-td-rownumber')
          g_cert_search_history = param
          cert_manage_data[certType == 'A' ? 'data1': 'data2'] = data;
        }
      }
    })
  }

  function certificate_management_updateState(buyerId, zzyuserId, buyerCertificateId, state) {
    const _request = () => {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/buyerCertificate/updateState',
        dataType: "json",
        data: {
          buyerId,
          zzyuserId,
          buyerCertificateId,
          state,
        },
        success({state, msg}) {
          if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
          $.messager.alert('提示', '操作成功', 'info')
          certificate_management_search()
        }
      })
    }

    const message = state === 0 ? '是否确认启用证件？' : '是否确认停用证件？证件将无法进行核销。'
    $.messager.confirm('错误', message, r => r && _request())
  }


  function certificateManagementFormatter(_, row) {
    const {state, buyerId, zzyuserId, buyerCertificateId, certificateDel} = row
    if (certificateDel === 1) return ''
    const color = state === 0 ? '#A2A2A2' : '#2E82E4'
    const targetState = state === 0 ? 1 : 0
    const text = state === 0 ? '停用' : '启用'
    const style = `style="color:\${color};font-weight:bold"`
    const fn = `certificate_management_updateState(\${buyerId},\${zzyuserId},'\${buyerCertificateId}',\${targetState})`
    return `<a href="javascript:void(0);" \${style} onclick="\${fn}">\${text}<a>`
  }

  function certificateManagementCheckNumFormatter(val, row) {
    return `<a href="javascript:" style="color: #2E82E4;font-weight:bold;" onclick="certificateManagementDumpCheckRecord('\${row.buyerCertificateCode}')">\${val}</a>`
  }

  function certificateManagementCertificateNameFormatter(val, row) {
    return row.certificateDel === 1 ? `<del style="color: #A2A2A2">\${val}(已删除)</del>` : val
  }

  function certificateManagementCertificateFromFormatter(val) {
    if (!val) return ''
    return (certificateManagementCertificateFrom.find(it => +it.id === val) || {})['text'] || ''
  }

  function certificateManagementPayTypeFormatter(val) {
    if (!val) return ''
    return (certificateManagementPayTypeList.find(it => it.id === val) || {})['text'] || ''
  }

  function certificateManagementDumpCheckRecord(cid) {
    const result = addTab('票证核销记录', '/eip-web-sponsor/backstage/spectator/certificate_check_record?cid=' + cid)
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_check_record_setCid(cid)
  }

  function certificate_management_unbind_wx() {
    const certType = $('#certificate-management-keyword-certType').combobox("getValue");
    const _request = buyerId => {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/buyerCertificate/clearOpenId',
        dataType: "json",
        data: {buyerId},
        success({state, msg}) {
          if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
          $.messager.alert('提示', '操作成功', 'info')
          certificate_management_search()
        }
      })
    }
    const _getCountByBuyerId = (buyerId, callback) => {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/buyerCertificate/getCountByBuyerId',
        dataType: "json",
        data: {buyerId},
        success({data}) {
          callback(data || 1)
        },
        error() {
          callback(1)
        }
      })
    }
    const rows = $('#certificate-management-datagrid' + (certType=='A' ? '': '2')).datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请先选择需要解绑的微信', 'warning')
    // if (rows.length > 1) return $.messager.alert('提示', '解绑微信单次只能解绑一个', 'warning')
    const buyerIds = rows.map(item => item.buyerId).toString()
    _getCountByBuyerId(buyerIds, num => {
      let message = '是否确认解绑微信?'
      if (num > 1) message += '解绑微信将一并解绑该观众下已领取的' + num + '个证件的关联微信'
      $.messager.confirm('提示', message, r => r && _request(buyerIds))
    })
  }

  function certificate_management_check_push() {
    let isNeedPush = false
    if(window.origin.includes('localhost')) {
      isNeedPush = true
    } else {
      $.ajax({
        url: "/RegSever/register/needShowField", //请求服务器url地址.
        type: "post",
        datatype: 'json',
        data: {f_exhibit_code: exhibitCode_for_pass},
        xhrFields: {
          withCredentials: true
        },
        async: false,
        success(data) {
          if (data.state === 1) {
            const {pushField} = data.data || {}
            isNeedPush = pushField || false
          }
        }
      })
    }
    // $('#certificate-management-push-btn')[isNeedPush ? 'show': 'hide']()
    if(!isNeedPush) {
      $('.push-control').addClass('hide')
      $('#certificate-management-push-btn').remove()
      return
    }
    const pushField = {
      field: 'pushStateList',
      align: 'center',
      width: '180px',
      title: '推送状态',
      formatter(v, row) {
        const stateFn = (val) => {
          switch (val) {
            case 0:
              return'推送失败'
            case 1:
              return '推送成功'
            default:
              return '未推送'
          }
        }
        if (Array.isArray(v) && v.length) {
          return v.map(item => {
            return `<a onclick="certificate_management_gotoPushRecord(\`\${row.buyerCertificateId}\` ,\`\${item.apiType}\`)" href="javascript:" style="margin:10px;text-align:left;display:block;color:#2E82E4;font-weight:bold">\${item.apiTypeName} - \${stateFn(item.pushState)}</a>`
          }).join('')
        }
        return `<a onclick="certificate_management_gotoPushRecord(\`\${row.buyerCertificateId}\`)" href="javascript:" style="margin:10px;text-align:left;display:block;color:#2E82E4;font-weight:bold">\${stateFn(row.dataPush)}</a>`
      }
    }
    const datagrid = $('#certificate-management-datagrid')
    const columns = datagrid.datagrid('options').columns
    columns[0].push(pushField)
    datagrid.datagrid({columns})
    const datagrid2 = $('#certificate-management-datagrid2')
    const columns2 = datagrid2.datagrid('options').columns
    columns2[0].push(pushField)
    datagrid.datagrid({columns: columns2})
  }

  function certificate_management_push_submit() {
    const pushTypeWindow = $('#certificate-management-push')
    const data = pushTypeWindow.data('params')
    if (!data.exhibitCode) data.exhibitCode = exhibitCode_for_pass
    data.pushType = $('input[name="certificate-pushType"]:checked').val()
    data.pushApiIds = $('#certificate-pushApiIds').combobox('getValues').filter(Boolean).toString()
    data.certType =  $('#certificate-management-keyword-certType').combobox("getValue");
    $.messager.progress()
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerCertificate/pushData',
      dataType: "json",
      data,
      success({state, msg, data: count}) {
        if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
        $.messager.alert('提示', '操作成功#条数据'.replace('#', count), 'info')
        certificate_management_search()
        pushTypeWindow.window('close').removeData('params')
      }
    }).done(() => $.messager.progress('close'))
  }
  function certificate_management_push() {
    const certType = $('#certificate-management-keyword-certType').combobox("getValue");
    const openWindow = data => $('#certificate-management-push').window('open').data('params', data)
    $('#certificate-pushApiIds').combobox('reload')
    const datagrid = $('#certificate-management-datagrid' + (certType == 'A' ? '' : '2'))
    const rows = datagrid.datagrid('getSelections')

    let tmp1 = 0,tmp2 = 0;
    if(rows.length > 0) {
      tmp1 = rows.length;
      tmp2 = rows.filter(it=> !it.pushStateList || (it.pushStateList ||[]).find(it=> it.pushState != 1)).length;
    } else {
      tmp1 = +cert_manage_data[certType == 'A' ? 'data1' : 'data2'].total || 0;
      tmp2 = +cert_manage_data[certType == 'A' ? 'data1' : 'data2'].data.notPushAndPushFailedNum || 0;
    }
    $('#js-cert-manage-push-all').html('(' + tmp1 +')')
    $('#js-cert-manage-push-lost').html('(' + tmp2 +')')

    if (!rows.length) {
      return $.messager.confirm('提示', '是否推送全部数据？', r => {
        if (!r) return
        openWindow(datagrid.datagrid('options').queryParams)
      })
    }
    const buyerCertificateIds = rows.map(item => item.buyerCertificateId).filter(Boolean).toString()
    openWindow({buyerCertificateIds})
  }

  function certificate_management_gotoPushRecord(buyerCertificateId, apiType) {
    event.stopPropagation()
    top.__TEMP_CERT_PUSH_RECORD = {
      apiType,
      certType: $('#certificate-management-keyword-certType').combobox('getValue') || 'A',
      tableId: buyerCertificateId
    }
    const result = addTab('推送明细', '/eip-web-sponsor/backstage/spectator/certificate_push_record')
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_push_setProps()
  }

</script>
