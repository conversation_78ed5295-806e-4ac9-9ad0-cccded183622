<!--进馆证管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/project.jsp" %>
<%@ page import="java.util.List" %>
<%
  boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
      fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
      fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
      fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
      fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
      fu_reg_pay_order_refund = false,
      fu_section_graph_set_reserve = false, fu_section_graph_set_kywy = false;
  List<String> funCodes = (List<String>) session.getAttribute("funCodes");
  for (String s : funCodes) {
    if (s.equals("fu_section_graph_set_yd")) fu_section_graph_set_yd = true;
    else if (s.equals("fu_section_graph_set_ydbl")) fu_section_graph_set_ydbl = true;
    else if (s.equals("fu_section_graph_set_ht")) fu_section_graph_set_ht = true;
    else if (s.equals("fu_section_graph_set_htbl")) fu_section_graph_set_htbl = true;
    else if (s.equals("fu_section_graph_set_preorder")) fu_section_graph_set_preorder = true;
    else if (s.equals("fu_section_graph_set_contract")) fu_section_graph_set_contract = true;
    else if (s.equals("fu_section_graph_set_payorder")) fu_section_graph_set_payorder = true;
    else if (s.equals("fu_section_graph_set_booth")) fu_section_graph_set_booth = true;
    else if (s.equals("fu_section_graph_set_booth_type")) fu_section_graph_set_booth_type = true;
    else if (s.equals("fu_section_graph_set_draw")) fu_section_graph_set_draw = true;
    else if (s.equals("fu_section_graph_set_reserve")) fu_section_graph_set_reserve = true;
    else if (s.equals("fu_section_graph_set_kywy")) fu_section_graph_set_kywy = true;
    else if (s.equals("fu_reg_pay_order_refund")) fu_reg_pay_order_refund = true;
  }
%>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
    #certificate-order-searchExt .easyui-panel span {
        text-align: right;
    }
</style>
<link rel="stylesheet" href="${eippath}/css/view/reg_pay_order.css?v=23.2">
<table id="certificate-order-datagrid" class="easyui-datagrid"
       data-options="rownumbers:true,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},"
       toolbar="#certificate-order-toolbar"
>
  <thead>
  <tr id="certificate-order-datagrid-tr">
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="certificateName" width="220" formatter="certificate_order_genLinkFn('certificateName')">票证名称</th>
    <th field="orderPayState" width="120" align="center" formatter="certificate_order_orderPayStateFormat">支付状态</th>
    <th field="payType" width="120" align="center" formatter="certificate_order_payTypeFormat">支付方式</th>
    <th field="refundLock" width="120" align="center" formatter="v => certificate_order_refundLockFormat(v)">退款锁定</th>
    <th field="quantity" width="120" formatter="certificate_order_genTextFn('quantity')">购买数量</th>
    <th field="useNumber" width="120" formatter="certificate_order_genLinkFn('myReceiveNumByThisOrder')">已自用</th>
    <th field="receiveNumber" width="120" formatter="certificate_order_genTextFn('transferNotReceiveNum')">转赠中</th>
    <th field="residueNumber" width="120" formatter="certificate_order_genLinkFn('transferReceiveNum')">已转赠</th>
    <th field="checkNumber" width="120" formatter="certificate_order_genTextFn('checkNumber')">已核销</th>
    <th field="orderTradeId" width="165" align="center"
        formatter="(v,r) => `<a style=color:#00f href=javascript:certificate_order_openOrderDetail('\${v}','\${r.projectId}')>\${v}</a>`">
      订单ID
    </th>
    <th field="zzyuserName" width="150" align="center">会员名</th>
    <th field="buyerName" width="150" align="center">提交人姓名</th>
    <th field="regNum" width="150" align="center">提交人登记号码</th>
    <th field="oriAmount" width="150" align="center">订单金额</th>
    <th field="discountPrice" width="150" align="center">优惠金额</th>
    <th field="inviteCode" width="150" align="center">邀请码/优惠码</th>
    <th field="inviteCodeCompanyName" width="150" align="center" formatter="certificate_order_inviteCodeCompanyName">邀请展商名称</th>
    <th field="totalFree" width="150" align="center">付款金额</th>
    <th field="refundAmount" width="120" align="center">退款金额</th>
    <th field="refundTime" width="120" align="center">退款时间</th>
    <th field="refunderName" width="120" align="center">退款操作人</th>
    <!-- v=>v?(v+'张'):'' -->
    <th field="invoiceNumber" width="150" align="center" formatter="certificate_order_invoiceNumber">开票数量</th>
    <th field="createTime" width="150" align="center">订单创建时间</th>
    <th field="timeEnd" width="150" align="center">订单完成时间</th>
<%--    <th field="payType" width="120" align="center"  formatter="certificate_order_payType">支付方式</th>--%>
    <th field="transactionId" width="150" align="center">支付订单ID</th>
    <th field="postAddress" width="300">邮寄详情</th>
    <th field="memo" width="120" align="center">备注</th>
  </tr>
  </thead>
</table>
<div id="certificate-order-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" plain="true"
       onclick="certificate_order_saveOrderCreateOpen()">手动新增订单</a>
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       iconCls="icon-view" plain="true" onclick="certificate_order_view_details()">查看</a>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       iconCls="icon-download-all" plain="true" onclick="certificate_order_search(void 0, true)">导出</a>
    <c:if test="<%=fu_reg_pay_order_refund%>">
      <a href="javascript:void(0);" class="easyui-linkbutton"
         iconCls="icon-refund" plain="true" onclick="certificate_order_batchRefund(void 0, true)">批量线上退款</a>
    </c:if>
    <a href="javascript:void(0);" class="easyui-linkbutton"
       iconCls="icon-view" plain="true" onclick="certificate_order_goManagementBatch()">批量查看领用状态</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;margin-bottom: 18px">会员/绑定手机/绑定邮箱</span>
    <input id="certificate-order-keyword-zzyuserName" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block; ">证件名称</span>
    <input id="certificate-order-keyword-certificateName" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block; ">订单ID</span>
    <input id="certificate-order-keyword-orderTradeIdLike" class="my-text easyui-textbox"
           style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block;">支付状态</span>
    <select id="certificate-order-keyword-orderPayState" class="easyui-combobox" style="width:150px;"
            data-options="panelHeight:'auto'">
      <option value="">全部</option>
      <option value="0">未支付</option>
      <option value="1">已支付</option>
      <option value="2">取消支付</option>
      <option value="3">已退款</option>
    </select>&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block;">退款锁定</span>
    <select id="certificate-order-keyword-refundLock" class="easyui-combobox" style="width:150px;"
            data-options="panelHeight:'auto'">
      <option value="">全部</option>
      <option value="0">未锁定</option>
      <option value="1">已锁定</option>
      <option value="2">已部分退款</option>
    </select>&nbsp;&nbsp;&nbsp;&nbsp;
<%--    <span style="display:inline-block;">支付方式</span>--%>
<%--    <select id="certificate-order-keyword-payType" class="easyui-combobox" style="width:100px;"--%>
<%--            data-options="panelHeight:'auto',onChange(n,o) {--%>
<%--              if(n) {--%>
<%--                $('#certificate-order-keyword-transactionId').textbox({--%>
<%--                  disabled: false,--%>
<%--                  value: ''--%>
<%--                })--%>
<%--              } else {--%>
<%--                $('#certificate-order-keyword-transactionId').textbox({--%>
<%--                  disabled: true,--%>
<%--                  value: ''--%>
<%--                })--%>
<%--              }--%>
<%--            }">--%>
<%--      <option value="">全部</option>--%>
<%--      <option value="0">微信</option>--%>
<%--      <option value="1">支付宝</option>--%>
<%--      <option value="2">网银</option>--%>
<%--      <option value="3">手动到款</option>--%>
<%--      <option value="4">优惠卷</option>--%>
<%--      <option value="5">邀请码</option>--%>
<%--    </select>&nbsp;&nbsp;&nbsp;&nbsp;--%>
<%--    <span style="display:inline-block;margin-bottom: 18px">支付订单ID</span>--%>
<%--    <input id="certificate-order-keyword-transactionId" data-options="disabled: true" class="my-text easyui-textbox" style="width:150px;height:25px"><br/>--%>
    <span style="display:inline-block;margin-bottom: 18px">订单创建时间</span>
    <input id="certificate-order-keyword-orderCreateTimeStart" class="easyui-datebox" style="width:120px;height: 25px;"
           data-options="panelHeight:'auto'"/>
    <span>-</span>
    <input id="certificate-order-keyword-orderCreateTimeEnd" class="easyui-datebox" style="width:120px;height: 25px;"
           data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
    <c:if test="<%=false%>">
      <span style="display:inline-block; ">支付完成时间</span>
      <input id="certificate-order-keyword-orderTimeEndStart" class="easyui-datebox" style="width:120px;height: 25px;"
             data-options="panelHeight:'auto'"/>
      <span>-</span>
      <input id="certificate-order-keyword-orderTimeEndEnd" class="easyui-datebox" style="width:120px;height: 25px;"
             data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
      <span style="display:inline-block; ">订单提交姓名</span>
      <input id="certificate-order-keyword-buyerName" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
      <span style="display:inline-block; ">提交人登记号码</span>
      <input id="certificate-order-keyword-regNum" class="my-text easyui-textbox" style="width:150px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
      <span style="display:inline-block; ">是否邮寄</span>
      <select id="certificate-order-keyword-needPost" class="easyui-combobox" style="width:150px;"
              data-options="panelHeight:'auto'">
        <option value="" selected>全部</option>
        <option value="true">是</option>
        <option value="false">否</option>
      </select>&nbsp;&nbsp;&nbsp;&nbsp;
    </c:if>
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="certificate_order_search()">查询</a>
    <a href="javascript:void(0);" class="easyui-linkbutton" style="height:25px"
       onclick="certificate_order_comboSearch()">组合查询</a>
  </div>
</div>

<div id="certificate-order-refund" class="easyui-dialog" title="线下退款"
     data-options="modal:true,shadow:false,closed:true"
     style="width:400px">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_order_saveOrderRefund()"
       plain="true"
       style="margin-right: 6px;">保存</a>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt"
       iconCls="icon-cancel"
       onclick="$('#certificate-order-refund').window('close')"
       plain="true"
       style="margin-right: 6px;">取消</a>
  </div>
  <div class="alex-form easyui-panel">
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>退款金额</span>
      <span class="form-item__value">
        <input
          id="certificate-order-refund-refundAmount"
          type="text"
          class="easyui-numberbox"
          data-options="min:0,precision:2"
          style="height: 30px;width: 200px;"/>
      </span>
    </div>
  </div>
</div>

<div id="certificate-order-create" class="easyui-dialog" title="手动新增订单"
     data-options="modal:true,shadow:false,closed:true"
     style="width:400px">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_order_saveOrderCreate()"
       plain="true"
       style="margin-right: 6px;">保存</a>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt"
       iconCls="icon-cancel"
       onclick="$('#certificate-order-create').window('close')"
       plain="true"
       style="margin-right: 6px;">取消</a>
  </div>
  <div class="alex-form easyui-panel">
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>会员</span>
      <span class="form-item__value">
        <input
            id="certificate-order-create-zzyuserName"
            type="text"
            class="easyui-textbox"
            data-options="prompt:'会员号/绑定手机/绑定邮箱',"
            style="height: 30px;width: 200px;"/>
      </span>
    </div>
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>项目</span>
      <span class="form-item__value">
        <input class="easyui-combobox"
               id="certificate-order-create-projectId"
               style="height: 30px;width: 200px;"
               data-options="
               prompt:'项目',
               onChange: certificate_order_CreateProjectChange,
               url: '/eip-web-sponsor/certificate/getPayProject',
               loadFilter: data => {
                 const result = data.state === 1 ? data.data: []
                 return result
               },
               queryParams: {exhibitCode:exhibitCode_for_pass},
               panelHeight:'auto',
               valueField:'projectId',
               textField: 'projectName',
               panelMaxHeight:'200px',
               limitToList:true"/>
      </span>
    </div>
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>证件名称</span>
      <span class="form-item__value">
        <input class="easyui-combobox"
               id="certificate-order-create-certificateId"
               style="height: 30px;width: 200px;"
               data-options="
               prompt:'证件名称',
               url: '/eip-web-sponsor/certificate/getPayCertificate',
               loadFilter: data => {
                 const result = data.state === 1 ? data.data: []
                 return result
               },
               queryParams: {projectId: $('#certificate-order-create-projectId').combobox('getValue').toString()},
               panelHeight:'auto',
               valueField:'certificateId',
               textField: 'certificateName',
               panelMaxHeight:'200px',
               limitToList:true"/>
      </span>
    </div>
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>购买数量</span>
      <span class="form-item__value">
        <input
            id="certificate-order-create-quantity"
            type="text"
            class="easyui-numberbox"
            data-options="min:1,precision:0"
            style="height: 30px;width: 200px;"/>
      </span>
    </div>
    <div class="alex-form-item full">
      <span class="form-item__label"><span style="color: #E84646">*</span>订单状态</span>
      <span class="form-item__value">
        <input class="easyui-combobox"
               id="certificate-order-create-orderPayState"
               style="height: 30px;width: 200px;"
               data-options="
               prompt:'订单状态',
               data: [{id:0, text:'未到款'},{id:1, text: '已到款'}],
               panelHeight:'auto',
               valueField:'id',
               textField: 'text',
               panelMaxHeight:'200px',
               limitToList:true"/>
      </span>
    </div>
  </div>
</div>

<style>
  .hide {
    display: none !important;
  }
  .only-show-invite-code {
      display: none;
  }
</style>
<div id="certificate-order-details" class="easyui-dialog" title="观众支付订单"
     data-options="modal:true,shadow:false,closed:true"
     style="width:840px">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_order_updateMemo()"
       plain="true"
       style="margin-right: 6px;">保存</a>
    <c:if test="<%=fu_reg_pay_order_refund%>">
      <a href="javascript:void(0);"
         class="easyui-linkbutton hide-locked"
         iconCls="icon-tool-lock"
         onclick="certificate_order_refundLock(1)"
         plain="true"
         style="margin-right: 6px;">退款锁定</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton hide-unlocked"
         iconCls="icon-tool-unlock1"
         onclick="certificate_order_refundLock(0)"
         plain="true"
         style="margin-right: 6px;">退款解锁</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton hide-unlocked"
         iconCls="icon-refund"
         onclick="certificate_order_openOrderRefund()"
         plain="true"
         style="margin-right: 6px;">线下退款</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton show-online-refund"
         iconCls="icon-refund"
         onclick="certificate_order_openOrderRefundOnline()"
         plain="true"
         style="margin-right: 6px;display: none">线上退款</a>
    </c:if>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt show-unpaid"
       iconCls="icon-select8"
       onclick="certificate_order_manualReceipt()"
       plain="true"
       style="margin-right: 6px;display: none">线下到款</a>
    <%--<a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-refund"
       id="certificate_order_refund_button"
       onclick="certificate_order_refund()"
       plain="true"
       style="margin-right: 6px;">退款</a>--%>
    <a href="javascript:void(0);"
       class="easyui-linkbutton hide-by-manual-receipt"
       iconCls="icon-wechat"
       onclick="certificate_order_queryWxPayOrRefundState()"
       plain="true"
       style="margin-right: 6px;">查询支付状态</a>
  </div>
  <div id="certificate-order-details-order-panel" class="easyui-panel" style="width: 100%" title="订单信息">
    <div class="alex-form">
      <div class="alex-form-item">
        <span class="form-item__label">订单编号</span>
        <span class="form-item__value" key="orderTradeId"></span>
      </div>
      <div class="alex-form-item">
        <span class="form-item__label">订单状态</span>
        <span class="form-item__value" key="orderPayState"></span>
      </div>
      <div class="alex-form-item">
        <span class="form-item__label">会员号</span>
        <span class="form-item__value" key="zzyuserName"></span>
      </div>
      <div class="alex-form-item">
        <span class="form-item__label">订单状态</span>
        <span class="form-item__value" key="refundLock"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">订单创建时间</span>
        <span class="form-item__value" key="createTime"></span>
      </div>
      <div class="alex-form-item full" style="align-items: flex-start">
        <span class="form-item__label">订单证件</span>
        <span class="form-item__value" key="certificateOrderList"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">订单金额</span>
        <span class="form-item__value" key="oriAmount"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">优惠金额</span>
        <span class="form-item__value" key="discountPrice"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">付款金额</span>
        <span class="form-item__value" key="totalFree"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">开票数量</span>
        <span class="form-item__value" key="invoiceNumber"></span>
      </div>
      <div class="alex-form-item  hide js-refundOnly">
        <span class="form-item__label">退款金额</span>
        <span class="form-item__value" key="refundAmount"></span>
      </div>
      <div class="alex-form-item  hide js-refundOnly">
        <span class="form-item__label">退款操作人</span>
        <span class="form-item__value" key="refunderName"></span>
      </div>
      <div class="alex-form-item  hide js-refundOnly">
        <span class="form-item__label">退款单ID</span>
        <span class="form-item__value" key="refunderName"></span>
      </div>
      <div class="alex-form-item  hide js-refundOnly">
        <span class="form-item__label">退款时间</span>
        <span class="form-item__value" key="refundTime"></span>
      </div>
      <div class="alex-form-item full">
        <span class="form-item__label">备注</span>
        <span class="form-item__value">
          <input type="text" class="easyui-textbox" key="memo" multiline="true" style="height: 60px;width: 700px;"/>
        </span>
      </div>
    </div>
  </div>
  <div id="certificate-order-details-wechat-panel" class="easyui-panel hide-by-manual-receipt"
       style="width: 100%;display: none"
       title="支付信息">
    <div class="alex-form">
      <div class="alex-form-item">
        <span class="form-item__label">支付方式</span>
        <span class="form-item__value" key="payTypeName"></span>
      </div>
      <!-- <div class="alex-form-item full"> -->
      <div class="alex-form-item">
        <span class="form-item__label">支付订单ID</span>
        <span class="form-item__value" key="orderId"></span>
      </div>
      <div class="alex-form-item">
        <span class="form-item__label">交易状态</span>
        <span class="form-item__value" key="stateName"></span>
      </div>
      <div class="alex-form-item">
        <span class="form-item__label">支付完成时间</span>
        <span class="form-item__value" key="payTime"></span>
      </div>
			<div class="alex-form-item hide js-refundOnly">
				<span class="form-item__label">退款单ID</span>
				<span class="form-item__value" key="refunderId"></span>
			</div>
      <div class="alex-form-item hide js-refundOnly">
        <span class="form-item__label">退款时间</span>
        <span class="form-item__value" key="refundTime"></span>
      </div>
    </div>
  </div>
</div>

<!-- 组合查询 -->
<div id="certificate-order-searchExt" class="easyui-dialog" title="组合查询"
     data-options="modal:true,shadow:false,closed:true"
     style="width: 750px">
  <div class="my-toolbar-button" style="display: flex;flex-direction: row-reverse">
    <a href="javascript:void(0);"
       class="easyui-linkbutton"
       iconCls="icon-save"
       onclick="certificate_order_comboSearchTrue()"
       plain="true"
       style="margin-right: 6px;">查询</a>
  </div>
  <div class="easyui-panel" title="基本信息">
    <div style="padding: 20px 20px 10px;display: grid;grid-template-columns: 155px 150px 155px 150px;grid-gap: 10px;">
      <span>会员/绑定手机/绑定邮箱</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-zzyuserName" style="width:150px;height:25px">
      </div>
      <span>证件名称</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-certificateName" style="width:150px;height:25px">
      </div>
      <span>订单ID</span>
      <div>
        <input type="text" class="easyui-combobox" id="jsf-cert-order-orderTradeIdLike" style="width:150px;height:25px">
      </div>
      <span>订单状态</span>
      <div>
        <select
            id="jsf-cert-order-orderPayState" class="easyui-combobox" style="width:150px;height:25px"
            data-options="panelHeight:'auto'">
          <option value="">全部</option>
          <option value="0">未支付</option>
          <option value="1">已支付</option>
          <option value="2">取消支付</option>
          <option value="3">已退款</option>
        </select>
      </div>
      <span>订单创建时间</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-datebox" id="jsf-cert-order-orderCreateTimeStart"
               style="width: 180px;height:25px">
        <input type="text" class="easyui-datebox" id="jsf-cert-order-orderCreateTimeEnd"
               style="width: 180px;height:25px">
      </div>
      <span>支付方式</span>
      <div style="grid-column: span 3">
        <select
            class="easyui-combobox"
            data-options="
              multiple:true,
              valueField:'id',
              textField:'text',
              panelHeight:'auto',
              limitToList:true,
              panelMaxHeight:'200px',
              onChange(values) {
                const vStr = values.toString()
                if (vStr !== '5') {
                  $('#jsf-cert-order-inviteCodeCompanyName').textbox('clear')
                  $('.only-show-invite-code').hide()
                }
                if (vStr !== '6') {
                  $('.only-hide-invite-code').hide()
                }
                if (vStr === '5') {
                  $('.only-show-invite-code').show()
                } else if (vStr === '6') {
                  $('.only-show-invite-code.discount-code').show()
                  $('.only-hide-invite-code').show()
                } else {
                  $('#jsf-cert-order-inviteCode').textbox('clear')
                }
              },
              data: certificateOrderPayTypeList"
            id="jsf-cert-order-payTypes"
            style="width: 150px;height:25px">
        </select>
        <label>
          <input type="checkbox" id="jsf-cert-order-useCouponFlag">
          是否使用代金券
        </label>
      </div>
      <span>支付完成时间</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-datebox" id="jsf-cert-order-orderTimeEndStart"
               style="width: 180px;height:25px">
        <input type="text" class="easyui-datebox" id="jsf-cert-order-orderTimeEndEnd"
               style="width: 180px;height:25px">
      </div>
      <span class="only-show-invite-code discount-code">邀请码/优惠码</span>
      <div class="only-show-invite-code discount-code">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-inviteCode" style="width:150px;height:25px">
      </div>
      <span class="only-show-invite-code">邀请码展商</span>
      <div class="only-show-invite-code">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-inviteCodeCompanyName" style="width:150px;height:25px">
      </div>
      <div class="only-hide-invite-code" style="grid-column: span 4;display: none"></div>
      <span>第三方订单ID</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-transactionId" style="width:150px;height:25px">
      </div>
      <span>订单提交姓名</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-order-buyerName" style="width:150px;height:25px">
      </div>
      <%--<span >提交人登记号码</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-order-regNum" style="width:150px;height:25px">
      </div>--%>
      <%--拆分成提交人手机和邮箱--%>
      <span>提交人手机</span>
      <div>
        <input type="text" class="easyui-textbox" id="jsf-cert-order-buyerMobile" style="width:150px;height:25px">
      </div>
      <span>提交人邮箱</span>
      <div style="grid-column: span 3">
        <input type="text" class="easyui-textbox" id="jsf-cert-order-buyerEmail" style="width:150px;height: 25px">
      </div>
      <span>是否邮寄</span>
      <div style="grid-column: span 3">
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-order-needPost"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="true">是</option>
          <option value="false">否</option>
        </select>
      </div>
      <span>领用情况</span>
      <div>
        <select
            class="easyui-combobox"
            data-options="panelHeight:'auto',limitToList:true"
            id="jsf-cert-order-certReceiveState"
            style="width: 150px;height:25px">
          <option value="" selected>全部</option>
          <option value="1">未领用</option>
          <option value="2">部分领用</option>
          <option value="3">全部领用</option>
        </select>
      </div>
  </div>
  </div>
</div>

<script>
  $(function () {
    setTimeout(() => {
      const tmp2 = top.certificate_order_buyer || '';
      if(tmp2) {
        top.certificate_order_buyer = '';
        certificate_order_search(tmp2);
      } else if(end_pos_for_pass && end_pos_for_pass.startsWith('CERT-')) { // crm跳转的
        $('#certificate-order-keyword-orderTradeIdLike').textbox('initValue',end_pos_for_pass.split('-')[1]);
        certificate_order_search();
      } else {
        const tmp = top.certificate_order_ids || '';
        if (tmp) top.certificate_order_ids = '';
        // $('#certificate-order-keyword-orderTradeIdLike').textbox("setValue", tmp);
        certificate_order_setProps(void 0, tmp)
        // certificate_order_search()
      }
    }, 1e2)
  });

  var certificateOrderCreateFormKeys = 'projectId,zzyuserName,certificateId,quantity,orderPayState'.split(',')
  var certificateOrderPayTypeList = [
    {id: '0', text: '微信支付'},
    {id: '1', text: '支付宝支付'},
    {id: '3', text: '线下支付'},
    {id: '4', text: '代金券全额抵扣'},
    {id: '5', text: '邀请码全额免费'},
    {id: '6', text: '优惠码全额免费'},
    {id: '10', text: '小程序支付'},
  ]
  var certificateOrderLastQuery = {}

  function certificate_order_initTable() {
    const tmp = top.certificate_order_ids || ''
    if (tmp) top.certificate_order_ids = ''
    // $('#certificate-order-keyword-zzyuserName').textbox("clear");
    // $('#certificate-order-keyword-certificateName').textbox("clear");
    // $('#certificate-order-keyword-orderTradeIdLike').textbox("clear");
    // $('#certificate-order-keyword-orderPayState').combobox("clear");
    // $('#certificate-order-keyword-transactionId').textbox("clear");
    // $('#certificate-order-keyword-orderCreateTimeStart').datebox("clear");
    // $('#certificate-order-keyword-orderCreateTimeEnd').datebox("clear");
    // $('#certificate-order-keyword-orderTimeEndStart').datebox("clear");
    // $('#certificate-order-keyword-orderTimeEndEnd').datebox("clear");
    certificate_order_search(tmp);
  }

  top.invoiceApply_applicantId = '';
	function certificate_order_goInvoice(zzyuserId) {
		if(!zzyuserId) return;
		top.invoiceApply_applicantId = zzyuserId || ''
    const title = '开票申请汇总'
    if (top.existTopTab(title,true)) {
      top.sever_invoice_initTable()
    } else {
      const href = '/eip-web-sponsor/backstage/sever/invoice_apply'
      top.addTab(title, href,'', 0, true, false);
    }
	}
  function certificate_order_payType(val, row) {
    return ["微信","支付宝","网银","手动到款","优惠卷","邀请码"][val] || '';
  }
  function certificate_order_invoiceNumber(val, row) {
		 return "<a style=\"color:#00f\" href=\"javascript:certificate_order_goInvoice('"+ (row.zzyuserId || '')+ "')\">"+ (val ? '该会员已开'+val + '张' : '') +"</a>";
	}

  function certificate_order_inviteCodeCompanyName(val, row) {
    if (row.inviteCodeId && row.serveCompanyDel === 0) return `<span style="color:#888">展商已被删除</span>`
    return val || ''
  }

  function certificate_order_setProps(inviteCodeClientId, tmp, inviteCodeId) {
    certificate_order_search(
      tmp,
      void 0,
      void 0,
      {
        inviteCodeClientId: inviteCodeClientId || '${inviteCodeClientId}',
        inviteCodeId: inviteCodeId || '${inviteCodeId}',
      }
    )
  }

  function certificate_order_search(zzyuserId, isExport, comboSearchPost, extParams) {

    let param = {
      exhibitCode: exhibitCode_for_pass,
      zzyuserName: $('#certificate-order-keyword-zzyuserName').textbox("getValue"),
      certificateName: $('#certificate-order-keyword-certificateName').textbox("getValue"),
      orderTradeIdLike: $('#certificate-order-keyword-orderTradeIdLike').textbox("getValue"),
      orderPayState: $('#certificate-order-keyword-orderPayState').combobox("getValue"),
      refundLock: $('#certificate-order-keyword-refundLock').combobox("getValue"),
      //transactionId: $('#certificate-order-keyword-transactionId').textbox("getValue"),
      // payType: $('#certificate-order-keyword-payType').combobox("getValue"),
      orderCreateTimeStart: $('#certificate-order-keyword-orderCreateTimeStart').datebox("getValue"),
      orderCreateTimeEnd: $('#certificate-order-keyword-orderCreateTimeEnd').datebox("getValue"),
      // orderTimeEndStart: $('#certificate-order-keyword-orderTimeEndStart').datebox("getValue"),
      // orderTimeEndEnd: $('#certificate-order-keyword-orderTimeEndEnd').datebox("getValue"),
      // buyerName: $('#certificate-order-keyword-buyerName').textbox("getValue"),
      // regNum: $('#certificate-order-keyword-regNum').textbox("getValue"),
      // needPost: $('#certificate-order-keyword-needPost').combobox("getValue"),
    }
    if(zzyuserId) {
      param = {
        exhibitCode: exhibitCode_for_pass,
        orderPayState: 1,
        zzyuserId,
      }
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (comboSearchPost) {
      param = comboSearchPost
    }
    if (isExport) {
      const exportTarget = '/eip-web-sponsor/order/exportData?' + new URLSearchParams(certificateOrderLastQuery).toString()
      window.open(exportTarget, '_blank')
    }
    $('#certificate-order-datagrid').datagrid({
      url: eippath + '/order/getCertificateOrderList',
      queryParams: {...param, ...extParams},
      onLoadSuccess() {
        certificateOrderLastQuery = $(this).datagrid('options').queryParams
      }
    })
  }

  function certificate_order_comboSearch() {
    $('#certificate-order-searchExt').window('open')
  }

  function certificate_order_comboSearchTrue() {
    const getEasyUIClass = ($el) => {
      if (!$el || !$el.attr('class')) return null
      const matcher = $el.attr('class').match(/easyui-(?<type>[a-z]+)/)
      return matcher && matcher.groups && matcher.groups.type
    }
    const post = {
      exhibitCode: exhibitCode_for_pass,
    }
    const add2post = ($el, data) => {
      const key = $el.attr('id').replace('jsf-cert-order-', '')
      const value = String(data || '')
      if (value !== '') post[key] = value
    }
    $('#certificate-order-searchExt .easyui-panel [id^="jsf-cert-order-"]').each(function () {
      const $el = $(this)
      const type = getEasyUIClass($el)
      if (!type) {
        if ($(this).attr('type') === 'checkbox') {
          add2post($el, $el.prop('checked'))
        }
        return
      }
      let optFuncName = 'getValue'
      if (type === 'combobox' && $el.combobox('options').multiple) {
        optFuncName = 'getValues'
      }
      try {
        add2post($el, $el[type](optFuncName))
      } catch (e) {
        console.warn(e)
      }
    })
    certificate_order_search(void 0, void 0, post)
    $('#certificate-order-searchExt').window('close')
  }

  function certificate_order_refund(orderTradeId) {
    const $details = $('#certificate-order-details')
    orderTradeId = orderTradeId || $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    const refund = (orderTradeId) => {
      $.messager.progress()
      $.ajax({
        url: eippath + '/refundOrder/certificateRefundForManage',
        dataType: "json",
        type: "post",
        data: {orderTradeId},
        success(res) {
          let message
          switch (res.state) {
            case 1:
              message = '操作成功！'
              break
            default:
              message = '数据加载失败！'
          }
          $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
          $.messager.progress('close')
          $details.window('close').removeAttr('orderTradeId')
          certificate_order_search()
        },
        error() {
          $.messager.alert('提示', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    const totalFree = $('#certificate-order-details-order-panel').find(`[key="totalFree"]`).text()
    $.messager.confirm('提示', '即将退款￥' + totalFree + ', 是否确认退款？', r => r && refund(orderTradeId))
  }

  function certificate_order_refundLock(refundLock, refundAmount) {
    const $details = $('#certificate-order-details')
    const orderTradeId = $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    const refund = (orderTradeId) => {
      $.messager.progress()
      $.ajax({
        url: eippath + '/order/refundLock',
        dataType: "json",
        type: "post",
        data: {
          orderTradeId,
          refundLock,
          refundAmount
        },
        success(res) {
          let message
          switch (res.state) {
            case 1:
              message = '操作成功！'
              break
            default:
              message = '数据加载失败！'
          }
          $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
          $.messager.progress('close')
          refundAmount && $('#certificate-order-refund').window('close')
          $details.window('close').removeAttr('orderTradeId')
          certificate_order_search()
        },
        error() {
          $.messager.alert('提示', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    if (refundLock === 1) {
      return $.messager.confirm('提示', '是否确认锁定该订单? 锁定订单可以手动发起退款。', r => r && refund(orderTradeId))
    }
    refund(orderTradeId)
  }

  function certificate_order_view_details() {
    const row = $('#certificate-order-datagrid').datagrid('getSelected')
    if (!row) return $.messager.alert('提示', '没有需要操作的数据！', 'warning')
    certificate_order_openOrderDetail(row.orderTradeId)
  }

  function certificate_order_queryWxPayOrRefundState(orderTradeId) {
    orderTradeId = orderTradeId || $('#certificate-order-details').attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    $.messager.progress()
    $.ajax({
      url: eippath + '/weChat/pay/queryWxPayOrRefundState',
      dataType: "json",
      type: "post",
      data: {orderTradeId},
      success(res) {
        let message
        switch (res.state) {
          case 1:
            message = '已更新微信支付信息！'
            break
          default:
            message = '数据加载失败！'
        }
        $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
        $.messager.progress('close')
        const data = res.data
        if (!data) return
        const wrapper = $('#certificate-order-details-wechat-panel')
        Object.keys(data).forEach(key => {
          const el = wrapper.find(`[key="\${key}"]`)
          if (!el.length) return
          el.text(data[key] || '')
        })
        wrapper.panel({closed: false})
      },
      error() {
        $.messager.alert('提示', '数据加载失败！', 'error')
        $.messager.progress('close')
      }
    })
  }

  function certificate_order_orderPayStateFormat(orderPayState, row) {
    const OrderStatusMap = {
      Unpaid: '<span style="color:#A2A2A2">未支付</span>',
      Paid: '<span style="color:#009900">已支付</span>',
      Cancelled: '<span style="color:#A2A2A2">超时取消</span>',
      Refunded: '<span style="color:#FF6C32">已退款</span>',
    }
    return Object.values(OrderStatusMap)[+orderPayState]
  }

  function certificate_order_payTypeFormat(val) {
    if (!val) return ''
    return (certificateOrderPayTypeList.find(item => item.id === val)||{})['text'] || ''
  }

  function certificate_order_refundLockFormat(refundLock) {
    const refundLockState = {
      unlock: '',
      lock: '<span style="color:#FF6C32">已锁定</span>',
      refundPart: '<span style="color:#FF6C32">已部分退款</span>',
    }
    return Object.values(refundLockState)[+refundLock]
  }

  function certificate_order_openOrderDetail(orderTradeId) {
    $('.js-refundOnly').addClass('hide');
    const $details = $('#certificate-order-details')
    const $hideLocked = $details.find('.hide-locked')
    const $hideUnlocked = $details.find('.hide-unlocked')
    orderTradeId = orderTradeId || $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    $.messager.progress()
    $.ajax({
      url: eippath + '/order/getCertificateOrderByIdForManage',
      dataType: "json",
      type: "post",
      data: {orderTradeId},
      success({data, state, msg}) {
        if (state !== 1)
          return $.messager.alert('提示', msg || '数据加载失败', 'error')
        if (!data) return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
        $.messager.progress('close')
        if (+data['refundLock']) {
          $hideUnlocked.show()
          $hideLocked.hide()
        }else {
          $hideLocked.show()
          $hideUnlocked.hide()
        }
        if (+data.orderPayState !== 1) $hideLocked.hide()
        if (+data.refundAmount > 0) $hideUnlocked.hide()
        // $('#certificate_order_refund_button').linkbutton(+data.orderPayState === 3 ? 'disable' : 'enable')
        $('.hide-by-manual-receipt')[+data.payType === 3 ? 'hide' : 'show']()
        $('.show-unpaid')[[0, 2].includes(+data.orderPayState) ? 'show' : 'hide']()
        const showOnlineRefund = ![3, 5, 6].includes(+data.payType) && data['refundLock'] !== 1 && +data.orderPayState === 1
        $('.show-online-refund')[showOnlineRefund ? 'show' : 'hide']()
        ;if(+data.orderPayState == 3) {
          $('.js-refundOnly').removeClass('hide');
        };
        Object.keys(data).forEach(key => {
          const el = $('#certificate-order-details-order-panel').find(`[key="\${key}"]`)
          if (!el.length) return
          const value = data[key]
          if (key === 'orderPayState')
            el.html(certificate_order_orderPayStateFormat(value, data))
          else if (key === 'refundLock')
            el.html(certificate_order_refundLockFormat(value))
          else if (key === 'memo')
            el.textbox('setValue', value)
          else if (key === 'certificateOrderList') {
            const html = (value || []).map(item => {
              const {certificateName, receiveNumber, quantity, checkNumber} = item
              return `<span style=margin-right:20px>\${certificateName}</span><span style=margin-right:20px>×\${quantity}</span><span>已领证\${receiveNumber||0}人 已核销\${checkNumber||0}</span>`
            }).join('<br>')
            el.html(html)
          } else if (key === 'discountPrice') {
            el.text(value ? '已抵扣' + value : '0')
          } else el.text(value !== void 0 && value !== null ? value : '')
        })
        $('#certificate-order-details-wechat-panel').panel({closed: true})
        $details.window('open').attr({orderTradeId})
      },
      error() {
        $.messager.alert('提示', '数据加载失败！', 'error')
        $.messager.progress('close')
      }
    })
  }

  function certificate_order_updateMemo(orderTradeId) {
    const $details = $('#certificate-order-details')
    orderTradeId = orderTradeId || $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    const memo = $('#certificate-order-details-order-panel').find(`[key="memo"]`).textbox('getValue')
    const updateMemo = (orderTradeId) => {
      $.messager.progress()
      $.ajax({
        url: eippath + '/order/updateMemo',
        dataType: "json",
        type: "post",
        data: {orderTradeId, memo},
        success(res) {
          let message
          switch (res.state) {
            case 1:
              message = '操作成功！'
              break
            default:
              message = '数据加载失败！'
          }
          $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
          $.messager.progress('close')
          $details.window('close').removeAttr('orderTradeId')
          certificate_order_search()
        },
        error() {
          $.messager.alert('提示', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    updateMemo(orderTradeId)
  }

  function certificate_order_manualReceipt(orderTradeId) {
    const $details = $('#certificate-order-details')
    orderTradeId = orderTradeId || $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    const manualReceipt = (orderTradeId) => {
      $.messager.progress()
      $.ajax({
        url: eippath + '/order/manualReceipt',
        dataType: "json",
        type: "post",
        data: {orderTradeId},
        success(res) {
          let message
          switch (res.state) {
            case 1:
              message = '操作成功！'
              break
            default:
              message = '数据加载失败！'
          }
          $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
          $.messager.progress('close')
          $details.window('close').removeAttr('orderTradeId')
          certificate_order_search()
        },
        error() {
          $.messager.alert('提示', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    $.messager.confirm('提示', '确定手动到款吗？', r => r && manualReceipt(orderTradeId))
  }

  function certificate_order_genLinkFn(picker) {
    return function (_, row) {
      const certificateOrderList = row.certificateOrderList || []
      if(picker === 'certificateName') {
        return '<div style="padding: 5px;line-height: 22px">' +
          certificateOrderList.map(item => {
            return item.certificateDel === 1 ? `<del style="color: #A2A2A2">\${item[picker]}(已删除)</del>` : item[picker]
          }).join('<br>')  +
          '</div>'
      }
      let html = ''
      if (picker === 'myReceiveNumByThisOrder') {
        html = certificateOrderList.map(item => {
          let text = item[picker] ? item[picker] : '　'
          if (item['myReceiveStopNumByThisOrder'] > 0) text += ' (已停用' + item['myReceiveStopNumByThisOrder'] + ')'
          return `<a href="javascript:"
                 style="color: #2e82e4;font-weight:bold;"
                 onclick="certificate_order_goManagement('\${item.orderTradeId}','\${item.certificateId}', {zzyuserId: \${row.zzyuserId}, certType: '\${item.certType}'})">\${text}</a>`
        }).join('<br>')
      } else if (picker === 'transferReceiveNum') {
        html = certificateOrderList.map(item => {
          let text = item[picker] ? item[picker] : '　'
          if (item['transferReceiveStopNum'] > 0) text += ' (已停用' + item['transferReceiveStopNum'] + ')'
          return `<a href="javascript:"
                 style="color: #2E82E4;font-weight:bold;"
                 onclick="certificate_order_goManagement('\${item.orderTradeId}','\${item.certificateId}', {notZzyuserId: \${row.zzyuserId}, certType: '\${item.certType}'})">\${text}</a>`
        }).join('<br>')
      }
      return '<div style="padding: 5px;line-height: 22px">' + html + '</div>'
    }
  }

  function certificate_order_genTextFn(picker) {
    return function (_, row) {
      const certificateOrderList = row.certificateOrderList || []
      const html = certificateOrderList.map(item => item[picker] ? item[picker] : '　').join('<br>')
      return '<div style="padding: 5px;line-height: 22px">' + html + '</div>'
    }
  }

  function certificate_order_openOrderRefund() {
    const totalFree = $('#certificate-order-details-order-panel').find(`[key="totalFree"]`).text()
    $('#certificate-order-refund-refundAmount').numberbox({min: 0, max: totalFree})
    $('#certificate-order-refund').window('open')
  }

  function certificate_order_startRefundOnline(data, resolve) {
    const {otherReceiveAndUnCheck, myReceiveAndUnCheck, existIntegralOrGoodsFlag, invoiceNumber, ptInvoiceNumber, receiveAndCheck, totalFree} = data
    const config = {
      actionTitle: '线上退款将会进行以下动作',
      otherReceiveAndUnCheck: '转赠未核销的<span style="color:rgb(214,34,71)">%d个证件</span>将自动退领',
      myReceiveAndUnCheck: '自用未核销的<span style="color:rgb(214,34,71)">%d个证件</span>将自动退领',
      existIntegralOrGoodsFlag: '购买证件发放的积分、积分商品将被回退',
      manualProcessTitle: '以下内容需要手动处理',
      invoiceNumber: '该会员已开的<span style="color:rgb(214,34,71)">%d张观众个人发票</span>',
      ptInvoiceNumber: '该会员已使用票通系统开的<span style="color:rgb(214,34,71)">%d张观众个人发票</span>',
      receiveAndCheck: '已被领用且已经过核销的<span style="color:rgb(214,34,71)">%d个证件</span>',
    }
    const isDelActionTitle = Object
      .entries({otherReceiveAndUnCheck, myReceiveAndUnCheck, existIntegralOrGoodsFlag})
      .map(([k, v]) => !v ? void (delete config[k]) : (config[k] = config[k].replace('%d', v)))
      .filter(Boolean)
      .length === 0
    if (isDelActionTitle) delete config.actionTitle
    const isDelManualProcessTitle = Object
      .entries({invoiceNumber, receiveAndCheck, ptInvoiceNumber})
      .map(([k, v]) => !v ? void (delete config[k]) : (config[k] = config[k].replace('%d', v)))
      .filter(Boolean)
      .length === 0
    if (isDelManualProcessTitle) delete config.manualProcessTitle
    if (isDelActionTitle && isDelManualProcessTitle) return certificate_order_refund(orderTradeId)
    const html = Object.entries(config).map(([k, v]) => {
      return k.endsWith('Title') ?
        `<li style="margin-bottom: 10px;font-size: 18px;">\${v}</li>` :
        `<li style="list-style:initial;margin-left: 20px;margin-bottom: 5px;">\${v}</li>`
    }).join('')
    const dlg = $.messager.confirm({
      title: '提示',
      width: '450px',
      content: `<div style="position: absolute" class="messager-icon messager-warning"></div><ul style="margin-left: 40px;">\${html}</ul><div style="clear:both;"/>`,
      buttons: [
        {
          text: '取消',
          onClick() {
            dlg.dialog('close')
          },
        },
        {
          text: '确认退款',
          onClick() {
            dlg.dialog('close')
            resolve && resolve()
          },
        },
      ]
    })
  }

  function certificate_order_openOrderRefundOnline() {
    const $details = $('#certificate-order-details')
    const orderTradeId = $details.attr('orderTradeId')
    if (!orderTradeId)
      return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
    $.messager.progress()
    $.ajax({
      url: eippath + '/order/getCertificateOrderByIdForManage',
      dataType: "json",
      type: "post",
      data: {orderTradeId},
      success({data, state, msg}) {
        if (state !== 1)
          return $.messager.alert('提示', msg || '数据加载失败', 'error')
        if (!data) return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
        $.messager.progress('close')
        certificate_order_startRefundOnline(data, () => certificate_order_refund(orderTradeId))
      },
      error() {
        $.messager.alert('提示', '数据加载失败！', 'error')
        $.messager.progress('close')
      }
    })
  }

  function certificate_order_batchRefund() {
    const rows = $('#certificate-order-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请至少选择一条数据！', 'warning')
    $.messager.confirm('提示', `已选择\${rows.length}个线上支付订单，是否确认批量线上退款订单?`, r => {
      if (!r) return
      const orderTradeIds = rows.map(item => item.orderTradeId).toString()
      $.messager.progress()
      $.ajax({
        url: eippath + '/order/getInfoBeforeBatchRefund',
        dataType: "json",
        type: "post",
        data: {orderTradeIds },
        success({data, state, msg}) {
          if (state !== 1)
            return $.messager.alert('提示', msg || '数据加载失败', 'error')
          if (!data) return $.messager.alert('提示', '数据异常，请刷新重试！', 'warning')
          $.messager.progress('close')
          certificate_order_startRefundOnline(data, () => {
            $.messager.progress()
            $.ajax({
              url: eippath + '/refundOrder/certificateBatchRefundForManage',
              dataType: "json",
              type: "post",
              data: {orderTradeIds},
              success(res) {
                let okMessage = '操作完成<br>'
                const {successNum, failedNum, failedReason} = res.data
                if (successNum) okMessage += '成功退款' + successNum + '个订单<br>'
                if (failedNum) {
                  okMessage += '退款失败' + failedNum + '个订单<br>'
                  if (failedReason && Object.keys(failedReason).length) {
                    okMessage += '<br/>失败详情：<br/>'
                    Object.entries(failedReason).forEach(([k, v]) => {
                      okMessage += k + ':<br>'
                      okMessage += v.join('<br/>') + '<br>'
                    })
                  }
                }
                okMessage = '<div style="max-height: 300px">' + okMessage + '</div>'
                let message
                switch (res.state) {
                  case 1:
                    message = okMessage || '操作成功！'
                    break
                  default:
                    message = '数据加载失败！'
                }
                $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
                $.messager.progress('close')
                certificate_order_search()
              },
              error() {
                $.messager.alert('提示', '数据加载失败！', 'error')
                $.messager.progress('close')
              }
            })
          })
        },
        error() {
          $.messager.alert('提示', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    })
  }

  function certificate_order_saveOrderRefund() {
    const refundAmount = $('#certificate-order-refund-refundAmount').numberbox('getValue')
    if (refundAmount === '' || +refundAmount < 0) return $.messager.alert('提示', '请输入退款金额', 'warning')
    const totalFree = $('#certificate-order-details-order-panel').find(`[key="totalFree"]`).text()
    if (+refundAmount > totalFree) return $.messager.alert('提示', `退款金额必须小于等于付款金额`, 'warning')
    certificate_order_refundLock(2, +refundAmount)
  }

  function certificate_order_goManagement(orderTradeIdStr, certificateId, ext) {
    event.stopPropagation();
    delete ext['certType']
    top.__TEMP_CERT_MANAGEMENT = {
      orderTradeIdStr,
      certificateId,
      ...ext
    }
    const result = addTab('票证管理', '/eip-web-sponsor/backstage/spectator/certificate_management')//?cid=' + certificateId + '&oid=' + orderTradeIdStr)
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_management_setProps(/*orderTradeIdStr, certificateId*/)
  }

  function certificate_order_goManagementBatch() {
    event.stopPropagation();
    const params = $('#certificate-order-datagrid').datagrid('options').queryParams
    const payload = {}
    Object.keys(params).forEach(key => {
      payload[`orderTradeDto.` + key] = params[key]
    })
    top.__TEMP_CERT_MANAGEMENT = payload
    const result = addTab('票证管理', '/eip-web-sponsor/backstage/spectator/certificate_management')
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_management_setProps()
  }

  function certificate_order_getEasyUIControlType($el) {
    if (!$el || !$el.attr('class')) return null
    const matcher = $el.attr('class').match(/easyui-(?<type>[a-z]+)/)
    return matcher && matcher.groups && matcher.groups.type
  }

  function certificate_order_saveOrderCreate() {
    const postData = {}
    certificateOrderCreateFormKeys.forEach(key => {
      const $item = $('#certificate-order-create-' + key)
      const type = certificate_order_getEasyUIControlType($item)
      switch (type) {
        case 'textbox':
          postData[key] = $item.textbox('getValue')
          break
        case 'numberbox':
          postData[key] = $item.numberbox('getValue')
          break
        case 'combobox':
          postData[key] = $item.combobox('getValue')
          break
        default:
          postData[key] = $item.val()
      }
    })
    if (!postData.zzyuserName) return $.messager.alert('提示', '请输入会员号/绑定手机/绑定邮箱', 'warning')
    if (!postData.projectId) return $.messager.alert('提示', '请选择项目', 'warning')
    if (!postData.certificateId) return $.messager.alert('提示', '请选择证件名称', 'warning')
    if (!postData.quantity) return $.messager.alert('提示', '请输入购买数量', 'warning')
    if (!postData.orderPayState) postData.orderPayState = 0
    $.messager.progress()
    $.ajax({
      url: eippath + '/order/createCertificateOrderForManager',
      dataType: "json",
      type: "post",
      data: postData,
      success(res) {
        let message
        switch (res.state) {
          case 1:
            message = '操作成功！'
            break
          default:
            message = '数据加载失败！'
        }
        $.messager.alert('提示', res.msg || message, res.state === 1 ? 'info' : 'error')
        $.messager.progress('close')
        if (res.state !== 1) return
        $('#certificate-order-create').window('close')
        certificate_order_search()
      },
      error() {
        $.messager.alert('提示', '数据加载失败！', 'error')
        $.messager.progress('close')
      }
    })
  }

  function certificate_order_saveOrderCreateOpen() {
    certificateOrderCreateFormKeys.forEach(key => {
      const $item = $('#certificate-order-create-' + key)
      const type = certificate_order_getEasyUIControlType($item)
      $item.length && type && $item[type]('clear')
    })
    $('#certificate-order-create').window('open')
  }

  function certificate_order_CreateProjectChange(projectId) {
    $('#certificate-order-create-certificateId').combobox({queryParams: {projectId}})
  }
</script>
