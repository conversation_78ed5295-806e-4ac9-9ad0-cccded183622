<%--
  Created by IntelliJ IDEA.
  User: Rex_Surprise
  Date: 2021-03-23
  Time: 9:11
  Except for the notes here.
  I wrote none of the notes below.
  The latecomer asks for more happiness  QAQ~
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<script src="${eippath}/js/jquery.qrcode.logo.ex.js" type="text/javascript" charset="utf-8"></script>
<style type="text/css">
  .activity-reg-toolbar {
    align-items: center;
    display: flex;
    height: 40px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #e0ecff;
    border-radius: 3px;
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
</style>
<style type="text/css">
  .all_address, .channel {
    box-sizing: border-box;
    padding-bottom: 10px;
    margin: 8px 30px 0 30px;
    border-bottom: 1px solid #ccc;
  }
  .channel .label{
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    padding-top: 10px;
    height: 30px;
  }

  .all_address h6, .channel h6{
    font-size: 16px;
    font-weight: normal;
    color: #333333;
    margin-bottom: 8px;
  }

  .all_address textarea {
    width: 100%;
    border: none;
    resize: none;
    outline: none;
    font-size: 14px;
    color: #808080;
  }

  .btn-primary {
    width: 84px;
    height: 32px;
    border-radius: 6px;
    font-size: 14px;
    border: solid 1px #4c72fe;
    color: #4c72fe;
    background-color: #fff;
    outline: none;
    cursor: pointer;
  }

  .all_address > a {
    width: 84px;
    height: 30px;
    background-color: #fff;
    border-radius: 6px;
    border: solid 1px #999999;
    display: inline-block;
    text-align: center;
    line-height: 30px;
    margin-left: 20px;
    color: #666666;
  }

  .all_address > a:hover {
    color: #666666;
  }

  .address_download {
    overflow: hidden;
    margin: 12px 30px 15px;
  }

  .address_download ul li {
    float: left;
    margin-right: 20px;
  }

  .am-btn {
    font-size: 14px;
    width: 100%;
    color: #333333;
    border: none;
    background-color: #fff;
    height: 26px;
    cursor: pointer;
    outline: none;
  }

  .prompt_text {
    width: 144px;
    font-size: 16px;
    line-height: 26px;
    color: #333333;
    margin-left: 14px;
    margin-top: 29px;
  }

  .register-exhibitionReg, .setdiv, .register-releaseReg, .register-address {
    width: 288px !important;
    height: 216px !important;
    border-radius: 10px;
    border: solid 1px #4c72fe;
    transition: transform 0.6s;
  }

  .register-exhibitionReg :hover img, .setdiv :hover img, .register-releaseReg :hover img, .register-address :hover img {
    transform: scale(1.1);
  }

  .detialdiv a {
    font-size: 24px;
    color: #4c72fe;
    display: block;
    margin-top: 20px;
  }

  .detialdiv img {
    margin: auto;
    margin-top: 42px;
  }
</style>
<div class="datagrid-toolbar activity-reg-toolbar" id="activity-reg-toolbar-2">
  <div class="my-toolbar-search">
    <label for="activity-reg-search-project-2">项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
    <input
        id="activity-reg-search-project-2"
        class="easyui-textbox"
        data-options="
          inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents, {
            keypress: function(e) {
               if(e.key === 'Enter')
                activityRegSearch(2)
            }
        })"
        style="width:200px;height: 25px;">
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
       onclick="ARSRegSearch(2)">查询</a>
  </div>
</div>
<div class="datagrid-toolbar activity-reg-toolbar" id="activity-reg-toolbar-3">
  <div class="my-toolbar-search">
    <label for="activity-reg-search-project-3">项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
    <input
        id="activity-reg-search-project-3"
        class="easyui-textbox"
        data-options="
          inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents, {
            keypress: function(e) {
               if(e.key === 'Enter')
                activityRegSearch(3)
            }
        })"
        style="width:200px;height: 25px;">
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
       onclick="ARSRegSearch(3)">查询</a>
  </div>
</div>
<div class="easyui-tabs" id="tab-panel" style="width:100%;height:100%;padding:5px;" data-options="tabPosition:'top'">
  <div title="供应商表单设置" style="width:100%;height:100%;">
    <table
        id="activity-reg-datagrid-2"
        toolbar="#activity-reg-toolbar-2"
        data-options="
            rownumbers:true,
            singleSelect:true,
            pageSize:20,
            pagination:false,
            multiSort:true,
            fitColumns:true,
            fit:true,
            method:'post',
            selectOnCheck:true,
            onClickRow: function (rowIndex, rowData) {
              var l = $(this).datagrid('getRows').length;
              for(var i = 0; i < l; i++){
                if(i != rowIndex)$(this).datagrid('unselectRow', i);
              }
            },">
      <thead>
      <tr>
        <th data-options="field:'projectName',width:80,align:'center'">项目名称</th>
        <th data-options="field:'isMain',width:50,align:'center'" formatter="ARSRegIsMain">是否主项目</th>
        <th data-options="field:'regCutoffDate',width:80,align:'center'">登记截止日期</th>
        <th data-options="field:'1',width:80,align:'center'" formatter="ARSRegSetBtn">登记设置</th>
        <th data-options="field:'releaseReg',width:80,align:'center'" formatter="ARSRegReleaseBtn">发布</th>
        <th data-options="field:'2',width:80,align:'center'" formatter="ARSRegAddressBtn">中文登记地址</th>
        <%--        <th data-options="field:'2',width:80,align:'center'" formatter="ARSRegAddressBtnEn">英文登记地址</th>--%>
        <th data-options="field:'synData',width:80,align:'center'" formatter="ARSRegSynDataBtn">数据同步</th>
      </tr>
      </thead>
    </table>
  </div>
  <div title="采购商表单设置" style="width:100%;height:100%;">
    <table
        id="activity-reg-datagrid-3"
        toolbar="#activity-reg-toolbar-3"
        data-options="
            rownumbers:true,
            singleSelect:true,
            pageSize:20,
            pagination:false,
            multiSort:true,
            fitColumns:true,
            fit:true,
            method:'post',
            selectOnCheck:true,
            onClickRow: function (rowIndex, rowData) {
              var l = $(this).datagrid('getRows').length;
              for(var i = 0; i < l; i++){
                if(i != rowIndex)$(this).datagrid('unselectRow', i);
              }
            },">
      <thead>
      <tr>
        <th data-options="field:'projectName',width:80,align:'center'">项目名称</th>
        <th data-options="field:'isMain',width:50,align:'center'" formatter="ARSRegIsMain">是否主项目</th>
        <th data-options="field:'regCutoffDate',width:80,align:'center'">登记截止日期</th>
        <th data-options="field:'1',width:80,align:'center'" formatter="ARSRegSetBtn">登记设置</th>
        <th data-options="field:'releaseReg',width:80,align:'center'" formatter="ARSRegReleaseBtn">发布</th>
<%--        <th data-options="field:'2',width:80,align:'center'" formatter="ARSRegAddressBtn">中文登记地址</th>--%>
        <th data-options="field:'2',width:80,align:'center'" formatter="ARSRegAddressBtnEn">英文登记地址</th>
        <th data-options="field:'synData',width:80,align:'center'" formatter="ARSRegSynDataBtn">数据同步</th>
      </tr>
      </thead>
    </table>
  </div>
</div>

<div id="activity-reg-address-window"
     class="easyui-window"
     data-options="
      closed:true,
      title:'登记地址',
      modal:true,
      maximizable: false,
      resizable: false,
      minimizable: false,
      shadow:false,
      top:80"
     style="width:600px;">
  <div id="activity-reg-address-panel-1" class="easyui-panel" title="">
    <div class="channel">
      <h6>登记渠道设置</h6>
      <label class="label">
        <span>渠道名称</span>&nbsp;&nbsp;
        <input class="easyui-combobox" id="activity-reg-channel-name" style="width: 200px;height: 25px;" data-options="
          valueField:'channelId',
          textField:'channelName',
          limitToList: false,
          panelHeight:'auto',
          panelMaxHeight:'200px'">

      </label>
    </div>
    <div class="all_address">
      <h6>手机登记地址</h6>
      <textarea cols="50" rows="3" id="activity-reg-address-phone" readonly="readonly"></textarea>
      <button class="btn-primary" onclick="ARSCopyText('activity-reg-address-phone')">复制链接</button>
      <a id="activity-reg-address-phone-open" href="" target="_blank">直接打开</a>
    </div>
    <!-- <div class="all_address">
       <h6>手机登记地址（单层）</h6>
       <textarea cols="50" rows="3" id="register-address-phoneSingle" readonly="readonly" ></textarea>
       <button class="btn-primary" onclick="copySingleText()">复制链接</button>
       <a id="register-address-phoneSingle-open" href="" target="_blank" >直接打开</a>
    </div> -->
    <div class="all_address">
      <h6>电脑登记地址</h6>
      <textarea cols="50" rows="3" id="activity-reg-address-computer" readonly="readonly"></textarea>
      <button class="btn-primary" onclick="ARSCopyText('activity-reg-address-computer')">复制链接</button>
      <a id="activity-reg-address-computer-open" href="" target="_blank">直接打开</a>
    </div>
    <div class="address_download">
      <ul>
        <li>
          <div id="activity-reg-address-code" class="qr_code"></div>
          <div class="address_download_button">
            <a id="activity-reg-address-download" download="qrcode.jpg"></a>
            <button id="activity-reg-address-saveQrCode" class="am-btn" onclick="ARSDownload()">下载</button>
          </div>
        </li>
        <li>
          <div class="prompt_text">
            <p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>
<!--发布展会-->
<div id="activity-reg-release-window"
     class="easyui-window"
     data-options="
      closed:true,
      title:'发布展会',
      modal:true,
      maximizable: false,
      resizable: false,
      minimizable: false"
     style="width:500px;height:200px">
  <div>
    <div style="margin-top: 15px;text-align: center">
      <label><span style="color: red;">*</span>截止时间</label>
      <input id="activity-reg-release-regcuttof" class="easyui-datetimebox" style="width:200px;height:25px;">
      <button type="button" onclick="ARSSaveRegDate()">保存截止时间</button>
    </div>

    <div style="margin-top: 10px;text-align: center">
      <p style="color: black;">提示：<span style="color: red;">发布前请设置截止时间</span></p>
    </div>
    <div style="margin-top: 15px;text-align: center">
      <button type="button" onclick="ARSSaveRelease()" id="register-release-release">发布展会</button>
    </div>
  </div>
</div>
<!--数据同步-->
<div id="activity-reg-open-team-window" class="easyui-window" title="数据同步" data-options="modal:true,closed:true"
     style="width:400px;height:auto;padding:2px;">
  <div id="activity-reg-open-team-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save"
         onclick="ARSSaveSynData()" plain="true" style="float:right;margin-right: 6px;">确定</a>
    </div>
  </div>
  <div id="activity-reg-open-team-panel" class="easyui-panel" title="" style="width:100%;height:100px;padding:15px;">
    <ul style="list-style:none;margin:0;padding:0;">
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        <input id="activity-reg-project-team" class="easyui-combobox" style="width:100%;height:100%"
               data-options="label:'请选择团队:',valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
        <p style="text-align: center; margin-top: 10px;padding-left: 54px;color: red;">同时同步到数据总览中</p>
      </li>
    </ul>

  </div>
</div>
<script>
  const projectId = project_for_pass
  const exhibitCode = exhibitCode_for_pass
  const orgNum = org_num_for_pass

  function ARSRegSetBtn(val, row) {
    return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSOpenRegSet(' +
      row.projectId + ', ' + row.targetType + ')">登记设置</a>';
  }

  function ARSOpenRegSet(projectId, target) {
    var regSet = variableReg +
      '/RegSever/RegPage/pages/pms_set_reg_eip_activity.html?orgnum=' + orgNum +
      '&target=' + target +
      '&pid=' + projectId;
    window.open(regSet, '_blank');
  }

  function ARSRegReleaseBtn(val, row) {
    const releaseReg = row.releaseReg
    if (releaseReg) {
      return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSRevoke(' +
        row.projectId + ', ' +
        row.targetType + ')">撤销</a>';
    } else {
      return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSOpenReleaseWindow(\'' +
        row.regCutoffDate + '\',' +
        row.projectId + ', ' +
        row.targetType + ')">发布</a>';
    }
  }

  function ARSOpenReleaseWindow(regCutoffDate, projectId, targetType) {
    const $releaseWindow = $('#activity-reg-release-window')
    $releaseWindow.attr('projectId', projectId)
    $releaseWindow.attr('targetType', targetType)
    if (regCutoffDate == null || regCutoffDate == "null") {
      regCutoffDate = '';
    }
    $("#activity-reg-release-regcuttof").textbox('setValue', regCutoffDate);
    $releaseWindow.window('open');
  }

  function ARSRevokeRelease(projectId, targetType, isRelease) {
    const releaseReg = !!isRelease
    const msgText = releaseReg ? '发布' : '撤销发布'
    $.ajax({
      url: '/RegSever/projRegSet/setReleaseReg',
      data: {
        projectId,
        targetType,
        releaseReg
      },
      type: "post",
      dataType: 'json',
      success(data) {
        if (data.state === 2) return $.messager.alert('错误', '请先保存截止日期！', 'error')
        if (data.state !== 1) return $.messager.alert('错误', msgText + '失败！', 'error')
        $.messager.alert('提示', msgText + '成功！', 'info')
        $('#activity-reg-release-window').window('close');
        ARSQueryTableData(Number(targetType))
      },
      error(e) {
        $.messager.alert('错误', msgText + '失败！', 'error')
        console.warn(e);
      }
    })
  }

  function ARSRevoke(projectId, targetType) {
    $.messager.confirm('提示', '确定撤销发布吗？', r => {
      if (!r) return
      const isRelease = false
      ARSRevokeRelease(projectId, targetType, isRelease)
    })
  }

  function ARSSaveRegDate() {
    const $releaseWindow = $('#activity-reg-release-window')
    const projectId = $releaseWindow.attr('projectId')
    const targetType = $releaseWindow.attr('targetType')
    if (!projectId || !targetType) return console.warn('数据：projectId或targetType异常！')
    const msgText = '保存'
    const regCutoffDate = $("#activity-reg-release-regcuttof").textbox('getValue');
    if (regCutoffDate == '') return $.messager.alert('提示', '请选择时间！', 'warning')
    $.ajax({
      url: '/RegSever/projRegSet/setRegCutoffDate',
      data: {
        projectId,
        targetType,
        regCutoffDate
      },
      type: "post",
      dataType: 'json',
      success(data) {
        if (data.state !== 1) return $.messager.alert('错误', data.msg || msgText + '失败！', 'error')
        $.messager.alert('提示', msgText + '成功！', 'info')
      },
      error(e) {
        $.messager.alert('错误', msgText + '失败！', 'error')
        console.warn(e);
      }
    })
  }

  function ARSSaveRelease() {
    const $releaseWindow = $('#activity-reg-release-window')
    const projectId = $releaseWindow.attr('projectId')
    const targetType = $releaseWindow.attr('targetType')
    const isRelease = true
    if (!projectId || !targetType) return console.warn('数据：projectId或targetType异常！')
    ARSRevokeRelease(projectId, targetType, isRelease)
  }

  function ARSRegAddress(row, isEn) {
    const en = Number(!!isEn)
    return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSOpenRegAddress(' +
      orgNum + ', ' +
      row.projectId + ', ' +
      row.targetType + ',' + en + ')">登记地址</a>'
  }

  function ARSRegAddressBtn(val, row) {
    return ARSRegAddress(row)
  }

  function ARSGetLogoImage(f_project_id) {
    let qrCodeLogo = ''
    $.ajax({
      url: '/RegSever/projRegTemp/selectAllTemp',
      data: {f_project_id},
      type: "post",
      async: false,
      xhrFields: {withCredentials: true},
      success(data) {
        if (data.state !== 1) return $.messager.alert('提示', '获取Logo失败', 'error')
        const qrCodeImg = data.data['f_2code_img']
        if (qrCodeImg != "" && qrCodeImg != null) {
          qrCodeLogo = qrCodeImg
        }
      },
      error(e) {
        $.messager.alert('提示', '获取Logo失败', 'error')
      }
    })
    if(!qrCodeLogo)return '/RegSever/RegPage/images/top-lucency.png'
    return qrCodeLogo.startsWith('http')  ? qrCodeLogo : '/image/' + qrCodeLogo
  }

  function ARSOpenRegAddress(orgNum, projectId, targetType, isEn) {
    const $ARSAddressCode = $("#activity-reg-address-code")
    const $ARSAddressWindow = $('#activity-reg-address-window')
    const $ARSChannelName = $('#activity-reg-channel-name')
    const __DATA = []
    $ARSChannelName.combobox({
      url: eippath + '/channel/getList',
      queryParams: {projectId: project_for_pass},
      onLoadSuccess(data){
        if(data.length){
          data.forEach(it=>__DATA.push(it))
          $(this).combobox('setValue', data[0].channelId)
        }
      },
      onChange(nv, ov){
        const _CURRENT = __DATA.find(_T=>_T.channelId === nv)
        _CURRENT && setLinker(_CURRENT.channelId, _CURRENT.channelTypeId)
      }
    })
    function setLinker(cid, ctid){
      cid = cid || ''
      ctid = ctid || ''
      $ARSAddressCode.empty();
      let computer = variableReg +
        "/RegSever/RegPage/pages/" + (isEn ? 'purchasersReg' : 'supplierReg') + ".html?target=" + targetType +
        "&orgnum=" + orgNum +
        "&version=" + String(isEn + 1) +
        "&cid=" + cid +
        "&ctid=" + ctid +
        "&pid=" + projectId;
      $("#activity-reg-address-computer").val(computer);
      $("#activity-reg-address-computer-open").attr({"href": computer})
      const $phoneAddr = $('.all_address').eq(0)
      const $qrCodeAddr = $('.address_download')
      if(targetType === 3){
        $ARSAddressWindow.window('open')
        $phoneAddr.hide()
        $qrCodeAddr.hide()
        return
      }else {
        $phoneAddr.show()
        $qrCodeAddr.show()
      }

      let phone = variableReg +
        "/RegSever/RegPage/pages/sj_reg_supplier.html?target=" + targetType +
        "&orgnum=" + orgNum +
        "&version=" + String(isEn + 1) +
        "&cid=" + cid +
        "&ctid=" + ctid +
        "&pid=" + projectId;
      $('#activity-reg-address-phone').val(phone);
      $("#activity-reg-address-phone-open").attr({"href": phone})
      $("canvas").css("width", "170px")
      const codeLogo = ARSGetLogoImage(projectId)
      $ARSAddressCode.qrcode({
        src: codeLogo,
        text: phone,
        correctLevel: 0,
        width: '140', //二维码的宽度
        height: '140', //二维码的高度
        imgWidth: 140 / 4,			 //图片宽
        imgHeight: 140 / 4,			 //图片高
      })
    }
    setLinker()
    $ARSAddressWindow.window('open')
  }

  function ARSRegAddressBtnEn(val, row) {
    return ARSRegAddress(row, true)
  }

  function ARSRegSynDataBtn(val, row) {
    const {synData, synTeamName, projectId, synTeam, targetType} = row
    if (!synData)
      return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSOpenRegSynData(' +
        projectId + ', ' +
        targetType + ', ' +
        synTeam + ')">未开启</a>'
    return (synTeamName ? synTeamName: '') + '&nbsp;&nbsp;&nbsp;' +
      '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSOpenRegSynData(' +
      projectId + ', ' +
      targetType + ', ' +
      synTeam + ')">修改</a>' + '&nbsp;&nbsp;&nbsp;' +
      '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="ARSSetSynData(' +
      projectId + ', ' +
      targetType + ', ' +
      Number(!synData) + ',' +
      synTeam + ')">关闭</a>'
  }

  function ARSOpenRegSynData(projectId, targetType, _synTeam) {
    const $projectTeam = $("#activity-reg-project-team")
    const $teamWindow = $("#activity-reg-open-team-window")
    $projectTeam.combobox({
      url: eippath + "/project/getWorkTeamByProjectId", //获取数据
      method: "post",
      queryParams: {projectId},
      onLoadSuccess(data) {
        if (_synTeam) $projectTeam.combobox('setValue', _synTeam);
      }
    })
    $teamWindow.attr('projectId', projectId)
    $teamWindow.attr('targetType', targetType)
    $teamWindow.window('open');
  }

  function ARSSetSynData(projectId, targetType, synData, _synTeam) {
    const synTeam = _synTeam ? _synTeam : ''
    const msgText = '设置'
    $.ajax({
      url: '/RegSever/projRegSet/setSynData',
      data: {
        projectId,
        targetType,
        synData,
        synTeam
      },
      type: "post",
      dataType: 'json',
      success(data) {
        if (data.state !== 1) return $.messager.alert('错误', msgText + '失败！', 'error')
        $.messager.alert('提示', msgText + '成功！', 'info')
        $("#activity-reg-open-team-window").window('close')
        ARSQueryTableData(Number(targetType))
      },
      error(e) {
        $.messager.alert('错误', msgText + '失败！', 'error')
        console.warn(e);
      }
    })
  }

  function ARSSaveSynData() {
    const $teamWindow = $("#activity-reg-open-team-window")
    const projectId = $teamWindow.attr('projectId')
    const targetType = $teamWindow.attr('targetType')
    const synTeam = $("#activity-reg-project-team").combobox("getValue")
    ARSSetSynData(projectId, targetType, 1, synTeam)
  }

  function ARSRegIsMain(val, row) {
    return Number(projectId) === row.projectId ? '是' : '否'
  }

  function ARSRegSearch(targetType) {
    const val = $('#activity-reg-search-project-' + targetType).textbox('getValue')
    ARSQueryTableData(targetType, val)
  }

  function ARSCopyText(id) {
    document.getElementById(id).select()
    document.execCommand("copy")
  }

  function ARSDownload() {
    const type = 'png'
    const c = $('#activity-reg-address-code').find('canvas')[0]
    let imgdata = c.toDataURL("image/png")
    const fixtype = function (type) {
      type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg')
      const r = type.match(/png|jpeg|bmp|gif/)[0]
      return 'image/' + r
    };
    imgdata = imgdata.replace(fixtype(type), 'image/octet-stream')
    const savaFile = function (data, filename) {
      const save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
      save_link.href = data
      save_link.download = filename
      const event = document.createEvent('MouseEvents')
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      save_link.dispatchEvent(event)
    };
    const filename = '' + new Date().getDate() + '.' + type
    savaFile(imgdata, filename);
  }

  function ARSQueryTableData(targetType, searchProName) {
    if (![2, 3].includes(targetType)) return console.warn('targetType数据异常！')
    const projectName = searchProName ? searchProName : ''
    $('#activity-reg-datagrid-' + targetType).datagrid({
      url: '/RegSever/projRegSet/getProjectAndChild',
      queryParams: {
        projectName,
        targetType,
        projectId
      },
      loadFilter(data) {
        if (data.state !== 1) {
          $.messager.alert('错误', '数据加载失败！', 'error')
          return []
        }
        return data.data
      }
    })
  }

  $(function () {
    ARSQueryTableData(2)
    ARSQueryTableData(3)
  })
</script>