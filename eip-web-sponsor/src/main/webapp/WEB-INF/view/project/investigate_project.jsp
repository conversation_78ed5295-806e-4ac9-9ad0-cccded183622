<!-- 项目管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="investigate-project-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="investigate_project_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="investigate_project_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-edit" onclick="investigate_project_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="investigate_project_del_batch()" plain="true">删除</a>
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-add" onclick="investigate_project_new_exhibition()" plain="true">生成展会</a> -->
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="investigate_project_view()" plain="true">查看</a> -->
            </div>
            <div class="my-toolbar-search">
            	<label>项目状态</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="investigate-project-combobox-projectState" data-options="panelHeight:'auto'" class="easyui-combobox" style="width:100px;height:25px">
	            	<option value="" ></option>
	            	<option value="1" >进行中</option>
	            	<option value="2" >已结束</option>
            	</select>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="selectDemo" class="easyui-combobox" style="width:225px;height:25px" >
            	</select>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-keyword-projectName" class="my-text" style="width:200px">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="investigate_project_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="investigate-project-datagrid" class="easyui-datagrid" toolbar="#investigate-project-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			investigate_project_mod_db(rowData);
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'isMain',width:100" hidden="true">是否是主项目</th>
					<th data-options="field:'projectId',width:60">项目编号</th>
					<th data-options="field:'projectName',width:120">项目名称</th>
					<th data-options="field:'projKindName',width:80">项目类别</th>
					<th data-options="field:'exhibitCode',width:80">展会代号</th>
					<th data-options="field:'exihibitName',width:120">展会名称</th>
					<th data-options="field:'createTime',width:100">立项时间</th>
					<th data-options="field:'projectState',width:60" formatter="investigate_project_formatProjectState">项目状态</th>
					<th data-options="field:'do',width:150,align:'center'" formatter="investigate_project_formatDo">操作</th>
				</tr>
			</thead>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="investigate-project-window" class="easyui-window" toolbar="#investigate-project-window-toolbar" data-options="closed:true,title:'修改',modal:true" style="width:572px;">
	<!-- begin of toolbar -->
	<div id="investigate-project-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="investigate-project-save" class="easyui-linkbutton" iconCls="icon-save" onclick="investigate_project_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="investigate-project-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="investigate_project_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="investigate-project-panel-1" class="easyui-panel" title="基本信息" style="width:558px;height:120px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目编号</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-textbox-projectId" class="easyui-textbox" style="width:175px;height:25px" readonly></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-textbox-projectName" class="easyui-textbox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="investigate-project-combobox-proj-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/projKind/getAll'" style="width:175px;height:25px"></select></li>
       	</ul>
   	</div>

</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="investigate-project-window-select-taskKind" class="easyui-window" data-options="closed:true,title:'选取任务类别',modal:true" style="width:572px;">
	<div id="investigate-project-panel-select-taskKind" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="investigate-project-datagrid-select-taskKind" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'taskKindCode',width:20">任务类别代号</th>
							<th data-options="field:'taskKindName',width:40">任务类别名称</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-keyword-select-taskKind" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="investigate_project_search_select_taskKind()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="investigate_project_ok_select_taskKind()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="investigate_project_return_select_taskKind()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="investigate-project-window-task" class="easyui-window" toolbar="#investigate-project-window-task-toolbar" data-options="closed:true,title:'新增任务',modal:true" style="width:572px;">
	<!-- begin of toolbar -->
	<div id="investigate-project-window-task-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="investigate-project-task-add" class="easyui-linkbutton" iconCls="icon-add" onclick="investigate_project_task_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" id="investigate-project-task-save" class="easyui-linkbutton" iconCls="icon-save" onclick="investigate_project_task_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="investigate-project-task-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="investigate_project_task_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="investigate-project-panel-task-1" class="easyui-panel" title="基本信息" style="width:558px;height:150px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>任务类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="investigate-project-task-combobox-task-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/taskKind/getAll'" style="width:175px;height:25px"></select></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>任务主题</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-task-textbox-taskTopic" class="easyui-textbox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>开始时间</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-task-textbox-startTime" class="easyui-datetimebox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>截止时间</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-task-textbox-endTime" class="easyui-datetimebox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>备注</label>&#12288;&#12288;&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-task-textbox-memo" class="easyui-textbox" style="width:175px;height:25px"></li>
		</ul>
    </div>
    <div id="investigate-project-panel-task-2" class="easyui-panel" title="任务概览" style="width:558px;height:400px;">
    	<div class="easyui-layout" data-options="fit:true">
    		<div data-options="region:'center'"><!--
    			<table id="investigate-project-datagrid-task" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'taskKindName',width:30">任务类型</th>
							<th data-options="field:'cnt',width:30" formatter="investigate_project_task_formatCnt">是否创建</th>
						</tr>
					</thead>
		        </table> -->
    		</div>
    	</div>
    </div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="investigate-project-window-member" class="easyui-window" data-options="closed:true,title:'人员管理',modal:true" style="width:572px;">
	<div id="investigate-project-panel-member-1" class="easyui-panel" style="width:558px;height:400px;">
    	<div class="easyui-layout" data-options="fit:true">
    		<div data-options="region:'center'"><!--
    			<table id="investigate-project-datagrid-member" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'roleName',width:40">角色</th>
							<th data-options="field:'operName',width:30">人员</th>
							<th data-options="field:'do',width:60,align:'center'" formatter="investigate_project_member_formatDo">操作</th>
						</tr>
					</thead>
		        </table> -->
    		</div>
    	</div>
    </div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="investigate-project-window-member-set" class="easyui-window" toolbar="#investigate-project-window-toolbar-member-set" data-options="closed:true,title:'设置人员',modal:true" style="width:292px;">
	<!-- begin of toolbar -->
	<div id="investigate-project-window-toolbar-member-set">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="investigate-project-member-set-save" class="easyui-linkbutton" iconCls="icon-save" onclick="investigate_project_member_set_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="investigate-project-member-set-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="investigate_project_member_set_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="investigate-project-panel-member-set-1" class="easyui-panel" title="基本信息" style="width:278px;height:90px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>人员</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="investigate-project-member-combobox-oper" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'" style="width:175px;height:25px"></select></li>
       	</ul>
   	</div>
</div>
<!-- end of easyui-window -->

<!-- 创建项目 -->
<!-- begin of easyui-window -->
<div id="ini-project-window-setup" class="easyui-window" toolbar="#ini-project-window-setup-toolbar" data-options="closed:true,title:'新增项目',modal:true" style="width:572px;">
	<!-- begin of toolbar -->
	<div id="ini-project-window-setup-toolbar">
	    <div class="my-toolbar-button">
	    	<a href="javascript:void(0);" id="investigate-project-setup-save" class="easyui-linkbutton" iconCls="icon-save" onclick="investigate_project_setup_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="investigate-project-setup-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="all_project_setup_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="investigate-project-panel-setup-1" class="easyui-panel" title="基本信息" style="width:558px;height:120px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">

       		<form id="proform">
       			<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="add-project-select-add" class="easyui-combobox" name="exhibitCode" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/exhibition/GetSelectOne'"   style="width:175px;height:25px"></select></li>
        		<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目编号</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-project-textbox-add" class="easyui-textbox" name="projectId" style="width:175px;height:25px" readonly></li>
        		<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="investigate-projectname-textbox-add" class="easyui-textbox" name = "projectName" style="width:175px;height:25px"></li>
        		<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="investigate-project-combobox-add" class="easyui-combobox" name = "projKindCode" data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/projKind/getAll'"   style="width:175px;height:25px"></select></li>
       		</form>
       	</ul>
    </div>
</div>
<!-- end of easyui-window -->

<script type="text/javascript">
	var investigate_project_edit_mode;
	var investigate_project_button_edit_mode;
	var investigate_project_alocate_id = -1;
	var investigate_project_window_close_set = false;
	var investigate_project_save_id;
	var investigate_project_projectId;
	var investigate_project_task_save_id;
	var investigate_project_member_edit_mode;
	var investigate_project_member_save_id;
	var investigate_project_member_operRoleId;
	var investigate_project_exhibitCode_for_pass;
	var investigate_project_taskGroupId;


	$(function(){
		//investigate_project_fresh();
	});

	$(function(){
        investigate_project_fresh();
        loadExhibit();
    });
     function loadExhibit() {
        $('#selectDemo').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/exhibition/GetSelectOne'  ,
			onLoadSuccess: function(data){
				 var val = $('#selectDemo').combobox('getData');
                 for (var item in val[0]) {
                     if (item == 'id') {
                    	 $('#selectDemo').combobox('select', val[0][item]);
                     }
                 }
			}
		});
    }
	function investigate_project_add2(){
		$('#investigate-project-window-select-taskKind').window('open');
		$('#investigate-project-datagrid-select-taskKind').datagrid({
			url: eippath + '/taskKind/selectTaskKind?keyword=' + ''
		});
	}
	function investigate_project_del2(){
		var rows = $('#investigate-project-datagrid-taskKind').datagrid('getSelections');
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				var rowIndex = $('#investigate-project-datagrid-taskKind').datagrid('getRowIndex', rows[i]);
				$('#investigate-project-datagrid-taskKind').datagrid('deleteRow', rowIndex);
			}
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function investigate_project_search_select_taskKind(){
		$('#investigate-project-datagrid-select-taskKind').datagrid({
			url: eippath + '/taskKind/selectTaskKind?keyword=' + $('#investigate-project-keyword-select-taskKind').val()
		});
	}
	function investigate_project_ok_select_taskKind(){
		var rows = $('#investigate-project-datagrid-select-taskKind').datagrid('getSelections');
		var rows2 = $("#investigate-project-datagrid-taskKind").datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				flag = 0;
				for(var j = 0; j < rows2.length; j++){
					if(rows2[j].taskKindCode == rows[i].taskKindCode){
						flag = 1;
						break;
					}
				}
				if(flag == 0){
					$('#investigate-project-datagrid-taskKind').datagrid('appendRow', {
						taskKindCode: rows[i].taskKindCode,
						taskKindName: rows[i].taskKindName
					});
				}
			}
			$('#investigate-project-window-select-taskKind').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function investigate_project_return_select_taskKind(){
		$('#investigate-project-window-select-taskKind').window('close');
	}
	function investigate_project_formatProjectState(val, row){
		if(val == 1)return '进行中';
		else if(val == 2)return '已结束';
	}
	function investigate_project_task_formatCnt(val, row){
		if(val == 0)return '否';
		else return '是';
	}
	function investigate_project_task_fresh(){
		$('#investigate-project-datagrid-task').datagrid({
			url: eippath + '/taskKind/getCount?projectId=' + investigate_project_projectId
		});
	}
	function investigate_project_member_fresh(){
		$('#investigate-project-datagrid-member').datagrid({
			url: eippath + '/projMember/selectByProjectId?projectId=' + investigate_project_projectId
		});
		$('#project-info-task-combobox-excute-oper').combobox({
			url: eippath + '/operator/selectByProjectId?projectId=' + investigate_project_projectId
		});
	}
	function investigate_project_formatDo(val, row){/*
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_task_add_out(" + row.projectId + ")'>新增任务</a>&nbsp;&nbsp;" +
		"<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_info(" + row.projectId + "," + row.exhibitCode + ")'>项目信息中心</a>&nbsp;&nbsp;" +
		"<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_member(" + row.projectId + ")'>人员管理</a>";*/
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_info(" + row.projectId + ",\"" + row.exhibitCode + "\")'>项目信息中心</a>&nbsp;&nbsp;"
		// 		+
		// "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_task(" + row.projectId + ",\"" + row.exhibitCode + "\")'>项目任务设置</a>";
	}
	function investigate_project_info(projectId, exhibitCode){
		investigate_project_projectId = projectId;
		closeTab("项目信息中心");
		addTab("项目信息中心", eippath + "/backstage/project/project_info", "icon-tip", false);
		//investigate_project_member_fresh();
		investigate_project_exhibitCode_for_pass = exhibitCode;
	}
	function investigate_project_task(projectId, exhibitCode){
		console.log(projectId,exhibitCode);
		investigate_project_projectId = projectId;
		closeTab("项目任务设置");
		addTab("项目任务设置", eippath + "/backstage/project/task_mgr", "icon-tip", false);
		//investigate_project_member_fresh();
		investigate_project_exhibitCode_for_pass = exhibitCode;
	}
	function investigate_project_member(projectId){
		investigate_project_projectId = projectId;
		$('#investigate-project-window-member').window('open');
		investigate_project_member_fresh();
	}
	function investigate_project_member_formatDo(val, row){
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_member_set(" + row.id + "," + row.operRoleId + "," + row.operatorId + ")'>设置人员</a>&nbsp;&nbsp;" +
		"<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='investigate_project_member_delete(" + row.id + ")'>删除人员</a>";
	}
	function investigate_project_member_set(id, operRoleId, operatorId){
		$('#investigate-project-window-member-set').window('open');
		investigate_project_member_operRoleId = operRoleId;
		if(id == null)investigate_project_member_edit_mode = 'Add';
		else{
			investigate_project_member_edit_mode = 'Mod';
			investigate_project_member_save_id = id;
			$('#investigate-project-member-combobox-oper').combobox('setValue', operatorId);
		}
	}
	function investigate_project_member_delete(id){
		if(id == null){
			$.messager.alert('提示','未设置人员！','warning');
			return;
		}
		var vId = id;
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/projMember/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {id: vId},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							investigate_project_member_fresh();
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function investigate_project_member_set_save(){
		if($('#investigate-project-member-combobox-oper').combobox('getValue') == ''){
			$.messager.alert('提示','人员不能为空！','warning');
			return;
		}
		var postData = {};
		postData['editMode'] = investigate_project_member_edit_mode;
		postData['id'] = investigate_project_member_save_id;
		postData['projectId'] = investigate_project_projectId;
		postData['operRoleId'] = investigate_project_member_operRoleId;
		postData['operatorId'] = $('#investigate-project-member-combobox-oper').combobox('getValue');
		$.ajax({
			url: eippath + '/projMember/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					$('#investigate-project-window-member-set').window('close', true);
					investigate_project_member_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function investigate_project_member_set_cancel(){
		$('#investigate-project-window-member-set').window('close', true);
	}
	function investigate_project_fresh(){
        loadExhibit();
		$('#investigate-project-datagrid').datagrid({
			url: eippath + '/project/getList?key=0',
			onLoadSuccess: function(data){
				if(investigate_project_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].projectId == investigate_project_alocate_id){
							$(this).datagrid('selectRow', i);
							investigate_project_alocate_id = -1;
							break;
						}
					}
				}
				// $(".datagrid-header-check").html("");
			}
		});
	}
	function investigate_project_search(){
		//alert($('#selectDemo').combobox('getValue'))
		$('#investigate-project-datagrid').datagrid({
			url: eippath + '/project/getList?key=1',
			queryParams: {
				projectName: $('#investigate-project-keyword-projectName').val(),
				projectState: $('#investigate-project-combobox-projectState').combobox('getValue')
				/* ,ecode: $('#selectDemo').combobox('getValue')	 */
				,ecode: $('#selectDemo').combobox('getText')
            }
		});
	}
	function investigate_project_window_close(){
		$('#investigate-project-window').window({
			onBeforeClose: function(){
				if(investigate_project_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#investigate-project-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				investigate_project_fresh();
			}
		});
	}


	function investigate_project_mod(){
		if (flag)return;
		investigate_project_mod_or_view(1);
	}
	function investigate_project_new_exhibition(){
		if (flag)return;
		console.log('开始生成展会');
		var row = $('#investigate-project-datagrid').datagrid('getSelected');
		if (row){
			var vprojectId = row.projectId;
			$.ajax({
				url: eippath + '/project/newExhibiton',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {
					projectId : vprojectId
				},	//发送数据
				async: true,
				success: function(data){
					if(data.state == 1){
						$.messager.alert('提示','生成成功！','info');
						//investigate_project_fresh();
					}else if(data.state == -1){
						$.messager.alert('提示','已有关联展会！','info');
					}
					else{
						$.messager.alert('提示','生成失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function investigate_project_mod_db(row){
		investigate_project_alocate_id = row.projectId;
		$('#investigate-project-window').window('open');
		$("#investigate-project-textbox-projectId").textbox("setValue", row.projectId);
		$("#investigate-project-textbox-projectName").textbox("setValue", row.projectName);
		$('#investigate-project-combobox-proj-kind').combobox('setValue', row.projKindCode);
		investigate_project_taskGroupId = row.taskGroupId;
		//$("#investigate-project-textbox-groupName").textbox("setValue", row.groupName);
		$('#investigate-project-datagrid-taskKind').datagrid({
			url: eippath + '/taskKind/getTaskKind?taskGroupId=' + row.taskGroupId
		});
	}

	function investigate_project_mod_or_view(flag){
		if(!investigate_project_window_close_set){
			investigate_project_window_close();
			investigate_project_window_close_set = true;
		}
		var row = $('#investigate-project-datagrid').datagrid('getSelected');
		if (row){
			investigate_project_alocate_id = row.projectId;
			$('#investigate-project-window').window('open');
			$("#investigate-project-textbox-projectId").textbox("setValue", row.projectId);
			$("#investigate-project-textbox-projectName").textbox("setValue", row.projectName);
			$('#investigate-project-combobox-proj-kind').combobox('setValue', row.projKindCode);
			investigate_project_taskGroupId = row.taskGroupId;
			//$("#investigate-project-textbox-groupName").textbox("setValue", row.groupName);
			$('#investigate-project-datagrid-taskKind').datagrid({
				url: eippath + '/taskKind/getTaskKind?taskGroupId=' + row.taskGroupId
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	setInterval("getLoc()",50);
	var flag = false;
	function getLoc(){
		var rows = $('#investigate-project-datagrid').datagrid('getSelections');
		if (rows.length > 1){
		       	$(".row-lenght").css("opacity","0.5")
		       flag =true;
		     }else{
				 $(".row-lenght").css("opacity","1")
			   flag = false;
		     }
	}
	function investigate_project_del_batch(){
		var rows = $('#investigate-project-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {projectId: rows[i].projectId,exhibitCode: rows[i].exhibitCode,isMain: rows[i].isMain};
			}
			//'当前选择的项目数为：' + rows.length +
			$.messager.confirm('提示', '确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/project/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								investigate_project_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function investigate_project_save(){
		if($('#investigate-project-combobox-proj-kind').combobox('getValue') == ''){
			$.messager.alert('提示','项目类别不能为空！','warning');
			return;
		}
		var postData = {};
		//postData['editMode'] = 'Mod';
		postData['projectId'] = $("#investigate-project-textbox-projectId").val();
		postData['projectName'] = $("#investigate-project-textbox-projectName").val();
		postData['projKindCode'] = $('#investigate-project-combobox-proj-kind').combobox('getValue');
		/* postData['taskGroupId'] = investigate_project_taskGroupId;
		postData['groupName'] = $("#investigate-project-textbox-projectName").val();
		var rows = $('#investigate-project-datagrid-taskKind').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['taskKinds[' + i + '].taskKindCode'] = rows[i].taskKindCode;
				postData['taskKinds[' + i + '].taskKindName'] = rows[i].taskKindName;
			}
		} */
		$.ajax({
			url: eippath + '/project/save?editMode=Mod',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					investigate_project_alocate_id = postData['projectId'];
					$('#investigate-project-window').window('close', true);
				}else{
					if(data.state == -1){
						$.messager.alert('提示','项目名称已存在，请更换！','error');
					}else {
						$.messager.alert('提示','保存失败！','error');
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function investigate_project_cancel(){
		$('#investigate-project-window').window('close', true);
	}
	function  investigate_project_task_add_out(projectId){
		investigate_project_projectId = projectId;
		//investigate_project_task_fresh();
		investigate_project_task_add();
	}
	function investigate_project_task_add(){
		$.ajax({
			url: eippath + '/task/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			contentType: "application/json",
			data: JSON.stringify({}),	//发送数据
			async: true,
			success: function(data){
				$('#investigate-project-window-task').window('open');
				investigate_project_task_fresh();
				investigate_project_task_save_id = data.id;
				$("#investigate-project-task-textbox-taskTopic").textbox("setValue", '');
				$("#investigate-project-task-textbox-startTime").textbox("setValue", '');
				$("#investigate-project-task-textbox-endTime").textbox("setValue", '');
				$("#investigate-project-task-textbox-memo").textbox("setValue", '');
				investigate_project_task_setButtonEditMode(1);
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function investigate_project_task_save(){
		if($('#investigate-project-task-combobox-task-kind').combobox('getValue') == ''){
			$.messager.alert('提示','任务类别不能为空！','warning');
			return;
		}
		var postData = {};
		postData['editMode'] = 'Add';
		postData['taskId'] = investigate_project_task_save_id;
		postData['taskKindCode'] = $('#investigate-project-task-combobox-task-kind').combobox('getValue');
		postData['projectId'] = investigate_project_projectId;
		postData['taskTopic'] = $("#investigate-project-task-textbox-taskTopic").val();
		if($("#investigate-project-task-textbox-startTime").val() != '')postData['startTime'] = $("#investigate-project-task-textbox-startTime").val();
		if($("#investigate-project-task-textbox-endTime").val() != '')postData['endTime'] = $("#investigate-project-task-textbox-endTime").val();
		postData['memo'] = $("#investigate-project-task-textbox-memo").val();
		postData['taskState'] = 1;
		$.ajax({
			url: eippath + '/task/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					investigate_project_task_fresh();
					investigate_project_task_setButtonEditMode(2);
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function investigate_project_task_cancel(){
		$('#investigate-project-task-window').window('close', true);
	}
	function investigate_project_task_setButtonEditMode(flag){
		if(flag == 1){
			$("#investigate-project-task-add").linkbutton('disable');
			$("#investigate-project-task-save").linkbutton('enable');
			$("#investigate-project-task-cancel").linkbutton('enable');
		}else if(flag == 2){
			$("#investigate-project-task-add").linkbutton('enable');
			$("#investigate-project-task-save").linkbutton('disable');
			$("#investigate-project-task-cancel").linkbutton('disable');
		}
	}
	/*
	function project_info(){
		addTab("项目信息中心", eippath + "/backstage/project/project_info", "icon-tip", false);
	}*/


	function investigate_project_add(){
			$.ajax({
				url: eippath + '/project/getSysId',
				dataType: "json",	//返回数据类型
				type: "post",
				contentType: "application/json",
				data: JSON.stringify({}),	//发送数据
				async: true,
				success: function(data){
					$('#ini-project-window-setup').window('open');
					$("#investigate-project-textbox-add").textbox("setValue", data.id);
					$("#investigate-project-textbox-projectName").textbox("setValue", '');
                    $("#investigate-projectname-textbox-add").textbox("setValue", '');
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});

	}
	let investigate_project_saving = false
	function investigate_project_setup_save(){
		/* console.log($("#proform").serialize()); */
		if($('#add-project-select-add').combobox('getValue') == ''){
			$.messager.alert('提示','展会名称不能为空！','warning');
			return;
		}
		if($('#investigate-project-textbox-add').val() == ''){
			$.messager.alert('提示','项目编号不能为空！','warning');
			return;
		}
		if($('#investigate-projectname-textbox-add').val() == ''){
			$.messager.alert('提示','项目名称不能为空！','warning');
			return;
		}
		if($('#investigate-project-combobox-add').combobox('getValue') == ''){
			$.messager.alert('提示','项目类别不能为空！','warning');
			return;
		}
		/* var postData = {};
		postData['editMode'] = 'Add';
		postData['projectId'] = $("#investigate-project-textbox-projectId").val();
		postData['projectName'] = $("#investigate-project-textbox-projectName").val();
		postData['projKindCode'] = $('#investigate-project-combobox-proj-kind').combobox('getValue');
		postData['exhibitCode'] = all_project_save_exhibitCode;
		postData['groupName'] = $("#investigate-project-textbox-projectName").val();
		var rows = $('#investigate-project-datagrid-taskKind').datagrid("getRows"); */
		/* if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['taskKinds[' + i + '].taskKindCode'] = rows[i].f_task_kind_code;
				postData['taskKinds[' + i + '].taskKindName'] = rows[i].f_task_memo;
			}
		} */
		if(investigate_project_saving) return;
		investigate_project_saving = true;
		$.ajax({
			url: eippath + '/project/save?editMode=Add',
			dataType: "json",	//返回数据类型
			type: "post",
			data: $("#proform").serialize(),	//发送数据
			// async: true,
			success: function(data){
				investigate_project_saving = false;
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					//var  ecode = $('#add-project-select-add').combobox('getValue');
					var  ecode = $('#add-project-select-add').combobox('getText');
					reflushByAdd(ecode)
					$('#ini-project-window-setup').window('close', true);
				}else{
					if(data.state == -1){
						$.messager.alert('提示','项目名称已存在，请更换！','error');
					}else {
						$.messager.alert('提示','保存失败！','error');
					}
				}
			},
			error: function(){
				investigate_project_saving = false;
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function all_project_setup_cancel(){
		$('#ini-project-window-setup').window('close', true);

	}
	function reflushByAdd(ecode){
		$('#investigate-project-datagrid').datagrid({
			url: eippath + '/project/getList?key=1',
			queryParams: {
				 ecode: ecode
            }
		});
	}
</script>
