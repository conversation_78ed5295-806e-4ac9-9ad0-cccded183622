<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    
    	<!-- begin of toolbar -->
        <div id="finish_project-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="all_reload()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="operate_role_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="operate_role_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="operate_role_del_batch()" plain="true">删除</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="operate_role_view()" plain="true">查看</a>
            </div>
            <div class="my-toolbar-search">
            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="finish_project-keyword" class="my-text" style="width:200px">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="operate_role_search()">查询</a>
            </div>
        </div>
        
        <!-- end of toolbar -->
        <table id="finish_project-datagrid" class="easyui-datagrid" toolbar="#finish_project-toolbar"  >
        
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="finish_project-window" class="easyui-window" toolbar="#finish_project-window-toolbar" data-options="closed:true,title:'操作角色内容',modal:true" style="width:572px;">
	<!-- begin of toolbar -->
	<div id="finish_project-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="finish_project-add" class="easyui-linkbutton" iconCls="icon-add" onclick="getrowtest()" plain="true">测试</a>
        	<a href="javascript:void(0);" id="finish_project-mod" class="easyui-linkbutton" iconCls="icon-edit" onclick="operate_role_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" id="finish_project-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="operate_role_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" id="finish_project-save" class="easyui-linkbutton" iconCls="icon-save" onclick="operate_role_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="finish_project-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="operate_role_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="finish_project-panel-1" class="easyui-panel" title="基本信息" style="width:558px;height:120px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;float:left;margin-right:15px;margin-bottom:5px;"><label>角色编号</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="finish_project-textbox-operRoleId" class="easyui-textbox" style="width:175px;height:25px" readonly></li>
        	<li style="width:250px;float:left;margin-right:15px;margin-bottom:5px;"><label>角色名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="finish_project-textbox-roleName" class="easyui-textbox" style="width:175px;height:25px"></li>
        	<li style="width:250px;float:left;margin-right:15px;margin-bottom:5px;"><label>角色说明</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="finish_project-textbox-roleRemark" class="easyui-textbox" style="width:175px;height:25px"></li>
        </ul>
    </div>
    <div id="finish_project-panel-2" class="easyui-panel" title="所拥有的权限" style="width:558px;height:484px;">
    	<div class="easyui-layout" data-options="fit:true">
    		<div data-options="region:'north'" style="height:47px;padding:10px;">
    			<label>从角色</label>
    			<select id="finish_project-combobox-basic-role" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/operateRole/getBasicRole'" style="width:120px;height:25px"></select>
    			<label>继承权限</label>
    			&nbsp;&nbsp;&nbsp;&nbsp;
    			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:140px;height:25px" onclick="operate_role_reset()">重置为继承权限</a>
    			&nbsp;
    			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="all_reload()">刷新</a>
    		</div>
    		<div data-options="region:'center'" style="padding:10px;">
    			<ul id="finish_project-tree" class="easyui-tree" data-options="method:'post',animate:true,checkbox:true"></ul>
    		</div>
    	</div>
    </div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	var operate_role_edit_mode;
	var operate_role_button_edit_mode;
	var operate_role_alocate_id = -1;
	var operate_role_window_close_set = false;
	var operate_role_default_basic_role_id = 1;
	$(function(){
		getrowtest();
	});
	function operate_role_formatIsBasic(val, row){
		if(val == false)return '否';
		else return '是';
	}
	function operate_role_fresh(){
		$('#finish_project-datagrid').datagrid({
			url: eippath + '/project/getList?key=0',
			onLoadSuccess: function(data){
				if(operate_role_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].f_oper_role_id == operate_role_alocate_id){
							$(this).datagrid('selectRow', i);
							operate_role_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function operate_role_search(){
		$('#finish_project-datagrid').datagrid({
			url: eippath + '/operateRole/getList?key=1&keyword=' + $('#finish_project-keyword').val()
		});
	}
	function operate_role_window_close(){
		$('#finish_project-window').window({
			onBeforeClose: function(){
				if(operate_role_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#finish_project-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				operate_role_fresh();
			}
		});
	}
	function operate_role_add(){
		if(!operate_role_window_close_set){
			operate_role_window_close();
			operate_role_window_close_set = true;
		}
		$.ajax({
			url: eippath + '/operateRole/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			contentType: "application/json",
			data: JSON.stringify({}),	//发送数据
			async: true,
			success: function(data){
				operator_alocate_id = -1;
				$('#operator-window').window('open');
				$("#operator-textbox-operator-id").textbox("setValue", data.id);
				$("#operator-textbox-oper-kind").textbox("setValue", '');
				$("#operator-textbox-oper-name").textbox("setValue", '');
				document.getElementById('operator-checkbox-forbidden').checked = false;
				$("#operator-textbox-oper-memo").textbox("setValue", '');
				$('#operator-datagrid-role').datagrid({
					url:'http://' + variable + '/operator/getOperateRole?operatorId=' + data.id
				});
				operator_edit_mode = "Add";
				operator_setEditInfoReadOnly(1);
				operator_setButtonEditMode(1);
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
		
		
		
		$('#finish_project-window').window('open');
		//$('#finish_project-combobox-basic-role').combobox('reload', eippath + '/operateRole/getBasicRole');
		operate_role_tree_fresh(operate_role_default_basic_role_id);
		
	}
	function operate_role_tree_fresh(operRoleId){
		$('#finish_project-tree').tree({
			url: eippath + '/operateRole/makeTreeJson?operRoleId=' + operRoleId,
			onLoadSuccess: function(data){
				$('#finish_project-tree').find(".tree-icon,tree-folder").removeClass('tree-icon tree-folder tree-folder-open tree-folder-closed');
				$('#finish_project-tree').find(".tree-icon,.tree-file").removeClass('tree-icon tree-file');
			}
		});
	}
	function operate_role_reset(){
		if($('#finish_project-combobox-basic-role').combobox('getValue') == '')operate_role_tree_fresh(operate_role_default_basic_role_id);
		else operate_role_tree_fresh($('#finish_project-combobox-basic-role').combobox('getValue'));
	}
	function all_reload(){
		 //重新加载当前页面数据   
		$("#finish_project-datagrid").datagrid('reload'); 
	}
	function getrowtest(){
		//获取用户列表
		    $("#finish_project-datagrid").datagrid({
		    	url:eippath+ '/data/data.json',
		    	method:'post',
		        queryParams://每次请求的参数
		            {
		                cmd: 'demo',
		                strWhere: 'test',
		                key:'0',
		            },
		        pagination:true,
		        multiSort:true,
		        fitColumns:true,
		        fit:true,
		        pagination: true,//允许分页
		        rownumbers: true,//行号
		        singleSelect: false,//只选择一行
		        pageSize: 20,//每一页数据数量
		        checkOnSelect: false,
		        pageList: [20,30],
		        columns: [[{
		            field: 'f_id',
		            checkbox: true,
		        },
		        {
		            field: "f_exhibit_code",
		            title: "展会代号",
		            align: "center",
		            width: 50
		        }, {
		            field: "f_exihibit_name",
		            title: "展会名称",
		            align: "center",
		            width: 100
		        }, {
		            field: "f_exhbit_kind_id",
		            title: "展会类别",
		            align: "center",
		            width: 100,
		            formatter: function (val, row) {
		                if (val == 1) {
		                    return "展览";
		                }
		                else if (val == 2) {
		                    return "会议";
		                }
		            }
		        }, {
		            field: "f_approve_date",
		            title: "立项时间",
		            align: "center",
		            width: 100,
		            formatter: function (val, row) {
	                	return "2018-8-18 09:30:00";
		            	
		            }
		        }, {
		            field: "f_exhibit_place",
		            title: "展会地点",
		            align: "center",
		            width: 100
		        }, {
		            field: "f_start_date",
		            title: "开幕时间",
		            align: "center",
		            width: 80,
		            formatter: function (val, row) {
		                 
		            	return "2018-8-18 09:30:00";
		            }
		        }, {
		            field: "f_end_date",
		            title: "闭幕时间",
		            align: "center",
		            width: 80,
		            formatter: function (val, row) {
		                 
		            	return "2018-8-18 09:30:00";
		                
		            	
		            }
		            
		        }, {
		            field: "f_exhibit_state",
		            title: "进程",
		            align: "center",
		            width: 100,
		            formatter: function (val, row) {
		                if(val=='1'){
		                	return "筹备期";
		                }else  if(val=='2'){
		                	return "进行中";
		                }else if(val=='3'){
		                	return "已结束";
		                }
		                	
		            	
		            }
		        }, {
		            field: "f_create_oper_id",
		            title: "操作",
		            align: "center",
		            formatter: function (val, row) {
		                return "<a href='javascript:void(0);' class='easyui-linkbutton l-btn l-btn-small easyui-fluid' style='width:70px;height:25px' onclick='alert("+"1"+")'>查看</a>";
				                
		            }
		        } 
		        ]],
		        
		        //点击每一行的时候触发
		        //onClickRow: function (rowIndex, rowData) {
		        //    alert(rowData["UserId"]);
		        //}
		    });
	}
</script>