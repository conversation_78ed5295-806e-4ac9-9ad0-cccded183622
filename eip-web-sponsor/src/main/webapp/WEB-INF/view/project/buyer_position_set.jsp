<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<link rel="stylesheet" href="${eippath}/css/view/alex-form.css?v=1.0">
<%@ include file="../common/select_exhibit_window.jsp" %>
<style>
    .channelButtonStyle {
        color: #fff;
        display: block;
        width: 146px;
        height: 32px;
        background-color: #4fa0ff;
        border-radius: 6px;
        text-align: center;
        line-height: 32px;
        margin: 5px auto 15px;
        font-size: 14px;
        cursor: pointer;
    }
</style>
<div class="datagrid-toolbar" id="buyer-position-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:reflush()" class="easyui-linkbutton" iconCls="icon-reload" plain="true">刷新</a>
    <a href="javascript:buyer_position_add()" class="easyui-linkbutton" iconCls="icon-add" plain="true">新增</a>
    <a href="javascript:buyer_position_del()" class="easyui-linkbutton" iconCls="icon-remove" plain="true">删除</a>
    <a href="javascript:buyer_position_mh_open()" class="easyui-linkbutton" iconCls="icon-import"
       plain="true">导入历史展会</a>
    <a href="javascript:$('#buyer-position-import').window('open')" class="easyui-linkbutton" iconCls="icon-import"
       plain="true">导入</a>
    <a href="javascript:buyer_position_standard_open('standardDepartment')" class="easyui-linkbutton" iconCls="icon-menu3"
       plain="true">规范部门设置</a>
    <a href="javascript:buyer_position_standard_open('standardPosition')" class="easyui-linkbutton" iconCls="icon-menu3"
       plain="true">规范职位设置</a>
    <a href="javascript:buyer_position_bc_open()" class="easyui-linkbutton" iconCls="icon-tool-batch-edit" plain="true">批量修改规范部门及职位</a>
  </div>
</div>
<table
    id="buyer-position-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               	if(i != rowIndex)$(this).datagrid('unselectRow', i);
              }
 				},"
    toolbar="#buyer-position-toolbar"
>
  <thead>
  <tr>
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="position" width="30%">职位关键词</th>
    <th field="standardDepartment" width="30%">规范部门</th>
    <th field="standardPosition" width="30%">规范职位</th>
    <th field="buyerPositionId" width="120px"
        formatter="v => `<a href='javascript:buyer_position_edit(\`\${v}\`)' style='color:blue;font-weight:bold'>修改</a>`">
      操作
    </th>
  </tr>
  </thead>
</table>

<div id="buyer-position-mh-window" class="easyui-window"
    data-options="closed:true,title:'请选择历史展会',modal:true,cls:'__buyer-position-mh-window'"
    style="width:400px;height:auto;"
    toolbar="#buyer-position-mh-window-toolbar">
  <div id="buyer-position-mh-window-toolbar">
    <div class="my-toolbar-button" style="text-align:right">
      <a href="javascript:buyer_position_mh_import()" class="easyui-linkbutton" iconCls="icon-save"
         plain="true">保存</a>
      <a href="javascript:$('#buyer-position-mh-window').window('close')" class="easyui-linkbutton"
         iconCls="icon-cancel"
         plain="true">取消</a>
    </div>
    <div class="easyui-panel" title="基本信息" style="width: 100%;padding: 10px">
      <form class="alex-form">
        <div class="alex-form-item full">
          <div style="position: relative;width: 300px">
            <input
                readonly
                id="buyer-position-mh-editor-exhibitCode"
                class="my-text easyui-textbox"
                data-options="
                 prompt:'展会名称',
                 labelWidth: '80px',
                 labelAlign: 'right',
                 label:'<span class=require-flag>*</span>展会名称'"
                style="width:300px;height:28px">
            <a href="javascript:"
               class="textbox-icon icon-close"
               style="width: 26px; height: 28px;position: absolute;right: 6px;">
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div id="buyer-position-standard-window" class="easyui-window"
     data-options="closed:true,title:'请选择历史展会',modal:true,cls:'__buyer-position-mh-window'"
     style="width:500px;"
     toolbar="#buyer-position-standard-window-toolbar">
  <div id="buyer-position-standard-window-toolbar">
    <div class="my-toolbar-button" style="text-align:left">
      <a href="javascript:buyer_position_standard_add()" class="easyui-linkbutton" iconCls="icon-add"
         plain="true">新增</a>
      <a href="javascript:buyer_position_standard_del()" class="easyui-linkbutton"
         iconCls="icon-remove"
         plain="true">删除</a>
    </div>
    <div class="easyui-panel" style="width: 100%;height: 400px">
      <table id="buyer-position-standard-table"></table>
    </div>
  </div>
</div>

<div id="buyer-position-sf-window" class="easyui-window" toolbar="#buyer-position-sf-window-toolbar"
     data-options="closed:true,title:'基本信息设置',modal:true,cls:'__buyer-position-window'"
     style="width:400px;height:auto;">
  <!-- begin of toolbar -->
  <div id="buyer-position-sf-window-toolbar">
    <div class="my-toolbar-button" style="text-align:right">
      <a href="javascript:buyer_position_sf_save()" class="easyui-linkbutton" iconCls="icon-save" plain="true">保存</a>
      <a href="javascript:$('#buyer-position-sf-window').window('close')" class="easyui-linkbutton" iconCls="icon-cancel"
         plain="true">取消</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <div class="easyui-panel" style="width: 100%;">
    <form class="alex-form">
      <input type="hidden" id="buyer-position-sf-editor-standardId">
      <div class="alex-form-item full">
        <input
            class="easyui-textbox"
            id="buyer-position-sf-editor-standardName"
            style="width: 300px"
            data-options="
             prompt:'规范职位',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'规范职位'"/>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-numberbox"
            id="buyer-position-sf-editor-showOrder"
            style="width: 300px"
            data-options="
             min: 0,
             prompt:'显示顺序',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'显示顺序'"/>
      </div>
    </form>
  </div>
</div>

<div id="buyer-position-import" class="easyui-window"
     data-options="closed:true,title:'导入',modal:true,maximizable: false,resizable: false,minimizable: false,cls:'__buyer-position-window'"
     style="width:300px;">
  <div class="easyui-panel" style="width:100%;height:160px;padding:10px;">
    <span class="channelButtonStyle" style='margin-top: 30px;'
          onclick="window.open('/eip-web-sponsor/document/观众职位导入模板.xlsx', '_blank')">下载导入模板</span>
    <span class="channelButtonStyle" onclick="$('#buyer-position-record-file').click()">导入</span>
    <input type="file" id="buyer-position-record-file" style="display: none"
           accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
  </div>
</div>

<div id="buyer-position-window" class="easyui-window" toolbar="#buyer-position-window-toolbar"
     data-options="closed:true,title:'基本信息设置',modal:true,cls:'__buyer-position-window'"
     style="width:680px;height:auto;">
  <!-- begin of toolbar -->
  <div id="buyer-position-window-toolbar">
    <div class="my-toolbar-button" style="text-align:right">
      <a href="javascript:buyer_position_save()" class="easyui-linkbutton" iconCls="icon-save" plain="true">保存</a>
      <a href="javascript:$('#buyer-position-window').window('close')" class="easyui-linkbutton" iconCls="icon-cancel"
         plain="true">取消</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <div class="easyui-panel" title="基本信息设置" style="width: 100%;padding: 10px">
    <form class="alex-form" id="buyer-position-editor-form">
      <input type="hidden" id="buyer-position-editor-buyerPositionId">
      <div class="alex-form-item full buyer-position-hide-by-batch">
        <input
            class="easyui-textbox"
            id="buyer-position-editor-position"
            style="width: 300px"
            data-options="
             prompt:'职位关键字',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'<span class=require-flag>*</span>职位关键字'"/>
      </div>
      <div class="alex-form-item">
        <input
            class="easyui-combobox"
            id="buyer-position-editor-standardPositionId"
            style="width: 300px"
            data-options="
             prompt:'规范职位',
             loadFilter: data => data.rows,
             queryParams: {
               exhibitCode: exhibitCode_for_pass,
               page: 1,
               rows: 1e5
             },
             labelWidth: '100px',
             labelAlign: 'right',
             panelHeight: 'auto',
             panelMaxHeight: '200px',
             method: 'POST',
             valueField: 'standardPositionId',
             textField: 'standardPosition',
             label:'规范职位'"/>
      </div>
      <div class="alex-form-item">
        <input
            class="easyui-combobox"
            id="buyer-position-editor-standardDepartmentId"
            style="width: 300px"
            data-options="
             prompt:'规范部门',
             loadFilter: data => data.rows,
             queryParams: {
               exhibitCode: exhibitCode_for_pass,
               page: 1,
               rows: 1e5
             },
             labelWidth: '100px',
             labelAlign: 'right',
             panelHeight: 'auto',
             panelMaxHeight: '200px',
             method: 'POST',
             valueField: 'standardDepartmentId',
             textField: 'standardDepartment',
             label:'规范部门'"/>
      </div>
    </form>
  </div>
</div>

<script type="text/javascript">
  $(function () {
    setTimeout(() => {
      buyer_position_load_table()
      buyer_position_load_options()
      buyer_position_hm_init_event()
      $('#buyer-position-record-file').change(function () {
        if (!this.files.length) return
        buyer_position_import_file(this.files[0])
      })
    }, 1e2)
  });

  /**@typedef {'standardDepartment'|'standardPosition'} StandardType*/

  /**
   * @param {StandardType} type
   * */
  function buyer_position_standard_open(type) {
    const title = type === 'standardDepartment' ? '规范部门设置' : '规范职位设置'
    $('#buyer-position-standard-window').window({title}).window('open').data({type})
    buyer_position_standard_load_table(type)
  }

  /**
   * @param {StandardType} type
   * */
  function buyer_position_standard_load_table(type) {
    const $table = $('#buyer-position-standard-table')
    let url, col
    switch (type) {
      case 'standardDepartment':
        url = '/eip-web-sponsor/buyerStandardDepartment/getList'
        col = [{field: 'standardDepartment', title: '规范部门', width: 200}]
        break
      case 'standardPosition':
        url = '/eip-web-sponsor/buyerStandardPosition/getList'
        col = [{field: 'standardPosition', title: '规范职位', width: 200}]
        break
    }
    $table.datagrid({
      url,
      rownumbers: true,
      singleSelect: false,
      pageSize: 20,
      pagination: true,
      multiSort: true,
      fitColumns: false,
      fit: true,
      queryParams: {
        exhibitCode: exhibitCode_for_pass
      },
      columns: [[
        {field: 'ck', checkbox: true},
        ...col,
        {field: 'showOrder', title: '显示顺序', width: 110},
        {
          field: type + 'Id',
          title: '操作',
          formatter(v) {
            return `<a style="color:blue;font-weight:bold" href="javascript:buyer_position_standard_edit('\${type}', '\${v}')">修改</a>`
          },
          width: 110
        },
      ]],
      onLoadSuccess() {
        buyer_position_load_options()
      }
    })
  }

  function buyer_position_standard_add() {
    const type = /**@type{StandardType}*/$('#buyer-position-standard-window').data('type')
    const name = type === 'standardDepartment' ? '规范部门' : '规范职位'
    $('#buyer-position-sf-window').window({title: `新增\${name}`}).window('open')
    buyer_position_sf_form_each((key, result) => {
      if (key === 'standardName') {
        result.control({
          prompt: name,
          label: `<span class=require-flag>*</span>\${name}`,
        })
      } else if (key === 'showOrder') {
        result.control()
      }
      result.control(result.type === 'val' ? '' : 'clear')
    })
  }
  function buyer_position_standard_del() {
    const $window = $('#buyer-position-standard-window')
    const $table = $('#buyer-position-standard-table')
    const type = /**@type{StandardType}*/$window.data('type')
    const rows = $table.datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请选择需要删除的数据', 'warning')
    let url, data
    switch (type) {
      case 'standardDepartment':
        url = '/eip-web-sponsor/buyerStandardDepartment/delete'
        data = {standardDepartmentIds: rows.map(item => item.standardDepartmentId).toString()}
        break
      case 'standardPosition':
        url = '/eip-web-sponsor/buyerStandardPosition/delete'
        data = {standardPositionIds: rows.map(item => item.standardPositionId).toString()}
        break
    }
    $.messager.confirm('提示', `确定删除选择的\${rows.length}条数据吗？`, r => r && _request(data, url))

    function _request(data, url) {
      $.ajax({
        type: 'post',
        url,
        dataType: "json",
        data,
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '删除失败！', 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          buyer_position_standard_load_table(type)
        }
      })
    }
  }
  function buyer_position_standard_edit(type, standardId) {
    if (!standardId) return
    const key = type + 'Id'
    const name = type === 'standardDepartment' ? '规范部门' : '规范职位'
    const currentRow = $('#buyer-position-standard-table').datagrid('getRows').find(item => item[key] === standardId)
    const data = {
      standardId: currentRow[key],
      standardName: currentRow[type],
      showOrder: currentRow.showOrder,
    }
    buyer_position_sf_form_each((key, result) => {
      if (key === 'standardName') {
        result.control({
          prompt: name,
          label: `<span class=require-flag>*</span>\${name}`,
        })
      } else if (key === 'showOrder') {
        result.control()
      }
      result.control(result.type === 'val' ? data[key] : 'setValue', data[key])
    })
    $('#buyer-position-sf-window').window({title: `修改\${name}`}).window('open')
  }
  function buyer_position_sf_save() {
    const type = /**@type{StandardType}*/$('#buyer-position-standard-window').data('type')
    const post = {}
    buyer_position_sf_form_each((key, result) => post[key] = result.type === 'val' ? result.control() : result.control('getValue'))
    const {standardId, standardName} = post
    delete post.standardId
    delete post.standardName
    if (!standardName) return $.messager.alert('提示', '请填写必填项！', 'warning')
    let url
    switch (type) {
      case 'standardDepartment':
        url = '/eip-web-sponsor/buyerStandardDepartment/save'
        post.standardDepartment = standardName
        standardId && (post.standardDepartmentId = standardId)
        break
      case 'standardPosition':
        url = '/eip-web-sponsor/buyerStandardPosition/save'
        post.standardPosition = standardName
        standardId && (post.standardPositionId = standardId)
        break
    }
    _request(post, url)

    function _request(data, url) {
      $.messager.progress()
      $.ajax({
        type: 'post',
        url,
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass,
          ...data,
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '保存失败！', 'error')
          $.messager.alert('提示', '保存成功！', 'info')
          $('#buyer-position-sf-window').window('close')
          buyer_position_standard_load_table(type)
        }
      }).done(() => $.messager.progress('close'))
    }
  }

  function buyer_position_import_file(file) {
    if (!file) return
    $.messager.progress()
    const fd = new FormData()
    fd.append('exhibitCode', exhibitCode_for_pass)
    fd.append('serialNum', Date.now() + '-' + Math.random())
    fd.append('file', file)
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerPosition/importData',
      dataType: "json",
      contentType: false,
      processData: false,
      data: fd,
      success({state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
        buyer_position_load_table()
        $('#buyer-position-record-file').val('')
        $.messager.alert('提示', '操作成功', 'info')
        $.messager.progress('close')
        $('#buyer-position-import').window('close')
      },
      error(err) {
        console.warn(err)
        $.messager.alert('导入失败')
        $.messager.progress('close')
      }
    })
  }

  function buyer_position_load_table() {
    $('#buyer-position-datagrid').datagrid({
      url: '/eip-web-sponsor/buyerPosition/getList',
      queryParams: {
        exhibitCode: exhibitCode_for_pass
      }
    })
  }

  function buyer_position_hm_init_event() {
    const exhibitCodeCtrl = $('#buyer-position-mh-editor-exhibitCode')
    exhibitCodeCtrl.textbox({
      events: {
        click() {
          openSelectExhibit(result => {
            if (!result) return
            const {exhibitCode, exhibitName} = result
            exhibitCodeCtrl.textbox('setValue', exhibitName).data({exhibitCode})
          })
        }
      }
    })
    exhibitCodeCtrl.nextAll('a').click(() => exhibitCodeCtrl.textbox('clear').removeData('exhibitCode'))
  }

  function buyer_position_mh_open() {
    const _open = () => $('#buyer-position-mh-window').window('open')
    if (!$('#buyer-position-datagrid').datagrid('getRows').length) return _open()
    $.messager.confirm('提示', '导入历史展会将清空当前列表，确定导入嘛？', r => r && _open())
  }

  function buyer_position_mh_import() {
    const exhibitCodeCtrl = $('#buyer-position-mh-editor-exhibitCode')
    const historyExhibitCode = exhibitCodeCtrl.data('exhibitCode')
    if (!historyExhibitCode)
      return $.messager.alert('提示', '请选择历史展会！', 'warning')
    $.messager.progress()
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/buyerPosition/importDataByHistory',
      dataType: "json",
      data: {
        exhibitCode: exhibitCode_for_pass,
        historyExhibitCode,
      },
      success(data) {
        if (data.state !== 1)
          return $.messager.alert('错误', '导入失败！', 'error')
        $.messager.alert('提示', '导入成功！', 'info')
        $('#buyer-position-mh-window').window('close')
        buyer_position_load_table()
      }
    }).done(() => $.messager.progress('close'))
  }

  function buyer_position_save() {
    const post = {}
    buyer_position_form_each((key, result) => post[key] = result.type === 'val' ? result.control() : result.control('getValue'))
    const $window = $('#buyer-position-window')
    const _rows = $window.data('rows')
    if (Array.isArray(_rows) && _rows.length) {
      delete post.position
      delete post.buyerPositionId
      if (!post.standardPositionId && !post.standardDepartmentId)
        return $.messager.alert('提示', '请选择规范部门或规范职位！', 'warning')
      post.buyerPositionIds = _rows.map(item => item.buyerPositionId).toString()
      return _request(post, '/eip-web-sponsor/buyerPosition/batchUpdate')
    }
    if (!post.position) return $.messager.alert('提示', '请填写必填项！', 'warning')
    _request(post)

    function _request(post, url) {
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: url || '/eip-web-sponsor/buyerPosition/save',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass,
          ...post,
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '保存失败！', 'error')
          $.messager.alert('提示', '保存成功！', 'info')
          $window.window('close').removeData('rows')
          buyer_position_load_table()
        }
      }).done(() => $.messager.progress('close'))
    }
  }

  function buyer_position_add() {
    // clear form control
    buyer_position_form_each((key, result) => result.control(result.type === 'val' ? '' : 'clear'))
    $('.buyer-position-hide-by-batch').show()
    $('#buyer-position-window').window({title: '新增关键词'}).window('open')
  }

  function buyer_position_edit(buyerPositionId) {
    const currentRow = $('#buyer-position-datagrid').datagrid('getRows').find(item => item.buyerPositionId === buyerPositionId)
    if (!currentRow) return null
    buyer_position_form_each((key, result) => {
      return result.type === 'val' ?
        result.control(currentRow[key]) :
        result.control('setValue', currentRow[key])
    })
    $('.buyer-position-hide-by-batch').show()
    $('#buyer-position-window').window({title: '修改'}).window('open')
  }

  function buyer_position_sf_form_each(callback) {
    const prefix = '#buyer-position-sf-editor-'
    const formType = {
      standardId: 'val',
      standardName: 'textbox',
      showOrder: 'numberbox',
    }
    return buyer_position_form_each(callback, formType, prefix)
  }

  function buyer_position_form_each(callback, formTypes, prefix) {
    prefix = prefix || '#buyer-position-editor-'
    formTypes = formTypes || {
      buyerPositionId: 'val',
      position: 'textbox',
      standardPositionId: 'combobox',
      standardDepartmentId: 'combobox'
    }
    Object.keys(formTypes).forEach(key => {
      const result = {
        formType: formTypes,
        type: formTypes[key],
        $el: $(`\${prefix}\${key}`.replace(/\s/g, '')),
      }
      result.control = (...args) => result.$el[result.type](...args)
      callback(key, result)
    })
  }

  function buyer_position_bc_open() {
    const rows = $('#buyer-position-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请选择需修改的数据', 'warning')
    buyer_position_form_each((key, result) => result.control(result.type === 'val' ? '' : 'clear'))
    $('.buyer-position-hide-by-batch').hide()
    $('#buyer-position-window').window({title: '批量修改规范部门及职位'}).window('open').data({rows})
  }

  function buyer_position_del() {
    const rows = $('#buyer-position-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请选择需要删除的数据', 'warning')
    $.messager.confirm('提示', `确定删除选择的\${rows.length}条数据吗？`, r => r && _request())

    function _request() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/buyerPosition/delete',
        dataType: "json",
        data: {
          buyerPositionIds: rows.map(item => item.buyerPositionId).toString()
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '删除失败！', 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          buyer_position_load_table()
        }
      })
    }
  }

  function buyer_position_load_options() {
    buyer_position_form_each((key, result) => {
      switch (key) {
        case 'position':
          result.control()
          break
        case 'standardDepartmentId':
          result.control({
            url: '/eip-web-sponsor/buyerStandardDepartment/getList',
          })
          break
        case 'standardPositionId':
          result.control({
            url: '/eip-web-sponsor/buyerStandardPosition/getList',
          })
          break
      }
    })
  }
</script>


