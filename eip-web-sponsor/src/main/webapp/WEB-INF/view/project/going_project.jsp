<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="going-project-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="going_project_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="going_project_add_task()" plain="true">新增任务</a>
            </div>
            <div class="my-toolbar-search">
            	<label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="going-project-keyword-exihibitName" class="my-text" style="width:200px">&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>展会地点</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="going-project-keyword-exhibitPlace" class="my-text" style="width:150px">&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>展会年份</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="going-project-keyword-yearMonth" class="my-text" style="width:150px">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="going_project_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="going-project-datagrid" class="easyui-datagrid" toolbar="#going-project-toolbar" 
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'exhibitCode',width:80">展会代号</th>
					<th data-options="field:'exihibitName',width:120">展会名称</th>
					<th data-options="field:'exhibitKindName',width:60">展会类别</th>
					<th data-options="field:'yearMonth',width:60">展会年份</th>
					<th data-options="field:'approveDate',width:100">立项时间</th>
					<th data-options="field:'exhibitPlace',width:110">展会地点</th>
					<th data-options="field:'startDate',width:100">开幕时间</th>
					<th data-options="field:'endDate',width:100">闭幕时间</th>
					<th data-options="field:'exhibitState',width:60" formatter="going_project_formatExhibitState">进程</th>
					<th data-options="field:'do',width:200,align:'center'" formatter="going_project_formatDo">操作</th>
				</tr>
			</thead>
        </table>
    </div>
</div>
<script type="text/javascript">
	$(function(){
		going_project_fresh();
	});
	function going_project_formatExhibitState(val, row){
		if(val == 1)return '筹备期';
		else if(val == 2)return '进行中';
		else if(val == 3)return '已结束';
	}
	function going_project_formatDo(val, row){
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='project_open(" + row.id + ")'>展会信息设置</a>&nbsp;&nbsp;" + 
		"<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick=''>观众服务设置</a>&nbsp;&nbsp;" + 
		"<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick=''>展商服务设置</a>";
	}
	function going_project_fresh(){
		$('#going-project-datagrid').datagrid({
			url: eippath + '/exhibition/getListByState?key=0&exhibitState=2'
		});
	}
	function going_project_search(){
		$('#going-project-datagrid').datagrid({
			url: eippath + '/exhibition/getListByState?key=1&exhibitState=2',
			queryParams: {
				exihibitName: $('#going-project-keyword-exihibitName').val(),
				exhibitPlace: $('#going-project-keyword-exhibitPlace').val(),
				yearMonth: $('#going-project-keyword-yearMonth').val()
            }
		});
	}




$(function (){
	//going_getrowtest();
});
function going_getrowtest(){
	//获取用户列表
	    $("#going_project-datagrid").datagrid({
	    	url:eippath+ '/data/data.json',
	    	method:'post',
	        queryParams://每次请求的参数
	            {
	                cmd: 'demo',
	                strWhere: 'test',
	                key:'0',
	            },
	        pagination:true,
	        multiSort:true,
	        fitColumns:true,
	        fit:true,
	        pagination: true,//允许分页
	        rownumbers: true,//行号
	        singleSelect: false,//只选择一行
	        pageSize: 20,//每一页数据数量
	        checkOnSelect: false,
	        pageList: [20,30],
	        columns: [[{
	            field: 'f_id',
	            checkbox: true,
	        },
	        {
	            field: "f_exhibit_code",
	            title: "展会代号",
	            align: "center",
	            width: 50
	        }, {
	            field: "f_exihibit_name",
	            title: "展会名称",
	            align: "center",
	            width: 100
	        }, {
	            field: "f_exhbit_kind_id",
	            title: "展会类别",
	            align: "center",
	            width: 100,
	            formatter: function (val, row) {
	                if (val == 1) {
	                    return "展览";
	                }
	                else if (val == 2) {
	                    return "会议";
	                }
	            }
	        }, {
	            field: "f_approve_date",
	            title: "立项时间",
	            align: "center",
	            width: 100,
	            formatter: function (val, row) {
                	return "2018-8-18 09:30:00";
	            	
	            }
	        }, {
	            field: "f_exhibit_place",
	            title: "展会地点",
	            align: "center",
	            width: 100
	        }, {
	            field: "f_start_date",
	            title: "开幕时间",
	            align: "center",
	            width: 80,
	            formatter: function (val, row) {
	                 
	            	return "2018-8-18 09:30:00";
	            }
	        }, {
	            field: "f_end_date",
	            title: "闭幕时间",
	            align: "center",
	            width: 80,
	            formatter: function (val, row) {
	                 
	            	return "2018-8-18 09:30:00";
	                
	            	
	            }
	            
	        }, {
	            field: "f_exhibit_state",
	            title: "进程",
	            align: "center",
	            width: 100,
	            formatter: function (val, row) {
	                	return "筹备期";
	                
	                	
	            	
	            }
	        }, {
	            field: "f_create_oper_id",
	            title: "操作",
	            align: "center",
	            width:220,
	            formatter: function (val, row) {
	            	  return "<a href='javascript:void(0);' style='color:blue;'   		   onclick='project_open()'>展会信息设置</a>"+
			            	 "<a href='javascript:void(0);' class=''  style='color:blue;'  onclick='project_spectator()'> 观众服务管理中心</a>"+
			            	 "<a href='javascript:void(0);' class=''  style='color:blue;'  onclick='project_exhibitor()'> 展商服务管理中心</a>";
				         
			                
	            }
	        } 
	        ]],
	        
	        //点击每一行的时候触发
	        //onClickRow: function (rowIndex, rowData) {
	        //    alert(rowData["UserId"]);
	        //}
	    });
}
	function going_project_infoset(){
		addTab("展会信息设置", eippath + "/backstage/project/exhibition_infoset", "icon-tip", false);
	}
	function going_info_set(){
		var  linka =  $(this).find('a');
		var title = linka.text();
		var url = linka.attr('data-link');
		var iconCls = linka.attr('data-icon');
		var iframe = linka.attr('iframe') == 1 ? true : false;
		parent.addTab(title,url,iconCls,iframe);
	}
	
 
	
</script>