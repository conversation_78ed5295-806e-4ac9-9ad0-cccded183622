<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
	<script>
		var fu_task_kind_org = top.checkFunCode('fu_task_kind_org'); // 任务设置-机构
	</script>
<style type="text/css">
	.panel-body-noborder {
		overflow: overlay;
	}
	.hide {
		display: none !important;
	}
	#taskmsg-project-panel-2 .datagrid-wrap {
		border: none;
	}
</style>
<div class="easyui-layout" data-options="fit:true">
	<!-- <div data-options="region:'west',border:true,split:true" style="width:200px;padding:10px;">
		<ul id="task-mgr-subProTask-tree"></ul>
  </div> -->
	<div data-options="region:'center'" style="height:100%;">
		<div style="margin-left: 10px;text-align: center;font-size: 14px;font-weight:bold;display: none;">
			当前展会:<span id="task-mgr-detail-mgr-projectName"></span>
		</div>
		<!-- begin of toolbar -->
		<div id="task-mgr-detail-toolbar" class="hide">
			<div class="my-toolbar-button datagrid-toolbar">
				<a href="javascript:void(0);" class="easyui-linkbutton hide" iconCls="icon-reload"
					onclick="projectWindowTaskMsrSubPro.freshMain()" plain="true">刷新</a>
				<a href="javascript:void(0);" class="easyui-linkbutton hide" iconCls="icon-save"
					onclick="task_mgr_detail_save()" plain="true">保存</a>
				<a href="javascript:void(0);" id="taskMgr_tool_self" class="easyui-linkbutton" iconCls="icon-save"
					onclick="projectWindowTaskMsrSubPro.selfEnable()" plain="true">初始化任务</a>
				<a href="javascript:void(0);" id="taskMgr_tool_add" class="easyui-linkbutton hide" iconCls="icon-add"
				 onclick="taskmsg_project_setup_add();" plain="true">新增</a>
				<a href="javascript:void(0);" id="taskMgr_tool_edit" class="easyui-linkbutton" iconCls="icon-edit"
				onclick="taskmsg_project_edit_open();" plain="true">修改</a>
				<a href="javascript:void(0);" id="taskMgr_tool_del" class="easyui-linkbutton hide" iconCls="icon-remove"
				 onclick="taskmsg_project_setup_delete();" plain="true">删除</a>
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton hide" iconCls="icon-select11"
				 onclick="taskmsg_project_window_select_display_menu_open();" plain="true">会员中心菜单设置</a> -->
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton hide" iconCls="icon-select11"
				 onclick="projectWindowTaskMsrSubPro.open()" plain="true">子项目任务特殊设置</a> -->
				<a href="javascript:void(0);" id="taskMgr_tool_time" class="easyui-linkbutton hide" iconCls="icon-select11"
					onclick="taskMgr_setTime_show('','','')" plain="true">批量调整任务时间</a>
			</div>
		</div>
		<!-- end of toolbar -->
		<!-- ,fitColumns:true -->
		 <div style="height: calc( 100vh - 170px )">
		<table id="task-mgr-detail-datagrid" class="easyui-datagrid- hide" toolbar="task-mgr-detail-toolbar" data-options="rownumbers:true,singleSelect:false,multiSort:true
			,method:'post',fitColumns:true,fit:true
			,selectOnCheck:true,pagination: true,pageSize: 20
			,onClickRow: task_mgr_onClickRow
		">
			<!-- <thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'taskKindName',width:40">任务类型</th>
					<th data-options="field:'projectName',width:80">项目</th>
					<th data-options="field:'f_step_type',width:40">环节</th>
					<th data-options="field:'taskTopic',width:110,editor:'textbox'">任务主题</th>
					<th data-options="field:'taskTopicEn',width:110,editor:'textbox'">任务主题(英文)</th>
					<th data-options="field:'startTime', width:180,formatter: taskMgr_fmtTime">填报时间</th>
					<!-- <th data-options="field:'startTime',width:100,editor:{type:'datebox'}">开始时间</th>
					<th data-options="field:'endTime',width:100,editor:{type:'datebox'}">截止时间</th> -- >
					<th data-options="field:'taskBelong',width:100,
						formatter:function(val, row, index){
								if(val=='client_serve')return '展商和客服';
								else if(val=='serve') return '客服';
								else return val;
						},
						editor:{
							type: 'combobox',
							options: {
								valueField: 'id',
								textField: 'text',
								editable: false,
								panelHeight:'auto',
								panelMaxHeight:'200px',
								data:[{'id':'client_serve','text':'展商和客服'},{'id':'serve','text':'客服'}]
							}
						}">任务归属</th>
					<th data-options="field:'memo',width:120,editor:'textbox'">备注</th>
					<th data-options="field:'excuteOperId',width:40,formatter:function(value,row){return row.excuteOperName;},editor:task_mgr_editor">角色</th>
					<th data-options="field:'isSet',width:40,formatter: taskMgr_fmtTool">任务信息</th>
				</tr>

			</thead> -->
		</table>	 </div>
		<script>
			function taskMgr_fmtTool(value,row,index) {
				if(['VISA_MATERIALS','VISA'].includes(row.taskKindCode)) return '';
				let msg = ' (默认)';
				if(row.enableCustomSet) msg = '';//row.taskKindCode === 'FILE' ||
				return `<a href="javascript:void(0);" style="color: #4C72FE;display:inline-block;" onclick="taskMgr_goTaskDetail(\${row.taskId || 0},'\${row.taskKindCode}','\${row.taskKindName}',\${!msg},\${row.projectId}, '\${row.taskCategory}');return false;" >设置\${msg}</a>`;
			}
			function taskMgr_goTaskDetail(taskId, taskKindCode='', taskKindName, isSelf = false, pid, taskCategory ='') {
				const tasks = {
					PROD: 'product',
					WRITE_INSTRUCTIONS: 'filling_explanation',//填写说明
					BUILDER_BOOTH_TOOL: 'construct',//搭建方
				}
				if(taskKindCode == 'EVENT') taskKindCode += '_' + taskCategory;
				const page = tasks[taskKindCode] || taskKindCode.toLocaleLowerCase();
				if(!page){
					//  $('#project-document-task-set').html('<h3 style="text-align: center">暂未开启</h3>');
					err('暂未开启！', '','info', true);
				}
				top.addTab(taskKindName + (isSelf ? '自定义' : '默认') + '设置', eippath + "/pages/system/task_"+ page+".html?taskId=" + taskId
				+ "&orgNum=" + org_num_for_pass
				+ "&v=240905"
				+ "&exhibitCode=" + exhibitCode_for_pass
				+ "&projectId=" + pid, "icon-tip", true, true, true);
				// + "&projectId=" + project_for_pass, "icon-tip", true, true, true);
			}
			function taskMgr_fmtTime(value,row,index) {
				const msg = (row.startTime || row.endTime) ? (row.startTime || '~') + ' 至 ' + (row.endTime || '~') : '设置';
				return '<a href="javascript:void(0);" style="color: #4C72FE;display:inline-block;" onclick="taskMgr_setTime_show('+ row.taskId+',\''+ (row.startTime || '')+'\',\''+ (row.endTime || '')+'\')">'+ msg+'</a>';
			}
			function taskMgr_setTime_save() {
				projectWindowTaskMsrSubPro.updTime();
			}
			function taskMgr_setTime_cancel() {
				$('#taskMgr-window-setTime').window('close')
			}
			function taskMgr_setTime_show(id,startTime,endTime) {
				if(!id) {
					const rows = $('#task-mgr-detail-datagrid').datagrid('getSelections');
					if(!rows || rows.length<1) projectWindowTaskMsrSubPro.err('请选择操作行')
					id = rows.map(it=> it.taskId).join();
				}
				$('#taskMgr-window-setTime-id').val(id);
				$('#taskMgr-window-setTime-startTime').datebox('setValue', startTime || '');
				$('#taskMgr-window-setTime-endTime').datebox('setValue', endTime || '');
				$('#taskMgr-window-setTime').window({title: id ? '批量调整任务时间' : '填报时间'}).window('open').window('center');
			}
		</script>
		<div id="task-mgr-initTip" class="hide" style="height: calc(100vh - 180px);width: 100%;display: flex;align-items: center;justify-content: center;">
			<center style="">
				<img src="../../img/imgs/task_df.png" alt="" style="width: 94px;height: 94px;">
				<h2 style="color: #585046;font-size: 26px;line-height: 32px;font-weight: normal;">项目未设置展商参展须填报任务信息，<br/>需初始化项目任务后即可正常使用展商参展填报</h2>
				<button style="color: white;background: #1685ed;border-radius: 4px;line-height: 32px;
				padding: 0 42px;margin-bottom: 35px;
				outline: none;border: none;" onclick="projectWindowTaskMsrSubPro.selfEnable()" >初始化任务</button>
			</center>
		</div>
	</div>
</div>
<div id="taskMgr-window-setTime" class="easyui-window" title="填报时间"
	toolbar="#taskMgr-window-setTime-toolbar"
 	data-options="closed:true,modal:true" style="width: 580px;">
	<!-- begin of toolbar -->
	<div id="taskMgr-window-setTime-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="taskMgr_setTime_save()"
			 plain="true">保存</a>
			<a href="javascript:void(0);" id="taskmsg-project-setup-cancel" class="easyui-linkbutton" iconCls="icon-cancel"
			 onclick="taskMgr_setTime_cancel()" plain="true">取消</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<div id="taskMgr-window-setTime-1" class="easyui-panel" title="" style="padding:10px;">
		<input type="hidden" id="taskMgr-window-setTime-id" >
		<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<label>开始时间</label>&nbsp;&nbsp;
				<input id="taskMgr-window-setTime-startTime" class="easyui-datebox" style="width:175px;height:25px">
			</li>
			<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<label>截止时间</label>&nbsp;&nbsp;
				<input id="taskMgr-window-setTime-endTime" class="easyui-datebox" style="width:175px;height:25px">
			</li>
		</ul>
	</div>
</div>

<%@ include file="../common/project_window_taskMgr.jsp" %>

<style>
	.taskmgr-hide {
		display: none !important;
	}
	.taskmsg-li {
		width: 275px;margin-right:15px;margin-bottom:5px;
		display: inline-flex;align-items: center;
	}
	.taskmsg-label {
		width: 110px;text-align: right;display: inline-block;
		padding-right: 15px;
	}
</style>
<div id="taskmsg-project-window-edit" class="easyui-window" toolbar="#taskmsg-project-window-edit-toolbar"
 data-options="closed:true,title:'任务编辑',modal:true" style="width: 650px;max-height:60%;">
	<!-- begin of toolbar -->
	<div id="taskmsg-project-window-edit-toolbar" style="border-bottom: 1px solid #ccc;">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" id="taskmsg-project-edit-save" class="easyui-linkbutton" iconCls="icon-save" onclick="taskmsg_project_edit_save()"
			 plain="true">保存</a>
			<a href="javascript:void(0);" id="taskmsg-project-edit-cancel" class="easyui-linkbutton" iconCls="icon-cancel"
			 onclick="taskmsg_project_edit_cancel()" plain="true">取消</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<!-- <div id="taskmsg-project-panel-edit-1" class="easyui-panel" title="" style="width:558px;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;"> -->
			<ul style="list-style:none;margin:0;padding:20px;">
				<li class="taskmsg-li">
					<label class="taskmsg-label"><i style="color: red">*</i> 项目名称</label>
					<input id="taskmsg-project-edit-taskId" type="hidden" >
					<select id="taskmsg-project-edit-projectId" data-options="panelMaxHeight: 200,panelHeight:'auto',limitToList: true,valueField: 'projectId',textField: 'projectName',disabled: true," readonly class="easyui-combobox" style="width:175px;"></select>
				</li>
				<li class="taskmsg-li">
					<label class="taskmsg-label">任务主题</label>
					<input id="taskmsg-project-edit-taskTopic" class="easyui-textbox" style="width:175px;">
				</li>
				<li class="taskmsg-li taskmgr-hide taskmgr-en-show">
					<label class="taskmsg-label">任务主题(英文)</label>
					<input id="taskmsg-project-edit-taskTopicEn" class="easyui-textbox" style="width:175px;">
				</li>
				<li class="taskmsg-li">
					<label class="taskmsg-label">开始时间</label>
					<input id="taskmsg-project-edit-startTime" class="easyui-datebox" style="width:175px;">
				</li>
				<li class="taskmsg-li">
					<label class="taskmsg-label">截止时间</label>
					<input id="taskmsg-project-edit-endTime" class="easyui-datebox" style="width:175px;">
				</li>
				<li class="taskmsg-li">
					<label class="taskmsg-label">任务归属</label>
					<input id="taskmsg-project-edit-taskBelong" data-options="valueField: 'id',
					textField: 'text',
					editable: false,
					panelHeight:'auto',
					panelMaxHeight:'200px',
					data:[{'id':'client_serve','text':'展商和客服'},{'id':'serve','text':'客服'}]" class="easyui-combobox" style="width:175px;">
				</li>
				<li class="taskmsg-li">
					<label class="taskmsg-label">角色</label>
					<input id="taskmsg-project-edit-excuteOperId" data-options="
						valueField: 'excuteOperId',
						textField: 'excuteOperName',
						panelHeight: 'auto',
						method: 'post',
						url: eippath + '/operator/selectByProjectId2?projectId=' + investigate_project_projectId
					" class="easyui-combobox" style="width:175px;">
				</li>
				<li class="taskmsg-li" style="width: 569px;margin-right: 0;
					display: flex;box-sizing: border-box;align-items: flex-start;">
					<label class="taskmsg-label" style="width: 93px;">备注</label>
					<div style="flex: 1;overflow-x: hidden;">
						<input id="taskmsg-project-edit-memo" class="easyui-textbox" data-options="multiline: true" style="width: 455px;height: 70px;">
					</div>
				</li>
			</ul>
		<!-- </ul>
	</div> -->
</div>
<script>
	function taskmsg_project_edit_open() {
		var row = $('#task-mgr-detail-datagrid').datagrid("getSelected");
		if(!row){
			$.messager.alert('提示', '请选择操作行', 'warning');return;
		}
		$('#taskmsg-project-edit-taskId').val(row.taskId || '')
		$('#taskmsg-project-edit-taskTopic').textbox('setValue', row.taskTopic || ''),
		$('#taskmsg-project-edit-taskTopicEn').textbox('setValue', row.taskTopicEn || ''),
		$('#taskmsg-project-edit-startTime').datebox('setValue', row.startTime || ''),
		$('#taskmsg-project-edit-endTime').datebox('setValue', row.endTime || ''),
		$('#taskmsg-project-edit-memo').textbox('setValue', row.memo || ''),
		$('#taskmsg-project-edit-excuteOperId').combobox('setValue', row.excuteOperId || ''),
		$('#taskmsg-project-edit-taskBelong').combobox('setValue', row.taskBelong || ''),
		$('#taskmsg-project-edit-projectId').combobox('setValue', row.projectId || ''),
		$('#taskmsg-project-window-edit').window('open');
	}
	function taskmsg_project_edit_save() {
		$.ajax({
			url: eippath + '/task/updateTaskById',
			dataType: "json", //返回数据类型
			type: "post",
			data: JSON.stringify({
				taskId: $('#taskmsg-project-edit-taskId').val() || '',
				taskTopic: $('#taskmsg-project-edit-taskTopic').textbox('getValue') || '',
				taskTopicEn: $('#taskmsg-project-edit-taskTopicEn').textbox('getValue') || '',
				startTime: $('#taskmsg-project-edit-startTime').datebox('getValue') || '',
				endTime: $('#taskmsg-project-edit-endTime').datebox('getValue') || '',
				memo: $('#taskmsg-project-edit-memo').textbox('getValue') || '',
				excuteOperId: $('#taskmsg-project-edit-excuteOperId').combobox('getValue') || '',
				taskBelong: $('#taskmsg-project-edit-taskBelong').combobox('getValue') || '',
			}),
			async: false,
			contentType: "application/json; charset=utf-8",
			success(data) {
				if(data.state != 1) {
					$.messager.alert('提示','保存失败！','error');return;
				}
				$.messager.alert('提示','保存成功！','info');
				projectWindowTaskMsrSubPro.freshMain();
				taskmsg_project_edit_cancel()
			},
			error() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}
	function taskmsg_project_edit_cancel() {
		$('#taskmsg-project-window-edit').window('close', true);
	}
</script>
<!-- begin of easyui-window -->
<div id="taskmsg-project-window-setup" class="easyui-window" toolbar="#taskmsg-project-window-setup-toolbar"
 data-options="closed:true,title:'任务设置',modal:true" style="width:600px;">
	<!-- begin of toolbar -->
	<div id="taskmsg-project-window-setup-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" id="taskmsg-project-setup-save" class="easyui-linkbutton" iconCls="icon-save" onclick="taskmsg_project_setup_save()"
			 plain="true">保存</a>
			<a href="javascript:void(0);" id="taskmsg-project-setup-cancel" class="easyui-linkbutton" iconCls="icon-cancel"
			 onclick="taskmsg_project_setup_cancel()" plain="true">取消</a>
			<a href="javascript:void(0);" class="fu_task_kind_org easyui-linkbutton hide" iconCls="icon-db"
			 onclick="top.goOrgMenu('task_kind_org')" plain="true">任务类别设置</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<div id="taskmsg-project-panel-setup-1" class="easyui-panel" title="基本信息" style="padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
			<ul style="list-style:none;margin:0;padding:0;">
				<li style="height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					<label><i style="color: red">*</i>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select id="taskmsg-project-textbox-projectId" data-options="panelMaxHeight: 200,panelHeight:'auto',limitToList: true,valueField: 'projectId',textField: 'projectName',onSelect: function(rec){
						loadTask(rec.projectId);
					} " class="easyui-combobox" style="width:225px;height:25px"></select>
				</li>
				<!-- <li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目编号</label>&nbsp;&nbsp;&nbsp;&nbsp;<input
					 id="taskmsg-project-textbox-projectId" class="easyui-textbox" style="width:175px;height:25px" readonly></li>
				<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input
					 id="taskmsg-project-textbox-projectName" class="easyui-textbox" style="width:175px;height:25px" readonly></li>
				<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<input
					 id="taskmsg-project-combobox-proj-kind" class="easyui-textbox" style="width:175px;height:25px" readonly></li>
				<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目ID</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input
					 id="taskmsg-project-combobox-proj-id" class="easyui-textbox" style="width:175px;height:25px" readonly></li> -->
			</ul>
		</ul>
	</div>
	<div id="taskmsg-project-panel-2" class="easyui-panel" title="任务信息" style="height:400px;">
		<div class="easyui-layout" data-options="fit:true">
			<!--
    		<div data-options="region:'north'" style="height:47px;padding:10px;">
    			<label>分组名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="taskmsg-project-textbox-groupName" class="easyui-textbox" style="width:175px;height:25px">
    		</div> -->
			<div data-options="region:'center'">
				<div class="easyui-layout" data-options="fit:true">
					<div data-options="region:'center'">
						<table id="taskmsg-project-datagrid-taskKind" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
							<thead>
								<tr>
									<th data-options="field:'ck',checkbox:true"></th>
									<th data-options="field:'f_task_kind_code2',width:20">任务类别代号</th>
									<th data-options="field:'f_task_memo',width:40">任务类别名称</th>
								</tr>
							</thead>
						</table>
					</div>
					<!-- <div data-options="region:'east'" style="width:100px;padding:16px;">
		    			<div class="my-toolbar-button">

				        	<a href="javascript:void(0);" id="taskmsg-project-add2" class="easyui-linkbutton" onclick="taskmsg_project_add2()" plain="false" style="width:60px;height:25px">添加</a>
				        	<br /><br />
				        	<a href="javascript:void(0);" id="taskmsg-project-del2" class="easyui-linkbutton" onclick="taskmsg_project_del2()" plain="false" style="width:60px;height:25px">删除</a>
					    </div>
		    		</div> -->
				</div>
			</div>
		</div>
	</div>
</div>
<!-- begin of easyui-window -->
<div id="taskmsg-project-window-select-display-menu" class="easyui-window" data-options="closed:true,title:'会员中心菜单设置',modal:true"
 style="width:572px;height: 560px">
	<div id="taskmsg-project-panel-select-display-menu" class="easyui-panel" style="width:558px;height: 100%;">
		<div class="easyui-layout" data-options="fit:true">
			<form id="member_center_menu_settings_form" onsubmit="false">
				<ul style="list-style:none;margin:0;padding:0;border-bottom:0px solid #ccc;height:440px;width:200px">
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="project-information" name="member_inform_tab" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;展会项目信息</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="notice-document" name="member_inform" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;展会通知文件</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="exhibition-information" name="member_info" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;参展信息填报</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="exhibition-progress-inquiry" name="member_schedule" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;参展进度查询</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="booth-map-reservation" name="exhibits-reserve" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;展位图预定</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="my-booth-order" name="member_myorder" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;我的展位订单</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="invitation-setting" name="member_invitation" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;邀请函设置</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience" name="Invite-audience" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;邀约观众</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience1" name="matching" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;供需智能匹配</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience2" name="my_collect" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;我的收藏</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience3" name="enquiry-centre" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;询盘中心</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience4" name="exhibits-interest" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;被关注的展品</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience5" name="invite-results" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;邀约结果统计</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="inviting-audience6" name="make_invoicel" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;申请开票</label>
						</div>
					</li>
					<li style="width:300px;height:25px;margin-right:15px;margin-bottom:5px;">
						<div style="width:300px;height:25px;display:inline-block;padding-left: 45%;">
							<label><input id="exhibition-apply" name="exhibition_apply" type="checkbox" class="my-checkbox" checked="checked" />&nbsp;参展申请</label>
						</div>
					</li>
				</ul>
			</form>
			<div data-options="region:'south'" style="height:57px;padding:12px;">
				<div class="my-toolbar-search">
					<div style="float:right;">
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="sendMemberCenterMenuSettings()" style="width:60px;height:25px">确定</a>
						&nbsp;
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="taskmsg_project_window_select_display_menu_closed()"
						 style="width:60px;height:25px">返回</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- begin of easyui-window -->
<div id="taskmsg-project-window-select-taskKind" class="easyui-window" data-options="closed:true,title:'选取任务类别',modal:true"
 style="width:572px;">
	<div id="taskmsg-project-panel-select-taskKind" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'">
				<table id="taskmsg-project-datagrid-select-taskKind" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'taskKindCode',width:20">任务类别代号</th>
							<th data-options="field:'taskKindName',width:40">任务类别名称</th>
						</tr>
					</thead>
				</table>
			</div>
			<div data-options="region:'south'" style="height:57px;padding:12px;">
				<div class="my-toolbar-search">
					<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="taskmsg-project-keyword-select-taskKind" class="my-text"
					 style="width:150px">&nbsp;
					<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="taskmsg_project_search_select_taskKind()">查询</a>
					<div style="float:right;">
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="taskmsg_project_ok_select_taskKind()" plain="false"
						 style="width:60px;height:25px">确定</a>
						&nbsp;
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="taskmsg_project_return_select_taskKind()" plain="false"
						 style="width:60px;height:25px">返回</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	$(function(){
		setTimeout(() => {
			task_mgr_checkEnSetting();
			projectWindowTaskMsrSubPro.freshMain();
			projectWindowTaskMsrSubPro.getProjectWithChild(exhibitCode_for_pass, '#taskmsg-project-textbox-projectId,#taskmsg-project-edit-projectId');
		}, 0);
		if(!fu_task_kind_org) {
			$('.fu_task_kind_org').remove()
		} else {
			$('.fu_task_kind_org').removeClass('hide')
		}
	})
	var investigate_project_projectId = getQueryString('projectId')
	var exhibitCode = getQueryString('exhibitCode')
	console.log(investigate_project_projectId,exhibitCode)
	$.extend($.fn.datagrid.defaults.editors, {
		//扩展dategrid的editors的类型
		datebox: {
			init: function(container, options) {
				var input = $('<input type="text" data-options="onChange:onChangeDate" class="easyui-datebox">').appendTo(
					container);
				//编辑框延迟加载
				window.setTimeout(function() {
					input.datebox($.extend({
						editable: false
					}, options));
				}, 10);
				return input;
			},
			getValue: function(target) {
				return $(target).datebox('getValue');
			},
			setValue: function(target, value) {
				$(target).val(value);
				window.setTimeout(function() {
					$(target).datebox('setValue', value);
				}, 10);
			},
			resize: function(target, width) {
				var input = $(target);
				if ($.boxModel == true) {
					input.width(width - (input.outerWidth() - input.width()));
				} else {
					input.width(width);
				}
			}
		}
	});
	function getQueryString(name) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
		var r = window.location.search.substr(1).match(reg);
		if (r != null) return unescape(r[2]);
		return null;
	}
	// $(function() {
	// 	// var investigate_project_projectId = 1837
	// 	console.log(investigate_project_projectId,exhibitCode)
	// 	//改版，先注释
	// 	setTimeout(function() {
	// 		/* $('#task-mgr-detail-datagrid').datagrid({
	// 			url: eippath + '/data/clear_datagrid.json'
	// 		});
	// 		$('#task-mgr-datagrid').datagrid({
	// 			url: eippath + '/task/selectByProjectId2',
	// 			queryParams: {
	// 				projectId: investigate_project_projectId
	// 			}
	// 			,onLoadSuccess:function(data){
	// 				console.log(data.rows == ''  );
	// 				if(data.rows == ''){
	// 					loadTask()
	// 				}
	// 			}
	// 		});*/
	// 		//初始化表格
	// 		task_mgr_detail(null, investigate_project_projectId)
	// 	}, 100);
	// 	setTimeout(function() {
	// 		var rows = $('#task-mgr-detail-datagrid').datagrid("getRows");
	// 		if (rows.length == 0) {
	// 			loadTask();
	// 		}
	// 		$("#task-mgr-detail-mgr-projectName").html(rows[0].projectName);
	// 	}, 1000);
	// });

	/**
	 * 时间变化回调
	 */
	function onChangeDate() {
		// console.log($(this).datebox('getValue'));
		// $('#task-mgr-detail-datagrid').datagrid('endEdit', task_mgr_editIndex);
		// $(".datebox-calendar-inner").parent().css("display", "none"); //隐藏元素
		// task_mgr_detail_save();
	}

	function loadtaskTop1() {
		$('#task-mgr-detail-datagrid').datagrid({
			url: eippath + '/data/clear_datagrid.json'
		});
		$('#task-mgr-datagrid').datagrid({
			url: eippath + '/task/selectByProjectId2',
			queryParams: {
				projectId: investigate_project_projectId
			}
		});
	}
	function loadTask(pid='') {
		$.ajax({
			url: eippath + '/project/selectByProjectId?editMode=Add',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				projectId: pid || investigate_project_projectId
			}, //发送数据  projectId  $("#proform").serialize()
			async: true,
			success: function(data) {
				if(pid) { // onSelect
					$('#taskmsg-project-datagrid-taskKind').datagrid({
						url: eippath + '/taskKind/selectTaskKindByProKind?proKind=' + data.projKindCode + '&projectId=' + pid,
						loadFilter: function(data){
							(data || []).map(it=> {
								if(it.f_task_kind_code == 'EVENT') {
									it.f_task_kind_code2 = 'EVENT/' + it.f_task_category
								} else {
									it.f_task_kind_code2 = it.f_task_kind_code
								}
								return it
							});
							return data;
						}
					});
					return
				}
				console.log(data);
				$("#task-mgr-detail-mgr-projectName").html(data.projectName);
				$('#taskmsg-project-window-setup').window('open').window('center');
				$("#taskmsg-project-textbox-projectId").combobox("setValue", data.projectId);
				// $("#taskmsg-project-textbox-projectName").textbox("setValue", data.projectName);
				// $('#taskmsg-project-combobox-proj-kind').textbox('setValue', data.projKindName);
				// $('#taskmsg-project-combobox-proj-id').textbox('setValue', data.projectId);
				var exhibitType = data.projKindCode;
				$('#taskmsg-project-datagrid-taskKind').datagrid({
					url: eippath + '/taskKind/selectTaskKindByProKind?proKind=' + exhibitType + '&projectId=' +
						investigate_project_projectId,
					loadFilter: function(data){
						(data || []).map(it=> {
							if(it.f_task_kind_code == 'EVENT') {
								it.f_task_kind_code2 = 'EVENT/' + it.f_task_category
							} else {
								it.f_task_kind_code2 = it.f_task_kind_code
							}
							return it
						});
						return data;
					}
				});
				return;
				/* if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					var  ecode = $('#add-project-select-add').combobox('getValue');
					reflushByAdd(ecode)
					$('#ini-project-window-setup').window('close', true);
				}else{
					$.messager.alert('提示','保存失败！','error');
				} */
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});

		/*
			$("#investigate-project-textbox-projectId").textbox("setValue", row.projectId);
			$("#investigate-project-textbox-projectName").textbox("setValue", row.projectName);
			$('#investigate-project-combobox-proj-kind').combobox('setValue', row.projKindCode);
		*/

	}

	function task_mgr_formatDo(val, row) {
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='task_mgr_set(" + row.projectId +
			",\"" + row.taskKindCode + "\")'>设置</a>";
	}
	var task_mgr_editor = {
		type: 'combobox',
		options: {
			valueField: 'excuteOperId',
			textField: 'excuteOperName',
			panelHeight: 'auto',
			method: 'post',
			url: eippath + '/operator/selectByProjectId2?projectId=' + investigate_project_projectId,
		}
	}

	window.task_mgr_enEnable = false;
	// 启用了英文版？
	function task_mgr_checkEnSetting() {
		$.ajax({
			url: eippath + '/project/information/getSystemEnVersionSet',
			dataType: 'json',
			type: 'post',
			async: false,
			success(data) {
				if(!data || data.state != 1) return $.messager.alert('提示', '请求失败', 'error');
				if(data.data === true) {
					task_mgr_enEnable = true;
					$('.taskmgr-en-show').removeClass('taskmgr-hide');
				}
			},
			error() {
				$.messager.alert('提示', '请求失败', 'error');
			}
		})
	}
	//初始化任务表格
	function task_mgr_detail(taskKindCode, projectId) {
		// $('#task-mgr-detail-datagrid').datagrid({
		// 	url: eippath + '/data/clear_datagrid.json'
		// });
		$('#task-mgr-detail-datagrid').datagrid({
			// url: eippath + '/task/selectByProjectId2AndTaskKindCode',
			// url: eippath + '/task/getTaskList',
			url: eippath + '/task/getTaskPage',
			columns: [
				[
				{field:'ck',checkbox:true},
				{field:'taskKindName',width:40,title:'任务类型'},
				{field:'projectName',width:80,title:'项目'},
				{field:'f_step_type',width:40,title:'环节'},
				{field:'taskTopic',width:110,editor:'textbox',title:'任务主题'},
				...(task_mgr_enEnable ? [{field:'taskTopicEn',width:110,editor:'textbox',title:'任务主题(英文)'}]: []),
				{field:'startTime', width:180,formatter: taskMgr_fmtTime,title:'填报时间'},
				{field:'taskBelong',width:100,formatter:function(val, row, index){
							if(val=='client_serve')return '展商和客服';
							else if(val=='serve') return '客服';
							else return val;
					},editor:{
						type: 'combobox',
						options: {
							valueField: 'id',
							textField: 'text',
							editable: false,
							panelHeight:'auto',
							panelMaxHeight:'200px',
							data:[{'id':'client_serve','text':'展商和客服'},{'id':'serve','text':'客服'}]
						}
					}, title: '任务归属'},
				{field:'memo',width:120,editor:'textbox',title: '备注'},
				{field:'excuteOperId',width:40,formatter:function(value,row){return row.excuteOperName;},editor:task_mgr_editor,title:'角色'},
				{field:'isSet',width:40,formatter: taskMgr_fmtTool,title:'任务信息'},
			]
			],
			queryParams: {
				projectId: projectId,
				exhibitCode: exhibitCode,
				taskKindCode: taskKindCode
			},
			// onLoadSuccess(data, node) {
			// 	if(data.rows && data.rows.length) return;
			// 	loadTask();
			// }
		});
	}
	var task_mgr_editIndex = null;
	var task_mgr_editDate  = null;
	/**
	 * 开始编辑
	 */
	function task_mgr_onClickRow(index, rowData) {
		// //给编辑框绑定失去焦点事件
		// $(".datagrid-row-editing .textbox-text").on("blur", function(e) {
		// 	task_mgr_detail_save()
		// });
		if (task_mgr_editIndex != null && task_mgr_editIndex != index) {
			task_mgr_endEditing();
			$('#task-mgr-detail-datagrid').datagrid('beginEdit', index);
		} else if (task_mgr_editIndex == null) {
			$('#task-mgr-detail-datagrid').datagrid('beginEdit', index);
		}
		task_mgr_editIndex = index;
		task_mgr_editData  = rowData;
	}

	function task_mgr_endEditing() {
		var ed = $('#task-mgr-detail-datagrid').datagrid('getEditor', {
			index: task_mgr_editIndex,
			field: 'excuteOperId'
		});
		if (ed == null) {
			$('#task-mgr-detail-datagrid').datagrid('endEdit', task_mgr_editIndex);
			return false;
		}
		if ($(ed.target).combobox('getValue') != null && $(ed.target).combobox('getValue') != '') {
			$('#task-mgr-detail-datagrid').datagrid('getRows')[task_mgr_editIndex]['excuteOperId'] = $(ed.target).combobox(
				'getValue');
			$('#task-mgr-detail-datagrid').datagrid('getRows')[task_mgr_editIndex]['excuteOperName'] = $(ed.target).combobox(
				'getText');
		}
		$('#task-mgr-detail-datagrid').datagrid('endEdit', task_mgr_editIndex);
	}
	//任务明细修改保存
	function task_mgr_detail_save() {
		task_mgr_endEditing();
		// if (task_mgr_editIndex != null) {
		// 	task_mgr_endEditing();
		// 	task_mgr_editIndex = null;
			var postData = {
				projectId: investigate_project_projectId,
				exhibitCode,
			};
			// var rows = $('#task-mgr-detail-datagrid').datagrid("getSelections") || []
			// var rows = [$('#task-mgr-detail-datagrid').datagrid("getRows")[task_mgr_editIndex]];
			var rows = $('#task-mgr-detail-datagrid').datagrid("getRows");
			if (rows && rows.length) {
				for (var i = 0; i < rows.length; i++) {
					postData['tasks[' + i + '].taskId'] = rows[i].taskId;
					postData['tasks[' + i + '].taskKindCode'] = rows[i].taskKindCode;
					postData['tasks[' + i + '].projectId'] = rows[i].projectId;
					postData['tasks[' + i + '].taskTopicEn'] = rows[i].taskTopicEn;
					postData['tasks[' + i + '].taskTopic'] = rows[i].taskTopic;
					if (rows[i].startTime != '' && rows[i].startTime != null)
						postData['tasks[' + i + '].startTime'] = rows[i].startTime;
					if (rows[i].endTime != '' && rows[i].endTime != null)
						postData['tasks[' + i + '].endTime'] = rows[i].endTime;
					postData['tasks[' + i + '].memo'] = rows[i].memo;
					if (rows[i].excuteOperId != '' && rows[i].excuteOperId != null)
						postData['tasks[' + i + '].excuteOperId'] = rows[i].excuteOperId;
					if (rows[i].taskBelong != '' && rows[i].taskBelong != null)
						postData['tasks[' + i + '].taskBelong'] = rows[i].taskBelong;
				}
				$.ajax({
					url: eippath + '/task/batchUpdateTask',
					dataType: "json", //返回数据类型
					type: "post",
					data: postData, //发送数据
					async: false,
					success: function(data) {
						if (data.state == 1) {
							// $.messager.alert('提示','保存成功！','info');
							$.messager.show({
								title: '提示',
								msg: '保存成功',
								timeout: 1000,
								showType: 'slide'
							});
						} else {
							$.messager.show({
								title: '提示',
								msg: '保存失败',
								timeout: 100,
								showType: 'slide'
							});
							// $.messager.alert('提示','保存失败！','error');
						}
					},
					error: function() {
						$.messager.alert('提示', '数据发送失败！', 'error');
					}
				});
			}else{
				$.messager.alert('提示', '请先选择!','info');
			}
		// }
	}

	function taskmsg_project_add2() {
		$('#taskmsg-project-window-select-taskKind').window('open').window('center');
		$('#taskmsg-project-datagrid-select-taskKind').datagrid({
			url: eippath + '/taskKind/selectTaskKind?keyword=' + ''
		});
	}

	function taskmsg_project_ok_select_taskKind() {
		var rows = $('#taskmsg-project-datagrid-select-taskKind').datagrid('getSelections');
		var rows2 = $("#taskmsg-project-datagrid-taskKind").datagrid("getRows");
		if (rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				flag = 0;
				for (var j = 0; j < rows2.length; j++) {
					if (rows2[j].taskKindCode == rows[i].taskKindCode) {
						flag = 1;
						break;
					}
				}
				if (flag == 0) {
					$('#taskmsg-project-datagrid-taskKind').datagrid('appendRow', {
						f_task_kind_code: rows[i].taskKindCode,
						f_task_memo: rows[i].taskKindName
					});
				}
			}
			$('#taskmsg-project-window-select-taskKind').window('close');
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function taskmsg_project_setup_cancel() {
		$('#taskmsg-project-window-setup').window('close', true);
	}

	function taskmsg_project_del2() {
		var rows = $('#taskmsg-project-datagrid-taskKind').datagrid('getSelections');
		if (rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var rowIndex = $('#taskmsg-project-datagrid-taskKind').datagrid('getRowIndex', rows[i]);
				$('#taskmsg-project-datagrid-taskKind').datagrid('deleteRow', rowIndex);
			}
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function taskmsg_project_setup_save() {
		var postData = {
			exhibitCode,
			projectId: $("#taskmsg-project-textbox-projectId").combobox('getValue'),
			// projectId: $("#taskmsg-project-combobox-proj-id").val(),
		};
		/* postData['editMode'] = 'Add';
		postData['projectId'] = $("#taskmsg-project-textbox-projectId").val();
		postData['projectName'] = $("#taskmsg-project-textbox-projectName").val();
		postData['projKindCode'] = $('#taskmsg-project-combobox-proj-kind').combobox('getValue');
		postData['groupName'] = $("#taskmsg-project-textbox-projectName").val(); */
		var rows = $('#taskmsg-project-datagrid-taskKind').datagrid("getSelections");
		if (rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				postData['taskKinds[' + i + '].taskCategory'] = rows[i].f_task_category;
				postData['taskKinds[' + i + '].taskKindCode'] = rows[i].f_task_kind_code;
				postData['taskKinds[' + i + '].taskKindName'] = rows[i].f_task_kind_name;
				postData['taskKinds[' + i + '].taskKindNameEn'] = rows[i].f_task_kind_name_en;
			}
		} else {
			$.messager.alert('提示', '未选中任务！', 'warning');
			return;
		}
		$.messager.progress({
			title: '请稍后',
			msg: '处理数据...',
			text: '处理中.......'
		});
		$.ajax({
			url: eippath + '/project/saveTask',
			dataType: "json", //返回数据类型
			type: "post",
			data: postData, //发送数据
			async: true,
			success: function(data) {
				if (data.state == 1) {
					$.messager.alert('提示', '保存成功！', 'info');
					$('#taskmsg-project-window-setup').window('close', true);
					$("#task-mgr-detail-datagrid").datagrid("reload");
					$("#task-mgr-detail-datagrid").datagrid("clearSelections");
					taskmsg_project_currency_insert();
				} else {
					$.messager.alert('提示', '保存失败！', 'error');

				}
				setTimeout(function() {
					var rows = $('#task-mgr-detail-datagrid').datagrid("getRows");
					if (rows.length == 0) {
						loadTask();
					}
					console.log(rows[0].projectName);
				}, 1000);
				$.messager.progress('close');
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
				$.messager.progress('close');
			},
		});

		function taskmsg_project_currency_insert() {
			$.ajax({
				url: eippath + '/prodCurrency/selectOrInsert',
				dataType: "json", //返回数据类型
				type: "post",
				data: {
					f_project_id: investigate_project_projectId
				}, //发送数据
				success: function (data) {

				}
			})
		}

	}
	//删除任务类型
	function taskmsg_project_setup_delete() {
		var rows = $('#task-mgr-detail-datagrid').datagrid("getSelections");
		if (rows.length > 0) {
			var postData = {};
			for (var i = 0; i < rows.length; i++) {
				postData['tasks['+i+'].taskId'] = rows[i].taskId
				// postData[i] = {
				// 	taskId: rows[i].taskId
				// };
			}
			postData['exhibitCode'] = exhibitCode;
			postData['projectId'] = investigate_project_projectId;
			$.messager.confirm('提示', '删除任务，相关联的任务数据(展商填报数据)也将被删除，确定删除吗？', function(r) {
				if (r) {
					$.ajax({
						url: eippath + '/task/batchDeleteTask',
						dataType: "json", //返回数据类型
						type: "post",
						// contentType: "application/json",
						// data: JSON.stringify(postData), //发送数据
						data: postData, //发送数据
						success: function(data) {
							if (data.state == 1) {
								$.messager.alert('提示', '删除成功！', 'info');
								$("#task-mgr-detail-datagrid").datagrid("reload");
								$("#task-mgr-detail-datagrid").datagrid("clearSelections");
							} else {
								$.messager.alert('提示', '删除失败！', 'error');
							}
						},
						error: function() {
							$.messager.alert('提示', '数据发送失败！', 'error');
						}
					});
				}
			});
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}
	//新增任务
	function taskmsg_project_setup_add() {
		loadTask();
	}

	/**
	 * 开启会员中心菜单设置弹窗
	 */
	function taskmsg_project_window_select_display_menu_open() {
		$.ajax({
			url: eippath + '/project/getMenuSetting',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				"projectId": investigate_project_projectId
			},
			success: function(data) {
				var menuSetting = data.rows[0].businessMenuSetting;
				menuSetting = JSON.parse(menuSetting);
				$.each(menuSetting, function(key, value) {
					$("input[name='" + key + "']").prop("checked", true);
					if (value == false) {
						$("input[name='" + key + "']").prop("checked", false);
					}
				});
				$('#taskmsg-project-window-select-display-menu').window('open').window('center');
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}

	function taskmsg_project_window_select_display_menu_closed() {
		$('#taskmsg-project-window-select-display-menu').window('close', true);
	}

	/**
	 * 修改会员中心菜单请求方法
	 */
	function sendMemberCenterMenuSettings() {
		var postData = {};
		//展会项目菜单列表
		var lenghts = $("#member_center_menu_settings_form").find("input").length
		for (var i = 1; i <= lenghts; i++) {
			var name = ""
			name = $("#member_center_menu_settings_form").find("input").eq(i-1).attr("name")
			postData[name] = $("#member_center_menu_settings_form").find('input').eq(i-1).prop("checked") ? true : false;
		}
		var businessMenuSetting = JSON.stringify(postData);
		$.ajax({
			url: eippath + '/project/updateMenuSetting',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				"projectId": investigate_project_projectId,
				"businessMenuSetting": businessMenuSetting
			},
			success: function(data) {
				if (data.state === 1) {
					$.messager.alert('提示', '修改成功！', 'info');
				} else {
					$.messager.alert('提示', '修改失败！', 'error');
				}
				taskmsg_project_window_select_display_menu_closed()
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}

	//为jquery.serializeArray()解决radio,checkbox未选中时没有序列化的问题
	$.fn.ghostsf_serialize = function() {
		var a = this.serializeArray();
		var radio = $('input[type=checkbox]', this);
		var temp = {};
		$.each(radio, function(index, value) {
			if (!temp.hasOwnProperty(value.name)) {
				if ($("input[name='" + value.name + "']:checked").length === 0) {
					temp[value.name] = 'f';
					a.push({
						name: value.name,
						value: "f"
					});
				}
			}
		});
		return a;
	};
</script>
