<!--进馆证管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style scoped="scoped">
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }
</style>
<table
    id="check-point-statistics-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
          onClickRow: function (rowIndex, rowData) {
            var l = $(this).datagrid('getRows').length;
            for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
 				},"
    toolbar="#check-point-statistics-toolbar"
>
  <thead>
  <tr id="check-point-statistics-datagrid-tr">
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="sectionName" width="180" align="center">核销点位置</th>
    <th field="checkPointName" width="180" align="center">核销点</th>
    <th field="checkTime" width="300"  align="center">可核销时间段</th>
    <th field="buyerCount" width="120" align="center">参会人数</th>
    <th field="checkCount" width="120" align="center">核销次数</th>
    <th field="setNumber" width="120" align="center">座位数</th>
    <th field="setNumberRate" width="120" align="center">上座率</th>
  </tr>
  </thead>

</table>
<div id="check-point-statistics-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:check_point_statistics_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;margin-bottom: 18px">核销点类型</span>
    <select id="check-point-statistics-keyword-siteType" class="easyui-combobox" style="width:150px;"
            data-options="panelHeight:'auto'">
      <option selected value="">全部</option>
      <option value="1">展会进场</option>
      <option value="2">会议签到</option>
    </select>&nbsp;&nbsp;&nbsp;&nbsp;
    <label>核销点位置</label>&nbsp;
    <div style="position: relative;display: inline-block;margin-right: 10px;">
      <input
          id="check-point-statistics-keyword-sectionCode"
          class="easyui-combotree"
          style="width:240px;height: 26px;"/>
      <a href="javascript:$('#check-point-statistics-keyword-sectionCode').combotree('clear')"
         class="textbox-icon icon-close"
         style="width: 26px; height: 26px;position: absolute;right: 28px;">
      </a>
    </div>
    <span style="display:inline-block;">核销点：</span>
    <input class="easyui-combobox"
        id="check-point-statistics-keyword-checkPointId"
        style="width: 175px;height:25px"/>&nbsp;&nbsp;
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="check_point_statistics_search()">查询</a>
  </div>
</div>

<script>
  $(function () {
    function loadSection() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/exhibitSection/makeTreeJson',
        dataType: "json",
        async: false,
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success(data) {
          $('#check-point-statistics-keyword-sectionCode').combotree({
            data,
            valueField: 'sectionCode',
            textField: 'sectionName',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          })
        }
      })
    }
    function loadCheckpoint() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/getPage',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass
        },
        success({rows, state}) {
          if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
          $('#check-point-statistics-keyword-checkPointId').combobox({
            valueField: 'checkPointId',
            textField: 'checkPointName',
            panelHeight: '260px',
            // panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', rows)
        }
      })
    }
    // 加载展馆展区

    setTimeout(() => {
      check_point_statistics_search()
      loadSection()
      loadCheckpoint()
    }, 1e2)
  });

  function check_point_statistics_search(isExport) {
    const param = {
      exhibitCode: exhibitCode_for_pass,
      sectionCode: $('#check-point-statistics-keyword-sectionCode').textbox("getValue"),
      siteType: $('#check-point-statistics-keyword-siteType').combobox("getValue"),
      checkPointId: $('#check-point-statistics-keyword-checkPointId').combobox("getValue"),
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (isExport){
      const target = new URL('/eip-web-sponsor/checkPoint/exportStatistics', location.origin)
      target.search = new URLSearchParams(param).toString()
      return window.open(target, '_blank')
    }
    $('#check-point-statistics-datagrid').datagrid({
      url: eippath + '/checkPoint/statistics',
      queryParams: param,
    })
  }
</script>
