<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<title></title>
<%@ include file="../common/include_base.jsp" %>
<%@ page import="java.util.List" %>
<% 
	boolean  fu_section_graph_set_draw = false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;		
	}
%>
<style type="text/css">
.my-text,
.my-textarea { padding:3px; border:1px #95b8e7 solid; width:260px; }
.my-text { height:14px; line-height:14px; }
.my-checkbox { height:16px; width:16px; }
.my-select { height:22px; width:100px; border:1px #95b8e7 solid;}
.my-toolbar-button,
.my-toolbar-search { padding:5px 5px; }
.all_future_expo ul li{
				width: 285px;
				height: 304px;
				float: left;
				box-sizing: border-box;
				border: solid 1px #eaeaea;
				margin: 0 32px 32px 0;
				overflow: hidden;
				cursor: pointer;
			}
			.all_future_expo ul li:hover{
				border: solid 1px #ff9c9c;			
						}
			.future_expo_top{
				width: 285px;
				height: 171px;
			}
			.future_expo_top img{
				width: 100%;
				height: 100%;
				display: block;
				overflow: hidden;
			}
			.future_expo_button{
				width: 100%;
				height: 124px;
				background-color: #fff;
				box-sizing: border-box;
				padding: 0 16px;
			}
			.future_expo_button p:nth-of-type(1){
				font-size: 16px;
				color: #333333;
				line-height: 24px;
			    margin: 10px 0 10px 0;
				max-height: 48px;
				overflow: hidden;
			
			}
			.future_expo_button p:nth-of-type(1):hover{
				color: #ff5858;
			
			}
			.future_expo_button p:nth-of-type(2),
			.future_expo_button p:nth-of-type(3){
				font-size: 14px;
				color: #666666;
				box-sizing: border-box;
				padding-left: 20px;
				
			}
			.future_expo_button p:nth-of-type(2){
				background: url(../../img/future_expo_time.png) no-repeat left center;
				background-size: 16px;
				margin: 0;
				margin-bottom: 10px;

			}
			.future_expo_button p:nth-of-type(3){
				background: url(../../img/future_expo_addtr.png) no-repeat left center;
				background-size: 16px;
				margin-top: 6px;
			}
			a{
				text-decoration: none;
			}
</style>
</head>
<body class="easyui-layout">
<div data-options="region:'north',border:true" style="height:auto;">
	<div >
		<div class="my-toolbar-search">
        	<label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exihibitName-key" class="easyui-textbox" style="width:10%;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
        	<label>开展时间</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="start-date" class="easyui-datetimebox" style="width:10%;height:25px">-<input id="end-date" class="easyui-datetimebox" style="width:10%;height:25px">
        	&nbsp;&nbsp;&nbsp;&nbsp;
        	<label>展会类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
        		<%-- <select id="exhibit_kind_name"  data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/exhibitKind/getAll' class="easyui-combobox" style="width:100px">
        			<option value="-1">全部</option>
        		</select> --%>
        		<select id="exhibitKindId" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/exhibitKind/getAll'" style="width:10%;height:25px"></select>
        		&nbsp;&nbsp;&nbsp;&nbsp;
        	       	
        	<label id="realease-label" >是否发布</label>&nbsp;&nbsp;&nbsp;&nbsp;
        		<select id="exhibit-realease"   class="easyui-combobox" style="width:7%;;height:25px">
        			<option value="-1"></option>
        			<option value="1">已发布</option>
        			<option value="2">未发布</option>
        		</select>&nbsp;&nbsp;&nbsp;&nbsp;
        	    	
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="_search1()">查询</a>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:110px;height:25px" onclick="_search2()">显示当前展会</a>
        </div>
    </div>
</div>
<div  data-options="region:'center'" class="all_future_expo">
	<ul id="center">
		<li>
			<a href="exhibitor_member_new.html" target="_blank">
			<div class="future_expo_top">
				<img src="../../../img/newpage/index_logo.jpg" >
			</div>
			<div class="future_expo_button">
				<p>2020年第27届巴西圣保罗国际医疗展</p>
				<p>2019.08.08 - 2019.08.11</p>
				<p>浙江 杭州 </p>
			</div>
			</a>
		</li>
	</ul>
</div>
</body>
</html>
<script type="text/javascript">
$(function(){
	var openTime = new Date().setMonth((new Date().getMonth()-1))//当前时间减去一个月
	 $("#start-date").textbox("setValue",new Date(openTime).format("yyyy-MM-dd"));
 	 <% 
	 	if(!fu_section_graph_set_draw){	 		
	 		out.println("$(\"#exhibit-realease+.combo\").hide();document.getElementById(\"realease-label\").style.display = \"none\";"); 			 			 		
	    }
	 %> 
	//out.println("$(\"#exhibit-realease+.combo\").show();document.getElementById(\"realease-label\").style.display = \"block\";");
	_search1();
// 	$("#exihibitName-key").keypress(function (e) {
// 	    if (e.which == 13) {
// 	    	$("#start-date").datetimebox('setValue', '');
// 	    	$("#end-date").datetimebox('setValue',"");
// 	    	$("#exhibitKindId").combobox('setValue',"");
// 	    	$("#exhibit-realease").combobox('setValue',"-1");    	
// 	    	search();
// 	    }
// 	});
});
//将回车键绑定到查询按钮
document.onkeydown = function(eve){
	var e = window.event?window.event:eve; 
	if(e.keyCode == 13){
    	$("#start-date").datetimebox('setValue', '');
    	$("#end-date").datetimebox('setValue',"");
    	$("#exhibitKindId").combobox('setValue',"");
    	$("#exhibit-realease").combobox('setValue',"-1");    	
    	search();
	    //document.getElementById("loginbtn").click();
	}
};
function _search1(){
/* 	var exihibitName=$("#exihibitName-key").val();
	var startDate =$("#start-date").val();
	var endDate =$("#end-date").val();
	var exhibitKind =$("#end-date").val();
	var release =$("#exhibit-realease").val();
	
	search(exihibitName,startDate,endDate,exhibitKind,release); */
	
	search();
}
function _search2(){
	var openTime = new Date().setMonth((new Date().getMonth()-1))//当前时间减去一个月
	$("#exihibitName-key").textbox("setValue", '');
	$("#start-date").datetimebox('setValue',new Date(openTime).format("yyyy-MM-dd hh:mm:ss"));
	$("#end-date").datetimebox('setValue',"");
	$("#exhibitKindId").combobox('setValue',"");
	$("#exhibit-realease").combobox('setValue',"-1");
	
	search();
}
//未发布显示灰色框，已发布显示绿色框，选中时显示红色框
function search(){
	
	var exihibitName=$("#exihibitName-key").val();
	/* var startDate =$("#start-date").val();
	var endDate =$("#end-date").val(); */
	/* var startDate=$("#start-date").datetimebox('getValue');
	var endDate=$("#end-date").datetimebox('getValue'); */
	var startDate=$("#start-date").datetimebox('getText');
	var endDate=$("#end-date").datetimebox('getText');
	var exhibitKindId =$("#exhibitKindId").combobox('getValue');
	var eRelease =$("#exhibit-realease").combobox('getValue');
	
	//alert(startDate+exhibitKindId+"--"+eRelease);
	var center = document.getElementById("center");
	while(center.hasChildNodes()){
		center.removeChild(center.firstChild);
	}
	$.ajax({
		url: eippath + '/exhibition/selectAll',
		dataType: "json",	//返回数据类型
		type: "post",
		/* data: {exihibitName: exihibitName},	//发送数据 */
		data: {exihibitName: exihibitName,startDate:startDate,
					endDate:endDate,exhibitKindId:exhibitKindId,eRelease:eRelease},	//发送数据
		async: true,
		success: function(data){
			//作为参考，进入展会中心
			var html = '';
			for(var i = 0; i < data.length; i++){
				var imgsrc = " ";
				if(data[i].exhibitLogo != null && data[i].exhibitLogo != '')
					imgsrc = "http://" + location.hostname+":"+location.port + '/image/' + data[i].exhibitLogo;
				/* html += "<div style=\"float:left;margin:20px;\">" + 
					"<a href=\"" + eippath + "/backstage/exhibit/section_graph_set?exhibitCode=" + 
					data[i].exhibitCode + "&sectionCode=-1\" target=\"_blank\" style=\"\">" + 
					"<div id=\"div" + i + "\" onmouseover=\"f_over('div" + i + "');\" " +
					"onmouseout=\"f_out('div" + i + "');\" " +
					"style=\"width:210px;height:160px;border:1px solid #D0D0D0;text-align:center;vertical-align:middle;display:table-cell;\">" + 
					"<image src=\"" + imgsrc + "\" style=\"max-width:150px;max-height:150px;\" /></div></a><p style=\"text-align:center;\">" + data[i].exihibitName + "</p></div>"; */					
				if(data[i].release){
					
					//ahref+="<a href='" + eippath + "/backstage/exhibit/index?exhibitCode=" + row.exhibitCode + "&projectId=" 
					//		+ row.projectId + "' style='color:blue;font-weight:bold;' target='_blank'> 信息设置</a>";
					
						
					// 	<div class="future_expo_top">
					// 		<img src="../../../img/newpage/index_logo.jpg" >
					// 	</div>
					// 	<div class="future_expo_button">
					// 		<p>2020年第27届巴西圣保罗国际医疗展</p>
					// 		<p>2019.08.08 - 2019.08.11</p>
					// 		<p>浙江 杭州 </p>
					// 	</div>
					// 	</a>
					// </li>

					html += "<li style=\"border:1px solid #4c72fe;\">" + 
					"<a href=\"" + eippath + "/backstage/exhibit/index?exhibitCode=" + 
					data[i].exhibitCode + "&projectId=" +data[i].projectId+ "\" target=\"_blank\" style=\"\">" + 
					"<div id=\"div" + i + "\" class=\"future_expo_top\" \" >" +
					"<image src=\"" + imgsrc + "\"/></div>" +
					"<div class=\"future_expo_button\"><p id=\"div" + i + "p\" >" + data[i].exihibitName + "</p><p>" +
					data[i].startDate + " - " + data[i].endDate + "</p><p>" + data[i].exhibitPlace + "</p></div></a></li>"
				}else{
					html += "<li>" +
					"<a href=\"" + eippath + "/backstage/exhibit/index?exhibitCode=" + 
					data[i].exhibitCode + "&projectId=" +data[i].projectId+ "\" target=\"_blank\" style=\"\">" + 
					"<div id=\"div" + i + "\" class=\"future_expo_top\" \" >" +
					"<image src=\"" + imgsrc + "\" /></div>" +
					"<div class=\"future_expo_button\"><p id=\"div" + i + "p\" >" + data[i].exihibitName + "</p><p>" +
					data[i].startDate + " - " + data[i].endDate + "</p><p>" + data[i].exhibitPlace + "</p></div></a></li>"
				}
					//alert(data[i].release)
			}
			 $("#center").append(html);
		},
		error: function(){
			$.messager.alert('提示','数据发送失败！','error');
		}
	});
}

Date.prototype.format = function (format) {
           var args = {
               "M+": this.getMonth() + 1,
               "d+": this.getDate(),
               "h+": this.getHours(),
               "m+": this.getMinutes(),
               "s+": this.getSeconds(),
               "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
               "S": this.getMilliseconds()
           };
           if (/(y+)/.test(format))
               format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
           for (var i in args) {
               var n = args[i];
               if (new RegExp("(" + i + ")").test(format))
                   format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
           }
           return format;
    };

</script>