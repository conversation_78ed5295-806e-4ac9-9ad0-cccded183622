<%@ page import="java.util.List" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<!-- <script src="${eippath}/js/jquery.qrcode.min.js" type="text/javascript" charset="utf-8"></script> -->
<script src="${eippath}/js/jquery.qrcode.logo.ex.js" type="text/javascript" charset="utf-8"></script>
<%@ include file="../common/reg_url.jsp" %>
<%
	List<String> funCodes = (List<String>) session.getAttribute("funCodes");
	boolean fu_meeting_certificate_set = funCodes.contains("fu_meeting_certificate_set");
%>
<style type="text/css">
.all_address{
    box-sizing: border-box;
    padding-bottom: 10px;
    margin: 8px 30px 0 30px;
    border-bottom: 1px solid #ccc;
}
.all_address h6{
    font-size: 16px;
    font-weight: normal;
    color: #333333;
    margin-bottom: 8px;
}
.all_address textarea{
	width: 100%;
	border: none;
	resize: none;
	outline: none;
	font-size: 14px;
	color: #808080;
}
.btn-primary{
	width: 84px;
	height: 32px;
	border-radius: 6px;
	font-size: 14px;
	border: solid 1px #4c72fe;
	color: #4c72fe;
	background-color: #fff;
    outline: none;
    cursor: pointer;
}
.all_address>a{
    width: 84px;
    height: 30px;
    background-color: #fff;
    border-radius: 6px;
    border: solid 1px #999999;
    display: inline-block;
    text-align: center;
    line-height: 30px;
    margin-left: 20px;
	color: #666666;
}
.all_address>a:hover{
  color: #666666;
}
.address_download{
    overflow: hidden;
   margin: 12px 30px 15px;
}
.address_download ul li{
	float: left;
	margin-right: 20px;
}

.am-btn{
    font-size: 14px;
    width: 100%;
    color: #333333;
    border: none;
    background-color: #fff;
    height: 26px;
    cursor: pointer;
    outline: none;
}
.prompt_text{
	width: 144px;
	font-size: 16px;
	line-height: 26px;
	color: #333333;
	margin-left: 14px;
	margin-top: 29px;
}
.register-exhibitionReg,.setdiv,.register-releaseReg,.register-address{
	width: 288px !important;
	height: 216px !important;
	border-radius: 10px;
	border: solid 1px #4c72fe;
    transition: transform 0.6s;
}
.register-exhibitionReg :hover img,.setdiv :hover img,.register-releaseReg :hover img,.register-address :hover img{
	transform: scale(1.1);
}
.detialdiv a{
	font-size: 24px;
	color: #4c72fe;
	display: block;
    margin-top: 20px;
}
.detialdiv img{
	 margin: auto;
    margin-top: 42px;
}
#register-address-code  canvas{
  width: 140px;
}
</style>
<style>
	#register-layout-blank{
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
	}
	#register-layout-blank h3{
		color: #666;
		font-size: 18px;
		font-weight: normal;
	}
	#register-layout-blank a{
		line-height: 35px;
		color: #fff;
		display: block;
		width: fit-content;
		padding: 0 15px;
		height: 35px;
		border-radius: 4px;
		background: #1685ed;
		border: none;
	}
</style>
<script>
	var code = "${exhibition.exhibitCode}";
	var orgNum = "${exhibition.f_org_num}";

	$(function(){
		loadProject()
		registerGetRegSyncCheck()
		$('#register-release-projectId').combobox({
			url: '/eip-web-sponsor/project/getAllByExhibitCode',
			queryParams: {exhibitCode: code}
		})
		const $actorCode = $("#register-release-actorCode")
		const $projectId = $("#register-release-projectId")
		$actorCode.combobox({
			onChange(value) {
				spectator_register_load_roles($projectId.combobox('getValue'), value)
			}
		})
		$projectId.combobox({
			onChange(value) {
				spectator_register_load_roles(value, $actorCode.combobox('getValue'))
			}
		})
	})

	function spectator_register_get_current_row(projRegSetId) {
		const {rows} = $('#spectator-register-datagrid').datagrid('getData')
		const result = rows.find(item => item['projRegSetId'] === projRegSetId)
		if (!result) {
			$.messager.alert('错误', '数据异常，请刷新重试', 'error')
			throw Error(`Can not found current row`)
		}
		return result
	}
	function spectator_register_ori2hump(name) {
		if (!name) throw Error('name is required!')
		if (name.startsWith('f_')) name = name.slice(2)
		return name.replace(/_(\w)/g, (_, letter) => letter.toUpperCase())
	}
	function spectator_register_load_roles(projectId, actorCode) {
		$("#register-release-targetType").combobox({
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {projectId, actorCode},
			method: 'POST',
			loadFilter(data) {
				return data.data
			},
			onLoadSuccess() {
				$(this).combobox('setValue', 1)
			}
		})
	}
	function spectator_register_open_special_rules() {
		$.ajax({
			url: '/RegSever/exhibition/getRegProjectSingleTarget',
			data: {exhibitCode: code},
			type: 'post',
			xhrFields: {withCredentials: true},
			success({state, msg, data}) {
				if (state !== 1) {
					return $.messager.alert('提示', msg || '查询特殊规则设置失败', 'error')
				}
				$('#regProjectSingleTarget').prop('checked', !!data)
				$('#register-special-rules-window').window('open')
			},
			error() {
				$.messager.alert('提示', '数据加载失败', 'error')
			}
		})
	}
	function spectator_register_save_special_rules() {
		const regProjectSingleTarget = $('#regProjectSingleTarget').prop('checked')
		$.ajax({
			url: '/RegSever/exhibition/saveRegProjectSingleTarget',
			data: {
				exhibitCode: code,
				regProjectSingleTarget
			},
			type: 'post',
			xhrFields: {withCredentials: true},
			success({state, msg}) {
				if (state !== 1) {
					return $.messager.alert('提示', msg || '保存失败', 'error')
				}
				$.messager.alert('提示', '保存成功!', 'info')
				spectator_register_close_special_rules()
			},
			error() {
				$.messager.alert('提示', '数据加载失败', 'error')
			}
		})
	}
	function spectator_register_close_special_rules() {
		$('#register-special-rules-window').window('close')
	}

	//兼容之前已经上传的图片
	function queryData2image(src) {
		const inner = src => {
			if (!src) return '/RegSever/RegPage/images/top-lucency.png'
			const prefix = location.origin
			if (/(http|https)/.test(src)) return src
			else if (/(^\\+|^\/)/.test(src)) return prefix + src
			return prefix + '/image/' + src
		}
		const url = inner(src)
		if (!url) return ''
		try {
			return new URL(url).host.indexOf('aliyuncs.com') !== -1
					? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
					: url
		} catch (e) {
			return url
		}
	}

	// 打开基本信息弹窗
	function openRegisterSettingWindow(projRegSetId) {
		const isAdd = !projRegSetId
		$('.spectator_register_hide_edit')[isAdd ? 'show' : 'hide']()
		const $settingWindow = $('#register-release-window')
		if (isAdd) {
			$settingWindow.removeData('projRegSetId')
			// cleanup settings window
			registerFullReleaseWindow()
			const defaultVal = '${exhibition.endDate}'
			const regCutoffDate = defaultVal ? defaultVal + ' 17:00:00' : ''
			// 新增的第一个默认上主项目
			const presetsProjectId = !$('#spectator-register-datagrid').datagrid('getData').total ? project_for_pass : ''
			const defaultActorCode = 'Viewer'
			$settingWindow.window({title: '新增观众登记项目'}).window('open')
			$("#register-release-projectId").combobox('setValue', presetsProjectId)
			$("#register-release-regcuttof").textbox('setValue', regCutoffDate || '')
			$("#register-release-actorCode").combobox('setValue', 'Viewer') // 默认为观众
			spectator_register_load_roles(presetsProjectId, defaultActorCode)
			return
		}
		const row = spectator_register_get_current_row(projRegSetId)
		registerFullReleaseWindow(row)
		$settingWindow.window({title: '基本信息设置'}).window('open').data({projRegSetId}).data({cutoffDate: row.regCutoffDate})
  }
	// 填充数据到设置弹窗上，如果没有传参数就表示清除弹窗
	function registerFullReleaseWindow(row) {
		let {
			regCutoffDate,
			projectId = '',
			targetType = '1',
			actorCode = 'Viewer',
			regEnglish = false,
			f_end_time
		} = row || {}
		if (!regCutoffDate) {
			const defaultVal = f_end_time || '${exhibition.endDate}'
			if (defaultVal) regCutoffDate = defaultVal + ' 17:00:00'
			else regCutoffDate = ''
		}
		$("#register-release-projectId").combobox('setValue', projectId)
		$("#register-release-actorCode").combobox('setValue', actorCode)
		setTimeout(()=>$("#register-release-targetType").combobox('setValue', targetType), 100)
		$("#register-release-regcuttof").textbox('setValue', regCutoffDate || '')
		$("#register-release-regEnglish").prop('checked', !!regEnglish)
	}
	// 保存基本信息
	function saveRegisterSetting(){
		const $window = $('#register-release-window')
		const projRegSetId = $window.data('projRegSetId') || ''
		const oldCutoffDate = $window.data('cutoffDate') || ''
		const regCutoffDate = $("#register-release-regcuttof").textbox('getValue')
		let targetType = $("#register-release-targetType").combobox('getValue')
		const projectId = $("#register-release-projectId").combobox('getValue')
		const regEnglish = $("#register-release-regEnglish").prop('checked')
		let updateCertCutOffDate = true
		if (!projRegSetId) targetType = targetType || '1'
		if (!projectId) return $.messager.alert('提示', '请选择项目名称！', 'warning')
		if (!targetType) return $.messager.alert('提示', '请选择角色！', 'warning')
		if (!regCutoffDate) return $.messager.alert('提示', '请选择时间！', 'warning')

		// 保存
		const requester = () => {
			$.ajax({
				url: '/RegSever/projRegSet/saveBuyerRegSet',
				data: {
					projRegSetId,
					regCutoffDate,
					targetType,
					projectId,
					regEnglish,
					updateCertCutOffDate,
					operEntrance:"观众登记设置"
				},
				type: 'post',
				async: false,  //同步，防止重复提交
				xhrFields: {withCredentials: true},
				success({state, msg}) {
					if (state !== 1) {
						const message = state === -1 ? '已存在同项目同身份登记设置!' : (msg|| '保存失败!')
						return $.messager.alert('提示', message, 'error')
					}
					$.messager.alert('提示', '保存成功!', 'info')
					closeRegisterSettingWindow(!0)
				},
				error() {
					$.messager.alert('提示', '数据加载失败', 'error')
				}
			})
		}
		// 查询是否是免费证件
		$.ajax({
			url: '/eip-web-sponsor/certificate/getCertificateA',
			data: {
				targetType,
				projectId,
			},
			type: 'post',
			async: false,  //同步，防止重复提交
			xhrFields: {withCredentials: true},
			success({data}) {
				if (!data || data.isFree || oldCutoffDate === regCutoffDate) return requester()
				$.messager.confirm('提示', '登记截止日期已发生调整，是否要—并修改门票证件可购买日期?', r => {
					if (!r) updateCertCutOffDate = false
					requester()
				})
			},
			error() {
				$.messager.alert('提示', '数据加载失败', 'error')
			}
		})
	}
	// 关闭基本信息弹窗
	function closeRegisterSettingWindow(refresh) {
		refresh && loadProject()
		$('#register-release-window').window('close')
	}

	function regDeadlineBtn(val, row){
		const tableText = row.regCutoffDate ? `截止至\${row.regCutoffDate.replace(/-/g,'/')}` : '设置'
		return `<a href="javascript:void(0)" style="color:blue;font-weight:bold;margin-right: 0.5em;" onclick="openRegisterSettingWindow('\${row.projRegSetId}')">\${tableText}</a>`
	}

	//发布
	function spectator_register_release(projRegSetId){
		const row = spectator_register_get_current_row(projRegSetId)
		const requester = () => {
			$.ajax({
				url: '/RegSever/projRegSet/saveBuyerReleaseReg',
				data: {projRegSetId, releaseReg: !row.releaseReg},
				type:"post",
				async:false,  //同步，防止重复提交
				xhrFields:{withCredentials:true},
				success({state}) {
					if (state === 1) {
						$.messager.alert('提示', '操作成功', 'info')
						loadProject()
						return
					}
					let message = '操作失败'
					switch (state) {
						case -1:
							message = '截止时间未设置'
							break
						case -2:
							message = '短信签名未设置'
							break
					}
					$.messager.alert('提示', message, 'error')
				},
				error() {
					$.messager.alert('提示','数据加载失败','error')
				}
			})
		}
		if (row.releaseReg)
			return $.messager.confirm('提示', '确定撤销发布嘛？', r => r && requester())
		requester()
	}

	//英文发布
	function spectator_register_releaseEn(projRegSetId){
		const row = spectator_register_get_current_row(projRegSetId)
		const requester = () => {
			$.ajax({
				url: '/RegSever/projRegSet/saveBuyerReleaseRegEn',
				data: {projRegSetId, releaseRegEn: !row.releaseRegEn},
				type:"post",
				async:false,  //同步，防止重复提交
				xhrFields:{withCredentials:true},
				success({state}) {
					if (state === 1) {
						$.messager.alert('提示', '操作成功', 'info')
						loadProject()
						return
					}
					let message = '操作失败'
					switch (state) {
						case -1:
							message = '截止时间未设置'
							break
						case -2:
							message = '短信签名未设置'
							break
					}
					$.messager.alert('提示', message, 'error')
				},
				error() {
					$.messager.alert('提示','数据加载失败','error')
				}
			})
		}
		if (row.releaseRegEn)
			return $.messager.confirm('提示', '确定撤销发布嘛？', r => r && requester())
		requester()
	}


	$(".setdiv").click(function (){
	var  linka =  $(this).find('a');
	var url = linka.attr('data-link');
	window.open(url,'_blank');
	})
	$(".register-address").click(function (){
		 open2d(code,orgNum)
	})
	var QRCodeLogo = "";
	function getLogoImgs(f_project_id, f_version){
		$.ajax({
			url:variableReg +"/RegSever/projRegTemp/queryAppointTemp",
			data: {
				f_project_id,f_version,
				f_target: 1,
				appointTemp: 'f_2code_img'
			},
			type: "post",
			async: false,  //同步，防止重复提交
			xhrFields: {
				withCredentials:true
			},
			success: function(data) {
				if(data.state==1){
					if(data.data.f_2code_img!="" && data.data.f_2code_img!=null){
						QRCodeLogo = data.data.f_2code_img
					}
				}
			},
			error: function(data) {
				$.messager.alert('提示','数据加载失败','error');
			}
		});
	}
	function open2d(eid, orgnum, projectId, version){
		getLogoImgs(projectId, version)
		$("#register-address-code").empty();
		const search = '?EID=' + eid + '&target=1&orgnum=' + orgnum + '&pid='+ projectId + '&version=' + version + '&cid=&ctid=1'
		const computer = variableReg + '/web-reg-server/pc/vistor-register.html' + search
		const str = variableReg + '/web-reg-server/mobile/vistor-register-m.html' + search
		$('#register-address-phone').val(str);
		$("#register-address-computer").val(computer);
		$("#register-address-phone-open").attr({"href":str})
		$("#register-address-computer-open").attr({"href":computer})
		$("canvas").css("width","170px")
		$('#register-address-code').qrcode({
			src: queryData2image(QRCodeLogo),
			text: str,
			correctLevel: 0,
			width: '1000', //二维码的宽度
			height: '1000', //二维码的高度
			imgWidth : 200,			 //图片宽
			imgHeight : 200,			 //图片高
		});
		$('#register-address-window').window({title: version === 2 ? '英文登记地址' : '中文登记地址'}).window('open').attr({version})
	}
	function Download(){
		//cavas 保存图片到本地  js 实现
		//1.确定图片的类型  获取到的图片格式 data:image/Png;base64,......
		var type ='png';//你想要什么图片格式 就选什么吧
		var c=  $('#register-address-code' ).find('canvas')[0];
		var imgdata=c.toDataURL("image/png");
		//var imgdata=canvas.toDataURL("image/png");
		//2.0 将mime-type改为image/octet-stream,强制让浏览器下载
		var fixtype=function(type){
			type=type.toLocaleLowerCase().replace(/jpg/i,'jpeg');
			var r=type.match(/png|jpeg|bmp|gif/)[0];
			return 'image/'+r;
		};
		imgdata = imgdata.replace(fixtype(type),'image/octet-stream');
		//3.0 将图片保存到本地
		var savaFile = function(data, filename){
			var save_link=document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
			save_link.href=data;
			save_link.download=filename;
			var event=document.createEvent('MouseEvents');
			event.initMouseEvent('click',true,false,window,0,0,0,0,0,false,false,false,false,0,null);
			save_link.dispatchEvent(event);
		}
		const versionText = $('#register-address-window').attr('version') === '2' ? '英文版' : '中文版'
		savaFile(imgdata, `观众登记\${versionText}二维码.\${type}`)
	}
	function copyText() {
		var Url2 = document.getElementById("register-address-phone");
		Url2.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
	}
	function copySingleText() {
		var Url2 = document.getElementById("register-address-phoneSingle");
		Url2.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
	}
	function copyComputer() {
		var Url2 = document.getElementById("register-address-computer");
		Url2.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
	}
	function toUtf8(str) {
        var out, i, len, c;
        out = "";
        len = str.length;
        for (i = 0; i < len; i++) {
            c = str.charCodeAt(i);
            if ((c >= 0x0001) && (c <= 0x007F)) {
                out += str.charAt(i);
            } else if (c > 0x07FF) {
                out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
                out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
            } else {
                out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
            }
        }
        return out;
    }
	function loadProject(noPopup) {
		$('#spectator-register-datagrid').datagrid({
			url: '/RegSever/projRegSet/getBuyerRegSetList',
			queryParams: {exhibitCode: code},
			loadFilter(data) {
				const list = data.data
				return {
					rows: list,
					total: list.length
				}
			},
			onLoadSuccess({total}) {
				$('#register-layout-table>.datagrid')[total ? "show" : "hide"]()
				$('#register-layout-blank')[!total ? "show" : "hide"]()
				!total && !noPopup && openRegisterSettingWindow()
			}
		})
	}

  function regSetBtn(val, row) {
    const targetType = row.targetType
    return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegSet(' + row.projectId + ', 1, ' + targetType + ')">中文版设置</a>' +
      (row.regEnglish ? '<i style="margin-right: 20px"/><a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegSet(' + row.projectId + ', 2, ' + targetType + ')">英文版设置</a>' : '')
  }
	function regCertSetBtn(_, row){
		const text = row['enable_certificate'] === 'true' ? '已启用': '设置'
		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegCertSet(' + row.projectId + ',' + row.targetType + ')">' + text + '</a>'
	}
	function regReleaseBtn(val, row){
		return `<a href="javascript:void(0);"
							 style="color:blue;font-weight:bold;"
							 onclick="spectator_register_release('\${row.projRegSetId}')">\${val ? '撤销' : '发布'}</a>`
	}
	function regReleaseEnBtn(val, row) {
		if (!row.regEnglish) return '';
		return `<a href="javascript:void(0);"
							 style="color:blue;font-weight:bold;"
							 onclick="spectator_register_releaseEn('\${row.projRegSetId}')">\${val ? '撤销' : '发布'}</a>`
	}
  function regAddressBtn(val, row){
		const {projectId, targetType, projectName, targetName} = row
    const htmlList = [`<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegChannelLink(\${[projectId, targetType].toString()}, '\${projectName}')">查看</a>`]
    // if (row.regEnglish) htmlList.push(`<i style="margin-right: 20px"/><a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegChannelLink(\${[projectId, targetType, title].toString()}, 2)">英文版地址</a>`)
    return htmlList.join('')
  }
	function regSynDataBtn(val, row){
		return `<a href="javascript:void(0);"
				   style="color:blue;font-weight:bold;"
				   onclick="openRegSynData('\${row.projRegSetId}')">设置</a>`
	}
  function openRegChannelLink(projectId, targetType, projectName) {
		const isExist = addTab('观众邀请渠道', `/eip-web-sponsor/backstage/project/channel_details?projectName=\${projectName}&targetType=\${targetType}`) === '__EXISTS__'
    if (isExist && typeof channel_details_setQuery === 'function') {
			channel_details_setQuery({targetType, projectName}, true)
		}
  }
  function openRegSet(projectId, version, targetType) {
    const target = new URL(location.origin)
    target.pathname = '/web-reg-server/pc/register-settings.html'
    target.searchParams.set('pid', projectId)
    target.searchParams.set('EID', code)
    target.searchParams.set('orgnum', orgNum)
    target.searchParams.set('version', version)
    target.searchParams.set('target', targetType)
    window.open(target.href, '_blank');
  }
	function openRegCertSet(projectId, targetType){
		const target = new URL('/web-reg-server/pc/cert-settings.html', location.origin)
		target.searchParams.set('pid', projectId)
		target.searchParams.set('EID', code)
		target.searchParams.set('orgnum', orgNum)
		target.searchParams.set('version', '1')
		target.searchParams.set('target', targetType)
		target.searchParams.set('popup', 'true')
		window.open(target.toString(), '_blank')
	}

	//保存同步数据
	function regSynData() {
		$.messager.progress()
		const data = {};
		'f_from_buyer f_from_client f_from_invite f_syn_data notSynTrade f_syn_team f_syn_group'.split(' ').forEach(key => {
			const $current = $('#register-project-' + key)
			const dataKey = spectator_register_ori2hump(key)
			if (key === 'f_syn_data') {
				data[dataKey] = Number($current.prop('checked'))
			}else if (key === 'notSynTrade') {
				data[dataKey] = $current.prop('checked')
			}else if (key === 'f_syn_group') {
				data[dataKey] = $current.combotree('getValue')
			} else {
				data[dataKey] = $current.combobox('getValue')
			}
		})
		data.projRegSetId = $('#register-open-team-window').data('projRegSetId')
		if (!data['synData']) {
			delete data['synTeam']
			delete data['synGroup']
		}
		$.ajax({
			url: '/RegSever/projRegSet/saveBuyerSync',
			data,
			type: 'post',
			dataType: 'json',
			success(data) {
				if (data.state !== 1) return $.messager.alert('提示', '保存失败！', 'error')
				$.messager.alert('提示', '保存成功！', 'info')
				closeRegSynData()
				loadProject()
			},
			error(err) {
				console.log(err)
				$.messager.alert('提示', '保存失败！', 'error')
			}
		}).done(() => $.messager.progress('close'))
	}

	function regLoadGroupTree(workTeamId) {
		const $group = $('#register-project-f_syn_group')
		if (!+workTeamId) workTeamId = -1
		$group.combotree({
			url: '/RegSever/teamCltGroup/getGroupByTeamId',
			method: "post",
			queryParams: {workTeamId},
			loadFilter(data) {
				if (data.state === 1)
					return [{id: '', text: ''}, ...data.data]
				return []
			},
		})
	}

	//打开同步数据设置弹框
	function openRegSynData(projRegSetId) {
		const row = spectator_register_get_current_row(projRegSetId)
		$('#register-open-team-window').window('open').data({projRegSetId})
		const projectId = row.projectId
		$("#register-project-f_syn_team").combobox({
			url: eippath + "/project/getWorkTeamByProjectId", //获取数据
			method: "post",
			queryParams: {projectId},
			loadFilter(data) {
				return [{id: '', text: '公司数据总览'}, ...data]
			},
			onLoadSuccess(workTeamData) {
				if (workTeamData.length > 0 && row['synTeam']) {
					$(this).combobox('setValue', row['synTeam'])
					$('#register-project-f_syn_group').combotree('setValue', row['synGroup'] || '')
				}
			},
			onChange(workTeamId) {
				regLoadGroupTree(workTeamId)
			}
		})
		regLoadGroupTree()
		$('#register-project-f_syn_data')
				.change(function () {
					$('#register-project-syn_data_show')[$(this).prop('checked') ? 'show' : 'hide']()
				})
				.prop('checked', Boolean(row['synData']))
				.change();
		$('#register-project-notSynTrade').prop('checked', row['notSynTrade']);
		'f_from_buyer,f_from_client,f_from_invite'.split(',').forEach(key => {
			$('#register-project-' + key).combobox('setValue', row[spectator_register_ori2hump(key)] ? '1' : '0')
		})
	}

	//关闭同步数据设置弹框
	function closeRegSynData() {
		'f_from_buyer f_from_client f_from_invite f_syn_data notSynTrade f_syn_team f_syn_group'.split(' ').forEach(key => {
			const $current = $('#register-project-' + key)
			if (key === 'f_syn_data' || key === 'notSynTrade') {
				$current.prop('checked', false)
			} else if (key === 'f_syn_group') {
				$current.combotree('clear')
			} else {
				$current.combobox('clear')
			}
		})
		$('#register-project-f_syn_team').combobox('loadData', [])
		$('#register-project-f_syn_group').combotree('loadData', [])
		$('#register-open-team-window').removeAttr('projectId').window('close')
	}

	function registerGetRegSyncCheck() {
		$.ajax({
			url: '/RegSever/register/getRegSyncCheck',
			type: 'post',
			dataType: 'json',
			data: {orgNum: org_num_for_pass},
			success(data) {
				if (data.data === '1') {
					$('.hideBySyncCheck').hide()
				}
			},
		})
	}
	// 删除登记设置
	function registerDeleteRegSet() {
		const rows = $('#spectator-register-datagrid').datagrid('getSelections')
		if (!rows.length) return $.messager.alert('提示','未选中内容！','warning')
		const projRegSetIds = rows.map(item => item.projRegSetId).toString()
		const _request = () => {
			$.messager.progress()
			$.ajax({
				url: '/RegSever/projRegSet/delete',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {projRegSetIds,operEntrance:"观众登记设置"},	//发送数据
				success({state}){
					if (state !== 1) return $.messager.alert('提示', '删除失败！', 'error')
					$.messager.alert('提示', '删除成功！', 'info')
					loadProject(!0)
				},
				error() {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			}).done(() => $.messager.progress('close'))
		}
		$.messager.confirm('提示',  '确定删除吗？', r => r && _request())
	}
</script>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'north',split:true" style="height:100px;padding: 0 20px">
		<div style="width: fit-content;height: 50px;display: flex;align-items: center;justify-content: space-between;">
			<span style="display:inline-block;font-size: 20px" id="register-info-exihibitName" >${exhibition.exihibitName}</span>
			<a style="margin-left: 10px" href="javascript:spectator_register_open_special_rules()" class="easyui-linkbutton" iconCls="icon-db" plain="true">特殊规则设置</a>
			<a style="margin-left: 10px" href="/eip-web-sponsor/document/观众登记展前检查.doc" download="观众登记展前检查.doc" class="easyui-linkbutton" iconCls="icon-before-check" plain="true">观众登记展前检查</a>
		</div>
		<div style="width: 100%;height:30px;margin-top:10px;">
			<span style="display:inline-block;">开幕时间：</span>
			<span style="display:inline-block;width: 150px;" id="register-info-startDate">${exhibition.startDate}</span>
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="display:inline-block;">闭幕时间：</span>
			<span style="display:inline-block;width: 150px;" id="register-info-endDate">${exhibition.endDate}</span>
		</div>
	</div>
	<div id="register-layout-table" data-options="region:'center'" title="" style="width:100%;height:100%;">
		<div id="spectator-register-toolbar">
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="loadProject()"
					 plain="true">刷新</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="openRegisterSettingWindow()"
					 plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="registerDeleteRegSet()"
					 plain="true">删除</a>
			</div>
		</div>
		<table
			id="spectator-register-datagrid"
			class="easyui-datagrid"
			toolbar="#spectator-register-toolbar"
			data-options="rownumbers:true,
				 singleSelect:false,
				 pageSize:20,
				 pagination:false,
				 multiSort:true,
				 fitColumns:true,
				 fit:true,method:'post',
				 selectOnCheck:true,
				 onClickRow(rowIndex, rowData) {
					var l = $(this).datagrid('getRows').length;
					for(var i = 0; i < l; i++){
						if(i != rowIndex)$(this).datagrid('unselectRow', i);
					}
				}">
			<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'projectName',width:80,align:'center'">项目名称</th>
				<th data-options="field:'actorName',width:80,align:'center'">参与类别</th>
				<th data-options="field:'targetName',width:80,align:'center'">身份</th>
				<th data-options="field:'isMain',width:80,align:'center'" formatter="v => v ? '是': '否'">是否主项目</th>
				<th data-options="field:'regCutoffDate',width:130,align:'center'" formatter="regDeadlineBtn">基本信息设置</th>
				<th data-options="field:'1',width:180,align:'center'" formatter="regSetBtn">登记设置</th>
				<th data-options="field:'3',width:180,align:'center'" formatter="regCertSetBtn">票证设置</th>
				<th data-options="field:'releaseReg',width:90,align:'center'" formatter="regReleaseBtn">中文版发布</th>
				<th data-options="field:'releaseRegEn',width:90,align:'center'" formatter="regReleaseEnBtn">英文版发布</th>
				<th data-options="field:'2',width:180,align:'center'" formatter="regAddressBtn">登记地址</th>
				<th data-options="field:'synData',width:80,align:'center'" formatter="regSynDataBtn">数据同步</th>
			</tr>
			</thead>
		</table>
		<div id="register-layout-blank" style="width:100%;height:100%;">
			<img src="/eip-web-crm/pages/crm/img/project-placeholder.png" alt=""/>
			<h3>请点击新增观众登记项目，设置观众登记以获取观众信息</h3>
			<a href="javascript:openRegisterSettingWindow()">新增观众登记项目</a>
		</div>
	</div>
</div>
<div id="register-address-window" class="easyui-window" toolbar="#register-address-window-toolbar" data-options="closed:true,title:'登记地址',modal:true,maximizable: false,resizable: false,minimizable: false,shadow:false,top:80" style="width:600px;">
    <div id="register-address-panel-1" class="easyui-panel" title="">
			<div class="all_address">
			 	<h6>手机登记地址</h6>
			 	<textarea cols="50" rows="3" id="register-address-phone" readonly="readonly" ></textarea>
			 	<button class="btn-primary" onclick="copyText()">复制链接</button>
			 	<a id="register-address-phone-open" href="" target="_blank"  >直接打开</a>
			</div>
			<!-- <div class="all_address">
			 	<h6>手机登记地址（单层）</h6>
			 	<textarea cols="50" rows="3" id="register-address-phoneSingle" readonly="readonly" ></textarea>
			 	<button class="btn-primary" onclick="copySingleText()">复制链接</button>
			 	<a id="register-address-phoneSingle-open" href="" target="_blank" >直接打开</a>
			</div> -->
			<div class="all_address">
			 	<h6>电脑登记地址</h6>
			 	<textarea cols="50" rows="3" id="register-address-computer" readonly="readonly"></textarea>
			 	<button class="btn-primary" onclick="copyComputer()" >复制链接</button>
			 	<a id="register-address-computer-open" href="" target="_blank"  >直接打开</a>
			</div>
			<div class="address_download">
				<ul>
					<li>
						<div id="register-address-code" class="qr_code"></div>
						<div class="address_download_button">
							<a id="register-address-download" download="qrcode.jpg"></a>
							 <button id="register-address-saveQrCode"  class="am-btn" onclick="Download()">下载</button>
						</div>
					</li>
					<!-- <li>
						<div id="register-address-code2" class="qr_code"></div>
						<div class="address_download_button">
							<a id="register-address-download" download="qrcode.jpg"></a>
							<button id="register-address-saveQrCode"  class="am-btn" onclick="DownloadSingle()">下载（单层）</button>
						</div>
					</li> -->
					<li>
						<div class="prompt_text">
							<p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
						</div>
					</li>
				</ul>
			</div>
	</div>
 </div>
<style>
	.tips-icon{
		display: inline-block;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		color: rgb(63,164,255);
		background-color: rgb(230,247,255);
		font-style: normal;
		text-align: center;
		line-height: 16px;
		font-size: 12px;
		font-weight: bold;
		margin-right: 5px;
	}
</style>
<!--设置基本信息-->
<div id="register-release-window"
		 class="easyui-window"
		 data-options="closed:true,title:'基本信息设置',shadow:false,modal:true,maximizable: false,resizable: false,minimizable: false"
		 style="width:480px;">
	<div id="register-release-window-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);"
				 class="easyui-linkbutton"
				 iconCls="icon-save"
				 onclick="saveRegisterSetting()"
				 plain="true"
				 style="margin-right: 6px;">保存</a>
			<a href="javascript:void(0);"
				 class="easyui-linkbutton"
				 iconCls="icon-cancel"
				 onclick="closeRegisterSettingWindow()"
				 plain="true"
				 style="margin-right: 6px;">取消</a>
		</div>
	</div>
	<div class="easyui-panel" style="display: flex;justify-content: center;flex-direction: column;padding: 10px">
		<div style="margin-top: 10px;display:none" class="spectator_register_hide_edit">
			<label style="width:120px;display: inline-block;text-align: right;">
				<font style="color: red;">*</font>项目名称
			</label>
			<input
				class="easyui-combobox"
				style="width:200px;height:25px"
				id="register-release-projectId"
				data-options="valueField:'projectId',textField:'projectName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>
		</div>
		<div style="margin-top: 10px">
			<label style="width:120px;display: inline-block;text-align: right;">
        <font style="color: red;">*</font>登记截止时间
      </label>
			<input id="register-release-regcuttof" class="easyui-datetimebox" data-options="editable:false" style="width:200px;height:25px;">
      <span style="display: inline-block;width: 30px;"></span>
		</div>
		<div style="margin-top: 10px;display:none" class="spectator_register_hide_edit">
			<label style="width:120px;display: inline-block;text-align: right;">
				<font style="color: red;">*</font>参与类别
			</label>
			<input
				class="easyui-combobox"
				style="width:200px;height:25px"
				id="register-release-actorCode"
				data-options="
					valueField:'actorCode',
					textField:'actorName',
					panelHeight:'auto',
					panelMaxHeight:'200px',
					limitToList:true,
					data: [{actorCode: 'Viewer', actorName: '观众'}]
				"/>
		</div>
		<div style="margin-top: 10px;display:none" class="spectator_register_hide_edit">
			<label style="width:120px;display: inline-block;text-align: right;">
				<font style="color: red;">*</font>身份
			</label>
			<input
				class="easyui-combobox"
				style="width:200px;height:25px"
				id="register-release-targetType"
				data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>
		</div>
		<div style="margin-top: 10px">
			<label>
				<span style="width:120px;display: inline-block;text-align: right">启用英文登记</span>
				<input type="checkbox" id="register-release-regEnglish">
			</label>
		</div>

		<%--<c:if test="<%=false%>">
			<div style="margin-top: 10px">
				<label>
					<span style="width:120px;display: inline-block;text-align: right">启用登记付费</span>
					<input type="checkbox" id="register-release-regPay">
				</label>
			</div>
			<div style="margin-top: 10px;display:none">
				<label style="width:120px;display: inline-block;text-align: right;">
					<font style="color: red;">*</font>付款金额
				</label>
				<input
						id="register-release-regPrice"
						class="easyui-numberbox"
						style="width:200px;height:25px;" data-options="precision:2">
				<span style="display: inline-block;width: 30px;">元</span>
			</div>
			<div style="margin-top: 10px;display:none">
				<label>
					<span style="width:120px;display: inline-block;text-align: right">启用免费邀请码</span>
					<input type="checkbox" id="register-release-enableInviteCode">
				</label>
			</div>
			<div style="margin-top: 10px;display:none">
				<label>
					<span style="width:120px;display: inline-block;text-align: right">允许用户退款</span>
					<input type="checkbox" id="register-release-regRefund">
				</label>
			</div>
			<div style="margin-top: 10px;display:none">
				<label>
					<label style="width:120px;display: inline-block;text-align: right;">
						<font style="color: red;">*</font>退款时间段
					</label>
					<input type="checkbox" id="register-release-regRefundBefore">
					开展前可退
					<span style="float: right;padding-right: 45px;">
					<i class="tips-icon">i</i><span id="register-release-regRefundBefore-text"></span>
				</span>
				</label>
			</div>
			<div style="margin-top: 10px;display:none">
				<label>
					<span style="width:120px;display: inline-block;text-align: right"></span>
					<input type="checkbox" id="register-release-regRefundAfter">
					展会结束后可退
					<span style="float: right;padding-right: 45px;">
					<i class="tips-icon">i</i><span id="register-release-regRefundAfter-text"></span>
				</span>
				</label>
			</div>
		</c:if>--%>
	</div>
</div>
<!--数据同步-->
<div id="register-open-team-window" class="easyui-window" title="数据同步" data-options="modal:true,closed:true,shadow:false" style="width:800px;">
	<div id="register-open-team-window-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);"
				 class="easyui-linkbutton"
				 iconCls="icon-save"
				 onclick="regSynData()"
				 plain="true"
				 style="margin-right: 6px;">保存</a>
			<a href="javascript:void(0);"
				 class="easyui-linkbutton"
				 iconCls="icon-cancel"
				 onclick="closeRegSynData()"
				 plain="true"
				 style="margin-right: 6px;">取消</a>
		</div>
	</div>
	<div id="register-open-team-panel" class="easyui-panel" style="width: 100%;">
		<h3 class="block-title" style="border-top:none;">登记引用后台数据</h3>
		<ul class="setting-list">
			<li>
				<div style="display: flex;">
					<span class="item-label">本届邀请登记信息</span>
					<div style="margin-left: 20px;">
						<span class="form-label">启用</span>
						<select id="register-project-f_from_invite"
									 class="easyui-combobox"
									 data-options="
									 width: 180,
									 panelHeight:'auto',
									 panelMaxHeight:'200px',
									 limitToList:true">
							<option value="1">启用</option>
							<option value="0">不启用</option>
						</select>
						<p class="tips">观众验证手机号，获取CRM展会项目的邀请参观记录信息并自动填充，可修改</p>
					</div>
				</div>
			</li>
			<li>
				<div style="display: flex;">
					<span class="item-label">观众历史登记信息</span>
					<div style="margin-left: 20px;">
						<span class="form-label">启用</span>
						<select id="register-project-f_from_buyer"
									 class="easyui-combobox"
									 data-options="
									 width: 180,
									 panelHeight:'auto',
									 panelMaxHeight:'200px',
									 limitToList:true">
							<option value="1">启用</option>
							<option value="0">不启用</option>
						</select>
						<p class="tips">观众验证手机号，自动填充观众历史登记信息，可修改</p>
					</div>
				</div>
			</li>
			<li>
				<div style="display: flex;">
					<span class="item-label">观众CRM信息</span>
					<div style="margin-left: 20px;">
						<span class="form-label">启用</span>
						<select id="register-project-f_from_client"
									 class="easyui-combobox"
									 data-options="
									 width: 180,
									 panelHeight:'auto',
									 panelMaxHeight:'200px',
									 limitToList:true">
							<option value="1">启用</option>
							<option value="0">不启用</option>
						</select>
						<p class="tips">观众验证手机号，在没有历史登记信息时，获取CRM观众的信息并自动填充，可修改</p>
					</div>
				</div>
			</li>
		</ul>
		<h3 class="block-title hideBySyncCheck">数据同步回CRM</h3>
		<ul class="setting-list hideBySyncCheck">
			<li style="display:flex;">
				<label style="display: flex;margin-left: 102px;margin-bottom: 20px;">
					<input class="my-checkbox" type="checkbox" id="register-project-f_syn_data" value="0">
					<span style="color: #333;margin-left: 13px;line-height: 22px;">数据同步到公司客户、联系人</span>
				</label>
				<label style="display: flex;margin-left: 160px;margin-bottom: 20px;">
					<input class="my-checkbox" type="checkbox" id="register-project-notSynTrade" value="0">
					<span style="color: #333;margin-left: 13px;line-height: 22px;">不同步行业字段</span>
				</label>
			</li>
			<li id="register-project-syn_data_show" style="display:none;">
				<div style="display: flex;">
					<div style="margin-left: 47px; width: 100%;" class="team-group">
						<input id="register-project-f_syn_team"
										style="width: 225px;"
										class="easyui-combobox"
										data-options="
										 valueField: 'id',
										 textField: 'text',
										 width: 225,
										 label: '选择团队',
										 panelHeight:'auto',
										 panelMaxHeight:'200px',
										 limitToList:true"/>
						<input id="register-project-f_syn_group"
										class="easyui-combotree"
										style="width: 225px;"
										data-options="
										 width: 225,
										 label: '选择分组',
										 panelHeight:'auto',
										 panelMaxHeight:'200px',
										 limitToList:true"/>
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>
<style>
	#register-release-window-toolbar .my-toolbar-button,
	#register-open-team-window-toolbar .my-toolbar-button{
		display: flex;
		align-items: center;
		justify-content: flex-end;
		height: 36px;
		background-color: #f5f5f5;
	}
	#register-open-team-panel .block-title{
		margin: 0;
		font-size: 14px;
		height: 30px;
		line-height: 30px;
		border: 1px solid #ccc;
		border-left: none;
		border-right: none;
		padding-left: 1em;
	}
	#register-open-team-panel .setting-list{
		list-style: none;
		margin: 0;
		padding: 20px;
	}
	#register-open-team-panel .tips{
		color: #999;
	}
	#register-open-team-panel .item-label{
		height: 30px;
		line-height: 30px;
		width: 120px;
		text-align: right;
	}
	#register-open-team-panel .form-label{
		color: #333;
		margin-right: 12px;
		line-height: 30px;
		height: 30px;
		display: inline-block;
		vertical-align: middle;
  }
	#register-open-team-panel .team-group label{
		text-align: right!important;
		height: 30px!important;
		padding-right: 11px!important;
		line-height: 30px!important;
	}
	#register-open-team-panel .team-group{
		padding-bottom: 30px;
	}
	#register-open-team-panel .team-group span.combo{
		width: 225px!important;
	}
	.panel.combo-p.panel-htop .combobox-item{
		height: 20px;
	}
	.panel.combo-p.panel-htop .tree>li:first-child .tree-node .tree-icon.tree-file{
		background: none;
	}
</style>

<div id="register-special-rules-window"
		 class="easyui-window"
		 data-options="closed:true,title:'特殊规则设置',shadow:false,modal:true,maximizable: false,resizable: false,minimizable: false"
		 style="width:480px;">
	<div class="my-toolbar-button" style="text-align: right">
		<a href="javascript:void(0);"
			 class="easyui-linkbutton"
			 iconCls="icon-save"
			 onclick="spectator_register_save_special_rules()"
			 plain="true"
			 style="margin-right: 6px;">保存</a>
		<a href="javascript:void(0);"
			 class="easyui-linkbutton"
			 iconCls="icon-cancel"
			 onclick="spectator_register_close_special_rules()"
			 plain="true">取消</a>
	</div>
	<div class="easyui-panel" style="padding: 20px">
		<label style="display: inline-flex;align-items: center">
			不允许同项目多身份登记
			<input style="margin-left: 15px;width: 1rem;height: 1rem;" type="checkbox" id="regProjectSingleTarget"/>
		</label>
	</div>
</div>
