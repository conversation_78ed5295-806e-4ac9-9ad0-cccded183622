<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style type="text/css">
	.posf{
		position: fixed;
		width: inherit;
		line-height: 34px;
		background-color: #fff;
		z-index: 3;
		box-sizing: border-box;
		border-bottom: 1px solid #ccc;
	}
	#travel-set-trip-window>div:nth-of-type(2){
		margin-top: 40px;
	}
	.window{
		position: fixed !important;
	}
	.hide {
		display: none !important;
	}
	.js-disable {
		pointer-events: none;
	}
</style>
<script>
var fu_visa_data = top.checkFunCode('fu_visa_data');
</script>
<div  class="easyui-tabs" style="width:100%;height:100%;padding:5px;" data-options="tabPosition:'top'"><!-- bottom -->
    <div title="行程设置" style="width:100%;height:100%;padding:5px;">
		   <div id="travel-set-trip-toolbar">
	            <div class="my-toolbar-button">
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="travel_set_trip_fresh()" plain="true">刷新</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_trip_add()" plain="true">新增</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght event1" iconCls="icon-edit" onclick="travel_set_trip_mod()" plain="true">修改</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_trip_del_batch()" plain="true">删除</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton event2 row-lenght" iconCls="icon-view" onclick="travel_set_trip_view()"  plain="true">查看</a>
								<!-- <a href="javascript:travel_set_rebuildFee();" class="easyui-linkbutton" iconCls="icon-view" plain="true">批量生产项目定价</a> -->
								<script>
									function travel_set_rebuildFee() {
										// travelSet.alert('TODO !', 'info');
										const rows = $('#travel-set-trip-datagrid').datagrid('getSelections')
										const len  = rows.length;
										$.messager.confirm('提示', len<1 ? '确定全部生成吗？' : '确定生成吗?', function(r){
											if (r){
												travelSet.post('/projTrip/batchGenerateTripPrice',{
													projTripIds: rows.map(i => i.projTripId).join(','),
													projectId: project_for_pass,
													// operEntrance:'展位类型设置'
												},{
												},function(res){
													travelSet.checkReturn(res);
													travel_set_trip_fresh();
													travel_set_init_priceLink();
													if(res.data.failNum > 0) {
														travelSet.alert('<div>'
															+ '操作成功 !<br > 成功: '+ res.data.successNum
															+ '<br > 失败: '+ res.data.failNum
															+'</div>','info');
													} else {
														travelSet.alert(len == 1 ? '操作成功!' : '操作成功, 成功: '+ res.data.successNum +' !', 'info');
													}
												},function(){
													travelSet.alert('操作失败 !')
												})
											}
										});
									}
								</script>
	            </div>
	        </div>
  			<table id="travel-set-trip-datagrid" class="easyui-datagrid" toolbar="#travel-set-trip-toolbar"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,remoteSort:true,
	        			multiSort:false,fitColumns:true,fit:true,method:'post',selectOnCheck:true,nowrap:false,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {
	        			travel_set_trip_view_db(rowData);
	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'projTripId',width:30,sortable:true">行程ID</th>
						<th data-options="field:'tripName',width:30,sortable:true">行程名称</th>
						<th data-options="field:'beginPlace',width:30">出发地</th>
						<th data-options="field:'endPlace',width:30">目的地</th>
						<!-- <th data-options="field:'isDefault',width:30" formatter="travel_set_trip_format_IsDefault">默认行程</th> -->
						<th data-options="field:'difPrice',width:30">人员价格</th>
						<th data-options="field:'difPriceRoom',width:30">单房差</th>
						<th data-options="field:'daysNum',width:30">共几晚</th>
						<th data-options="field:'tripDetail',width:60">行程描述</th>
						<th data-options="field:'airTicket',width:60,">机票详情</th>
					</tr>
				</thead>
			</table>
    </div>
    <div title="地接设置"  style="width:100%;height:100%;padding:5px;">
		    <div id="travel-set-reception-toolbar">
	            <div class="my-toolbar-button">
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="travel_set_reception_fresh()" plain="true">刷新</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_reception_add()" plain="true">新增</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="travel_set_reception_mod()" plain="true">修改</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_reception_del_batch()" plain="true">删除</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="travel_set_reception_view()" plain="true">查看</a>
	            </div>
	        </div>
  			<table id="travel-set-reception-datagrid" class="easyui-datagrid" toolbar="#travel-set-reception-toolbar"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {
	        			travel_set_reception_view_db(rowData);
	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'localReceptionId',width:30">地接ID</th>
						<th data-options="field:'reception',width:40">地接名称</th>
						<th data-options="field:'hotel',width:40">酒店名称</th>
						<th data-options="field:'startDate',width:40">开始时间</th>
						<th data-options="field:'endDate',width:40">结束时间</th>
					</tr>
				</thead>
			</table>
    </div>
    <div title="签证材料设置"  style="width:100%;height:100%;padding:5px;">
		    <div id="travel-set-visa-data-toolbar">
	            <div class="my-toolbar-button">
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="travel_set_visa_data_fresh()" plain="true">刷新</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_visa_data_add()" plain="true">新增</a>
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="travel_set_visa_data_update_batch()" plain="true">保存</a>
	            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="travel_set_visa_data_mod()" plain="true">修改</a> -->
	            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_visa_data_del_batch()" plain="true">删除</a>
	            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="travel_set_visa_data_view()" plain="true">查看</a> -->
								<a href="javascript:void(0);" class="fu_visa_data easyui-linkbutton hide" iconCls="icon-db" onclick="top.goOrgMenu('visa_data')" plain="true">签证材料设置</a>
	            </div>
	        </div>
  			<table id="travel-set-visa-data-datagrid" class="easyui-datagrid" toolbar="#travel-set-visa-data-toolbar"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex){
	               				$(this).datagrid('unselectRow', i)
	               				$(this).datagrid('unselectRow', i).datagrid('endEdit', i);
	               			};
	               		}
	               		$(this).datagrid('beginEdit', rowIndex);
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {
	        			//travel_set_visa_data_view_db(rowData);
	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						 <th data-options="field:'visaDataId',width:30">材料ID</th>
						<th data-options="field:'dataNumber',width:40">材料编号</th>
						<th data-options="field:'dataName',width:40">材料名称</th>
						<th data-options="field:'amount',width:40,editor:'numberbox'" >材料数量</th>
					</tr>
				</thead>
			</table>
    </div>

</div>

<style>
	#travel-set-trip-panel-1 li>div>label {
		padding-right: 2px;
	}
</style>
<!-- begin of easyui-window -->
<div id="travel-set-trip-window" class="easyui-window" toolbar="#travel-set-trip-window-toolbar" data-options="closed:true,title:'行程内容',modal:true" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="travel-set-trip-window-toolbar" class="posf">
	    <div class="my-toolbar-button">
	    	<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_trip_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" hint="mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="travel_set_trip_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_trip_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="travel_set_trip_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="travel_set_trip_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="travel-set-trip-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0 0 0 20px;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label>行程名称</label></div>
				<input hint="tripName" class="easyui-textbox" control="textbox" tag="0" style="width:164px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:126px;height:25px;display:inline-block;text-align: right;"><label>出发地</label></div>
				<input hint="beginPlace" class="easyui-textbox" control="textbox" tag="0" style="width:164px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label>目的地</label></div>
				<input hint="endPlace" class="easyui-textbox" control="textbox" tag="0" style="width:164px;height:25px">
			</li>
			<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>默认行程</label></div>
				<input hint="isDefault" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
					id = "travel-set-trip-isDefault"
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[
        			{id:'true',text:'是'},{id:'false',text:'否'}],limitToList:'true' ">
			</li> -->
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:126px;height:25px;display:inline-block;text-align: right;"><label>人员价格<!--<span id="travel-set-trip-person-currency"></span>--></label></div>
				<input hint="difPrice" class="easyui-numberbox" control="numberbox" tag="0"
				  style="width:80px;height:25px" data-options="min:0,precision:2">
				<input hint="personTripCurrency"  id="travel-set-trip-personTripCurrency"
				  class="easyui-combobox" control="combobox" tag="0" style="width:80px;height:25px"
					data-options="valueField:'id',textField:'text',panelHeight:'auto'">
			</li>
			<li style="width:315px;height:25px;float:left;margin-bottom:5px;display: flex;">
				<div style="width:104px;height:25px;display:inline-block;text-align: right;"><label>单房差<!--（<span id="travel-set-trip-room-currency"></span>/每晚）-->&nbsp;</label></div>
				<div style="width: 209px;overflow: hidden;">
					<div style="width: 300px;">
						<input hint="difPriceRoom" class="easyui-numberbox" control="numberbox" tag="0"
							style="width:80px;height:25px" data-options="min:0,precision:2">
						<input hint="roomCurrency"  id="travel-set-trip-roomCurrency"
							class="easyui-combobox" control="combobox" tag="0" style="width:80px;height:25px"
							data-options="valueField:'id',textField:'text',panelHeight:'auto'">&nbsp;/每晚
					</div>
				</div>
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:126px;height:25px;display:inline-block;text-align: right;"><label>共几晚</label></div>
				<input hint="daysNum" class="easyui-numberbox" control="numberbox" tag="0" style="width:164px;height:25px" data-options="min:0">
			</li>
			<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label>领队人员</label></div>
				<input hint="tourManagerId" id="travel-set-trip-tourManagerId" class="easyui-combobox" control="combobox" tag="0" style="width:145px;height:100%">
			</li>
			<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		       	<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label>行程描述</label></div>
		        <input hint="tripDetail" class="easyui-textbox" data-options="multiline:true,validType:'length[0,200]'" control="textbox" tag="0" style="width:506px;height:100%">
		    </li>
			<li style="width:640px;float:left;margin-right:15px;margin-bottom:5px;position: relative;">
		       	<div style="width:100px;height:25px;display:inline-block;position: absolute;text-align: right;"><label>机票详情</label></div>
		        <!-- <input hint="airTicket" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%"> -->
		        <div style="width: 531px;margin-left: 105px ;">
		        	<textarea id="travel-set-trip-airTicket" name="airTicket" style="width: 434px;height: 100%;margin-left: 124px;border: 1px solid #95B8E7;border-radius: 4px;outline: none;">

							</textarea>
		        </div>

		    </li>
				<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;
					display: grid;grid-template-columns: 105px 1fr;">
					<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label style="padding-right: 0;">行程关联定价</label></div>
					<div style="position: relative;width: 511px;padding-right: 0;">
						<input id="travel-set-trip-personTripProdName"
							style="width:505px;height:25px;text-indent: 10px;outline: none;"
							placeholder="点击选择" class="textbox"
							onclick="selectProdPrice.init('#travel-set-trip-personTripProdName','#travel-set-trip-personTripProdCode')" />
						<a href="javascript:$('#travel-set-trip-personTripProdName,#travel-set-trip-personTripProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 4px;top: 1px"></a>
						<input type="hidden" id="travel-set-trip-personTripProdCode" >
					</div>
				</li>
				<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;
					display: grid;grid-template-columns: 105px 1fr;">
					<div style="width:100px;height:25px;display:inline-block;text-align: right;"><label style="padding-right: 0;">单房差关联定价</label></div>
					<div style="position: relative;width: 511px;padding-right: 0;">
						<input id="travel-set-trip-roomProdName"
							style="width:505px;height:25px;text-indent: 10px;outline: none;"
							placeholder="点击选择" class="textbox"
							onclick="selectProdPrice.init('#travel-set-trip-roomProdName','#travel-set-trip-roomProdCode')" />
						<a href="javascript:$('#travel-set-trip-roomProdName,#travel-set-trip-roomProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 4px;top: 1px"></a>
						<input type="hidden" id="travel-set-trip-roomProdCode" >
					</div>
				</li>
				<!-- <li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					<div style="width:150px;height:25px;display:inline-block;"><label>行程关联定价</label></div>
					<input hint="projProductTripId" class="easyui-combobox" id="travel-set-trip-projProductTripId" control="combobox" tag="0"
						data-options="
							cls: 'travel-set-trip-projProductTripId',
							panelHeight: 'auto',panelMaxHeight:300,
						"
					  style="width:460px;height:25px;">
				</li>
				<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					<div style="width:150px;height:25px;display:inline-block;"><label>单房差关联定价</label></div>
					<input hint="projProductRoomId" class="easyui-combobox" id="travel-set-trip-projProductRoomId" control="combobox" tag="0"
						data-options="
							cls: 'travel-set-trip-projProductRoomId',
							panelHeight: 'auto',panelMaxHeight:300,
						"
					  style="width:460px;height:25px;">
				</li>
				<script>
					$(_=>{
						var $sc = $('#travel-set-trip-projProductTripId');
						$sc.combobox({
							icons: [{
								iconCls:'icon-remove',
								handler: function(e){
									$sc.combobox('clear');
									// $('.travel-set-trip-projProductTripId .icon-remove').parent().hide();
								}
							}],
						});
						var $sc2 = $('#travel-set-trip-projProductRoomId');
						$sc2.combobox({
							icons: [{
								iconCls:'icon-remove',
								handler: function(e){
									$sc2.combobox('clear');
									// $('.travel-set-trip-projProductRoomId .icon-remove').parent().hide();
								}
							}],
						});
					})
				</script> -->
		</ul>
	</div>
	<div id="travel-set-trip-panel-2" class="easyui-panel" title="地接信息" style="width:100%;height:200px;padding:10px;">
		    <div id="travel-set-tripChild-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_tripChild_add()" plain="true">选取</a>
            	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_tripChild_del_batch()" plain="true">删除</a>
            </div>
	        </div>
  			<table id="travel-set-tripChild-datagrid" class="easyui-datagrid" toolbar="#travel-set-tripChild-toolbar"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			travel_set_tripChild_datagrid_onClickRow(rowIndex);

	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			//if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {

	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'localReceptionId',width:30">地接ID</th>
						<th data-options="field:'reception',width:40">地接名称</th>
						<th data-options="field:'hotel',width:40">酒店名称</th>
						<th data-options="field:'startDate',width:40,editor:{type:'datebox',
								options:{
						            onSelect:function(record){
						                travel_set_tripChild_datagrid_validate(1,record)
						            }
						        }
							}">开始时间</th>
						<th data-options="field:'endDate',width:40,editor:{type:'datebox',
								options:{
						            onSelect:function(record){
						                travel_set_tripChild_datagrid_validate(2,record)
						            }
						        }

							}">结束时间</th>
						<!--editor:{type:'datebox',options:{required:true}  -->
					</tr>
				</thead>
			</table>
	</div>
</div>
<!-- end of easyui-window -->


<!-- begin of easyui-window -->
<div id="travel-set-reception-select-window" class="easyui-window" toolbar="#travel-set-reception-select-window-toolbar" data-options="closed:true,title:'地接选择',modal:true" style="width:700px;height:500px;">
	<!-- begin of toolbar -->
	<div id="travel-set-reception-select-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="travel_set_reception_select_save()" plain="true">确定</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="travel_set_reception_select_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="travel-set-reception-select-panel-1" class="easyui-panel" title="" style="width:100%;height:90%;padding:10px;">
		<table id="travel-set-reception-select-datagrid" class="easyui-datagrid"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {

	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'localReceptionId',width:30">地接ID</th>
						<th data-options="field:'reception',width:40">地接名称</th>
						<th data-options="field:'hotel',width:40">酒店名称</th>
						<th data-options="field:'startDate',width:40">开始时间</th>
						<th data-options="field:'endDate',width:40">结束时间</th>
					</tr>
				</thead>
		</table>
	</div>
</div>
<!-- end of easyui-window -->



<!-- begin of easyui-window -->
<div id="travel-set-reception-window" class="easyui-window" toolbar="#travel-set-reception-window-toolbar" data-options="closed:true,title:'地接内容',modal:true" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="travel-set-reception-window-toolbar">
	    <div class="my-toolbar-button">
	    	<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="travel_set_reception_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" hint="mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="travel_set_reception_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="travel_set_reception_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="travel_set_reception_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="travel_set_reception_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="travel-set-reception-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>地接名称</label></div>
				<input hint="reception" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>酒店名称</label></div>
				<input hint="hotel" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>开始时间</label></div>
				<input hint="startDate"  class="easyui-datebox" id="data" control="datebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>结束时间</label></div>
				<input hint="endDate" class="easyui-datebox"  id="data2" control="datebox" tag="0" style="width:175px;height:25px">
			</li>
			<p id="result"></p>

		</ul>
	</div>
</div>
<!-- end of easyui-window -->

<!-- begin of easyui-window -->
<div id="travel-set-visa-data-select-window" class="easyui-window" toolbar="#travel-set-visa-data-select-window-toolbar" data-options="closed:true,title:'材料选择',modal:true" style="width:700px;height:500px;">
	<!-- begin of toolbar -->
	<div id="travel-set-reception-select-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="travel_set_visa_data_select_save()" plain="true">确定</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="travel_set_visa_data_select_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="travel-set-visa-data-select-panel" class="easyui-panel" title="" style="width:100%;height:90%;padding:10px;">
		<table id="travel-set-visa-data-select-datagrid" class="easyui-datagrid"
	        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			//if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {

	 				},
	 				">
	        	<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'visaDataId',width:40">材料ID</th>
						<th data-options="field:'dataNumber',width:40">材料编号</th>
						<th data-options="field:'dataName',width:40">材料名称</th>

					</tr>
				</thead>
		</table>
	</div>
</div>
<!-- end of easyui-window -->

<script type="text/javascript">
	function travel_set_init_priceLink() {
		// $('#travel-set-trip-projProductTripId').combobox({
		// 	valueField:'projProductId',textField:'showProdContent'
		// 	,limitToList:true,panelHeight:'auto'
		// 	,url: eippath + '/boothType/selectProductTypePricing'
		// 	,onBeforeLoad: function (param) {
		// 		param.projectId = project_for_pass;
		// 		param.businessType = 4; // 业务类型: 1 展位、 2 进馆证 、3 邀请函 、4 行程人员费 、 5 单房费 , 默认1
		// 	},onSelect: function(record){
		// 	},onLoadSuccess: function(data){
		// 		if(data instanceof Array){ return; }
		// 		if (data && data.state === 1){
		// 			$(this).combobox('loadData', JSON.parse(data.data));
		// 		}
		// 	}
		// });
		// $('#travel-set-trip-projProductRoomId').combobox({
		// 	valueField:'projProductId',textField:'showProdContent'
		// 	,limitToList:true,panelHeight:'auto'
		// 	,url: eippath + '/boothType/selectProductTypePricing'
		// 	,onBeforeLoad: function (param) {
		// 		param.projectId = project_for_pass;
		// 		param.businessType = 5; // 业务类型: 1 展位、 2 进馆证 、3 邀请函 、4 行程人员费 、 5 单房费 , 默认1
		// 	},onSelect: function(record){
		// 	},onLoadSuccess: function(data){
		// 		if(data instanceof Array){ return; }
		// 		if (data && data.state === 1){
		// 			$(this).combobox('loadData', JSON.parse(data.data));
		// 		}
		// 	}
		// })
	}
 	$(function(){
		setTimeout(function(){
			travel_set_loadKindEditor();
			travel_set_init_priceLink();

			travel_set_loadOperators();
			travel_set_trip_fresh();
			travel_set_reception_fresh();
			travel_set_translate_fresh();
			travel_set_visa_data_fresh();

			if(!fu_visa_data) {
				$('.fu_visa_data').remove();
			} else {
				$('.fu_visa_data').removeClass('hide');
			}
		}, 0);
	});
	window.travelSet = {
		checkReturn: function (obj, msg = "", icon = "", throws = false) {
			icon = icon || "warning";
			if (obj && obj.state === 1) {
				return true;
			} else {
				this.alert(msg || obj.msg, icon, throws);
				return false;
			}
		},
		checkDate: function(str){
			dateFormat = /^[12](\d{3})-[01][0-9]-[0123][0-9]$/;
			return dateFormat.test(str)
		},
		error(msg ='请求失败', icon = 'error', throws = true) {
			return this.alert(msg, icon, throws);
		},
		alert: function (msg = "", icon, throws = false) {
			if (msg) {
				$.messager.alert("提示", msg, icon ? icon : "warning");
				if (throws) throw new Error("error: " + msg);
			}
		},
		postJson: function (url, data, options, cb, cbErr) {
			const df = {
				// async:true,	// 默认,异步
				type: "post",
				url: eippath + url,
				data: JSON.stringify(data),
				contentType: "application/json",
				dataType: "json",
				success: cb || function (rsp) {
					console.log("rsp", rsp);
				},
				error: cbErr || function (data,type, err) {
					console.log("error", data , type , err);
				},
			};
			$.ajax(options ? Object.assign(df, options) : df);
		},
		post: function (url, data, options, cb, cbErr) {
			const df = {
				// async:true,	// 默认,异步
				type: "post",
				url: eippath + url,
				data,
				dataType: "json",
				success: cb || function (rsp) {
					console.log("rsp", rsp);
				},
				error: cbErr || function (data,type, err) {
					console.log("error", data , type , err);
				},
			};
			$.ajax(options ? Object.assign(df, options) : df);
		},
		//
	}
	// 行程页面相关功能 start
 	var travel_set_trip_edit_mode;
	var travel_set_trip_button_edit_mode;
	var travel_set_trip_alocate_id = -1;
	var travel_set_trip_window_close_set = false;
	var travel_set_trip_save_id;

	function travel_set_loadOperators() {
		$.ajax({
			url: eippath + '/operator/getAll',
			dataType: "json",
			type: "post",
			data: { },
			async: false,
			success(data){
				$('#travel-set-trip-tourManagerId').combobox({
					textField: 'text',
					valueField: 'id',
				}).combobox('loadData', data);
			}
		});
	}

	function travel_set_trip_fresh(){
		$('#travel-set-trip-datagrid').datagrid({
			url: eippath + '/projTrip/getList?projectId=' + project_for_pass,
			onLoadSuccess: function(data){
				if(travel_set_trip_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].projTripId == travel_set_trip_alocate_id){
							$(this).datagrid('selectRow', i);
							travel_set_trip_alocate_id = -1;
							break;
						}
					}
				}
			},
			onSortColumn:function(sort,order){
				//alert(sort+"/"+order)
			}
		});
	}

	function travel_set_trip_format_IsDefault(value,row,index){
		if(value){
			return '是';
		}else{
			return '否';
		}
	}

	function travel_set_trip_window_close(){
		$('#travel-set-trip-window').window({
			onBeforeClose: function(){
				if(travel_set_trip_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){

							// KindEditor.html("#travel-set-trip-airTicket","");
						    // KindEditor.remove('#travel-set-trip-airTicket');
							$('#travel-set-trip-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				//travel_set_trip_fresh();
			}
		});
	}


	  //富文本编辑器
 	var travel_set_editor ;
	 /*KindEditor 编辑器设置像素的话最低 宽度为650px  高度为202px 如果宽度用px设置，那么如果 小于650px，编辑器宽度将采用650px。
	       如果遇到特殊情况 编辑器宽度需要小于650px时，采用%设置
	 */
	function travel_set_loadKindEditor(){
		var options = {
				filterMode : false,//true时过滤HTML代码，false时允许输入任何代码。
				allowFileManager : false,  //true或false，true时显示浏览服务器图片功能。
				uploadJson : eippath +'/news/upload', //图片上传路径
	            afterUpload: function(){this.sync();}, //图片上传后，将上传内容同步到textarea中
	            afterBlur: function(){this.sync();},   ////失去焦点时，将上传内容同步到textarea中
		        items:['undo', 'redo','insertorderedlist','insertunorderedlist','|', 'justifyleft', 'justifycenter', 'justifyright',],
		        width:"95%",
		        minheight: 30,
	            resizeType: 0,
		};
		travel_set_editor = KindEditor.create('#travel-set-trip-airTicket',options);
		//KindEditor.html("#travel-set-trip-airTicket","");
	}



	function travel_set_trip_add(){

		travel_set_trip_query_currency();

		if(!travel_set_trip_window_close_set){
			travel_set_trip_window_close();
			travel_set_trip_window_close_set = true;
		}
		travel_set_editor.html("");
		//travel_set_loadKindEditor();//加载富文本框

		travel_set_trip_alocate_id = -1;
		$('#travel-set-trip-window').window('open');
		travel_set_trip_save_id = -1;
		setEditInfoClear("#travel-set-trip-panel-1 ul");
		// $('#travel-set-trip-projProductTripId').combobox('setValue', '');
		// $('#travel-set-trip-projProductRoomId').combobox('setValue', '');
		$('#travel-set-trip-personTripProdCode,#travel-set-trip-personTripProdName,#travel-set-trip-roomProdCode,#travel-set-trip-roomProdName').val('');
		$('#travel-set-trip-roomCurrency,#travel-set-trip-personTripCurrency').combobox('setValue', '');
		$('#travel-set-trip-tourManagerId').combobox('setValue', '');
		travel_set_trip_edit_mode = "Add";
		setEditInfoReadOnly(1, "#travel-set-trip-panel-1 ul");
		$('#travel-set-trip-personTripProdName,#travel-set-trip-roomProdName').parent().removeClass('js-disable')
		$('#travel-set-trip-personTripCurrency').combobox('readonly', !true)
		$('#travel-set-trip-roomCurrency').combobox('readonly', !true)
		setButtonEditMode(1, "#travel-set-trip-window-toolbar div");
		setButtonEditMode(1, "#travel-set-tripChild-toolbar div");
		travel_set_trip_button_edit_mode = 1;
		/* $('#travel-set-tripChild-datagrid').datagrid({
			url: eippath + '/tripChild/getList?projTripId=' + travel_set_trip_save_id,
			onLoadSuccess: function(data){

			}
		}); */
		$('#travel-set-tripChild-datagrid').datagrid({
			url: eippath + '/data/clear_datagrid.json',
			onLoadSuccess: function(data){

			}
		});


		travel_set_editor.readonly(false);
	}
	setInterval("getLoc()",150);
	var flag = false;
	function getLoc(){
		var rows = $('#travel-set-trip-datagrid').datagrid('getSelections');
		if (rows.length > 1){
		       	$(".row-lenght").css("opacity","0.5")
		       flag =true;
		     }else{
				 $(".row-lenght").css("opacity","1")
			   flag = false;
		     }
	}

	function travel_set_trip_mod(){
		if (flag)return;
		travel_set_trip_mod_or_view(1);
	}
	function travel_set_trip_mod_or_view(flag){
		travel_set_trip_query_currency();

		if(!travel_set_trip_window_close_set){
			travel_set_trip_window_close();
			travel_set_trip_window_close_set = true;
		}
		var row = $('#travel-set-trip-datagrid').datagrid('getSelected');
		if (row){

			//travel_set_loadKindEditor();//加载富文本框
			travel_set_editor.html("");
			travel_set_editor.html(row.airTicket);

			travel_set_trip_alocate_id = row.projTripId;
			$('#travel-set-trip-window').window('open');
			travel_set_trip_save_id = row.projTripId;
			setEditInfoInit(row, "#travel-set-trip-panel-1 ul");
			$('#travel-set-trip-tourManagerId').combobox('setValue', row.tourManagerId || '');
			$("#travel-set-trip-isDefault").combobox('setValue',row.isDefault+"");

			$("#travel-set-trip-roomProdCode").val(row.roomProdCode || '');
			$("#travel-set-trip-roomProdName").val(row.roomProdName || '');
			$("#travel-set-trip-personTripProdCode").val(row.personTripProdCode || '');
			$("#travel-set-trip-personTripProdName").val(row.personTripProdName || '');
			$("#travel-set-trip-personTripCurrency").combobox('setValue',row.personTripCurrency || '');
			$("#travel-set-trip-roomCurrency").combobox('setValue',row.roomCurrency || '');


			$('#travel-set-tripChild-datagrid').datagrid({
				url: eippath + '/tripChild/getList?projTripId=' + row.projTripId,
				onLoadSuccess: function(data){

				}
			});

			travel_set_trip_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_trip_mod2(){
		travel_set_trip_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function travel_set_trip_edit(flag){
		travel_set_trip_edit_mode = "Mod";
		setEditInfoReadOnly(flag, "#travel-set-trip-panel-1 ul");
		if(flag === 1) {
			$('#travel-set-trip-personTripCurrency').combobox('readonly', !true)
			$('#travel-set-trip-roomCurrency').combobox('readonly', !true)
			$('#travel-set-trip-personTripProdName,#travel-set-trip-roomProdName').parent().removeClass('js-disable')
		} else {
			$('#travel-set-trip-personTripProdName,#travel-set-trip-roomProdName').parent().addClass('js-disable')
			$('#travel-set-trip-personTripCurrency').combobox('readonly', true)
			$('#travel-set-trip-roomCurrency').combobox('readonly', true)
		}
		setButtonEditMode(flag, "#travel-set-trip-window-toolbar div");
		setButtonEditMode(flag, "#travel-set-tripChild-toolbar div");
		travel_set_trip_button_edit_mode = flag;
		//alert(flag)
		if(flag==2){
			travel_set_editor.readonly(true);
		}else{
			travel_set_editor.readonly(false);
		}
	}
	function travel_set_trip_view(){
		if (flag)return;
		travel_set_trip_mod_or_view(2);
	}
	function travel_set_trip_view_db(row){
		travel_set_trip_query_currency();

		if(!travel_set_trip_window_close_set){
			travel_set_trip_window_close();
			travel_set_trip_window_close_set = true;
		}
		//travel_set_loadKindEditor();//加载富文本框
		travel_set_editor.html("");
		travel_set_editor.html(row.airTicket);
		travel_set_trip_alocate_id = row.projTripId;
		$('#travel-set-trip-window').window('open');
		travel_set_trip_save_id = row.projTripId;
		setEditInfoInit(row, "#travel-set-trip-panel-1 ul");
		$("#travel-set-trip-isDefault").combobox('setValue',row.isDefault+"");
		$('#travel-set-tripChild-datagrid').datagrid({
			url: eippath + '/tripChild/getList?projTripId=' + row.projTripId,
			onLoadSuccess: function(data){

			}
		});
		travel_set_trip_edit(2);

	}


	function travel_set_tripChild_load(){

		$('#travel-set-tripChild-datagrid').datagrid({
			url: eippath + '/tripChild/getList?projTripId=' + travel_set_trip_alocate_id,
			onLoadSuccess: function(data){

			}
		});
	}


	function travel_set_trip_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/projTrip/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {projTripId: travel_set_trip_save_id},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							travel_set_trip_alocate_id = -1;
							$('#travel-set-trip-window').window('close');
							travel_set_trip_fresh();
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function travel_set_trip_del_batch(){
		var rows = $('#travel-set-trip-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {projTripId: rows[i].projTripId};
			}
			$.messager.confirm('提示', '当前选择的行程数为：' + rows.length + '，确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/projTrip/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								travel_set_trip_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_trip_save(){
		travel_set_tripChild_datagrid_endEditing();
		var postData = {};
		postData['editMode'] = travel_set_trip_edit_mode;
		postData['projTripId'] = travel_set_trip_save_id;
		postData['projectId'] = project_for_pass;
		setPostData(postData, "#travel-set-trip-panel-1 ul");
		postData['projectId'] = project_for_pass;
		// postData['projProductTripId'] = $('#travel-set-trip-projProductTripId').combobox('getValue');
		// postData['projProductRoomId'] = $('#travel-set-trip-projProductRoomId').combobox('getValue');
		postData['personTripProdCode'] = $('#travel-set-trip-personTripProdCode').val();
		postData['roomProdCode'] = $('#travel-set-trip-roomProdCode').val();
		postData['roomCurrency'] = $('#travel-set-trip-roomCurrency').combobox('getValue');
		postData['personTripCurrency'] = $('#travel-set-trip-personTripCurrency').combobox('getValue');
		postData['airTicket'] = travel_set_editor.html();


		if(postData['tripName'].trim()==''){
			$.messager.alert('提示','行程名称不能为空','warning');
			return;
		}
		if(postData['beginPlace'].trim()==''){
			$.messager.alert('提示','出发地不能为空','warning');
			return;
		}
		if(postData['endPlace'].trim()==''){
			$.messager.alert('提示','目的地不能为空','warning');
			return;
		}
		// if(postData['difPrice']==undefined||postData['difPrice']==''){
		// 	$.messager.alert('提示','人员价格不能为空','warning');
		// 	return;
		// }
		// if(postData['difPriceRoom']==undefined||postData['difPriceRoom']==''){
		// 	$.messager.alert('提示','单房差价不能为空','warning');
		// 	return;
		// }
		/* if(postData['isDefault']==''){
			postData['isDefault'] = 'false';
		} */
		postData['isDefault'] = 'false';
		var rows = $('#travel-set-tripChild-datagrid').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['tripChildList[' + i + '].tripChildId'] = rows[i].tripChildId;
				postData['tripChildList[' + i + '].projTripId'] = rows[i].projTripId;
				postData['tripChildList[' + i + '].localReceptionId'] = rows[i].localReceptionId;
				if(rows[i].startDate!=''){
					postData['tripChildList[' + i + '].startDate'] = rows[i].startDate;
				}
				if(rows[i].endDate!=''){
					postData['tripChildList[' + i + '].endDate'] = rows[i].endDate;
				}
			}
		}

	/* 	if(postData['isDefault']=='true'||postData['isDefault']==true){
			var isExistDefault = travel_set_trip_isExistDefault(travel_set_trip_save_id);
			//alert(isExistDefault)
			if(isExistDefault==-1){
				return;
			}else if(isExistDefault>0){
				$.messager.alert('提示','已经存在默认行程','warning');
				return;
			}
		}
		 */
		//alert(JSON.stringify(postData))
		$.ajax({
			url: eippath + '/projTrip/saveMore',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state > 0){
					$.messager.alert('提示','保存成功！','info');
					travel_set_trip_edit(2);
					//alert(data.state)
					//travel_set_trip_alocate_id = postData['projTripId'];
					travel_set_trip_alocate_id = data.state;
					travel_set_trip_save_id = data.state;
					// $('#travel-set-trip-window').window('close');
					 travel_set_trip_fresh();
				}else{
					$.messager.alert('提示',data.msg || data.message || '保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}

	function travel_set_trip_isExistDefault(id){
		var state = -1;
		$.ajax({
			url: eippath + '/projTrip/isExistDefault',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {projectId:project_for_pass,notEqualId:id},	//发送数据
			async: false,
			success: function(data){
				state = data.state;
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
		return state;
	}

	function travel_set_trip_cancel(){
		// 关闭Dialog前移除编辑器
		// KindEditor.html("#travel-set-trip-airTicket","");
	    // KindEditor.remove('#travel-set-trip-airTicket');

		$('#travel-set-trip-window').window('close', true);
	}


	function travel_set_tripChild_add(){
		$('#travel-set-reception-select-datagrid').datagrid({
			url: eippath + '/localReception/getList?projectId=' + project_for_pass,
			onLoadSuccess: function(data){
				//$('#travel-set-reception-select-datagrid').datagrid('fitColumns');
				if(data!=null&&data.total>0){
					$('#travel-set-reception-select-window').window('open', true);
					$('#travel-set-reception-select-datagrid').datagrid('fitColumns');
				}else{
					$.messager.alert('提示','未查询到地接信息，请先到地接设置里填写相关信息','warning');
				}

			}
		});


	}
	function travel_set_tripChild_del_batch(){
		var rows = $('#travel-set-tripChild-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				var index = $('#travel-set-tripChild-datagrid').datagrid('getRowIndex',rows[i]);
			    $('#travel-set-tripChild-datagrid').datagrid('deleteRow',index);
			}

		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_reception_select_save(){
		var rows = $('#travel-set-reception-select-datagrid').datagrid('getSelections');
		if (rows.length > 0){

			var tableData = $('#travel-set-tripChild-datagrid').datagrid('getData').rows;
			var n = -1;
			for(var i=0;i<tableData.length;i++){
				for(var j=0;j<rows.length;j++){
					if(tableData[i].localReceptionId == rows[j].localReceptionId){
						n = j;
						break;
					}
				}
				if(n>-1){
					break;
				}
			}
			if(n>-1){
				$.messager.alert('提示','已存在 '+rows[n].reception+'，请不要重复选择','info');
				return;
			}


			for(var i = 0; i < rows.length; i++){
				/* $('#travel-set-tripChild-datagrid').datagrid('insertRow',{
					index: 0,	// index start with 0
					row: {
						tripChildId:-1,
						projTripId:travel_set_trip_save_id,
						localReceptionId:rows[i].localReceptionId,
						startDate:rows[i].startDate,
						endDate:rows[i].endDate,
						reception:rows[i].reception,
						hotel:rows[i].hotel
					}
				}); */
				$('#travel-set-tripChild-datagrid').datagrid('appendRow',{
					tripChildId:-1,
					projTripId:travel_set_trip_save_id,
					localReceptionId:rows[i].localReceptionId,
					startDate:rows[i].startDate,
					endDate:rows[i].endDate,
					reception:rows[i].reception,
					hotel:rows[i].hotel

				});
			}
			$('#travel-set-reception-select-window').window('close', true);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_reception_select_cancel(){
		$('#travel-set-reception-select-window').window('close', true);
	}


	var travel_set_tripChild_datagrid_editIndex = null;
	function travel_set_tripChild_datagrid_onClickRow(index){
		if(travel_set_trip_button_edit_mode!=1){
			return;
		}

		if(travel_set_tripChild_datagrid_editIndex != null && travel_set_tripChild_datagrid_editIndex != index){
			travel_set_tripChild_datagrid_endEditing();
			$('#travel-set-tripChild-datagrid').datagrid('beginEdit', index);
			travel_set_tripChild_datagrid_editIndex = index;
		}else{
			$('#travel-set-tripChild-datagrid').datagrid('beginEdit', index);
			travel_set_tripChild_datagrid_editIndex = index;
		}
	}
	function travel_set_tripChild_datagrid_endEditing(){

		if(travel_set_tripChild_datagrid_editIndex!=null){
			$('#travel-set-tripChild-datagrid').datagrid('endEdit', travel_set_tripChild_datagrid_editIndex);
		}

	}


     function travel_set_tripChild_datagrid_validate(flag,val){

    	 var startTarget = $('#travel-set-tripChild-datagrid').datagrid('getEditor',
 			 	{'index':travel_set_tripChild_datagrid_editIndex,'field':'startDate'}).target;
 		 var endTarget = $('#travel-set-tripChild-datagrid').datagrid('getEditor',
			 	{'index':travel_set_tripChild_datagrid_editIndex,'field':'endDate'}).target;
    	 if(flag == 1){
    		 var startDate = val;
        	 var endDate = endTarget.datebox('getValue');
        	 if(endDate!=''){
        		 var stime = val.getTime();
        		 var etime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
        		 if(stime>etime){
        			 endTarget.datebox('setValue','');
        		 }
        	 }
    	 }else{
    		 var startDate = startTarget.datebox('getValue');
        	 var endDate = val;
        	 if(startDate!=''){
        		 var stime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
        		 var etime = endDate.getTime();
        		 if(stime>etime){
        			 startTarget.datebox('setValue', '');
        		 }
        	 }
    	 }

    }


    function travel_set_trip_query_currency() {
			// 查询币种
			$.ajax({
				url: eippath + '/fnCurrency/getAll',
				dataType: "json",	//返回数据类型
				type: "post",
				data: { },
				async: false,
				success (data) {
					let rsp =  data || [];
					$('#travel-set-trip-roomCurrency').combobox('loadData',rsp)
					$('#travel-set-trip-personTripCurrency').combobox('loadData',rsp)
				}
			})
        //  $.ajax({
        //      url: eippath + '/prodCurrency/getList',
        //      dataType: "json",	//返回数据类型
        //      type: "post",
        //      data: {f_project_id: project_for_pass},
        //      async: true,
        //      success: function (data) {
        //          //alert(JSON.stringify(data))
        //          if (data.total > 0) {
        //              rows = data.rows;
        //              for (var x in rows) {
        //               	if (rows[x].f_type == 'ROOM_DIFF') {
        //               		//$("#travel-set-trip-room-currency").html("（"+rows[x].f_currency_name+"）");
        //               		$("#travel-set-trip-room-currency").html(rows[x].f_currency_name);
        //                  }
        //               	if (rows[x].f_type == 'PERSON_PRICE') {
        //                 	 $("#travel-set-trip-person-currency").html("（"+rows[x].f_currency_name+"）");
        //                  }
        //              }

        //          }else{
        //         	 $("#travel-set-trip-room-currency").html("");
        //         	 $("#travel-set-trip-room-currency").html("");
        //          }
        //      },
        //      error: function () {
        //     	 $("#travel-set-trip-room-currency").html("");
        //     	 $("#travel-set-trip-room-currency").html("");
        //      }
        //  });
    }





	// 行程页面相关功能end


	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------



	//地接页面相关功能start
	var travel_set_reception_edit_mode;
	var travel_set_reception_button_edit_mode;
	var travel_set_reception_alocate_id = -1;
	var travel_set_reception_window_close_set = false;
	var travel_set_reception_save_id;

	function travel_set_reception_fresh(){
		$('#travel-set-reception-datagrid').datagrid({
			url: eippath + '/localReception/getList?projectId=' + project_for_pass,
			onLoadSuccess: function(data){
				if(travel_set_reception_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].localReceptionId == travel_set_reception_alocate_id){
							$(this).datagrid('selectRow', i);
							travel_set_reception_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}



	function travel_set_reception_window_close(){
		$('#travel-set-reception-window').window({
			onBeforeClose: function(){
				if(travel_set_reception_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#travel-set-reception-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				travel_set_reception_fresh();
			}
		});
	}
	function travel_set_reception_add(){
		if(!travel_set_reception_window_close_set){
			travel_set_reception_window_close();
			travel_set_reception_window_close_set = true;
		}
		travel_set_reception_alocate_id = -1;
		$('#travel-set-reception-window').window('open');
		travel_set_reception_save_id = -1;
		setEditInfoClear("#travel-set-reception-panel-1 ul");
		travel_set_reception_edit_mode = "Add";
		setEditInfoReadOnly(1, "#travel-set-reception-panel-1 ul");
		setButtonEditMode(1, "#travel-set-reception-window-toolbar div");
		travel_set_reception_button_edit_mode = 1;

		$("#data").datebox({
			onChange: function(n, o){
				if(n != ''){
					var startDate = new Date(Date.parse(n.replace(/-/g, "/"))).getTime();
					var end = $('#data2').datebox("getValue");
					if(end!=''){
						var endDate = new Date(Date.parse(end.replace(/-/g, "/"))).getTime();
						var days = endDate - startDate;
					    var day = parseInt(days / (1000 * 60 * 60 * 24));
					   if(day<0){
						  $('#data2').datebox("setValue",'');
					   }
					}
				}
			}
		});
		$("#data2").datebox({
			onChange: function(n, o){
				if(n != ''){
					var endDate = new Date(Date.parse(n.replace(/-/g, "/"))).getTime();
					var start = $('#data').datebox("getValue");
					if(start!=''){
						var startDate = new Date(Date.parse(start.replace(/-/g, "/"))).getTime();
						var days = endDate - startDate;
					    var day = parseInt(days / (1000 * 60 * 60 * 24));
					    if(day<0){
							 $('#data').datebox("setValue",'')
						}

					}
				}
				}
		});
	}
	function travel_set_reception_mod(){
		travel_set_reception_mod_or_view(1);
	}
	function travel_set_reception_mod_or_view(flag){
		if(!travel_set_reception_window_close_set){
			travel_set_reception_window_close();
			travel_set_reception_window_close_set = true;
		}
		var row = $('#travel-set-reception-datagrid').datagrid('getSelected');
		if (row){
			travel_set_reception_alocate_id = row.localReceptionId;
			$('#travel-set-reception-window').window('open');
			travel_set_reception_save_id = row.localReceptionId;
			setEditInfoInit(row, "#travel-set-reception-panel-1 ul");
			travel_set_reception_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_reception_mod2(){
		travel_set_reception_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function travel_set_reception_edit(flag){
		travel_set_reception_edit_mode = "Mod";
		setEditInfoReadOnly(flag, "#travel-set-reception-panel-1 ul");
		setButtonEditMode(flag, "#travel-set-reception-window-toolbar div");
		travel_set_reception_button_edit_mode = flag;
	}
	function travel_set_reception_view(){
		travel_set_reception_mod_or_view(2);
	}
	function travel_set_reception_view_db(row){
		if(!travel_set_reception_window_close_set){
			travel_set_reception_window_close();
			travel_set_reception_window_close_set = true;
		}
		travel_set_reception_alocate_id = row.localReceptionId;
		$('#travel-set-reception-window').window('open');
		travel_set_reception_save_id = row.localReceptionId;
		setEditInfoInit(row, "#travel-set-reception-panel-1 ul");
		travel_set_reception_edit(2);

	}



	function travel_set_reception_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/localReception/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {localReceptionId: travel_set_reception_save_id},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							travel_set_reception_alocate_id = -1;
							$('#travel-set-reception-window').window('close');
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function travel_set_reception_del_batch(){
		var rows = $('#travel-set-reception-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {localReceptionId: rows[i].localReceptionId};
			}
			$.messager.confirm('提示', '当前选择的行程数为：' + rows.length + '，确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/localReception/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								travel_set_reception_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_reception_save(){
		var postData = {};
		postData['editMode'] = travel_set_reception_edit_mode;
		postData['localReceptionId'] = travel_set_reception_save_id;
		postData['projectId'] = project_for_pass;
		setPostData(postData, "#travel-set-reception-panel-1 ul");
		if(postData['reception'].trim()==''){
			$.messager.alert('提示','地接名称不能为空','warning');
			return;
		}
		$.ajax({
			url: eippath + '/localReception/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state >0){
					$.messager.alert('提示','保存成功！','info');
					travel_set_reception_edit(2);
					travel_set_reception_alocate_id = data.state;
					travel_set_reception_save_id = data.state;
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
                travel_set_reception_fresh();
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function travel_set_reception_cancel(){
		$('#travel-set-reception-window').window('close', true);
	}


	//地接页面相关功能end

	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------

	// 翻译页面相关功能 start
	var travel_set_translate_save_id = 0;

	function travel_set_translate_fresh(){
		$.ajax({
			url: eippath + '/translate/getByProjectId',
			dataType: "json",
			type: "post",
			data: {projectId:project_for_pass},
			async: true,
			success: function(data){
				if(data!=null){
					travel_set_translate_save_id = data.f_translate_id;
					$("#travel-set-translate-unitPrice").textbox("setValue",data.f_unit_price);
				}else{
					travel_set_translate_save_id = 0;
					$("#travel-set-translate-unitPrice").textbox("setValue",'');
				}
			},
			error: function(){
				$.messager.alert('提示','翻译数据加载失败！','error');
			}
		});
		travel_set_translate_query_currency();
	}
	function travel_set_translate_query_currency(){
		$.ajax({
			url: eippath + '/prodCurrency/selectCurrency',
			dataType: "json",
			type: "post",
			data: {projectId:project_for_pass,type:"TRANSLATE_PRICE"},
			async: true,
			success: function(data){

				//alert(JSON.stringify(data))
				if(data!=null){
					$("#travel-set-translate-currency").html(data.f_currency_name);
				}else{
					$("#travel-set-translate-currency").html("");
				}


			},
			error: function(){
				$("#travel-set-translate-currency").html("");
			}
		});
	}


	// 翻译页面相关功能end


	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------
	//--------------------------------------------------------------------------------------------------------

	// 签证材料页面相关功能 start

	var travel_set_visa_data_alocate_id = -1;

	function travel_set_visa_data_fresh(){
		$('#travel-set-visa-data-datagrid').datagrid({
			url: eippath + '/projVisaData/getList?projectId=' + project_for_pass,
			onLoadSuccess: function(data){
				if(travel_set_visa_data_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].projVisaDataId == travel_set_visa_data_alocate_id){
							$(this).datagrid('selectRow', i);
							travel_set_visa_data_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function travel_set_visa_data_add(){

		$('#travel-set-visa-data-select-window').window('open', true);
		$('#travel-set-visa-data-select-datagrid').datagrid({
			url: eippath + '/visaData/getList',
			onLoadSuccess: function(data){
				$('#travel-set-visa-data-select-datagrid').datagrid('fitColumns');
			}
		});

	}
	function travel_set_visa_data_select_save(){
		var rows = $('#travel-set-visa-data-select-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {projectId: project_for_pass,
							   visaDataId: rows[i].visaDataId,
							   amount:1
						   };
				}

			$.ajax({
				url: eippath + '/projVisaData/insertBatch',
				dataType: "json",	//返回数据类型
				type: "post",
				contentType: "application/json",
				data: JSON.stringify(postData),	//发送数据
				async: true,
				success: function(data){
					if(data.state == 1){

						$('#travel-set-visa-data-select-window').window('close', true);
						$.messager.alert('提示','操作成功！','info');
						travel_set_visa_data_fresh();
					}else{
						$.messager.alert('提示','操作失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});


		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function travel_set_visa_data_select_cancel(){
		$('#travel-set-visa-data-select-window').window('close', true);
	}

	function travel_set_visa_data_update_batch(){
		var l = $('#travel-set-visa-data-datagrid').datagrid('getRows').length;
        for (var i = 0; i < l; i++) {
            $('#travel-set-visa-data-datagrid').datagrid('endEdit', i);
        }

	    var rows = $('#travel-set-visa-data-datagrid').datagrid("getRows");

		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {projVisaDataId:rows[i].projVisaDataId,
							   projectId: rows[i].projectId,
							   visaDataId: rows[i].visaDataId,
							   amount:rows[i].amount
						   };
				}

			$.ajax({
				url: eippath + '/projVisaData/updateBatch',
				dataType: "json",	//返回数据类型
				type: "post",
				contentType: "application/json",
				data: JSON.stringify(postData),	//发送数据
				async: true,
				success: function(data){
					if(data.state == 1){
						$.messager.alert('提示','操作成功！','info');
					}else{
						$.messager.alert('提示','操作失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});


		}else{
			$.messager.alert('提示','没有数据！','warning');
		}
	}

	function travel_set_visa_data_del_batch(){
		var rows = $('#travel-set-visa-data-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {projVisaDataId: rows[i].projVisaDataId};
			}
			$.messager.confirm('提示', '当前选择的数量为：' + rows.length + '，确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/projVisaData/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state >0){


								$.messager.alert('提示','删除成功！','info');
								travel_set_visa_data_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}

	// 签证材料页面相关功能 end
</script>

