<!-- 邀请函设置 前置选择 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="2022030711" />
<script>var eippath = '${eippath}'</script>
<!-- <script src="${eippath}/js/jquery.qrcode.min.js" ></script> -->
<script src="${eippath}/js/jquery.qrcode.logo.ex.js" ></script>
<%@ include file="../common/reg_url.jsp" %>
<style type="text/css">
	/* .btn-primary{
		width: 84px;
		height: 32px;
		border-radius: 6px;
		font-size: 14px;
		border: solid 1px #4c72fe;
		color: #4c72fe;
		background-color: #fff;
		outline: none;
		cursor: pointer;
	} */
	.datagrid-cell, .datagrid-cell-group, .datagrid-header-rownumber, .datagrid-cell-rownumber {
		padding: 0 12px !important;
	}
	.datagrid-cell a{
		text-decoration: none;
	}
	.red {
		color: #F74444 !important;
	}
	body .window-shadow {
		height: auto !important;
	}
	.fix-easyui-warning .messager-question{
		background-position: -97px 0 !important;
	}
	.fix-easyui-button .messager-button{
		display: flex;
    justify-content: center;
    flex-direction: row-reverse;
	}
</style>
</head>
<body>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'north',split:true"  title=""   style="height:100px;"><!-- changeinfo -->
		<div style="height:50px;padding-left: 20px;">
			<span style="display:inline-block;font-size: 20px;margin-top:15px;" id="register-info-exihibitName" >${exhibition.exihibitName}</span>
		</div>
		<div style="height:30px;margin-top:10px;padding-left: 20px;">
			<span style="display:inline-block;">开幕时间：</span>
			<span style="display:inline-block;width: 150px;" id="register-info-startDate">${exhibition.startDate}</span>
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="display:inline-block;">闭幕时间：</span>
			<span style="display:inline-block;width: 150px;" id="register-info-endDate">${exhibition.endDate}</span>
		</div>
	</div>
	<!-- end of toolbar -->
	<div data-options="region:'center'" title="" style="width:100%;height:100%;">
		<!-- begin of toolbar -->
		<div id="app-set-toolbar">
			<div class="my-toolbar-button datagrid-toolbar">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add"
				 onclick="app.add();" plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit"
				onclick="app.edit();" plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove"
				 onclick="app.del();" plain="true">删除</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-db"
					onclick="app.rankSetting()" plain="true">邀请排行榜设置</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload"
				onclick="fresh()" plain="true">刷新</a>
			</div>
		</div>
		<table id="app-set-datagrid" class="easyui-datagrid" toolbar="#app-set-toolbar"
	    data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
			onClickRow: function (rowIndex, rowData) {
				var l = $(this).datagrid('getRows').length;
				for(var i = 0; i < l; i++){
					if(i != rowIndex)$(this).datagrid('unselectRow', i);
				}
			},">
	  </table>
	</div>
</div>

<!-- 新增修改弹窗 -->
<style>
#app-set-window{
	display: none;
	overflow: hidden !important;
}
.hide {
	display: none !important;
}
#app-set-form li+li{
	margin-top: 10px;
}
#app-set-form li>*{
	display: inline-block;
	box-sizing: border-box;
}
#app-set-form li>label{
	width: 85px;
}
</style>
<div id="app-set-window" data-options="title: '基本信息设置',width: 400,closed:true,modal:true,minimizable: false,maximizable: false,">
	<div class="my-toolbar-button datagrid-toolbar">
		<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-save" onclick="app.save()"
			plain="true">保存</a>
		<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel" onclick="app.close('app-set')" plain="true">取消</a>
	</div>
	<form id="app-set-form" style="width:100%;height:auto;padding:20px 0 0 15px;">
		<!-- <div id="app-set-panel-1" class="easyui-panel" title="--" style="width:100%;height:auto;padding:10px;"> -->
		<input type="hidden" id="jsf-inviteTemplateId" >
		<ul>
			<li>
				<label><i class="red">*</i> 邀请函名称</label>
				<input name="inviteTemplateName" class="easyui-textbox" control="textbox"
					tag="0" style="width: 255px;height:25px"
					id="jsf-inviteTemplateName" >
			</li>
			<li class="hide js-pids-only">
				<label style="padding-left: 10px;">限制项目</label>
				<div  style="position: relative;display: inline-block;margin-right: 10px;">
					<input name="limitProjectIds" class="easyui-combobox" data-options="
						panelMaxHeight: 200,panelHeight:'auto',limitToList: true,
						valueField: 'projectId',
						textField: 'projectName',
						prompt: '无限制',
						multiple: true,
						editable: false,
					"  control="combobox"
					 tag="1" style="width: 255px;height:25px"
					id="jsf-limitProjectIds" >
					<a href="javascript:;" onclick="$('#jsf-limitProjectIds').combobox('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
				</div>
			</li>
			<li>
				<label>模板类型</label>
				<div>
					<label style="display: inline-flex;align-items:center;">
						<input value="1" style="margin: 0;" type="radio" name="jsf-version">
						&nbsp;中文模板
					</label>
					<label style="margin-left: 20px;display: inline-flex;align-items:center;">
						<input value="2" checked style="margin: 0;" type="radio" name="jsf-version">
						&nbsp;英文模板
					</label>
				</div>
			</li>
		</ul>
		<!-- </div> -->
	</form>
</div>

<script>
	var app = {
		checkReturn(obj, msg = "", icon = "error", throws = true) {
			icon = icon || "warning";
			if (obj && obj.state === 1) {
				return true;
			} else {
				this.alert(msg || obj.msg || obj.message, icon, throws);
				return false;
			}
		},
		ok(msg='操作成功',title='提示信息', ops) {
			const df = {
					title:"<span style='color: #000000;'>"+title+"</span>",
					width:'300px',
					height:'120px',
					msg:"<span style='color: #000000;font-size: 20px;text-align:center;'>"+msg+"</span>",
			}
			$.messager.show(ops  ?  $.extends(df,ops) : df);
		},
		error(msg = "请求失败", icon="error", throws = true){
			this.alert(msg,icon,throws)
		},
		alert(msg = "", icon="error", throws = true) {
			//    msg = msg || '操作失败'
			msg = '<span style="word-break: break-word">' + (msg || '请求失败') + '</span>'
			if (msg) {
				$.messager.alert("提示", msg, icon ? icon : "warning");
				if (throws) throw new Error("error: " + msg);
			}
		},
		post(url, data, options, cb, cbErr) { // 注意 版本<2
			const that = this
			const df = {
				// async:true,  // 默认,异步
				type: "post",
				url,
				data,
				dataType: "json",
				success: cb || function (rsp) { // 异常抛不出
					console.log("rsp", rsp);
				},
				error: cbErr || function (data,type, err) {
					console.log("error", data , type , err);
					that.error()
				},
			};
			return $
				.ajax(options ? Object.assign(df, options) : df)
				//.done(cb || function (rsp) { // 无法同步
				//  console.log("rsp", rsp);
				//})
				//.fail(cbErr || function (data,type, err) {
			// that.alert('请求失败')
				//  console.log("error", data , type , err);
				//});
		},
		open(id = '', closeTip = false, ops) {
			// this.init()
			const opts = {
				closed:false,
				cls: id + '-window',
				onBeforeOpen() {
					//  或者取 $(ul).eq(0) 的值
					let w = $('.'+ id + '-window')
					if(w.length>1){
						for (let i = 0; i < w.length-1; i++) {
							w[i].remove()
						}
					}
				},
				onBeforeClose: closeTip ? function(){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#'+id+ '-window').window('close', true);
							//这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
						}
					});
					return false;
				} : function() {},
				onClose: function(){
				}
			}
			$('#'+id+ '-window').window(ops ? Object.assign(opts,ops) : opts).window('center');
		},
		close(id) {
			// console.log('close')
			$("#" + id + '-window').window("close", true);
		},
	};
	Object.assign(app, {
		plist: '', // >1 , 多项目
		add() {
			const that = this;
			$('#jsf-inviteTemplateId').val('');
			$('#jsf-inviteTemplateName').textbox('clear');
			$('[name="jsf-version"][value="1"]').prop('checked', true);
			$('[name="jsf-version"]').prop('disabled', false);
			that.open('app-set');
		},
		edit() {
			const that = this;
			let rows = $('#app-set-datagrid').datagrid('getSelections');
			if(!rows || rows.length<1) that.error('请选择操作行');
			if(rows && rows.length>1) that.error('仅支持单行操作');
			$('#jsf-inviteTemplateId').val(rows[0].inviteTemplateId || '');
			$('#jsf-inviteTemplateName').textbox('setValue', rows[0].inviteTemplateName || '');
			const version = rows[0].version || 1
			$(`[name="jsf-version"][value="\${version}"]`).prop('checked', true);
			$('[name="jsf-version"]').prop('disabled', true);
			that.open('app-set');
		},
		del() {
			const that = this;
			let rows = $('#app-set-datagrid').datagrid('getSelections');
			if(!rows || rows.length<1) that.error('请选择操作行');
			if(rows && rows.length>1) that.error('仅支持单行操作');
			const tplId = rows[0].inviteTemplateId || '';
			$.messager.confirm('提示', '确定删除<span style="color: red">'+ rows[0].inviteTemplateName +'</span>吗？', function(r){
				if (r){
					const data = {
						inviteTemplateId: tplId,
					}
					that.post(eippath + '/inviteTemplate/deleteById', data, {}, function(data) {
						if(!data) this.error();
						else if(data.state == -1) {
							$.messager.confirm({
								title: '提示',
								msg: `<div style="padding-left: 45px;">邀请函已被展商使用, 无法删除 。</div>`,
								ok: '查看展商',
								cancel: '确定',
								fn: function(r){
									if(r){
										sessionStorage.setItem('tplId', tplId);
										top.addTab('观众邀请渠道','/eip-web-sponsor/backstage/project/channel_details', '', 0,true,true)
									}
								}
							}).window({
								width: '380px',
								cls: 'fix-easyui-warning fix-easyui-button',
								onBeforeClose: function() {
								}
							});
						} else {
							that.checkReturn(data);
							loadTable();
						}
					})
				}
			})
		},
		save() {
			const that = this;
			const data = {
				inviteTemplateId: $('#jsf-inviteTemplateId').val() || '',
				inviteTemplateName: $('#jsf-inviteTemplateName').textbox('getValue'),
				limitProjectIds: $('#jsf-limitProjectIds').combobox('getValues').join(),
				version: $('[name="jsf-version"]:checked').val(),
				exhibitCode,
			}
			that.post(eippath + '/inviteTemplate/save', data, {}, function(data) {
				that.checkReturn(data);
				loadTable();
				that.close('app-set');
			})
		},
		rankSetting() {
			const href = '/eip-web-sponsor/backstage/project/invitation_set_detail?exhibitCode='+exhibitCode+'&projectId=' + projectId;
			top.addTab('展商邀请排行榜设置', href, '', 1, true, true);
		},
		getProjectTreeList() {
			const that = this;
			that.plist = '';
			let plist = '';
			that.post(eippath + '/project/getAllByExhibitCode', {
				exhibitCode
			}, {
				async: false
			}, data => {
				// that.checkReturn(data);
				if( data.constructor  == Array && data.length>1) {
					plist = data;
				}
			})
			if(plist) {
			// if(!true) { // test only
				that.plist = plist;
				$('#jsf-limitProjectIds').combobox('loadData', plist);
				$('.js-pids-only').removeClass('hide')
			} else {
				$('.js-pids-only').addClass('hide')
				$('#jsf-limitProjectIds').combobox('clear');
			}
		},
	})
</script>

<script>
	let url = (new URL(window.location.href)).searchParams;
	var code= exhibitCode = r_exhibitCode= url.get('exhibitCode');
	var projectId = url.get('projectId');

	$(function(){
		app.getProjectTreeList();
		fresh();
	})
	function fresh() {
		loadTable();
		getExhInfo();
	}
	function loadTable(){
		// <th data-options="field:'ck',checkbox:true,width: 80"></th>
		// <!-- <th data-options="field:'projectName',width:220">项目名称</th> -->
		// <th data-options="field:'inviteTemplateName',width: 500">邀请函名称</th>
		// <!-- <th data-options="field:'isMain',width:80">是否主项目</th> -->
		// <th data-options="field:'projectNames',width: 500">限制项目</th>
		// <th data-options="field:'inviteTemplateId',width:300,align:'center'" formatter="regSetBtn">邀请函设置</th>
		// <th data-options="field:'publishFlag',width:200,align:'center'" formatter="regReleaseBtn">发布</th>
		$('#app-set-datagrid').datagrid({
			/* url: eippath + '/project/getList?key=1', */
			// url: eippath + '/project/getAllByExhibitCode',
			url: eippath + '/inviteTemplate/getList',
			columns:[[
				{field:'ck',checkbox:true,width: 80},
				{title: '邀请函名称',field:'inviteTemplateName',width: 500},
				{title: '模板类型',field:'version',width: 500, formatter: val => val === 2 ? '英文模板' : '中文模板'},
				...(app.plist ? [{title: '限制项目',field:'projectNames',width: 500,formatter: val => (val || '无项目限制') }] : []),
				{title: '邀请函设置',field:'inviteTemplateId',width:300,align:'center',formatter: regSetBtn },
				{title: '发布',field:'publishFlag',width:200,align:'center',formatter: regReleaseBtn },
			]],
			queryParams: {
				exhibitCode
      },
			loadFilter: function(data){
				app.checkReturn(data);
				return data.data || [];
			}
		});
	}

	function getExhInfo(callback = null) {
			$.ajax({
				url: eippath + '/exhibition/selectByExhibitCode',
				data: { exhibitCode },
				type: "post",
				dataType: "json",
				// async: false,
				success: function(data) {
					if(!data) return;
					$('#register-info-exihibitName').text(data.exihibitName || '');
					$("#register-info-startDate").text(data.startDate || '')
					$("#register-info-endDate").text(data.endDate || '')
					callback && callback()
				}
			});
	}
	// 发布/撤销发布
	function handleRelease(publishFlag, inviteTemplateId){
		// if(!Ecode || !projectId)
		// 	return $.messager.alert('提示','数据异常，请刷新重试','error')
 		$.ajax({
			url:eippath +"/inviteTemplate/updateInviteTemplateState",
			data: {publishFlag, inviteTemplateId},
			type:"post",
			async:false,  //同步，防止重复提交
			xhrFields:{withCredentials:true},
			success: function(data) {
				const state = +data.state;
				const msg   = data.msg || data.message || '';
				// if(state === -1) {
				// 	$.messager.alert('提示', msg || '项目不存在 !','error');
				// } else if(state === 0) {
				// 	$.messager.alert('提示',msg || '参数缺失 !','error');
				// } else
				if(state === 1) {
					$.messager.alert('提示','操作成功~','info');
					loadTable();
				} else {
					$.messager.alert('提示',msg || '操作失败 !','error');
				}
			},
			error: function(data) {
				$.messager.alert('提示','数据加载失败','error');
			}
		});
	}
	// function get_later_time(t){
	// 	var lw = new Date(t.substring(0, 10));
	// 	var lastY = lw.getFullYear();
	// 	var lastM = lw.getMonth() + 1;
	// 	var lastD = lw.getDate();
	// 	var endDate = lastY+"-"+(lastM<10 ? "0" + lastM : lastM)+"-"+(lastD<10 ? "0"+ lastD : lastD);
	// 	var time=" 17:30:00";
	// 	return endDate + time;
	// }
	// function toUtf8(str) {
	// 		var out, i, len, c;
	// 		out = "";
	// 		len = str.length;
	// 		for (i = 0; i < len; i++) {
	// 				c = str.charCodeAt(i);
	// 				if ((c >= 0x0001) && (c <= 0x007F)) {
	// 						out += str.charAt(i);
	// 				} else if (c > 0x07FF) {
	// 						out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
	// 						out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
	// 						out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
	// 				} else {
	// 						out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
	// 						out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
	// 				}
	// 		}
	// 		return out;
	// }
	// table formater
	function regSetBtn(val, row){
		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegSet(' + row.projectId + ',' + (row.version || '1') + ','+ !!row.publishInvatationFlag +',\''+ row.inviteTemplateId +'\')">设置</a>';
		// + (row.regEnglish ? '<i style="margin-right: 20px"/><a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="openRegSet(' + row.projectId + ',2)">英文版设置</a>' : '')
	}
	// table formater
	function regReleaseBtn(val, row){
		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="handleRelease(\''+ Boolean(!val) +'\',\'' + row.inviteTemplateId + '\')">'+ (val ? '撤销' : '发布') +'</a>';
	}
	// table formater
	function openRegSet(projectId, version, isPublish, id) {
		const href = '/eip-web-sponsor/backstage/project/invitation_set_detail?exhibitCode='+exhibitCode + '&sectionCode=-1&publish=' + (+isPublish) +'&id='+ id + '&version=' + version;
		top.addTab('邀请函模板设置', href, '', 1, true, true)
		// window.location.href =  href;
		// var regSet = variableReg +
    //     '/web-reg-server/pc/register-settings.html?EID=' + code +
    //     '&orgnum=' + orgNum + '&target=1&pid='+ projectId+ '&version=' + version
		// window.open(regSet,'_blank');
	}
</script>
</body>
</html>