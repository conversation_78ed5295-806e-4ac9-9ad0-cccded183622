<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<link rel="stylesheet" href="${eippath}/css/view/alex-form.css?v=1.0">

<div class="datagrid-toolbar" id="exhibit-hotel-set-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:reflush()" class="easyui-linkbutton" iconCls="icon-reload" plain="true">刷新</a>
    <a href="javascript:exhibit_hotel_set_add()" class="easyui-linkbutton" iconCls="icon-add" plain="true">新增</a>
    <a href="javascript:exhibit_hotel_set_del()" class="easyui-linkbutton" iconCls="icon-remove" plain="true">删除</a>
  </div>
</div>
<table
    id="exhibit-hotel-set-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               	if(i != rowIndex)$(this).datagrid('unselectRow', i);
              }
 				},"
    toolbar="#exhibit-hotel-set-toolbar"
>
  <thead>
  <tr>
    <th data-options="field:'ck',checkbox:true"></th>
    <th field="hotelName" width="20%">酒店名称</th>
    <th field="hotelNameEn" width="20%">酒店英文名称</th>
    <th field="hotelGradeName" width="20%">酒店等级</th>
    <th field="hotelAddress" width="30%">酒店地址</th>
    <th field="exhibitHotelId" width="120px"
        formatter="v => `<a href='javascript:exhibit_hotel_set_edit(\`\${v}\`)' style='color:blue;font-weight:bold'>修改</a>`">
      操作
    </th>
  </tr>
  </thead>
</table>

<div id="exhibit-hotel-set-window" class="easyui-window" toolbar="#exhibit-hotel-set-window-toolbar"
     data-options="closed:true,title:'基本信息设置',modal:true,cls:'__exhibit-hotel-set-window'"
     style="width:680px;height:auto;">
  <!-- begin of toolbar -->
  <div id="exhibit-hotel-set-window-toolbar">
    <div class="my-toolbar-button" style="text-align:right">
      <a href="javascript:exhibit_hotel_set_save()" class="easyui-linkbutton" iconCls="icon-save" plain="true">保存</a>
      <a href="javascript:$('#exhibit-hotel-set-window').window('close')" class="easyui-linkbutton" iconCls="icon-cancel"
         plain="true">取消</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <div class="easyui-panel" title="基本信息设置" style="width: 100%;padding: 10px">
    <form class="alex-form" id="exhibit-hotel-set-editor-form">
      <input type="hidden" id="exhibit-hotel-set-editor-exhibitHotelId">
      <div class="alex-form-item">
        <input
            class="easyui-textbox"
            id="exhibit-hotel-set-editor-hotelName"
            style="width: 300px"
            data-options="
             prompt:'酒店名称',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'<span class=require-flag>*</span>酒店名称'"/>
      </div>
      <div class="alex-form-item">
        <input
            class="easyui-textbox"
            id="exhibit-hotel-set-editor-hotelNameEn"
            style="width: 300px"
            data-options="
             prompt:'酒店英文名称',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'酒店英文名称'"/>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-combobox"
            id="exhibit-hotel-set-editor-hotelGradeId"
            style="width: 300px"
            data-options="
             prompt:'酒店等级',
             url: '/eip-web-sponsor/itemCode/selectByItemKindCodeElse?itemKindCode=hotel_grade',
             labelWidth: '100px',
             labelAlign: 'right',
             panelHeight: 'auto',
             panelMaxHeight: '200px',
             method: 'POST',
             valueField: 'id',
             textField: 'text',
             label:'酒店等级'"/>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-textbox"
            id="exhibit-hotel-set-editor-hotelAddress"
            style="width: 620px"
            data-options="
             prompt:'酒店地址',
             labelWidth: '100px',
             labelAlign: 'right',
             label:'酒店地址'"/>
      </div>
    </form>
  </div>
</div>

<script type="text/javascript">
  $(function () {
    setTimeout(() => {
      exhibit_hotel_set_load_table()
    }, 1e2)
  });

  function exhibit_hotel_set_load_table() {
    $('#exhibit-hotel-set-datagrid').datagrid({
      url: '/eip-web-sponsor/exhibitHotel/getPage',
      queryParams: {
        exhibitCode: exhibitCode_for_pass
      }
    })
  }

  function exhibit_hotel_set_save() {
    const post = {}
    exhibit_hotel_set_form_each((key, result) => post[key] = result.type === 'val' ? result.control() : result.control('getValue'))
    const $window = $('#exhibit-hotel-set-window')
    if (!post.hotelName) return $.messager.alert('提示', '请填写必填项！', 'warning')
    _request(post)

    function _request(post, url) {
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: url || '/eip-web-sponsor/exhibitHotel/save',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass,
          ...post,
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '保存失败！', 'error')
          $.messager.alert('提示', '保存成功！', 'info')
          $window.window('close')
          exhibit_hotel_set_load_table()
        }
      }).done(() => $.messager.progress('close'))
    }
  }

  function exhibit_hotel_set_add() {
    // clear form control
    exhibit_hotel_set_form_each((key, result) => result.control(result.type === 'val' ? '' : 'clear'))
    $('#exhibit-hotel-set-window').window({title: '新增酒店服务'}).window('open')
  }

  function exhibit_hotel_set_edit(exhibitHotelId) {
    const currentRow = $('#exhibit-hotel-set-datagrid').datagrid('getRows').find(item => item.exhibitHotelId === exhibitHotelId)
    if (!currentRow) return null
    exhibit_hotel_set_form_each((key, result) => {
      return result.type === 'val' ?
        result.control(currentRow[key]) :
        result.control('setValue', currentRow[key])
    })
    $('#exhibit-hotel-set-window').window({title: '修改酒店服务'}).window('open')
  }

  function exhibit_hotel_set_form_each(callback, formTypes, prefix) {
    prefix = prefix || '#exhibit-hotel-set-editor-'
    formTypes = formTypes || {
      exhibitHotelId: 'val',
      hotelName: 'textbox',
      hotelNameEn: 'textbox',
      hotelGradeId: 'combobox',
      hotelAddress: 'textbox',
    }
    Object.keys(formTypes).forEach(key => {
      const result = {
        formType: formTypes,
        type: formTypes[key],
        $el: $(`\${prefix}\${key}`.replace(/\s/g, '')),
      }
      result.control = (...args) => result.$el[result.type](...args)
      callback(key, result)
    })
  }

  function exhibit_hotel_set_del() {
    const rows = $('#exhibit-hotel-set-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请选择需要删除的数据', 'warning')
    $.messager.confirm('提示', `确定删除选择的\${rows.length}条数据吗？`, r => r && _request())

    function _request() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/exhibitHotel/batchDelete',
        dataType: "json",
        data: {
          exhibitHotelIds: rows.map(item => item.exhibitHotelId).toString()
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '删除失败！', 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          exhibit_hotel_set_load_table()
        }
      })
    }
  }
</script>
