<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_draw = false;
	boolean fu_booth_material_set_reserved_lock = false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		if(s.equals("fu_booth_material_set_reserved_lock"))fu_booth_material_set_reserved_lock = true;
	}
%>
<style>
	.project-boothdata-toolbar-top label{
		margin-left:15px;
		margin-right:5px;
	}
	.channelButtonStyle{
		color: #fff;
		display: block;
		width: 146px;
		height: 32px;
		background-color: #4fa0ff;
		border-radius: 6px;
		text-align: center;
		line-height: 32px;
		margin: 5px auto 15px;
		font-size: 14px;
		cursor: pointer;
	}
	.hide {
		display: none !important;
	}


	.project-boothdata-datagrid {
    height: calc(100vh - 307px) !important;
	}
	.my-toolbar-search .hide{
		display: none !important;
	}
	/* .project-boothdata-datagrid .datagrid-body .sumTable td{ */
	#project-boothdata-total .sumTable td{
    border-width: 1px 1px 0 0;height: 40px;
	}
	.project-boothdata-datagrid .datagrid-body{
			position: relative;
	}
</style>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'north',border:true" style="height:auto;overflow: hidden;">
		<!-- begin of toolbar -->
        <div id="project-boothdata-toolbar" class="datagrid-toolbar" style="height: 100%;">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="project_boothdata_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothdata_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="project_boothdata_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_boothdata_del_batch()" plain="true">删除</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="project_boothdata_view()" plain="true">查看</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-menu13" onclick="project_boothdata_add_batch()" plain="true">批量新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="project_boothdata_import()"
				   plain="true">导入</a><input type="file" onchange="project_boothdata_importf(this)" id="project_boothdata_choiseexcel" hidden="hidden" />
				<a href="javascript:;" class="easyui-linkbutton" iconCls="icon-download-all" plain="true" onclick="project_boothdata_export()">导出</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-tool-batch-edit" onclick="projectBoothdata_sectionEditBatch.open()" plain="true">批量修改展馆展区</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-tool-batch-edit" onclick="project_boothdata_edit_productTypeId_batch()" plain="true">批量修改展品范围</a>
				<%
					if(fu_booth_material_set_reserved_lock) {
						out.println("<a href='javascript:void(0);' class='easyui-linkbutton' iconCls='icon-tool-lock' onclick='project_boothdata_lockbooth()' plain='true'>预留锁定</a>");
						out.println("<a href='javascript:void(0);' class='easyui-linkbutton' iconCls='icon-tool-unlock1' onclick='project_boothdata_unlockbooth()' plain='true'>取消预留锁定</a>");
					}
				%>
				<div class="project-boothdata-toolbar-top">
					<style>
						.project-boothdata-tool-bottom-sectionCode .textbox-focused{
							box-shadow: none;
						}
						.project-boothdata-tool-bottom-sectionCode .textbox{
							border: none;
						}
						.project-boothdata-toolbar-top label{
							margin-left: 3px !important;
						}
					</style>
					<style>
						#project-boothdata-subpro+.combo{
							margin-right: 0 !important;
						}
						.hide {
							display: none !important;
						}
					</style>
					<div style="display: inline-block;margin-bottom: 4px;">
						<label>项目</label>
						<div style="position: relative;display: inline-block;margin-right: 10px;">
							<select id="project-boothdata-subpro" class="easyui-combotree" style="width: 220px;height:25px;"></select>
							<a href="javascript:;" onclick="$('#project-boothdata-subpro').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
						</div>
					</div>
					<div style="display: inline-block;margin-bottom: 4px;">
						<label style="margin-left: 6px !important;margin-bottom: -3px !important">展区</label>
						<div style="position: relative;display: inline-block;margin-right: 10px;">
						<input id="project-boothdata-tool-bottom-sectionCode" class="easyui-combotree" style="width:200px;height: 26px;"
						data-options="valueField: 'sectionCode',textField: 'sectionName'
							,panelHeight: 'auto',panelMaxHeight:500,cls:'project-boothdata-tool-bottom-sectionCode'
							,editable:true,limitToList:true">
							<a href="javascript:;" onclick="$('#project-boothdata-tool-bottom-sectionCode').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
						</div>
					</div>
					<label style="margin-left: 6px !important;margin-bottom: -3px !important">展位号</label>
					<input id="project-boothdata-tool-bottom-boothCode" class="easyui-textbox" style="width:110px;height: 26px;">
					<label style="margin-right:5px !important">&nbsp;展位类型</label>
					<input data-options="limitToList:true,panelHeight:'auto',panelMaxHeight:300,valueField: 'id',textField: 'text'" id="project-boothdata-tool-bottom-typeCode" class="easyui-combobox" style="width:110px;height: 26px;">
					<label>&nbsp;展位面积㎡</label>
					<input id="project-boothdata-tool-bottom-boothArea-from" class="easyui-numberbox" style="width:60px;height: 26px;">
					<label style="margin-left: 5px!important;">至</label>
					<input id="project-boothdata-tool-bottom-boothArea-to" class="easyui-numberbox" style="width:60px;height: 26px;">
					<label>&nbsp;开口</label>
					<input data-options="limitToList:true,panelHeight:'auto'" id="project-boothdata-tool-bottom-openInfoId" class="easyui-combobox" style="width:100px;height: 26px;">
					<label>&nbsp;展品范围</label>
					<input data-options="limitToList:true,panelHeight:'auto'" id="project-boothdata-tool-bottom-productTypeId" class="easyui-combobox" style="width:120px;height: 26px;">
					<label>&nbsp;预留锁定</label>
					<input data-options="limitToList:true,panelHeight:'auto'" id="project-boothdata-tool-bottom-reserveLock" class="easyui-combobox" style="width:100px;height: 26px;">
					<label>&nbsp;展位图</label>
					<input data-options="limitToList:true,valueField: 'id',textField: 'text',panelHeight:'auto',data: [{id: '',text: '所有'},{id: 1,text: '未关联'},{id: 2,text: '已关联'}]" id="project-boothdata-tool-bottom-boothMapType" class="easyui-combobox" style="width:100px;height: 26px;">
					<label>&nbsp;状态</label>
					<!-- ,panelMaxHeight:200 -->
					<input data-options="limitToList:true,valueField: 'saleState',textField: 'stateName',panelHeight:'auto'" id="project-boothdata-tool-bottom-saleState" class="easyui-combobox" style="width:100px;height: 26px;">
					<a href="javascript:void(0);" style="margin-left: 15px" id="project-boothdata-tool-bottom-search" class="easyui-linkbutton" onclick="project_boothdata_tool_bottom_search()">查询</a>
				</div>
            </div>
        </div>
<%--		<div id="project-boothdata-tool-bottom">--%>
<%--			<label>展位号</label>--%>
<%--			<input id="project-boothdata-tool-bottom-boothId" style="width:200px">--%>
<%--		</div>--%>
        <!-- end of toolbar -->
    </div>
    <div data-options="region:'center',border:false">
    	<div class="easyui-layout" data-options="fit:true">
			<!-- <div data-options="region:'west',split:true" style="width:260px;padding:10px;">
				<ul id="project-boothdata-tree" class="easyui-tree" data-options="method:'post',animate:true"></ul>
			</div> -->
		   	<div data-options="region:'center'">
		   		<table id="project-boothdata-datagrid" class="easyui-datagrid"></table>
					<div id="project-boothdata-total" style="height: 40px;line-height: 40px;"></div>
					<div id="project-boothdata-pager" class="easyui-pagination" style="background:#efefef;border:1px solid #ccc;"></div>
		   	</div>
		</div>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="project-boothdata-window" class="easyui-window" toolbar="#project-boothdata-window-toolbar" data-options="closed:true,title:'展位内容',modal:true" style="overflow:hidden;width:750px;height:auto;">
	<!-- begin of toolbar -->
	<div id="project-boothdata-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothdata_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" hint="mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="project_boothdata_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_boothdata_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="project_boothdata_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothdata_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="project-boothdata-panel-1" class="easyui-panel" title="基本信息" style="overflow:hidden;width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
		<div>
			<li style="width:665px;margin-right:15px;height:25px;margin-bottom:7px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展馆展区</label></div>
					<input style="width:538px; height:25px"
					id="project-boothdata-set-sectionCode" class="easyui-combotree"
					data-options="valueField: 'sectionCode',textField: 'sectionName'
									,panelHeight: 'auto',panelMaxHeight:500,cls:'project-boothdata-set-sectionCode'
							,editable:true,limitToList:true" >
			</li>
		</div>
		<div style="list-style:none;margin:0;padding:0;
			display: grid;grid-template-columns: 350px 300px;grid-gap: 5px 15px;margin-bottom:7px;">
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位号</label></div>
				<input id="project-boothdata-textbox-boothCode" hint="boothCode" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
				<div style="display: inline-block;" id="js_project_boothdata_edit_boothCode">
					<a href="javascript:void(0)" title="变更展位号"
						 data-options="position: 'top'"
						 onclick="project_boothdata_edit_boothCode()"
						 style="cursor: pointer;"
						 class="easyui-tooltip"> <span class="icon-tool-batch-edit" style="width: 30px;display: inline-block;">&nbsp;</span></a>
				</div>
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位类型</label></div>
				<input id="project-boothdata-combobox-typeCode" hint="typeCode" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位长度</label></div>
				<input id="project-boothdata-numberbox-specLength" hint="specLength" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位宽度</label></div>
				<input id="project-boothdata-numberbox-specWidth" hint="specWidth" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位规格</label></div>
				<input id="project-boothdata-textbox-boothSpec" hint="boothSpec" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位面积</label></div>
				<input id="project-boothdata-numberbox-boothArea" hint="boothArea" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>开口情况</label></div>
				<input id="project-boothdata-numberbox-openInfoName" hint="openInfoId"  class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
			</li>
			<!-- <li style="height:25px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>双开口</label></div>
        		<input id="project-boothdata-numberbox-twoOpen" hint="twoOpen" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li>
        	<li style="height:25px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>三开口</label></div>
        		<input id="project-boothdata-numberbox-threeOpen" hint="threeOpen" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li> -->
      <li style="height:25px;position: relative;">
				<div style="width:120px;height:25px;display:inline-block;"><label>售价</label></div>
				<input id="project-boothdata-numberbox-salePrice" hint="salePrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
				<a href="#" title="价格说明"
					onclick="project_boothdata_price_detail(this)" " id="js-salePrice-detail"
				style="color: white;border-radius: 50%;background-color: #aaa;width: 16px;height: 16px;font-size: 12px;display: inline-block;position: absolute;top:4px;right: 5px;text-align: center;">!</a>
				<!-- data-options="position: 'top'" class="easyui-tooltip">!</a> -->
			</li>
			<li class="js-enableForeignCurrency hide" style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>外币售价</label></div>
				<input id="project-boothdata-numberbox-foreignPrice" hint="foreignPrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li class="js-enableForeignCurrency hide" style="height:25px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>外币币种</label></div>
				<input id="project-boothdata-numberbox-currencyName" hint="currencyName" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
			</li>
		</div>
		<div style="list-style:none;margin:0;padding:0;">
			<li style="width:100%;margin-right:15px;margin-bottom:5px;">
				<div style="display: flex;">
					<div style="width:123px;height:60px;display:inline-block;"><label>备注</label></div>
					<input hint="memo" class="easyui-textbox" control="textbox" tag="0"  data-options="multiline: true" style="width:540px;height: 60px;">
				</div>
			</li>
			<li style="width:100%;height:25px;margin-right:15px;margin-bottom:5px;">
				<div style="display: flex;">
					<div style="width:123px;height:25px;display: inline-block"><label>展品范围</label></div>
					<input data-options="editable:false,panelHeight:'auto'" hint="productTypeId" id="project-boothdata-panel-1-combobox-productTypeId" class="easyui-combobox" control="combobox" tag="0" style="width:540px; height:25px;">
				</div>
			</li>
    </div>
		</ul>
    </div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="project-boothdata-window-b" class="easyui-window" toolbar="#project-boothdata-window-toolbar-b" data-options="closed:true,title:'展位内容',modal:true" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="project-boothdata-window-toolbar-b">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="project_boothdata_save_b()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothdata_cancel_b()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="project-boothdata-panel-1-b" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:615px;margin-right:15px;height:25px;float:left;margin-bottom:7px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展馆展区</label></div>
					<input style="width:488px; height:25px"
					id="project-boothdata-setBatch-sectionCode" class="easyui-combotree"
					data-options="valueField: 'sectionCode',textField: 'sectionName'
									,panelHeight: 'auto',panelMaxHeight:500,cls:'project-boothdata-setBatch-sectionCode'
							,editable:true,limitToList:true" >
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位前缀</label></div>
				<input id="project-boothdata-textbox-pre" hint="pre" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>后缀起始序号</label></div>
				<input id="project-boothdata-textbox-las1" hint="las1" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>后缀终止序号</label></div>
				<input id="project-boothdata-textbox-las2" hint="las2" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位类型</label></div>
				<input id="project-boothdata-combobox-typeCode-b" hint="typeCode" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位长度</label></div>
				<input id="project-boothdata-numberbox-specLength-b" hint="specLength" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位宽度</label></div>
				<input id="project-boothdata-numberbox-specWidth-b" hint="specWidth" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位规格</label></div>
				<input id="project-boothdata-textbox-boothSpec-b" hint="boothSpec" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位面积</label></div>
				<input id="project-boothdata-numberbox-boothArea-b" hint="boothArea" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>开口情况</label></div>
				<input id="project-boothdata-numberbox-openInfoName-b" hint="openInfoId"  class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
			</li>
			<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>双开口</label></div>
        		<input id="project-boothdata-numberbox-twoOpen-b" hint="twoOpen" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>三开口</label></div>
        		<input id="project-boothdata-numberbox-threeOpen-b" hint="threeOpen" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li> -->
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;position: relative;">
				<div style="width:120px;height:25px;display:inline-block;"><label>售价</label></div>
				<input id="project-boothdata-numberbox-salePrice-b" hint="salePrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
				<a href="#" title="价格说明"
				 onclick="project_boothdata_price_detail(this)" id="js-salePrice-detail-b"
				style="color: white;border-radius: 50%;background-color: #aaa;width: 16px;height: 16px;font-size: 12px;display: inline-block;position: absolute;top:4px;right: 5px;text-align: center;">!</a>
				<!-- data-options="position: 'top'" class="easyui-tooltip">!</a> -->
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>外币售价</label></div>
				<input id="project-boothdata-numberbox-foreignPrice-b" hint="foreignPrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>外币币种</label></div>
				<input id="project-boothdata-numberbox-currencyName-b" hint="currencyName" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
				<input hint="memo" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
        </ul>
    </div>
</div>
<div id="project-boothdata-window-c" class="easyui-window" toolbar="#project-boothdata-window-toolbar-c" data-options="closed:true,title:'批量修改展品范围',modal:true" style="width:500px;height:auto;">
	<div style="width:100%;display:flex;flex-direction:row;border-bottom: 1px solid lightgray;">
		<h4 style="width:330px;font-weight:normal;font-size:14px;color:#f74444">当前选择的展位的数量为：<span id="project_count"></span></h4>
		<a  style="margin-bottom:14px;margin-top: 14px; text-align: center" href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save"   value="保存" onclick="project_boothdata_productTypeId_save()" plain="true">保存</a>
		<a  style="margin-bottom:14px;margin-top: 14px;text-align: center" href="javascript:void(0);" class ="easyui-linkbutton"  iconCls="icon-cancel"  value="取消"  onclick="project_boothdata_productTypeId_cancel()" plain = "true">取消</a>
	</div>
	<div class="window-c-down" style="width:100%;height:50px;margin-top:20px;">
		<label style="margin-left:30px;margin-right:10px;width:90px">展品范围</label>
		<input style="margin-left:140px ;margin-top:20px;margin-bottom:20px;width:350px; height:auto" id="project_boothdata_productTypeId"  data-options="editable:false,panelHeight:'auto'" class="easyui-combobox">
	</div>
</div>

<!-- 批量修改展馆展区 -->
<%@ include file="../common/project_boothdata_window_sectionEditBatch.jsp" %>

<!-- end of easyui-window -->

<div id="project-boothdata-window-priceDetail" class="easyui-window"
	data-options="closed:true,title:'价格说明'" style="width: 680px;height:auto;">
	<div class="easyui-panel" style="padding: 6px;">
		价格详细算法
	</div>
	<div style="padding: 15px;border-radius: 5px;margin: 25px;
		background-color: #f3f3f3;border: 1px solid #eee;color: gray;
		display: grid;grid-template-columns: 100px 1fr 100px 1fr;grid-gap: 5px 0;">
		<div>展位类型:</div>
		<div id="project-boothdata-priceDetail-boothType"></div>
		<div></div>
		<div></div>

		<div>定价方式:</div>
		<div id="project-boothdata-priceDetail-priceType"></div>
		<div>单价:</div>
		<div id="project-boothdata-priceDetail-price"></div>

		<div>开口加价算法</div>
		<div id="project-boothdata-priceDetail-openType"></div>
		<div>开口加价</div>
		<div id="project-boothdata-priceDetail-openPrice"></div>

		<div class="js-enableForeignCurrency hide">外币单价</div>
		<div class="js-enableForeignCurrency hide" id="project-boothdata-priceDetail-foreignPrice"></div>
		<div class="js-enableForeignCurrency hide">外币开口加价</div>
		<div class="js-enableForeignCurrency hide" id="project-boothdata-priceDetail-foreignOpenPrice"></div>
	</div>
</div>
<script>

function project_boothdata_edit_boothCode() {
	// ? readonly
	// if(!$('#project-boothdata-textbox-boothCode').textbox('options').disabled) {
	// 	$.messager.alert('提醒', '当前展位号可编辑呢 !', 'warning');
	// 	return;
	// }
	$.messager.confirm("提示", `该展位已与业务产生关联, <br > 变更展位号将更新相关业务上的展位信息, 是否确定?`, function (r) {
		if (r) {
			$('#project-boothdata-textbox-boothCode').textbox({'disabled': false})
		}
  }).window({ width: 420});
}

	// 子项目
	setTimeout(() => {
		loadProjectBoothdataSubpro();
	}, 0);
	function loadProjectBoothdataSubpro(){
		const $subProjectTree = $('#project-boothdata-subpro')
		$subProjectTree.combotree({
			url: eippath + '/exhibition/selectAllProject?exhibitCode=' + exhibitCode_for_pass,
			loadFilter(data) {
				let tmp = data.data || [];
				if(!tmp || tmp.length < 2){
					$('#project-boothdata-subpro').parent().parent().addClass('hide');
				}
				return tmp.map(it=> ({
					id: it.projectId,
					text: it.projectName,
				}))
			},
			onLoadError(e) {
				$.messager.alert('子项目加载失败！')
			},
		})
	}
function project_boothdata_price_detail(obj) {
	let b = '';
	if(obj.id == 'js-salePrice-detail-b') {
		b = '-b'
	}
	const $obj = $('#project-boothdata-combobox-typeCode' + b);
	let typeText = $obj.combobox('getText');
	let typeCode = $obj.combobox('getValue');
	const pos = $obj.closest('.window')[0].getBoundingClientRect();

	// project_boothdata_foreignCurrency = data.foreignCurrency;
	// project_boothdata_priceWay = data.priceWay;
	// project_boothdata_price = data.price;
	// project_boothdata_openPrice = data.openPrice;
	// project_boothdata_foreignPrice = data.foreignPrice;
	// project_boothdata_foreignOpenPrice = data.foreignOpenPrice;
	// project_boothdata_openAlgorithmType 	 = data.openAlgorithmType;
	$('#project-boothdata-priceDetail-boothType').text(typeText)
	$('#project-boothdata-priceDetail-priceType').text(['按个','按面积'][+project_boothdata_priceWay] || '')
	$('#project-boothdata-priceDetail-price').text(project_boothdata_price)
	var type = +project_boothdata_openAlgorithmType;
	$('#project-boothdata-priceDetail-openType').text(['','按开口数加价','按开口数及面积加价','按开口长边加价','按开口情况加价'][type] || '');
	if(type == 4) {
		var openType = +$('#project-boothdata-numberbox-openInfoName' + b).combobox('getValue');
		var openTypeKey  = ['twoOpenInfoPrice','threeOpenInfoPrice','fourOpenInfoPrice'];
		var openTypeKey2 = ['twoOpenInfoForeignPrice','threeOpenInfoForeignPrice','fourOpenInfoForeignPrice'];
		project_boothdata_openPrice = project_boothdata_typeDetail[openTypeKey[openType - 2]] || '0';
		project_boothdata_foreignOpenPrice = project_boothdata_typeDetail[openTypeKey2[openType - 2]] || '0';
	}
	const pre = ['','开口数X', '开口数X展位面积X', '最大边长X'][type] || '';
	const after = ['','/个', '/M²', ''][type] || '';
	$('#project-boothdata-priceDetail-openPrice').text(pre + project_boothdata_openPrice + after)
	$('#project-boothdata-priceDetail-foreignPrice').text(project_boothdata_foreignPrice + project_boothdata_foreignCurrency)
	$('#project-boothdata-priceDetail-foreignOpenPrice').text(pre + project_boothdata_foreignOpenPrice + project_boothdata_foreignCurrency + after)

	const wid = 'project-boothdata-window-priceDetail';
	$('#' + wid).window({
		closed: false,
		cls: wid,
		onBeforeOpen() {
			let w = $('.' + wid)
			if(w.length>1){
				for (let i = 0; i < w.length-1; i++) {
					w[i].remove()
				}
			}
		},
		onOpen() {
			$('#' + wid).closest('.window')
			.css('left', pos.left + pos.width - 100)
			.css('top', pos.top + 100);
		},
	});
}
</script>

<!-- 拼展详情 -->
<div id="project-boothdata-union-window" data-options="closed:true,title:'拼展展位',modal:true,maximizable: false,resizable: false,minimizable: false" style="width:600px;display: none;">
	<div style="display: flex;padding: 10px;align-items: center;">
		<div style="width:60px">展位号</div>
		<div>
			<input id="project-boothdata-union-boothCode" class="easyui-textbox"
				data-options="disabled: true,height: 25" style="width:200px;" />
		</div>
	</div>
	<div style="padding: 0 10px 10px;height: 350px;">
		<table id="project-boothdata-union-datagrid" style="height: 350px;">
			<thead>
				<tr>
					<th data-options="field:'companyName',width:280">拼展公司</th>
					<th data-options="field:'masterSlaveBooth',width:100,formatter: project_boothdata_fmt_isunion2">拼展主从</th>
					<th data-options="field:'saleState',width:180,formatter: saleState_type">当前展位状态</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
<script>
	function project_boothdata_detail_union(boothId = '', boothCode = '') {
		const id = 'project-boothdata-union';
		$('#'+ id+ '-boothCode').textbox('setValue', boothCode || '');
		$('#'+ id+ '-window').window({
			closed:false,
      cls: id + '-window',
      onBeforeOpen() {
        let w = $('.'+ id + '-window')
        if(w.length>1){
          for (let i = 0; i < w.length-1; i++) {
            w[i].remove()
          }
        }
      },
		}).window('center');
		$('#project-boothdata-union-datagrid').datagrid({
			fit:true,fitColumns:true,singleSelect:true,data: [],
		})
		$.ajax({
			url: eippath + '/exhibitBooth/getJointBoothInfo',
			dataType: "json",
			type: "post",
			data: {
				boothId,
			},
			async: false,
			success(data){
				if(!data) return $.messager.alert('提示','请求失败！','error');
				if(data.state !==1) return $.messager.alert('提示',data.msg || data.message || '请求失败！','error');
				$('#project-boothdata-union-datagrid').datagrid('loadData', data.data || []);
			},
			error(){ $.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothdata_fmt_isunion2(value,row,index){
		return value ? '主' : '从';
	}
	function project_boothdata_fmt_isunion(value,row,index){
		if(!value) return '';
		return '<a style="color: #3232d3" href="javascript:project_boothdata_detail_union(\''+ row.boothId + '\',\''+ row.boothCode + '\')">是</a>';
	}
</script>

<!-- 导入 -->
<div id="project-boothdata-window-import" data-options="closed:true,title:'导入',modal:true,maximizable: false,resizable: false,minimizable: false" style="width:300px;display: none;">
	<div  class="easyui-panel"  style="width:100%;height:160px;padding:10px;">
		<a class="channelButtonStyle" style='margin-top: 30px;color: white;' href="/eip-web-sponsor/document/展位资料导入模板.xlsx" download="展位资料导入模板">下载导入模板</a>
		<span class="channelButtonStyle" onclick='project_boothdata_import2()'>导入</span>
	</div>
</div>


<script type="text/javascript">
	var project_boothdata_edit_mode;
	var project_boothdata_edit_row;
	var project_boothdata_button_edit_mode;
	var project_boothdata_alocate_id = -1;
	var project_boothdata_window_close_set = false;
	var project_boothdata_save_id;
	var project_boothdata_sectionCode; // 已废弃
	var project_boothdata_foreignCurrency;
	var project_boothdata_typeDetail = {};
	var project_boothdata_priceWay;
	var project_boothdata_price=0;
	var project_boothdata_openPrice= 0;
	var project_boothdata_foreignPrice=0;
	var project_boothdata_foreignOpenPrice=0;
	var project_boothdata_enableStagePrice=false; // 是否时段调价
	var project_boothdata_openAlgorithmType; 	    // 开口加价算法
	var project_boothdata_stagePriceList; 		    // 时段调价策略 列表 后端处理为 命中的第一条
	var project_boothdata_stagePriceListDetail;   // 时段调价策略 命中详情
	$(function(){
		setTimeout(function(){
			project_boothdata_fresh();
			project_boothdata_change();
			project_boothdata_resetCombobox();
		}, 100);
	});
	var project_boothdata_typeData = [];
	var project_boothdata_enableForeignCurrency = false;
	function project_boothdata_ifEnableCurrency() {
		$.ajax({
			url: eippath + '/sysSettingOrg/getValue',
			dataType: "json",
			type: "post",
			data: {
				settingCode: 'enable_foreign_currency',
				orgNum: top.org_num_for_pass,
			},
			async: false,
			success(data){
				// if(data.state !==1) return $.messager.alert('提示',data.msg || data.message || '请求失败！','error');
				if(data && data.data && data.data.settingValue == 'true'){
					project_boothdata_enableForeignCurrency = data.data.settingValue == 'true';
					$('.js-enableForeignCurrency').removeClass('hide');
				}
			},
			error(){ $.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothdata_resetCombobox(ifMod = false) {
		project_boothdata_typeCode_fresh();
		$("#project-boothdata-combobox-typeCode").combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: 'auto',
			limitToList: true,
			onChange: function(n,o){
				$.ajax({
					url: eippath + '/boothType/selectByTypeCode',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {typeCode: n},	//发送数据
					async: false,
					success: function(data){
						console.log('类型r')
						if(!data) return;
						project_boothdata_afterGetType(data);
						$("#project-boothdata-numberbox-currencyName").textbox("setValue", data.currencyName);
						var l,w;
						// if(ifMod){
						// 	l = $("#project-boothdata-numberbox-specLength").textbox("getValue"),
						// 	w = $("#project-boothdata-numberbox-specWidth").textbox("getValue");
						// }else{
							l = data.specLength;
							w = data.specWidth;
							$("#project-boothdata-numberbox-specLength").textbox("setValue", l);
							$("#project-boothdata-numberbox-specWidth").textbox("setValue", w);
						// }
						project_boothdata_changePrice(l,w,$("#project-boothdata-numberbox-openInfoName").combobox('getValue'));
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		}).combobox('loadData',project_boothdata_typeData);
		$("#project-boothdata-combobox-typeCode-b").combobox({
			valueField: 'id',
			textField: 'text',
			limitToList: true,
			panelHeight: 'auto',
			onChange: function(n, o){
				$.ajax({
					url: eippath + '/boothType/selectByTypeCode',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {typeCode: n},	//发送数据
					async: false,
					success: function(data){
						console.log('类型rb')
						if(!data) return;
						project_boothdata_afterGetType(data);
						$("#project-boothdata-numberbox-currencyName-b").textbox("setValue", data.currencyName);
						var l,w;
						// if(ifMod){
						// 	l = $("#project-boothdata-numberbox-specLength-b").textbox("getValue"),
						// 	w = $("#project-boothdata-numberbox-specWidth-b").textbox("getValue");
						// }else{
							l = data.specLength;
							w = data.specWidth;
							$("#project-boothdata-numberbox-specLength-b").textbox("setValue", l);
							$("#project-boothdata-numberbox-specWidth-b").textbox("setValue", w);
						// }
						project_boothdata_changePrice(l,w,$("#project-boothdata-numberbox-openInfoName-b").combobox('getValue'));
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		}).combobox('loadData',project_boothdata_typeData);
		$("#project-boothdata-numberbox-openInfoName").combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: 'auto',
			limitToList:true,
			url: '${eippath}/itemCode/selectByItemKindCodeElse?itemKindCode=open_info&projectId=' + getQueryString("projectId"),
			onChange: function(n,o){
				console.log('开口r',n, o);
				project_boothdata_changeOpen(n)
			}
		});
	}
	// 查看/修改/添加 查询展位类型后
	function project_boothdata_afterGetType(data) {
		data = data || {};
		project_boothdata_typeDetail = data;
		project_boothdata_foreignCurrency = data.foreignCurrency || '';
		project_boothdata_priceWay = data.priceWay || '';
		project_boothdata_price = data.price || 0;
		project_boothdata_openPrice = data.openPrice || 0;
		project_boothdata_foreignPrice = data.foreignPrice || 0;
		project_boothdata_foreignOpenPrice = data.foreignOpenPrice || 0;
		//
		project_boothdata_enableStagePrice  	 = data.enableStagePrice || 0;
		project_boothdata_openAlgorithmType 	 = data.openAlgorithmType || '';
		project_boothdata_stagePriceList    	 = data.stagePriceList || [];
		if(project_boothdata_enableStagePrice && project_boothdata_stagePriceList){ // 启用了时段调价
			// 查询今天命中的 调价策略 :
			if(project_boothdata_stagePriceList.length){
				project_boothdata_stagePriceListDetail = project_boothdata_stagePriceList[0]
			}
		}
	}
	// function loadProjectBoothType(){
	// 	$("#project-boothdata-combobox-typeCode").combobox({
	// 		valueField: 'id',
	// 		textField: 'text',
	// 		panelHeight: 'auto',
	// 		url: '${eippath}/boothType/getAll?exhibitCode=' + exhibitCode_for_pass,
	// 		onSelect: function(rec){
	// 			$.ajax({
	// 				url: eippath + '/boothType/selectByTypeCode',
	// 				dataType: "json",	//返回数据类型
	// 				type: "post",
	// 				data: {typeCode: rec.id},	//发送数据
	// 				async: false,
	// 				success: function(data){
	// 					console.log('类型');
	// 					project_boothdata_afterGetType(data);
	// 					$("#project-boothdata-numberbox-currencyName").textbox("setValue", data.currencyName);
	// 					if(data.ifStandard == true){
	// 						$("#project-boothdata-numberbox-specLength").textbox("setValue", data.specLength);
	// 						$("#project-boothdata-numberbox-specWidth").textbox("setValue", data.specWidth);
	// 						$("#project-boothdata-numberbox-boothSpec").textbox("setValue", data.boothSpec);
	// 						$("#project-boothdata-numberbox-boothArea").textbox("setValue", data.boothArea);
	// 					}
	// 				},
	// 				error: function(){
	// 					$.messager.alert('提示','数据发送失败！','error');
	// 				}
	// 			});
	// 			// $("#project-boothdata-numberbox-salePrice").textbox("setValue", project_boothdata_price);
	// 			// $("#project-boothdata-numberbox-foreignPrice").textbox("setValue", project_boothdata_foreignPrice);
	// 			// 为了触发改变事件
	// 			$("#project-boothdata-numberbox-openInfoName").combobox("setValue","").combobox("setValue","1");
	// 		}
	// 	});
	// }

	function project_boothdata_add_batch(){
		project_boothdata_afterGetType()
		// // var node = $('#project-boothdata-tree').tree('getSelected');
		// var node = $('#project-boothdata-tool-bottom-sectionCode').combotree('tree').tree('getSelected');
		// if(node == null){
		// 	$.messager.alert('提示','请先选择展区！','warning');
		// 	return;
		// }
		// if(node.sectionLevel > 1){
		// 	$.messager.alert('提示','所选展区节点不是展位节点，不能新增展位！','warning');
		// 	return;
		// }
		setEditInfoClear("#project-boothdata-panel-1-b ul");
		// clear combobox
		$("#project-boothdata-panel-1-b ul").find("li").each(function () {
			var control = $(this).find("input").attr("control");
			if (control == "combobox") $($(this).find("input")[0]).combobox("clear");
		})
		// project_boothdata_sectionCode = node.sectionCode;
		// $('#project-boothdata-setBatch-sectionCode').combotree('setValue', project_boothdata_sectionCode);
		$("#project-boothdata-numberbox-openInfoName-b").combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: 'auto',
			limitToList:true,
			url: '${eippath}/itemCode/selectByItemKindCodeElse?itemKindCode=open_info&projectId=' + getQueryString("projectId"),
			onChange: function(n,o){
				console.log('开口b',n,o);
				project_boothdata_changeOpen(n,true)
			}
		});

		$('#project-boothdata-window-b').window('open');
	}
	function project_boothdata_setPostData(postData, ul) {
		$(ul).find("li").each(function () {
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if (control == "textbox") {
				let val = $($(this).find("input")[0]).textbox("getValue");
				if (val == null || val == undefined || val == '') {
					val = $($(this).find("input")[0]).val();
				}
				postData[hint] = val;
			} else if (control == "numberbox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "datebox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "datetimebox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "combobox") {
				var v = $($(this).find("input")[0]).combobox("getValue");
				if (v != '') postData[hint] = v;
			} else if (control == "combobox2") {
				var v = $($(this).find("input")[0]).combobox("getText")
				if (v != '') postData[hint] = v;
			} else if (control == "checkbox") {
				var v = $(this).find("input")[0].checked
				postData[hint] = v;
			}
		});
	}
	function project_boothdata_save_b(){
		project_boothdata_checkBoothCode($("#project-boothdata-textbox-pre").val(),'展位前缀', false)
		project_boothdata_checkBoothCode($("#project-boothdata-textbox-las1").val(),'后缀起始序号')
		project_boothdata_checkBoothCode($("#project-boothdata-textbox-las2").val(),'后缀终止序号')
		if($('#project-boothdata-combobox-typeCode-b').combobox('getValue') == ''){
			$.messager.alert('提示','展位类型不能为空！','warning');
			return;
		}
		var postData = {};
		postData['operEntrance'] = "展位资料设置";
		postData['foreignCurrency'] = project_boothdata_foreignCurrency;
		postData['sectionCode'] = $('#project-boothdata-setBatch-sectionCode').combotree('getValue');// project_boothdata_sectionCode;
		postData['exhibitCode'] = exhibitCode_for_pass;
		setPostData(postData, "#project-boothdata-panel-1-b ul");
		// project_boothdata_setPostData(postData, "#project-boothdata-panel-1-b ul");
		$.ajax({
			url: eippath + '/exhibitBooth/insertBatch',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					if(data.data) {
						$.messager.alert('提示','成功批量新增' + (+data.data.successNum || 0) + '个展位'
						+ (data.data.existBoothNum ? `，<br>未处理展位\${data.data.existBoothNum}个（展位号重复）` : '')
						+ (data.data.failNum ? `，<br>失败展位\${data.data.failNum}个` : '')
						,'warning').window({width: 380});
					} else {
						$.messager.alert('提示','保存成功！','info');
					}
					$('#project-boothdata-window-b').window('close', true);
					// project_boothdata_datagrid_reload();
					project_boothdata_tool_bottom_search();
				}else{
					$.messager.alert('提示',data.msg || data.message || '保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothdata_cancel_b(){
		$('#project-boothdata-window-b').window('close', true);
	}
	function project_boothdata_fresh(){
		project_boothdata_ifEnableCurrency();
		project_boothdata_tree_fresh();
		project_boothdata_productTypeId_fresh();
		project_boothdata_typeCode_fresh();
		project_boothdata_datagrid_fresh();
	}
	// 开口变更 重新计算 显示: 售价 开口价
	function project_boothdata_changeOpen(o,batch=false){
		var b = batch ? '-b' : '';
		var l = $("#project-boothdata-numberbox-specLength"+b).textbox("getValue");
		var w = $("#project-boothdata-numberbox-specWidth"+b).textbox("getValue");
		var a = $("#project-boothdata-numberbox-boothArea"+b).textbox("getValue");
		// var o = $("#project-boothdata-numberbox-openInfoName"+b).combobox("getValue");
		project_boothdata_changePrice_fix(l,w,a, o,b)
	}

	// 注意: 只能处理 长宽改变
	function project_boothdata_changePrice(l,w,openInfoName,b=''){
		project_boothdata_changePrice_fix(l,w, l*w ,openInfoName,b='')
	}
	// 长宽 开口 面积改变
	function project_boothdata_changePrice_fix(l,w,area,openInfoName,b=''){
		area = area || 0;
		var tmp_openPrice = Number(project_boothdata_openPrice || 0); // 开口加价
		var tmp_foreignOpenPrice = Number(project_boothdata_foreignOpenPrice || 0); // 外币开口加价
		var tmp_price  = Number(project_boothdata_price || 0);				// 展位单价,不含开口
		var tmp_priceF = Number(project_boothdata_foreignPrice || 0); // 外币展位单价,不含开口
		// var area = (l * w ) || 0;
		var tOpen  = 1; // 开口加价倍数
		var tOpenF = 1; // 外币开口加价倍数
		var isType3 = false; // 是否为开口长边算法
		var isType4 = false; // 是否开口单独定价
		if(project_boothdata_openAlgorithmType == 1){ // 开口 * 加价
		}else if(project_boothdata_openAlgorithmType == 2) { // 开口 * area * 加价
			tOpen  = area;
			tOpenF = area;
		}else if(project_boothdata_openAlgorithmType == 3) { // 开口长边 * 加价
			isType3 = true;
			var tmp =  (Math.max(Number(l) , Number(w)) || 0);
			tOpen   = tmp;
			tOpenF  = tmp;
		} else if(project_boothdata_openAlgorithmType == 4) { // 分别指定价格
			isType4 = true
		}
		var tmp = 0, tmp2 = 0; // 开口价,外币开口价
		if(isType4) {
			var tmpKeys  = ['twoOpenInfoPrice','threeOpenInfoPrice','fourOpenInfoPrice'];
			var tmp2Keys = ['twoOpenInfoForeignPrice','threeOpenInfoForeignPrice','fourOpenInfoForeignPrice'];
			tmp  = +project_boothdata_typeDetail[tmpKeys[+openInfoName -2]] || 0;
			tmp2 = +project_boothdata_typeDetail[tmp2Keys[+openInfoName -2]] || 0;
		} else {
		if(openInfoName==2){   		 // 2开口
			tmp  = tmp_openPrice * tOpen;
			tmp2 = tmp_foreignOpenPrice * tOpenF;
		}else if(openInfoName==3){ // 3开口
			tmp  = (isType3 ? 1:2) * tmp_openPrice * tOpen;
			tmp2 = (isType3 ? 1:2) * tmp_foreignOpenPrice * tOpenF;
		}else if(openInfoName==4){ // 4开口
			tmp  = (isType3 ? 1:3) * tmp_openPrice * tOpen;
			tmp2 = (isType3 ? 1:3) * tmp_foreignOpenPrice * tOpenF;
		}
		}
		var tmp_price_all  = tmp_price;  //最终售价 	//按个
		var tmp_priceF_all = tmp_priceF;  //最终售价 	//按个
		if(project_boothdata_priceWay == 1){ // 按面积 : 原价x面积
			tmp_price_all  *= area;
			tmp_priceF_all *= area;
		}
		tmp_price_all  += tmp;  // 最终售价+开口价
		tmp_priceF_all += tmp2; // 最终售价+外币开口价
		// var detail  = project_boothdata_stagePriceListDetail; // 时段优惠详情
		// if(project_boothdata_enableStagePrice && detail){ // 有时段调价
		// 	// 折扣/金额
		// 	if(detail.discountType == 'disAmount'){ // 金额/M^2	// -优惠x面积
		// 		tmp_price_all  -= Number(detail.discountNum) * area
		// 		tmp_priceF_all -= Number(detail.foreignCurrency) * area
		// 	}else if(detail.discountType == 'disRatio') {
		// 		var dis = Number(detail.discountRatio/100);
		// 		tmp_price_all  *= dis
		// 		tmp_priceF_all *= dis
		// 	}
		// }
		$("#project-boothdata-numberbox-salePrice"+b).textbox("setValue",
			( tmp_price_all || 0).toFixed(2));
		$("#project-boothdata-numberbox-foreignPrice"+b).textbox("setValue",
			( tmp_priceF_all || 0).toFixed(2));
	}
	// specLength specWidth : boothSpec规格 boothArea面积
	// openInfoName开口
	// salePrice 售价
	// foreignPrice 外币售价
	function project_boothdata_changeLW(l,w,batch=false){
		var b = batch ? '-b' : '';
		var openInfoName = $("#project-boothdata-numberbox-openInfoName"+b).combobox("getValue")
		project_boothdata_changePrice(l,w,openInfoName,b)
		if(l != '' && l > 0.00 && w != '' && w > 0.00){
			$("#project-boothdata-textbox-boothSpec"+b).textbox("setValue", parseFloat(l).toFixed(2) + '*' + parseFloat(w).toFixed(2));
			$("#project-boothdata-numberbox-boothArea"+b).textbox("setValue", (l * w).toFixed(2));
		}else{
			$("#project-boothdata-textbox-boothSpec"+b).textbox("setValue", '');
			$("#project-boothdata-numberbox-boothArea"+b).textbox("setValue", '');
		}
	}
	function project_boothdata_change(){
		$("#project-boothdata-numberbox-specLength").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changeLW(n,$("#project-boothdata-numberbox-specWidth").textbox("getValue"));
		}});
		$("#project-boothdata-numberbox-specWidth").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changeLW($("#project-boothdata-numberbox-specLength").textbox("getValue"),n);
		}});
		$("#project-boothdata-numberbox-boothArea").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changePrice_fix(
				$("#project-boothdata-numberbox-specLength").textbox("getValue"),
				$("#project-boothdata-numberbox-specWidth").textbox("getValue"),
				n,
				$("#project-boothdata-numberbox-openInfoName").combobox("getValue")
			);
		}});
		// $("#project-boothdata-numberbox-openInfoName").combobox({onChange: function(n){
		// 	project_boothdata_changeOpen(n)
		// });
		$("#project-boothdata-numberbox-specLength-b").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changeLW(n,$("#project-boothdata-numberbox-specWidth-b").textbox("getValue"),true)
		}});
		$("#project-boothdata-numberbox-specWidth-b").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changeLW($("#project-boothdata-numberbox-specLength-b").textbox("getValue"),n,true)
		}});
		$("#project-boothdata-numberbox-boothArea-b").textbox({ onChange: function(n, o){
			if(+n!=+o) project_boothdata_changePrice_fix(
				$("#project-boothdata-numberbox-specLength-b").textbox("getValue"),
				$("#project-boothdata-numberbox-specWidth-b").textbox("getValue"),
				n,
				$("#project-boothdata-numberbox-openInfoName-b").combobox("getValue"),
				'-b'
			);
		}});
		// $("#project-boothdata-numberbox-openInfoName-b").combobox({onChange:function(n){
		// 	project_boothdata_changeOpen(n,true)
		// });
	}
	// 获取 : 展品范围
	var project_boothdata_productTypeData = [];
	function project_boothdata_productTypeId_fresh(){
		$.ajax({
			url: eippath + '/productType/getByMap',
			dataType : "json", //返回数据类型
			type:"post",
			data:{
				exhibitCode: exhibitCode_for_pass
			},
			async: false,
			success: function(data) {
				project_boothdata_productTypeData = data;
				// 搜索 修改展品范围
				$('#project-boothdata-tool-bottom-productTypeId,#project_boothdata_productTypeId').combobox({
					valueField: 'productTypeId',
					textField: 'typeName',
				}).combobox('loadData',project_boothdata_productTypeData);
				// 批量新增
				$('#project-boothdata-panel-1-combobox-productTypeId').combobox({
					valueField: 'productTypeId',
					textField: 'typeName',
					multiple: !0,
				}).combobox('loadData',project_boothdata_productTypeData);
			},
			error: function(err){
				console.log('err',err);
				$.messager.alert('提示','获取展品范围失败！','error');
			}
		})
	}
		// 获取 : 展位类型
	function project_boothdata_typeCode_fresh(){
		$.ajax({
			url:'${eippath}/boothType/getAll?exhibitCode=' + exhibitCode_for_pass,
			dataType : "json", //返回数据类型
			type:"post",
			async: false,
			success: function(data) {
				project_boothdata_typeData = data;
				$('#project-boothdata-tool-bottom-typeCode').combobox('loadData',project_boothdata_typeData);
			},
			error: function(err){
				console.log('err',err);
				$.messager.alert('提示','获取展位类型失败！','error');
			}
		})
	}
	function project_boothdata_combotree_clear(){
		var $sc = $('#project-boothdata-tool-bottom-sectionCode');
		$sc.combotree('clear');
		try{
			$sc.combotree('textbox').textbox('clear')
		}catch(e){ }
	}
	// 搜索 : 展区
	window.boothSaleStates = [];
	function project_boothdata_tree_fresh(){
		project_boothdata_combotree_clear()
		var tmpn = null;
		$.ajax({
			url: eippath + '/exhibitSection/makeTreeJson',
			dataType: "json",	//返回数据类型
			type: "post",
			data: { exhibitCode: exhibitCode_for_pass },
			async: false,
			success: function (data) {
				projectBoothdata_sectionEditBatch.sections = JSON.parse(JSON.stringify(data || []));
				$('#project-boothdata-setBatch-sectionCode').combotree('loadData', JSON.parse(JSON.stringify(data || [])));
				$('#project-boothdata-set-sectionCode').combotree('loadData', JSON.parse(JSON.stringify(data || [])));
	  		var $sc = $('#project-boothdata-tool-bottom-sectionCode');
				$sc.combotree('loadData',data).combotree('tree').tree({
					// onSelect: function(node){
					// 	console.log('node', node);
					// 	var t = $sc.combotree('tree').tree('getSelected');
					// 	if(t){
					// 		if(t.sectionCode !== $sc.combotree('tree').tree('getRoot').sectionCode){
					// 			$('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().show();
					// 		}else{
					// 			$('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().hide();
					// 		}
					// 		// try{
					// 		// 	$sc.combotree('textbox').textbox('setValue', t.sectionName);
					// 		// }catch(e){ }
					// 	}
					// }
				}).tree('collapseAll');
				// $sc.combotree('textbox').textbox({
				// 	editable: false,
				// 	icons: [{
				// 		iconCls:'icon-remove',
				// 		handler: function(e){
				// 			// $sc.combotree('clear');
				// 			var $root = $sc.combotree('tree').tree('getRoot');
				// 			if($root){
				// 				$sc.combotree('setValue', $root.sectionCode)
				// 				$('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().hide();
				// 				// $sc.combotree('tree').tree('collapseAll').tree('expand',$root.target).tree('select',$root.target);
				// 				// $('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().hide();
				// 				// try{
				// 				// 	$sc.combotree('textbox').textbox('setValue', $root.sectionName);
				// 				// }catch(e){ }
				// 			}
				// 		}
				// 	}],
				// 	// onChange: function(n,o){
				// 	// 	if(n){ // 查询是否在数据中
				// 	// 		if(n === tmpn){
				// 	// 			throw new Error('跳出死循环')
				// 	// 		}
				// 	// 		if(n!= o){ // 防死循环
				// 	// 			tmpn  = n;
				// 	// 			var t = $sc.combotree('tree').tree('getSelected');
				// 	// 			// try{
				// 	// 			// 	t && $sc.combotree('textbox').textbox('setValue', t.sectionName);
				// 	// 			// }catch(e){
				// 	// 			// }
				// 	// 			if(t){
				// 	// 				if(t.sectionCode !== $sc.combotree('tree').tree('getRoot').sectionCode){
				// 	// 					$('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().show();
				// 	// 				}else{
				// 	// 					$('.project-boothdata-tool-bottom-sectionCode .icon-remove').parent().hide();
				// 	// 				}
				// 	// 				try{
				// 	// 					$sc.combotree('textbox').textbox('setValue', t.sectionName);
				// 	// 				}catch(e){ }
				// 	// 			}
				// 	// 		}
				// 	// 	}else{
				// 	// 		project_boothdata_combotree_clear()
				// 	// 	}
				// 	// }
				// });
				// setTimeout(() => {
				// 	var $root = $sc.combotree('tree').tree('getRoot');
				// 	if($root){
				// 		$sc.combotree('setValue', $root.sectionCode).combotree('tree').tree('expand',$root.target);
				// 		// $sc.combotree('tree').tree('expand',$root.target).tree('select',$root.target);
				// 		// try{
				// 		// 	$sc.combotree('textbox').textbox('setValue', $root.sectionName);
				// 		// }catch(e){ }
				// 	}
				// }, 0);
			}
		})
		// 查询状态和颜色
		$.ajax({
			url: eippath + '/api/exhibitBooth/selectBoothColor',
			dataType: "json",	//返回数据类型
			type: "post",
			// data: { exhibitCode: exhibitCode_for_pass },
			async: false,
			success(data) {
				if(data.state !==1 ){
					$.messager.alert('提示',data.msg || data.message || '加载展位状态定义失败','warning');
					return;
				}
				window.boothSaleStates = data.data || [];
	  		$('#project-boothdata-tool-bottom-saleState').combobox('loadData',
					[{id:0,saleState: '',stateColor: '', stateName: '所有'},...window.boothSaleStates.filter(it => it.saleState !== -1)]
				);
			},
			error(e) {
				$.messager.alert('提示','加载展位状态定义失败','warning');
			}
		})
		// $('#project-boothdata-tree').tree({
		// 	url: eippath + '/exhibitSection/makeTreeJson?exhibitCode=' + exhibitCode_for_pass,
		// 	onSelect: function(node){
		// 		$('#project-boothdata-datagrid').datagrid({
		// 			url: eippath + '/exhibitBooth/getListBySectionCode1?sectionCode=' + node.sectionCode + '&exhibitCode='+ exhibitCode_for_pass
		// 		});
		// 		/*if(node.sectionLevel == 3){
		// 			project_boothdata_datagrid_fresh();
		// 		}else if(node.sectionLevel == 2){
		// 			$('#project-boothdata-datagrid').datagrid({
		// 				url: eippath + '/exhibitBooth/getListBySectionCode2?sectionCode=' + node.sectionCode
		// 			});
		// 		}else if(node.sectionLevel == 1){
		// 			$('#project-boothdata-datagrid').datagrid({
		// 				url: eippath + '/exhibitBooth/getListBySectionCode1?sectionCode=' + node.sectionCode
		// 			});
		// 		}*/
		// 	},
		// 	onLoadSuccess: function (node, data) {
		// 		if (data.length > 0) {
		// 			var n = $('#project-boothdata-tree').tree('find', data[0].id);
		// 			$('#project-boothdata-tree').tree('select', n.target);
		// 		}
		// 	}
		// });
	}
	function project_boothdata_datagrid_fresh(){
		$('#project-boothdata-tool-bottom-sectionCode').combotree('clear');
		$('#project-boothdata-tool-bottom-boothCode').textbox("clear");
		$('#project-boothdata-tool-bottom-typeCode').combobox("clear");
		$('#project-boothdata-tool-bottom-boothArea-from').numberbox("clear");
		$('#project-boothdata-tool-bottom-boothArea-to').numberbox("clear");
		$('#project-boothdata-tool-bottom-openInfoId').combobox("clear");
		$('#project-boothdata-tool-bottom-productTypeId').combobox("clear");
		$('#project-boothdata-tool-bottom-reserveLock').combobox("clear");
		$('#project-boothdata-tool-bottom-saleState').combobox('clear');
		$('#project-boothdata-tool-bottom-boothMapType').combobox('clear');
		project_boothdata_tool_bottom_search();
		// $('#project-boothdata-datagrid').datagrid({
		// 	url: eippath + '/exhibitBooth/getListByExhibitCode?exhibitCode=' + exhibitCode_for_pass,
		// 	queryParams: {
		// 		reserveLock: '',
		// 		openInfoId: '',
		// 		productScope: '',
		// 		typeCode:'',
		// 		boothAreaStart:'',
		// 		boothAreaEnd:'',
		// 		boothCode:''
		// 	}
		// });
	}
	function project_boothdata_datagrid_reload(){
		var $pg = $('#project-boothdata-pager').pagination('options');
		project_boothdata_datagrid_load(+$pg.pageNumber, +$pg.pageSize)
		// $('#project-boothdata-datagrid').datagrid({
		// 	onLoadSuccess: function(data){
		// 		if(project_boothdata_alocate_id == -1)$(this).datagrid('selectRow', -1);
		// 		else{
		// 			var rows = $(this).datagrid("getRows");
		// 			for(var i = 0; i < rows.length; i++){
		// 				if(rows[i].boothId == project_boothdata_alocate_id){
		// 					$(this).datagrid('selectRow', i);
		// 					project_boothdata_alocate_id = -1;
		// 					break;
		// 				}
		// 			}
		// 		}
		// 	}
		// }).datagrid("reload");
	}
	function project_boothdata_window_close(){
		$('#project-boothdata-window').window({
			onBeforeClose: function(){
				if(project_boothdata_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#project-boothdata-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				project_boothdata_datagrid_reload();
			}
		});
	}
	function project_boothdata_add(){
		$('#js_project_boothdata_edit_boothCode').hide();
		project_boothdata_afterGetType()

		// // var node = $('#project-boothdata-tree').tree('getSelected');
		// var node = $('#project-boothdata-tool-bottom-sectionCode').combotree('tree').tree('getSelected');
		// if(node == null){
		// 	$.messager.alert('提示','请先选择展区！','warning');
		// 	return;
		// }
		// if(node.sectionLevel > 1){
		// 	$.messager.alert('提示','所选展区节点不是展位节点，不能新增展位！','warning');
		// 	return;
		// }
		// project_boothdata_sectionCode = node.sectionCode;
		// $('#project-boothdata-set-sectionCode').combotree('setValue', project_boothdata_sectionCode);
		if(!project_boothdata_window_close_set){
			project_boothdata_window_close();
			project_boothdata_window_close_set = true;
		}
		project_boothdata_resetCombobox();
		$.ajax({
			url: eippath + '/exhibitBooth/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {},	//发送数据
			async: true,
			success: function(data){
				project_boothdata_alocate_id = -1;
				// loadProjectBoothType()
				$('#project-boothdata-window').window('open');
				project_boothdata_save_id = data.id;
				setEditInfoClear("#project-boothdata-panel-1 ul");
				// clear combobox
				$("#project-boothdata-panel-1 ul").find("li").each(function () {
	        var control = $(this).find("input").attr("control");
					if (control == "combobox") $($(this).find("input")[0]).combobox("clear");
				})
				project_boothdata_edit_mode = "Add";
				setEditInfoReadOnly(1, "#project-boothdata-panel-1 ul");
				setButtonEditMode(1, "#project-boothdata-window-toolbar div");
				$('#project-boothdata-textbox-boothCode').textbox('enable');
				$('#project-boothdata-set-sectionCode').combotree('enable');
				project_boothdata_button_edit_mode = 1;
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothdata_mod(){
		project_boothdata_mod_or_view(1);
	}
	function project_boothdata_mod_or_view(flag){
		$('#js_project_boothdata_edit_boothCode')[flag === 1 ? 'show' : 'hide']();
		project_boothdata_afterGetType()
		project_boothdata_resetCombobox(true)
		// loadProjectBoothType()
		if(!project_boothdata_window_close_set){
			project_boothdata_window_close();
			project_boothdata_window_close_set = true;
		}
		var row = $('#project-boothdata-datagrid').datagrid('getSelected');
		if (row){
			project_boothdata_alocate_id = row.boothId || '';

			$('#project-boothdata-window').window('open');
			project_boothdata_save_id = row.boothId;
			setEditInfoInit(row, "#project-boothdata-panel-1 ul");
			// project_boothdata_changePrice(
			// 	$('#project-boothdata-numberbox-specLength').textbox('getValue'),
			// 	$('#project-boothdata-numberbox-specWidth').textbox('getValue'),
			// 	$('#project-boothdata-numberbox-openInfoName').combobox('getValue'));
			// 显示保存的价格
			$("#project-boothdata-numberbox-salePrice").textbox("setValue", +row.salePrice);
			$("#project-boothdata-numberbox-foreignPrice").textbox("setValue", +row.foreignPrice);
			$('#project-boothdata-set-sectionCode').combotree('setValue', row.sectionCode);
			const $product = $('#project-boothdata-panel-1-combobox-productTypeId')
			if(row.productTypeId){
				$product.combobox('setValues', row.productTypeId.split(','))
			}else{
				$product.combobox('clear')
			}
			//$('#project_boothdata_productTypeId').combobox('setValue',row.productTypeId);
			project_boothdata_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothdata_mod2(){ // 查看里的修改
		project_boothdata_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function project_boothdata_edit(flag){
		project_boothdata_edit_mode = "Mod";
		$('#js_project_boothdata_edit_boothCode')[flag === 1 ? 'show' : 'hide']();
		setEditInfoReadOnly(flag, "#project-boothdata-panel-1 ul");
		setButtonEditMode(flag, "#project-boothdata-window-toolbar div");
		project_boothdata_button_edit_mode = flag;
		var row = $('#project-boothdata-datagrid').datagrid('getSelected');
		project_boothdata_edit_row = row || project_boothdata_edit_row;
		// if(row && row.graphId) {
		// 	$('#project-boothdata-numberbox-specLength,#project-boothdata-numberbox-specWidth').textbox('readonly', true);
		// }
		if(flag == 1) {
			if(row && (row.reserveLock == 99 || row.saleState>0)) {
				$('#project-boothdata-textbox-boothCode').textbox('disable')
			} else {
				$('#project-boothdata-textbox-boothCode').textbox('enable');
			}
		}
		if((flag == 1 && row.graphId) || flag == 2) {
			$('#project-boothdata-set-sectionCode').combotree('disable')
		} else {
			$('#project-boothdata-set-sectionCode').combotree('enable');
		}
	}
	function project_boothdata_view(){
		project_boothdata_mod_or_view(2);
	}
	function project_boothdata_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/exhibitBooth/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {boothId: project_boothdata_save_id,operEntrance:"展位资料设置"},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							project_boothdata_alocate_id = -1;
							$('#project-boothdata-window').window('close');
						}else{
							$.messager.alert('提示',data.message || data.msg || '删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function project_boothdata_del_batch(){
		var rows = $('#project-boothdata-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			var zz = 0;
			for(var i = 0; i < rows.length; i++){
				if (rows[i].saleState == 0){
					// $.messager.alert('提示','当前选中展位订单状态有 已预订/已签合同/已完成的情况不能删除!','warning');
					postData.push({boothId: rows[i].boothId,operEntrance:"展位资料设置"})
					zz++;
				}
			}
			// console.log(zz);
			if(zz != rows.length){
				$.messager.alert('提示','当前选中展位订单状态有 已预订/已录合同/已完成的情况不能删除!','warning');
			}else {
				$.messager.confirm('提示', '当前选择的展位数为：' + rows.length + '，确定删除吗？', function (r) {
					if (r) {
						$.ajax({
							url: eippath + '/exhibitBooth/deleteBatch',
							dataType: "json",	//返回数据类型
							type: "post",
							contentType: "application/json",
							data: JSON.stringify(postData),	//发送数据
							async: true,
							success: function (data) {
								if (data.state == 1) {
									if(data.data) {
										$.messager.alert('提示','成功删除' + (+data.data.successNum || 0) + '个展位,<br>删除失败' + (+data.data.existBoothNum || 0) + '个展位（展位已关联预订单或合同）','warning').window({width: 380});
									} else {
										$.messager.alert('提示','导入成功！','info');
									}
									project_boothdata_datagrid_reload();
								} else {
									$.messager.alert('提示', data.message || data.msg || '删除失败！', 'error');
								}
							},
							error: function () {
								$.messager.alert('提示', '数据发送失败！', 'error');
							}
						});
					}
				});
			}
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothdata_checkBoothCode(boothCode, msg='', checkNull = true) {
		msg = msg || '展位号';
		if(checkNull && !boothCode){
			$.messager.alert('提示', msg+'不能为空！','warning');
			throw new Error(msg+'不能为空！');
		}
		if(/(；|;|,|，)/.test(boothCode)) {
			$.messager.alert('提示',msg+'不能包含分隔符(逗号或分号)','warning');
			throw new Error(msg+'不能包含分隔符(逗号或分号)');
		}
	}
	function project_boothdata_save(){
		const boothCode = ($("#project-boothdata-textbox-boothCode").val() || '').trim();
		project_boothdata_checkBoothCode(boothCode,'',false)
		if($('#project-boothdata-combobox-typeCode').combobox('getValue') == ''){
			$.messager.alert('提示','展位类型不能为空！','warning');
			return;
		}
		var postData = {};var productTypeId = [];
		postData['operEntrance'] = "展位资料设置";
		postData['editMode'] = project_boothdata_edit_mode;
		postData['boothId'] = project_boothdata_save_id;
		postData['foreignCurrency'] = project_boothdata_foreignCurrency;
		postData['sectionCode'] = $('#project-boothdata-set-sectionCode').combotree('getValue');// project_boothdata_sectionCode;
		postData['exhibitCode'] = exhibitCode_for_pass;
		// postData['exhibitCode'] = exhibitCode_for_pass;
		postData['openInfoId'] = $("#project-boothdata-numberbox-openInfoName").combobox("getValue");
		productTypeId = $("#project-boothdata-panel-1-combobox-productTypeId").combobox("getValues");

		// console.log(productTypeId,"返回的productTypeId2");
		// productTypeId2 = productTypeId.toString();
		// console.log(productTypeId2,"第二次返回的productTypeId");
		// postData['productTypeNames'] = $("#project-boothdata-panel-1-combobox-productTypeId").combobox("getTexts").split(",");
		productTypeId = productTypeId.toString();
		setPostData(postData, "#project-boothdata-panel-1 ul");
		// project_boothdata_setPostData(postData, "#project-boothdata-panel-1 ul");
		postData['boothCode'] = boothCode;
		postData['productTypeId'] = productTypeId;
		//postData['productTypeNames'] = $("#project-boothdata-panel-1-combobox-productTypeId").combobox("getText").toString();
		let isTip = false;
		if(project_boothdata_edit_mode == 'Mod') {
			let row = project_boothdata_edit_row || {};
			if(row.graphId && (row.specLength != postData.specLength || row.specWidth != postData.specWidth)) {
				isTip = true;
			}
		}
		console.log(postData);
		$.ajax({
			url: eippath + '/exhibitBooth/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					if(isTip) {
						$.messager.alert('提示','展位长宽已发生改变, 请到绘制页面确认图形是否一致. ','warning').window({width: 380});
					} else {
						$.messager.alert('提示','保存成功！','info');
					}
					project_boothdata_edit(2);
					project_boothdata_datagrid_reload()
					project_boothdata_alocate_id = postData['boothId'];
				}else if(data.state == -1){
					$.messager.alert('提示','保存失败, 该项目内存在重复的展位号. ','warning');
				}else{
					$.messager.alert('提示',data.msg ? ('保存失败: ' + data.msg) : '保存失败!','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothdata_cancel(){
		$('#project-boothdata-window').window({
			closed: true
		});
	}
	function project_boothdata_import() {
		// document.getElementById('choiseexcel').click()
		$('#project-boothdata-window-import').window({
			closed: false,
      cls: 'project-boothdata-window-import',
      onBeforeOpen() {
        let w = $('.project-boothdata-window-import')
        if(w.length>1){
          for (let i = 0; i < w.length-1; i++) {
            w[i].remove()
          }
        }
      },
		});
	}
	function project_boothdata_import2() {
		document.getElementById('project_boothdata_choiseexcel').click()
	}
	function project_boothdata_importf(obj) {
		console.log('开始导入');
		if (!obj.files) {
			return;
		}
		var file = obj.files[0];
		document.getElementById('project_boothdata_choiseexcel').value = '';
		var formData = new window.FormData();
		formData.append('file', file);
		formData.append('pid', getQueryString("projectId"));
		formData.append('exhibitCode', getQueryString("exhibitCode"));
		formData.append('operEntrance',"展位资料设置");
		let tmpAlert ;
		$.ajax({
			// url: eippath + "/serveBooth/exhibitBoothImport",
			url: eippath + "/exhibitBooth/importExhibitBooth",
			type: "post",
			data: formData,
			processData: false,  // 不处理数据
			contentType: false,   // 不设置内容类型
			beforeSend: function() {
				tmpAlert = $.messager.alert('提示', '导入中。。请稍候', 'info');
			},
			success: function(data) {
				tmpAlert.window({closed: true});
				if(data.state == 1){
					if(data.data) {
						$.messager.alert('提示',
						`<div style="padding-left: 50px;">成功批量导入\${+data.data.successNum || 0}个展位
						\${data.data.existBoothNum ? ('<br>未处理展位' + data.data.existBoothNum + '个（展位号重复）') :''}
						\${data.data.failNum ? ('<br>失败展位' + data.data.failNum + '个') :''}
						\${data.data.failBoothTypeNum ? ('<br>展位类型错误' + data.data.failBoothTypeNum + '个') :''}
						\${data.data.failExhibitSectionNum ? ('<br>展区编号错误' + data.data.failExhibitSectionNum + '个') :''}
						</div>`,'warning').window({width: 380});
					} else {
						$.messager.alert('提示','导入成功！','info');
					}
					// $('#project-boothdata-window-b').window('close', true);
					project_boothdata_tool_bottom_search();
				}else{
					$.messager.alert('提示','导入失败！','error');
				}
			},
		});
	}
	function project_boothdata_edit_productTypeId_batch(){
		// var rows = $('#project-boothdata-datagrid').datagrid('getSelected');
		// if(rows){
		// 	alert('00000099888');
		// }else{
		// 	$.messager.alert('提示','未选中内容！','warning');
		// }
		var rows = $('#project-boothdata-datagrid').datagrid('getSelections');
		if(rows.length>0){
			$('#project-boothdata-window-c').window('open');
			$('#project_boothdata_productTypeId').combobox("clear");
			$('#project_count').html(rows.length)
		}else{
			// var total = $('#project-boothdata-datagrid').datagrid('getPager').data("pagination").options.total;
			// if(total<=0){
				$.messager.alert('提示','没有需要操作的数据！','warning');
			// 	return
			// }else{
			// 	$.messager.confirm('提示', '确定要操作全部的'+total+'条数据吗?', function(r) {
			// 		if (r) {
			// 			$("#project-boothdata-window-c").window('open');
			// 			$('#project_boothdata_productTypeId').combobox("clear");
			// 			$("#project_count").html(total)
			// 		}
			// 	});
			// }
		}
	}
	function project_boothdata_productTypeId_cancel(){
		$("#project-boothdata-window-c").window('close');
	}
	function project_boothdata_productTypeId_save(){
		var value = $('#project_boothdata_productTypeId').combobox("getValues");

		var text = $('#project_boothdata_productTypeId').combobox("getText");
		console.log(value,222);
		if(value == '' || value === undefined){
			$.messager.alert('提示', '请选择展品范围！', 'warning');
			return;
		}
		var productTypeIds = value.toString();

		var rows = $('#project-boothdata-datagrid').datagrid('getSelections');
		if(rows.length>0){
			var postData = {};
			var exhibitBooths = [];
			for (var i = 0; i < rows.length; i++) {
				// exhibitBooths.boothId = rows[i].boothId;
				postData['exhibitBooths[' + i + '].boothId'] = rows[i].boothId;
			}
			// postData["exhibitBooths"] = exhibitBooths;
			postData["productTypeIds"] = productTypeIds;
			postData["operEntrance"] = "展位资料设置";
			postData["exhibitCode"] = getQueryString("exhibitCode");
			$.ajax({
				url: eippath + '/exhibitBooth/batchProductTypeByIds',
				dataType: "json",	//返回数据类型JSON.stringify(
				type: "post",
				data: postData,
				// contentType: "formdata",
				async: true,
				success: function(data){
					if(data.state == 1){
						if(data.data) {
							$.messager.alert('提示','更新展位展品成功' + (+data.data.successNum || 0) + '个,<br>失败' + (+data.data.failNum || 0) + '个','warning').window({width: 380});
						} else {
							$.messager.alert('提示','保存成功！','info');
						}
						project_boothdata_datagrid_reload();
						$("#project-boothdata-window-c").window('close');
						// project_boothdata_alocate_id = postData['boothId'];
					}else{
						$.messager.alert('提示','保存失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}else{

		}
	}
	function project_boothdata_lockbooth(){
		var rows = $('#project-boothdata-datagrid').datagrid('getSelections');
		var exhibitBooths = [];
		var exhibitCode = exhibitCode_for_pass;
		var k = 0;var x = 0;var y = 0;
		var postData ={};
		if(rows.length<=0){
			$.messager.alert('提示', '请先选择需要锁定的展位！', 'warning');
		}else{
		    for(var i=0;i<rows.length;i++){
                if(rows[i].reserveLock != 99 && rows[i].saleState == 0){
					postData['exhibitBooths[' + i + '].boothId'] = rows[i].boothId;
					postData['exhibitBooths[' + i + '].operEntrance'] = "展位资料设置";
					k++;
                }

		    }if(k==0) {
				$.messager.alert('提示', '展位已锁定，展位状态为已预订、已签合同、已成交时，无法设为预留锁定展位！', 'warning');
			}else {
				$.messager.confirm('提示', '确认锁定这' + k + '个展位吗？', function (r) {
					if (r) {
						$.ajax({
							url: eippath + '/exhibitBooth/batchReserLockBoothNum',
							dataType: "json",	//返回数据类型
							type: "post",
							data: postData,
							async: true,
							success: function (data) {
								if (data.state == 1) {
									$.messager.alert('提示','成功 ' + (+data.data.success || 0) + ' 个展位'
											+ (data.data.fail ? `，失败 \${data.data.fail} 个展位` : '')
											,'info').window({width: 300});
									// $.messager.alert('提示', '锁定成功！', 'info');
									project_boothdata_datagrid_reload()
									// project_boothdata_alocate_id = postData['boothId'];
								} else {
									$.messager.alert('提示', '锁定失败！', 'error');
								}
							},
							error: function () {
								$.messager.alert('提示', '数据发送失败！', 'error');
							}
						});
					}
				});
			}
			}
	}
	function project_boothdata_unlockbooth(){
		var rows = $('#project-boothdata-datagrid').datagrid('getSelections');
		var k = 0;
		if(rows.length<=0){
			$.messager.alert('提示', '请先选择需要取消预留锁定的展位！', 'warning');
		}else{
			var postData ={};
			for(var i=0;i<rows.length;i++){
				if(rows[i].reserveLock == 99){
					postData['exhibitBooths[' + i + '].boothId'] = rows[i].boothId;
					postData['exhibitBooths[' + i + '].operEntrance'] = "展位资料设置";
					k+=1;
				}
			}
			if(k==0){
				$.messager.alert('提示', '所选展位未被锁定！！', 'warning');
			}else {
				$.messager.confirm('提示', '确认取消预留锁定这' + k + '个展位吗？', function (r) {
					if (r) {
						$.ajax({
							url: eippath + '/exhibitBooth/batchCancelReserLockBoothNum',
							dataType: "json",	//返回数据类型
							type: "post",
							data: postData,
							async: true,
							success: function (data) {
								if (data.state == 1) {
									$.messager.alert('提示','成功 ' + (+data.data.success || 0) + ' 个展位'
											+ (data.data.fail ? `，失败 \${data.data.fail} 个展位` : '')
											,'info').window({width: 300});
									// $.messager.alert('提示', '取消预留锁定成功！', 'info');
									project_boothdata_datagrid_reload()
									// project_boothdata_alocate_id = postData['boothId'];
								} else {
									$.messager.alert('提示', '取消预留锁定失败！', 'error');
								}
							},
							error: function () {
								$.messager.alert('提示', '数据发送失败！', 'error');
							}
						});
					}
				});
			}
		}
	}
	function saleState_type(value,row,index){
		const tmpData = window.boothSaleStates.find(it=> (+it.saleState) === value) || {};
		return '<span style="color: '+ (tmpData.stateColor || '#000000') + ';">'+ (tmpData.stateName || '') +'</span>';
		// if(row.saleState == 0){
		// 	return "可销售";
		// }else if(row.saleState == 1){
		// 	return "已预订";
		// }else if(row.saleState == 2){
		// 	return "已签合同";
		// }else if(row.saleState ==3){
		// 	return "已成交";
		// }
		// return '';
	}
	function reserveLockName_type(value,row,index){
		if(row.reserveLock == 99){
			return "已锁定";
		} else{
			return "";
		}
	}
	function boothMapType_type(value,row,index){
		if(row.graphId){
			return "已关联";
		} else{
			return "";
		}
	}
	function sectionName_type(value,row,index){
		// console.log('v',value,row,index)
		var v = value || ''
		return '<span class="project-boothdata-datagrid-note" title="'+ v +'" >' + v + '</span>';
	}
	// begin of tool-bottom  页面上方工具栏下方的输入框
	$('#project-boothdata-tool-bottom-reserveLock').combobox({
		valueField: 'reserveLock',
		textField: 'reserveLockName',
		data: [{
			reserveLock: 'lock',
			reserveLockName: '已锁定'
		}, {
			reserveLock: 'unLock',
			reserveLockName: '未锁定'
		}]
	})
	$('#project-boothdata-tool-bottom-openInfoId').combobox({
		valueField: 'openInfoId',
		textField: 'openInfoName',
		data: [{
			openInfoId:1,
			openInfoName:'单开口'
		},{
			openInfoId: 2,
			openInfoName: '双开口'
		}, {
			openInfoId: 3,
			openInfoName: '三开口'
		},{
			openInfoId: 4,
			openInfoName: '四开口'
		}]
	})
	$('#project-boothdata-tool-bottom-boothArea-from').numberbox({
		min:0,
		precision:0
	})
	$('#project-boothdata-tool-bottom-boothArea-to').numberbox({
		min:0,
		precision:0
	});

	function project_boothdata_export() {
		const $pg = $('#project-boothdata-pager');
		const total = +$pg.pagination('options').total || 0;
		if(total < 1) {
			$.messager.alert('提示', '暂无数据可导出', 'error');
			throw new Error('暂无数据可导出')
		}
		const $dg = $('#project-boothdata-datagrid');
		$dg.datagrid('loading');
		let rows = $dg.datagrid('getSelections') || [];
		let isAll = false;
		if(!rows.length) { isAll = true;
			rows = project_boothdata_datagrid_getData(1, total);
			const sum = window.project_boothdata_datagrid_rsp.data || {};
			const tmpCol = JSON.parse(JSON.stringify(rows[0]));
			Object.keys(tmpCol).map(it=> {
				if(it == 'boothArea'  || it == 'salePrice' || it == 'foreignPrice') {
					tmpCol[it] = +sum['count' + it.toLocaleLowerCase()] || 0;
				} else if(it == 'sectionName') {
					tmpCol[it] = '总计'
				} else {
					tmpCol[it] = ''
				}
			})
			rows.push(tmpCol);
		}
		$dg.datagrid("toExcel", {
			filename: '展位资料导出',
			rows,
		});
		$dg.datagrid('loaded');
	}
	function project_boothdata_datagrid_getData(page=1,rows=20) {
		// var postData ={}
		// var reserveLock = $('#project-boothdata-tool-bottom-reserveLock').combobox("getValue");
		// console.log(reserveLock);
		// postData["reserveLock"] = reserveLock;
		// var node = $('#project-boothdata-tree').tree('getSelected');
		var node = $('#project-boothdata-tool-bottom-sectionCode').combotree('tree').tree('getSelected');
		var sectionCode = node ? node.sectionCode : '';
		// var sectionCode = $('#project-boothdata-tool-bottom-sectionCode').combotree('getValue') || '';
		let rsp = [];
		window.project_boothdata_datagrid_rsp = {total:0,data:{},rows: []};
		let flag = $.ajaxSettings.async
		$.ajaxSettings.async = false
		try{
			$.post(eippath + '/exhibitBooth/getListBySectionCode1?sectionCode=' + sectionCode + '&exhibitCode='+ exhibitCode_for_pass,{
				reserveLock: $('#project-boothdata-tool-bottom-reserveLock').val(),
				openInfoId:  $('#project-boothdata-tool-bottom-openInfoId').val(),
				productScope: $('#project-boothdata-tool-bottom-productTypeId').val(),
				typeCode:$('#project-boothdata-tool-bottom-typeCode').val(),
				boothAreaStart:$('#project-boothdata-tool-bottom-boothArea-from').val(),
				boothAreaEnd:$('#project-boothdata-tool-bottom-boothArea-to').val(),
				boothCode: ($('#project-boothdata-tool-bottom-boothCode').val() || '').trim(),
				saleState:$('#project-boothdata-tool-bottom-saleState').combobox('getValue') || '',
				boothMapType:$('#project-boothdata-tool-bottom-boothMapType').combobox('getValue') || '',
				affiliatedProjectId: $('#project-boothdata-subpro').combotree('getValue') ||'',
				page,
				rows,
			},function(data,status,xhr){
				if(data.state !== 1){
					$.messager.alert('提示', data.msg || data.message || '数据查询失败', 'error');
					throw new Error(data.msg || data.message || '数据查询失败')
				}
				window.project_boothdata_datagrid_rsp = data;
				rsp = data.rows || [];
			})
		}catch(e) {
			$.ajaxSettings.async = flag
			throw e;
		}
		return rsp;
	}
	function project_boothdata_datagrid_load(page=1,rows=20) {
		let $dg = $('#project-boothdata-datagrid');
		$dg.datagrid({
			cls:'project-boothdata-datagrid',rownumbers:true,singleSelect:false,
			pageSize:20,pagination:false,multiSort:true,
			fitColumns:true,fit:true,
			method:'post',selectOnCheck:true,border:false,
			onClickRow: function (rowIndex, rowData) {
				var l = $(this).datagrid('getRows').length;
				for(var i = 0; i < l; i++){
					if(i != rowIndex)$(this).datagrid('unselectRow', i);
				}
			},
			columns:[[
				{field:'ck',checkbox:true,title:""},
				{field:'sectionName',width:120,title:"展区",formatter:sectionName_type},
				{field:'boothCode',width:80,title:"展位号"},
				{field:'typeName',width:100,title:"展位类型"},
				{field:'specLength',width:80,title:"展位长度"},
				{field:'specWidth',width:80,title:"展位宽度"},
				{field:'boothSpec',width:100,title:"展位规格"},
				{field:'boothArea',width:90,title:"展位面积"},
				{field:'openInfoName',width:50,title:"开口情况"},
				{field:'salePrice',width:90,title:"售价"},
				...(project_boothdata_enableForeignCurrency ? [
					{field:'foreignPrice',width:90,title:"外币售价"},
					{field:'currencyName',width:90,title:"外币币种"},
				]:[]),
				{field:'productTypeNames',width:90,title:"展品范围"},
				{field:'reserveLock',width:50,title:"预留锁定",formatter:reserveLockName_type},
				{field:'graphId',width:50,title:"展位图",formatter:boothMapType_type},
				{field:'saleState', width:80,title:"销售状态",formatter:saleState_type},
				{field:'memo',width:120,title:"备注"},
				{field:'enableJointBooth',width:120,title:"是否拼展",formatter:project_boothdata_fmt_isunion},
			]],
			onLoadSuccess:function(data){
				$(".project-boothdata-datagrid-note").tooltip({
					position: 'top',
					onShow: function(){
						$(this).tooltip('tip').css({
							width:'300',
							backgroundColor: 'rgba(0,0,0,0.7)',
							borderColor: '#00000033',
							color: 'white',
							boxShadow: '1px 1px 3px #00000033'
						});
					}
				});
			}
		});
		const options = $dg.datagrid('options')
		$dg.datagrid('loading');
		let $pg = $('#project-boothdata-pager')
		project_boothdata_datagrid_getData(page, rows);
		const data = window.project_boothdata_datagrid_rsp;
		$dg.datagrid('loadData',data.rows).datagrid('loaded');
		const all = data.data || {}
		const tr = $('.project-boothdata-datagrid').find('.datagrid-header-inner:eq(1) tr:eq(0)').clone()
		tr.find('td').each((idx,it)=>{
			let field = options.columns[0][idx].field
			if(field === 'boothArea'){
				$(it).find('.datagrid-cell').html('<span>'+ (+all['countbootharea'] || 0) +'</span>');
			}  else if(field === 'ck') {
				$(it).find('div').html('')
			}  else if(field === 'salePrice') {
				$(it).find('.datagrid-cell').html('<span>'+ (+all['countsaleprice'] || 0).toFixed(2) +'</span>');
			}  else if(field === 'foreignPrice') {
				$(it).find('.datagrid-cell').html('<span>'+ (+all['countforeignprice'] || 0).toFixed(2) +'</span>');
			} else {
				$(it).find('.datagrid-cell').html('<span>'+ (idx!=1 ? (all[field] || '') : '总计') +'</span>');
			}
		})
		$('#project-boothdata-total').html('<table class="datagrid-footer datagrid-btable sumTable" cellspacing="0" cellpadding="0" border="0" style=""><tr><td class="datagrid-td-rownumber"><div class="datagrid-cell-rownumber"></div></td>' + tr.html() + '</tr></table>')
		// const $body = $('.project-boothdata-datagrid').find('.datagrid-body:eq(1)');
		// if($body.find('table.sumTable') && $body.find('table.sumTable').length) {
		// 		$body.find('.sumTable').html('<tr>' + tr.html() + '</tr>')
		// } else {
		// 		$body.append('<table class="datagrid-btable sumTable" cellspacing="0" cellpadding="0" border="0"  style="position:absolute;bottom:0;left:0;"><tr>' + tr.html() + '</tr></table>')
		// }
		$pg.pagination({
				total: data.total,
				pageNumber: page,
				pageSize: rows,
		})
	}
	function project_boothdata_tool_bottom_search(){
		//alert('点击了查询按钮触发事件！');
		// $('#project-boothdata-datagrid').datagrid({
		// 	cls:'sever-appliance-datagrid',
		// rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',border:false,selectOnCheck:true,pageList:[10,20,50,100,500],
		//         		onClickRow: function (rowIndex, rowData) {
		//         			var l = $(this).datagrid('getRows').length;
		//         			for(var i = 0; i < l; i++){
		//                			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		//                		}
		//  				}
		// 	url: eippath + '/exhibitBooth/getListBySectionCode1?sectionCode=' + sectionCode + '&exhibitCode='+ exhibitCode_for_pass,
		// 	queryParams: {
		// 		reserveLock: $('#project-boothdata-tool-bottom-reserveLock').val(),
		// 		openInfoId:  $('#project-boothdata-tool-bottom-openInfoId').val(),
		// 		productScope: $('#project-boothdata-tool-bottom-productTypeId').val(),
		// 		typeCode:$('#project-boothdata-tool-bottom-typeCode').val(),
		// 		boothAreaStart:$('#project-boothdata-tool-bottom-boothArea-from').val(),
		// 		boothAreaEnd:$('#project-boothdata-tool-bottom-boothArea-to').val(),
		// 		boothCode: ($('#project-boothdata-tool-bottom-boothCode').val() || '').trim(),
		// 		saleState:$('#project-boothdata-tool-bottom-saleState').combobox('getValue') || '',
		// 		boothMapType:$('#project-boothdata-tool-bottom-boothMapType').combobox('getValue') || '',
		// 	},
		// 	onLoadSuccess:function(data){
		// 		$(".project-boothdata-datagrid-note").tooltip({
		// 			position: 'top',
		// 			onShow: function(){
		// 				$(this).tooltip('tip').css({
		// 					width:'300',
		// 					backgroundColor: 'rgba(0,0,0,0.7)',
		// 					borderColor: '#00000033',
		// 					color: 'white',
		// 					boxShadow: '1px 1px 3px #00000033'
		// 				});
		// 			}
		// 		});
		// 	}
		// });
		project_boothdata_datagrid_load();
		// init pager
		let $pg = $('#project-boothdata-pager')
		$pg.pagination({
				// total: datas.total,
				pageSize: 20,
				pageList: [10,20,30,40,50],
				pageNumber: 1,
				onSelectPage:function(pageNumber, pageSize){
						$pg.pagination('loading');
						// console.log('pageNumber:'+pageNumber+',pageSize:'+pageSize);
						project_boothdata_datagrid_load(pageNumber,pageSize)
						$pg.pagination('loaded');
				}
		});
	}
</script>