<%@ page contentType="text/html;charset=UTF-8" %>
<div style=" float: right;position: fixed;">
	<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
</div>
 <br />
	<span style="font-size: 22px;margin-left: 20px; float: left;">LOGO</span>
	<span style="font-size: 22px;margin-left: 20px; float: left">2018上海家居展----观众服务管理中心</span>
			
		<span style="margin-left: 450px;">当前展会:</span>
		<select id="state" class="easyui-combobox" name="state"  labelPosition="top" style="width:200px;">
				<option value="AL">测试展会2</option>
				<option value="AK">测试展会3</option>
				<option value="AZ">测试展会4</option>
				<option value="AR">测试展会5</option>
				<option value="CA">测试展会6</option>
				<option value="CO">测试展会7</option>
			</select>
		 
	 
	 
	<div style="width: 80%;margin-left: 10%;min-height: 300px;border:1px solid  #00BBEE; margin-top: 50px;">
		<div style="margin-top: 20px;">
			<span style=" margin-left: 50px;">开展时间：2018年9月11日</span>
		<span style="float: right; margin-right: 50px;">闭展时间：2018年9月11日</span>
		</div>
	</div> 
	<div style="width: 80%;margin-left: 10%;border:1px solid  #00BBEE; margin-top: 50px;">
		 <p>观众总数量:26438人</p> 
		 <p>线下邀请数:主办邀请：3283人  展商邀请：5856人</p>
		 <p>系统登记数:线上预登记：6506人 现场描码：12396人  现场填表：2239人  现场名片：3281人 </p>
		 <p>领参观证数：现场领参观证：17526人</p>
		 <p>进展馆次数：18632 人次	</p>
	</div> 
	<script type="text/javascript">
		function setvalue(){
			$.messager.prompt('SetValue','Please input the value(CO,NV,UT,etc):',function(v){
				if (v){
					$('#state').combobox('setValue',v);
				}
			});
		}
	</script>

