<%@ page contentType="text/html;charset=UTF-8" %>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<script>
	$(".setdiv").hover(function(){
		$(this).find(".detialdiv").css("border","solid red 1px");
		$(this).find(".mode_1").toggle();
	},function(){
		$(this).find(".detialdiv").css("border","solid red 0px");
		$(this).find(".mode_1").toggle();
	})
	$(".setdiv").click(function (){
		var  linka =  $(this).find('a');
		var title = linka.text();
		var url = linka.attr('data-link');
		var iconCls = linka.attr('data-icon');
		var iframe = linka.attr('iframe') == 1 ? true : false;
		parent.addTab(title,url,iconCls,iframe); 
	})
	$("#BoothType").click(function (){
		
	})
		function openTab(title,href){
		    var e = $('#tabs').tabs('exists',title);
		    if(e){
		        $("#tabs").tabs('select',title);
		 
		        var tab = $("#tabs").tabs('getSelected');
		        $('#tabs').tabs('update',{
		            tab:tab,
		            options:{
		                title:title,
		                content:'<iframe name="indextab" scrolling="auto" src="'+href+'" frameborder="0" style="width:100%;height:100%;"></iframe>',
		                closable:true,
		                selected:true
		            }
		        });
		    }else{
		        $('#tabs').tabs('add',{
		            title:title,
		            content:'<iframe name="indextab" scrolling="auto" src="'+href+'" frameborder="0" style="width:100%;height:100%;"></iframe>',
		            iconCls:'',
		            closable:true
		        });
		    }
		}

	 
</script>
<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'north',split:true"  title="展会信息"   style="height:150px;"><!-- changeinfo -->
			<div class="my-toolbar-button" style=" ">
				项目名称：<select><option>上海科技博览会<option></select>
				<a href="#" class="easyui-linkbutton" iconCls="icon-reload"   onclick="reflush()">刷新</a>
				<a href="#" class="easyui-linkbutton" iconCls="icon-edit"   onclick="$('#changeinfo').window('open')">修改展会信息</a>
			</div>
				
				
				<div class="project-info-a">
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">展会名称：</span>
						<span style="display:inline-block;" id="project-info-exihibitName"></span>
					</div>
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">展会地点：</span>
						<span style="display:inline-block;" id="project-info-exhibitPlace"></span>
					</div>
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">展会年份：</span>
						<span style="display:inline-block;" id="project-info-yearMonth"></span>
					</div>
				</div>
				<div class="project-info-a">
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">开幕时间：</span>
						<span style="display:inline-block;" id="project-info-startDate"></span>
					</div>
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">闭幕时间：</span>
						<span style="display:inline-block;" id="project-info-endDate"></span>
					</div>
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">项目名称：</span>
						<span style="display:inline-block;" id="project-info-projectName"></span>
					</div>
				</div>
				<div class="project-info-a">
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">项目类型：</span>
						<span style="display:inline-block;" id="project-info-projKindName"></span>
					</div>
					<div style="width: 33%;float: left;">
						<span style="display:inline-block; width: 96px;">立项时间：</span>
						<span style="display:inline-block;" id="project-info-createTime"></span>
					</div>
				</div>
				
			</div>
			<div data-options="region:'center'" title="基本信息" style="width:100%;height:300px;">
				<div style=" border:0px solid  #00BBEE ; width: 100%;">
			<%-- <div id="Staffing"  class="setdiv-cancel" style=" border:1px  solid   #0081C2;margin:  40px 40px 40px 13%; width: 20%; text-align: center;float: left;height: 90px; position: relative; z-index: 60;">
				<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip"     data-link="${eippath}/backstage/project/project_staffing" iframe="0">展会人员设置（取消）</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
				
				
			</div> --%>
		<div class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 13%; width: 20%;float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/Supply&demand.html" iframe="0">供需匹配设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
		<div id="Areasetting" class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 40px; width: 20%;float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
				<br />
				<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/project_areasetting" iframe="0">展馆展区设置</a></p>
				
				<p class="mode_1" hidden="hidden">设置</p>
			</div>
		</div>
		<div id="BoothType" class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 40px; width: 20%;float: left;text-align: center;height: 90px;">
				<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/project_boothType" iframe="0">展位类型设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
	</div>
	
	<div style=" border:0px solid  #00BBEE ; width: 100%;">
		<div  class="setdiv"  style=" border:1px  solid   #0081C2;margin:  40px 40px 40px 13%; width: 20%; float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/project_boothdata" iframe="0">展位资料设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
		<div  class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 40px; width: 20%;float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/Floorplan.html" iframe="0">展位图设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
		<div class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 40px; width: 20%;float: left;text-align: center;height: 90px;">

			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/project_document" iframe="0">展会文档设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div> 
		</div>
	</div>
	
		<div  style=" border:0px solid  #00BBEE ; width: 100%;">
		<div class="setdiv" style=" border:1px  solid   #0081C2;margin:  40px 40px 40px 13%; width: 20%; float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/SalesReg.html" iframe="0">展商登记设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
		<div  class="setdiv" style="border:1px  solid   #0081C2;margin:  40px 40px 40px 40px; width: 20%;float: left;text-align: center;height: 90px;">
			<div  class="detialdiv" style="border: 0px solid red;height: 100%;text-align: center; position: relative;z-index: 90;">
					<br />
					<p class="mode_1"><a href="javascript:void(0);" data-icon="icon-tip" data-link="${eippath}/backstage/project/BuyerReg.html" iframe="0">观众登记设置</a></p>
					<p class="mode_1" hidden="hidden">设置</p>
				</div>
		</div>
	</div>
				
			</div>
		   	 
		   	
		</div>
	<script type="text/javascript">
		function setvalue(){
			$.messager.prompt('SetValue','Please input the value(CO,NV,UT,etc):',function(v){
				if (v){
					$('#state').combobox('setValue',v);
				}
			});
		}
	</script>
	<div id="changeinfo"    class="easyui-window" title="修改展会信息" data-options="modal:true,closed:true,iconCls:'icon-save'" style="min-width: 1060px;width: 1300px;height:auto;padding:10px;">
	 	<div style="border:1px  solid   #31B0D5;min-width: 1060px;width: 100%; float: left;">
		<div style="width: 100%; float: left;">
			<div style="width: 50%; float: left;">
				<img  style="margin:10px 10px 10px 10px ; height: 256px; width: 95%;" src="../img/img.png"/>
				
			</div>
			<div style="width: 50%; float: left;">
				<div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">展会名称：</span>
						<input type="text" class="easyui-textbox" />
					</div>
					 <div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">客户名称：</span>
						<select class="easyui-combobox" style="width: 170px;">
							<option>杭州海派家具有限公司</option>
						</select> 
					</div>
				</div>
				<div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 99%;float: left;">
						<span style="display:inline-block; width: 96px;">展会名称：</span>
						<input type="text" class="easyui-textbox" style="width: 79%;" />
					</div>
				</div>
				<div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 99%;float: left;">
						<span style="display:inline-block; width: 96px;">展会图标：</span>
						<input type="text" class="easyui-textbox" style="width: 59%;" />
						<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-filesave'"   onclick="$('#changeinfo').window('open')" data-link="Exhibitorinfo.html" iframe="0" style="width:20%;">选择文件</a>
					</div>
				</div>
				<div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">举办国家/地区：</span>
						<input type="text" class="easyui-textbox" />
					</div>
					 <div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">举办城市：</span>
						<input type="text" class="easyui-textbox" /> 
					</div>
				</div><div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">举办年份：</span>
						<input type="text" class="easyui-textbox" />
					</div>
					 <div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">开展时间：</span>
						<input type="text" class="easyui-textbox" /> 
					</div>
				</div>
				
				<div class="" style="min-width: 605px;margin-top: 20px;margin-bottom: 20px; height: 20px;">
					<div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">立项时间：</span>
						<input type="text" class="easyui-textbox" />
					</div>
					 <div style="width: 50%;float: left;">
						<span style="display:inline-block; width: 96px;">闭幕时间：</span>
						<input type="text" class="easyui-textbox" /> 
					</div>
				</div>
			</div>
			
		</div>
		<div style="width: 100%; float: left;margin:10px 10px 10px 10px;float: left ; ">
			<span style="display:inline-block; width: 96px;">项目性质</span>
			<br />
			<div style="margin:10px 10px 10px 10px ; border:1px solid #31B0D5;width: 20%;float: left;height: 36px;padding-top: 10px;">
				<form>
					<input type="radio"  id="myself" name="exhi"/><label for="myself">自办展</label>
					<input type="radio"  id="other" name="exhi"/><label for="other">代理展</label>
				</form>
			</div>
			<div style="margin:10px 10px 10px 10px ;height: 36px; border:1px solid #31B0D5;width: 70%;float: left;padding-top: 10px;">
					<span style="display:inline-block; width: 96px;">客户名称：</span>
						<select class="easyui-combobox" style="width: 170px;">
							<option>杭州海派家具有限公司</option>
						</select> 
						
						<span style="display:inline-block; width: 96px;margin-left: 60px;">客户名称：</span>
						<select class="easyui-combobox" style="width: 170px;">
							<option>杭州海派家具有限公司</option>
						</select> 	
			</div>
		
		</div>
		<div style="width: 100%; float: left;margin:10px 10px 10px 10px;float: left ; ">
				<div>
					<span style="display:inline-block; width: 96px;float: left ;">服务人员设置</span>
				<div style="float: left ;">
					<form>
					<input type="radio"  id="gawei" name="exhi"style="margin-left: 200px;" /><label for="gawei">按岗位</label>
					<input type="radio"  id="human" name="exhi"/><label for="human">按人员</label>

					</form>
				</div>
				<div>
					<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-no'" style="width:80px;float: right;margin-right: 50px;margin-left: 10px;">删除</a>
							<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" style="width:80px;float: right;margin-left: 10px;">修改</a>
							<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add'" style="width:80px;float: right;margin-left: 10px;">添加</a>
				</div>
				</div>
				
		</div>
		<table id="tt" class="easyui-datagrid" style="width:100%"
		url="data/datagrid_data.json"
		 iconCls="icon-save"
		>
	<thead>
		<tr>
			<th field="itemid" width="10%">岗位角色</th>
			<th field="productid" width="86%">人员名称</th>
		</tr>
	</thead>
	<tbody>
		 <tr>
	      <td>销售</td>
	      <td>张三</td>
	    </tr>
	    <tr>
	      <td>销售审批人</td>
	      <td>李四</td>
	    </tr>
	     <tr>
	      <td>客服</td>
	      <td>王五</td>
	    </tr>
	     <tr>
	      <td>客服审核人</td>
	      <td></td>
	    </tr>
	     <tr>
	      <td>签证</td>
	      <td>赵六</td>
	    </tr>
	     <tr>
	      <td>外联</td>
	      <td>詹姆斯</td>
	    </tr>
	     <tr>
	      <td>机票</td>
	      <td>鹿晗</td>
	    </tr>
	     <tr>
	      <td>酒店</td>
	      <td>陈小春</td>
	    </tr>
	     <tr>
	      <td>地接</td>
	      <td>刘能</td>
	    </tr>
		
	</tbody>
</table>
			 
	</div>


	
	</div>
	
<div id="boothtypemodel" class="easyui-window" title="展位类型设置" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:800px;height:auto;padding:10px;">
 	<h2>2018上海家居展  -- 展位类型设置        </h2>
		
	
	
	
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#boothtypemodel').window('close')">关闭</a>
	</div>
	
	
