<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>

<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'west',border:true,split:true" style="width:200px;padding:10px;">
		<ul id="project-document-task-tree" class="easyui-tree"
                    data-options="method:'post',animate:true"></ul>
     
    </div>
    <div data-options="region:'center',border:false" id="project-document-task-set">
   			
    </div>
</div>
     <!-- <div class="easyui-layout" data-options="fit:true">
            <div data-options="region:'north',border:true,split:true," style="padding:10px;">
                <a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
                <a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">保存</a>
            </div>
            <div data-options="region:'center'">
                
            </div>
        </div> -->

<script type="text/javascript" >
	
	var project_document_tree_node = "";
	var project_document_task_id = 0;
	var project_document_task_kind = "";

	$(function(){
		project_document_task_tree_fresh();
	})
	
	function project_document_task_tree_fresh() {
	    $('#project-document-task-tree').tree({
	        url: eippath + '/examine/makeTreeJson?projectId=' + project_for_pass,
	        onClick: function (node) {
	        	//alert(JSON.stringify(node))	
	       
	        	project_document_load_set(node);
	        
	        },
	        onLoadSuccess:function(node, data){
	        	
	        }
	    });
	}
	
	function project_document_load_set(node){
		if(node==""||node.id==0)return;
		project_document_tree_node = node;
		project_document_task_id = node.id;
		project_document_task_kind = node.taskKindCode;
        console.log(node.taskKindCode);
        var page = "";
		if(node.taskKindCode == 'CATA'){			    
			page = 'set_cata' ;
		}else if(node.taskKindCode == 'COMPANY'){
			page = 'set_company' ;
		}else if(node.taskKindCode == 'PROD'){
			page = 'set_product' ;
		}else if(node.taskKindCode == 'BOOTH'){
			page = 'set_booth' ;
		}else if(node.taskKindCode == 'CATA_NORMAL'){
			page = 'set_cata_normal' ;
		}else if (node.taskKindCode == 'PERSON'){//人员签证
            page = 'set_person' ;
        }else if (node.taskKindCode == 'FILE'){
            page = 'set_file' ;
        }else if (node.taskKindCode == 'INVITE'){//外展邀请函
            page = 'set_invite' ;
        }else if (node.taskKindCode == 'CERT'){//进馆证
		    page = 'set_cert' ;
		}else if (node.taskKindCode == 'TRANS'){//翻译
		    page = 'set_trans' ;
		}else if (node.taskKindCode == 'WRITE_INSTRUCTIONS'){//填写说明
		    page = 'set_filling_explanation' ;
		}else if (node.taskKindCode == 'BUILDER_BOOTH_TOOL'){//搭建方
		    page = 'set_construct' ;
		}
     	if(page == ""){
     		 $('#project-document-task-set').html('<h3 style="text-align: center">暂未开启</h3>');
     		 //$.messager.alert('提示', '暂未开启！', 'info');
     		return;
     	}
    	 // return;	
		  $.ajax({
              url: eippath + '/set/'+ page,
              dataType: "html",
              type: "get",
              async: false,
              success: function (data) {
                  $('#project-document-task-set').html(data);
                  $.parser.parse($('#project-document-task-set')); 
                  $("#set-top-title").html(node.text)
                  
              },
              error: function () {
                  $.messager.alert('提示', '数据发送失败！', 'error');
              }
          });
      
	}
   	 function refresh_set(){
   		project_document_load_set(project_document_tree_node);
   	 }
   	 function fileName_click(val, row, index) {
        
         let newVar = val==undefined?'下载':val;
		 let url = val==undefined?row.fileExplainAttach:row.fileAttach;
		 let goOpen = url.indexOf("http://zhanzhiyun.oss");
		 if(goOpen == -1){
			 var attachUrl =  "http://"+window.location.hostname+":"+window.location.port+"/image";
			 url = attachUrl + url
			 return '<a href="javascript:void(0);" filePath="'+ url+'" fileName="'+ row.fileName +'" onclick="downfiles(this)" >' +newVar + '</a>';
		 }else{
			return '<a href="javascript:void(0);" filePath="'+ url+'" fileName="'+ row.fileName +'" onclick="downfiles(this)">' +newVar + '</a>';
		 }
     }
	 function downfiles(obj){
		 var a = document.createElement('a');
		 var filePath = $(obj).attr("filePath")
		 // if(filePath.indexOf("http://zhanzhiyun.oss-cn-beijing.aliyuncs.com") != -1){
		 //    filePath = filePath;
		 // }else{
		 // 	filePath = filePath;
		 // }
		 a.href = filePath;
		 a.download = $(obj).attr("fileName");
		 a.click();
	 }
   	 function project_document_upolad_attachment(obj,type){
         console.log(obj);
         var file =  $(obj).filebox('files')[0]
         if(file == null){
             $.messager.alert('提示','请选择附件！','warning');
             return;
         }
         var formData = new FormData();
         formData.append("file", file);
		 $.ajax({
		     url: eippath+ "/upload/uploadFile",
		     dataType: "json",
		     type: "post",
		     data: formData,
		     processData: false,
		     contentType: false,
		     success: function(data){
		         if (type==1){
		        	 $("#set-file-textbox-fileAttach").textbox("setValue",data.result.path);
		             $("#set-file-textbox-fileName").textbox("setValue",data.result.fileName);
		         }else if (type==2){
		             $("#set-file-textbox-fileExplainAttach").textbox("setValue",data.result.path)
		         }else {
		             project_document_show_attachment(data.result.fileName,data.result.path);
		         }
		         $.messager.alert('提示', '上传成功！', 'info');
		     },
		     error: function(){
		     }
		 });
		 
         // $.ajax({
         //     url: eippath+ "/upload/uploadFileOss",
         //     dataType: "json",
         //     type: "post",
         //     data: formData,
         //     processData: false,
         //     contentType: false,
         //     success: function(data){
         //         if (type==1){
         //        	 $("#set-file-textbox-fileAttach").textbox("setValue",data.result.path);
         //             $("#set-file-textbox-fileName").textbox("setValue",data.result.fileName);
         //         }else if (type==2){
         //             $("#set-file-textbox-fileExplainAttach").textbox("setValue",data.result.path)
         //         }else {
         //             project_document_show_attachment(data.result.fileName,data.result.path);
         //         }
         //         $.messager.alert('提示', '上传成功！', 'info');
         //     },
         //     error: function(){
         //     }
         // });
   	 }
   	 function project_document_show_attachment(fileName,path){
         var html = "<a id='general-settings-attachment' attrName='"+fileName+"' attrsrc='" + path + "'" +
         " href='javascript:void(0);' onclick='project_document_downAttch(this)'>"+fileName+"</a><span class='upfile_del' onclick='project_document_delete_attachment()'><img src='../../img/close.png' ></span>" ;
         $("#general-settings-attachment-show").html(html);
   	 }
   	 function project_document_delete_attachment(){
         $("#general-settings-attachment-show").html('');
   	 }
   	 /**
 	 * 下载附件
 	 * @param obj
 	 */
 	function project_document_downAttch(obj) {
		var variablePic ="http://"+ window.location.hostname+":"+window.location.port;
		let jQuery = $(obj).attr("attrsrc");
		if (jQuery.indexOf("http") == -1)
			jQuery = variablePic + "/image/" + jQuery;
		var a = document.createElement('a');
		a.href = jQuery;
		a.download = $(obj).attr("attrName");
		a.click();
 	}
 		
</script>