<!-- 活动推广设置 : 文件拆分 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="2022030711" />
    <!-- <meta http-equiv='x-dns-prefetch-control' content='on'>
    <link rel='dns-prefetch' href='//placehold.it'> -->
	<title>活动推广设置</title>
	<link rel="stylesheet" href="../../css/colpick.css" type="text/css" />

    <script src="../../js/colpick.js"></script>
    <script src="../../js/wangEditor.min.js"></script>
	<script>var eippath = '${eippath}'</script>
    <style>
    .projectSettings {
        width: 100%;
    }

    .picker {
        position: absolute;
        top: 0px;
    }

    ul, li {
        list-style: none;
    }

    p {
        margin: 0;
        padding: 0;
    }

    .project-info1 > ul > li {
        margin-top: 16px;
        box-sizing: border-box;
        padding-left: 50px;
    }

    label {
        margin-bottom: 10px;
        display: inline-block;
    }

    .ke-container, .ke-container-default {
        height: 256px;
        width: 80% !important;
    }

    .ke-edit {
        height: 230px !important;
    }

    .datagrid-toolbar {
        background: #F4F4F4;
        /*  position: fixed;  */
        /*   width: 100%; */
    }

    .project-info1 {
        margin-top: 35px;
    }

    .project-info-img {
        width: 79%;
        margin-left: 40px;
    }

    .project-info-img img {
        width: 150px;
        height: 150px;
        overflow: hidden;
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }

    .ke-edit-iframe {
        height: 100% !important;
    }

    .product-img {
        position: relative;
        width: 320px;
        height: 224px;
        background-color: #ffffff;
        float: left;
        margin-right: 20px;
        margin-bottom: 20px;
        display: none;
    }

    .product-img img {
        display: block;
        width: 320px;
        height: 168px;
        border: solid 1px #cccccc;
    }

    .ke-content {
        color: #999;
    }

    .template_set {
        box-sizing: border-box;
        padding-left: 10px;
        margin-top: 20px;
    }

    .template_set_pc h6 {
        font-size: 15px;
        color: #333;
        font-weight: normal;
    }

    .template_set_pc ul li {
        display: block;
        box-sizing: border-box;
        margin: 0 50px 10px 50px;
        padding-bottom: 14px;
        overflow: hidden;
    }

    .template_set_pc ul li > label {
        display: block;
    }

    .template_set_pc {
        overflow: hidden;
        margin-bottom: 10px;
    }

    .my-toolbar-button {
        background-color: #f5f5f5;
        padding: 6px 5px;
    }

    .upFileDivButton {
        font-style: normal;
        margin: 0 20px 0 10px;
        display: inline-block;
        position: relative;
    }

    .upFileDivFileButton {
        width: 60px;
    }

    .upFileDivButton > span {
        opacity: 0;
        position: absolute;
        left: 0;
    }

    .upFileDivP {
        display: inline-block;
    }

    .upFileImgDiv {
        width: 80px;
        height: 80px;
        border: solid 1px #cccccc;
        margin-top: 10px;
    }

    .upFileDivButton > em {
        width: 64px;
        height: 30px;
        background-color: #ffffff;
        border-radius: 4px;
        border: solid 1px #b2b2b2;
        color: #333333;
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        font-style: normal;
        display: inline-block;
        cursor: pointer;
    }

    .selectColor > span {
        display: inline-block;
        border-radius: 4px;
        box-sizing: border-box;
        padding: 6px;
        cursor: pointer;
    }
    .selectColorspan{border: 1px solid transparent;}
    .selectColor > span > i {
        width: 18px;
        height: 18px;
        border-radius: 4px;
        display: inline-block;
        vertical-align: bottom;
    }

    .selectColor {
        display: inline-block;
    }

    .activeBorder {
        border: solid 1px #95b8e7;
    }

    .upFileImgDiv2 {
        width: 300px;
        height: 94px;
        border: solid 1px #cccccc;
        margin-top: 10px;
        margin-bottom: 45px;
    }

    .upFileImgDiv > img, .upFileImgDiv2 > img {
        width: 100%;
        height: 100%;
        display: block;
        overflow: hidden;
    }

    .upBannerStyle {
        margin-top: 16px;
        height: 30px;
        background-image: linear-gradient(0deg, #efefef 0%, #fefefe 100%);
        border-radius: 4px;
        border: solid 1px #95b8e7;
        color: #5567a5;
        font-size: 14px;
        padding: 0 10px;
        outline: none;
        cursor: pointer;
    }

    .upBannerLiStyle {
        border: dotted 1px #cccccc;
        padding: 10px 0px 10px 20px !important;
        margin: 0 50px;
        margin-bottom: 10px;
    }

    .delBannerPc, .hideLink {
        display: none !important;
    }

    .delBannerMb {
        display: none !important;
    }

    .pcHide, .showLink {
        display: block !important;
    }

    .mbHide {
        display: block !important;
    }

    .webSetStyle {
        width: 172px;
        height: 34px;
        background-color: #ffffff;
        border-radius: 4px;
        border: solid 1px #999999;
        display: inline-block;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #666;
        margin-right: 24px;
        cursor: pointer;
    }

    .webSetStyleActive {
        background-color: #f2f5ff;
        border: solid 1px #5567a5;
        color: #5567a5;
    }

    .set-color-div {
        float: left;
        margin-right: 20px;
        margin-bottom: 10px;
    }

    .set-color-div > label {
        margin: 0 16px;
    }

    .set-color-div > i {
        vertical-align: bottom;
        width: 18px;
        height: 18px;
        border-radius: 4px;
        display: inline-block;
        cursor: pointer;
        border: 1px solid #ccc;
    }

    .isShowLinkDiv {
        border: dotted 1px #cccccc;
        padding: 10px 20px !important;
    }

    .showEg {
        display: none;
    }

    </style>
    <style>
    .textbox .textbox-button-right {
        width: 100%;
    }

    #links-container, #footerLinkList {
        border: dotted 1px #cccccc;
    }

    #links-container .links-item, #footerLinkList .link-item{
        margin: 0 0 10px;
        padding: 10px;
    }

    #footerLinkList .link-item{
        display: flex;
    }
    #footerLinkList .link-item>div:nth-of-type(1){
        margin-right: 40px;
    }

    #links-container .links-item .up > div {
        margin-right: 30px;
        float: left;
    }

    #links-container .links-item .up > div:last-of-type {
        margin-right: 0;
        float: none;
    }

    #links-container .links-item .links-item-name {
        transform: translateY(3px);
    }

    #links-container .links-item .delete {
        margin-right: 0;
    }

    #links-container .links-item .clear {
        margin-left: -12px;
        margin-right: 0;
    }

    #links-container .links-item .down {
        display: flex;
    }

    #links-container .links-item .down > div {
        flex-basis: 485px;
        border: none;
    }

    #links-container .links-item .down > div > img {
        width: 191px;
        height: 76px;
    }

    #links-container .links-item .down > p {
        margin-top: 30px;
    }
    </style>
    <style>
        .label-inner{
            margin-bottom: 0;
            vertical-align: top;
            cursor: pointer;
            margin-right: 6px;
        }
        .label-inner:hover{
            text-decoration: underline;
        }
        .line{
            width: auto;
            height: 1px;
            margin: 20px 50px;
            background-color: #ccc;
        }
        .upFileDivP{
            color: #666;
        }
    </style>
    <!--引入引入kindeditor编辑器相关文件-->
    <script charset="utf-8" src="${eippath}/kindeditor/kindeditor-all.js"></script>
    <script charset="utf-8" src="${eippath}/kindeditor/lang/zh-CN.js"></script>
    <style>
        body{ margin: 0 }
    </style>
</head>
<body>
<div class="easyui-tabs" id="tab-panel" style="width:100%;height:100%;padding:5px;" data-options="tabPosition:'top'">
    <div title="邀请函模板设置" style="width:100%;height:100%;">
        <div id="" style="position:sticky;top:0;z-index:999">
            <div class="my-toolbar-button">
                <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="loadTempData()"
                   plain="true">刷新</a>
                <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="seveTemp4()"
                   plain="true">保存</a>
            </div>
        </div>
        <div class="project-info1">
            <ul>
                <li>
                    <label>移动端邀请函模板:</label>
                    <div class="">
                        <input id="projectInfo_invitation" class="easyui-combobox" style="width:350px;height:25px"
                               data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList: true,
		        					data:[{id:'invitation-letter-standard',text:'标准模板'},{id:'invitation-letter-standard-old',text:'标准模板（可点赞留言）'},{id:'invitation-letter-m',text:'农业模板'},{id:'mobile-invitation-letter3',text:'其他模板'},{id:'invitation-letter-m-hrb',text:'食品模板'},{id:'mobile-invitation-letter',text:'厂商采购模板'}]">
                    </div>
                </li>
                <li>
                    <label>
                        底部按钮链接设置
                    </label>
                    <ul id="footerLinkList">
                        <li class="link-item">
                            <div>
                                <label>名称1</label>
                                <style>
                                    .links-item-1{ color: #333; }
                                    .hide{ display: none !important; }
                                    .links-item-1>div{ display: inline-block; }
                                    .links-item-1 .dot{ display: inline-block; width: 6px;height: 6px;border-radius: 50%;margin-right: 6px; }
                                    .links-item-1 .green{ background: #007b42; }
                                    .links-item-1 .red{ background: #f3ba1d; }
                                </style>
                                <div class="links-item-1">
                                    <div style="color: #999;margin-right: 20px;">观众登记链接</div>
                                    <div class="hide">
                                        <span class="dot green"></span>已发布
                                    </div>
                                    <div class="hide">
                                        <span class="dot red"></span>未发布
                                    </div>
                                </div>
                            </div>
                        </li>
                        <!--<li class="link-item">
                            <div>
                                <label>名称1</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkName1" style="width:200px;" data-options="prompt:'六个字以内',disabled:true,validType:'length[2,5]'">
                                </div>
                            </div>
                            <div>
                                <label>链接1</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkAddress1" style="width:400px;" data-options="disabled:true,prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                                </div>
                            </div>
                        </li>-->
                        <li class="link-item">
                            <div>
                                <label>名称2</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkName2" style="width:200px;" data-options="prompt:'六个字以内',validType:'length[2,5]'">
                                </div>
                            </div>
                            <div>
                                <label>链接2</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkAddress2" style="width:400px;" data-options="prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                                </div>
                            </div>
                        </li>
                        <li class="link-item">
                            <div>
                                <label>名称3</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkName3" style="width:200px;" data-options="prompt:'六个字以内',validType:'length[2,5]'">
                                </div>
                            </div>
                            <div>
                                <label>链接3</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox" id="footerLinkAddress3" style="width:400px;" data-options="prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                                </div>
                            </div>
                        </li>
                    </ul>
                </li>
                <li>
                    <label>移动端官网首页链接</label>
                    <div class="upFileDiv">
                        <input class="easyui-textbox" id="officialWebsiteLink" style="width:350px;">
                        <p class="upFileDivP" style="margin-left: 10px;">
                            <span>填写之后，用户在邀请函上跳转到官网上；若不填写邀请函上则不显示线上展厅按钮</span></p>
                    </div>
                </li>
                <li>
                    <label>移动端邀请函顶部背景图</label>
                    <div class="upFileDiv">
                        <input class="easyui-textbox" id="upTempBgiName2" style="width:350px;">
                        <i class="upFileDivButton">
                            <em>上传</em>
                            <input id="upTempBgi2" class="easyui-filebox upFileDivFileButton" name="upfile"
                                   data-options="buttonText:'选择',prompt:''">
                        </i>
                        <p class="upFileDivP"><span>规格要求：750*890</span><span style="margin-left: 24px;">建议小于200K</span>
                        </p>
                    </div>
                    <div class="upFileImgDiv2" style="width: 286px;height: 338px;">
                        <img src="" alt="" id="upTempBgiImg2">
                    </div>
                </li>
                <li>
                    <label>移动端邀请函底部背景图</label>
                    <div class="upFileDiv">
                        <input class="easyui-textbox" id="upTempBgiName3" style="width:350px;">
                        <i class="upFileDivButton">
                            <em>上传</em>
                            <input id="upTempBgi3" class="easyui-filebox upFileDivFileButton" name="upfile"
                                   data-options="buttonText:'选择',prompt:''">
                        </i>
                        <p class="upFileDivP"><span>规格要求：750*726</span><span style="margin-left: 24px;">建议小于100K</span>
                        </p>
                    </div>
                    <div class="upFileImgDiv2" style="width: 286px;height: 276px;">
                        <img src="" alt="" id="upTempBgiImg3">
                    </div>
                </li>
                <div class="line"></div>
            </ul>
        </div>
    </div>
    <div title="邀请活动设置" style="width:100%;height:100%;">
        <div>
            <div class="my-toolbar-button">
                <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="loadTempData()"
                plain="true">刷新</a>
                <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="saveInvitationEventSetting()"
                plain="true">保存</a>
            </div>
        </div>
        <div class="project-info1">
        <ul>
            <li>
            <label>邀请函热度排行榜顶图</label>
            <div class="upFileDiv">
                <input class="easyui-textbox" id="invitationEventTopBanner" style="width:350px;">
                <i class="upFileDivButton">
                <em>上传</em>
                <input id="invitationEventTopBannerUploader" class="easyui-filebox upFileDivFileButton" name="upfile"
                        data-options="buttonText:'选择',prompt:''">
                </i>
                <p class="upFileDivP"><span>规格要求：750*328</span><span style="margin-left: 24px;">建议小于200K</span>
                </p>
            </div>
            <div class="upFileImgDiv2" style="width: 400px;height: 175px;">
                <img src="" alt="" id="invitationEventTopBannerCover">
            </div>
            </li>
            <li>
            <label>活动规则: <span style="color: #999;">（由于展商使用手机端比较频繁，建议排版符合手机端阅读，不使用大段标题样式~）</span></label>
            <div id="invitationEventRules"></div>
            </li>
        </ul>
        </div>
  </div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
    var url = (new URL(window.location.href)).searchParams
    var project_for_pass = url.get('projectId')
    let editorMenus = [
        // 菜单配置
        'head', // 标题
        'bold', // 粗体
        'fontSize', // 字号
        'fontName', // 字体
        'italic', // 斜体
        'underline', // 下划线
        'strikeThrough', // 删除线
        'foreColor', // 文字颜色
        'backColor', // 背景颜色
        'link', // 插入链接
        'list', // 列表
        'justify', // 对齐方式
        'quote', // 引用
        'undo', // 撤销
        'image', // 插入图片
        'redo' // 重复
    ];
    let pcActive = 1; //1 中文 0 英文
    const E = window.wangEditor
    const contactWays = new E('#contactWay')
    contactWays.customConfig.menus = editorMenus;
    contactWays.customConfig.uploadImgMaxSize = 204800
    contactWays.customConfig.zIndex = 1e3
    contactWays.customConfig.uploadImgShowBase64 = false
    contactWays.customConfig.uploadImgServer = '${eippath}/upload/uploadFile' // 填写配置服务器端地址
    contactWays.customConfig.uploadFileName = 'file' // 后端接受上传文件的参数名
    contactWays.customConfig.uploadImgParams = {
        file_type: 'img'
    }
    contactWays.customConfig.uploadImgHooks = {
        customInsert: (insertImgFn, result, contactWays) => {
            let url = result.result.path
            JSON.stringify(url)
            insertImgFn("http://" + window.location.hostname + ":" + window.location.port + '/image' + url)
        }
    }
    contactWays.create()
    const invitationEventRules = new E('#invitationEventRules')
    invitationEventRules.customConfig = Object.assign({}, contactWays.customConfig)
    invitationEventRules.customConfig.menus = [
        // 菜单配置
        'head', // 标题
        'bold', // 粗体
        'fontSize', // 字号
        'fontName', // 字体
        'italic', // 斜体
        'underline', // 下划线
        'strikeThrough', // 删除线
        'foreColor', // 文字颜色
        'backColor', // 背景颜色
        'link', // 插入链接
        'list', // 列表
        'justify', // 对齐方式
        'quote', // 引用
        'undo', // 撤销
        'redo' // 重复
    ]
    invitationEventRules.create()

    var colorIndex    = 0 //自助移动端 选取颜色的下标
    var colorIndexPc  = 0 //自助pc主色 选取颜色的下标
    var colorIndex2Pc = 0 //自助pc辅色 选取颜色的下标
    var colorIndex3Pc = 0 //自助pc登录页辅色1 选取颜色的下标
    var colorIndex4Pc = 0 //自助pc登录页辅色2 选取颜色的下标
    var colorIndex5Pc = 0 //自助pc辅色2 选取颜色的下标
    $('.color-box').colpick({
        colorScheme: 'dark',
        layout: 'rgbhex',
        color: '28ae39',
        onSubmit: function (hsb, hex, rgb, el) {
            $(el).css('background-color', '#' + hex);
            $(el).colpickHide();
        }
    })

    // 删除上传文件的赋值
    function exhibitionTemplateDelImg(idPre, idSuffix=''){
        if(idPre){
            $('#'+ idPre + 'Url' + idSuffix).attr('src', '')
            $('#' + idPre + idSuffix).textbox('clear')
        }
    }
    function setColors(that, type) {
        let color = $(that).css('background-color')
        let color2 = rgb2hex(color)
        $('.set-color-box' + type).colpick({
            colorScheme: 'dark',
            layout: 'rgbhex',
            color: color2,
            onSubmit: function (hsb, hex, rgb, el) {
                $(el).css('background-color', '#' + hex);
                $(el).colpickHide();
                if (type == 7) {
                    console.log("111")
                    let color3 = rgb2hex($('.set-color-box8').css('background-color'))
                    $('#webGradientColorPc').css({
                        'background': 'linear-gradient(to right, #' + hex + ' 0%, #' + color3 + ' 100%)'
                    })
                } else if (type == 8) {
                    let color4 = rgb2hex($('.set-color-box7').css('background-color'))
                    $('#webGradientColorPc').css({
                        'background': 'linear-gradient(to right, #' + color4 + ' 0%, #' + hex + ' 100%)'
                    })
                }
            }
        })
    }

    function rgb2hex(rgb) {
        rgb = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);

        function hex(x) {
            return ("0" + parseInt(x).toString(16)).slice(-2);
        }

        return hex(rgb[1]) + hex(rgb[2]) + hex(rgb[3]);
    }

    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        return r != null ? unescape(r[2]) : null;
    }

    function isOpenLink(type) {
        let text
        if (type == 1) {
            window.linkManager.changeDisplay($("#enableBlogrollPc").prop('checked'))
        } else if (type == 2) {
            text = $("#enableContactWayPc").prop('checked')
            if (text == true) $('.isOpenContact').show()
            else $('.isOpenContact').hide()
        }
    }

    // 移动端邀请函顶部背景图
    $("#upTempBgi2").filebox({
        onChange: function (n, o) {
            var file = $("#upTempBgi2").filebox("files")[0];
            uploadFileImg(file, 3)
        }
    });
    // 移动端邀请函底部背景图
    $("#upTempBgi3").filebox({
        onChange: function (n, o) {
            var file = $("#upTempBgi3").filebox("files")[0];
            uploadFileImg(file, 4)
        }
    });

    // 邀请函热度排行榜顶图
    $("#invitationEventTopBannerUploader").filebox({
        onChange: function (n, o) {
            uploadFileImg($(this).filebox("files")[0], 21)
        }
    });

    //邀请函模板设置保存
    function seveTemp4() {
      const footerData = operationFooterLinks('export')
      let errIndex = 0, errInfo = ''
      /**
       三个名称和链接都是必填项，
       如果用户填写了名称1未填写链接1，保存时提醒他填写链接1
       填写了链接未填写名称的同样处理；
       可以空白，名称和链接都不填时，可以成功保存；
       */
      const result = footerData
        .every((item, index)=>{
          errIndex = index + 1
          if((!item['name'] && !item['link']) || (item['name'] && item['link']))return true
          if(!item['name'])errInfo = '名称'
          if(!item['link'])errInfo = '链接'
        })
      if(!result)return $.messager.alert('提示', `请填写\${errInfo}\${errIndex}！`, 'warning')
      console.log(JSON.stringify(footerData))
      $.ajax({
            url: "${eippath}/project/information/saveInvitationSetting",
            data: {
                'projectId': GetQueryString("projectId"),
                'invitation': $("#projectInfo_invitation").combobox("getValue"),
                'inviteTopBgImg': $("#upTempBgiName2").textbox("getValue"),
                'inviteTopBgImgUrl': $("#upTempBgiImg2").attr("src"),
                'inviteBottomBgImg': $("#upTempBgiName3").textbox("getValue"),
                'inviteBottomBgImgUrl': $("#upTempBgiImg3").attr("src"),
                'officialWebsiteLink': $("#officialWebsiteLink").textbox('getValue'),
                'inviteLinks': JSON.stringify(footerData)
            },
            type: "POST",
            dataType: "json",
            success: function (data) {
                if (data.state == 1) {
                    $.messager.alert('提示', '保存成功！', 'info');
                } else {
                    $.messager.alert('提示', '保存失败！', 'error');
                }
            },
            error: function (xhr, ajaxoptions, thrownError) {
                // alert(xhr.responseText);
            }
        });

    }


    //加载邀请活动设置
    function saveInvitationEventSetting(){
        $.ajax({
            url: eippath + '/project/information/saveInvitationEventSettings',
            data: {
                projectId: project_for_pass,
                invitationEventRules: invitationEventRules.txt.html(),
                invitationEventTopBanner: $("#invitationEventTopBannerCover").attr('src'),
            },
            dataType: "json",
            type: "post",
            success(resp){
                if(resp.state != 1)
                    return $.messager.alert('错误', '数据加载失败！', 'error')
                $.messager.alert('提示', '保存成功！', 'info')
                loadTempData()
            }
        })
    }
    // 回显
    //  邀请函模板设置
    //  邀请活动设置
    function loadTempData(type) {
        let projectId =  GetQueryString("projectId") || 0
        $.ajax({
            url: "${eippath}/project/information/getInformationByProjectId ",
            dataType: "json",
            type: "POST",
            data: { projectId },
            success: function (data) {
                if (data.state == 1) {
                    // 查询项目观众登记是否发布
                    loadTempDataSet(data, type)
                    $.ajax({
                        url: "${eippath}/project/queryRegServeFlag ",
                        dataType: "json",
                        type: "POST",
                        data: { projectId },
                        success: function (data2) {
                            if (data2.state == 1) {
                                $('.links-item-1 .dot').parent().addClass('hide')
                                if(data2.data){ // true
                                    $('.links-item-1 .green').parent().removeClass('hide')
                                }else{ // null false
                                    $('.links-item-1 .red').parent().removeClass('hide')
                                }
                            } else {
                                $.messager.alert('提示', data2.msg , 'error');
                            }
                        }
                    })
                } else {
                    $.messager.alert('提示', '加载项目配置失败！', 'error');
                }
            },
        });
    }

    function loadTempDataSet(data,type) {
        var datas = data.result
        // console.log(datas)
        datas.forEach(function (element, index) {
            if (element.type == 5) {
                $("#projectInfo_website_index").combobox("setValue", element.content)
            } else if (element.type == 6) {
                $("#projectInfo_invitation").combobox("setValue", element.content)
            } else if (element.type == 11) {
                $("#upTempBgiName2").textbox("setValue", element.content)
                $("#upTempBgiImg2").attr("src", element.pic)
            } else if (element.type == 12) {
                $("#upTempBgiName3").textbox("setValue", element.content)
                $("#upTempBgiImg3").attr("src", element.pic)
            // } else if (element.type == 13) {
            //     if(element.content){
            //         exhibitionTplGlobal.logo = element.pic
            //         $("#upTempLogoName").textbox("setValue", element.content)
            //         $("#upTempLogoImg").attr("src", element.pic)
            //     }
            // } else if (element.type == 14) {
            //     if(element.content) {
            //         exhibitionTplGlobal.mColorMain = element.content
            //         colorIndex = element.enable
            //         exhibitionTplGlobal.setColorPickerVal2('#selfServiceColor', element)
            //     }
            // } else if (element.type == 15) {
            //     if(element.content){
            //         exhibitionTplGlobal.mbg = element.pic
            //         $("#upTempBgiName").textbox("setValue", element.content)
            //         $("#upTempBgiImg").attr("src", element.pic)
            //     }
            // } else if (element.type == 16) {
            //     if(element.content){
            //         exhibitionTplGlobal.mTpl = element.content
            //         $("#mobile-farm-project-temporary").combobox("setValue", element.content)
            //     }
            } else if (element.type == 17) {
                $("#officialWebsiteLink").textbox("setValue", element.content)
            }else if (element.type == 106) {
              operationFooterLinks('import', JSON.parse(element.content))
            }else if (element.type == 113) {
                $('#invitationEventTopBanner').textbox('setValue', element.pic || '')
                $('#invitationEventTopBannerCover').attr('src', element.pic || '')
            }else if (element.type == 114) {
                invitationEventRules.txt.html(element.content || '')
            }
        });
    }

    //上传图片
    function uploadFileImg(file, type) {
        if (file == null) {
            $.messager.alert('提示', '请选择附件！', 'warning');
            return;
        }
        var formData = new FormData();
        formData.append("file", file);
        if(type == 123){
            if(file.type !== 'image/jpeg') return $.messager.alert('提示','需要jpg/jpeg格式','warning')
            if(file.size > 150 * 1000) return $.messager.alert('提示','图片不能超过150kb','warning')
        }else if(file.type === 'image/png' && file.size > 300 * 1000){
            return $.messager.alert('提示','png格式的图片不能超过300kb','warning')
        }
        $.ajax({
            url: "${eippath}/upload/uploadFileOss",
            dataType: "json",
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            success: function (data) {
                if (data.state == 1) {
                    if (type == 1) {
                        $("#upTempLogoImg").attr("src", data.result.path);
                        $("#upTempLogoName").textbox("setValue", data.result.fileName)
                    } else if (type == 2) {
                        $("#upTempBgiImg").attr("src", data.result.path);
                        $("#upTempBgiName").textbox("setValue", data.result.fileName)
                    } else if (type == 3) {
                        $("#upTempBgiImg2").attr("src", data.result.path);
                        $("#upTempBgiName2").textbox("setValue", data.result.fileName)
                    } else if (type == 4) {
                        $("#upTempBgiImg3").attr("src", data.result.path);
                        $("#upTempBgiName3").textbox("setValue", data.result.fileName)
                    } else if (type == 5) {
                        $("#websiteTopBgImgUrlPc").attr("src", data.result.path);
                        $("#websiteTopBgImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 6) {
                        $("#websiteTopLogoUrlPc").attr("src", data.result.path);
                        $("#websiteTopLogoPc").textbox("setValue", data.result.fileName)
                    } else if (type == 7) {
                        $("#websiteTopImgUrlPc").attr("src", data.result.path);
                        $("#websiteTopImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 8) {
                        $("#websiteBanner1ImgUrlPc").attr("src", data.result.path);
                        $("#websiteBanner1ImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 9) {
                        $("#websiteBanner2ImgUrlPc").attr("src", data.result.path);
                        $("#websiteBanner2ImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 10) {
                        $("#websiteBanner3ImgUrlPc").attr("src", data.result.path);
                        $("#websiteBanner3ImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 11) {
                        $("#websiteBanner4ImgUrlPc").attr("src", data.result.path);
                        $("#websiteBanner4ImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 12) {
                        $("#websiteInsideTopImgUrlPc").attr("src", data.result.path);
                        $("#websiteInsideTopImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 13) {
                        $("#websiteTopLogoUrl").attr("src", data.result.path);
                        $("#websiteTopLogo").textbox("setValue", data.result.fileName)
                    } else if (type == 14) {
                        $("#websiteBanner1ImgUrl").attr("src", data.result.path);
                        $("#websiteBanner1Img").textbox("setValue", data.result.fileName)
                    } else if (type == 15) {
                        $("#websiteBanner2ImgUrl").attr("src", data.result.path);
                        $("#websiteBanner2Img").textbox("setValue", data.result.fileName)
                    } else if (type == 16) {
                        $("#websiteBanner3ImgUrl").attr("src", data.result.path);
                        $("#websiteBanner3Img").textbox("setValue", data.result.fileName)
                    } else if (type == 17) {
                        $("#websiteBanner4ImgUrl").attr("src", data.result.path);
                        $("#websiteBanner4Img").textbox("setValue", data.result.fileName)
                    } else if (type == 18) {
                        $("#websiteCopyrightInfoImgUrlPc").attr("src", data.result.path);
                        $("#websiteCopyrightInfoImgPc").textbox("setValue", data.result.fileName)
                    } else if (type == 19) {
                        $("#QrCodeImgUrlPc").attr("src", data.result.path);
                        $("#QrCodeNamePc").textbox("setValue", data.result.fileName)
                    } else if (type == 20) {
                        $("#websiteFaviconUrlPc").attr("src", data.result.path);
                        $("#websiteFaviconPc").textbox("setValue", data.result.fileName)
                    } else if (type == 21) {
                        $("#invitationEventTopBannerCover").attr("src", data.result.path)
                        $("#invitationEventTopBanner").textbox("setValue", data.result.fileName)
                    } else if (type == 22) { // pc登录页背景图
                        $("#upTempBgiImgPc").attr("src", data.result.path)
                        $("#upTempBgiNamePc").textbox("setValue", data.result.fileName)
                    } else if (type == 123) { // pc端官网 行业模板 底部背景图
                        $("#websiteFootBgImgUrlPc").attr("src", data.result.path);
                        $("#websiteFootBgImgPc").textbox("setValue", data.result.fileName)
                    }

                } else $.messager.alert('提示', '上传失败！', 'error');
            },
        });
    }

    var variablePic = location.origin

    $(function () {
        // 加载 PC端官网模板设置 手机端官网模板设置 自助服务中心模板设置(3:额外处理全局) 邀请函模板设置 邀请活动设置
        loadTempData(3)
    });

    function check_url(_url){
        var str = _url;
        var Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
        var objExp= new RegExp(Expression);
        return objExp.test(str)
    }

    //对底部链接操作的封装
    function operationFooterLinks(type, data=null){
      const _J = [2,3].map(index=>[$('#footerLinkName' + index), $('#footerLinkAddress' + index)])
      //   const _J = [1,2,3].map(index=>[$('#footerLinkName' + index), $('#footerLinkAddress' + index)])
      switch(type){
        case 'export':
          return _J.map(item=>{
            const [$name,$link] = item
            return {
              name: $name.textbox('getValue'),
              link: $link.textbox('getValue'),
            }
          })
        case 'import':
          if(!Array.isArray(data)) return
          if(data.legnth > 2){ // 兼容老数据
            data.shift()
          }
          data.forEach((item, index) => {
            const [$name, $link] = _J[index]
            $name.textbox('setValue', item['name'])
            $link.textbox('setValue', item['link'])
          })
          break
      }
    }
</script>
</body>
</html>