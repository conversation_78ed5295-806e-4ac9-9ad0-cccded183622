<!-- 渠道基础表 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<script src="${eippath}/js/exceljs.min.js" type="text/javascript" charset="utf-8"></script>
<script src="${eippath}/js/FileSaver.js" type="text/javascript" charset="utf-8"></script>
 <%@ include file="../common/reg_url.jsp" %>
<style type="text/css">
.all_address{
    box-sizing: border-box;
    padding-bottom: 10px;
    margin: 8px 30px 0 30px;
    border-bottom: 1px solid #ccc;
}
.all_address h6{
    font-size: 16px;
    font-weight: normal;
    color: #333333;
    margin-bottom: 8px;
}
.all_address textarea{
	width: 100%;
	border: none;
	resize: none;
	outline: none;
	font-size: 14px;
	color: #808080;
}
.btn-primary{
	width: 84px;
	height: 32px;
	border-radius: 6px;
	font-size: 14px;
	border: solid 1px #4c72fe;
	color: #4c72fe;
	background-color: #fff;
    outline: none;
    cursor: pointer;
}
.all_address>a{
    width: 84px;
    height: 30px;
    background-color: #fff;
    border-radius: 6px;
    border: solid 1px #999999;
    display: inline-block;
    text-align: center;
    line-height: 30px;
    margin-left: 20px;
	color: #666666;
}
.all_address>a:hover{
  color: #666666;
}
.address_download{
    overflow: hidden;
   margin: 12px 30px 15px;
}
.address_download ul li{
	float: left;
	margin-right: 20px;
}

.am-btn{
    font-size: 14px;
    width: 100%;
    color: #333333;
    border: none;
    background-color: #fff;
    height: 26px;
    cursor: pointer;
    outline: none;
}
.prompt_text{
	width: 144px;
	font-size: 16px;
	line-height: 26px;
	color: #333333;
	margin-left: 14px;
	margin-top: 29px;
}
.register-exhibitionReg,.setdiv,.register-releaseReg,.register-address{
	width: 288px !important;
	height: 216px !important;
	border-radius: 10px;
	border: solid 1px #4c72fe;
    transition: transform 0.6s;
}
.register-exhibitionReg :hover img,.setdiv :hover img,.register-releaseReg :hover img,.register-address :hover img{
	transform: scale(1.1);
}
.detialdiv a{
	font-size: 24px;
	color: #4c72fe;
	display: block;
    margin-top: 20px;
}
.detialdiv img{
	 margin: auto;
    margin-top: 42px;
}
.tips-icon {
	display: inline-block;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	color: rgb(63,164,255);
	background-color: rgb(230,247,255);
	font-style: normal;
	text-align: center;
	line-height: 16px;
	font-size: 12px;
	font-weight: bold;
	margin-right: 5px;
}
</style>
<style>
	.my-text{
		border-radius: 4px;
		width: 150px;
		height: 25px;
		box-sizing: border-box;
	}
	#channel-details-inviteCodeWindow-eicw-form,
	#channel-details-inviteCodeWindow-beicw-form,
	#channel-details-inviteCodeWindow-form {
		list-style:none;
		margin:0;
		padding:0;
	}
	#channel-details-inviteCodeWindow-eicw-form li,
	#channel-details-inviteCodeWindow-beicw-form li,
	#channel-details-inviteCodeWindow-form li {
		margin-right: 15px;
		margin-bottom: 12px;
	}
	#channel-details-inviteCodeWindow-eicw-form li > div,
	#channel-details-inviteCodeWindow-beicw-form li > div,
	#channel-details-inviteCodeWindow-form li > div{
		width: 105px;
		height: 25px;
		display: inline-block;
		text-align-last: right;
		margin-right: 10px
	}
	#channel-details-inviteCodePanel-companyName + span.textbox {
		border-top-right-radius: 0;
		border-bottom-right-radius: 0
	}

  .channel-details-batch-wrapper {
      display: inline-block;
      position: relative;
  }
  .channel-details-batch-wrapper:hover  .channel-details-batch-list {
      display: flex;
  }
  .channel-details-batch-list {
      display: none;
      flex-direction: column;
      background-color: #fff;
      box-shadow: 0 0 5px #aaa;
      border-radius: 4px;
      position: absolute;
      left: 10%;
      top: 100%;
      width: 150px;
      z-index: 2;
  }
  .channel-details-batch-list span {
      cursor: pointer;
      padding: 5px 10px;
  }
  .channel-details-batch-list span:hover {
      background-color: #f5f5f5;
  }

  .autoUseInviteCodeWrapper .autoUseInviteCodeAfterForward {
      margin-left: 40px;
      display: none;
  }

  .autoUseInviteCodeWrapper .autoUseInviteCode input:checked + .autoUseInviteCodeAfterForward {
      display: inline;
  }

	.webSetStyle {
		width: 172px;
		height: 34px;
		background-color: #ffffff;
		border-radius: 4px;
		border: solid 1px #999999;
		display: inline-block;
		line-height: 34px;
		text-align: center;
		font-size: 14px;
		color: #666;
		margin-right: 24px;
		cursor: pointer; }
	.webSetStyleActive {
		background-color: #f2f5ff;
		border: solid 1px #5567a5;
		color: #5567a5; }
</style>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false" toolbar="#channel-details-toolbar">
		<!-- begin of toolbar -->
		<div id="channel-details-toolbar" style="position: absolute;top: 0;z-index: 99999;width: 100%">
			<div class="my-toolbar-search" style="min-width: 1160px">
				<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input id="channel-details-search-projectName" class="my-text">&nbsp;
				<span class="show-of-tab show-of-tab-0">
					<label>身份</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input
						class="easyui-combobox"
						style="width:150px;height:25px"
						id="channel-details-search-targetType"
						data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>&nbsp;
					<label>渠道类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input id="channel-details-search-channelTypeName" class="my-text">&nbsp;
					<label>渠道名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input id="channel-details-search-channelName" class="my-text">&nbsp;
				</span>
				<span class="show-of-tab show-of-tab-1" style="display: none">
					<label>展商名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input id="channel-details-search-companyName" class="my-text">&nbsp;
					<label>邀请码状态</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select
						class="easyui-combobox"
						style="width:150px;height:25px"
						id="channel-details-search-inviteCodeState"
						data-options="panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
						<option value="" selected>全部</option>
						<option value="0">启用</option>
						<option value="1">停用</option>
					</select>&nbsp;
					<label>邀请函状态</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select
							class="easyui-combobox"
							style="width:150px;height:25px"
							id="channel-details-search-invitationState"
							data-options="panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
						<option value="" selected>全部</option>
						<option value="1">已设置</option>
						<option value="2">未设置</option>
					</select>&nbsp;
				</span>
				<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
					 onclick="channel_details_current_tab_index ? channel_details_inviteCodeTable() : channel_details_search()">查询</a>
			</div>
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="channel_details_fresh()"
					 plain="true">刷新</a>
				<%--<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-1" iconCls="icon-add" onclick="channel_details_invite_form_functions.add()"
					 plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-1" iconCls="icon-edit" onclick="channel_details_mod()"
					 plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-1" iconCls="icon-remove" onclick="channel_details_DeleteInviteCode()"
					 plain="true">删除</a>--%>
				<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-add" onclick="channel_details_set_add()"
					 plain="true">新增</a>
        <a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-edit" onclick="channel_details_set_edit()"
           plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-remove" onclick="channel_details_set_delete()"
					 plain="true">删除</a>
				<div class="channel-details-batch-wrapper">
          <a href="javascript:void(0);" style="display: none" class="easyui-linkbutton show-of-tab show-of-tab-1"
             iconCls="icon-maker" plain="true">邀请码设置</a>
          <div class="channel-details-batch-list">
            <span onclick="channel_details_invite_form_functions.make()">批量生成邀请码</span>
            <span onclick="channel_details_invite_form_functions.openBeicWindow(1, '修改验证码限制次数')">修改验证码限制次数</span>
            <span onclick="channel_details_invite_form_functions.openBeicWindow(2, '修改邀请码状态')">修改邀请码状态</span>
          </div>
        </div>
				<a href="javascript:void(0);" style="display: none" class="easyui-linkbutton show-of-tab show-of-tab-1"
					 iconCls="icon-maker" onclick="channel_details_invite_form_functions.makeInvite()" plain="true">批量生成邀请函</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download-all"
					 onclick="channel_details_export()" plain="true">导出</a>
			</div>
		</div>
		<!-- end of toolbar -->
		<div id="channel-details-tabs" data-options="onSelect(){channel_details_tabs_change(...arguments)}" class="easyui-tabs" style="width:100%;height:100%;padding-top: 67px;">
			<div title="自定义渠道">
				<table id="channel-details-datagrid" class="easyui-datagrid" data-options="rownumbers:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',scrollbarSize:0">
					<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'channelId',width:20">渠道ID</th>
						<th data-options="field:'projectName',width:40">项目名称</th>
						<th data-options="field:'targetName',width:30,align:'center'">身份</th>
						<th data-options="field:'channelTypeName',width:40" formatter="channel_details_channelTypeName">渠道类型</th>
						<th data-options="field:'channelName',width:40">渠道名称</th>
						<th data-options="field:'2',width:50" formatter="channel_details_regAddressBtn">登记地址</th>
						<th data-options="field:'3',width:40" formatter="channel_details_dataPush">渠道获客接口设置</th>
					</tr>
					</thead>
				</table>
			</div>
			<div title="展商渠道">
				<table id="channel-details-inviteCodeTable" class="easyui-datagrid"
							 data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,scrollbarSize:0,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData){channel_details_mod(rowData)},">
					<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'projectName',width:40">项目名称</th>
						<th data-options="field:'companyName',width:40">展商名称</th>
						<th data-options="field:'userName',width:40">会员号</th>
						<th data-options="field:'zzyUserMobile',width:40">绑定手机</th>
						<th data-options="field:'zzyUserEmail',width:40">绑定邮箱</th>
						<th data-options="field:'inviteCode',width:30" formatter="channel_details_inviteCode">邀请码</th>
						<th data-options="field:'autoUseInviteCode',width:30" formatter="r => r ? '是': '否'">自动使用</th>
						<th data-options="field:'buyerNumber',width:30" formatter="channel_details_buyerNumber">邀请码已使用次数</th>
						<th data-options="field:'useNumber',width:30">邀请码限制次数</th>
						<th data-options="field:'inviteState',width:30" formatter="r=>+r===1?'已设置':''">邀请函</th>
						<th data-options="field:'inviteTemplateName',width:30" formatter="channel_details_invitTpl">邀请函模板</th>
						<th data-options="field:'invitationList',width:30" formatter="channel_details_invitationList">邀请函链接</th>
						<th data-options="field:'projRegSetList',width:30" formatter="channel_details_projRegSetList">观众邀请链接</th>
						<th data-options="field:'inviteBuyerNum',width:30" formatter="channel_details_inviteBuyerNum">邀请观众次数</th>
						<%--<th data-options="field:'state',width:30,align:'center'" formatter="channel_details_operation">操作</th>--%>
					</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
</div>
<div id="channel-details-set-window" class="easyui-window" toolbar="#channel-details-set-window-toolbar"
		 data-options="closed:true,title:'渠道',modal:true,shadow: false" style="width:700px;height:auto;">
	<div id="channel-details-set-window-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save"
				 onclick="channel_details_set_save()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel"
				 onclick="channel_details_set_cancel()" plain="true">取消</a>
		</div>
	</div>
	<div id="channel-details-set-panel-1" class="easyui-panel" title="基本信息"
			 style="width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>项目</label></div>
				<input hint="projectId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
							 id="channel-details-set-panel-projectId"
							 data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>适用身份</label></div>
				<input class="easyui-combobox" style="width:175px;height:25px" id="channel-details-set-panel-targetList"
							 data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>渠道类型</label></div>
				<input hint="channelTypeId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
							 id="channel-details-set-panel-channelTypeId"
							 data-options="valueField:'channelTypeId',textField:'channelTypeName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true, onChange(value){$('#channel-details-set-panel-4-tips')[value==4?'show':'hide']()}">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>渠道名称</label></div>
				<input hint="channelName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px"
							 id="channel-details-set-panel-channelName">
			</li>
			<li id="channel-details-set-panel-4-tips" style="clear: both; width: 100%;display:flex;">
        <div style="width:120px; text-align:right;flex-shrink: 0"><label><span class="tips-icon"
                                                                               style="margin-right: 10px">i</span></label>
        </div>
        <p style="margin: 0">
          渠道类型为扫码即到场时，使用该渠道登记进来的观众将标记为已到场。本渠道会与闸机/PDA回传数据产生冲突，
          如使用闸机/PDA请避免使用扫码即到场渠道。
        </p>
      </li>
		</ul>
	</div>
</div>
<div id="channel-details-invite-address-window" class="easyui-window" data-options="closed:true,title:'邀请函链接',modal:true,maximizable: false,resizable: false,minimizable: false,shadow:false,top:120" style="width:600px;">
	<div class="easyui-panel" title="">
		<div id="channel-details-invite-address-enable-english" class="all_address hide" style="border: none;margin-top: 2em;margin-bottom: -2em;">
			<span id="channel-details-invite-address-version1" class="webSetStyle" onclick="channel_details_invite_open2d(void 0, void 0, 1)">中文版地址</span>
			<span id="channel-details-invite-address-version2" class="webSetStyle" onclick="channel_details_invite_open2d(void 0, void 0, 2)">英文版地址</span>
		</div>
		<div class="all_address">
			<h6 id="channel-details-invite-address-phone-text">手机邀请函地址</h6>
			<textarea cols="50" rows="3" id="channel-details-invite-address-phone" readonly="readonly" ></textarea>
			<button class="btn-primary" onclick="channel_details_copyText('channel-details-invite-address-phone')">复制链接</button>
			<a id="channel-details-register-invite-phone-open" href="" target="_blank"  >直接打开</a>
		</div>
		<div class="address_download">
			<ul>
				<li>
					<div id="channel-details-invite-address-code" class="qr_code"></div>
					<div class="address_download_button">
						<a id="channel-details-invite-address-download" download="qrcode.jpg"></a>
						<button id="channel-details-invite-address-saveQrCode"  class="am-btn" onclick="channel_details_invite_Download()">下载</button>
					</div>
				</li>
				<li>
					<div class="prompt_text">
						<p>左侧手机邀请函二维码点击下载即可手机扫码可预览</p>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>
<!-- 登记链接 -->
<%@ include file="../common/reg_url_qr.jsp" %>

<div
	id="channel-details-dataPushWindow"
	class="easyui-window"
	data-options="closed:true,title:'接口参数',modal:true,resizable: false,minimizable: false" style="width:760px;height:auto;">
	<div id="channel-details-panel-dataPush" class="easyui-panel" style="width:100%;height:auto;padding:10px;">
		<ul class="channel-details-dataPush">
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            接口路径
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-APIAddress">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            项目ID
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-projectId">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            项目名称
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-projectName">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            渠道ID
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-channelId">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            TOKEN
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-token">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            密钥
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-secretKey">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
          <span class="panel-label__text">
            身份ID
          </span>
					<span class="panel-label__input">
            <input type="text" readonly id="channel-details-targetType">
          </span>
					<span class="panel-label__operat">
            <button>复制</button>
          </span>
				</div>
			</li>
			<li>
				<div class="panel-label">
					<span class="panel-label__text"></span>
					<span class="panel-label__input">
            <button id="channel_details_downloadExplain">下载API接口及第三方表单填写说明文档</button>
            <button id="channel_details_downBuyerQuestion">下载问卷填写说明文档</button>
          </span>
					<span class="panel-label__operat"></span>
				</div>
			</li>
		</ul>
	</div>
</div>
<div
		id="channel-details-PushInterWindow"
		class="easyui-window"
		toolbar="channel-details-PushInterWindow-toolbar"
		data-options="closed:true,title:'设置',modal:true,resizable: false,minimizable: false" style="width:550px;height:auto;">
	<div id="channel-details-PushInterWindow-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save"
				 onclick="channel_details_PushInterDataSetSave()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel"
				 onclick="$('#channel-details-PushInterWindow').window('close')" plain="true">取消</a>
		</div>
	</div>
	<div class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul style="padding: 10px;">
			<li style="margin-bottom: 10px;">门票发放逻辑</li>
			<li style="margin-bottom: 10px;color: #000;">
				<label>
					该渠道来源的观众，自动发放门票证件
					<input type="checkbox" id="channel-details-autoSendTickets" onclick="$('#channel-details-li-srm')[$(this).prop('checked') ? 'show': 'hide']()">
				</label>
			</li>
			<li style="margin-bottom: 10px;color: #000;">
				<label>
					发放门票时，以第三方渠道胸卡号为准
					<input type="checkbox" id="channel-details-useThirdBarcode">
				</label>
			</li>
			<li style="margin-bottom: 10px;">更新观众数据逻辑</li>
			<li style="margin-bottom: 10px;color: #000;">
				<label>
					更新观众信息时，以本渠道信息为准，覆盖其他渠道来源的观众信息
					<input type="checkbox" id="channel-details-currentChannelCoverOtherChannelBuyerInfo">
				</label>
			</li>
			<li style="margin-bottom: 10px;">其他设置</li>
			<li style="margin-bottom: 10px;color: #000;">
				<label>
					返回胸卡号
					<input type="checkbox" id="channel-details-channelRepeatReturnBarcode">
				</label>
			</li>
			<li style="margin-bottom: 10px;color: #000;">
				<label>
					接收观众问卷信息
					<input type="checkbox" id="channel-details-receiveBuyerQuestionnaireInfo">
				</label>
			</li>
			<li id="channel-details-li-srm" style="color: #000;">
				<label>
					向观众发送回执短信/邮件
					<input type="checkbox" id="channel-details-sendRegResultMsg">
				</label>
			</li>
		</ul>
	</div>
</div>
<div
		id="channel-details-inviteCodeWindow-eic-window"
		class="easyui-window" toolbar="#channel-details-inviteCodeWindow-eicw-toolbar"
		data-options="closed:true,title:'展商渠道',modal:true,shadow: false"
		style="width:460px;">
	<div id="channel-details-inviteCodeWindow-eicw-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="channel_details_invite_form_functions.saveInnerForm()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="channel_details_invite_form_functions.closeInner()" plain="true">取消</a>
		</div>
	</div>
	<div class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul id="channel-details-inviteCodeWindow-eicw-form">
			<li>
				<div><label>邀请码</label></div>
				<input
						class="easyui-textbox"
						style="width:270px;height:25px"
						maxlength="6"
						id="channel-details-inviteCodePanel-eicw-inviteCode">
			</li>
			<li>
				<div><label>最大使用</label></div>
				<input
						class="easyui-numberbox"
						style="width:70px;height:25px"
						id="channel-details-inviteCodePanel-eicw-useNumber">
				<span style="margin-left: 10px">次</span>
			</li>
			<li>
				<div><label>启用观众邀请码</label></div>
				<span id="channel-details-inviteCodePanel-eicw-state" style="display: inline-flex;width: 120px;justify-content: space-between;">
					<label><input type="radio" name="cdi-eicw-state" value="0"> 启用</label>
					<label><input type="radio" name="cdi-eicw-state" value="1"> 停用</label>
				</span>
			</li>
		</ul>
	</div>
</div>
<div
    id="channel-details-inviteCodeWindow-beic-window"
    class="easyui-window" toolbar="#channel-details-inviteCodeWindow-beic-toolbar"
    data-options="closed:true,title:'展商渠道',modal:true,shadow: false"
    style="width:460px;">
  <div id="channel-details-inviteCodeWindow-beic-toolbar">
    <div class="my-toolbar-button" style="display:flex;align-items: center;">
      <span id="channel-details-inviteCodeWindow-beicw-hint"></span>
      <a style="margin-left: auto" href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="channel_details_invite_form_functions.saveBeicForm()" plain="true">保存</a>
      <a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="channel_details_invite_form_functions.closeBeicWindow()" plain="true">取消</a>
    </div>
  </div>
  <div class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
    <ul id="channel-details-inviteCodeWindow-beicw-form">
      <li style="display: none">
        <div><label>最大使用</label></div>
        <input
            class="easyui-numberbox"
            style="width:70px;height:25px"
            id="channel-details-inviteCodePanel-beicw-updateNumber">
        <span style="margin-left: 10px">次</span>
      </li>
      <li style="display: none">
        <div><label>状态</label></div>
        <span id="channel-details-inviteCodePanel-beicw-updateState" style="display: inline-flex;width: 120px;justify-content: space-between;">
					<label><input type="radio" name="cdi-beicw-state" value="0"> 启用</label>
					<label><input type="radio" name="cdi-beicw-state" value="1"> 停用</label>
				</span>
      </li>
    </ul>
  </div>
</div>

<div
		id="channel-details-inviteCodeWindow"
		class="easyui-window" toolbar="#channel-details-inviteCodeWindow-toolbar"
		data-options="closed:true,title:'展商渠道',modal:true,shadow: false"
		style="width:530px;">
	<div id="channel-details-inviteCodeWindow-toolbar" class="hideEditInviteCode">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="channel_details_invite_form_functions.saveForm()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="channel_details_invite_form_functions.close()" plain="true">取消</a>
		</div>
	</div>
	<div class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul id="channel-details-inviteCodeWindow-form">
			<li>
				<div><label>项目</label></div>
				<input type="hidden" id="channel-details-inviteCodePanel-inviteCodeId">
				<input
					class="easyui-combobox"
					style="width:270px;height:25px"
					id="channel-details-inviteCodePanel-contractProjectId"
					data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,onChange(){
						$('#channel-details-inviteCodePanel-clientId').val('');
						$('#channel-details-inviteCodePanel-companyName').textbox('clear');
						$('#channel-details-inviteCodePanel-clientIds').val('');
						$('#channel-details-inviteCodePanel-selectedLength').text('0');
						$('#channel-details-inviteCodePanel-inviteTemplateId').combobox('clear');
						channel_details_invite_form_functions.initInviteTpls($('#channel-details-inviteCodePanel-contractProjectId').combobox('getValue'))
					}">
			</li>
			<li>
				<div><label>邀请函模板</label></div>
				<input
					class="easyui-combobox"
					style="width:270px;height:25px"
					id="channel-details-inviteCodePanel-inviteTemplateId"
					data-options="valueField:'inviteTemplateId',textField:'inviteTemplateName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
			</li>
			<li class="hideMultipleSelect">
				<div><label>客户名称</label></div>
				<input
						class="easyui-textbox"
						style="width:210px;height:25px"
						id="channel-details-inviteCodePanel-companyName"
						readonly/>
				<input type="hidden" id="channel-details-inviteCodePanel-clientId">
				<a href="javascript:channel_details_invite_form_functions.openCompanySelector()"
					 class="easyui-linkbutton"
					 style="
						width: 60px;
						height: 25px;
						border-top-left-radius: 0;
						border-bottom-left-radius: 0;
						border-left: none;
						margin-left: -4px;
						padding-left: 4px">选择</a>
			</li>
			<li class="showMultipleSelect">
				<div><label>客户名称</label></div>
				<span>已选择<span id="channel-details-inviteCodePanel-selectedLength">0</span>个</span>
				<input type="hidden" id="channel-details-inviteCodePanel-clientIds">
				<a href="javascript:channel_details_invite_form_functions.openCompanySelector()"
					 class="easyui-linkbutton" style="width: 60px;height: 25px">选择</a>
			</li>
			<li class="show-make-invite" style="display: none">
				<div><label>引用会刊信息</label></div>
				<input
						type="checkbox"
						style="width: 16px;height: 16px;vertical-align: middle;"
						id="channel-details-inviteCodePanel-citeCataInfoFlag">
			</li>
			<li class="hideMultipleSelect hideEditInviteCode">
				<div><label>启用观众邀请码</label></div>
				<input
					type="checkbox"
					style="width: 16px;height: 16px;vertical-align: middle;"
					id="channel-details-inviteCodePanel-state">
			</li>
			<li class="hideMultipleSelect hideInviteCodePanelStateOff hideEditInviteCode">
				<div><label>邀请码</label></div>
				<input
						class="easyui-textbox"
						style="width:270px;height:25px"
						maxlength="6"
						id="channel-details-inviteCodePanel-inviteCode">
			</li>
			<li class="hide-make-invite hideInviteCodePanelStateOff hideEditInviteCode">
				<div><label>最大使用</label></div>
				<input
						class="easyui-numberbox"
						style="width:70px;height:25px"
						id="channel-details-inviteCodePanel-useNumber">
				<span style="margin-left: 10px">次</span>
			</li>
			<li style="display: none">
				<span class="autoUseInviteCodeWrapper">
					<label class="autoUseInviteCode">
            自动使用邀请码
						<input onchange="channel_details_invite_form_functions.autoUseInviteCodeChange()" type="checkbox"
                   id="channel-details-inviteCodePanel-beicw-autoUseInviteCode"/>
					  <label class="autoUseInviteCodeAfterForward">
              转发仍自动使用邀请码
              <input onchange="channel_details_invite_form_functions.autoUseInviteCodeChange()" type="checkbox"
                     id="channel-details-inviteCodePanel-beicw-autoUseInviteCodeAfterForward"/>
					  </label>
          </label>
				</span>
			</li>
		</ul>
	</div>
	<div class="easyui-panel" cls="showEditInviteCode" title="展商邀请码" style="width:100%;height:280px;padding:10px;">
		<div id="channel-details-inviteCodeWindow-eic-toolbar">
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="channel_details_invite_form_functions.addInner()"
					 plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="channel_details_invite_form_functions.editInner()"
					 plain="true">修改</a>
			</div>
		</div>
		<table
				id="channel-details-inviteCodeWindow-eic-datagrid"
				class="easyui-datagrid"
				toolbar="#channel-details-inviteCodeWindow-eic-toolbar"
				data-options="rownumbers:true,
				 singleSelect:true,
				 pagination:false,
				 multiSort:true,
				 fitColumns:true,
				 fit:true,method:'post',
				 selectOnCheck:true,
				 scrollbarSize:0,
				 onClickRow(rowIndex, rowData) {
					var l = $(this).datagrid('getRows').length;
					for(var i = 0; i < l; i++){
						if(i != rowIndex)$(this).datagrid('unselectRow', i);
					}
				}">
			<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'inviteCode',width:80,align:'center'">邀请码</th>
				<th data-options="field:'buyerNumber',width:80,align:'center'">使用次数</th>
				<th data-options="field:'useNumber',width:80,align:'center'">限制次数</th>
				<th data-options="field:'state',width:80,align:'center'" formatter="v => v === 0 ? `<span style='color:rgb(76,114,254);'>已启用</span>`: `<span>已停用</span>`">状态</th>
			</tr>
			</thead>
		</table>
	</div>
</div>
<div
		id="channel-details-register-address"
		class="easyui-window" toolbar="#channel-details-register-address-toolbar"
		data-options="closed:true,title:'新增登记地址',modal:true,shadow: false"
		style="width:400px;">
	<div id="channel-details-register-address-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="channel_details_AddNextStep()" plain="true">下一步</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="channel_details_CloseTargetType()" plain="true">取消</a>
		</div>
	</div>
	<div class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul id="channel-details-register-address-form">
			<li>
				<div><label>项目</label></div>
				<input
						class="easyui-combobox"
						style="width:200px;height:25px"
						id="channel-details-register-address-projectId"
						data-options="
						valueField:'projectId',
						textField:'projectName',
						panelHeight:'auto',
						panelMaxHeight:'200px',
						limitToList:true,
						url: '/eip-web-sponsor/project/getBaseList',
						method: 'POST',
						queryParams: {exhibitCode: exhibitCode_for_pass,haveBuyerRegSet:true},
						onChange(value) {channel_details_LoadRoles(value)},
						loadFilter(data){
							return data.state === 1 ?  data.data : []
						}">
			</li>
			<li>
				<div><label>适用身份</label></div>
				<input
						class="easyui-combobox"
						style="width:200px;height:25px"
						id="channel-details-register-address-targetType"
						data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
			</li>
		</ul>
	</div>
</div>
<div
		id="channel-details-selector"
		class="easyui-window" toolbar="#channel-details-selector-toolbar"
		data-options="closed:true,title:'选取登记地址渠道',modal:true,shadow: false"
		style="width:780px;">
	<div id="channel-details-channel-selector-toolbar">
		<div class="my-toolbar-search">
			<label>渠道类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
			<input id="channel-details-selector-search-channelTypeName" class="my-text">&nbsp;
			<label>渠道名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
			<input id="channel-details-selector-search-channelName" class="my-text">&nbsp;
			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
				 onclick="channel_details_SelectorSearch()">查询</a>
			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px;float: right;"
				 onclick="channel_details_SelectorBack()">返回</a>
			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px;float: right;margin-right: 10px"
				 onclick="channel_details_SelectorConfirm()">确定</a>
		</div>
	</div>
	<div class="easyui-panel" style="width:100%;height:400px;padding:10px;">
		<table id="channel-details-selector-datagrid" class="easyui-datagrid"
					 data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,scrollbarSize:0,
        		onClickRow(rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               if(i != rowIndex)$(this).datagrid('unselectRow', i);
             }
 				}">
			<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'projectName',width:30">项目名称</th>
				<th data-options="field:'channelTypeName',width:40"
						formatter="(val, row) => !val && row.channelTypeId === 4 ? '扫码即到场' : val">渠道类型
				</th>
				<th data-options="field:'channelName',width:40">渠道名称</th>
			</tr>
			</thead>
		</table>
	</div>
</div>

<!-- 展商渠道 修改邀请函信息 -->
<style>
	#channelDetailsTpl-window{
		display: none;
	}
	.channelDetailsTpl-window .text-black{
		color: black;
	}
	.channelDetailsTpl-window .flex {
		display: flex;
	}
</style>
<div id="channelDetailsTpl-window" class="easyui-window" title="修改邀请函信息"
	data-options="width: 500,closed:true,modal:true,minimizable: false,maximizable: false,">
	<div class="my-toolbar-button datagrid-toolbar">
		<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-save" onclick="channelDetailsTpl.save()"
			plain="true">保存</a>
		<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel" onclick="channelDetailsTpl.close()" plain="true">取消</a>
	</div>
	<form id="channelDetailsTpl-form" style="height:auto;">
	<div id="channelDetailsTpl-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;">
		<ul style="padding: 10px">
			<li style="padding: 10px 0">
				<label for="" style="width: 130px;display: inline-block;text-align: right;">
					<i style="color: red">* </i>公司名称</label>
				<input id="channelDetailsTpl-companyName" class="easyui-textbox" style="width: 200px">
			</li>
			<li style="padding: 10px 0">
				<label for="" style="width: 130px;display: inline-block;text-align: right;">
					<i style="color: red">* </i>公司名称(英文)</label>
				<input id="channelDetailsTpl-companyNameEn" class="easyui-textbox" style="width: 200px">
			</li>
			<li style="padding: 10px 0">
				<label for="" style="width: 130px;display: inline-block;text-align: right;">公司简称</label>
				<input id="channelDetailsTpl-companyNameAbbr" class="easyui-textbox" style="width: 200px">
			</li>
			<li style="padding: 10px 0">
				<label for="" style="width: 130px;display: inline-block;text-align: right;">公司简称(英文)</label>
				<input id="channelDetailsTpl-companyNameAbbrEn" class="easyui-textbox" style="width: 200px">
			</li>
		</ul>
	 </div>
	</form>
</div>
<script>
	var channelDetailsTpl = {
		wid: 'channelDetailsTpl',
		ids: '',
		detail: {},
		save() {
			const that = this;
			const tmp = {
				companyName: $('#channelDetailsTpl-companyName').textbox('getValue'),
				companyNameEn: $('#channelDetailsTpl-companyNameEn').textbox('getValue'),
				companyNameAbbr: $('#channelDetailsTpl-companyNameAbbr').textbox('getValue'),
				companyNameAbbrEn: $('#channelDetailsTpl-companyNameAbbrEn').textbox('getValue'),
			};
			if(this.detail.cnInviteCompanyNameMap){
				if(!tmp.companyName) this.alert('需要公司名称')
			}
			if(this.detail.enInviteCompanyNameMap){
				if(!tmp.companyNameEn) this.alert('需要公司名称(英文)')
			}
			this.post(eippath + '/invitation/updateInvitationCompanyName',{
				serveBoothId: that.detail.serveBoothId || '',
				...tmp,
      }, null, rsp => {
				that.checkReturn(rsp);
				channel_details_inviteCodeTable();
				that.close();
				// $.messager.alert('提示','<div style="padding-left: 50px">操作成功，<br>成功数量：' + rsp.data.successNum +
				// 	(rsp.data.failNum ? ('<br>失败数量：' + rsp.data.failNum) :'') + '</div>','warning').window({width: 280});
				$.messager.show({
					title:"<span>提示</span>",
					width:'300px',
					height:'120px',
					msg:"<span style='font-size: 18px;text-align:center;'>操作成功!</span>",
				});
      })
		},
		checkReturn(obj, msg = "", icon = "error", throws = true) {
			icon = icon || "warning";
			if (obj && obj.state === 1) {
				return true;
			} else {
				this.alert(msg || obj.msg, icon, throws);
				return false;
			}
		},
		alert(msg = "", icon="error", throws = true) {
			msg = msg || '操作失败'
			if (msg) {
				$.messager.alert("提示", msg, icon ? icon : "warning");
				if (throws) throw new Error("error: " + msg);
			}
		},
		post(url, data, options, cb, cbErr) {
			const that = this
			const df = {
				async: false,
				type: "post",
				url,
				data,
				dataType: "json",
			};
			return $
			.ajax(options ? Object.assign(df, options) : df)
			.done(cb || function (rsp) {
				console.log("rsp", rsp);
			})
			.fail(cbErr || function (data,type, err) {
				console.log("error", data , type , err);
				that.alert('请求失败')
			});
		},
		open(id, index, ops) { // closeTip = false, ops) {
			if (index === void 0) this.alert('数据异常');
			this.detail = $('#channel-details-inviteCodeTable').datagrid('getRows')[index];
			// const rows = $('#sever-invoice-datagrid').datagrid('getSelections');
			// if(!rows || rows.length<1) this.alert('至少选择一项');
			$('#channelDetailsTpl-companyName,#channelDetailsTpl-companyNameAbbr').closest('li')[this.detail.cnInviteCompanyNameMap ? 'show' : 'hide']();
			$('#channelDetailsTpl-companyNameEn,#channelDetailsTpl-companyNameAbbrEn').closest('li')[this.detail.enInviteCompanyNameMap ? 'show' : 'hide']();
			let cmap = this.detail.cnInviteCompanyNameMap || {};
			let emap = this.detail.enInviteCompanyNameMap || {};
			$('#channelDetailsTpl-companyName').textbox('setValue', cmap.companyName || '');
			$('#channelDetailsTpl-companyNameEn').textbox('setValue', emap.companyNameEn || '');
			$('#channelDetailsTpl-companyNameAbbr').textbox('setValue', cmap.companyNameAbbr || '');
			$('#channelDetailsTpl-companyNameAbbrEn').textbox('setValue', emap.companyNameAbbrEn || '');
			id = (id || this.wid) + '-window'
			// this.init()
			const opts = {
				closed:false,
				cls: id,
				onBeforeOpen() {
					let w = $('.'+id)
					if(w.length>1){
						for (let i = 0; i < w.length-1; i++) {
							w[i].remove()
						}
					}
				},
			}
			$('#'+id).window(ops ? Object.assign(opts,ops) : opts).window('center');
		},
		close(id) {
			id = (id || this.wid) + '-window'
			// console.log('close')
			$("#" + id).window("close", true);
		},
	};

	function channel_details_invitTpl(name, row, index) {
		return name ? `已设置 <a href="javascript:channelDetailsTpl.open('', \${index});" style="color:blue;font-weight:bold">修改</a>` : '';
	}
</script>


<!-- 选取客户 -->
<div id="channel-details-inviteCode-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true" style="width:600px;height:500px;display: none;">
	<div class="easyui-panel" title="客户信息" style="width:100%;height:100%">
		<div id="channel-details-inviteCode-tool-bar" style="padding:6px">
			<span class="show-add-type" style="display: none">
				<label>范围</label>
				<input value="3" id="channel-details-inviteCode-tool-queryClientScope" class="easyui-combobox" style="width:150px;height: 30px;" data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,data: [{id: 1, text:'全部'},{id: 2, text:'已生成邀请码展商'},{id: 3, text:'未生成邀请码展商'}]">
			</span>
			<label>展商状态</label>
			<input value="1" id="channel-details-inviteCode-tool-queryClientState" class="easyui-combobox" style="width:150px;height: 30px;" data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,data: [{id: 1, text:'全部'},{id: 2, text:'已定展展商'},{id: 3, text:'已移交客服展商'}]">
			<div style="margin-top: 8px">
				<label>名称</label>
				<input id="channel-details-inviteCode-tool-companyName" class="easyui-textbox" style="width:364px;height: 30px;" data-options="inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents,{keyup(e){e.keyCode === 13 && channel_details_invite_form_functions.loadCompany()}})">
				<a style="width: 60px;height: 30px;margin-left: 10px;" href="javascript:void(0);" class="easyui-linkbutton" onclick="channel_details_invite_form_functions.loadCompany()">查询</a>
				<a style="width: 60px;height: 30px;float: right" href="javascript:void(0);" class="easyui-linkbutton" onclick="channel_details_invite_form_functions.selectedCompany()">确定</a>
			</div>
		</div>
		<table
				id="channel-details-inviteCode-clientTable"
				class="easyui-datagrid"
				toolbar="#channel-details-inviteCode-tool-bar"
				data-options="rownumbers:true,pageSize:20,pagination:true,multiSort:true,fitColumns:false,fit:true,method:'post',selectOnCheck:true,onDblClickRow:function (rowIndex, rowData){channel_details_invite_form_functions.selectedCompany(rowData)}">
			<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'clientId',width:70">编号</th>
					<th data-options="field:'companyName',width:450">公司名称</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
<style>
	.channel-details-dataPush {
		list-style: none;
		padding: 10px;
		margin: 0;
	}

	.channel-details-dataPush .panel-label {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 32px;
		margin-bottom: 12px;
	}

	.channel-details-dataPush .panel-label .panel-label__text {
		flex-basis: 120px;
		margin-right: 12px;
		font-size: 14px;
		text-align: right;
		line-height: 30px;
	}

	.channel-details-dataPush .panel-label .panel-label__input {
		margin-right: 12px;
		flex: 1;
		text-align: left;
	}

	.channel-details-dataPush .panel-label .panel-label__input input[type="text"] {
		height: 30px;
		padding: 3px 10px;
		border-radius: 5px;
		border: 1px solid #aaa;
		outline: none;
		width: calc(100%);
		box-sizing: border-box;
	}

	.channel-details-dataPush .panel-label .panel-label__input input[type="text"]:focus {
		border-color: #666;
	}

	.channel-details-dataPush .panel-label .panel-label__input button {
		outline: none;
		border: 1px solid #eb7878;
		background: none;
		border-radius: 5px;
		height: 30px;
		color: #eb7878;
		cursor: pointer;
	}

	.channel-details-dataPush .panel-label .panel-label__input button:hover, .channel-details-dataPush .panel-label .panel-label__input button:active {
		color: #eb4c4c;
		border: 1px solid #eb4c4c;
	}

	.channel-details-dataPush .panel-label .panel-label__operat {
		flex-basis: 120px;
	}

	.channel-details-dataPush .panel-label .panel-label__operat button {
		outline: none;
		cursor: pointer;
		border: 1px solid #3275ff;
		background: none;
		border-radius: 5px;
		height: 30px;
		color: #3275ff;
		width: 90px;
	}

	.channel-details-dataPush .panel-label .panel-label__operat button:hover, .channel-details-dataPush .panel-label .panel-label__operat button:active {
		color: #0075ff;
		border: 1px solid #0075ff;
	}
</style>
<script type="text/javascript">
	var channel_details_current_tab_index = 0
	var channel_details_current_row = null
	var channel_details_tplId = sessionStorage.getItem('tplId') || ''; // 展商邀请函模板ID
	sessionStorage.removeItem('tplId');
	var channel_details_invite_form_functions = {
		// $window: $('#channel-details-inviteCodeWindow'),
		// $companySelector: $('#channel-details-inviteCode-select-company'),
		// $clientTable: $('#channel-details-inviteCode-clientTable'),
		isMultipleSelect: false,
		currentOpenType: '',
		queryClientScope: '',
    store: {},
		get$companySelector() {
			return $('#channel-details-inviteCode-select-company');
		},
		get$clientTable() {
			return $('#channel-details-inviteCode-clientTable');
		},
		get$window() {
			return $('#channel-details-inviteCodeWindow');
		},
		get$eicTable() {
			return $('#channel-details-inviteCodeWindow-eic-datagrid')
		},
		get$eicWindow() {
			return $('#channel-details-inviteCodeWindow-eic-window')
		},
    get$beicWindow() {
      return $('#channel-details-inviteCodeWindow-beic-window')
    },
		getEasyUIControlType($el) {
			if (!$el || !$el.attr('class')) return null
			const matcher = $el.attr('class').match(/easyui-(?<type>[a-z]+)/)
			return matcher && matcher.groups && matcher.groups.type
		},
		eachForm(callback) {
			'inviteCodeId,contractProjectId,clientId,companyName,inviteCode,useNumber,state,inviteTemplateId'.split(',').forEach(key => {
				const $el = $('#channel-details-inviteCodePanel-' + key)
				callback(key, $el, this.getEasyUIControlType($el), $el.attr('type') === 'checkbox')
			})
		},
		eachInnerForm(callback) {
			'inviteCode,useNumber,state'.split(',').forEach(key => {
				const $el = $('#channel-details-inviteCodePanel-eicw-' + key)
				callback(key, $el, this.getEasyUIControlType($el), $el.attr('type') === 'checkbox')
			})
		},
		eachCompanySelectorSearch(callback) {
			'queryClientScope,queryClientState,companyName'.split(',').forEach(key => {
				const $el = $('#channel-details-inviteCode-tool-' + key)
				callback(key, $el, this.getEasyUIControlType($el))
			})
		},
		initInviteTpls(pid) {
			let flag = $.ajaxSettings.async
			$.ajaxSettings.async = false
			$('#channel-details-inviteCodePanel-inviteTemplateId').combobox({
				url: eippath + '/inviteTemplate/getInviteTemplateList',
				queryParams: {
					exhibitCode: exhibitCode_for_pass,
					projectId: pid || '',
					publishFlag: true,
				},
				loadFilter(data) {
					return data.data || []
				}
			})
			$.ajaxSettings.async = flag
		},
		clear() {
			this.eachForm((key, $el, type, isCheckbox) => type ? $el[type]('clear') : isCheckbox ? $el.prop('checked', false) : $el.val(''))
		},
		close() {
			this.currentOpenType = ''
			this.queryClientScope = ''
			$('#channel-details-inviteCodePanel-contractProjectId').combobox('enable')
			$('#channel-details-inviteCodePanel-clientId').parent().css('opacity', 1)
			$('.show-add-type').hide()
			$('.show-make-invite').hide()
			$('.hide-make-invite').show()
			this.get$window().window('close')
		},
		autoUseInviteCodeChange() {
			const serveBoothId = this.get$window().data('serveBoothId')
			const autoUseInviteCode = $('#channel-details-inviteCodePanel-beicw-autoUseInviteCode').prop('checked')
			const autoUseInviteCodeAfterForward = $('#channel-details-inviteCodePanel-beicw-autoUseInviteCodeAfterForward').prop('checked')
			const post = {
				serveBoothId,
				autoUseInviteCode,
        autoUseInviteCodeAfterForward,
			}
			$.messager.progress()
			$.ajax({
				url: eippath + '/inviteCode/updateAutoUseInviteCode',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: post,
				success: function (data) {
					if (data.state !== 1)
						return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
					$.messager.progress('close')
					$.messager.alert('提示', '保存成功', 'info')
					channel_details_inviteCodeTable()
				},
				error: function () {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			})
		},
		closeInner() {
			this.get$eicWindow().window('close').removeAttr('inviteCodeId')
		},
		open(type, options) {
			this.currentOpenType = type
			$('.showMultipleSelect,.hideMultipleSelect').hide()
			this.isMultipleSelect ? $('.showMultipleSelect').show() : $('.hideMultipleSelect').show()
			const $autoUseInviteCodeLi = $('#channel-details-inviteCodePanel-beicw-autoUseInviteCode').parents('li')
			if (type !== 'edit') {
				$('.showEditInviteCode').hide()
				$('#channel-details-inviteCodeWindow-toolbar').show()
				$autoUseInviteCodeLi.hide()
			} else {
				$autoUseInviteCodeLi.show()
			}
			$('#channel-details-inviteCodePanel-inviteTemplateId').closest('li').show();
			$('#channel-details-inviteCodePanel-inviteTemplateId').combobox('enable')
			if(type == 'make') {
				$('#channel-details-inviteCodePanel-inviteTemplateId').closest('li').hide();
			}
			if(type == 'edit') {
				$('#channel-details-inviteCodePanel-inviteTemplateId').combobox('disable')
			}

			this.get$window().window({...options}).window('open')
		},
		openInner(row) {
			const _win = this.get$eicWindow()
			_win.window({title: !!row ? '修改邀请码': '新增邀请码'}).window('open')
			if (row) {
				_win.attr({inviteCodeId: row.inviteCodeId})
				this.eachInnerForm((key, $el, type) => {
					if (key === 'state') {
						const val = row[key] === 1 ? 1 : 0
						return $el.find(`[name="cdi-eicw-state"][value="\${val}"]`).prop('checked', true)
					}
					type ? $el[type]('setValue', row[key]) : $el.val(row[key])
				})
			} else {
				this.eachInnerForm((key, $el, type) => {
					if (key === 'state') {
						return $el.find(`[name="cdi-eicw-state"][value="0"]`).prop('checked', true)
					}
					type ? $el[type]('clear') : $el.val('')
				})
			}
		},
		addInner() {
			return this.openInner()
		},
		editInner() {
			const table = this.get$eicTable()
			const selected = table.datagrid('getSelected')
			if (!selected) return $.messager.alert('提示', '请先选择一条记录', 'warning')
			this.openInner(selected)
		},
		add() {
			this.isMultipleSelect = false
			channel_details_load_project()
			this.clear()
			$('.show-add-type').show()
			$('#channel-details-inviteCodePanel-contractProjectId').combobox('enable').combobox('setValue', project_for_pass)
			this.triggerStateEvent(true)
			this.randomInviteCode()
			this.open('add', {title: '新增展商渠道'})
		},
		edit(row) {
			const $hideEditInviteCode = $('.hideEditInviteCode')
			this.isMultipleSelect = false
			channel_details_load_project()
			this.clear()
			this.eachForm((key, $el, type) => {
				if (key === 'state') return this.triggerStateEvent(row[key] === 0)
				type ? $el[type]('setValue', row[key]) : $el.val(row[key])
			})
			$('.show-make-invite').hide()
			$('.showEditInviteCode').show()
			$hideEditInviteCode.hide()
			$('#channel-details-inviteCodePanel-contractProjectId').combobox('disable')
			$('#channel-details-inviteCodePanel-inviteTemplateId').combobox('setValue',row.inviteTemplateId)
			$('#channel-details-inviteCodePanel-clientId').parent().css('opacity', 0.5)
			const $autoUseInviteCode = $('#channel-details-inviteCodePanel-beicw-autoUseInviteCode')
			$autoUseInviteCode.parents('li').show()
			$autoUseInviteCode.prop('checked', !!row.autoUseInviteCode)
			$('#channel-details-inviteCodePanel-beicw-autoUseInviteCodeAfterForward').prop('checked', !!row.autoUseInviteCodeAfterForward)
			this.open('edit', {title: '修改展商渠道'})
			setTimeout(() => {
				$hideEditInviteCode.hide()
				this.get$window().data('serveBoothId', row.serveBoothId)
				this.getInviteCodeDetails(row.serveBoothId)
			})
		},
		make() {
			this.isMultipleSelect = true
			channel_details_load_project()
			this.clear()
			$('#channel-details-inviteCodePanel-contractProjectId').combobox('enable').combobox('setValue', project_for_pass)
			$('#channel-details-inviteCodePanel-selectedLength').text('0')
			$('#channel-details-inviteCodePanel-clientIds').val('')
			this.open('make', {title: '批量生成邀请码'})
		},
		makeInvite() {
			this.isMultipleSelect = true
			channel_details_load_project()
			this.clear()
			$('.show-make-invite').show()
			$('.hide-make-invite').hide()
			$('#channel-details-inviteCodePanel-contractProjectId').combobox('enable').combobox('setValue', project_for_pass)
			$('#channel-details-inviteCodePanel-selectedLength').text('0')
			$('#channel-details-inviteCodePanel-clientIds').val('')
			this.open('make-invite', {title: '批量生成邀请函'})
		},
		loadCompany() {
			const projectId = $('#channel-details-inviteCodePanel-contractProjectId').combobox('getValue')
			if (!projectId) return false
			const queryParams = {projectId}
			this.eachCompanySelectorSearch((key, $el, type) => {
				queryParams[key] = type ? $el[type]('getValue') : $el.val()
			})
			if (this.queryClientScope) {
				queryParams.queryClientScope = this.queryClientScope
			}
			this.get$clientTable().datagrid({
				url: '/eip-web-sponsor/inviteCode/getClients',
				queryParams,
				singleSelect: !this.isMultipleSelect
			})
			return true
		},
		openCompanySelector() {
			const $queryClientState = $('#channel-details-inviteCode-tool-queryClientState').combobox('enable')
			this.queryClientScope = ''
			if (this.currentOpenType === 'edit') return
			else if (this.currentOpenType === 'make') this.queryClientScope = 3
			else if (this.currentOpenType === 'make-invite') {
				this.queryClientScope = 4
				$queryClientState.combobox('setValue', 3).combobox('disable')
			}
			if (!this.loadCompany()) return $.messager.alert('提示', '请先选择项目', 'warning')
			this.get$companySelector().window('open')
		},
		selectedCompany(_row) {
			if (_row && this.isMultipleSelect) return
			const rows = _row ? [_row] : this.get$clientTable().datagrid('getSelections')
			const rowsLength = rows.length
			if (!this.isMultipleSelect) {
				if (!rowsLength) return $.messager.alert('提示', '请先选择客户', 'warning')
				const row = rows[0]
				this.eachForm((key, $el, type) => {
					if (key === 'clientId') {
						$el.val(row.clientId)
					} else if (key === 'companyName') {
						$el[type]('setValue', row.companyName)
					}
				})
			} else {
				const inner = (clientIds) => {
					const total = this.get$clientTable().datagrid('getPager').data("pagination").options.total
					$('#channel-details-inviteCodePanel-selectedLength').text(clientIds ? rowsLength : total)
					$('#channel-details-inviteCodePanel-clientIds').val(clientIds ? clientIds.toString() : 'QUERY')
				}
				if (!rowsLength) $.messager.confirm('提示', '未勾选公司，是否操作所有检索结果?', r => r && inner())
				else inner(rows.map(item => item.clientId))
			}
			this.get$companySelector().window('close')
		},
		randomInviteCode() {
			const inviteCode = Math.random().toString(10).slice(2, 8)
			$('#channel-details-inviteCodePanel-inviteCode').textbox('setValue', inviteCode)
		},
		batchCreateInvitation(post) {
			delete post.useNumber
			post.citeCataInfoFlag = $('#channel-details-inviteCodePanel-citeCataInfoFlag').prop('checked')
			$.messager.progress()
			$.ajax({
				url: eippath + '/invitation/batchCreateInvitation',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: post,
				success: function (data) {
					if (data.state !== 1)
						return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
					channel_details_inviteCodeTable()
					$.messager.progress('close')
					$.messager.alert('提示', '保存成功', 'info')
					channel_details_invite_form_functions.close()
				},
				error: function () {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			})
		},
		saveForm() {
			const post = {}
			this.eachForm((key, $el, type) => {
				if (key === 'state'){
					post[key] = $('#channel-details-inviteCodePanel-state').prop('checked') ? 0 : 1
					return
				}
				post[key] = type ? $el[type]('getValue') : $el.val()
			})
			if (!post.contractProjectId) return $.messager.alert('提示', '请选择项目', 'warning')
			if (!this.isMultipleSelect) {
				if (!post.clientId) return $.messager.alert('提示', '请选择客户', 'warning')
				if (post.state === 0 && !post.inviteCode) return $.messager.alert('提示', '请设置邀请码', 'warning')
				$.messager.progress()
				$.ajax({
					url: eippath + '/inviteCode/save',
					dataType: "json",	//返回数据类型
					type: 'post',
					data: post,
					success: function (data) {
						if (data.state !== 1)
							return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
						channel_details_inviteCodeTable()
						$.messager.progress('close')
						$.messager.alert('提示', '保存成功', 'info')
						channel_details_invite_form_functions.close()
					},
					error: function () {
						$.messager.alert('提示', '数据发送失败！', 'error')
						$.messager.progress('close')
					}
				})
			} else {
				const clientIds = $('#channel-details-inviteCodePanel-clientIds').val()
				if (!clientIds) return $.messager.alert('提示', '请设置客户', 'warning')
				let mergeParams = {}
				if (clientIds === 'QUERY') {
					mergeParams = this.get$clientTable().datagrid('options').queryParams
				} else {
					mergeParams = {clientIds}
				}
				const makePost = {
					projectId: post.contractProjectId,
					useNumber: post.useNumber,
					...mergeParams,
				}
				if (this.currentOpenType === 'make-invite'){
					makePost.inviteTemplateId = $('#channel-details-inviteCodePanel-inviteTemplateId').combobox('getValue');
					return this.batchCreateInvitation(makePost)
				}
				$.messager.progress()
				$.ajax({
					url: eippath + '/inviteCode/batchAdd',
					dataType: "json",	//返回数据类型
					type: 'post',
					data: makePost,
					success: function (data) {
						if (data.state !== 1)
							return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
						channel_details_inviteCodeTable()
						$.messager.progress('close')
						$.messager.alert('提示', '保存成功', 'info')
						channel_details_invite_form_functions.close()
					},
					error: function () {
						$.messager.alert('提示', '数据发送失败！', 'error')
						$.messager.progress('close')
					}
				})
			}
		},
		saveInnerForm() {
			const _win = this.get$eicWindow()
			const post = {
				contractProjectId: $('#channel-details-inviteCodePanel-contractProjectId').combobox('getValue'),
				clientId: $('#channel-details-inviteCodePanel-clientId').val(),
				inviteCodeId: _win.attr('inviteCodeId') || ''
			}
			this.eachInnerForm((key, $el, type) => {
				if (key === 'state') {
					post[key] = $el.find('[name="cdi-eicw-state"]:checked').val()
					return
				}
				post[key] = type ? $el[type]('getValue') : $el.val()
			})
			if (!post.contractProjectId) return $.messager.alert('提示', '请选择项目', 'warning')
			if (post.state === 0 && !post.inviteCode) return $.messager.alert('提示', '请设置邀请码', 'warning')
			post['autoUseInviteCode'] = $('#channel-details-inviteCodePanel-beicw-autoUseInviteCode').prop('checked')
			$.messager.progress()
			$.ajax({
				url: eippath + '/inviteCode/save',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: post,
				success: function (data) {
					if (data.state !== 1)
						return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
					channel_details_inviteCodeTable()
					$.messager.progress('close')
					$.messager.alert('提示', '保存成功', 'info')
					channel_details_invite_form_functions.closeInner()
					channel_details_invite_form_functions.refreshDetails()
				},
				error: function () {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			})
		},
		initEvent() {
			$('#channel-details-inviteCodePanel-state').change(function(){
				const off = !$(this).prop('checked')
				$('.hideInviteCodePanelStateOff')[off ? 'hide': 'show']()
			})
		},
		triggerStateEvent(setValue) {
			const $state = $('#channel-details-inviteCodePanel-state')
			if (setValue !== void 0) $state.prop('checked', !!setValue)
			setTimeout(() => $state.trigger('change'))
		},
		getInviteCodeDetails(serveBoothId) {
			const $table = this.get$eicTable()
			$table.datagrid({
				url: '/eip-web-sponsor/inviteCode/selectExhibitorInviteCode',
				queryParams: {serveBoothId},
				loadFilter(data) {
					return data.state === 1 ? data.data : []
				}
			})
		},
		refreshDetails() {
			this.get$eicTable().datagrid('reload')
		},
    openBeicWindow(operatorType, title) {
      if (![1, 2].includes(operatorType)) throw new Error('参数错误')

			const _innerOpen = (total, queryParams) => {
				const $hint = $('#channel-details-inviteCodeWindow-beicw-hint')
				$hint.text('已选择' + total + '个展商渠道')
				const $beicWindow = this.get$beicWindow()
				const keyMap = ['','updateNumber','updateState']
				const showId = '#channel-details-inviteCodePanel-beicw-' + keyMap[operatorType]
				$(showId).parents('li').show()
				$beicWindow.window({title: '批量' + title}).window('open')
				this.store.beicw = {total, queryParams, operatorType}
			}

      const $table = $('#channel-details-inviteCodeTable')
      const rows = $table.datagrid('getSelections')
      if (!rows.length) {
        const data = ($table.datagrid('getData')|| {}).rows || []
        if (!data.length) return $.messager.alert('提示', '没有可操作的数据', 'warning')
        return $.messager.confirm('提示', '是否操作全部数据？', r => {
          if (!r) return
					const total = $table.datagrid('getPager').data("pagination").options.total
					const queryParams = $table.datagrid('options').queryParams
					_innerOpen(total, queryParams)
				})
      }
			const total = rows.length
			const queryParams = {serveBoothIds: rows.map(item => item.serveBoothId).join(',')}
			_innerOpen(total, queryParams)
    },
    closeBeicWindow() {
      const keyMap = ['updateNumber','updateState']
      keyMap.forEach(key => {
        const showId = '#channel-details-inviteCodePanel-beicw-' + key
        $(showId).parents('li').hide()
      })
      this.get$beicWindow().window('close')
    },
		saveBeicForm() {
			const {queryParams, operatorType} = this.store.beicw
			if (operatorType === 0) return $.messager.alert('提示', '参数错误', 'warning')
      const post = {...queryParams, operatorType}
      const key = ['','updateNumber','updateState'][operatorType]
			const $el = $('#channel-details-inviteCodePanel-beicw-' + key)
			if (key === 'updateState') {
				post[key] = $el.find('[name="cdi-beicw-state"]:checked').val()
			} else {
				post[key] = $el.val()
			}
      $.messager.progress()
      $.ajax({
        url: eippath + '/inviteCode/batchUpdateInviteCode',
        dataType: "json",	//返回数据类型
        type: 'post',
        data: post,
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('提示', data.msg || '数据加载失败！', 'error', () => $.messager.progress('close'))
          channel_details_inviteCodeTable()
          $.messager.progress('close')
          $.messager.alert('提示', data.msg || '操作成功', 'info')
          channel_details_invite_form_functions.closeBeicWindow()
        },
        error () {
          $.messager.alert('提示', '数据发送失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
	}
	var channel_details_query = {}
	var channel_details_config = {
		projectId: '',
		targetType: ''
	}
	setTimeout(() => {
		$("#channel-details-search-targetType").combobox({
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {projectId: '', actorCode: 'Viewer'},
			method: 'POST',
			loadFilter(data) {
				return data.data
			},
			onLoadSuccess() {
				channel_details_setQuery(null, true)
			}
		})
		$('#channel-details-set-panel-channelTypeId').combobox({
			url: eippath + '/channelType/getList',
			loadFilter(data) {
				data.unshift({
					channelTypeId: 4,
					channelTypeName: "扫码即到场"
				})
				return data
			},
		})
		$('#channel-details-set-panel-4-tips').hide()
		channel_details_invite_form_functions.initEvent()
    channel_details_load_project()
	}, 0);
	function channel_details_setQuery(query, refresh) {
		channel_details_query = query || {projectName: '${projectName}', targetType: '${targetType}'}
		$('#channel-details-search-projectName').val(channel_details_query.projectName)
		channel_details_query.targetType && $('#channel-details-search-targetType').combobox('setValue', +channel_details_query.targetType)
		if (refresh) {
			$('#channel-details-tabs').tabs('select', channel_details_tplId ? 1 : 0)
			channel_details_fresh()
		}
	}
	function channel_details_load_project(id, callback) {
    id = id || '#channel-details-inviteCodePanel-contractProjectId'
		$.ajax({
			url: eippath + '/project/selectByExhibitCode',
			data: {
				exhibitCode: exhibitCode_for_pass
			},
			type: 'post',
			success(resp) {
				try {
					const data = JSON.parse(resp.replace(/\t/g, '    '))
					$(id).combobox('loadData', data)
          callback && callback(data)
				} catch (e) {
					console.log(e, resp)
					$.messager.alert('错误', '数据加载失败！', 'error')
				}
			},
			error(err) {
				console.log(err)
				$.messager.alert('错误', '数据加载失败！', 'error')
			}
		})
	}
  function channel_details_load_roles(regProjectId, setDefault) {
    $("#channel-details-set-panel-targetList").combobox({
      url: '/RegSever/projRegSet/getRegTarget',
      queryParams: {regProjectId},
      method: 'POST',
      loadFilter(data) {
        return data.data
      },
      onLoadSuccess({data}) {
        if (!setDefault) return
        const item = data[0]
        if (!item) return
        $(this).combobox('setValue', item.targetId)
      }
    })
  }
	function channel_details_fresh() {
		channel_details_inviteCodeTable()
		channel_details_search(true)
	}
	function channel_details_mod(_row, index) {
		if (channel_details_current_tab_index === 1) {
			const $table = $('#channel-details-inviteCodeTable')
			if (!_row) {
				if (index === void 0) return $.messager.alert('数据异常', '错误', 'warning')
				_row = $table.datagrid('getRows')[index]
			}
			const rows = _row ? [_row] : $table.datagrid('getSelections');
			let error;
			if (!rows.length) error = '未选中内容'
			if (rows.length > 1) error = '一次只能编辑一条数据'
			if (error) return $.messager.alert('提示', error, 'warning')
			return channel_details_invite_form_functions.edit(rows[0])
		}
	}
	function channel_details_tabs_change(title, index) {
		channel_details_current_tab_index = index
		$('.show-of-tab').hide()
		$('.show-of-tab-' + index).show()
	}
	function channel_details_operation(state, row, index) {
		return `<a href="javascript:" onclick="channel_details_mod(null, \${index})" style="color:#2E82E4;font-weight:bold;">编辑</a>`
	}
	function channel_details_buyerNumber(buyerNumber, row) {
		const {clientId} = row
		if (!clientId || !buyerNumber) return
		return `<a href="javascript:" onclick="channel_details_goto_order_list(\${clientId})" style="color:blue;font-weight:bold;" target="_blank">\${buyerNumber}</a>`
	}
	function channel_details_inviteCode(inviteCode, row, index) {
		return `<a href="javascript:" onclick="channel_details_mod(null, \${index})" style="color:blue;font-weight:bold">\${inviteCode || '设置'}</a>`
	}
	function channel_details_inviteBuyerNum(inviteBuyerNum, row) {
		const {clientId} = row
		if (!inviteBuyerNum || !clientId) return
		return `<a href="/RegSever/RegPage/pages/pms_buyer_reg_list_eip.html?EID=\${exhibitCode_for_pass}&orgnum=\${org_num_for_pass}&target=1&channelType=2&channelId=\${clientId}&channelName=&buyerId=" style="color:blue;font-weight:bold;" target="_blank">\${inviteBuyerNum}</a>`
	}
	function channel_details_invitationList(invitationList, row, index) {
		if (!Array.isArray(invitationList) || !invitationList.length) return
		return `<a onclick="channel_details_invite_open2d(\${index},\`\${row.companyName}\`)" href="javascript:" style="color:blue;font-weight:bold;">查看</a>`
	}
	function channel_details_projRegSetList(val, row, index) {
		if (!val) return
		return `<a onclick="channel_details_open2d({type:'mpro',index:\${index}})" href="javascript:" style="color:blue;font-weight:bold;">查看</a>`

	}
	function channel_details_change_operation(inviteCodeId, state) {
		const _request = () => {
			$.messager.progress()
			$.ajax({
				url: eippath + '/inviteCode/updateState',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: {inviteCodeId, state},
				success: function (data) {
					if (data.state !== 1)
						return $.messager.alert('提示', '数据加载失败！', 'error', () => $.messager.progress('close'))
					channel_details_inviteCodeTable()
					$.messager.progress('close')
					$.messager.alert('提示', '操作成功', 'info')
				},
				error: function () {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			})
		}
		state === 1 ? $.messager.confirm('提示', '确定要停用该邀请码吗？', r => r && _request()) : _request()
	}
	function channel_details_inviteCodeTable(isExport) {
		const queryParams = {
			exhibitCode: exhibitCode_for_pass,
			projectName: $('#channel-details-search-projectName').val(),
			companyName: $('#channel-details-search-companyName').val(),
			inviteCodeState: $('#channel-details-search-inviteCodeState').combobox('getValue'),
			inviteTemplateId: channel_details_tplId,
			invitationState: $('#channel-details-search-invitationState').combobox('getValue')
		}
		channel_details_tplId = '';
		if (isExport) return channel_invite_table_export(queryParams)
		$('#channel-details-inviteCodeTable').datagrid({
			url: eippath + '/inviteCode/getExhibitorList',
			queryParams,
			loadFilter(data) {
				data.rows.forEach(row => row.state = row.inviteCodeState)
				return data
			}
		});
	}
	function channel_details_DeleteInviteCode() {
		const rows = $('#channel-details-inviteCodeTable').datagrid('getSelections');
		if (!rows.length > 0) return $.messager.alert('提示', '未选中内容！', 'warning');
		const inviteCodeIds = rows.map(item => item.inviteCodeId).toString()
		$.messager.confirm('提示', `确定删除\${rows.length}条数据吗？`, function (r) {
			if (!r) return
			$.messager.progress()
			$.ajax({
				url: eippath + '/inviteCode/batchDelete',
				dataType: "json",
				type: "post",
				data: {inviteCodeIds},
				async: true,
				success: function (data) {
					if (data.state !== 1){
						$.messager.alert('提示', '删除失败！', 'error');
						return $.messager.progress('close')
					}

					let message = '操作成功'
					const failure = data.data|| []
					if (failure.length) {
						message += `, 但是有\${failure.length}条数据删除失败了。 `
					}
					$.messager.alert('提示', message, 'info');
					channel_details_inviteCodeTable();
					$.messager.progress('close')
				},
				error() {
					$.messager.alert('提示', '数据发送失败！', 'error');
					$.messager.progress('close')
				}
			});
		});
	}
	function channel_details_search(flag){
		$.ajax({
			url: '/eip-web-sponsor/project/getAllByExhibitCode',
			type: "post",
			data: {exhibitCode: exhibitCode_for_pass},
			datatype: 'json',
			success(resp) {
				try {
					window.AllProjectByExhibitCode = JSON.parse(resp.replace(/\t/g, '    '))
				} catch(e) {
					console.log(e, resp)
					window.AllProjectByExhibitCode = []
					$.messager.alert('警告', '项目名存在坏字符！', 'warning')
				}
				if(flag)$('#channel-details-search-channelTypeName').val("");
				$('#channel-details-datagrid').datagrid({
					url: '/eip-web-sponsor/channel/getChannelTargetList',
					queryParams: {
						channelTypeName: $('#channel-details-search-channelTypeName').val(),
						channelName: $('#channel-details-search-channelName').val(),
						projectName: $('#channel-details-search-projectName').val(),
						exhibitCode: exhibitCode_for_pass,
						targetType: $('#channel-details-search-targetType').combobox('getValue'),
					},
					onLoadSuccess(data) {
						if (channel_details_query.targetType && channel_details_query.projectName) {
							const rows = data.rows
							if (rows.length) {
								channel_details_open2d(1,``,rows[0].projectId,'', 1, rows[0].targetType)
								channel_details_query = {}
							}
						}
					}
				});
			},
			error(err) {
				$.messager.alert('数据加载失败！')
			},
		})
	}
	function channel_details_channelTypeName(val, row){
		const isColor = row.channelTypeId === 4 || row.channelTypeId === 1 || !row.channelTypeId
		const t = !val && row.channelTypeId === 4 ? '扫码即到场' : val
		return isColor ? `<span style="color: #4c72fe;">\${t}</span>` : t
	}
	function channel_details_regAddressBtn(val, row) {
		let {regEnglish, channelName, channelTypeId, channelId, projectId, targetType} = row
		channelId = channelId || '``'
		channelTypeId = channelTypeId || '``'
		channelName = channelName || ''
		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_open2d(' + channelTypeId + ',' + channelId + ',' + projectId + ',\'' + channelName + '\', 1, ' + targetType + ')">中文版地址</a>' + '<i style="margin-right: 40px"/>' +
				(regEnglish ? '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_open2d(' + channelTypeId + ',' + channelId + ',' + projectId + ',\'' + channelName + '\', 2, ' + targetType + ')">英文版地址</a>' : '')
	}
	/**
	 * @param {number} projectId
	 * @param {number} version
	 * @param {number|''} targetType
	 * */
	function channel_details_get_project_logo(projectId, version, targetType = 1) {
		// targetType === '' 表示需要直接获取展会或项目LOGO
		function queryData2image(src) {
			const inner = src => {
				if (!src) return '/RegSever/RegPage/images/top-lucency.png'
				const prefix = location.origin
				src = src.replace(/\\/g, '/')
				if (/^(https?:)?\/\//.test(src)) return src
				if (src.startsWith('/')) return prefix + src
				return prefix + '/image/' + src
			}
			const url = inner(src)
			if (!url) return ''
			try {
				return new URL(url).host.indexOf('aliyuncs.com') !== -1
						? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
						: url
			} catch (e) {
				return url
			}
		}
		let QRCodeLogo = ''
		$.ajax({
			url: '/eip-web-sponsor/api/project/getProjectLogo',
			data: {projectId, targetType},
			type: "post",
			async: false,
			success(data) {
				if (data.state !== 1) return
				const buyerLogoKey = version === 2 ? 'buyerRegEnLogo' : 'buyerRegCnLogo'
				const result = data.data || {}
				let logo = result[buyerLogoKey] || result['exhibitionLogo'] || result['projectLogo']
				if (targetType === '') {
					logo = result['exhibitionLogo'] || result['projectLogo']
				}
				QRCodeLogo = queryData2image(logo)
			},
			error(err) {
				console.warn(err)
			}
		})
		return queryData2image(QRCodeLogo)
	}
	function channel_details_get_cached_logoFn() {
		const cache = {}
		return function (pid, version, target) {
			let key = pid + '-' + version
			if (target) key += '-' + target
			if (cache[key]) return cache[key]
			return (cache[key] = channel_details_get_project_logo(pid, version, target))
		}
	}

	function channel_details_invite_open2d(index, companyName, version) {
		const $win = $('#channel-details-invite-address-window')
		if (index === void 0) {
			index = +$win.attr('index')
		}
		if (companyName === void 0) {
			companyName = $win.attr('companyName')
		}
		if (version === void 0) {
			version = 1
		}
		if (isNaN(index)) return
		const {invitationList, projectId} = $('#channel-details-inviteCodeTable').datagrid('getRows')[index]
		if (!Array.isArray(invitationList) || !invitationList.length) return
		$('[id^="channel-details-invite-address-version"]').removeClass('webSetStyleActive')
		$('#channel-details-invite-address-version' + version).addClass('webSetStyleActive')
		const changer = $('#channel-details-invite-address-enable-english')
		invitationList.length > 1 ? changer.removeClass('hide') : changer.addClass('hide')
		const invitation = invitationList.find(item => item.version === version) || invitationList[0]
		const uri = '/RegSever/RegPage/pages/invitation-letter/invitation-letter-2024.html'
		let target = new URL(uri, location.origin)
		target.searchParams.set('id', invitation.invitationId)
		target = target.href
		let tip = '手机邀请函地址'
		if (version === 2) tip += ' (英文)'
		$("#channel-details-invite-address-phone-text").text(tip);
		$("#channel-details-invite-address-code").empty();
		$('#channel-details-invite-address-phone').val(target);
		$("#channel-details-register-invite-phone-open").attr({href: target})
		const logo = channel_details_get_project_logo(projectId, invitation.version, '')
		$('#channel-details-invite-address-code').qrcode({
			text: target,
			correctLevel: 0,
			src: logo,
			width: '1000',
			height: '1000',
			imgWidth : 200,
			imgHeight : 200,
		});
		$("canvas").css("width","170px")
		$win.window('open').attr({companyName, index})
	}
	function channel_details_invite_Download() {
		var type = 'png';
		var c = $('#channel-details-invite-address-code').find('canvas')[0];
		var imgdata = c.toDataURL("image/png");
		var fixtype = function (type) {
			type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
			var r = type.match(/png|jpeg|bmp|gif/)[0];
			return 'image/' + r;
		}
		imgdata = imgdata.replace(fixtype(type), 'image/octet-stream');
		var savaFile = function (data, filename) {
			var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
			save_link.href = data;
			save_link.download = filename;
			var event = document.createEvent('MouseEvents');
			event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
			save_link.dispatchEvent(event);
		}
		const $window = $('#channel-details-invite-address-window')
		const companyName = $window.attr('companyName')
		savaFile(imgdata, `\${companyName}邀请函二维码.\${type}`)
	}

	function channel_details_dataPush(val, row) {
		const {startUsing, projectId, channelTargetId} = row
		if (!channelTargetId) return ''
		if (!startUsing) {
			return `<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_StartUsing(\${startUsing},\${projectId},\${channelTargetId})">启用</a>`
		}
		return `<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_StartUsing(\${startUsing},\${projectId},\${channelTargetId})">关闭</a>
					&nbsp;&nbsp;&nbsp;
					<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_dataPushSetting(\${channelTargetId})">接口参数</a>&nbsp;&nbsp;&nbsp;
					<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_PushInterDataSet(\${channelTargetId})">设置</a>`
	}
	//设置启用与不启用
	function channel_details_StartUsing(startUsing, projectId, channelTargetId) {
		const request = ()=>{
			$.ajax({
				url: eippath + '/channel/getStartUsing',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: {
					startUsing: !startUsing,
					projectId,
					channelTargetId
				},
				success: function(data){
					if(data.state !== 1)
						return $.messager.alert('提示','数据加载失败！','error', ()=>$.messager.progress('close'))
					channel_details_fresh()
					$.messager.progress('close')
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error')
				}
			})
		}
		startUsing　? $.messager.confirm('提示', '确定要关闭吗？', r=> r && request()) : request()
	}
	function channel_details_dataPushSetting(channelTargetId) {
		dataPushGetStartUsing({channelTargetId}, data=>{
			$('#channel-details-dataPushWindow').window('open')
			const $copyButtons = $('.channel-details-dataPush .panel-label__operat button')
			const $downloadExplain = $('#channel_details_downloadExplain')
			const $downBuyerQuestion = $('#channel_details_downBuyerQuestion')
			$copyButtons.click(function () {
				const $input = $(this).parents('.panel-label').find('.panel-label__input input[type="text"]')
				$input.select() // 选择对象
				document.execCommand("Copy") // 执行浏览器复制命令
			})
			$downloadExplain.click(function () {
				window.open('/eip-web-sponsor/document/展之系统推送接收数据API操作文档.docx', '_blank')
			})
			$downBuyerQuestion.off('click').click(function () {
				const {projectId, targetType} = data
				const search = new URLSearchParams({projectId, targetType}).toString()
				window.open('/eip-web-sponsor/question/downBuyerQuestion?' + search, '_blank')
			})
			dataPushAssignment(data)
		})
	}
	function channel_details_PushInterDataSet(channelTargetId) {
		dataPushGetStartUsing({channelTargetId}, data => {
			const {
				channelRepeatReturnBarcode,
				autoSendTickets,
				sendRegResultMsg,
				receiveBuyerQuestionnaireInfo,
				currentChannelCoverOtherChannelBuyerInfo,
				useThirdBarcode
			} = data
			$('#channel-details-autoSendTickets').prop('checked', autoSendTickets)
			$('#channel-details-li-srm')[autoSendTickets ? 'show': 'hide']()
			$('#channel-details-sendRegResultMsg').prop('checked', sendRegResultMsg)
			$('#channel-details-channelRepeatReturnBarcode').prop('checked', channelRepeatReturnBarcode)
			$('#channel-details-receiveBuyerQuestionnaireInfo').prop('checked', receiveBuyerQuestionnaireInfo)
			$('#channel-details-currentChannelCoverOtherChannelBuyerInfo').prop('checked', currentChannelCoverOtherChannelBuyerInfo)
			$('#channel-details-useThirdBarcode').prop('checked', useThirdBarcode)
			$('#channel-details-PushInterWindow').window('open').attr({channelTargetId})
		})
	}

	function channel_details_PushInterDataSetSave() {
		const $window = $('#channel-details-PushInterWindow')
		const channelTargetId = $window.attr('channelTargetId')
		const autoSendTickets = $('#channel-details-autoSendTickets').prop('checked')
		const sendRegResultMsg = $('#channel-details-sendRegResultMsg').prop('checked')
		const channelRepeatReturnBarcode = $('#channel-details-channelRepeatReturnBarcode').prop('checked')
		const receiveBuyerQuestionnaireInfo = $('#channel-details-receiveBuyerQuestionnaireInfo').prop('checked')
		const currentChannelCoverOtherChannelBuyerInfo = $('#channel-details-currentChannelCoverOtherChannelBuyerInfo').prop('checked')
		const useThirdBarcode = $('#channel-details-useThirdBarcode').prop('checked')
		$.messager.progress()
		$.ajax({
			url: eippath + '/channel/updatePushInterDataSet',
			dataType: "json",	//返回数据类型
			type: 'post',
			data: {
				channelTargetId,
				autoSendTickets,
				sendRegResultMsg,
				channelRepeatReturnBarcode,
				receiveBuyerQuestionnaireInfo,
				currentChannelCoverOtherChannelBuyerInfo,
				useThirdBarcode,
			},
			success(data) {
				if (data.state !== 1)
					return $.messager.alert('提示', '数据加载失败！', 'error')
				channel_details_fresh()
				$.messager.alert('提示', '保存成功', 'info')
				$window.window('close')
			},
			error() {
				$.messager.alert('提示', '数据发送失败！', 'error')
			}
		}).done(() => $.messager.progress('close'))
	}

	// 赋值
	function dataPushAssignment(data){
		'projectId projectName channelId token secretKey targetType'.split(' ').forEach(item=>{
			const $currentField = $('#channel-details-' + item)
			$currentField.val(data[item] || '')
		})
		$('#channel-details-APIAddress').val(location.origin + '/eip-web-crm/api/getThridPlatformData')
	}
	function dataPushGetStartUsing(postData, callback){
		$.messager.progress()
		$.ajax({
			url: eippath + '/channel/getThridPlatFormDetail',
			dataType: "json",	//返回数据类型
			type: 'post',
			data: postData,
			success: function(data){
				if(data.state !== 1)
					return $.messager.alert('提示','数据加载失败！','error', ()=>$.messager.progress('close'))
				callback && callback(data.data)
				$.messager.progress('close')
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error')
			}
		})
	}
	function channel_details_export() {
		if (channel_details_current_tab_index === 1) return channel_invite_table_export()
		const orgnum = org_num_for_pass;
		const eid = exhibitCode_for_pass;
		const computerURI = variableReg + '/web-reg-server/pc/vistor-register.html'
		const mobileURI = variableReg + '/web-reg-server/mobile/vistor-register-m.html'
		const logoFn = channel_details_get_cached_logoFn()

		const $table = $('#channel-details-datagrid')
		const rows = $table.datagrid('getSelections')
		if (!rows.length) {
			const data = ($table.datagrid('getData')|| {}).rows || []
			if (!data.length) return $.messager.alert('提示', '没有可导出的数据', 'warning')
			return $.messager.confirm('提示', '是否导出全部数据？', r => r && main(data))
		}
		main(rows)


		async function exports(list) {
			const workbook = new ExcelJS.Workbook();
			const worksheet = workbook.addWorksheet('邀请渠道')
			let nextIndex = 0
			const head = getValuesFromRow(null)
			worksheet.addRow(head).height = 30
			const cnQRIndex = head.indexOf('中文版二维码')
			const enQRIndex = head.indexOf('英文版二维码')
			nextIndex = 1
			for (const index in list) {
				const item = list[index]
				const {mobileURL, logo, mobileURLEN, logoEN} = item
				const row = worksheet.addRow(getValuesFromRow(item, index))
				row.height = 150
				const imgId = workbook.addImage({
					base64: await getQRCodeDataURL(mobileURL, logo),
					extension: 'png',
				})
				worksheet.addImage(imgId, {
					tl: {col: cnQRIndex, row: nextIndex},
					br: {col: cnQRIndex + 1, row: nextIndex + 1},
					editAs: 'oneCell'
				})
				if (mobileURLEN && logoEN) {
					const imgId = workbook.addImage({
						base64: await getQRCodeDataURL(mobileURLEN, logoEN),
						extension: 'png',
					})
					worksheet.addImage(imgId, {
						tl: {col: enQRIndex, row: nextIndex},
						br: {col: enQRIndex + 1, row: nextIndex + 1},
						editAs: 'oneCell'
					})
				}
				nextIndex++
			}
			worksheet.columns = head.map((_, index) => (index > 1 ? {width: 30} : {}))
			// 将所有单元格文本设置为居中
			worksheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
				row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
					if (rowNumber === 1) {
						cell.font = { bold: true }
					}
					cell.alignment = { horizontal: 'center', vertical: 'middle' };
				});
			});
			workbook.xlsx.writeBuffer().then(buffer=>saveAs(new Blob([buffer], {type: "application/octet-stream"}), "自定义渠道.xlsx"))
		}

		function getRegEnglishByPid(pid) {
			const project = window.AllProjectByExhibitCode.find(item => item.projectId === pid)
			return project && project.regEnglish
		}

		function makeURLSearch(row, version) {
			const {channelTypeId, channelId, projectId, targetType} = row
			return `?EID=\${eid}&target=\${targetType}&orgnum=\${orgnum}&pid=\${projectId}&version=\${version}&cid=\${channelId}&ctid=\${channelTypeId}`;
		}

		function getQRCodeDataURL(content, logo) {
			const option = {
				src: logo,
				text: content,
				correctLevel: 0,
				width: 800,
				height: 800,
				imgWidth: 200,
				imgHeight: 200
			}
			const $el = $('<div/>').qrcode(option)
			const canvas = $el.find('canvas')[0]
			if (!logo) {
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				$el.remove()
				return dataURL
			}
			// append logo to canvas
			const image = new Image()
			image.src = logo
			let _resolve
			const promise = new Promise(resolve => _resolve = resolve)
			image.onload = () => {
				const ctx = canvas.getContext('2d')
				ctx.drawImage(image, (option.width - option.imgWidth) / 2, (option.height - option.imgHeight) / 2, option.imgWidth, option.imgHeight)
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				_resolve(dataURL)
				$el.remove()
			}
			image.onerror = err => {
				console.error(err)
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				_resolve(dataURL)
				$el.remove()
			}
			return promise
		}

		function getValuesFromRow(row, index) {
			// 返回表头
			if (!row) {
				return [
					'序号',
					'渠道ID',
					'项目名称',
					'身份',
					'渠道类型',
					'渠道名称',
					'中文版手机端登记地址',
					'中文版PC端登记地址',
					'中文版二维码',
					'英文版手机端登记地址',
					'英文版PC端登记地址',
					'英文版二维码',
				]
			}
			const {
				channelId,
				projectName,
				targetName,
				channelTypeName,
				channelName,
				computerURL,
				mobileURL,
				computerURLEN,
				mobileURLEN
			} = row
			return [
				index + 1,
				channelId,
				projectName,
				targetName,
				channelTypeName,
				channelName,
				mobileURL,
				computerURL,
				'',
				mobileURLEN,
				computerURLEN,
				'',
			]
		}

		function main(rows) {
			$.messager.progress()
			const list = rows.map(item => {
				const temp = {...item}
				const regEnglish = getRegEnglishByPid(temp.projectId)
				temp.computerURL = computerURI + makeURLSearch(temp, 1)
				temp.mobileURL = mobileURI + makeURLSearch(temp, 1)
				temp.logo = logoFn(temp.projectId, 1)
				if (regEnglish) {
					temp.computerURLEN = computerURI + makeURLSearch(temp, 2)
					temp.mobileURLEN = mobileURI + makeURLSearch(temp, 2)
					temp.logoEN = logoFn(temp.projectId, 2)
				}
				if (temp.channelTypeId === 4) temp.channelTypeName = '扫码即到场'
				return temp
			})
			exports(list).finally(() => $.messager.progress('close'))
		}
	}
	function channel_invite_table_export() {
		const orgnum = org_num_for_pass;
		const eid = exhibitCode_for_pass;
		const logoFn = channel_details_get_cached_logoFn()
		const mobileURI = variableReg + '/web-reg-server/mobile/vistor-register-m.html'
		const $table = $('#channel-details-inviteCodeTable')
		const rows = $table.datagrid('getSelections')
		if (!rows.length) {
			const data = ($table.datagrid('getData')|| {}).rows || []
			if (!data.length) return $.messager.alert('提示', '没有可导出的数据', 'warning')
			return $.messager.confirm('提示', '是否导出全部数据？', r => {
				if (!r) return
				$.messager.progress()
				const {url, queryParams} = $table.datagrid('options')
				$.ajax({
					url,
					dataType: "json",	//返回数据类型
					type: 'post',
					data: {...queryParams, rows: 1e4, page: 1},
					success(data) {
						if (data.state !== 1)
							return $.messager.alert('提示', '导出失败！', 'error', () => $.messager.progress('close'))
						exports(data.rows).finally(() => $.messager.progress('close'))
					},
					error() {
						$.messager.alert('提示', '导出失败！', 'error')
						$.messager.progress('close')
					}
				})
			})
		}
		$.messager.progress()
		exports(rows).finally(() => $.messager.progress('close'))
		async function exports(list) {
			const longProjRegSetList = getLongProjRegSetList(list)
			const workbook = new ExcelJS.Workbook();
			const worksheet = workbook.addWorksheet('邀请渠道')
			let nextIndex = 0
			const head = getValuesFromRow(null, -1, longProjRegSetList)
			worksheet.addRow(head).height = 30
			nextIndex = 1
			for (let index in list) {
				const item = list[index]
				const values = getValuesFromRow(item, index, longProjRegSetList)
				// 添加二维码，并返回新的行
				const rowValues = values.map(() => '')
				await Promise.all(values.map(async (val, index) => {
					if (!String(val).startsWith('$QR$')) {
						rowValues[index] = val
						return
					}
					const imgId = workbook.addImage({
						base64: await getQRCodeDataURLEx(val.replace('$QR$', ''), item),
						extension: 'png',
					})
					worksheet.addImage(imgId, {
						tl: {col: index, row: nextIndex},
						br: {col: index + 1, row: nextIndex + 1},
						editAs: 'oneCell'
					})
				}))
				const row = worksheet.addRow(rowValues)
				row.height = 150
				nextIndex++
			}

			// list.forEach((item, index) => {
			//
			// })
			worksheet.columns = head.map((_, index) => (index > 1 ? {width: 30} : {}))
			// 将所有单元格文本设置为居中
			worksheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
				row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
					if (rowNumber === 1) {
						cell.font = { bold: true }
					}
					cell.alignment = { horizontal: 'center', vertical: 'middle' };
				});
			});
			workbook.xlsx.writeBuffer().then(buffer=>saveAs(new Blob([buffer], {type: "application/octet-stream"}), "展商邀请渠道.xlsx"))
		}

		function getQRCodeDataURLEx(content, item) {
			const cu = new URL(content)
			const pid = cu.searchParams.get('pid') || item.contractProjectId || item.projectId
			const version = cu.searchParams.get('version') || ''
			const target = cu.searchParams.get('target') || ''
			const logo = logoFn(pid, version, target)
			const option = {
				text: content,
				correctLevel: 0,
				width: 800,
				height: 800,
				imgWidth: 200,
				imgHeight: 200
			}
			const $el = $('<div/>').qrcode(option)
			const canvas = $el.find('canvas')[0]
			if (!logo) {
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				$el.remove()
				return dataURL
			}
			// append logo to canvas
			const image = new Image()
			image.src = logo
			let _resolve
			const promise = new Promise(resolve => _resolve = resolve)
			image.onload = () => {
				const ctx = canvas.getContext('2d')
				ctx.drawImage(image, (option.width - option.imgWidth) / 2, (option.height - option.imgHeight) / 2, option.imgWidth, option.imgHeight)
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				_resolve(dataURL)
				$el.remove()
			}
			image.onerror = err => {
				console.error(err)
				const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
				_resolve(dataURL)
				$el.remove()
			}
			return promise
		}

		function makeURLSearch(conf) {
			const {channelTypeId, channelId, projectId, targetType, version=1} = conf
			return `?EID=\${eid}&target=\${targetType}&orgnum=\${orgnum}&pid=\${projectId}&version=\${version}&cid=\${channelId}&ctid=\${channelTypeId}`;
		}

		function getValuesFromRow(row, index, longProjRegSetList) {
			// 返回表头
			if (!row) {
				const extHead = longProjRegSetList.map(item => {
					const {projectName, targetTypeName, versionName} = item
					const text = projectName + targetTypeName + '(' + (versionName || '中文版') + ')'
					return [text + '展商渠道链接', text + '展商渠道二维码']
				})
				return [
					'序号',
					'项目名称',
					'展商名称',
					'会员号',
					'绑定手机',
					'绑定邮箱',
					'邀请码',
					'使用该邀请码登记观众数',
					'邀请码限制次数',
					'邀请函中文链接',
					'邀请函中文链接二维码',
					'邀请函英文链接',
					'邀请函英文链接二维码',
					...extHead.flat(),
					'邀请观众次数',
				]
			}
			const {
				projectName,
				companyName,
				userName,
				zzyUserMobile,
				zzyUserEmail,
				inviteCode,
				buyerNumber,
				useNumber,
				invitationList,
				inviteBuyerNum,
				clientId,
				projRegSetList,
			} = row

			const extValue = longProjRegSetList.map(item => {
				const {targetType, projectId, version} = item
				if (!(projRegSetList || []).find(item => item.targetType === targetType && item.projectId === projectId)) return ['', '']
				const target = new URL(mobileURI)
				target.search = makeURLSearch({channelTypeId: 2, channelId: clientId, projectId, targetType, version})
				return [target.href, '$QR$' + target.href]
			}).filter(Boolean)

			const buildInviteJumpUrls = () => {
				const ret = ['', '', '', '']
				if (!Array.isArray(invitationList) || !invitationList.length) return ret
				const zh = invitationList.find(item => item.version === 1)
				const uri = '/RegSever/RegPage/pages/invitation-letter/invitation-letter-2024.html'
				const target = new URL(uri, location.origin)
				if (zh) {
					target.searchParams.set('id', zh.invitationId)
					ret[0] = target.href
					ret[1] = '$QR$' + target.href
				}
				const en = invitationList.find(item => item.version === 2)
				if (en) {
					target.searchParams.set('id', en.invitationId)
					ret[2] = target.href
					ret[3] = '$QR$' + target.href
				}
				return ret
			}
			return [
				index + 1,
				projectName,
				companyName,
				userName,
				zzyUserMobile,
				zzyUserEmail,
				inviteCode,
				buyerNumber,
				useNumber,
				...buildInviteJumpUrls(),
				...extValue.flat(),
				inviteBuyerNum,
			]
		}

		function getLongProjRegSetList(list) {
			let longProjRegSetList = []
			const seen = new Set()
			list.forEach(item => {
				const projRegSetList = [(item.projRegSetList || []).map(item => {
					const {releaseReg, releaseRegEn} = item
					const ret = []
					if (releaseReg) ret.push({...item, version: 1, versionName: '中文版'})
					if (releaseRegEn) ret.push({...item, version: 2, versionName: '英文版'})
					return ret
				})].flat(2)
				projRegSetList.forEach(item => {
					const {projectId, targetType, version} = item
					const key = [projectId, targetType || 1, version || 1].join('-')
					if (seen.has(key)) return
					longProjRegSetList.push(item)
					seen.add(key)
				})
			})
			return longProjRegSetList
		}
	}
	function channel_details_AddTargetType(noClearCtrl) {
		$('#channel-details-register-address').window('open')
		if (!noClearCtrl) {
			channel_details_config.projectId = ''
			channel_details_config.targetType = ''
			$('#channel-details-register-address-projectId').combobox('clear')
			$('#channel-details-register-address-targetType').combobox('clear')
		}
	}
	function channel_details_CloseTargetType() {
		$('#channel-details-register-address').window('close')
	}
	function channel_details_AddNextStep() {
		const projectId = $('#channel-details-register-address-projectId').combobox('getValue')
		const targetType = $('#channel-details-register-address-targetType').combobox('getValue')
		if (!projectId) return $.messager.alert('提示', '请选择项目', 'warning')
		if (!targetType) return $.messager.alert('提示', '请选择适用身份', 'warning')
		channel_details_config.projectId = projectId
		channel_details_config.targetType = targetType
		channel_details_CloseTargetType()
		channel_details_SelectorSearch()
		$('#channel-details-selector').window('open')
	}
	function channel_details_LoadRoles(regProjectId) {
		$("#channel-details-register-address-targetType").combobox({
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {regProjectId},
			method: 'POST',
			loadFilter(data) {
				return data.state === 1 ? data.data: []
			},
		})
	}
	function channel_details_DeleteTargetType() {
		const request = (channelTargetIds) => {
			if (!channelTargetIds) return
			$.messager.progress()
			$.ajax({
				url: '/eip-web-sponsor/channel/delTargetType',
				type: "post",
				data: {channelTargetIds},
				datatype: 'json',
				success(data) {
					$.messager.progress('close')
					if (data.state !== 1) return $.messager.alert('提示', '删除失败！', 'error')
					$.messager.alert('提示', '删除成功', 'info')
					channel_details_fresh()
				},
				error(err) {
					$.messager.alert('数据加载失败！')
					$.messager.progress('close')
					console.warn(err)
				},
			})
		}
		const $table = $('#channel-details-datagrid')
		const rows = $table.datagrid('getSelections')
		if (!rows.length) return $.messager.alert('提示', '请选择要删除的数据', 'warning')
		const channelTargetIds = rows.map(({channelTargetId}) => channelTargetId).filter(Boolean).join(',')
		if (!channelTargetIds) return $.messager.alert('提示', '主办方渠道不允许删除', 'warning')
		$.messager.confirm('提示', `确定要删除吗？(不含主办方渠道)`, r => r && request(channelTargetIds))
	}
	function channel_details_SelectorSearch() {
		$('#channel-details-selector-datagrid').datagrid({
			url: '/eip-web-sponsor/channel/getPage',
			queryParams: {
				channelTypeName: $('#channel-details-selector-search-channelTypeName').val(),
				channelName: $('#channel-details-selector-search-channelName').val(),
				projectId: channel_details_config.projectId,
				notTargetTypeId: channel_details_config.targetType,
				exhibitCode: exhibitCode_for_pass
			},
			onLoadSuccess() {
				setTimeout(() => $(this).datagrid('resize'))
			}
		})
	}
	function channel_details_SelectorConfirm(){
		const request = (targetType, channelIds) => {
			if (!channelIds) return
			$.messager.progress()
			$.ajax({
				url: '/eip-web-sponsor/channel/addTargetType',
				type: "post",
				data: {targetType, channelIds},
				datatype: 'json',
				success(data) {
					$.messager.progress('close')
					if (data.state !== 1) return $.messager.alert('提示', '添加失败！', 'error')
					$.messager.alert('提示', '添加成功', 'info')
					$('#channel-details-selector').window('close')
					channel_details_fresh()
				},
				error(err) {
					$.messager.alert('数据加载失败！')
					$.messager.progress('close')
					console.warn(err)
				},
			})
		}
		const $table = $('#channel-details-selector-datagrid')
		const rows = $table.datagrid('getSelections')
		if (!rows.length) return $.messager.alert('提示', '请选择要添加的数据', 'warning')
		const channelIds = rows.map(({channelId}) => channelId).filter(Boolean).join(',')
		request(channel_details_config.targetType, channelIds)
	}
	function channel_details_SelectorBack(){
		channel_details_AddTargetType(true)
		$('#channel-details-selector').window('close')
	}
  function channel_details_set_getOne(channelId, callback) {
    $.ajax({
      url: '/eip-web-sponsor/channel/getMoreById',
      data: {channelId},
      type: 'post',
      success({data, state}) {
        if (state !== 1) return $.messager.alert('错误', '数据加载失败！', 'error')
        callback && callback(data)
      },
      error(err) {
        console.log(err)
        $.messager.alert('错误', '数据加载失败！', 'error')
      }
    })
  }
  function channel_details_set_add() {
		$('#channel-details-set-panel-targetList').combobox('clear')
		setTimeout(() => {
			$('#channel-details-set-panel-targetList').combobox('enable')
		})
		$('#channel-details-set-panel-channelTypeId').combobox('clear')
		channel_details_load_project('#channel-details-set-panel-projectId')
    $("#channel-details-set-panel-channelName").textbox("clear");
    const $project = $("#channel-details-set-panel-projectId")
    const projectId = $project.combobox("getValue") || project_for_pass;
    $project.combobox("setValue", projectId).combobox('enable');
    channel_details_load_roles(projectId, true)
    $('#channel-details-set-window').window('open');
  }
  function channel_details_set_edit() {
    const rows = $('#channel-details-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '未选中内容！', 'warning')
    if (rows.length !== 1) return $.messager.alert('提示', '每次只能编辑一条数据！', 'warning')
    const {channelId} = rows[0]
		if (!channelId) return $.messager.alert('提示', '主办方渠道不允许编辑！', 'warning')
		channel_details_set_getOne(channelId, data => {
			const {projectId, channelTypeId, channelName, targetList} = data
			if (targetList && targetList.length > 1) {
				return $.messager.confirm('提示', '该渠道已被多角色身份使用，是否立刻跳转至邀请渠道设置进行手动设置?！', r => {
					if (!r) return
					top.addTab('邀请渠道设置', eippath + '/backstage/project/channel_set', '', 0)
				})
			}
			channel_details_load_project('#channel-details-set-panel-projectId')
			channel_details_load_roles(projectId, false)
			const $formWindow = $('#channel-details-set-window')
			$("#channel-details-set-panel-projectId").combobox("setValue", projectId).combobox('disable')
			$("#channel-details-set-panel-targetList").combobox('setValue', targetList[0] || '1').combobox('disable')
			$("#channel-details-set-panel-channelTypeId").combobox("setValue", channelTypeId);
			$("#channel-details-set-panel-channelName").textbox("setValue", channelName)
			$formWindow.window('open').data({channelId});
		})
  }
	function channel_details_set_delete() {
    const rows = $('#channel-details-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '未选中内容！', 'warning')
    if (rows.length !== 1) return $.messager.alert('提示', '每次只能删除一条数据！', 'warning')
    const {channelId} = rows[0]
		if (!channelId) return $.messager.alert('提示', '主办方渠道不允许删除！', 'warning')
		channel_details_set_getOne(channelId, data => {
			const {targetList} = data
			if (targetList && targetList.length > 1) {
				return $.messager.confirm('提示', '该渠道已被多角色身份使用，是否立刻跳转至邀请渠道设置进行手动设置?！', r => {
					if (!r) return
					top.addTab('邀请渠道设置', eippath + '/backstage/project/channel_set', '', 0)
				})
			}
			$.messager.confirm('提示', '确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/channel/delete',
						dataType: "json",
						type: "post",
						data: {channelId},
						success(data) {
							if (data.state === 1) {
								$.messager.alert('提示', '删除成功！', 'info');
								channel_details_search(true)
							} else {
								$.messager.alert('提示', '删除失败！', 'error');
							}
						},
						error() {
							$.messager.alert('提示', '数据发送失败！', 'error');
						}
					});
				}
			});
		})
  }
  function channel_details_set_save() {
    const $formWindow = $('#channel-details-set-window')
    var projectId = $("#channel-details-set-panel-projectId").combobox("getValue");
    var channelTypeId = $("#channel-details-set-panel-channelTypeId").combobox("getValue");
    var channelName = $("#channel-details-set-panel-channelName").textbox("getValue");
    var targetList = $("#channel-details-set-panel-targetList").combobox("getValue");
    if (!projectId) return $.messager.alert('提示', '项目不能为空！', 'warning')
    if (!targetList) return $.messager.alert('提示', '适用身份不能为空！', 'warning')
    if (!channelTypeId) return $.messager.alert('提示', '渠道类型不能为空！', 'warning')
    if (!channelName) return $.messager.alert('提示', '渠道名称不能为空！', 'warning')
    if (+channelTypeId === 4) {
      return $.messager.confirm('提示', '是否确认使用扫码及到场渠道？如启用闸机/PDA请避免使用该渠道。', f => f && _request())
    }
    _request()

    function _request() {
      var postData = {};
      postData['channelId'] = $formWindow.data('channelId') || '';
      postData['projectId'] = projectId;
      postData['channelTypeId'] = channelTypeId;
      postData['channelName'] = channelName;
      postData['targetList'] = [targetList];
      $.messager.progress()
      $.ajax({
        url: '/eip-web-sponsor/channel/save',
        dataType: "json",	//返回数据类型
        type: "post",
        contentType: "application/json",
        data: JSON.stringify(postData),	//发送数据
        success(data) {
          $.messager.progress('close')
          if (data.state === 1) {
            $.messager.alert('提示', '保存成功！', 'info');
            $formWindow.window('close', true).removeData('channelId');
            channel_details_search(true)
          } else {
            $.messager.alert('提示', '保存失败！', 'error');
          }
        },
        error() {
          $.messager.alert('提示', '数据发送失败！', 'error');
          $.messager.progress('close')
        }
      });
    }
  }
  function channel_details_set_cancel() {
    $('#channel-details-set-window').window('close', true);
  }
	function channel_details_goto_order_list(inviteCodeClientId) {
		event.stopPropagation();
		const result = addTab('票证支付订单', '/eip-web-sponsor/backstage/spectator/certificate_order?icid=' + inviteCodeClientId)
		// 已经存在是可以调用的
		if (result === '__EXISTS__') certificate_order_setProps(inviteCodeClientId)
	}
</script>