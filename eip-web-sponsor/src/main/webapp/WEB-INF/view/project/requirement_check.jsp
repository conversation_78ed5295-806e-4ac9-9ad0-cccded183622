<!-- 采购需求审核 -->
  <%@ page contentType="text/html;charset=UTF-8" %>
  <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
  <%@ include file="../common/iframe_head.jsp" %>
  <c:set var="v" value="22120702"/>
  <title>采购需求审核</title>
  <link rel="stylesheet" href="${eippath}/css/view/requirement_check.css?v=${v}">
  <script src="${eippath}/js/view/requirement_check.js?v=${v}"></script>
  <script>var eippath = '${eippath}'</script>
  <link rel="stylesheet" type="text/css" href="${eippath}/css/cropping/cropper.min.css" />
  <link rel="stylesheet" type="text/css" href="${eippath}/css/cropping/ImgCropping.css" />
  <script src="${eippath}/js/cropping/cropper.min.js"></script>
</head>
<body>
  <div class="easyui-layout app" data-options="fit:true">
	<div data-options="region:'center',border:false" id="main">
		<div id="app-toolbar">
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-add"
				 onclick="app.add()"  plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-edit"
				 onclick="app.edit()" plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-remove"
				 onclick="app.delete()" plain="true">删除</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-select11"
				 onclick="app.auth()" plain="true">批量审核</a>
			</div>
			<div id="app-search" class="my-toolbar-search my-toolbar-search-p flex">
        <form id="app-search-form" class="flex" style="flex: 1;flex-wrap: wrap;">
          <div class="p"><label for="">用户名</label>
            <input id="app-search-userName" class="easyui-textbox" style="width:120px;height:25px">
          </div>
          <div class="p"><label for="">求购标题</label>
            <input id="app-search-requirementTitle" class="easyui-textbox" style="width:120px;height:25px">
          </div>
          <div class="p"><label for="">求购分类</label>
            <input id="app-search-tradeId" class="easyui-combobox" style="width:120px;height:25px">
          </div>
          <div class="p"><label for="">公开状况</label>
            <input id="app-search-contactSecretDegree" class="easyui-combobox" style="width:120px;height:25px"
             data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'MERCHANT_OPEN',text:'商户公开'},{id:'ALL_USERS_OPEN',text:'所有用户公开'},{id:'KEEP_SECRET',text:'保密'},]">
          </div>
          <div class="p"><label for="">审核状态</label>
            <input id="app-search-checkState" class="easyui-combobox" style="width:120px;height:25px"
              data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'1',text:'草稿'},{id:'2',text:'审核'},{id:'9',text:'审核不通过'},]">
          </div>
          <div class="p"><label for="">公司名称</label>
            <input id="app-search-companyName" class="easyui-textbox" style="width:120px;height:25px">
          </div>
          <div class="p"><label for="">联系人</label>
            <input id="app-search-linkMan" class="easyui-textbox" style="width:120px;height:25px">
          </div>
          <div class="p"><label>填写时间</label>
            <input id="app-search-startPublishTime" class="easyui-datebox" style="width:120px;height:25px">&nbsp;-&nbsp;
            <input id="app-search-endPublishTime" class="easyui-datebox" style="width:120px;height:25px"></div>
          <div class="p">
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="app.search()">查询</a>
          </div>
          <div class="p">
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="app.searchReset()">重置</a>
          </div>
        </form>
			</div>
		</div>
		<table id="app-datagrid" class="easyui-datagrid" toolbar="#app-toolbar"></table>
	</div>
</div>

<!-- 新增修改弹窗 -->
<style>
#app-set-window{
	display: none;
}
</style>
<div id="app-set-window" class="easyui-window"
   data-options="title: '求购信息',width: 840,closed:true,modal:true,minimizable: false,maximizable: false,">
	<div class="my-toolbar-button datagrid-toolbar">
		<a href="javascript:void(0);" class="easyui-linkbutton setOnly" iconcls="icon-save" onclick="app.setSave()"
		 plain="true">保存</a>
     <a href="javascript:void(0);" class="easyui-linkbutton setPass" iconcls="icon-save" onclick="app.setPass()"
		 plain="true">审核通过</a>
     <a href="javascript:void(0);" class="easyui-linkbutton setBan" iconcls="icon-save" onclick="app.setBan()"
		 plain="true">审核驳回</a>
		<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel" onclick="app.close('set-window')" plain="true">关闭</a>
	</div>
  <form id="app-set-form">
    <div id="app-set-panel-1" class="easyui-panel" title="采购需求基本信息" style="width:100%;height:auto;padding:10px;">
      <ul>
      <input name="requireId" id="app-set-panel-requireId" type="hidden" control="text" />
      <li class="app-set-editOnly">
        <label>用户名</label>
        <span id="app-set-panel-userName"></span>
			</li>
      <li class="app-set-editOnly">
        <label>填写时间</label>
        <span id="app-set-panel-publishTime"></span>
			</li>
      <li style="width: 780px;height: 35px;">
				<label><i>*</i>求购标题</label>
				<input name="title" class="easyui-textbox" data-options="required:true,validType:'length[0,255]',
                invalidMessage: '求购描述长度需小于255'" control="textbox" tag="0" style="width:540px;height:25px" id="app-set-panel-title" maxlength="255">
			</li>
			<li>
				<label><i>*</i>行业分类</label>
				<input name="tradeId" class="easyui-combotree"  data-options="required:true" control="combotree" tag="0" style="width:175px;height:25px"
          id="app-set-panel-tradeId"
					data-options="valueField:'tradeId',textField:'tradeName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
			</li>
			<li>
				<label>产品类别</label>
				<input name="productTypeBase" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
          id="app-set-panel-productTypeBase"
					data-options="valueField:'f_product_type_id',textField:'f_type_name',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
			</li>
      <li style="width:780px;height: 85px;">
        <div style="display: flex;">
          <label><i>*</i>求购描述</label>
          <input name="description" class="easyui-textbox" control="textbox" tag="0" style="width:540px;height:80px"
            maxlength="1000"
            id="app-set-panel-description"  data-options="required:true,multiline: true,validType:'length[0,1000]',
                invalidMessage: '求购描述长度需小于1000'">
        </div>
			</li>
      <li>
        <div style="display: flex;">
          <label><i>*</i>求购数量</label>
          <div>
            <input name="quantity" class="easyui-numberbox" control="textbox" tag="0" style="width:175px;height:25px"
            data-options="required:true,min:0" id="app-set-panel-quantity">
          </div>
        </div>
			</li>
      <li>
				<label>国别</label>
				<input name="country" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px" id="app-set-panel-country">
			</li>
      <li style="width:780px;height: 123px;">
        <div style="display: flex;">
          <label>说明文件</label>
          <input type="file" name="file" id="addImg"  onchange="selecttype(this)" class="hidden" accept="image/*" />
          <div class="app-set-panel-upload" onclick="app.upload(this,1)">
            <input type="hidden" id="app-set-panel-productPic1" control="text" name="productPic1" />
            <div class="after">上传图片/视频</div>
            <div class="preview hide" style="position: relative;">
              <div class="delete" onclick="app.deleteImg(this,1);">
                <span class="l-btn-icon icon-select10">&nbsp;</span>
              </div>
              <!-- <img src="http://deve.smtcrm.com:81/zentao/data/upload/1/202208/0415310109658iqm.png" alt=""> -->
              <!-- <video src="http://deve.smtcrm.com:81/zentao/data/upload/1/202208/0415310109658iqm.png"
               autoplay="autoplay" controls="controls"></video> -->
            </div>
          </div>
          <div class="app-set-panel-upload" onclick="app.upload(this,2)">
            <input type="hidden" id="app-set-panel-productPic2" control="text" name="productPic2" />
            <div class="after">上传图片/视频</div>
						<div class="preview hide" style="position: relative;">
              <div class="delete" onclick="app.deleteImg(this,2);">
                <span class="l-btn-icon icon-select10">&nbsp;</span>
              </div>
						</div>
          </div>
          <div class="app-set-panel-upload" onclick="app.upload(this,3)">
            <input type="hidden" id="app-set-panel-productPic3" control="text" name="productPic3" />
            <div class="after">上传图片/视频</div>
						<div class="preview hide" style="position: relative;">
              <div class="delete" onclick="app.deleteImg(this,3);">
                <span class="l-btn-icon icon-select10">&nbsp;</span>
              </div>
						</div>
          </div>
        </div>
			</li>
		</ul>
	</div>
  <div id="app-set-panel-2" class="easyui-panel" title="求购联系信息" style="width:100%;height:auto;padding:10px;">
		<ul>
      <li>
				<label><i>*</i>公司名称</label>
				<input name="companyName" data-options="required:true" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px" id="app-set-panel-companyName">
			</li>
      <li></li>
			<li>
				<label><i>*</i>联系人</label>
				<input name="linkman"  data-options="required:true" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px"
          id="app-set-panel-linkman" >
			</li>
      <li>
				<label><i>*</i>联系人电话</label>
				<input name="linkmanPhone"  data-options="required:true" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px"
          id="app-set-panel-linkmanPhone" >
			</li>
      <li>
				<label><i>*</i>联系人邮箱</label>
				<input name="linkmanEmail" data-options="required:true" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px"
          id="app-set-panel-linkmanEmail" >
			</li>
		</ul>
	</div>
  <div id="app-set-panel-3" class="easyui-panel" title="求购其他信息" style="width:100%;height:auto;padding:10px;">
		<ul>
      <li style="width: 780px;">
				<label><i>*</i>公开设置 </label>
				<input type="radio" tag="0" name="contactSecretDegree" control="radio" value="MERCHANT_OPEN"> 向供应商公开
        <input type="radio" tag="0" name="contactSecretDegree" value="ALL_USERS_OPEN"> 向所有用户公开
        <input type="radio" tag="0" name="contactSecretDegree" value="KEEP_SECRET"> 保密
			</li>
      <li>
				<label><i>*</i>有效期至</label>
				<input name="validUntil"  data-options="required:true" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px"
          id="app-set-panel-validUntil">
			</li>
		</ul>
	</div>
  </form>
</div>
<script>
function isVideo(val){
	// 匹配 视频
	let end=val.substring(val.lastIndexOf(".")+1);
	var videolist = ['mp4', 'm2v', 'mkv','avi','mov','rmvb','rm','flv','3gp'];
	return videolist.some(function (item) {
		return item == end.toLowerCase();
	});
}
function uploadImgShow(val, showType) {
	if(!val) return;
	type = showType || +type;
	if (![1,2,3].includes(type)) app.alert('不支持的上传类型: ' + type);
	var $tar = $('#app-set-panel-productPic'+type);
	$tar.val(val);
	$tar.parent().find('.preview').removeClass('hide')
		.find('img,video').remove()
	if(isVideo(val)) {
		$tar.parent().find('.preview').append('<video src="' + val + '" controls="controls" autoplay="autoplay" />')
	} else {
		$tar.parent().find('.preview').append('<img src="' + val+'" alt="" />')
	}
}
</script>


<style>
#app-auth-window{
	display: none;
}
</style>
<div id="app-auth-window" class="easyui-window" title="审核驳回" data-options="modal:true,closed:true"
     style="width:400px;height:auto;padding:2px;">
    <div id="app-auth-window-toolbar">
        <div class="my-toolbar-button">
            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save"
               onclick="app.setBanSure()" plain="true">确定</a>
            <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel"
               onclick="app.close('auth-window')" plain="true">取消</a>
        </div>
    </div>
    <div id="app-auth-panel-1" class="easyui-panel" title="" style="width:100%;height:100px;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
            <li style="width:350px;height:50px;float:left;margin-right:15px;margin-bottom:5px;">
                <input id="app-auth-memo" class="easyui-textbox" style="width:100%;height:100%"
                  data-options="require: true,labelWidth: 80,label:'<i style=\'color:red\'>*&nbsp;</i>驳回原因',multiline:true,validType:'length[0,100]'">
            </li>
        </ul>
    </div>
</div>

<!-- 裁剪 start -->
<div class="tailoring-container ImgShow" style="display: none;">
	<div class="black-cloth" onclick="closeTailor(this)"></div>
	<div class="tailoring-content">
		<div class="tailoring-content-one">
			<label title="上传图片" for="chooseImg" class="l-btn choose-btn">
				<input type="file" name="file" id="chooseImg" onchange="selectImg(this)" class="hidden" accept="image/*">
				选择图片
			</label>
			<div class="close-tailoring" onclick="closeTailor(this)">×</div>
		</div>
		<div class="tailoring-content-two">
			<div class="tailoring-box-parcel">
				<img id="tailoringImg">
			</div>
			<div class="preview-box-parcel">
				<p>图片预览：</p>
				<div class="square previewImg"></div>
				<div class="circular previewImg"></div>
			</div>
		</div>
		<div class="tailoring-content-three">
			<button class="l-btn cropper-reset-btn">复位</button>
			<button class="l-btn cropper-rotate-btn">旋转</button>
			<button class="l-btn cropper-scaleX-btn">换向</button>
			<button class="l-btn sureCut" id="sureCut">确定</button>
		</div>
	</div>
</div>
<script>
	/**
	 * 裁剪 确定
	 */
	$("#sureCut").on("click", function() {
		if ($("#tailoringImg").attr("src") == null) return false;
		var cas = $('#tailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
		var base64url = cas.toDataURL()
		var timestamp = new Date().getTime()
		var file = dataURLtoFile(base64url, timestamp + ".jpg");
		uploadImg(file, true);
		// 关闭裁剪框
		$(".ImgShow").hide();
		$(".tailoring-box-parcel").hide();
		$(".preview-box-parcel").hide();
	});
	var type;
	function uploadImg(file, isAfterCropper = false) {
		if(!isAfterCropper) checkFile(file);
		// const idx = layer.load(3, {
		// 	shade: [0.2],
		// 	// time: 60000,
		// });
		// let idxTimer = setTimeout(() => {
		// 	layer.close(idx);
		// 	layer.msg('上传超时',{icon: 2});
		// }, 60000);
		var formData = new FormData();
    var fileType = file.type || '';
    // var isVideo  = fileType.startsWith('video/')
		formData.append("file", file); //data
		$.ajax({
			url: eippath + '/upload/uploadFileOss',
			dataType: "json", //返回数据类型
			type: 'post',
			data: formData,
			//cache : false,
			processData: false,
			contentType: false,
			async: false,
    }).done(res => {
      // layer.close(idx);clearTimeout(idxTimer);
      if (res.state == 0) app.alert('上传失败');
			uploadImgShow(res.result.path);
    }).fail(err =>  {
      // layer.close(idx);clearTimeout(idxTimer);
      app.alert('上传失败');
    })
	}
	//关闭裁剪框
	function closeTailor() {
		$(".tailoring-container").toggle();
	}
	var gfile;
	//弹出框水平垂直居中
	(window.onresize = function() {
		var win_height = $(window).height();
		var win_width = $(window).width();
		if (win_width <= 768) {
			$(".tailoring-content").css({
				"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
				"left": 0
			});
		} else {
			$(".tailoring-content").css({
				"top": (win_height - $(".tailoring-content").outerHeight()) / 2,
				"left": (win_width - $(".tailoring-content").outerWidth()) / 2
			});
		}
	})();
  function checkFile(file) {
    app.checkFile(file)
  }
	// 图像上传
	function selectImg(file) {
		var max_size = 10240;
		var img_size = 1024;
		var fileData = file.files[0];
		try{
			file.value = '';
			checkFile(fileData)
		}catch(e){
			$(file).val('');
			throw e
		}
		var size = fileData.size;
		$(".tailoring-box-parcel").show();
		$(".preview-box-parcel").show();
		if (size > img_size * 1024) {
			let fileObj = fileData; // document.getElementById('chooseImg').files[0];
			//alert(fileObj)
			var reader = new FileReader();
			reader.readAsDataURL(fileObj)
			reader.onload = function(e) {
				let image = new Image() //新建一个img标签（还没嵌入DOM节点)
				image.src = e.target.result
				image.onload = function() {
					let canvas = document.createElement('canvas'),
						context = canvas.getContext('2d'),
						imageWidth = image.width / 5, //压缩后图片的大小
						imageHeight = image.height / 5,
						data = ''
					canvas.width = imageWidth
					canvas.height = imageHeight
					context.drawImage(image, 0, 0, imageWidth, imageHeight)
					data = canvas.toDataURL('image/jpeg')
					// console.log(data)
					//压缩完成
					// document.getElementById('img').src = data
					$('#tailoringImg').cropper('replace', data, false)
				}
			}
		} else {
			var reader = new FileReader();
			let fileObj = fileData; // document.getElementById('chooseImg').files[0];
			reader.readAsDataURL(fileObj)
			reader.onload = function(evt) {
				var replaceSrc = evt.target.result;
				//更换cropper的图片
				$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
				$('#replaceImg').cropper('replace', replaceSrc, false);
			}
		}
	}
	var companysizes = 4/3; // 100/64.7;
	//var logosize = 25/25;
	//cropper图片裁剪
	$('#tailoringImg').cropper({
		aspectRatio: companysizes, //默认比例
		preview: '.previewImg', //预览视图
		guides: false, //裁剪框的虚线(九宫格)
		autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
		dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
		movable: true, //是否允许移动剪裁框
		resizable: true, //是否允许改变裁剪框的大小
		zoomable: true, //是否允许缩放图片大小
		mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
		touchDragZoom: true, //是否允许通过触摸移动来缩放图片
		rotatable: true, //是否允许旋转图片
		crop: function(e) {
			// 输出结果数据裁剪图像。
		}
	});
	//旋转
	$(".cropper-rotate-btn").on("click", function() {
		$('#tailoringImg').cropper("rotate", 45);
	});
	//复位
	$(".cropper-reset-btn").on("click", function() {
		$('#tailoringImg').cropper("reset");
	});
	//换向
	var flagX = true;
	$(".cropper-scaleX-btn").on("click", function() {
		if (flagX) {
			$('#tailoringImg').cropper("scaleX", -1);
			flagX = false;
		} else {
			$('#tailoringImg').cropper("scaleX", 1);
			flagX = true;
		}
		flagX != flagX;
	});

	function dataURLtoFile(dataurl, filename) { //将base64转换为文件
		var arr = dataurl.split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8arr], filename, {
			type: mime
		});
	}
	// 产品图片
	function selecttype(e) {
		let file = e.files[0];
		try{
			e.value = '';
			checkFile(file)
		}catch(e){
			$(e).val('');
			throw e;
		}
		var file_id = file.type || '';
		let fileObj = file; //document.getElementById('addPic').files[0];
		// file_id = file_id.substring(file_id.indexOf(".")).toLocaleLowerCase();
		if(file_id.startsWith('image/')) {
		// if (file_id == ".bmp" || file_id == ".png" || file_id == ".gif" || file_id == ".jpg" || file_id == ".jpeg") {
			var size = file.size;
			if(file.type.endsWith('png')) {
				uploadImg(file, true);
				return;
			}
			var max_size = 10240;
			var img_size = 1024;
			$(".ImgShow").toggle();
			if (size > img_size * 1024) {
				var reader = new FileReader();
				reader.readAsDataURL(fileObj)
				reader.onload = function(e) {
					let image = new Image() //新建一个img标签（还没嵌入DOM节点)
					image.src = e.target.result
					image.onload = function() {
						let canvas = document.createElement('canvas'),
							context = canvas.getContext('2d'),
							imageWidth = image.width / 5, //压缩后图片的大小
							imageHeight = image.height / 5,
							data = ''
						canvas.width = imageWidth
						canvas.height = imageHeight
						context.drawImage(image, 0, 0, imageWidth, imageHeight)
						data = canvas.toDataURL('image/jpeg')
						//压缩完成
						// document.getElementById('img').src = data
						$('#tailoringImg').cropper('replace', data, false)
					}
				}

			} else {
				var reader = new FileReader();
				reader.onload = function(evt) {
					var replaceSrc = evt.target.result;
					// 更换cropper的图片
					$('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
					$('#replaceImg').cropper('replace', replaceSrc, false);
					// 图像上传
				}
				reader.readAsDataURL(fileObj)
			}
			$(".tailoring-box-parcel").show();
		} else if( file_id.startsWith('video/') ){
			uploadImg(file, true)
		} else {
			app.alert("只能上传图片或视频 !");
		}
	}
</script>

</body>
</html>