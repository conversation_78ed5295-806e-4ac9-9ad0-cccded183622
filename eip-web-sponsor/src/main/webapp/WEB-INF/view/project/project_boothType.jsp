<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<%@ page import="java.util.List" %>
<%
	boolean fu_boothtype_set_tool = false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_boothtype_set_tool"))fu_boothtype_set_tool = true;
	}
%>
<script src="../../js/view/booth_type.js?v=250416"></script>
<style>
	.hide {
		display: none !important;
	}
	.fix-easyui-warning .messager-question{
		background-position: -97px 0 !important;
	}
	.fix-easyui-button .messager-button{
			display: flex;
			justify-content: center;
			flex-direction: row-reverse;
	}
	.js-disable {
		pointer-events: none;
	}
</style>
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="project-boothType-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="project_boothType_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-edit" onclick="project_boothType_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_boothType_del_batch()" plain="true">删除</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-view" onclick="project_boothType_view()" plain="true">查看</a>
            	<%
            		if(fu_boothtype_set_tool)
            			out.println("<a href=\"javascript:void(0);\" class=\"easyui-linkbutton row-lenght\" iconCls=\"icon-menu14\" onclick=\"project_boothType_tools()\" plain=\"true\">标配展具</a>");
            	%>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-menu14" onclick="project_boothType_cert()" plain="true">进馆证设置</a>
							<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-menu14" onclick="project_boothType_invite()" plain="true">邀请函设置</a>
							<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-calc" onclick="project_boothType_reCalc()" plain="true">批量重算展位价格</a>
							<!-- <a href="javascript:project_boothType_rebuildFee();" class="easyui-linkbutton" iconCls="icon-view" plain="true">批量生产项目定价</a> -->
							<script>
								// 批量生产项目定价
								function project_boothType_rebuildFee () {
									// projectBoothType.alert('TODO !', 'info');
									const rows = $('#project-boothType-datagrid').datagrid('getSelections')
									const len  = rows.length;
									$.messager.confirm('提示', len<1 ? '确定全部生成吗？' : '确定生成吗?', function(r){
										if (r){
											projectBoothType.post('/boothType/batchGenerateBoothPrice',{
												typeCodes: rows.map(i => i.typeCode).join(','),
												exhibitCode: exhibitCode_for_pass,
												// operEntrance:'展位类型设置'
											},{
											},function(res){
												projectBoothType.checkReturn(res);
											  project_boothType_search();
												projectBoothType.initPriceLink();
												if(res.data.failNum > 0) {
													projectBoothType.alert('<div>'
														+ '操作成功 !<br > 成功: '+ res.data.successNum
														+ '<br > 失败: '+ res.data.failNum
														+'</div>','info');
												} else {
													projectBoothType.alert(len == 1 ? '操作成功!' : '操作成功, 成功: '+ res.data.successNum +' !', 'info');
												}
											},function(){
												projectBoothType.alert('操作失败 !')
											})
										}
									});
								}
							</script>
            </div>
            <div class="my-toolbar-search">
            	<label>类型名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
							<input id="project-boothType-keyword-typeName" class="my-text" style="width:200px"
								onchange="this.value = this.value.trim()"
							>&nbsp;
							<script>
							$(_=> {
								$('#project-boothType-keyword-typeName').bind('keypress', function(event) {
									if (event.keyCode == 13) project_boothType_search();
								});
							})
							</script>
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="project_boothType_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="project-boothType-datagrid" class="easyui-datagrid" toolbar="#project-boothType-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},">
        	<!-- <thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'typeCode',width:80">类型代号</th>
					<th data-options="field:'typeName',width:100">类型名称</th>
					<th data-options="field:'ifStandard',width:60" formatter="project_boothType_formatIfStandard">是否标摊</th>
					<th data-options="field:'specLength',width:70">标摊长度</th>
					<th data-options="field:'specWidth',width:70">标摊宽度</th>
					<th data-options="field:'boothSpec',width:100">标摊规格</th>
					<th data-options="field:'boothArea',width:80">标摊面积</th>
					<th data-options="field:'certNum',width:60">进馆证数量</th>
					<th data-options="field:'certPrice',width:60">进馆证单价</th>
					<th data-options="field:'certMemo',width:100">进馆证提示信息</th>
					<th data-options="field:'priceWay',width:80" formatter="project_boothType_formatPriceWay">定价方式</th>
					<th data-options="field:'price',width:60">单价</th>
					<th data-options="field:'openPrice',width:60">开口加价</th>
					<th data-options="field:'foreignPrice',width:60">外币报价</th>
					<th data-options="field:'foreignOpenPrice',width:60">外币开口加价</th>
					<th data-options="field:'currencyName',width:60">外币币种</th>
				</tr>
			</thead> -->
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="project-boothType-window" class="easyui-window" toolbar="#project-boothType-window-toolbar" data-options="closed:true,title:'展位类型内容',modal:true,shadow:false,width:715" style="height:auto;">
	<!-- begin of toolbar -->
	<div id="project-boothType-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" hint="mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="project_boothType_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_boothType_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="project_boothType_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothType_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
		<style>
			.project-boothType-panel-ul{
				list-style:none;margin:0;padding:0;
			}
			.project-boothType-panel-ul li{
				width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;
			}
			.project-boothType-panel-ul li:nth-child(even){
				width:335px;
			}
			.project-boothType-panel-ul li>div{
				width:95px;height:25px;display:inline-block;padding-right: 15px;
				text-align: right;
			}
		</style>
    <div id="project-boothType-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
      <ul class="project-boothType-panel-ul">
        <li>
					<div style="padding-right: 20px;"><label>类型代号</label></div>
					<input id="project-boothType-textbox-typeCode" hint="typeCode" class="easyui-textbox" control="textbox" tag="1" style="width:170px;height:25px">
				</li><li>
					<div style="padding-right: 20px;width: 100px;"><label>类型名称</label></div>
					<input id="project-boothType-textbox-typeName" hint="typeName" class="easyui-textbox" control="textbox" tag="0" style="width:210px;height:25px">
				</li><li>
					<div><label>是否标摊</label></div>
					<input id="project-boothType-textbox-ifStandard" hint="ifStandard" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
				</li><li>
					<div style="width: 100px;"><label>启用时段调价</label></div>
					<input id="project-boothType-textbox-ifAdjust" hint="enableStagePrice" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
				</li><li>
					<div><label>展位关联产品</label></div>
					<div style="position: relative;width: 170px;">
						<input id="project-boothType-textbox-boothProdName"
							style="width:170px;height:25px;text-indent: 10px;outline: none;margin-left: 4px;"
							autocomplete="off"
							placeholder="点击选择" class="textbox"
							onclick="selectProdPrice.init('#project-boothType-textbox-boothProdName','#project-boothType-textbox-boothProdCode')" />
						<a href="javascript:$('#project-boothType-textbox-boothProdName,#project-boothType-textbox-boothProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 10px;top: 1px"></a>
						<input type="hidden" id="project-boothType-textbox-boothProdCode" >
					</div>
				</li><li>
					<div style="padding-right: 15px;width: 100px;"><label>开口费关联产品</label></div>
					<div style="position: relative;width: 212px;padding-right: 0;">
						<input id="project-boothType-textbox-openInfoProdName"
							style="width:205px;height:25px;text-indent: 10px;outline: none;"
							autocomplete="off"
							placeholder="点击选择" class="textbox"
							onclick="selectProdPrice.init('#project-boothType-textbox-openInfoProdName','#project-boothType-textbox-openInfoProdCode')" />
						<a href="javascript:$('#project-boothType-textbox-openInfoProdName,#project-boothType-textbox-openInfoProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 2px;top: 1px"></a>
						<input type="hidden" id="project-boothType-textbox-openInfoProdCode" >
					</div>
				</li>
			</ul>
		</div>
		<style>
			.project-boothType-panel-ul2{
				list-style:none;margin:0;padding:0;width:315px;
			}
			.project-boothType-panel-ul2 li{
				width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;
			}
			.project-boothType-panel-ul2 li:nth-child(even){
				width:325px;
			}
			.project-boothType-panel-ul2 li>div{
				width:95px;height:25px;display:inline-block;padding-right: 15px;
				text-align: right;
			}
		</style>
    <div id="project-boothType-panel-2" class="easyui-panel" title="标摊信息" style="width:100%;height:auto;padding:10px;display: flex;">
      <ul class="project-boothType-panel-ul2">
				<li>
					<div><label>标摊长度</label></div>
					<input id="project-boothType-textbox-specLength" hint="specLength" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
				</li><li>
					<div><label>标摊宽度</label></div>
					<input id="project-boothType-textbox-specWidth" hint="specWidth" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
				</li><li>
					<div><label>标摊规格</label></div>
					<input id="project-boothType-textbox-boothSpec" hint="boothSpec" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
				</li><li>
					<div><label>标摊面积</label></div>
					<input id="project-boothType-textbox-boothArea" hint="boothArea" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
				</li><li style="display: none">
					<input id="project-boothType-textbox-boothDiagramPic" hint="boothDiagramPic" class="easyui-textbox" control="textbox" tag="0">
				</li>
      </ul>
			<div>
					<div style="width:105px;height:25px;display:inline-block;padding-right: 15px;text-align: right;"><label>展位示意图</label></div>
					<input id="boothDiagramPicUploadPic" class="easyui-filebox" data-options="buttonText:'选择',prompt:'选择一个图片...'" style="width:195px;height:25px" accept="image/gif,image/jpeg,image/png,image/jpg,image/bmp" >
					<div style="width:120px;height:120px;margin-left:124px">
						<img id="boothDiagramPicUploadPicImage" src=""  style="width:100px;height:100px;border: 1px solid #d2d2d2;" />
					</div>
			</div>
    </div>
    <!-- <div id="project-boothType-panel-3" class="easyui-panel" title="定价信息" style="width:100%;height:auto;padding:10px;"> -->
			<style>
				.project-boothType-panel-ul3 .hide1{
					display: none;
				}
				.project-boothType-panel-ul3 .hide{
					display: none;
				}
				.project-boothType-panel-ul3{
					list-style:none;margin:0;padding:0;display: grid;grid-template-columns: 330px 325px;grid-gap: 5px 5px;
				}
				.project-boothType-panel-ul3 li>div{
					width:95px;height:25px;display:inline-block;padding-right: 15px;
					text-align: right;
				}
			</style>
		<!-- <div id="project-boothType-panel-3" class="easyui-tabs" style="width:100%;" data-options="height:275"> -->
		<div id="project-boothType-panel-3" class="easyui-tabs" style="width:100%;">
			<div title="定价信息" style="padding:20px;display:none;" data-options="selected:true">
				<ul class="project-boothType-panel-ul3">
					<li>
						<div><label>展位定价方式</label></div>
						<input id="project-boothType-window-priceWay" hint="priceWay" class="easyui-combobox" control="combobox" tag="0" style="width:208px;height:25px" data-options="valueField:'id',limitToList:true,textField:'text',panelHeight:'auto',data:[{id:'0',text:'按个'},{id:'1',text:'按面积'}]">
					</li><li class="js-enableForeignCurrency hide1">
						<div><label>外币币种</label></div>
						<input hint="foreignCurrency"  id="project-boothType-window-foreignCurrency" class="easyui-combobox" control="combobox" tag="0" style="width:158px;height:25px"
							data-options="valueField:'id',textField:'text',limitToList:true,panelHeight:'auto'">
					</li><li>
						<div><label>展位单价</label></div>
						<input id="project-boothType-window-price" hint="price" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
						<span>人民币</span>
					</li><li class="js-enableForeignCurrency hide1" style="position: relative;">
						<div><label>外币报价</label></div>
						<input id="project-boothType-window-foreignPrice" hint="foreignPrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
						<span class="jsf-fCurrency" style="position: absolute;left: 276px;top: 2px;"></span>
					</li><li style="grid-column-start: 1;grid-column-end: 3;
						border-top: 2px dashed #eee;padding-top: 10px;margin-top: 5px;">
						<div><label>开口加价算法</label></div>
						<input id="project-boothType-window-openPriceWay" hint="openAlgorithmType" class="easyui-combobox" control="combobox"
						 tag="0" style="width: 208px;height:25px" data-options="valueField:'id',textField:'text',panelHeight:'auto',
						 limitToList: true,data:[
							{id:'1',text:'按开口数加价'},
							{id:'2',text:'按开口数及面积加价'},
							{id:'3',text:'按开口长边加价'},
							{id:'4',text:'按开口情况加价'},
						]">
					</li><li class="hide
						project-boothType-window-openPrice-2
						project-boothType-window-openPrice-1
						project-boothType-window-openPrice-0 ">
						<div><label>开口加价</label></div>
						<span class="project-boothType-window-openPrice-0 hide">开口数X</span>
						<span class="project-boothType-window-openPrice-1 hide">开口数X展位面积X</span>
						<span class="project-boothType-window-openPrice-2 hide">最大边长X</span>
						<input id="project-boothType-window-openPrice" hint="openPrice"
						 class="easyui-numberbox project-boothType-window-openPrice-0 project-boothType-window-openPrice-1 project-boothType-window-openPrice-2"
						 data-options="precision:2" control="numberbox" tag="0" style="width:210px;height:25px">
						<span class="jsf-after"></span>
					</li><li class="js-enableForeignCurrency hide1 hide
						project-boothType-window-openPrice-2
						project-boothType-window-openPrice-1
						project-boothType-window-openPrice-0">
						<div><label>外币开口加价</label></div>
						<span class="project-boothType-window-openPrice-0 hide">开口数X</span>
						<span class="project-boothType-window-openPrice-1 hide">开口数X展位面积X</span>
						<span class="project-boothType-window-openPrice-2 hide">最大边长X</span>
						<input id="project-boothType-window-foreignOpenPrice" hint="foreignOpenPrice"
						 class="easyui-numberbox project-boothType-window-openPrice-0 project-boothType-window-openPrice-1 project-boothType-window-openPrice-2"
						 data-options="precision:2" control="numberbox" tag="0" style="width:210px;height:25px">
						 <span class="jsf-after"></span>
					</li><li class="project-boothType-window-openPrice-3 hide">
						<div><label>双开口加价</label></div>
						<input id="project-boothType-window-twoOpenInfoPrice" hint="twoOpenInfoPrice"
						 class="easyui-numberbox"
						 data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
						 <span>人民币</span>
					</li><li class="project-boothType-window-openPrice-3 hide js-enableForeignCurrency hide1">
						<div><label>外币加价</label></div>
						<input id="project-boothType-window-twoOpenInfoForeignPrice"  hint="twoOpenInfoForeignPrice"
						 class="easyui-numberbox"
						 data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
						 <span class="jsf-fCurrency"></span>
					</li><li class="project-boothType-window-openPrice-3 hide">
						<div><label>三开口加价</label></div>
						<input id="project-boothType-window-threeOpenInfoPrice"  hint="threeOpenInfoPrice"
							class="easyui-numberbox"
							data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
							<span>人民币</span>
					</li><li class="project-boothType-window-openPrice-3 hide js-enableForeignCurrency hide1">
						<div><label>外币加价</label></div>
						<input id="project-boothType-window-threeOpenInfoForeignPrice"  hint="threeOpenInfoForeignPrice"
							class="easyui-numberbox"
							data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
							<span class="jsf-fCurrency"></span>
					</li><li class="project-boothType-window-openPrice-3 hide">
						<div><label>四开口加价</label></div>
						<input id="project-boothType-window-fourOpenInfoPrice"  hint="fourOpenInfoPrice"
							class="easyui-numberbox"
							data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
							<span>人民币</span>
					</li><li class="project-boothType-window-openPrice-3 hide js-enableForeignCurrency hide1">
						<div><label>外币加价</label></div>
						<input id="project-boothType-window-fourOpenInfoForeignPrice"  hint="fourOpenInfoForeignPrice"
							class="easyui-numberbox"
							data-options="precision:2" control="numberbox" tag="0" style="width:158px;height:25px">
							<span class="jsf-fCurrency"></span>
					<!-- </li><li style="grid-column-start: 1;grid-column-end: 3;position: relative;" class="hide">
						<div><label>关联销售定价</label></div>
						<input id="project-boothType-window-projProductId" hint="projProductId" class="easyui-combobox" control="combobox"
						  tag="0" style="width:515px;height:25px"
							data-options="
								cls: 'project-boothType-window-projProductId',
							"  >
						<a href="#" title="关联后，新增/编辑销售合同时可获取到设置的展位关联的销售产品定价。"
							 style="color: white;border-radius: 50%;background-color: #aaa;width: 16px;height: 16px;font-size: 12px;display: inline-block;position: absolute;top:4px;right: -20px;text-align: center;"
							 data-options="position: 'top'" class="easyui-tooltip">!</a>
						<script>
							$(_=>{
								var $sc = $('#project-boothType-window-projProductId');
								$sc.combobox({
									icons: [{
										iconCls:'icon-remove',
										handler: function(e){
											$sc.combobox('clear');
											// $('.project-boothType-window-projProductId .icon-remove').parent().hide();
										}
									}],
								});
							})
						</script> -->
					</li>
				</ul>
			</div>
			<div title="优惠信息" style="overflow:auto;display:none;padding-bottom:20px">
				<div id="project-boothType-dis-toolbar" style="background-color: inherit;">
					<div class="my-toolbar-button">
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-add" onclick="projectBoothType.disOpen(0)" plain="true">增加</a>
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-edit" onclick="projectBoothType.disOpen(1)" plain="true">修改</a>
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-remove" onclick="projectBoothType.disDel()" plain="true">删除</a>
					</div>
				</div>
				<table id="project-boothType-dis-datagrid" class="easyui-datagrid"
				style="padding: 20px;"
 				  toolbar="#project-boothType-dis-toolbar"
        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true
						,selectOnCheck: true
						,onLoadSuccess: function() {
							// console.log(this);
						}
						,onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 					}"></table>
			</div>
			<div title="按时段报价" style="overflow:auto;display:none;padding-bottom:20px">
				<div id="project-boothType-adjust-toolbar" style="background-color: inherit;">
					<div class="my-toolbar-button">
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-add" onclick="projectBoothType.adjustOpen(0)" plain="true">增加</a>
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-edit" onclick="projectBoothType.adjustOpen(1)" plain="true">修改</a>
						<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-remove" onclick="projectBoothType.adjustDel()" plain="true">删除</a>
					</div>
				</div>
				<table id="project-boothType-adjust-datagrid" class="easyui-datagrid"
				style="padding: 20px;"
 				  toolbar="#project-boothType-adjust-toolbar"
        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true
						,selectOnCheck: true
						,onLoadSuccess: function() {
							// console.log(this);
						}
						,onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 					}"></table>
			</div>
    </div>
</div>
<!-- end of easyui-window -->

<!-- begin of easyui-window -->
<div id="project-boothType-tools-window" class="easyui-window" toolbar="#project-boothType-tools-window-toolbar" data-options="closed:true,title:'标配展具',modal:true" style="width:800px;height:500px;overflow-x: auto">
	<!-- begin of toolbar -->
	<div id="project-boothType-tools-toolbar">
        <div class="my-toolbar-button">
        	<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_tools_select()" plain="true">选取</a>
        	<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_boothType_tools_remove()" plain="true">删除</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="project_boothType_tools_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothType_tools_cancel()" plain="true">取消</a>
        </div>
     <!--    <div class="my-toolbar-search">
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="project_boothType_tools_select()">添加</a>
            &nbsp;
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="project_boothType_tools_remove()">删除</a>
        </div> -->
    </div>
	<!-- end of toolbar -->
    <table id="project-boothType-tools-datagrid" class="easyui-datagrid" toolbar=""
      	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
      	onClickRow: project_boothType_tools_onClickRow">
	      <thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'toolCode',width:30">展具代号</th>
				<th data-options="field:'toolName',width:40">展具名称</th>
				<th data-options="field:'toolType',width:40">展具型号规格</th>
				<!-- <th data-options="field:'preorderPrice',width:30">预订租赁价</th>
				<th data-options="field:'onsitePrice',width:30">现场租赁价</th> -->
				<th data-options="field:'toolPic',width:70" formatter="project_boothType_tools_formatToolPic">展具图片</th>
				<th data-options="field:'memo',width:50">展具备注</th>
				<th data-options="field:'defaultCount',width:50,editor:'numberbox'">标配数量</th>
				<th data-options="field:'isMoreSpec',editor:{
							type: 'combobox',
							options: {
								valueField: 'id',
								textField: 'text',
								editable: false,
								panelHeight:'auto',
								panelMaxHeight:'200px',
								data:[{'id':'1','text':'是'},{'id':'0','text':'否'}]
							}
						}" formatter="project_boothType_formatIsMoreSpec">是否多规格</th>
				<!-- <th data-options="field:'multiType',width:50,
					editor:{type:'checkbox',options:{on:'是',off:'否'}}">是否多选</th> -->
			</tr>
		</thead>
	</table>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="project-boothType-tools-select-window" class="easyui-window" toolbar="#project-boothType-tools-select-window-toolbar" data-options="closed:true,title:'展具选取',modal:true" style="width:800px;height:auto;">

	<!-- begin of toolbar -->
	<div id="project-boothType-tools-select-toolbar">
        <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_tools_select_save()" plain="true">确定</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothType_tools_select_cancel()" plain="true">取消</a>
        </div>
        <div class="my-toolbar-search">
        	<label>展具名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-boothType-tools-select-keyword-toolName" class="my-text" style="width:200px">&nbsp;
            <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="project_boothType_tools_select_search()">查询</a>
        </div>
    </div>
	<!-- end of toolbar -->
	<div  class="easyui-panel" title="" style="width:100%;height:400px;padding:10px;">
	<table id="project-boothType-tools-select-datagrid" class="easyui-datagrid"
      	data-options="rownumbers:true,singleSelect:false,pageSize:10,pagination:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
      		onClickRow: function (rowIndex, rowData) {
      			//var l = $(this).datagrid('getRows').length;
      			//for(var i = 0; i < l; i++){
             			//if(i != rowIndex)$(this).datagrid('unselectRow', i);
             	//	}
			},">
	      <thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'toolCode',width:30">展具代号</th>
				<th data-options="field:'toolName',width:40">展具名称</th>
				<th data-options="field:'toolType',width:40">展具型号规格</th>
				<!-- <th data-options="field:'preorderPrice',width:30">预订租赁价</th>
				<th data-options="field:'onsitePrice',width:30">现场租赁价</th> -->
				<th data-options="field:'toolPic',width:70" formatter="project_boothType_tools_formatToolPic">展具图片</th>
				<th data-options="field:'memo',width:50">展具备注</th>
			</tr>
		</thead>
	</table>
	</div>
</div>
<!-- end of easyui-window -->

<script>
	function project_boothType_cert_init(ifStandard = false) {
		if(ifStandard) {
			$('#project_boothType_cert_num').textbox({width: 120})
			$('#project_boothType_cert_certSquareMeter').parent().parent().hide();
		} else {
			$('#project_boothType_cert_num').textbox({width: 40})
			$('#project_boothType_cert_certSquareMeter').parent().parent().show();
		}
	}
	</script>
<div id="project-boothType-cert-window" class="easyui-window" toolbar="#project-boothType-cert-toolbar" data-options="closed:true,title:'进馆证信息设置',modal:true" style="width:550px;height:auto;">
	<!-- begin of toolbar -->
	<div id="project-boothType-cert-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_cert_save()" plain="true">保存</a>
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothType_cert_cancel()" plain="true">取消</a>
		</div>

	</div>
	<!-- end of toolbar -->
	<div id="project-boothType-cert-panel" class="easyui-panel" title="" style="width:100%;height:auto;padding:15px;">
		<ul style="list-style:none;margin:0;padding:0;height:50px;">
			<li style="width:250px;height:25px;float:left;margin-bottom:5px;
				display: grid;grid-template-columns: 75px 1fr;">
				<div style="height:25px;display:inline-block;"><label>标配数量</label></div>
				<div class="overflow: hidden;">
					<div style="width: 400px;display: flex;">
						<div class="">
							<input id="project_boothType_cert_num" class="easyui-numberbox"  control="numberbox" tag="0" style="height:25px;width: 40px;">
						</div>
						<div style="display: grid;grid-template-columns: 30px 1fr 30px;">
							<span style="font-size: 14px;text-align: center;">人/</span>
							<div>
								<input id="project_boothType_cert_certSquareMeter" class="easyui-numberbox"  control="numberbox" tag="0" style="height:25px;width: 40px;">
							</div>
							<span style="font-size: 14px;text-align: center;">平米</span>
						</div>
					</div>
				</div>
			</li>
			<li style="width:250px;height:25px;float:left;margin-bottom:5px;
				display: grid;grid-template-columns: 69px 1fr 1fr;">
				<div style="height:25px;display:inline-block;"><label>单价<!-- <span id="project_boothType_cert_currency"></span> --></label></div>
				<div>
					<input id="project_boothType_cert_price" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:100px;height:25px">
				</div>
				<div style="padding-left: 5px">
					<input id="project_boothType_cert_certCurrency" class="easyui-combobox" control="combobox" tag="0" style="width:75px;height:25px" data-options="valueField:'id',textField:'text',panelHeight:'auto'">
				</div>
			</li>
	     	<li style="width:500px;height:50px;float:left;margin-right:15px;margin-bottom:15px;">
	     		<div style="width:70px;height:25px;display:inline-block;"><label>提示信息</label></div>
	     		<input  id="project_boothType_cert_memo" class="easyui-textbox" control="textbox" tag="0"
	     		data-options="multiline:true,validType:'length[0,100]'" style="width: 425px;height:60px">
	     	</li>
				<li style="width:500px;float:left;margin-bottom:5px;
				  display: grid;grid-template-columns: 73px 1fr;">
					<div style="height:25px;display:inline-block;"><label>关联产品</label></div>
					<div style="position: relative;padding-right: 0;">
						<input id="project_boothType_cert_certProdName"
							style="width: 424px;height:25px;text-indent: 10px;outline: none;"
							autocomplete="off"
							placeholder="点击选择" class="textbox"
							onclick="selectProdPrice.init('#project_boothType_cert_certProdName','#project_boothType_cert_certProdCode')" />
						<a href="javascript:$('#project_boothType_cert_certProdName,#project_boothType_cert_certProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 2px;top: 1px"></a>
						<input type="hidden" id="project_boothType_cert_certProdCode" >
					</div>
				</li>
			<!-- <li style="width:450px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:100px;height:25px;display:inline-block;"><label>关联销售定价</label>&nbsp;&nbsp;</div>
				<input id="project_boothType_cert_projProductId" hint="projProductId" class="easyui-combobox" control="combobox"
					tag="0" style="width: 330px;height:25px"
					data-options="
						cls: 'project_boothType_cert_projProductId',
						panelHeight: 'auto',panelMaxHeight:300,
					"  >
				<script>
					$(_=>{
						var $sc = $('#project_boothType_cert_projProductId');
						$sc.combobox({
							icons: [{
								iconCls:'icon-remove',
								handler: function(e){
									$sc.combobox('clear');
									// $('.project_boothType_cert_projProductId .icon-remove').parent().hide();
								}
							}],
						});
					})
				</script>
			</li> -->
		</ul>
	</div>

</div>

<script>
	function project_boothType_invite_init(ifStandard = false) {
		if(ifStandard) {
			$('#project_boothType_invite_num').textbox({width: 120})
			$('#project_boothType_invite_inviteSquareMeter').parent().parent().hide();
		} else {
			$('#project_boothType_invite_num').textbox({width: 40})
			$('#project_boothType_invite_inviteSquareMeter').parent().parent().show();
		}
	}
</script>
<div id="project-boothType-invite-window" class="easyui-window" toolbar="#project-boothType-invite-toolbar" data-options="closed:true,title:'邀请函信息设置',modal:true" style="width:550px;height:auto;">
	<!-- begin of toolbar -->
	<div id="project-boothType-invite-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_boothType_invite_save()" plain="true">保存</a>
			<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_boothType_invite_cancel()" plain="true">取消</a>
		</div>

	</div>
	<!-- end of toolbar -->
	<div id="project-boothType-invite-panel" class="easyui-panel" title="" style="width:100%;height:auto;padding:15px;">
		<ul style="list-style:none;margin:0;padding:0;height:50px;">
			<li style="width:250px;height:25px;float:left;margin-bottom:5px;
				display: grid;grid-template-columns: 75px 1fr;">
				<div style="height:25px;display:inline-block;"><label>标配数量</label></div>
				<div class="overflow: hidden;">
					<div style="width: 400px;display: flex;">
						<div class="">
							<input id="project_boothType_invite_num" class="easyui-numberbox"  control="numberbox" tag="0" style="height:25px;width: 40px;">
						</div>
						<div style="display: grid;grid-template-columns: 30px 1fr 30px;">
							<span style="font-size: 14px;text-align: center;">人/</span>
							<div>
								<input id="project_boothType_invite_inviteSquareMeter" class="easyui-numberbox"  control="numberbox" tag="0" style="height:25px;width: 40px;">
							</div>
							<span style="font-size: 14px;text-align: center;">平米</span>
						</div>
					</div>
				</div>
			</li>
			<li style="width:250px;height:25px;float:left;margin-bottom:5px;
				display: grid;grid-template-columns: 68px 1fr 1fr;">
				<div style="height:25px;display:inline-block;"><label>单价<!--<span id="project_boothType_invite_currency"></span>--></label></div>
				<div>
					<input id="project_boothType_invite_price" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:100px;height:25px">
				</div>
				<div style="padding-left: 5px">
					<input id="project_boothType_invite_inviteCurrency" class="easyui-combobox" control="combobox" tag="0" style="width:75px;height:25px" data-options="valueField:'id',textField:'text',panelHeight:'auto'">
				</div>
			</li>
			<li style="width:500px;height:auto;float:left;margin-bottom:5px;">
				<div style="width:70px;height:25px;display:inline-block;"><label>提示信息</label></div>
				<input  id="project_boothType_invite_memo" class="easyui-textbox" control="textbox" tag="0"
						data-options="multiline:true,validType:'length[0,100]'" style="width: 425px;height:60px">
			</li>
			<li style="width:500px;float:left;margin-bottom:5px;
				  display: grid;grid-template-columns: 73px 1fr;">
				<div style="height:25px;display:inline-block;"><label>关联产品</label></div>
				<div style="position: relative;padding-right: 0;">
					<input id="project_boothType_invite_inviteProdName"
						style="width: 424px;height:25px;text-indent: 10px;outline: none;"
						autocomplete="off"
						placeholder="点击选择" class="textbox"
						onclick="selectProdPrice.init('#project_boothType_invite_inviteProdName','#project_boothType_invite_inviteProdCode')" />
					<a href="javascript:$('#project_boothType_invite_inviteProdName,#project_boothType_invite_inviteProdCode').val('');" class="textbox-icon icon-remove" style="width: 26px; height: 23px;position: absolute;right: 2px;top: 1px"></a>
					<input type="hidden" id="project_boothType_invite_inviteProdCode" >
				</div>
			</li>
			<!-- <li style="width:450px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:100px;height:25px;display:inline-block;"><label>关联销售定价</label>&nbsp;&nbsp;</div>
				<input id="project_boothType_invite_projProductId" hint="projProductId" class="easyui-combobox" control="combobox"
					tag="0" style="width: 330px;height:25px"
					data-options="
						cls: 'project_boothType_invite_projProductId',
						panelHeight: 'auto',panelMaxHeight:300,
					"  >
				<script>
					$(_=>{
						var $sc = $('#project_boothType_invite_projProductId');
						$sc.combobox({
							icons: [{
								iconCls:'icon-remove',
								handler: function(e){
									$sc.combobox('clear');
									// $('.project_boothType_invite_projProductId .icon-remove').parent().hide();
								}
							}],
						});
					})
				</script>
			</li> -->
		</ul>
	</div>
</div>

<%@ include file="../common/project_window_boothType.jsp" %>
<script type="text/javascript">
	var project_boothType_edit_mode;
	var project_boothType_button_edit_mode;
	var project_boothType_alocate_id = -1;
	var project_boothType_window_close_set = false;
	var project_boothType_save_id;
	var attachUrl =  window.origin + "/image/";
	//其他附件
	$("#boothDiagramPicUploadPic").filebox({
		onChange: function(n, o){
			var file = $("#boothDiagramPicUploadPic").filebox("files")[0];
			if(file) uploadFile(file)
		}
	});
	function uploadFile(file){
		if(file == null){
			$.messager.alert('提示','请选择附件！','warning');
			return;
		}
		var formData = new FormData();
		formData.append("file", file);
		$.ajax({
			url: "${eippath}/upload/uploadFile",
			dataType: "json",
			type: "post",
			data: formData,
			processData: false,
			contentType: false,
			success: function(data){
				if (data.state==1){
					$("#boothDiagramPicUploadPicImage").attr("src",attachUrl + data.result.path)
					$("#project-boothType-textbox-boothDiagramPic").textbox("setValue",data.result.path)
				} else $.messager.alert('提示','上传失败！','error');
			},
		});
	}
	/**
	 * 是否多规格
	 */
	function project_boothType_formatIsMoreSpec(val, row){
		return val==1?"是":"否";
	}

	$(function(){
		setTimeout(function(){
			projectBoothType.init()
			project_boothType_fresh();
			project_boothType_change();
			$('#project-boothType-tools-datagrid').datagrid({
				url: eippath + '/data/clear_datagrid.json'
			});
			$('#project-boothType-tools-select-datagrid').datagrid({
				url: eippath + '/data/clear_datagrid.json'
			});
			$('#project_boothType_cert_memo').textbox('textbox').attr('maxlength',100)
		}, 100);
	});
	var project_boothType_timer = setInterval("getLoc()",200);
	var flag = false;

	function getLoc() {
		var $row = $('#project-boothType-datagrid');
		if($row.length){
			var row = $row.datagrid('getSelections');
			if (row != null && row != "" && row != undefined) {
				if (row.length > 1) {
					$(".row-lenght").css("opacity", "0.5")
					flag = true;
				} else {
					$(".row-lenght").css("opacity", "1")
					flag = false;
				}
			}
		}else{
			clearInterval(project_boothType_timer);
		}
	}
	function project_boothType_change(){
		$("#project-boothType-textbox-specLength").textbox({
			onChange: function(n, o){
				if(n != '' && n > 0.00){
					var s = $("#project-boothType-textbox-boothSpec").textbox("getValue");
					$("#project-boothType-textbox-boothSpec").textbox("setValue", parseFloat(n).toFixed(2) + '*' + s.substring(s.indexOf('*') + 1, s.length));
					var t = $("#project-boothType-textbox-specWidth").textbox("getValue");
					if(t != ''){
						$("#project-boothType-textbox-boothArea").textbox("setValue", (n * t).toFixed(2));
					}
				}
			}
		});
		$("#project-boothType-textbox-specWidth").textbox({
			onChange: function(n, o){
				if(n != '' && n > 0.00){
					var s = $("#project-boothType-textbox-boothSpec").textbox("getValue");
					$("#project-boothType-textbox-boothSpec").textbox("setValue", s.substring(0, s.indexOf('*')) + '*' + parseFloat(n).toFixed(2));
					var t = $("#project-boothType-textbox-specLength").textbox("getValue");
					if(t != ''){
						$("#project-boothType-textbox-boothArea").textbox("setValue", (n * t).toFixed(2));
					}
				}
			}
		});
		$("#project-boothType-textbox-ifStandard").change(function(){
			if(document.getElementById('project-boothType-textbox-ifStandard').checked){
				$("#project-boothType-panel-2").show();
			}else{
				$("#project-boothType-panel-2").hide();
			}
			// 优惠信息重新显示
			projectBoothType.disTableReload()
		});
		$("#project-boothType-textbox-ifAdjust").change(function(){
			// 时段调价重新显示
			projectBoothType.adjustTableReload()
			project_boothType_toggleLastTab()
		});
		// 定价类型 change
		$('#project-boothType-window-priceWay').combobox({
			onSelect: function(n){
				console.log(n);
			},
			onChange: function(n,o){
				if(0 === +n && 3 === +$('#project-boothType-window-openPriceWay').combobox('getValue')) {
					projectBoothType.alert('定价类型不为 按面积, 无法使用 按开口长边加价 开口加价算法')
					$('#project-boothType-window-priceWay').combobox('setValue',1)
				}
				projectBoothType.priceWay = n;
				// console.log('n' , n ,o);
				var ifTip = false;
				if(projectBoothType.disData.size || projectBoothType.adjustData.size){
					ifTip = true
				}
				if(projectBoothType.priceWayFlag && ifTip){ // 排除初始
					$.messager.alert('提示', '存在优惠信息/时段报价，更换定价类型将清空优惠类型和时段报价设置!','warning');
					if(projectBoothType.disData.size){
						projectBoothType.disData.clear();
						projectBoothType.disTableReload();
					}
					if(projectBoothType.adjustData.size){
						projectBoothType.adjustData.clear();
						projectBoothType.adjustTableReload();
					}
				}
			}
		})
	}
	function project_boothType_fresh(){
		$('#project-boothType-datagrid').datagrid({
			url: eippath + '/boothType/getList?key=0',
			columns:[
				[
					{field:'ck',checkbox:true,title:''},
					{field:'typeCode',width:80,title:'类型代号'},
					{field:'typeName',width:100,title:'类型名称'},
					{field:'ifStandard',width:60,formatter: project_boothType_formatIfStandard,title:'是否标摊'},
					{field:'specLength',width:70,title:"标摊长度"},
					{field:'specWidth',width:70,title:"标摊宽度"},
					{field:'boothSpec',width:100,title:"标摊规格"},
					{field:'boothArea',width:80,title:"标摊面积"},
					{field:'certNum',width:60,formatter: project_boothType_formatCertNum,title:"进馆证数量"},
					{field:'certPrice',width:60,title:"进馆证单价"},
					{field:'certMemo',width:100,title:"进馆证提示信息"},
					{field:'priceWay',width:80,formatter: project_boothType_formatPriceWay, title:"定价方式"},
					{field:'price',width:60,title:"单价"},
					...(projectBoothType.enableForeignCurrency ? [
					{field:'openPrice',width:60,title:"开口加价"},
					{field:'foreignPrice',width:60,title:"外币报价"},
					{field:'foreignOpenPrice',width:60,title:"外币开口加价"},
					{field:'currencyName',width:60,title:"外币币种"},
					] : [])
				]
			],
			queryParams: {
				exhibitCode: exhibitCode_for_pass
      },
			onLoadSuccess: function(data){
				if(project_boothType_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].typeCode == project_boothType_alocate_id){
							$(this).datagrid('selectRow', i);
							project_boothType_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
		// 查询币种信息
		projectBoothType.currencys.clear();
		projectBoothType.post('/fnCurrency/getAll',{orgNum:org_num_for_pass},{ },function(rsp){
			// console.log('rsp ',rsp);
			if(rsp && rsp.length){
				rsp.forEach(it => {
					projectBoothType.currencys.set(it.id,it.name)
				})
			}
			$('#project_boothType_cert_certCurrency').combobox('loadData',rsp)
			$('#project_boothType_invite_inviteCurrency').combobox('loadData',rsp)
		},function(e){
			console.log('error',e);
			projectBoothType.alert('获取币种信息失败')
		})
		projectBoothType.post('/fnCurrency/getAll',{orgNum: org_num_for_pass,filterRmb: true},{ },function(rsp){
			if(rsp && rsp.length){
				rsp.forEach(it => {
					projectBoothType.currencys.set(it.id,it.name)
				})
			}
			$('#project-boothType-window-foreignCurrency').combobox({
				onSelect: function(record){ // {id,text}
					// console.log('onChange ',record);
					projectBoothType.setCurrency(record.id,record.text)
				}
			}).combobox('loadData',rsp)
		},function(e){
			console.log('error',e);
			projectBoothType.alert('获取币种信息失败')
		})
		// 开口算法 选择
		$('#project-boothType-window-openPriceWay').combobox({
			onChange: function(n,o){
				$('#project-boothType-window-openPrice,#project-boothType-window-foreignOpenPrice').textbox('setValue','');
				// console.log('onSelect ',record); // {id,text}
				// console.log('onChange ',n,typeof n,o,typeof o);
				$('.project-boothType-window-openPrice-0,.project-boothType-window-openPrice-1,.project-boothType-window-openPrice-2,.project-boothType-window-openPrice-3').addClass('hide')
				if(n == 2){ // 个 + area
					$('.jsf-after').html('/M²');
					$('.project-boothType-window-openPrice-1').removeClass('hide')
					$('#project-boothType-window-openPrice,#project-boothType-window-foreignOpenPrice').textbox({width:59})
				}else if(n == 4){ // 一口价
					$('.jsf-after').html('');
					$('.project-boothType-window-openPrice-3').removeClass('hide')
					$('#project-boothType-window-openPrice,#project-boothType-window-foreignOpenPrice').textbox({width:129})
				}else if(n == 3){ // long + area
					// 按个时不支持
					if('1' !== $('#project-boothType-window-priceWay').combobox('getValue')){
						projectBoothType.alert('定价类型不为 按面积, 无法使用 按开口长边加价 开口加价算法')
						$('#project-boothType-window-openPriceWay').combobox('setValue',o)
					}else{
						$('.jsf-after').html('');
						$('.jsf-after').removeClass('jsf-after-0 jsf-after-1').addClass('jsf-after-2');
						$('#project-boothType-window-openPrice,#project-boothType-window-foreignOpenPrice').textbox({width:139})
						$('.project-boothType-window-openPrice-2').removeClass('hide')
					}
				}else{ // 个
					$('.jsf-after').html('/个');
					$('.project-boothType-window-openPrice-0').removeClass('hide')
					$('#project-boothType-window-openPrice,#project-boothType-window-foreignOpenPrice').textbox({width:129})
				}
			}
		})
	}
	function project_boothType_search(){
		$('#project-boothType-datagrid').datagrid({
			url: eippath + '/boothType/getList?key=1',
			queryParams: {
				typeName: ($('#project-boothType-keyword-typeName').val()).trim(),
				exhibitCode: exhibitCode_for_pass
            }
		});
	}
	function project_boothType_formatIfStandard(val, row){
		if(val == false)return '否';
		else return '是';
	}
	function project_boothType_formatCertNum(val, row) {
		if(row.ifStandard) return val;
		else if(val || row.certSquareMeter) return (val || '_') + '人/' + (row.certSquareMeter || '_') + '平米';
		else return '';
	}
	function project_boothType_formatPriceWay(val, row){
		if(val == 0)return '按个';
		else if(val == 1)return '按面积';
	}
	function project_boothType_window_close(){
		$('#project-boothType-window').window({
			onBeforeClose: function(){
				if(project_boothType_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#project-boothType-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				project_boothType_fresh();
			}
		});
	}
	function project_boothType_toggleLastTab(){
		var ifShow = document.getElementById('project-boothType-textbox-ifAdjust').checked;
		if(ifShow){
			$('#project-boothType-panel-3 .tabs-last').css('display','inline-block');
		}else{
			// 当前是否为时段调价 tab
			var idx = $('#project-boothType-panel-3').tabs('getTabIndex',$('#project-boothType-panel-3').tabs('getSelected'));
			if(idx === 2){
				$('#project-boothType-panel-3').tabs('select',0);
			}
			$('#project-boothType-panel-3 .tabs-last').css('display','none');
		}
	}
	function project_boothType_setEditInfoClear(ul) {
		$(ul).find("li").each(function () {
			const $ipt  = $(this).find("input");
			var control = $ipt.attr("control");
			if (control == "textbox") $($ipt[0]).textbox("setValue", '');
			else if (control == "numberbox") $($ipt[0]).textbox("setValue", '');
			else if (control == "datebox") $($ipt[0]).textbox("setValue", '');
			else if (control == "datetimebox") $($ipt[0]).textbox("setValue", '');
			else if (control == "checkbox") $ipt[0].checked = false;
			// else if (control == "combobox") $($ipt[0]).combobox("clear");
		});
	}
	function project_boothType_add(){
		if(!project_boothType_window_close_set){
			project_boothType_window_close();
			project_boothType_window_close_set = true;
		}
		projectBoothType.disData.clear();
		projectBoothType.adjustData.clear();
		$.ajax({
			url: eippath + '/boothType/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {},	//发送数据
			async: false,
			success: function(data){
				project_boothType_alocate_id = -1;
				$("#boothDiagramPicUploadPicImage").attr("src","");
				$("#boothDiagramPicUploadPic").textbox('setText','');
				$('#project-boothType-window').window('open');
				// $('.jsf-fCurrency').html('');
				projectBoothType.initTab()
				project_boothType_save_id = data.id;
				project_boothType_setEditInfoClear("#project-boothType-panel-1 ul");
				$("#project-boothType-textbox-typeCode").textbox("setValue", data.id);
				project_boothType_setEditInfoClear("#project-boothType-panel-2 ul");
				projectBoothType.priceWayFlag = false;
				$('#project-boothType-window-priceWay').combobox('setValue','0'); // 按个
				projectBoothType.priceWayFlag = true;
				$('#project-boothType-textbox-boothProdCode,#project-boothType-textbox-boothProdName').val('');
				$('#project-boothType-textbox-openInfoProdCode,#project-boothType-textbox-openInfoProdName').val('');
				$('.project-boothType-window-openPrice-3').addClass('hide');
				$('#project-boothType-window-openPriceWay').combobox('clear'); // 开口数
				// $('#project-boothType-window-openPriceWay').combobox('setValue','1'); // 开口算法
				$('#project-boothType-window-projProductId').combobox('clear'); 		// 关联产品类型
				projectBoothType.setCurrency('','');
				$('#project-boothType-window-openPriceWay').combobox('setValue','');
				$('#project-boothType-window-foreignCurrency').combobox('setValue','');
				project_boothType_setEditInfoClear("#project-boothType-panel-3 ul");
				project_boothType_edit_mode = "Add";
				setEditInfoReadOnly(1, "#project-boothType-panel-1 ul");
				setEditInfoReadOnly(1, "#project-boothType-panel-2 ul");
				setEditInfoReadOnly(1, "#project-boothType-panel-3 ul");
				$('#project-boothType-textbox-openInfoProdName,#project-boothType-textbox-boothProdName').parent().removeClass('js-disable')
				setButtonEditMode(1, "#project-boothType-window-toolbar div");
				project_boothType_button_edit_mode = 1;

				if(document.getElementById('project-boothType-textbox-ifStandard').checked){
				$("#project-boothType-panel-2").show();
				}else{
					$("#project-boothType-panel-2").hide();
				}
				project_boothType_toggleLastTab();
				projectBoothType.disTableReload();
				projectBoothType.adjustTableReload();
				if(foreignCurrency_for_pass){
					$('#project-boothType-window-foreignCurrency').combobox('setValue', foreignCurrency_for_pass).combobox('disable')
				}else{
					$('#project-boothType-window-foreignCurrency').combobox('enable')
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothType_mod(){
	  if (flag)return;
		project_boothType_mod_or_view(1);
	}
	function project_boothType_mod_or_view(flag){
		if(!project_boothType_window_close_set){
			project_boothType_window_close();
			project_boothType_window_close_set = true;
		}
		var row = $('#project-boothType-datagrid').datagrid('getSelected');
		if (row) {
			project_boothType_setEditInfoClear("#project-boothType-panel-1 ul");
			project_boothType_setEditInfoClear("#project-boothType-panel-2 ul");
			// $('#project-boothType-window-priceWay').combobox('setValue','');
			projectBoothType.setCurrency(row.foreignCurrency,''); // 可先显示有效ID/无效RMB,如果有效值再触发select
			$('#project-boothType-window-openPriceWay').combobox('setValue','');
			$('#project-boothType-window-foreignCurrency').combobox('setValue','');
			 project_boothType_setEditInfoClear("#project-boothType-panel-3 ul");
			project_boothType_alocate_id = row.typeCode;
			if(![1,2,3,4].includes(row.openAlgorithmType)) row.openAlgorithmType = ''
			$('#project-boothType-window').window('open');
			projectBoothType.initTab();
			project_boothType_save_id = row.typeCode;
			setEditInfoInit(row, "#project-boothType-panel-1 ul");
			setEditInfoInit(row, "#project-boothType-panel-2 ul");
			row.priceWay = (typeof row.priceWay =='number') ? row.priceWay + '' : (row.priceWay || '');// fix bug
			projectBoothType.priceWayFlag = false;
			setEditInfoInit(row, "#project-boothType-panel-3 ul");
			// 因为combobox事件影响 重新设置单价
			$('#project-boothType-window-price').textbox('setValue',row.price || 0);
			projectBoothType.priceWayFlag = true;
			$('#project-boothType-textbox-boothProdCode').val(row.boothProdCode || '');
			$('#project-boothType-textbox-boothProdName').val(row.boothProdName || '');
			$('#project-boothType-textbox-openInfoProdCode').val(row.openInfoProdCode || '');
			$('#project-boothType-textbox-openInfoProdName').val(row.openInfoProdName || '');
			projectBoothType.disData.clear();
			projectBoothType.adjustData.clear();
			if(row.discountList && row.discountList.length){
				for(const it of row.discountList){
					projectBoothType.disData.set(it.memberGradeId,{
						memberGradeId:it.memberGradeId,
						discountType: it.discountType,
						discountNum: it.discountType === 'disAmount' ? it.discountNum: it.discountRatio,
						disF: it.foreignCurrency,
					})
				}
			}
			if(row.stagePriceList && row.stagePriceList.length){
				for(const it of row.stagePriceList){
					it.stageStartTime = it.stageStartTime.substr(0,10)
					it.stageEndTime   = it.stageEndTime.substr(0,10)
					projectBoothType.adjustData.set(it.stageStartTime + it.stageEndTime,{
						mark:it.stagePriceName,
						type: it.discountType,
						start: it.stageStartTime,
						end: it.stageEndTime,
						dis: it.discountType === 'disAmount' ? it.discountNum: it.discountRatio,
						disF: it.foreignCurrency,
					})
				}
			}
			project_boothType_edit(flag);
			project_boothType_toggleLastTab();
			projectBoothType.disTableReload()
			projectBoothType.adjustTableReload()
			let goOpen = row.boothDiagramPic ? row.boothDiagramPic.indexOf("http://zhanzhiyun.oss") : -1;
			if(goOpen == -1){
			$("#boothDiagramPicUploadPicImage").attr("src",attachUrl + row.boothDiagramPic)
			}else{
			$("#boothDiagramPicUploadPicImage").attr("src",row.boothDiagramPic)
			}
			$("#boothDiagramPicUploadPic").textbox('setText','');
			$('#project-boothType-window-foreignCurrency').combobox(Boolean(foreignCurrency_for_pass) ? 'disable': 'enable')
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_mod2(){
		project_boothType_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function project_boothType_edit(flag){
		project_boothType_edit_mode = "Mod";
		setEditInfoReadOnly(flag, "#project-boothType-panel-1 ul");
		setEditInfoReadOnly(flag, "#project-boothType-panel-2 ul");
		setEditInfoReadOnly(flag, "#project-boothType-panel-3 ul");
		if(flag === 1) {
			$('#project-boothType-textbox-openInfoProdName,#project-boothType-textbox-boothProdName').parent().removeClass('js-disable')
		} else {
			$('#project-boothType-textbox-openInfoProdName,#project-boothType-textbox-boothProdName').parent().addClass('js-disable')
		}
		setButtonEditMode(flag, "#project-boothType-window-toolbar div");
		setButtonEditMode(flag, "#project-boothType-dis-toolbar div");
		setButtonEditMode(flag, "#project-boothType-adjust-toolbar div");
		project_boothType_button_edit_mode = flag;

		if(document.getElementById('project-boothType-textbox-ifStandard').checked){
			$("#project-boothType-panel-2").show();
		}else{
			$("#project-boothType-panel-2").hide();
		}
	}
	function project_boothType_view(){
	    if (flag)return;
		project_boothType_mod_or_view(2);
	}
	function project_boothType_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/boothType/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {typeCode: project_boothType_save_id,exhibitCode:exhibitCode_for_pass,operEntrance:'展位类型设置'},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							project_boothType_alocate_id = -1;
							$('#project-boothType-window').window('close');
						}else{
							$.messager.alert('提示',data.msg || data.message || '删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function project_boothType_del_batch(){
		var rows = $('#project-boothType-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {typeCode: rows[i].typeCode,exhibitCode:rows[i].exhibitCode,operEntrance:'展位类型设置'};
			}
			$.messager.confirm('提示', '当前选择的展位类型数为：' + rows.length + '，确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/boothType/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','成功删除 ' + (+data.data.successNum || 0) + ' 个展位类型'
										+ (data.data.existRefBoothNum ? `，<br>未处理展位类型 \${data.data.existRefBoothNum} 个（已关联展位）` : '')
										+ (data.data.failNum ? `，<br>失败 \${data.data.failNum} 个（展位类型不存在）` : '')
										,'warning').window({width: 380});
								// $.messager.alert('提示','删除成功！','info');
								project_boothType_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_save(){
		if($("#project-boothType-textbox-typeCode").val() == ''){
			$.messager.alert('提示','类型代号不能为空！','warning');return;
		}
		const typeName = $("#project-boothType-textbox-typeName").val() || '';
		if(typeName.trim() == ''){
			$.messager.alert('提示','类型名称不能为空！','warning');return;
		} else if(typeName.length>50) {
			$.messager.alert('提示','类型名称长度不得超过50！','warning');return;
		}
		if(document.getElementById('project-boothType-textbox-ifStandard').checked){
			if($("#project-boothType-textbox-specLength").val() == '' ||
				$("#project-boothType-textbox-specWidth").val() == ''){
				$.messager.alert('提示','标摊长度与宽度不能为空！','warning');return;
			}
		}
		// // 开口算法 必须设置开口价
		// if($('#project-boothType-window-openPriceWay').combobox('getValue') != '4') {
		// 	if($('#project-boothType-window-openPrice').textbox('getValue') == '') {
		// 		$.messager.alert('提示','开口加价不能为空！','warning');return;
		// 	}
		// 	if($('#project-boothType-window-foreignPrice').textbox('getValue') != ''
		// 			&& $('#project-boothType-window-foreignOpenPrice').textbox('getValue') == '') {
		// 		$.messager.alert('提示','外币开口加价不能为空！','warning');return;
		// 	}
		// }
		/*$.ajax({
			url: eippath + '/boothType/isExistTypeCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {id: project_boothType_save_id, typeCode: $("#project-boothType-textbox-typeCode").val()},	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','该类型代号已经存在，请换一个！','warning');
				}else{*/
					var postData = {};
					postData['editMode'] = project_boothType_edit_mode;
					postData['operEntrance'] = '展位类型设置';
					if(project_boothType_edit_mode !== 'Add'){
						postData['boothType.id'] = $('#project-boothType-datagrid').datagrid('getSelected').id;
					}
					postData['boothType.exhibitCode'] = exhibitCode_for_pass;
					postData['boothType.boothProdCode'] = $('#project-boothType-textbox-boothProdCode').val();
					postData['boothType.openInfoProdCode'] = $('#project-boothType-textbox-openInfoProdCode').val();
					postData['boothType.twoOpenInfoPrice'] = $('#project-boothType-window-twoOpenInfoPrice').textbox('getValue');
					postData['boothType.twoOpenInfoForeignPrice'] = $('#project-boothType-window-twoOpenInfoForeignPrice').textbox('getValue');
					postData['boothType.threeOpenInfoPrice'] = $('#project-boothType-window-threeOpenInfoPrice').textbox('getValue');
					postData['boothType.threeOpenInfoForeignPrice'] = $('#project-boothType-window-threeOpenInfoForeignPrice').textbox('getValue');
					postData['boothType.fourOpenInfoPrice'] = $('#project-boothType-window-fourOpenInfoPrice').textbox('getValue');
					postData['boothType.fourOpenInfoForeignPrice'] = $('#project-boothType-window-fourOpenInfoForeignPrice').textbox('getValue');
					// if(!['1','2','3'].includes($('#project-boothType-window-openPriceWay').combobox('getValue'))){
					// 	$('#project-boothType-window-openPriceWay').combobox('setValue','1');
					// }
					projectBoothType.setPostData(postData, ".project-boothType-panel-ul");
					if(document.getElementById('project-boothType-textbox-ifStandard').checked)
						projectBoothType.setPostData(postData, ".project-boothType-panel-ul2");
					projectBoothType.setPostData(postData, ".project-boothType-panel-ul3");
					projectBoothType.setDisPostData(postData)
					if(document.getElementById('project-boothType-textbox-ifAdjust').checked){ // 启用调价
						projectBoothType.setAdjustPostData(postData)
					}
					$.ajax({
						url: eippath + '/boothType/save',
						dataType: "json",	//返回数据类型
						type: "post",
						data: postData,	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','保存成功！','info');
								project_boothType_edit(2);
								project_boothType_alocate_id = postData['typeCode'];
								$('#project-boothType-window').window('close');
							}else if(data.state == -1){
								$.messager.alert('提示','类型名称已存在,请更改','warning');
							}else{
								$.messager.alert('提示',data.msg || data.message || '保存失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
				/*}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});*/
	}
	function project_boothType_cancel(){
		$('#project-boothType-window').window('close', true);
	}
	var project_boothType_tools_boothTypeCode = null;
	function project_boothType_tools(){
	    if (flag)return;
		var row = $('#project-boothType-datagrid').datagrid('getSelected');
		if (row){
			$('#project-boothType-tools-window').window('open');
			project_boothType_tools_boothTypeCode = row.typeCode;
			$('#project-boothType-tools-datagrid').datagrid({
				url: eippath + '/defaultTool/getList',
				queryParams: {
					boothTypeCode: row.typeCode
	            },
	            onLoadSuccess: function(){
	            /* 	var rows = $("#project-boothType-tools-datagrid").datagrid("getRows");
					if (rows.length > 0){
						for(var i = 0; i < rows.length; i++){
							var tmp = '';
							if(rows[i].multiType == false)tmp = '否';
							else tmp = '是';
							$('#project-boothType-tools-datagrid').datagrid('updateRow',{
								index: i,
								row: {
									multiType: tmp
								}
							});
						}
					} */
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_tools_formatToolPic(val, row){
		if(val != null && val != '')
			return '<image src=\"http://' + location.hostname+':'+location.port + '/image/' + val + '\" style="width:100px;height:100px" />';
	}
	var project_boothType_tools_editIndex = null;
	function project_boothType_tools_onClickRow(index){
		if(project_boothType_tools_editIndex != null && project_boothType_tools_editIndex != index){
			$('#project-boothType-tools-datagrid').datagrid('endEdit', project_boothType_tools_editIndex);
			$('#project-boothType-tools-datagrid').datagrid('beginEdit', index);
			project_boothType_tools_editIndex = index;
		}else if(project_boothType_tools_editIndex == null){
			$('#project-boothType-tools-datagrid').datagrid('beginEdit', index);
			project_boothType_tools_editIndex = index;
		}else{
			$('#project-boothType-tools-datagrid').datagrid('beginEdit', index);
		}
	}
	function project_boothType_tools_save(){
		if(project_boothType_tools_editIndex != null){
			$('#project-boothType-tools-datagrid').datagrid('endEdit', project_boothType_tools_editIndex);
		}
		var postData = {};
		postData['boothTypeCode'] = project_boothType_tools_boothTypeCode;
		var rows = $('#project-boothType-tools-datagrid').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['defaultTools[' + i + '].boothTypeCode'] = project_boothType_tools_boothTypeCode;
				postData['defaultTools[' + i + '].toolId'] = rows[i].toolId;
				postData['defaultTools[' + i + '].isMoreSpec'] = rows[i].isMoreSpec;
				if(rows[i].defaultCount != '' && rows[i].defaultCount != null)
					postData['defaultTools[' + i + '].defaultCount'] = rows[i].defaultCount;
				if(rows[i].multiType == false || rows[i].multiType == '否')
					postData['defaultTools[' + i + '].multiType'] = false;
				else postData['defaultTools[' + i + '].multiType'] = true;
			}
			$.ajax({
				url: eippath + '/defaultTool/save',
				dataType: "json",
				type: "post",
				data: postData,
				success: function(data){
					if(data.state == 1){
						$('#project-boothType-tools-datagrid').datagrid("reload");
						$.messager.alert('提示','保存成功！','info');
					}else{
						$.messager.alert('提示','保存失败！','error');
					}
				}
			});
		}else{
			$.ajax({
				url: eippath + '/defaultTool/deleteAll',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {boothTypeCode:project_boothType_tools_boothTypeCode},	//发送数据
				async: true,
				success: function(data){
					if(data.state == 1){
						$.messager.alert('提示','保存成功！','info');
					}else{
						$.messager.alert('提示','保存失败！','error');
					}
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}
	}
	function project_boothType_tools_cancel(){
		$('#project-boothType-tools-window').window('close');
	}
	function project_boothType_tools_select(){
		$('#project-boothType-tools-select-window').window('open');
		$('#project-boothType-tools-select-datagrid').datagrid({
			url: eippath + '/tool/getListInExhibitNotInBooth',
			queryParams: {
				exhibitCode: exhibitCode_for_pass,
				boothTypeCode: project_boothType_tools_boothTypeCode
            },
			onLoadSuccess: function(data){
				if(data.total>0){
					var dataB = $('#project-boothType-tools-datagrid').datagrid('getData');
					if(dataB.total>0){
						for(var j=0;j<data.rows.length;j++){
							for(var i=0;i<dataB.rows.length;i++){
								if(data.rows[j].toolId==dataB.rows[i].toolId){
									var rowIndex = $('#project-boothType-tools-select-datagrid').datagrid('getRowIndex', data.rows[j]);
									$('#project-boothType-tools-select-datagrid').datagrid('deleteRow', rowIndex);

								}
							}
						}
					}
				}
			}
		});

	}
	function project_boothType_tools_remove(){
		var rows = $('#project-boothType-tools-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				var rowIndex = $('#project-boothType-tools-datagrid').datagrid('getRowIndex', rows[i]);
				$('#project-boothType-tools-datagrid').datagrid('deleteRow', rowIndex);
			}
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_tools_select_search(){
		$('#project-boothType-tools-select-datagrid').datagrid({
			url: eippath + '/tool/getListInExhibitNotInBooth',
			queryParams: {
				toolName: $('#project-boothType-tools-select-keyword-toolName').val(),
				exhibitCode: exhibitCode_for_pass,
				boothTypeCode: project_boothType_tools_boothTypeCode
            }
		});
	}
	function project_boothType_tools_select_save(){
		var rows = $('#project-boothType-tools-select-datagrid').datagrid('getSelections');
		var rows2 = $("#project-boothType-tools-datagrid").datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				flag = 0;
				for(var j = 0; j < rows2.length; j++){
					if(rows2[j].toolId == rows[i].toolId){
						flag = 1;
						break;
					}
				}
				if(flag == 0){
					$('#project-boothType-tools-datagrid').datagrid('appendRow', {
						toolId: rows[i].toolId,
						defaultCount: 0,
						multiType: '否',
						toolCode: rows[i].toolCode,
						toolName: rows[i].toolName,
						toolType: rows[i].toolType,
						defaultCount:1,
						preorderPrice: rows[i].preorderPrice,
						onsitePrice: rows[i].onsitePrice,
						toolPic: rows[i].toolPic,
						memo: rows[i].memo
					});
				}
			}
			$('#project-boothType-tools-select-window').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_tools_select_cancel(){
		$('#project-boothType-tools-select-window').window('close');
	}

	var project_boothType_cert_boothTypeCode = 0;
	function project_boothType_cert(){
	    if (flag)return;
		var row = $('#project-boothType-datagrid').datagrid('getSelected');
		if (row){
			$("#project_boothType_cert_memo").textbox("setValue",row.certMemo);
			$("#project_boothType_cert_num").textbox("setValue",row.certNum);
			project_boothType_certNum = +row.certNum || 0;
			project_boothType_certMeter = +row.certSquareMeter || 0;
			$("#project_boothType_cert_price").textbox("setValue",row.certPrice);
			// $('#project_boothType_cert_projProductId').combobox('setValue', row.projProductCertId || '');
			$("#project_boothType_cert_certSquareMeter").textbox("setValue",row.certSquareMeter);
			$('#project_boothType_cert_certProdCode').val(row.certProdCode || '');
			$('#project_boothType_cert_certProdName').val(row.certProdName || '');
			$("#project_boothType_cert_certCurrency").val(row.certCurrency || '');
			project_boothType_cert_init(row.ifStandard);
			$('#project-boothType-cert-window').window('open',true);
			project_boothType_cert_boothTypeCode = row.typeCode;

			project_boothType_cert_query_currency();
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	var project_boothType_certNum = 0;
	var project_boothType_certMeter = 0;
	var project_boothType_inviteNum = 0;
	var project_boothType_inviteMeter = 0;
	function project_boothType_checkNum(isCert = true, para) {
		let tip = false;
		if(isCert && (project_boothType_certNum != +para.certNum || project_boothType_certMeter != +para.certSquareMeter)) {
			tip = true;
		} else if(!isCert && (project_boothType_inviteNum != +para.inviteNum || project_boothType_inviteMeter != +para.inviteSquareMeter )) {
			tip = true;
		}
		if(!tip) {
			if(isCert) {
				project_boothType_cert_save_final(para)
			} else if(!isCert) {
				project_boothType_invite_save_final(para)
			}
			return;
		}
		let target = isCert ? '进馆证' : '邀请函';
		var updateTaskDtlScope = '',timer ='';
		$.messager.confirm({
			title: '提示',
			msg: `<div style="padding-left: 45px;">\${target}信息已发生修改, 是否更新已启动服务流程的\${target}工单 ?
				<br >
				<label>
					<input type="radio" name="projectBoothTypeTmpSure[]" value="1" checked />
					更新未审核工单
				</label>
				<br >
				<label>
				 <input type="radio" name="projectBoothTypeTmpSure[]" value="2" />
				 更新全部工单
				</label>
				<br >
				<label>
				 <input type="radio" name="projectBoothTypeTmpSure[]" value="3" />
				 不更新工单
				</label>
				</div>`,
			fn: function(r){
				if(timer) clearInterval(timer);
				if(r){
					para.updateTaskDtlScope = updateTaskDtlScope;
					if(isCert) {
						project_boothType_cert_save_final(para)
					} else if(!isCert) {
						project_boothType_invite_save_final(para)
					}
				}
			}
		}).window({
			width: '440px',
			cls: 'fix-easyui-warning',
			// icon: 'warning',
			onBeforeClose: function() {
				updateTaskDtlScope = +$("input[name='projectBoothTypeTmpSure[]']:checked").val() || 0;
			}
		});
	}
	function project_boothType_cert_cancel(){
		$('#project-boothType-cert-window').window('close');
	}
	function project_boothType_cert_save_final(para){
		$.ajax({
			url: eippath + '/boothType/updateCert',
			dataType: "json",	//返回数据类型
			type: "post",
			data: para,
			async: true,
			success: function(data){
				if(data.state > 0){
					project_boothType_certNum = +para.certNum;
					project_boothType_certMeter = +para.certSquareMeter;
					project_boothType_fresh();
					$.messager.alert('提示','保存成功！','info');
					// project_boothType_cert_cancel();
				}else{
					$.messager.alert('提示',data.msg || data.message || '保存失败！','error');
				}
				var row = $('#project-boothType-datagrid').datagrid('getSelected');
				project_boothType_alocate_id = row.typeCode;
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothType_cert_save(){
		//alert("a")

		var certNum = $("#project_boothType_cert_num").textbox("getValue");
		var certPrice = $("#project_boothType_cert_price").textbox("getValue");
		// if(certNum==''){
		// 	$.messager.alert('提示','请填写标配数量','warning');
		// 	return;
		// }
		if(certPrice==''){
			$.messager.alert('提示','请填写单价','warning');
			return;
		}
		project_boothType_checkNum(true, {
			typeCode:project_boothType_cert_boothTypeCode,
			exhibitCode:exhibitCode_for_pass,
			// projProductCertId: $("#project_boothType_cert_projProductId").combobox("getValue"),
			certNum:certNum ,
			certPrice:certPrice,
			certMemo:$("#project_boothType_cert_memo").textbox("getValue"),
			certSquareMeter:$("#project_boothType_cert_certSquareMeter").textbox("getValue"),
			certProdCode:$("#project_boothType_cert_certProdCode").val(),
			certCurrency:$("#project_boothType_cert_certCurrency").combobox("getValue"),
		});
	}


	function project_boothType_cert_query_currency(){
		$.ajax({
			url: eippath + '/prodCurrency/selectCurrency',
			dataType: "json",
			type: "post",
			data: {projectId:project_for_pass,type:"CERT_PRICE"},
			async: true,
			success: function(data){

				//alert(JSON.stringify(data))
				if(data!=null){
					$("#project_boothType_cert_currency").html("("+data.f_currency_name+")");
				}else{
					$("#project_boothType_cert_currency").html("");
				}


			},
			error: function(){
				$("#project_boothType_cert_currency").html("");
			}
		});
	}

	// 批量重算展位价格
	function project_boothType_reCalc(){
		var rows = $('#project-boothType-datagrid').datagrid('getSelections');
		if(rows && rows.length){
			$.messager.confirm('提示', '展位价格重新根据展位类型设置进行计算。 所有展位价格即将更新，请对存在特殊价格改动的展位设置展位类型，以避免价格更新后部分展位价格错误。', function(r){
				if (r){
					projectBoothType.post('/exhibitBooth/calculateBoothPrice',{
						boothTypeCodes: rows.map(i => i.typeCode).join(','),
						exhibitCode: exhibitCode_for_pass,
						operEntrance:'展位类型设置'
					},{
					},function(res){
						projectBoothType.checkReturn(res);
						projectBoothType.alert('操作成功 !', 'info');
					},function(){
						projectBoothType.alert('操作失败 !')
					})
				}
			})
		}else{
			projectBoothType.alert('需要至少选择一项 !')
		}

	}

	var project_boothType_invite_boothTypeCode = 0;
	function project_boothType_invite(){
		if (flag)return;
		var row = $('#project-boothType-datagrid').datagrid('getSelected');
		if (row){
			$("#project_boothType_invite_memo").textbox("setValue",row.inviteMemo);
			$("#project_boothType_invite_num").textbox("setValue",row.inviteNum);
			project_boothType_inviteNum = +row.inviteNum || 0;
			project_boothType_inviteMeter = +row.inviteSquareMeter || 0;
			$("#project_boothType_invite_price").textbox("setValue",row.invitePrice);
			// $('#project_boothType_invite_projProductId').combobox('setValue', row.projProductInviteId || '');
			$("#project_boothType_invite_inviteSquareMeter").textbox("setValue",row.inviteSquareMeter);
			$('#project_boothType_invite_inviteProdCode').val(row.inviteProdCode || '');
			$('#project_boothType_invite_inviteProdName').val(row.inviteProdName || '');
			$('#project_boothType_invite_inviteCurrency').val(row.inviteCurrency || '');
			project_boothType_invite_init(row.ifStandard);
			$('#project-boothType-invite-window').window('open',true);
			project_boothType_invite_boothTypeCode = row.typeCode;

			project_boothType_invite_query_currency();
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_boothType_invite_cancel(){
		$('#project-boothType-invite-window').window('close');
	}
	function project_boothType_invite_save_final(para){
		$.ajax({
			url: eippath + '/boothType/updateInvite',
			dataType: "json",	//返回数据类型
			type: "post",
			data: para,
			async: true,
			success: function(data){
				if(data.state > 0){
					project_boothType_inviteNum = +para.inviteNum;
					project_boothType_inviteMeter = +para.inviteMeter;
					project_boothType_fresh();
					$.messager.alert('提示','保存成功！','info');
					// project_boothType_invite_cancel();

				}else{
					$.messager.alert('提示',data.msg || data.message || '保存失败！','error');
				}
				var row = $('#project-boothType-datagrid').datagrid('getSelected');
				project_boothType_alocate_id = row.typeCode;
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_boothType_invite_save(){
		//alert("a")

		var inviteNum = $("#project_boothType_invite_num").textbox("getValue");
		var invitePrice = $("#project_boothType_invite_price").textbox("getValue");
		if(inviteNum==''){
			$.messager.alert('提示','请填写标配数量','warning');
			return;
		}
		if(invitePrice==''){
			$.messager.alert('提示','请填写单价','warning');
			return;
		}
		project_boothType_checkNum(false, {
			typeCode:project_boothType_invite_boothTypeCode,
			exhibitCode:exhibitCode_for_pass,
			// projProductInviteId: $("#project_boothType_invite_projProductId").combobox("getValue"),
			inviteNum:inviteNum ,
			invitePrice:invitePrice,
			inviteMemo:$("#project_boothType_invite_memo").textbox("getValue"),
			inviteSquareMeter:$("#project_boothType_invite_inviteSquareMeter").textbox("getValue"),
			inviteProdCode:$("#project_boothType_invite_inviteProdCode").val(),
			inviteCurrency:$("#project_boothType_invite_inviteCurrency").combobox("getValue"),
		});

	}


	function project_boothType_invite_query_currency(){
		$.ajax({
			url: eippath + '/prodCurrency/selectCurrency',
			dataType: "json",
			type: "post",
			data: {projectId:project_for_pass,type:"INVITE_PRICE"},
			async: true,
			success: function(data){

				//alert(JSON.stringify(data))
				if(data!=null){
					$("#project_boothType_invite_currency").html("("+data.f_currency_name+")");
				}else{
					$("#project_boothType_invite_currency").html("");
				}


			},
			error: function(){
				$("#project_boothType_invite_currency").html("");
			}
		});
	}
</script>