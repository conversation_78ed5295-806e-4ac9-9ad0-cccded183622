<%@ page contentType="text/html;charset=UTF-8" %>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<script src="${eippath}/js/jquery.qrcode.logo.ex.js" type="text/javascript" charset="utf-8"></script>
<style>
    .all_address {
        box-sizing: border-box;
        padding-bottom: 10px;
        margin: 8px 30px 0 30px;
        border-bottom: 1px solid #ccc;
    }

    .all_address h6 {
        font-size: 16px;
        font-weight: normal;
        color: #333333;
        margin-bottom: 8px;
    }

    .all_address textarea {
        width: 100%;
        border: none;
        resize: none;
        outline: none;
        font-size: 14px;
        color: #808080;
    }

    .btn-primary {
        width: 84px;
        height: 32px;
        border-radius: 6px;
        font-size: 14px;
        border: solid 1px #4c72fe;
        color: #4c72fe;
        background-color: #fff;
        outline: none;
        cursor: pointer;
    }

    .all_address > a {
        width: 84px;
        height: 30px;
        background-color: #fff;
        border-radius: 6px;
        border: solid 1px #999999;
        display: inline-block;
        text-align: center;
        line-height: 30px;
        margin-left: 20px;
        color: #666666;
    }

    .all_address > a:hover {
        color: #666666;
    }

    .address_download {
        overflow: hidden;
        margin: 12px 30px 15px;
    }

    .address_download ul li {
        float: left;
        margin-right: 20px;
    }

    .am-btn {
        font-size: 14px;
        width: 100%;
        color: #333333;
        border: none;
        background-color: #fff;
        height: 26px;
        cursor: pointer;
        outline: none;
    }

    .prompt_text {
        width: 144px;
        font-size: 16px;
        line-height: 26px;
        color: #333333;
        margin-left: 14px;
        margin-top: 29px;
    }

    #meeting-home-window canvas {
        width: 170px;
    }

    #meeting-home-window .datagrid-header-row > td,
    #meeting-home-window .datagrid-row > td {
        padding: 12px;
    }

    .cert-link-wrapper .datagrid-body{
        width: 680px!important;
    }
</style>
<div class="easyui-layout" data-options="fit:true">
  <div data-options="region:'north',split:true" style="height:100px;text-align:center;">
    <div style="height: 50px;line-height:50px;font-size: 20px;">
      <span>${exhibition.exihibitName}</span>
    </div>
    <div style="width: 100%;height:30px;margin-top:10px;">
      <span style="display:inline-block;">开幕时间：</span>
      <span style="display:inline-block;width: 150px;">${exhibition.startDate}</span>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <span style="display:inline-block;">闭幕时间：</span>
      <span style="display:inline-block;width: 150px;">${exhibition.endDate}</span>
    </div>
  </div>
  <div data-options="region:'center'" title="" style="width:100%;height:100%;">
    <table
        id="meeting-cert-set-datagrid"
        class="easyui-datagrid"
        data-options="rownumbers:true,
				 singleSelect:false,
				 pageSize:20,
				 pagination:false,
				 multiSort:true,
				 fitColumns:true,
				 fit:true,method:'post',
				 selectOnCheck:true,
				 onClickRow(rowIndex, rowData) {
					var l = $(this).datagrid('getRows').length;
					for(var i = 0; i < l; i++){
						if(i != rowIndex)$(this).datagrid('unselectRow', i);
					}
				}">
      <thead>
      <tr>
        <th data-options="field:'ck',checkbox:true"></th>
        <th data-options="field:'projectName',width:80,align:'center'">项目名称</th>
        <th data-options="field:'isMain',width:80,align:'center'" formatter="v => v ? '是': '否'">是否主项目</th>
        <th data-options="field:'enableCertificate',width:180,align:'center'" formatter="v => v ? '是': '否'">
          是否启用商城
        </th>
        <th data-options="field:'3',width:180,align:'center'" formatter="meetingCertSetFormatter">票证设置</th>
        <th data-options="field:'2',width:180,align:'center'" formatter="meetingCertSetHomeFormatter">首页地址</th>
      </tr>
      </thead>
    </table>
  </div>
</div>
<div id="meeting-home-window" class="easyui-window" toolbar="#meeting-home-window-toolbar"
     data-options="closed:true,title:'中文版链接',modal:true,maximizable: false,resizable: false,minimizable: false,shadow:false,top:80"
     style="width:700px;">
  <div class="easyui-panel">
    <div class="easyui-tabs" style="height:600px">
      <div style="width: 99%" title="会议系统首页">
        <div class="all_address">
          <h6>手机版首页地址</h6>
          <textarea cols="50" rows="3" id="meeting-home-phone" readonly="readonly"></textarea>
          <button class="btn-primary" onclick="meetingCertSetCopyLink(this.previousElementSibling.id)">复制链接</button>
          <a id="meeting-home-phone-open" href="" target="_blank">直接打开</a>
        </div>
        <div class="all_address">
          <h6>电脑版首页地址</h6>
          <textarea cols="50" rows="3" id="meeting-home-computer" readonly="readonly"></textarea>
          <button class="btn-primary" onclick="meetingCertSetCopyLink(this.previousElementSibling.id)">复制链接</button>
          <a id="meeting-home-computer-open" href="" target="_blank">直接打开</a>
        </div>
        <div class="address_download">
          <ul>
            <li>
              <div id="meeting-home-code" class="qr_code"></div>
              <div class="address_download_button">
                <a id="meeting-home-download" download="qrcode.jpg"></a>
                <button class="am-btn" onclick="meetingCertSetDownloadQrCode(this, '会议证件首页')">下载</button>
              </div>
            </li>
            <li>
              <div class="prompt_text">
                <p>左侧手机商城二维码点击下载即可手机扫码可预览</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div style="width: 99%" title="票证商城">
        <div class="all_address">
          <h6>手机版商城地址</h6>
          <textarea cols="50" rows="3" id="cert-mall-phone" readonly="readonly"></textarea>
          <button class="btn-primary" onclick="meetingCertSetCopyLink(this.previousElementSibling.id)">复制链接</button>
          <a id="cert-mall-phone-open" href="" target="_blank">直接打开</a>
        </div>
        <div class="all_address">
          <h6>电脑版商城地址</h6>
          <textarea cols="50" rows="3" id="cert-mall-computer" readonly="readonly"></textarea>
          <button class="btn-primary" onclick="meetingCertSetCopyLink(this.previousElementSibling.id)">复制链接</button>
          <a id="cert-mall-computer-open" href="" target="_blank">直接打开</a>
        </div>
        <div class="address_download">
          <ul>
            <li>
              <div id="cert-mall-code" class="qr_code"></div>
              <div class="address_download_button">
                <a id="cert-mall-download" download="qrcode.jpg"></a>
                <button class="am-btn" onclick="meetingCertSetDownloadQrCode(this, '票证商城')">下载</button>
              </div>
            </li>
            <li>
              <div class="prompt_text">
                <p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div style="width: 99%" title="票证列表" class="cert-link-wrapper">
        <div style="padding: 5px">
          <a href="javascript:void(0);"
             class="easyui-linkbutton"
             iconcls="icon-download-blue"
             onclick="meetingDetailsQRExport()"
             plain="true">导出票证详情链接</a>
        </div>
        <table id="cert-link-datagrid" class="easyui-datagrid"></table>
      </div>
    </div>
  </div>
</div>
<script>
  $(function () {
    meetingCertSetLoadTable()
  })

  function meetingCertSetLoadTable() {
    $('#meeting-cert-set-datagrid').datagrid({
      url: '/RegSever/projRegSet/getAllBuyerRegProject',
      queryParams: {exhibitCode: exhibitCode_for_pass},
    })
  }

  function meetingCertSetDownloadQrCode(btn, title) {
    title = title || '会议系统首页'
    var type = 'png';
    var c = btn.parentElement.previousElementSibling.querySelector('canvas')
    var imgdata = c.toDataURL("image/png");
    var fixtype = function (type) {
      type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
      var r = type.match(/png|jpeg|bmp|gif/)[0];
      return 'image/' + r;
    };
    imgdata = imgdata.replace(fixtype(type), 'image/octet-stream');
    //3.0 将图片保存到本地
    var savaFile = function (data, filename) {
      var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
      save_link.href = data;
      save_link.download = filename;
      var event = document.createEvent('MouseEvents');
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
      save_link.dispatchEvent(event);
    }
    const versionText = $('#meeting-home-window').attr('version') === '2' ? '英文版' : '中文版'
    savaFile(imgdata, `\${title}\${versionText}二维码.\${type}`)
  }

  function meetingCertSetCopyLink(id, originText, msgEl) {
    let el
    if (msgEl) {
      const m = msgEl.innerText
      msgEl.innerText = '复制成功'
      setTimeout(() => {
        msgEl.innerText = m
      }, 3000)
    }
    if (originText) {
      el = document.createElement('input')
      el.value = id
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
      return
    }
    el = document.getElementById(id)
    el.select()
    document.execCommand('copy')

  }

  function meetingCertSetFormatter(val, row) {
    const {buyerCertificateCount, meetCertificateCount} = row
    let text = '设置'
    const tmpTextList = []
    if (buyerCertificateCount) {
      tmpTextList.push(buyerCertificateCount + '种门票证件')
    }
    if (meetCertificateCount) {
      tmpTextList.push(meetCertificateCount + '种会议证件')
    }
    text = tmpTextList.length ? tmpTextList.join(' ') : text
    return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="meetingCertSetOpenSet(' + row.projectId + ')">' + text + '</a>'
  }

  function meetingCertSetHomeFormatter(val, row) {
    const {projectId, projectName} = row
    return `<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="meetingCertSetOpenHomePanel(\${projectId},'\${projectName}', 1, 1)">中文版地址</a>`
  }

  function meetingCertSetOpenHomePanel(projectId, projectName, target, version) {
    const eid = exhibitCode_for_pass
    const orgnum = org_num_for_pass
    const logoSrc = getLogoImgs(projectId, version)
    function setMeetHomeLink() {
      $("#meeting-home-code").empty();
      const search = '?EID=' + eid + '&target=1&orgnum=' + orgnum + '&pid=' + projectId + '&version=' + version + '&cid=&ctid=1#/meet'
      const computer = location.origin + '/web-reg-server/pc/vistor-register.html' + search
      const str = location.origin + '/web-reg-server/mobile/vistor-register-m.html' + search
      $('#meeting-home-phone').val(str);
      $("#meeting-home-computer").val(computer);
      $("#meeting-home-phone-open").attr({"href": str})
      $("#meeting-home-computer-open").attr({"href": computer})
      setTimeout('$("#meeting-home-code canvas").css("width", "170px")')
      $('#meeting-home-code').qrcode({
        src: logoSrc,
        text: str,
        correctLevel: 0,
        width: '1200',   //二维码的宽度
        height: '1200',  //二维码的高度
        imgWidth: 240,	 //图片宽
        imgHeight: 240, //图片高
      })
    }

    function setCertMallLink() {
      $("#cert-mall-code").empty();
      const search = '?eid=' + eid + '&pid=' + projectId + '&isEn=' + Boolean(version === 2)
      const computer = location.origin + '/eip-app-business/pages/cert-flows/cert-list.html' + search
      const str = location.origin + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/cert-flows/cert-list.html' + search
      $('#cert-mall-phone').val(str);
      $("#cert-mall-computer").val(computer);
      $("#cert-mall-phone-open").attr({"href": str})
      $("#cert-mall-computer-open").attr({"href": computer})
      setTimeout('$("#cert-mall-code canvas").css("width", "170px")')
      $('#cert-mall-code').qrcode({
        src: logoSrc,
        text: str,
        correctLevel: 0,
        width: '1200',   //二维码的宽度
        height: '1200',  //二维码的高度
        imgWidth: 240,	 //图片宽
        imgHeight: 240, //图片高
      })
    }

    function setCertTableLinks(data) {
      $('#cert-link-datagrid').datagrid({
        rownumbers: false,
        scrollbarSize: 0,
        singleSelect: true,
        pagination: false,
        multiSort: false,
        fitColumns: true,
        fit: true,
        columns: [[
          {
            field: 'certificateNameAbbr', title: '票证名称',
            formatter: (value, row) => {
              const name = value || row.certificateName
              return `<div style="max-width: 160px;word-break: break-all;white-space: break-spaces">\${name}</div>`
            },
          },
          {
            field: 'certType', title: '票证类型',
            formatter: value => value === 'C' ? '会议证件' : '门票证件',
          },
          {
            field: 'unitPrice', title: '价格',
            formatter: (value, row) => value && !row.isFree ? '￥' + value : '免费证件',
          },
          {
            field: 'detailLink', title: '详情链接',
            formatter: (value, row) => {
              return `
                <div style="width: 120px;">
                  <a style="color:#00f" href="javascript:" onclick="meetingCertSetCopyLink('\${value}', true, this)">复制</a>
                  <a style="color:#00f" href="\${value}" target="_blank">打开</a>
                </div>
              `
            },
          },
          {
            field: 'qrcode', title: '二维码',
            formatter: (_, row) => {
              return `
              <div style="display: flex;align-items: center;flex-direction: column">
                <div class="cert-qr-wrapper" style="width:120px;height:120px" data-flag="\${row.certificateId || ''}"></div>
                <div style="margin-top:10px"><a style="color:#00f" href="javascript:" onclick="meetingCertSetDownloadQrCode(this, '\${row.certificateName}')">下载二维码</a></div>
              <div>`
            }
          },
        ]],
        data,
        onLoadSuccess({rows}) {
          rows.forEach(item => {
            const {detailLink, certificateId} = item
            const $wrapper = $(`.cert-qr-wrapper[data-flag="\${certificateId}"]`)
            $wrapper.html("").qrcode({
              src: logoSrc,
              text: detailLink,
              correctLevel: 0,
              width: "1200",
              height: "1200",
            })
            setTimeout(() => {
              $wrapper.find('canvas').css("width", "120px")
            })
          })
        },
      })
    }

    setMeetHomeLink()
    setCertMallLink()

    meetingGetCertList(projectId, data => {
      setCertTableLinks(data.map(item => {
        const certificateId = item.certificateId
        const search = '?eid=' + eid + '&pid=' + projectId + '&isEn=' + Boolean(version === 2) + '&cid=' + certificateId
        item.detailLink = location.origin + '/eip-web-site/pages/mobile-farm-project-temporaryTwo/cert-flows/cert-details.html' + search
        return item
      }))
    })


    $('#meeting-home-window').window({title: version === 2 ? '英文版链接' : '中文版链接'}).window('open').attr({version})

    //兼容之前已经上传的图片
    function queryData2image(src) {
      const inner = src => {
        if (!src) return '/RegSever/RegPage/images/top-lucency.png'
        const prefix = location.origin
        src = src.replace(/\\/g, '/')
        if (/^(https?:)?\/\//.test(src)) return src
        if (src.startsWith('/')) return prefix + src
        return prefix + '/image/' + src
      }
      const url = inner(src)
      if (!url) return ''
      try {
        return new URL(url).host.indexOf('aliyuncs.com') !== -1
          ? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
          : url
      } catch (e) {
        return url
      }
    }

    function getLogoImgs(projectId, version) {
      let QRCodeLogo = ''
      $.ajax({
        url: '/eip-web-sponsor/api/project/getProjectLogo',
        data: {projectId},
        type: "post",
        async: false,
        success(data) {
          if (data.state !== 1) return
          const buyerLogoKey = version === 2 ? 'buyerRegEnLogo' : 'buyerRegCnLogo'
          const result = data.data || {}
          QRCodeLogo = result[buyerLogoKey] || result['exhibitionLogo']
        },
        error(err) {
          console.warn(err)
        }
      });
      return queryData2image(QRCodeLogo)
    }
  }

  function meetingCertSetOpenSet(projectId) {
    const target = new URL('/web-reg-server/pc/cert-settings.html', location.origin)
    target.searchParams.set('pid', projectId)
    target.searchParams.set('EID', exhibitCode_for_pass)
    target.searchParams.set('orgnum', org_num_for_pass)
    target.searchParams.set('version', '1')
    window.open(target.toString(), '_blank')
  }

  function meetingGetCertList(projectId, callback) {
    $.ajax({
      url: '/eip-web-sponsor/certificate/getList',
      data: {
        projectId,
        exhibitCode: exhibitCode_for_pass
      },
      type: 'post',
      success(data) {
        if (data.state !== 1) return $.messager.alert('提示', '数据加载失败', 'error')
        callback && callback(data.data)
      },
      error(err) {
        console.error(err)
        $.messager.alert('提示', '数据加载失败', 'error')
      }
    });
  }

  function meetingDetailsQRExport() {

    function getQRCodeDataURL(certificateId) {
      const canvas = $(`.cert-qr-wrapper[data-flag="\${certificateId}"] canvas`)[0]
      return canvas.toDataURL('image/png').replace(/^data:image\/(png|jpg);base64,/, "")
    }

    function exports(list) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('邀请渠道')
      let nextIndex = 0
      const head = getValuesFromRow(null)
      worksheet.addRow(head).height = 30
      const QRIndex = head.indexOf('二维码')
      nextIndex = 1
      list.forEach((item, index) => {
        const {certificateId} = item
        const row = worksheet.addRow(getValuesFromRow(item, index))
        row.height = 150
        const imgId = workbook.addImage({
          base64: getQRCodeDataURL(certificateId),
          extension: 'png',
        })
        worksheet.addImage(imgId, {
          tl: {col: QRIndex, row: nextIndex},
          br: {col: QRIndex + 1, row: nextIndex + 1},
          editAs: 'oneCell'
        })
        nextIndex++
      })
      worksheet.columns = head.map((_, index) => (index > 1 ? {width: 30} : {}))
      // 将所有单元格文本设置为居中
      worksheet.eachRow({includeEmpty: true}, function (row, rowNumber) {
        row.eachCell({includeEmpty: true}, function (cell, colNumber) {
          if (rowNumber === 1) {
            cell.font = {bold: true}
          }
          cell.alignment = {horizontal: 'center', vertical: 'middle'};
        });
      });
      workbook.xlsx.writeBuffer().then(buffer => saveAs(new Blob([buffer], {type: "application/octet-stream"}), "票证详情链接.xlsx"))
    }

    function getValuesFromRow(row, index) {
      // 返回表头
      if (!row) {
        return [
          '票证名称',
          '票证类型',
          '价格',
          '详情链接',
          '二维码'
        ]
      }
      const certTypeFmt = value => value === 'C' ? '会议证件' : '门票证件'
      const priceFmt = (value, isFree) => value && !isFree ? '￥' + value : '免费证件'
      const {
        certificateName,
        certificateNameAbbr,
        isFree,
        detailLink,
        unitPrice,
        certType,
      } = row
      return [
        certificateNameAbbr || certificateName,
        certTypeFmt(certType),
        priceFmt(unitPrice, isFree),
        detailLink,
        '',
      ]
    }

    exports(($('#cert-link-datagrid').datagrid('getData') || {}).rows || [])
  }
</script>