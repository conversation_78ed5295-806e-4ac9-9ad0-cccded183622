<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- <div id="project-floorplan-mgr"><h2>展位图已经初始化！</h2></div> -->
    	<div id="project-floorplan-panel-1" class="easyui-panel" title="设置展馆信息(注意：框架选定之后，不能修改，只能删除重制)" 
    		style="width:1200px;height:500px;padding:10px;">
    		<div class="easyui-layout" data-options="fit:true">
    			<div data-options="region:'west'" style="width:700px;padding:16px;">
    				<img id="project-floorplan-zimg" ondragstart="return false;">
    			</div>
    			<div data-options="region:'center'" style="padding:16px;">
    				<ul style="list-style:none;margin:0;padding:0;">
			        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>框架层级</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-floorplan-combobox-exhibitLevel" class="easyui-combobox" data-options="panelHeight:'auto'" style="width:175px;height:25px">
			        		<option value="1" >单层结构</option>
				            <option value="2" >两级结构</option>
				            <option value="3" >三级结构</option></select>
			        	</li>
			        	<li style="width:350px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>说明</label>&#12288;&#12288;&nbsp;&nbsp;&nbsp;&nbsp;单层结构 ：展位图>> 展位</li>
			        	<li style="width:350px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>&#12288;&#12288;&#12288;&#12288;</label>&nbsp;&nbsp;&nbsp;&nbsp;两级结构 ：展馆 >> 展位</li>
			        	<li style="width:350px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>&#12288;&#12288;&#12288;&#12288;</label>&nbsp;&nbsp;&nbsp;&nbsp;三级结构 ：展馆 >> 展区 >> 展位</li>
			        	
			       		<li id="project-floorplan-id5" style="width:350px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>展馆底图</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-floorplan-upfile" class="easyui-filebox" name="upfile" data-options="buttonText:'选择',prompt:'需小于2M'" style="width:250px;height:25px"></li>
			       		<li id="project-floorplan-id6" style="width:350px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>&#12288;&#12288;&#12288;&#12288;</label>&nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="easyui-linkbutton" onclick="project_floorplan_save()" style="width:60px;height:25px">保存</a></li>
			       	</ul>
    			</div>
    		</div>
    	</div>
    </div>
</div>
<script  type="text/javascript">
	$(function(){//alert("http://" + location.hostname+":"+location.port);
		$.ajax({
			url: eippath + '/exhibition/isInitMap',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {exhibitCode: exhibitCode_for_pass},	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					//$("#project-floorplan-mgr").show();
					//$("#project-floorplan-panel-1").panel("clear");
					//$("#project-floorplan-panel-1").panel("close");
					$("#project-floorplan-zimg").attr("src", "http://" + location.hostname+":"+location.port + '/image/' + data.pic);
					var img = document.getElementById('project-floorplan-zimg');
						img.width = 650;
						img.height = 400;
					$('#project-floorplan-combobox-exhibitLevel').combobox('setValue', data.level);
					$('#project-floorplan-combobox-exhibitLevel').combobox('disable');
					$("#project-floorplan-id5").hide();
					$("#project-floorplan-id6").hide();
				}else{
					$("#project-floorplan-mgr").hide();
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	
		$("#project-floorplan-upfile").filebox({
			onChange: function(n, o){
				var file = $("#project-floorplan-upfile").filebox("files")[0];
				if((file.size / 1024).toFixed(2) > 2050){
					//$("#project-floorplan-upfile").filebox("setValue", o);
					$.messager.alert('提示','所选图片大于2M，请先压缩到2M以下再上传。','warning');
					return;
				}
				var img = document.getElementById('project-floorplan-zimg');
				img.onload = function(){
					img.width = 650;
					img.height = 400;
				}
				var reader = new FileReader();
	    		reader.onload = function(evt){img.src = evt.target.result;}
	    		reader.readAsDataURL(file);
			}
		});
		
	});
	
	//计算图片大小
	function clacImgZoomParam( maxWidth, maxHeight, width, height ){
	    var param = {top:0, left:0, width:width, height:height};
	    if( width>maxWidth || height>maxHeight )
	    {
	        rateWidth = width / maxWidth;
	        rateHeight = height / maxHeight;
	        if( rateWidth > rateHeight )
	        {
	            param.width =  maxWidth;
	            param.height = Math.round(height / rateWidth);
	        }else
	        {
	            param.width = Math.round(width / rateHeight);
	            param.height = maxHeight;
	        }
	    }
	    param.left = Math.round((maxWidth - param.width) / 2);
	    param.top = Math.round((maxHeight - param.height) / 2);
	    return param;
	}
	
	function project_floorplan_save(){
		var file = $("#project-floorplan-upfile").filebox("files")[0];
		if(file == null){
			$.messager.alert('提示','请选择展位图！','warning');
			return;
		}
		if((file.size / 1024).toFixed(2) > 2050){
			$.messager.alert('提示','所选图片大于2M，请先压缩到2M以下再上传。','warning');
			return;
		}
		var formData = new FormData();
		formData.append("upfile", file);
		$.ajax({
			url: "${eippath}/exhibition/upload",
			dataType: "json",	//返回数据类型
			type: "post",
			data: formData,	//发送数据
			//告诉jQuery不要去处理发送的数据
        	processData: false,
        	//告诉jQuery不要去设置Content-Type请求头
        	contentType: false,
			async: true,
			success: function(data){
				if(data.state == 1){
					//$.messager.alert('提示','上传成功！','info');
					var uuid = data.uuid;
					$.ajax({
						url: eippath + '/exhibitSection/createExSection',
						dataType: "json",	//返回数据类型
						type: "post",
						data: {
							exhibitCode: exhibitCode_for_pass,
							uuid: uuid,
							sectionLevel: $('#project-floorplan-combobox-exhibitLevel').combobox('getValue')
						},	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','保存成功！','info');
								//$("#project-floorplan-panel-1").panel("clear");
								//$("#project-floorplan-panel-1").panel("close");
								//$("#project-floorplan-mgr").show();
								$('#project-floorplan-combobox-exhibitLevel').combobox('disable');
								$("#project-floorplan-id5").hide();
								$("#project-floorplan-id6").hide();
							}else{
								$.messager.alert('提示','保存失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
				}
				else $.messager.alert('提示','上传展位图失败！','error');
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
</script>
