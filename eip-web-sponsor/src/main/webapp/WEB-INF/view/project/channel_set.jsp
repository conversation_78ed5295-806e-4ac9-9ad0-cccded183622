<!-- 渠道基础表 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<script src="${eippath}/js/exceljs.min.js" type="text/javascript" charset="utf-8"></script>
<script src="${eippath}/js/FileSaver.js" type="text/javascript" charset="utf-8"></script>
<%@ include file="../common/reg_url.jsp" %>
<style>
	.tips-icon {
		display: inline-block;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		color: rgb(63,164,255);
		background-color: rgb(230,247,255);
		font-style: normal;
		text-align: center;
		line-height: 16px;
		font-size: 12px;
		font-weight: bold;
		margin-right: 5px;
	}
	.my-text{
		border-radius: 4px;
		width: 172px;
	}
	.hide {
		display:  none !important;
	}
</style>
<script>
	var fu_channel_type_set = top.checkFunCode('fu_channel_type_set');
</script>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false" toolbar="#channel-set-toolbar">
		<!-- begin of toolbar -->
		<div id="channel-set-toolbar">
			<div class="my-toolbar-search">
				<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input id="channel-set-search-projectName" class="my-text">&nbsp;
				<label>渠道类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input id="channel-set-search-channelTypeName" class="my-text">&nbsp;
				<label>渠道名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input id="channel-set-search-channelName" class="my-text">&nbsp;
				<a
						href="javascript:"
						class="easyui-linkbutton"
						style="width:70px;height:25px"
						onclick="channel_set_search()">查询</a>
			</div>
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="channel_set_fresh()"
					 plain="true">刷新</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="channel_set_add()"
					 plain="true">新增</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="channel_set_mod()"
					 plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="channel_set_del_batch()"
					 plain="true">删除</a>
				<a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-view" onclick="channel_set_view()"
					 plain="true">查看</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download-all"
					 onclick="channel_set_export()" plain="true">导出</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view"
					 onclick="open_visitor_set()" plain="true">点击数统计</a>
				<a href="javascript:void(0);" class="fu_channel_type_set easyui-linkbutton hide" iconCls="icon-db"
					 onclick="top.goOrgMenu('channel_type_set')" plain="true">渠道类别设置</a>
			</div>
		</div>
		<!-- end of toolbar -->
		<table id="channel-set-datagrid"
					 toolbar="#channel-set-toolbar"
					 class="easyui-datagrid"
					 data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,scrollbarSize:0,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			channel_set_mod_db(rowData);
 				},">
			<thead>
			<tr>
				<th data-options="field:'ck',checkbox:true"></th>
				<th data-options="field:'channelId',width:20">渠道ID</th>
				<th data-options="field:'projectName',width:30">项目名称</th>
				<th data-options="field:'targetName',width:30">适用身份</th>
				<th data-options="field:'channelTypeName',width:40" formatter="channel_set_channelTypeName">渠道类型</th>
				<th data-options="field:'channelName',width:40">渠道名称</th>
				<th data-options="field:'employeeName',width:40">操作人</th>
				<th data-options="field:'updateTime',width:40">修改时间</th>
			</tr>
			</thead>
		</table>
	</div>
</div>
<!-- begin of easyui-window -->
<div id="channel-set-window" class="easyui-window" toolbar="#channel-set-window-toolbar" data-options="closed:true,title:'渠道',modal:true,shadow: false" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="channel-set-window-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="add" class="easyui-linkbutton" iconCls="icon-add" onclick="channel_set_add()" plain="true">新增</a>
			<a href="javascript:void(0);" hint="mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="channel_set_mod2()" plain="true">修改</a>
			<a href="javascript:void(0);" hint="del" class="easyui-linkbutton" iconCls="icon-remove" onclick="channel_set_del()" plain="true">删除</a>
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="channel_set_save()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="channel_set_cancel()" plain="true">取消</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<div id="channel-set-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;">
		<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>项目</label></div>
				<input hint="projectId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" id="channel-set-panel-projectId"
							 data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>适用身份</label></div>
				<input class="easyui-combobox" style="width:175px;height:25px" id="channel-set-panel-targetList"
							 data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,multiple:true">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>渠道类型</label></div>
				<input hint="channelTypeId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" id="channel-set-panel-channelTypeId"
							 data-options="valueField:'channelTypeId',textField:'channelTypeName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>渠道名称</label></div>
				<input hint="channelName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px" id="channel-set-panel-channelName">
			</li>
			<li id="channel-set-panel-4-tips" style="clear: both; width: 100%;display:flex;">
				<div style="width:120px; text-align:right;flex-shrink: 0"><label><span class="tips-icon" style="margin-right: 10px">i</span></label></div>
				<p style="margin: 0">
					渠道类型为扫码即到场时，使用该渠道登记进来的观众将标记为已到场。本渠道会与闸机/PDA回传数据产生冲突，
					如使用闸机/PDA请避免使用扫码即到场渠道。
				</p>
			</li>
		</ul>
	</div>
</div>
<!-- end of easyui-window -->
<div id="visitor-set-window" class="easyui-window" toolbar="#visitor-set-window-toolbar" data-options="closed:true,title:'点击数统计',modal:true,shadow: false" style="width:480px;height: auto;">
	<!-- begin of toolbar -->
	<div id="visitor-set-window-toolbar">
		<div class="my-toolbar-button">
			<a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save" onclick="visitor_set_save()" plain="true">保存</a>
			<a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="visitor_set_close()" plain="true">取消</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<div class="easyui-panel" style="width: 100%;height:180px">
		<table id="visitor-set-datagrid" class="easyui-datagrid"
					 data-options="rownumbers:false,singleSelect:true,pagination:false,fitColumns:true,fit:true,method:'post',scrollbarSize:0">
			<thead>
			<tr>
				<th data-options="field:'name',width:20" align="center">设置项名称</th>
				<th data-options="field:'count',width:30" align="center" formatter="visitor_set_count_formatter">统计点击数</th>
				<th data-options="field:'exhibitCount',width:40" align="center" formatter="visitor_set_exhibitCount_formatter">开展期间统计点击数</th>
			</tr>
			</thead>
		</table>
	</div>
</div>

<script type="text/javascript">
	var channel_set_edit_mode;
	var channel_set_button_edit_mode;
	var channel_set_window_close_set = false;
	var channel_set_save_id;
	$(function(){
		setTimeout(function(){
			channel_set_fresh();
			visitor_set_load()
			$('#channel-set-panel-4-tips').hide()
			$("#channel-set-panel-projectId").combobox({
				onChange(value) {
					channel_set_load_load_roles(value)
				}
			})
		}, 0);
		if(fu_channel_type_set) {
			$('.fu_channel_type_set').removeClass('hide')
		} else {
			$('.fu_channel_type_set').remove();
		}
	});
	function channel_set_fresh() {
		channel_set_search(true)
	}
	function channel_set_operation(state, row) {
		const {inviteCodeId} = row
		const changeState = state === 1 ? 0 : 1
		const style = state === 0 ? 'color:#A2A2A2;font-weight:bold;' : 'color:#2E82E4;font-weight:bold;'
		const text = state === 0 ? '停用' : '启用'
		return `<a href="javascript:channel_set_change_operation('\${inviteCodeId}', \${changeState});" style="\${style}">\${text}</a>`
	}
	function channel_set_buyerNumber(buyerNumber, row) {
		const {inviteCodeId} = row
		if (!buyerNumber) return
		return `<a href="/RegSever/RegPage/pages/pms_buyer_reg_list_eip.html?EID=\${exhibitCode_for_pass}&orgnum=\${org_num_for_pass}&target=1&channelType=5&channelId=\${inviteCodeId}&channelName=&buyerId=" style="color:blue;font-weight:bold;" target="_blank">\${buyerNumber}</a>`
	}
	function channel_set_change_operation(inviteCodeId, state) {
		const _request = () => {
			$.messager.progress()
			$.ajax({
				url: eippath + '/inviteCode/updateState',
				dataType: "json",	//返回数据类型
				type: 'post',
				data: {inviteCodeId, state},
				success: function (data) {
					if (data.state !== 1)
						return $.messager.alert('提示', '数据加载失败！', 'error', () => $.messager.progress('close'))
					$.messager.progress('close')
					$.messager.alert('提示', '操作成功', 'info')
				},
				error: function () {
					$.messager.alert('提示', '数据发送失败！', 'error')
					$.messager.progress('close')
				}
			})
		}
		state === 1 ? $.messager.confirm('提示', '确定要停用该邀请码吗？', r => r && _request()) : _request()
	}
	function channel_set_search(flag){
		$.ajax({
			url: variableReg + '/eip-web-sponsor/project/getAllByExhibitCode',
			type: "post",
			data: {exhibitCode: exhibitCode_for_pass},
			datatype: 'json',
			success(resp) {
				try {
					window.AllProjectByExhibitCode = JSON.parse(resp.replace(/\t/g, '    '))
				} catch(e) {
					console.log(e, resp)
					window.AllProjectByExhibitCode = []
					$.messager.alert('警告', '项目名存在坏字符！', 'warning')
				}
				if(flag)$('#channel-set-search-channelTypeName').val("");
				$('#channel-set-datagrid').datagrid({
					url: eippath + '/channel/getPage',
					queryParams: {
						channelTypeName: $('#channel-set-search-channelTypeName').val(),
						channelName: $('#channel-set-search-channelName').val(),
						projectName: $('#channel-set-search-projectName').val(),
						exhibitCode: exhibitCode_for_pass
					}
				});
			},
			error(err) {
				$.messager.alert('数据加载失败！')
			},
		})
	}
	function channel_set_window_close(){
		$('#channel-set-window').window({
			onBeforeClose: function(){
				if(channel_set_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#channel-set-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
						}
					});
					return false;
				}
			},
			onClose: function(){
				channel_set_fresh();
			}
		});
	}
	function channel_set_add(){
		if(!channel_set_window_close_set){
			channel_set_window_close();
			channel_set_window_close_set = true;
		}
		channel_set_load_project();
		channel_set_load_channelType();
		$('#channel-set-window').window('open');
		channel_set_save_id = 0;
		channel_set_edit_mode = "Add";
		setEditInfoReadOnly(1, "#channel-set-panel-1 ul");
		setButtonEditMode(1, "#channel-set-window-toolbar div");
		$("#channel-set-panel-targetList").combobox('clear').combobox('enable')
		channel_set_button_edit_mode = 1;
		const $project = $("#channel-set-panel-projectId")
		const projectId = $project.combobox("getValue") || project_for_pass;
		$project.combobox("setValue", projectId);
		$("#channel-set-panel-channelName").textbox("clear");
	}
	function channel_set_mod_db(row){
		if(!channel_set_window_close_set){
			channel_set_window_close();
			channel_set_window_close_set = true;
		}
		channel_set_load_project();
		channel_set_load_channelType();
		channel_set_load_load_roles(row.projectId);
		$('#channel-set-window').window('open');
		channel_set_save_id = row.channelId;
		channel_set_getOne(row.channelId);
		channel_set_edit(2);
	}
	function channel_set_mod(_row){
		channel_set_mod_or_view(1);
	}
	function channel_set_mod_or_view(flag){
		if(!channel_set_window_close_set){
			channel_set_window_close();
			channel_set_window_close_set = true;
		}
		var row = $('#channel-set-datagrid').datagrid('getSelected');
		if (row){
			channel_set_load_project();
			channel_set_load_channelType();
			channel_set_load_load_roles(row.projectId);
			$('#channel-set-window').window('open');
			channel_set_save_id = row.channelId;
			channel_set_getOne(row.channelId);
			channel_set_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function channel_set_mod2(){
		channel_set_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function channel_set_edit(flag){
		channel_set_edit_mode = "Mod";
		setEditInfoReadOnly(flag, "#channel-set-panel-1 ul");
		setButtonEditMode(flag, "#channel-set-window-toolbar div");
		$("#channel-set-panel-targetList").combobox(flag === 1 ? 'enable' : 'disable')
		channel_set_button_edit_mode = flag;
	}
	function channel_set_view(){
		channel_set_mod_or_view(2);
	}
	function channel_set_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/channel/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {channelId: channel_set_save_id,operEntrance: "邀请渠道设置"},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							channel_set_save_id = 0;
							$('#channel-set-window').window('close');
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	function channel_set_del_batch(){
		var rows = $('#channel-set-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {channelId: rows[i].channelId,operEntrance: "邀请渠道设置"};
			}
			//'当前选择的展会类别数为：' + rows.length +
			$.messager.confirm('提示',  '确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/channel/batchDel',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								channel_set_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
				}
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function channel_set_save(){
		var projectId = $("#channel-set-panel-projectId").combobox("getValue");
		var channelTypeId = $("#channel-set-panel-channelTypeId").combobox("getValue");
		var channelName = $("#channel-set-panel-channelName").textbox("getValue");
		var targetList = $("#channel-set-panel-targetList").combobox("getValues") || [];
		if (!projectId) return $.messager.alert('提示', '项目不能为空！', 'warning')
		if (!targetList.length) return $.messager.alert('提示', '适用身份不能为空！', 'warning')
		if (!channelTypeId) return $.messager.alert('提示', '渠道类型不能为空！', 'warning')
		if (!channelName) return $.messager.alert('提示', '渠道名称不能为空！', 'warning')
		if (+channelTypeId === 4) {
			return $.messager.confirm('提示', '是否确认使用扫码及到场渠道？如启用闸机/PDA请避免使用该渠道。', f => f && _request())
		}
		_request()
		function _request() {
			var postData = {};
			// postData['editMode'] = channel_set_edit_mode;
			channel_set_save_id > 0 && (postData['channelId'] = channel_set_save_id)
			postData['projectId'] = projectId;
			postData['channelTypeId'] = channelTypeId;
			postData['channelName'] = channelName;
			postData['operEntrance'] = "邀请渠道设置";
			postData['targetList'] = targetList;
			$.messager.progress()
			$.ajax({
				url: '/eip-web-sponsor/channel/save',
				dataType: "json",	//返回数据类型
				type: "post",
				contentType: "application/json",
				data: JSON.stringify(postData),	//发送数据
				success(data) {
					$.messager.progress('close')
					if (data.state === 1) {
						channel_set_fresh();
						$.messager.alert('提示', '保存成功！', 'info');
						channel_set_edit(2);
						channel_set_save_id = data.data;
						$('#channel-set-window').window('close', true);
					} else {
						$.messager.alert('提示', '保存失败！', 'error');
					}
				},
				error() {
					$.messager.alert('提示', '数据发送失败！', 'error');
					$.messager.progress('close')
				}
			});
		}
	}
	function channel_set_cancel(){
		$('#channel-set-window').window('close', true);
	}
	function channel_set_load_project() {
		$.ajax({
			url: eippath + '/project/selectByExhibitCode',
			data: {
				exhibitCode: exhibitCode_for_pass
			},
			type: 'post',
			success(resp) {
				try {
					const data = JSON.parse(resp.replace(/\t/g, '    '))
					$('#channel-set-panel-projectId').combobox('loadData', data)
					$('#channel-set-inviteCodePanel-projectId').combobox('loadData', data)
				} catch (e) {
					console.log(e, resp)
					$.messager.alert('错误', '数据加载失败！', 'error')
				}
			},
			error(err) {
				console.log(err)
				$.messager.alert('错误', '数据加载失败！', 'error')
			}
		})
	}
	function channel_set_onchange_channelType(isFour) {
		$('#channel-set-panel-4-tips')[isFour ? 'show' : 'hide']()
		const $targetList = $("#channel-set-panel-targetList")
		const data = $targetList.combobox('getData')
		if (!isFour) {
			// $targetList.combobox('loadData', {data: data})
		} else {
			$targetList.combobox('loadData', {data: data.filter(({targetId}) => targetId !== 4)})
		}
		const v = $targetList.combobox('getValues')
		const d = $targetList.combobox('getData')
		const newValues = []
		if (Array.isArray(v) && v.length) {
			v.forEach(t => {
				if (d.find(item => item.targetId === +t)) {
					newValues.push(t)
				}
			})
			$targetList.combobox('setValues', newValues)
		}
	}
	function channel_set_load_channelType() {
		$('#channel-set-panel-channelTypeId').combobox({
			url: eippath + '/channelType/getList',
			onChange(value) {
				channel_set_onchange_channelType(+value === 4)
			},
			loadFilter(data) {
				data.unshift({
					channelTypeId: 4,
					channelTypeName: "扫码即到场"
				})
				return data
			},
		});
	}
	function channel_set_load_load_roles(regProjectId) {
		$("#channel-set-panel-targetList").combobox({
			url: '/RegSever/projRegSet/getRegTarget',
			queryParams: {regProjectId, containTrader: true},
			method: 'POST',
			loadFilter(data) {
				return data.data
			},
			onLoadSuccess(data) {
				if (data.state !== void 0) {
					const isFour = $('#channel-set-panel-channelTypeId').combobox('getValue') === '4'
					channel_set_onchange_channelType(isFour)
				}
			}
		})
	}
	function channel_set_getOne(channelId) {
		$.ajax({
			url: '/eip-web-sponsor/channel/getMoreById',
			data: {channelId},
			type: 'post',
			success({data, state}) {
				if (state !== 1) return $.messager.alert('错误', '数据加载失败！', 'error')
				setEditInfoInit(data, "#channel-set-panel-1 ul")
				$('#channel-set-panel-targetList').combobox('setValues', data.targetList)
			},
			error(err) {
				console.log(err)
				$.messager.alert('错误', '数据加载失败！', 'error')
			}
		})
	}
	function channel_set_channelTypeName(val, row) {
		return !val && row.channelTypeId === 4 ? '扫码即到场' : val
	}
	function channel_set_export() {
		function getValuesFromRow(row, index) {
			// 返回表头
			if (!row) {
				return [
					'序号',
					'渠道ID',
					'项目名称',
					'适用身份',
					'渠道类型',
					'渠道名称',
				]
			}
			const {
				channelId,
				projectName,
				targetName,
				channelTypeName,
				channelTypeId,
				channelName,
			} = row
			return [
				index + 1,
				channelId,
				projectName,
				targetName,
				channelTypeName || (channelTypeId === 4 ? '扫码即到场' : ''),
				channelName,
			]
		}
		function exportA(rows) {
			const workbook = new ExcelJS.Workbook();
			const worksheet = workbook.addWorksheet('邀请渠道设置')
			let nextIndex = 0
			const head = getValuesFromRow(null)
			worksheet.addRow(head).height = 30
			nextIndex = 1
			rows.forEach((item, index) => {
				worksheet.addRow(getValuesFromRow(item, index))
				nextIndex++
			})
			worksheet.columns = head.map((_, index) => (index > 1 ? {width: 30} : {}))
			// 将所有单元格文本设置为居中
			worksheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
				row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
					if (rowNumber === 1) {
						cell.font = { bold: true }
					}
					cell.alignment = { horizontal: 'center', vertical: 'middle' };
				});
			});
			workbook.xlsx.writeBuffer().then(buffer=>saveAs(new Blob([buffer], {type: "application/octet-stream"}), "邀请渠道设置.xlsx"))
		}

		const $table = $('#channel-set-datagrid')
		const rows = $table.datagrid('getSelections')
		if (!rows.length) {
			const data = ($table.datagrid('getData')|| {}).rows || []
			if (!data.length) return $.messager.alert('提示', '没有可导出的数据', 'warning')
			return $.messager.confirm('提示', '是否导出全部数据？', r => {
				if (!r) return
				$.messager.progress()
				$.ajax({
					url: eippath + '/channel/getPage',
					dataType: 'json',
					type: 'post',
					data: {
						channelTypeName: $('#channel-set-search-channelTypeName').val(),
						channelName: $('#channel-set-search-channelName').val(),
						projectName: $('#channel-set-search-projectName').val(),
						exhibitCode: exhibitCode_for_pass
					},
					success({state, rows, total}) {
						if (state !== 1) return $.messager.alert('提示', '导出失败！', 'error')
						if (!+total) return $.messager.alert('提示', '没有可导出的数据', 'warning')
						exportA(rows)
					},
					error() {
						return $.messager.alert('提示', '导出失败！', 'error')
					}
				}).done(() => $.messager.progress('close'))
			})
		}
		exportA(rows)
	}
	function open_visitor_set() {
		$('#visitor-set-window').window('open')
		visitor_set_load()
	}
	function visitor_set_load() {
		$('#visitor-set-datagrid').datagrid({
			url: '/RegSever/projRegTemp/queryVisitCountSet',
			queryParams: {exhibitCode: exhibitCode_for_pass},
			onClickRow() {
				$(this).datagrid('clearSelections');
			},
			loadFilter({data}) {
				const {systemChannelCount, systemChannelExhibitCount, customChannelCount, customChannelExhibitCount} = data
				return [
					{
						name: '固有渠道',
						count: systemChannelCount || false,
						countName: 'systemChannelCount',
						exhibitCount: systemChannelExhibitCount || false,
						exhibitCountName: 'systemChannelExhibitCount'
					},
					{
						name: '自定义渠道',
						count: customChannelCount || false,
						countName: 'customChannelCount',
						exhibitCount: customChannelExhibitCount || false,
						exhibitCountName: 'customChannelExhibitCount',
					}
				]
			}
		})
	}
	function visitor_set_close() {
		$('#visitor-set-window').window('close')
	}
	function visitor_set_count_formatter(_, row) {
		return `<label style="display:inline-block;width: 100%;text-align:center"><input id="visitor-set-item-\${row.countName}" type="checkbox" \${row.count ? 'checked' : ''}/></label>`
	}
	function visitor_set_exhibitCount_formatter(_, row) {
		return `<label style="display:inline-block;width: 100%;text-align:center"><input id="visitor-set-item-\${row.exhibitCountName}" type="checkbox" \${row.exhibitCount ? 'checked' : ''}/></label>`
	}
	function visitor_set_save() {
		const post = {}
		post.exhibitCode = exhibitCode_for_pass;
		'systemChannelCount,systemChannelExhibitCount,customChannelCount,customChannelExhibitCount'.split(',').forEach(key => {
			post[key] = $('#visitor-set-item-' + key).prop('checked')
		})
		$.messager.progress()
		$.ajax({
			url: '/RegSever/projRegTemp/saveVisitCountSet',
			dataType: "json",	//返回数据类型
			type: 'post',
			data: post,
			success(data) {
				if (data.state !== 1)
					return $.messager.alert('提示', data.msg || '数据加载失败！', 'error')
				visitor_set_close()
				$.messager.alert('提示', '保存成功', 'info')
			},
			error() {
				$.messager.alert('提示', '数据发送失败！', 'error')
			}
		}).done(() => $.messager.progress('close'))
	}
</script>