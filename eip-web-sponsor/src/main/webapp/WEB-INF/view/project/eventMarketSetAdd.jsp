<!-- 活动推广设置 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="240205"/>
<!-- <meta http-equiv='x-dns-prefetch-control' content='on'>
<link rel='dns-prefetch' href='//placehold.it'> -->
<title>活动推广设置</title>
<%--<link rel="stylesheet" href="../../css/colpick.css" type="text/css" />--%>
<link rel="stylesheet" href="${eippath}/css/view/eventMarketSetAdd.css?${v}">
<script>var eippath = '${eippath}'</script>
<style>
    .tips-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: rgb(63,164,255);
        background-color: rgb(230,247,255);
        font-style: normal;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        font-weight: bold;
        margin-right: 5px;
    }
    .poster-upload__desc {
        margin: 10px 0 20px;
    }
    .poster-upload__desc span:not(.tips-icon) {
        color: rgb(244, 48, 48);
    }
</style>
</head>
<body>
<div class="easyui-layout" data-options="fit:true" cloak>
  <div data-options="region:'center',border:false" id="conf-wrapper">
    <div style="width:100%;height:100%;">
      <div style="position: sticky; top:0; z-index: 999">
        <div class="my-toolbar-button">
          <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="util.fresh()"
             plain="true">刷新</a>
          <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="util.save()"
             plain="true">保存</a>
        </div>
      </div>
      <div class="template_set_pc">
        <ul>
          <li id="EnglishToggle" class="hide">
            <span class='webSetStyle webSetStyleActive' onclick='util.setLang(1,this)'>中文版网站设置</span>
            <span class='webSetStyle' onclick='util.setLang(2,this)'>英文版网站设置</span>
          </li>
          <li class="flex-float">
            <div style="width: 410px">
              <label style="width: 100%;">活动名称</label>
              <input id="eventName" class="easyui-textbox" style="width:350px;height:30px"
                     data-options="prompt:'活动名称'">
            </div>
            <%--<div>
              <label style="width: 100%;">相关展会</label>
              <input id="releProjectId" class="easyui-combobox" style="width:350px;height:30px"
                     data-options="
                     prompt:'主展会、子展会名称',
                     panelHeight:'auto',
                     valueField:'projectId',
                     textField: 'projectName',
                     panelMaxHeight:'200px',
                     limitToList:true,
                     editable:false,
                     multiple: true">
            </div>--%>
          </li>
          <li class="flex-float">
            <div style="width: 410px">
              <label style="width: 100%;">适用人员</label>
              <select
                  id="applyRoleType"
                  class="easyui-combobox"
                  style="width:350px;height:30px"
                  data-options="prompt:'适用人员',panelHeight:'auto',limitToList:true,editable:false">
                <option value="1">业务员</option>
                <option value="2">展商</option>
              </select>
            </div>
            <div style="width: 120px;display: none;" id="customPageWrapper">
              <label style="width: 100%;margin-bottom: 15px;">可自定义页面</label>
              <div style="display: flex;justify-content: space-between;">
                <label for="customPageTrue" style="margin:0">
                  <input id="customPageTrue" name="customPage" value="true" type="radio"> 是
                </label>
                <label for="customPageFalse" style="margin:0">
                  <input id="customPageFalse" name="customPage" checked value="false" type="radio"> 否
                </label>
              </div>
            </div>
          </li>
          <li class="flex-float">
            <div style="width: 410px">
              <label style="width: 100%;">活动模板类型</label>
              <select
                  id="eventTemplateType"
                  class="easyui-combobox"
                  style="width:350px;height:30px"
                  data-options="prompt:'活动模板类型',panelHeight:'auto',limitToList:true,editable:false,">
                <option value="poster">海报</option>
                <option value="micro-site">微站</option>
              </select>
            </div>
            <div class="poster-theme__item hide-by-poster-oper" style="padding-top: 5px;">
                <label>分享方式</label>
                <div>
                  <label style="margin: 0 20px 0 -7px">
                    <input name="shareType" value="EVENT_PROMOTION_POSTER" checked type="radio"> 展商邀请函海报
                  </label>
                  <label style="margin: 0 20px 0 0">
                    <input name="shareType" value="EXHIBITOR_INVITE" type="radio"> 展商邀请函
                  </label>
                  <label style="margin: 0">
                    <input name="shareType" value="CATA_SHARE" type="radio"> 会刊分享
                  </label>
                </div>
              </div>
          </li>
        </ul>
        <%--基础设置--%>
        <div class="module">
          <div class="module-title flex-between">
            <b>基础设置 <span class='showEg'>（英文版）</span></b>
          </div>
          <ul>
            <%--移动端顶部背景图--%>
            <li style="padding-bottom: 0;margin-bottom: 0;">
              <label id="mtTopBackImgLabel">移动端顶部背景图</label>
              <div class="upFileDiv flex">
                <input class="easyui-textbox" data-options="readonly:'true'"
                       id="jsf-mtTopBackImgName" style="width:348px;height: 30px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="jsf-mtTopBackImg" class="easyui-filebox upFileDivFileButton"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('mtTopBackImg')"><em>删除</em></i>
              </div>
            </li>
            <li class="flex-center-h">
              <div class="upFileImgDiv" style="width: 420px;height: 90px;margin-right: 20px">
                <img src="" alt="" id="jsf-mtTopBackImgUrl">
              </div>
              <div id="jsf-mtTopBackImgUrl-desc-1" class="poster-upload__desc">
                <span class="tips-icon">i</span>
                规格要求：图片宽度<span>750</span>px，<br/>推荐高度不小于<span>1656</span>px，图片小于<span>200</span>K
              </div>
              <p id="jsf-mtTopBackImgUrl-desc-2" class="upFileDivP">
                <span>规格推荐：建议小于200K图片建议以750像素进行设计<br> 注意：超出尺寸建议尺寸的图片可能会进行压缩处理，以保证用户正常使用</span>
              </p>
            </li>
            <%--移动端底部背景图--%>
            <li style="padding-bottom: 0;margin-bottom: 0;" class="hide-by-poster">
              <label>移动端底部背景图</label>
              <div class="upFileDiv flex">
                <input class="easyui-textbox" data-options="readonly:'true'"
                       id="jsf-mtBottomBackImgName" style="width:348px;height: 30px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="jsf-mtBottomBackImg" class="easyui-filebox upFileDivFileButton"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <i class="upFileDivButton" style="margin-left: 0;"
                   onclick="util.delImg('mtBottomBackImg', 'botImg')"><em>删除</em></i>
              </div>
            </li>
            <li class="flex-center-h hide-by-poster">
              <div class="upFileImgDiv" style="width: 424px;height: 145px;margin-right: 20px;">
                <img src="" alt="" id="jsf-mtBottomBackImgUrl">
              </div>
              <p class="upFileDivP">
                <span>规格推荐：建议小于200K图片建议以750像素进行设计<br> 注意：超出尺寸建议尺寸的图片可能会进行压缩处理，以保证用户正常使用</span>
              </p>
            </li>
            <%--热度排行榜顶图--%>
            <li style="padding-bottom: 0;margin-bottom: 0;" class="hide-by-poster">
              <label>热度排行榜顶图</label>
              <div class="upFileDiv flex">
                <input class="easyui-textbox" data-options="readonly:'true'"
                       id="jsf-hotListTopImgName" style="width:348px;height: 30px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="jsf-hotListTopImg" class="easyui-filebox upFileDivFileButton"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <i class="upFileDivButton" style="margin-left: 0;"
                   onclick="util.delImg('hotListTopImg')"><em>删除</em></i>
              </div>
            </li>
            <li class="flex-center-h hide-by-poster">
              <div class="upFileImgDiv" style="width: 424px;height: 90px;margin-right: 20px;">
                <img src="" alt="" id="jsf-hotListTopImgUrl">
              </div>
              <p class="upFileDivP">
                <span>规格推荐：建议小于200K图片建议以750像素进行设计<br> 注意：超出尺寸建议尺寸的图片可能会进行压缩处理，以保证用户正常使用</span>
              </p>
            </li>
            <%--快捷链接设置--%>
            <li class="flex-float">
              <div style="width: 410px" class="hide-by-poster">
                <label style="width: 100%;">启用顶部背景图下方快捷链接</label>
                <div style="width: 120px;display:flex;justify-content:space-between;">
                  <label for="enableTopLink-true" style="margin:0">
                    <input id="enableTopLink-true" name="enableTopLink" value="true" type="radio"> 是
                  </label>
                  <label for="enableTopLink-false" style="margin:0">
                    <input id="enableTopLink-false" name="enableTopLink" value="false" checked type="radio"> 否
                  </label>
                </div>
                <a href="javascript:"
                   style="margin-top: 13px;margin-left: 3px;"
                   onclick="util.openLinkSetting('top')"
                   class="easyui-linkbutton bor1 conf-enableTopLink"
                   plain="true">按钮链接配置</a>
              </div>
              <div style="width: 120px;">
                <label style="width: 100%;">启用底部快捷链接</label>
                <div style="display:flex;justify-content:space-between;">
                  <label for="enableBottomLink-true" style="margin:0">
                    <input id="enableBottomLink-true" name="enableBottomLink" value="true" type="radio"> 是
                  </label>
                  <label for="enableBottom-false" style="margin:0">
                    <input id="enableBottom-false" name="enableBottomLink" value="false" checked type="radio"> 否
                  </label>
                </div>
                <a href="javascript:"
                   style="margin-top: 13px;margin-left: 3px;"
                   onclick="util.openLinkSetting('bottom')"
                   class="easyui-linkbutton bor1 conf-enableBottomLink"
                   plain="true">按钮链接配置</a>
              </div>
            </li>
            <%--活动功能设置(模块开关)--%>
            <li class="hide-by-poster">
              <label>活动功能设置</label>
              <ul>
                <li class="flex-float" id="js-index-li">
                  <div>
                    <%--ms:module switch--%>
                    <label for="ms-ExhibitIntroduce">
                      <input type="checkbox" id="ms-ExhibitIntroduce">展会介绍
                    </label>
                  </div>
                  <div>
                    <label for="ms-OnLineExhibitionHall">
                      <input type="checkbox" id="ms-OnLineExhibitionHall">线上展厅
                    </label>
                  </div>
                  <div>
                    <label for="ms-ActivityRule">
                      <input type="checkbox" id="ms-ActivityRule">活动规则
                    </label>
                  </div>
                  <div>
                    <label for="ms-ProductDisplay">
                      <input type="checkbox" id="ms-ProductDisplay">产品展示
                    </label>
                  </div>
                  <div>
                    <label for="ms-AudienceComment">
                      <input type="checkbox" id="ms-AudienceComment">观众评论
                    </label>
                  </div>
                </li>
              </ul>
            </li>
            <li class="show-by-poster poster-theme flex-float" style="padding-bottom: 100px">
              <div class="poster-theme__item">
                <label>检索框底色</label>
                <div class="color-box-wrapper">
                  <span class="color-box" data-name="inputBgColor" style="background-color: #fff"></span>
                </div>
              </div>
              <div class="poster-theme__item">
                <label>检索框文字颜色</label>
                <div class="color-box-wrapper">
                  <span class="color-box" data-name="inputColor" style="background-color: #333"></span>
                </div>
              </div>
              <div class="poster-theme__item">
                <label>检索按钮颜色</label>
                <div class="color-box-wrapper">
                  <span class="color-box" data-name="buttonBgColor" style="background-color: #fff"></span>
                </div>
              </div>
              <div class="poster-theme__item">
                <label>检索按钮文字颜色</label>
                <div class="color-box-wrapper">
                  <span class="color-box" data-name="buttonColor" style="background-color: #333"></span>
                </div>
              </div>
              <div class="poster-theme__item " style="flex-basis: 100%;margin-top: 20px;display: grid;grid-template-columns: 120px 120px;grid-gap: 10px 20px;">
                <div>
                  <label style="color: #5567a5">横向偏移 (像素)</label>
                  <div class="search-offset-wrapper">
                    <input data-name="offsetH" class="easyui-numberbox search-set-ctrl" data-options="precision:0" style="width:120px;height: 30px;">
                  </div>
                </div>
                <div>
                  <label style="color: #5567a5">纵向偏移 (像素)</label>
                  <div class="search-offset-wrapper">
                    <input data-name="offsetV" class="easyui-numberbox search-set-ctrl" data-options="precision:0" style="width:120px;height: 30px;">
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="module hide-by-poster module-ExhibitIntroduce">
          <div class="module-title flex-between" onclick="util.toggleModule(this)">
            <b data-title="展会介绍"><span id="plateName-ExhibitIntroduce">展会介绍</span><span class='showEg'>（英文版）</span>
              <span class="title-edit" onclick="util.editTitle(this, event)">修改标题名称</span></b>
            <div class="flex-center">
              <a onclick="util.up(this,event)" href="#" class="easyui-linkbutton btn-up btn-plain"
                 data-options="iconCls:'icon-sort-up2'">上移</a>
              <a onclick="util.down(this,event)" href="#" class="easyui-linkbutton btn-down  btn-plain"
                 data-options="iconCls:'icon-sort-down2'">下移</a>
              <span class="eu-icon icon-arrow-down"></span>
            </div>
          </div>
          <ul>
            <li>
              <div>
                <label style="width: 100%;"><font color="red">*</font>展会介绍</label>
                <input class="easyui-textbox" id="ExhibitIntroduceText"
                       data-options="prompt: '请输入展会介绍（0-300字）',multiline: true" style="width: 875px;height:100px">
              </div>
            </li>
          </ul>
        </div>
        <div class="module hide-by-poster module-OnLineExhibitionHall">
          <div class="module-title flex-between" onclick="util.toggleModule(this)">
            <b data-title="线上展厅"><span id="plateName-OnLineExhibitionHall">线上展厅</span><span class='showEg'>（英文版）</span>
              <span class="title-edit" onclick="util.editTitle(this, event)">修改标题名称</span></b>
            <div class="flex-center">
              <a onclick="util.up(this,event)" href="#" class="easyui-linkbutton btn-up btn-plain"
                 data-options="iconCls:'icon-sort-up2'">上移</a>
              <a onclick="util.down(this,event)" href="#" class="easyui-linkbutton btn-down  btn-plain"
                 data-options="iconCls:'icon-sort-down2'">下移</a>
              <span class="eu-icon icon-arrow-down"></span>
            </div>
          </div>
          <ul>
            <li class="flex" style="padding-bottom: 0;margin-bottom: 0;flex-wrap: wrap">
              <div style="flex-basis: 100%;margin-bottom: 20px;">
                <label style="width: 100%;">设置线上展厅名称</label>
                <input id="OnLineExhibitionHallName" maxlength="10" class="easyui-textbox" style="width:350px;height:30px"
                       data-options="prompt:'设置线上展厅名称，限10字内',validType:['length[1,10]']">
              </div>
              <div style="width: 535px">
                <label><font color="red">*</font>设置线上展厅图片</label>
                <div class="upFileDiv flex">
                  <input class="easyui-textbox" data-options="readonly:'true'"
                         id="jsf-OnLineExhibitionHallName" style="width:348px;height: 30px;">
                  <i class="upFileDivButton">
                    <em>上传</em>
                    <input id="jsf-OnLineExhibitionHall" class="easyui-filebox upFileDivFileButton"
                           data-options="buttonText:'选择',prompt:''">
                  </i>
                  <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('OnLineExhibitionHall')"><em>删除</em></i>
                </div>
              </div>
              <div>
                <label>设置线上展厅链接</label>
                <div>
                  <input id="OnLineExhibitionHallLink" class="easyui-textbox" style="width:348px;height: 30px;" data-options="prompt:'http://example.com'">
                </div>
              </div>
            </li>
            <li class="flex-center-h">
              <div class="upFileImgDiv" style="width: 424px;height: 90px;margin-right: 20px;">
                <img src="" alt="" id="jsf-OnLineExhibitionHallUrl">
              </div>
              <p class="upFileDivP">
                <span>规格推荐：建议小于200K图片建议以750像素进行设计<br> 注意：超出尺寸建议尺寸的图片可能会进行压缩处理，以保证用户正常使用</span>
              </p>
            </li>
          </ul>
        </div>
        <div class="module hide-by-poster module-ProductDisplay">
          <div class="module-title flex-between">
            <b data-title="产品展示"><span id="plateName-ProductDisplay">产品展示</span><span class='showEg'>（英文版）</span>
              <span class="title-edit" onclick="util.editTitle(this, event)">修改标题名称</span></b>
            <div class="flex-center">
              <a onclick="util.up(this,event)" href="#" class="easyui-linkbutton btn-up btn-plain"
                 data-options="iconCls:'icon-sort-up2'">上移</a>
              <a onclick="util.down(this,event)" href="#" class="easyui-linkbutton btn-down  btn-plain"
                 data-options="iconCls:'icon-sort-down2'">下移</a>
            </div>
          </div>
          <%--<ul></ul>--%>
        </div>
        <div class="module hide-by-poster module-AudienceComment">
          <div class="module-title flex-between">
            <b data-title="观众评论"><span id="plateName-AudienceComment">观众评论</span><span class='showEg'>（英文版）</span>
              <span class="title-edit" onclick="util.editTitle(this, event)">修改标题名称</span>
            </b>
            <div class="flex-center">
              <a onclick="util.up(this,event)" href="#" class="easyui-linkbutton btn-up btn-plain"
                 data-options="iconCls:'icon-sort-up2'">上移</a>
              <a onclick="util.down(this,event)" href="#" class="easyui-linkbutton btn-down  btn-plain"
                 data-options="iconCls:'icon-sort-down2'">下移</a>
            </div>
          </div>
          <%--<ul></ul>--%>
        </div>
        <div class="module hide-by-poster module-ActivityRule">
          <div class="module-title flex-between">
            <b><span id="plateName-ActivityRule">活动规则</span><span class='showEg'>（英文版）</span></b>
          </div>
          <ul>
            <li>
              <div>
                <label style="width: 100%;"><font color="red">*</font>活动规则</label>
                <input class="easyui-textbox" id="ActivityRuleText" data-options="prompt: '请输入活动规则',multiline: true"
                       style="width: 875px;height:100px">
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <%@ include file="../common/project_widow_eventMarketSetAdd.jsp" %>
</div>
<span loading>加载中...</span>

<link rel="stylesheet" href="../../css/colpick.css" type="text/css" />
<script src="../../js/colpick.js"></script>
<script src="../../js/wangEditor.min.js"></script>
<script src="../../js/view/eventMarketSetAdd.js?${v}"></script>
</body>
</html>