<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<style>
    .tb {
        width: 100%;
        margin: 0;
        padding: 5px 4px;
        border: 1px solid #ccc;
        box-sizing: border-box;
    }

    .channelButtonStyle {
        color: #fff;
        display: block;
        width: 146px;
        height: 32px;
        background-color: #4fa0ff;
        border-radius: 6px;
        text-align: center;
        line-height: 32px;
        margin: 5px auto 15px;
        font-size: 14px;
        cursor: pointer;
    }
</style>
<table
    id="buyer-coupon-code-datagrid"
    class="easyui-datagrid"
    data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
          onClickRow: function (rowIndex, rowData) {
            var l = $(this).datagrid('getRows').length;
            for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);
 				},"
    toolbar="#buyer-coupon-code-toolbar"
>
  <thead>
  <tr id="buyer-coupon-code-datagrid-tr">
    <th data-options="field:'ck',checkbox:true"></th>
    <!-- <th field="projectName" width="180" align="center">项目</th> -->
    <th field="certificateName" width="180" align="center" formatter="buyer_coupon_code_certificateName">适用证件</th>
    <th field="inviteCode" width="300" align="center">优惠码</th>
    <th field="useState" width="120" align="center" formatter="buyer_coupon_code_useState">使用状态</th>
    <th field="userName" width="120" align="center">优惠码使用会员</th>
    <th field="mobile" width="120" align="center">绑定手机</th>
    <th field="email" width="120" align="center">绑定邮箱</th>
    <th field="buyerId" width="120" align="center" formatter="buyer_coupon_code_buyerId">会员观众信息</th>
    <th field="inviteCodeId" width="120" align="center" formatter="buyer_coupon_code_inviteCodeId">操作</th>
  </tr>
  </thead>
</table>

<div id="buyer-coupon-code-toolbar">
  <div class="my-toolbar-button">
    <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true"
       onclick="reflush()">刷新</a>
    <a href="javascript:buyer_coupon_code_add()" class="easyui-linkbutton" iconCls="icon-add" plain="true">新增</a>
    <a href="javascript:buyer_coupon_code_del()" class="easyui-linkbutton" iconCls="icon-remove" plain="true">删除</a>
    <a href="javascript:$('#buyer-coupon-import').window('open')" class="easyui-linkbutton" iconCls="icon-import"
       plain="true">导入</a>
    <a href="javascript:buyer_coupon_code_search(!0)" class="easyui-linkbutton"
       iconCls="icon-view" plain="true">导出</a>
  </div>
  <div class="my-toolbar-search">
    <span style="display:inline-block;">项目：</span>
    <input class="easyui-combobox"
           id="buyer-coupon-code-keyword-projectId"
           style="width: 175px"/>&nbsp;&nbsp;
    <span style="display:inline-block;">适用证件：</span>
    <input class="easyui-combobox"
           data-options="panelHeight:'auto',panelMaxHeight:'200px'"
           id="buyer-coupon-code-keyword-certificateId"
           style="width: 175px;height:25px"/>&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">使用状态</span>
    <select id="buyer-coupon-code-keyword-useState" class="easyui-combobox" style="width:150px;"
            data-options="panelHeight:'auto'">
      <option selected value="">全部</option>
      <option value="0">未使用</option>
      <option value="1">已使用</option>
    </select>&nbsp;&nbsp;&nbsp;&nbsp;
    <span style="display:inline-block;margin-bottom: 18px">会员</span>
    <input id="buyer-coupon-code-keyword-userName" class="easyui-textbox">&nbsp;
    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px"
       onclick="buyer_coupon_code_search()">查询</a>
  </div>
</div>

<!--导入-->
<div id="buyer-coupon-import" class="easyui-window"
     data-options="closed:true,title:'导入',modal:true,maximizable: false,resizable: false,minimizable: false,cls:'__buyer-position-window'"
     style="width:300px;">
  <div class="easyui-panel" style="width:100%;height:160px;padding:10px;">
    <span class="channelButtonStyle" style='margin-top: 30px;'
          onclick="window.open('/eip-web-sponsor/document/优惠码导入模板.xlsx', '_blank')">下载导入模板</span>
    <span class="channelButtonStyle" onclick="$('#buyer-coupon-code-record-file').click()">导入</span>
    <input type="file" id="buyer-coupon-code-record-file" style="display: none"
           accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
  </div>
</div>

<!--设置基本信息-->
<div id="buyer-coupon-code-window"
     class="easyui-window"
     data-options="closed:true,title:'新增优惠码',shadow:false,modal:true,maximizable: false,resizable: false,minimizable: false"
     style="width:520px;">
  <div id="buyer-coupon-code-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-save"
         onclick="buyer_coupon_code_save()"
         plain="true"
         style="margin-right: 6px;">保存</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-cancel"
         onclick="$('#buyer-coupon-code-window').window('close')"
         plain="true"
         style="margin-right: 6px;">取消</a>
    </div>
  </div>
  <div class="easyui-panel" style="display: flex;justify-content: center;flex-direction: column;padding: 20px 10px;">
    <div style="margin-top: 10px;display: none;">
      <label style="width:120px;display: inline-block;text-align: right;">
        <font style="color: red;">*</font>项目名称
      </label>
      <input
          class="easyui-combobox"
          style="width:300px;height:25px"
          id="buyer-coupon-code-form-projectId"
          data-options="valueField:'projectId',textField:'projectName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>
    </div>
    <div style="margin-top: 15px;">
      <label style="width:120px;display: inline-block;text-align: right;">适用证件</label>
      <input
          class="easyui-combobox"
          style="width:300px;height:25px"
          data-options="panelHeight:'auto',panelMaxHeight:'200px',multiple:true,
          onChange(val) {
            if (!val.length) return
            const allSelected = val.slice(-1)[0] === ''
            if (allSelected) {
              $(this).combobox('setValues', [''])
            } else {
              $(this).combobox('setValues', val.filter(Boolean))
            }
          }"
          id="buyer-coupon-code-form-certificateList"/>
    </div>
    <div style="margin-top: 15px">
      <label style="width:120px;display: inline-block;text-align: right;">
        <font style="color: red;">*</font>优惠码
      </label>
      <input
          class="easyui-textbox"
          style="width:300px;height:25px"
          id="buyer-coupon-code-form-inviteCode"/>
    </div>
  </div>
</div>

<script>
  $(function () {
    setTimeout(() => {
      buyer_coupon_code_loadCertificate(['#buyer-coupon-code-keyword-certificateId'])
      buyer_coupon_code_search()
      buyer_coupon_code_load_project('buyer-coupon-code-keyword-projectId')
      buyer_coupon_code_load_project('buyer-coupon-code-form-projectId')
      $('#buyer-coupon-code-record-file').change(function () {
        if (!this.files.length) return
        buyer_coupon_code_import_file(this.files[0])
      })
    }, 1e2)
  });

  function buyer_coupon_code_loadCertificate(idList, extParams, callback) {
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/certificate/getCertificateAndProject',
      dataType: "json",
      data: {
        exhibitCode: exhibitCode_for_pass,
        ...extParams
      },
      success({data, state}) {
        if (state !== 1) return $.messager.alert('错误', '证件列表加载失败', 'error')
        data.unshift({longId: '', text: '全部证件'})
        data.forEach(item => {
          if (item.state === 1) item.text += '(已删除)'
        })
        idList.forEach(id => {
          $(id).combobox({
            valueField: 'longId',
            textField: 'text',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          }).combobox('loadData', data)
        })
        callback && callback(data)
      }
    })
  }

  function buyer_coupon_code_search(isExport) {
    const param = {
      exhibitCode: exhibitCode_for_pass,
      projectId: $('#buyer-coupon-code-keyword-projectId').combobox("getValue"),
      useState: $('#buyer-coupon-code-keyword-useState').combobox("getValue"),
      certificateId: $('#buyer-coupon-code-keyword-certificateId').textbox("getValue"),
      userName: $('#buyer-coupon-code-keyword-userName').textbox("getValue"),
    }
    Object.keys(param).forEach(key => '' === param[key] && (delete param[key]))
    if (isExport) {
      const target = new URL('/eip-web-sponsor/inviteCode/exportCouponCode', location.origin)
      target.search = new URLSearchParams(param).toString()
      return window.open(target, '_blank')
    }
    $('#buyer-coupon-code-datagrid').datagrid({
      url: eippath + '/inviteCode/getCouponCodePage',
      queryParams: param,
    })
  }

  function buyer_coupon_code_load_roles(id, projectId, onLoadSuccess) {
    const actorCode = 'Viewer'
    onLoadSuccess = onLoadSuccess || (Boolean)
    $("#" + id).combobox({
      url: '/RegSever/projRegSet/getRegTarget',
      queryParams: {projectId, actorCode},
      method: 'POST',
      loadFilter(data) {
        return data.data
      },
      onLoadSuccess() {
        onLoadSuccess && onLoadSuccess.call(this)
      }
    })
  }

  function buyer_coupon_code_load_project(id) {
    $('#' + id).combobox({
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode: exhibitCode_for_pass},
      method: 'POST',
      valueField: 'projectId',
      textField: 'projectName',
      panelHeight: 'auto',
      panelMaxHeight: '200px',
      limitToList: true,
    })
  }

  function buyer_coupon_code_useState(value) {
    if (value === null || value === void 0) return ''
    return value === 1 ? '已使用' : '未使用'
  }

  function buyer_coupon_code_buyerId(value, row) {
    if (!row.useState) return ''
    return `<a href="javascript:" onclick="buyer_coupon_code_goto_order_list(\${row.inviteCodeId})" style="color:blue;font-weight:bolder">查看</a>`
  }

  function buyer_coupon_code_inviteCodeId(value) {
    return `<a href="javascript:buyer_coupon_code_edit(\${value})" style="color:blue;font-weight:bolder">编辑</a>`
  }

  function buyer_coupon_code_certificateName(val, row) {
    const {certificateList} = row
    if (!Array.isArray(certificateList) || !certificateList.length) {
      return '全部证件'
    }
    return certificateList.map(item => {
      const {certificateDel} = item
      return +certificateDel === 1 ? `<del style="color: #A2A2A2;">\${item.certificateName}(已删除)</del>` : item.certificateName
    }).join('、')
  }

  function buyer_coupon_code_import_file(file) {
    if (!file) return
    $.messager.progress()
    const fd = new FormData()
    fd.append('exhibitCode', exhibitCode_for_pass)
    fd.append('serialNum', Date.now() + '-' + Math.random())
    fd.append('file', file)
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/inviteCode/importCouponCode',
      dataType: "json",
      contentType: false,
      processData: false,
      data: fd,
      success({state, msg}) {
        if (state !== 1) return $.messager.alert('错误', msg || '操作失败', 'error')
        buyer_coupon_code_search()
        $('#buyer-coupon-code-record-file').val('')
        $.messager.alert('提示', '操作成功', 'info')
        $.messager.progress('close')
        $('#buyer-coupon-import').window('close')
      },
      error(err) {
        console.warn(err)
        $.messager.alert('导入失败')
        $.messager.progress('close')
      }
    })
  }

  function buyer_coupon_code_add() {
    buyer_coupon_code_loadCertificate(['#buyer-coupon-code-form-certificateList'], {}, () => {
      $('#buyer-coupon-code-form-certificateList').combobox('setValues', [''])
    })
    $('#buyer-coupon-code-form-projectId').combobox('setValue', project_for_pass)
    $('#buyer-coupon-code-form-inviteCode').textbox('clear')
    $('#buyer-coupon-code-window').window({title: '新增优惠码'}).window('open')
  }

  function buyer_coupon_code_edit(inviteCodeId) {
    if (!inviteCodeId) return console.warn('数据异常')
    const getCouponCodeById = (callback) => {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/inviteCode/getCouponCodeById',
        data: {inviteCodeId},
        success({state, msg, data}) {
          if (state !== 1) return $.messager.alert('错误', msg || '数据加载失败', 'error')
          callback(data)
        },
        error() {
          $.messager.alert('错误', '数据加载失败', 'error')
        }
      })
    }
    getCouponCodeById(data => {
      const appointCertificateIds = (data.certificateList||[]).map(item => item.certificateId).filter(Boolean).toString()
      buyer_coupon_code_loadCertificate(['#buyer-coupon-code-form-certificateList'], {appointCertificateIds}, () => {
        $('#buyer-coupon-code-form-certificateList').combobox('setValues', appointCertificateIds.split(','))
      })
      $('#buyer-coupon-code-form-projectId').combobox('setValue', data.projectId)
      $('#buyer-coupon-code-form-inviteCode').textbox('setValue', data.inviteCode)
      $('#buyer-coupon-code-window').data({inviteCodeId}).window({title: '编辑优惠码'}).window('open')
    })
  }

  function buyer_coupon_code_save() {
    const formWindow = $('#buyer-coupon-code-window')
    const projectId = $('#buyer-coupon-code-form-projectId').combobox('getValue')
    const inviteCode = $('#buyer-coupon-code-form-inviteCode').textbox('getValue')
    const certificateList = $('#buyer-coupon-code-form-certificateList').combobox('getValues')
    // if (!projectId) return $.messager.alert('提示', '请选择项目', 'warning')
    if (!inviteCode) return $.messager.alert('提示', '请输入优惠码', 'warning')
    const inviteCodeId = formWindow.data('inviteCodeId') || ''
    formWindow.removeData('inviteCodeId')
    const certificateObj = Object.fromEntries(certificateList.map((id, idx) => [`certificateList[\${idx}].certificateId`, id]))

    function _request(post) {
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/inviteCode/saveCouponCode',
        dataType: "json",
        data: {
          exhibitCode: exhibitCode_for_pass,
          ...post,
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', data.msg || '保存失败！', 'error')
          $.messager.alert('提示', '保存成功！', 'info')
          formWindow.window('close').removeData('inviteCodeId')
          buyer_coupon_code_search()
        }
      }).done(() => $.messager.progress('close'))
    }

    _request({
      // projectId,
      inviteCode,
      inviteCodeId,
      ...certificateObj
    })
  }

  function buyer_coupon_code_del() {
    const rows = $('#buyer-coupon-code-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '请选择需要删除的数据', 'warning')
    $.messager.confirm('提示', `确定删除选择的\${rows.length}条数据吗？`, r => r && _request())

    function _request() {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/inviteCode/batchDelete',
        dataType: "json",
        data: {
          inviteCodeIds: rows.map(item => item.inviteCodeId).toString()
        },
        success(data) {
          if (data.state !== 1)
            return $.messager.alert('错误', '删除失败！', 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          buyer_coupon_code_search()
        }
      })
    }
  }

  function buyer_coupon_code_goto_order_list(inviteCodeId) {
    event.stopPropagation();
    const result = addTab('票证支付订单', '/eip-web-sponsor/backstage/spectator/certificate_order?inviteCodeId=' + inviteCodeId)
    // 已经存在是可以调用的
    if (result === '__EXISTS__') certificate_order_setProps(void 0, void 0, inviteCodeId)
  }
</script>
