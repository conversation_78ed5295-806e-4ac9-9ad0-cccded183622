<!-- PC官网模板设置 (行业模板3)-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="250522" />
    <!-- <meta http-equiv='x-dns-prefetch-control' content='on'>
    <link rel='dns-prefetch' href='//placehold.it'> -->
	<title>PC官网模板设置 - 行业模板3/5(海名)</title>
	<link rel="stylesheet" href="../../css/colpick.css" type="text/css" />
	<link rel="stylesheet" href="${eippath}/css/view/exhibitionTemplate5.css?${v}">
	<script>
        var eippath = '${eippath}';
        let url = (new URL(window.location.href)).searchParams;
        var exhibitCode = url.get('exhibitCode');
        var pid = url.get('pid');
    </script>
    <style>
        .pcLink-item, .pcBanner-item{
            margin-bottom: 15px;
        }
        #js-indexIntro-photoList>div:last-child {
            padding-bottom: 20px;
        }
        body .window-shadow {
            height: auto;
        }
    </style>
</head>
<body>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false">
		<div style="width:100%;height:100%;">
			<div style="position: sticky; top:0; z-index: 999">
				<div class="my-toolbar-button">
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="util.fresh()" plain="true">刷新</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="util.save()" plain="true">保存</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-link" onclick="openGatewayPc($('#jsf-tpl').combobox('getValue'))" plain="true">电脑版官网地址</a>
				</div>
			</div>
			<div class="template_set_pc" style="margin-top: 20px">
                <ul>
                    <li>
                        <span class='webSetStyle webSetStyleActive' onclick='util.setLang(1,this)'>中文版网站设置</span>
                        <span class='webSetStyle' onclick='util.setLang(0,this)'>英文版网站设置</span>
                    </li>
                </ul>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this)">
                        <b>基础设置 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 20px 0 0;">
                        <li style='display:flex;align-items: center;'>
                            <div>
                                <label style="width: 100%;">PC端官网首页模板</label>
                                <div style="width: 390px;">
                                    <!-- <label color="#333">专业版</label> -->
                                    <select id="jsf-tpl" class="easyui-combobox" style="width:200px;height:30px">
                                        <option value="PCTemplate5">专业版</option>
                                        <option value="PCTemplate6">专业版(海明)</option><!-- 海明2023 -->
                                    </select>
                                    <a href="javascript:;" class="easyui-linkbutton upBannerStyle" onclick="util.goStandard()">切换标准版</a>
                                </div>
                                <script>
                                    $(function() {
                                        $('#jsf-tpl').combobox({
                                            onChange(param,old) { // bug 第一次不触发
                                                afterSwithTpl(param)
                                            }
                                        });
                                    })
                                    function afterSwithTpl(param) {
                                        if(param == 'PCTemplate6') {
                                            $('.js-tpl2-only').removeClass('hide');
                                        }else{
                                            $('.js-tpl2-only').addClass('hide');
                                        }
                                    }
                                </script>
                            </div>
                            <div>
                                <label style="width: 100%;">子项目背景图裁切比例(未使用)</label>
                                <select id="jsf-bgRate" class="easyui-combobox" data-options="panelHeight:100" style="width:350px;height:30px">
                                    <option value="4/3" selected>4:3</option>
                                    <option value="9/5">9:5</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <label>PC端官网色调设置</label>
                            <div class="set-color-div">
                                <label style="color: #333;">主色调</label>
                                <i class="color-box" id='jsf-color0'
                                onclick='util.setColors(this,0)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">辅色1</label>
                                <i class="color-box" id='jsf-color1'
                                onclick='util.setColors(this,1)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">辅色2 </label>
                                <i class="color-box" id='jsf-color2'
                                onclick='util.setColors(this,2)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">点缀色</label>
                                <i class="color-box" id='jsf-color3'
                                onclick='util.setColors(this,3)'></i>
                            </div>
                        </li>
                        <!-- <li style="display: none;">
                            <label>注册会员调用观众登记
                                <input type="checkbox" id="websiteRegServerEnable">
                            </label>
                        </li> -->
                        <style>
                            .tpl-list-preview{ display:flex !important; }
                                .tpl-list-preview label{ width: 100%; }
                                .tpl-list-preview>div>div>div:hover,
                                .tpl-list-preview>div>div>div.hover{
                                    border-color: #95b8e7;
                                }
                                .tpl-list-preview>div>div{
                                    display:flex;
                                }
                                .tpl-list-preview>div>div>div{
                                    cursor: pointer;
                                    display:flex;align-items: center;justify-content: center;
                                    box-sizing: border-box;
                                    min-width: 93px;
                                    height: 93px;
                                    background: #fff;
                                    border: 1px solid #fff;
                                    border-radius: 4px;
                                    margin-right: 6px;
                                }
                                .tpl-list-preview img{ max-height: 79px; }
                        </style>
                        <li class="tpl-list-preview">
                            <div style="margin-right: 40px;width: 350px;">
                                <label>产品库样式<span class='showEg'>（英文版）</span></label>
                                <div id="tpl-list-preview-prod">
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-1.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-2.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-3.png" alt="" srcset=""></div>
                                </div>
                            </div>
                            <div>
                                <label>展商名录样式<span class='showEg'>（英文版）</span></label>
                                <div id="tpl-list-preview-corp">
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/corp-preview-1.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/corp-preview-2.png" alt="" srcset=""></div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this)">
                        <b>首页顶部设置 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 20px 0 0;">
                        <li style='display:flex;align-items: center;'>
                            <div>
                                <label style="width: 100%;">PC端欢迎词</label>
                                <input id="jsf-welcome" class="easyui-textbox" data-options="prompt: '如：欢迎来到XXX展会'" style="width:350px;height:30px">
                            </div>
                            <div>
                                <label style="width: 100%;">PC端网站标题</label>
                                <input id="jsf-title" class="easyui-textbox" data-options="prompt: '如：XXX展会'" style="width:350px;height:30px">
                            </div>
                        </li>
                        <li style="display: inline-block;">
                            <label>PC端官网顶部图片上传</label>
                            <div class="upFileDiv">
                                <input class="easyui-textbox" data-options="readonly:'true'" id="jsf-topImgName"
                                    style="width:350px;">
                                <i class="upFileDivButton">
                                    <em>上传</em>
                                    <input id="jsf-topImg" class="easyui-filebox upFileDivFileButton"
                                        data-options="buttonText:'选择',prompt:''">
                                </i>
                                <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('topImg')">
                                    <em>删除</em>
                                </i>
                                <p class="upFileDivP"><span>要求：</span><span style="margin-left: 24px;">jpg或jpeg格式，规格要求：1920*154，大小不超过150K</span>
                                </p>
                            </div>
                            <div class="upFileImgDiv" style="width: 620px;height: 50px;">
                                <img src="" alt="" id="jsf-topImgUrl">
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this)">
                        <b>导航栏设置 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 20px 0 0;">
                        <li style="margin-bottom: 0;padding-bottom: 5px;">
                            <label class="inline-block">
                                <input type="checkbox" id="jsf-cateEnable" value="1">
                                启用行业分类
                            </label>
                        </li>
                        <li style="margin-bottom: 0;padding-bottom: 0;">
                            <div id="bannerSetList" class="bannerSetList">
                            </div>
                        </li>
                        <li>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.bannerSetAdd()">添加更多导航栏</a>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <label></label>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <label class="module-titleLabel"><input type="checkbox" id="jsf-pcBanner-enable"><b> banner上传 <span class='showEg'>（英文版）</span></b></label>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: 0;">
                            <label>要求：banner高度最好控制在180-700之间，并 <span style="color:#FA6734">上传相应尺寸的banner图片</span>，如高度200，请上传1920*200的图片以避免样式错误。大小不超过300k。</label>
                        </li>
                        <li style="display: grid;grid-template-columns: 540px 1fr;">
                            <div>
                                <label style="display: block;">banner高度</label>
                                <input class="easyui-numberbox" id="jsf-pcBanner-height" data-options="min:180,max:700,prompt: '180-700'" style="width:350px;height:30px">
                            </div>
                            <div>
                                <label style="display: block;">内页banner高度</label>
                                <input class="easyui-numberbox" id="jsf-pcBanner-heightInner" data-options="min:180,max:700,prompt: '180-700'" style="width:350px;height:30px">
                            </div>
                        </li>
                        <li class="upBannerLiStyle">
                            <div id="pcBannerList">
                            </div>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.pcBannerAdd()">上传更多banner</a>
                        </li>
                        <li id="pcBanner-inner">
                            <div>
                                <label class="tip">导航栏内页banner上传图片</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox pcBanner-innerImgName"
                                        data-options="readonly:'true'" control='textbox' style="width:350px;">
                                    <i class="upFileDivButton">
                                        <em>上传</em>
                                        <input class="easyui-filebox upFileDivFileButton"
                                            name="upfile" data-options="buttonText:'选择',prompt:''">
                                    </i>
                                    <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('pcBanner-inner')">
                                        <em>删除</em>
                                    </i>
                                </div>
                            </div>
                            <div class="upFileImgDiv" style="width: 232px;height: 60px;">
                                <img src="" alt="" class="pcBanner-innerImgUrl">
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <label></label>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <label class="module-titleLabel"><input type="checkbox" id="jsf-jgArea-enable"><b> 金刚区菜单 <span class='showEg'>（英文版）</span></b></label>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: 0;padding-bottom: 0;">
                            <div id="jgAreaList" class="jgAreaList">
                            </div>
                        </li>
                        <li>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.jgAreaSet()">添加</a>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <b>首页内容板块 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                        </div>
                    <style>
                        #js-index-li{
                            display: flex;
                        }
                        #js-index-li label{
                            cursor: pointer;
                            margin: 0 8px 10px 1px;
                            color: #5567a5; /* #666 */
                        }
                        #js-index-li div{
                            display: inline-block;
                            order: 0;
                        }
                    </style>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: 0;padding-bottom: 0;" id="js-index-li">
                            <div>
                                <input type="checkbox" id="jsf-index-introduce">
                                <label onclick="util.indexIntroSet(this,true)">展会介绍</label>
                            </div>
                            <div>
                                <input type="checkbox" id="jsf-index-subProject">
                                <label onclick="util.indexIntroSet(this)">相关展会信息</label>
                            </div>
                            <div>
                                <input type="checkbox" id="jsf-index-product">
                                <label onclick="util.indexIntroSet(this)">推荐产品</label>
                            </div>
                            <div>
                                <input type="checkbox" id="jsf-index-merchant">
                                <label onclick="util.indexIntroSet(this)">推荐商户</label>
                            </div>
                            <div>
                                <input type="checkbox" id="jsf-index-apply">
                                <label onclick="util.indexIntroSet(this)">求购动态</label>
                            </div>
                            <div>
                                <input type="checkbox" id="jsf-index-news">
                                <label onclick="util.indexIntroSet(this)">新闻动态</label>
                            </div>
                            <div class="js-tpl2-only">
                                <input type="checkbox" id="jsf-index-photo">
                                <label onclick="util.indexIntroSet(this)">照片墙</label>
                            </div>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <label></label>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <label class="module-titleLabel"><input type="checkbox" id="jsf-pcLink-enable"><b> 友情链接 <span class='showEg'>（英文版）</span></b></label>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: 0;">
                            <label>要求：jpg或jpeg格式，规格要求：192*76，大小不超过50k，<span style="color:#FA6734">必须填写</span>友情链接名称及地址，否则保存不上</label>
                        </li>
                        <li class="upBannerLiStyle">
                            <div id="pcLinkList">
                            </div>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.pcLinkAdd()">添加更多友情链接</a>
                        </li>
                    </ul>
                </div>
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <b>底部板块 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: -30px;display: flex;">
                            <label style="width: 200px;margin-top: 12px">底部文字颜色设置</label>
                            <ul style="width: 200px;display:flex;" id="colorTitle">
                                <li class="selectColor"  style="margin-left: 0;">
                                    <label style="color: #333;display: inline-block;">标题颜色</label>
                                    <span class="selectColorspan activeBorder" style="margin-left: 14px;"><i
                                            style="background-color: #000;"></i></span>
                                    <span class="selectColorspan"><i style="background-color: #fff;"></i></span>
                                </li>
                                <!--li class="selectColor">
                                    <label style="color: #333;display: inline-block;">自定义颜色</label>
                                    <span class="selectColorspan" style="margin-left: 14px;position: relative;"><i
                                            class="color-box" style="background-color: #28ae39;"></i></span>
                                </li-->
                            </ul>
                            <ul style="display:flex;" id="colorMain">
                                <li class="selectColor"  style="margin-left: 0;">
                                    <label style="color: #333;display: inline-block;">正文颜色</label>
                                    <span class="selectColorspan activeBorder" style="margin-left: 14px;"><i
                                            style="background-color: #C3BDBE;"></i></span>
                                    <span class="selectColorspan"><i style="background-color: #ccc;"></i></span>
                                </li>
                                <!--li class="selectColor">
                                    <label style="color: #333;display: inline-block;">自定义颜色</label>
                                    <span class="selectColorspan" style="margin-left: 14px;position: relative;"><i
                                            class="color-box" style="background-color: #28ae39;"></i></span>
                                </li-->
                            </ul>
                        </li>
                        <li>
                            <div>
                                <label style="width: 100%;">底部标题名</label>
                                <input class="easyui-textbox" id="jsf-botTitle" data-options="prompt: '如：联系我们'" style="width:200px;height:30px">
                            </div>
                        </li>
                        <li>
                            <div>
                                <label style="width: 100%;">底部正文内容</label>
                                <input class="easyui-textbox" id="jsf-botContent" data-options="prompt: '如：电话号：0571-88888888\n  邮箱：<EMAIL>\n  北京xxxx展览有限公司\n  地址：北京市朝阳区朝阳门外XXX大厦XXX室',multiline: true" style="width:1200px;height:126px">
                            </div>
                        </li>
                        <li style="display: inline-flex;">
                            <div>
                                <label style="width: 100%;">PC端官网版权声明</label>
                                <input class="easyui-textbox" id="jsf-botCopyright" data-options="prompt: '如：Copyright©展之科技Allrightsreserved'" style="width:370px;height:30px">
                            </div>
                            <div>
                                <label style="width: 100%;">PC端官网工信备案</label>
                                <input class="easyui-textbox" id="jsf-botIcp" data-options="prompt: '如：浙ICP备xxxxxxxxxx号'" style="width:370px;height:30px">
                            </div>
                            <div>
                                <label style="width: 100%;">PC端官网公安备案</label>
                                <input class="easyui-textbox" id="jsf-botIsp" data-options="prompt: '如：浙公网安备 xxxxxxxxxxxxxx号'" style="width:370px;height:30px">
                            </div>
                        </li>
                        <li style="display: inline-block;">
                            <label>PC端官网底部图片上传</label>
                            <div class="upFileDiv">
                                <input class="easyui-textbox" data-options="readonly:'true'" id="jsf-botImgName"
                                    style="width:350px;">
                                <i class="upFileDivButton">
                                    <em>上传</em>
                                    <input id="jsf-botImg" class="easyui-filebox upFileDivFileButton"
                                        data-options="buttonText:'选择',prompt:''">
                                </i>
                                <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('botImg')">
                                    <em>删除</em>
                                </i>
                                <p class="upFileDivP">jpg或jpeg格式，规格要求：1920*370，大小不超过150k，底部72px需要留白显示PC端版权声明、工信备案、公安备案等内容</span>
                                </p>
                            </div>
                            <div class="upFileImgDiv" style="width: 620px;height: 82px;">
                                <img src="" alt="" id="jsf-botImgUrl">
                            </div>
                        </li>
                    </ul>
                </div>
			</div>
		</div>
	</div>
</div>

<!-- upload -->
<div>
 <input type="file" id="js-uploader" accept="image/*" style="display:none;" onchange="uploadFileChange(this)">
 <script>
    var g_uploadObj = ''; // 当前上传的jquery对象
    function uploadFileChange(obj) {
        const file = obj.files[0];
        obj.value = '';
        uploadFileImgObj(file, 'jsf-indexPhoto-', g_uploadObj);
    }
 </script>
</div>

<!-- loading -->
<div id="loading-over" class="over2"></div>
<div id="loading-layout" class="layout2"><img src="${eippath}/img/loading.gif" /></div>

<%@ include file="../common/project_widow_template5.jsp" %>

<%@ include file="../common/window_gatewayPc.jsp" %>

<script src="../../js/colpick.js"></script>
<script src="../../js/wangEditor.min.js"></script>
<script src="../../js/view/project_template5.js?${v}"></script>
</body>
</html>