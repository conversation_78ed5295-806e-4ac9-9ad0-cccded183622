<!-- 渠道基础表 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.List" %>
<%
  boolean fu_meeting_room = false, fu_certificate_management = false;
  List<String> funCodes = (List<String>) session.getAttribute("funCodes");
  for (String s : funCodes) {
    if (s.equals("fu_meeting_room")) fu_meeting_room = true;
    else if (s.equals("fu_certificate_management")) fu_certificate_management = true;
  }
%>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<%@ include file="../common/reg_url.jsp" %>
<link rel="stylesheet" href="${eippath}/css/view/alex-form.css?v=1.0">
<div class="easyui-layout" data-options="fit:true">
  <div data-options="region:'center',border:false">
    <!-- begin of toolbar -->
    <div id="checkpoint-set-toolbar">
      <div class="my-toolbar-button">
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="checkpoint_set_search()"
           plain="true">刷新</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="checkpoint_set_add()"
           plain="true">新增</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="checkpoint_set_edit()"
           plain="true">修改</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove"
           onclick="checkpoint_set_del_batch()"
           plain="true">删除</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-select11"
           onclick="checkpoint_set_checker_setting()"
           plain="true">核销人员设置</a>
      </div>
      <div class="my-toolbar-search">
        <span style="display:inline-block;margin-bottom: 18px">核销点类型</span>
        <select id="checkpoint-set-siteType" class="easyui-combobox" style="width:150px;height: 26px"
                data-options="panelHeight:'auto'">
          <option selected value="">全部</option>
          <option value="1">展会进场</option>

          <c:if test="<%=fu_meeting_room%>">
            <option value="2">会议签到</option>
          </c:if>
        </select>&nbsp;&nbsp;&nbsp;&nbsp;
        <label>核销点位置</label>&nbsp;
        <div style="position: relative;display: inline-block;margin-right: 10px;">
          <input
              id="checkpoint-set-sectionCode"
              class="easyui-combotree"
              data-options="panelHeight: 'auto',panelMaxHeight: '200px'"
              style="width:240px;height: 26px;"/>
          <a href="javascript:"
             onclick="$('#checkpoint-set-sectionCode').combotree('clear')"
             class="textbox-icon icon-close"
             style="width: 26px; height: 26px;position: absolute;right: 28px;">
          </a>
        </div>
        <label>可核销时间段</label>&nbsp;
        <input
            class="easyui-datetimebox"
            id="checkpoint-set-startTime"
            style="width:170px;height: 26px"
            data-options="
             prompt:'核销时间起',
             labelWidth: '120px',
             labelAlign: 'right'"/>
        <span>-</span>
        <input
            class="easyui-datetimebox"
            id="checkpoint-set-endTime"
            style="width:170px;height: 26px"
            data-options="
             prompt:'核销时间止',
             labelWidth: '0',"/>
        <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
           onclick="checkpoint_set_search()">查询</a>
      </div>
    </div>
    <!-- end of toolbar -->
    <table id="checkpoint-set-datagrid" class="easyui-datagrid" toolbar="#checkpoint-set-toolbar"
           data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);}">
      <thead>
      <tr>
        <th data-options="field:'ck',checkbox:true"></th>
        <th data-options="field:'checkPointName',width:65">核销点名称</th>
        <th data-options="field:'sectionName',width:65">核销点位置</th>
        <th data-options="field:'certName',width:85" formatter="checkpoint_set_certName">可核销证件</th>
        <th data-options="field:'employeeName',width:30" formatter="checkpoint_set_employeeName">核销人员</th>
        <th data-options="field:'setNumber',width:30">座位数</th>
        <th data-options="field:'startTime',width:100" formatter="checkpoint_set_timeRange">核销时间段</th>
        <th data-options="field:'checkNumber',width:40">每个证件可核销数</th>
        <th data-options="field:'currentLimit',width:40" formatter="checkpoint_set_Boolean2String">限流</th>
        <c:if test="<%=fu_certificate_management%>">
          <th data-options="field:'convertible',width:40" formatter="checkpoint_set_Boolean2String">可兑换</th>
        </c:if>
      </tr>
      </thead>
    </table>
  </div>
</div>
<!-- begin of easyui-window -->
<div id="checkpoint-set-window" class="easyui-window" toolbar="#checkpoint-set-window-toolbar"
     data-options="closed:true,title:'基本信息设置',modal:true,cls:'__checkpoint-set-window'" style="width:650px;height:auto;">
  <!-- begin of toolbar -->
  <div id="checkpoint-set-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:checkpoint_set_save()" class="easyui-linkbutton" iconCls="icon-save" plain="true">保存</a>
      <a href="javascript:$('#checkpoint-set-window').window('close')" class="easyui-linkbutton" iconCls="icon-cancel" plain="true">取消</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <div class="easyui-panel" title="基本信息设置" style="width: 100%;padding: 10px">
    <form class="alex-form" id="checkpoint-set-editor-form">
      <div class="alex-form-item full">
        <input
            class="easyui-textbox"
            id="checkpoint-set-editor-checkPointName"
            style="width: 520px"
            data-options="
             prompt:'核销点名称',
             labelWidth: '120px',
             labelAlign: 'right',
             label:'<span class=require-flag>*</span>核销点名称'"/>
      </div>
      <div class="alex-form-item full">
        <div style="line-height: 30px">
          <span style="display: inline-block;width: 130px;padding-right: 15px;text-align: right;box-sizing: border-box">
            <span class=require-flag>*</span>核销点类型</span>
          <div style="display:inline-flex;align-items:center;justify-content:space-between;width: 200px">
            <label>
              <input style="font-size: 16px;" type="radio" name="checkpoint-set-editor-siteType" value="1">
              <span>展会进场</span>
            </label>
            <c:if test="<%=fu_meeting_room%>">
              <label>
                <input type="radio" name="checkpoint-set-editor-siteType" value="2">
                <span>会议签到</span>
              </label>
            </c:if>
          </div>
        </div>
        <div style="padding-left: 130px">
          <input
              class="easyui-combobox"
              id="checkpoint-set-editor-sectionCode"
              style="width: 400px"
              data-options="prompt:'选择位置',panelHeight: 'auto', panelMaxHeight: '200px',"/>
        </div>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-combobox"
            id="checkpoint-set-editor-checkPointOperators"
            style="width: 520px"
            data-options="
             prompt:'限核销人员',
             labelWidth: '120px',
             labelAlign: 'right',
             valueField:'id',
             textField:'text',
             panelHeight:'200px',
             url:'${eippath}/operator/getAll',
             limitToList:true,
             multiple: true,
             label:'限核销人员'"/>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-combobox"
            id="checkpoint-set-editor-checkPointCerts"
            style="width: 530px"
            data-options="
             prompt:'可核销证件',
             labelWidth: '130px',
             labelAlign: 'right',
             multiple: true,
             panelHeight: 'auto',
             panelMaxHeight: '200px',
             label:'可核销证件'"/>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-datetimebox"
            id="checkpoint-set-editor-startTime"
            style="width: 315px"
            data-options="
             prompt:'核销时间起',
             labelWidth: '120px',
             labelAlign: 'right',
             label:'核销时间段'"/>
        <span>-</span>
        <input
            class="easyui-datetimebox"
            id="checkpoint-set-editor-endTime"
            style="width: 190px"
            data-options="
             prompt:'核销时间止',
             labelWidth: '0',"/>
      </div>
      <div class="alex-form-item full">
        <label class="checkbox-wrapper">
          <span class="checkbox-label">核销时间段控制</span>
          <input type="checkbox" id="checkpoint-set-editor-checkTimeControl">非核销时间段不可用</label>
      </div>
      <div class="alex-form-item full">
        <label class="checkbox-wrapper">
          <span class="checkbox-label" style="position:relative">
            <span
              id="checkpoint-set-currentLimit-tip"
              style="
                display: none;
                position: absolute;
                background-color: rgba(0,0,0,0.6);
                color: #fff;
                white-space: nowrap;
                padding: 10px;
                z-index: 999;
                top: -45px;
                left: 45px;">如核销证件存在预约时间，非预约时间段不允许入场</span>
            按预约限流
            <i
              class="tips-icon"
              style="margin: 0 5px;cursor: pointer"
              onmouseenter="$('#checkpoint-set-currentLimit-tip').stop(false, false).fadeIn(500)"
              onmouseleave="$('#checkpoint-set-currentLimit-tip').stop(false, false).fadeOut(500)"
            >i</i>
          </span>
          <input type="checkbox" id="checkpoint-set-editor-currentLimit"></label>
      </div>
      <div class="alex-form-item full">
        <input
            class="easyui-numberbox"
            id="checkpoint-set-editor-checkNumber"
            style="width: 520px"
            data-options="
             prompt:'每个证件可核销数',
             labelWidth: '120px',
             labelAlign: 'right',
             label:'每个证件可核销数'"/>
      </div>
      <div class="alex-form-item full">
        <div style="line-height: 30px">
          <span style="display: inline-block;width: 130px;padding-right: 15px;text-align: right;box-sizing: border-box">核销次数控制</span>
          <div style="display:inline-flex;align-items:center;justify-content:space-between;width: 320px">
            <label>
              <input checked style="font-size: 16px;" type="radio" name="checkpoint-set-editor-checkControl" value="1">
              <span>超核销次数仅提醒</span>
            </label>
            <label>
              <input type="radio" name="checkpoint-set-editor-checkControl" value="2">
              <span>超核销次数不让进入</span>
            </label>
          </div>
        </div>
      </div>
      <c:if test="<%=fu_certificate_management%>">
        <div class="alex-form-item full">
          <label class="checkbox-wrapper">
            <span class="checkbox-label">可兑换核销点</span>
            <input type="checkbox" id="checkpoint-set-editor-convertible"></label>
        </div>
      </c:if>
      <div class="alex-form-item full">
        <input
            class="easyui-numberbox"
            id="checkpoint-set-editor-setNumber"
            style="width: 520px"
            data-options="
             prompt:'座位数',
             labelWidth: '120px',
             labelAlign: 'right',
             label:'座位数'"/>
      </div>
    </form>
  </div>
</div>
<!-- end of easyui-window -->
<%--核销人员设置--%>
<div id="checkpoint-set-checker-window" class="easyui-window" toolbar="#checkpoint-set-checker-window-toolbar"
     data-options="closed:true,title:'核销人员设置',modal:true,cls:'__checkpoint-set-window'" style="width:650px;height:auto;">
  <!-- begin of toolbar -->
  <div id="checkpoint-set-checker-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:checkpoint_set_checker_select_open()" class="easyui-linkbutton" iconCls="icon-add" plain="true">选取</a>
      <a href="javascript:checkpoint_set_checker_delete()" class="easyui-linkbutton" iconCls="icon-remove" plain="true">删除</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <p style="padding: 5px 10px 10px;margin: 0;background: none;" id="checkpoint-set-checker-datagrid-bar">请选取当前展会所有可操作核销人员</p>
  <div class="easyui-panel" style="width: 100%;height: 320px;">
    <table id="checkpoint-set-checker-datagrid" class="easyui-datagrid"
           toolbar="#checkpoint-set-checker-datagrid-bar"
           data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);}">
      <thead>
      <tr>
        <th data-options="field:'ck',checkbox:true"></th>
        <th data-options="field:'operatorId',width:'30%'">操作员编号</th>
        <th data-options="field:'employeeName',width:'65%'">核销人员</th>
      </tr>
      </thead>
    </table>
  </div>
</div>


<div id="checkpoint-set-checker-select-window" class="easyui-window" toolbar="#checkpoint-set-checker-select-window-toolbar"
     data-options="closed:true,title:'选取核销人员',modal:true,cls:'__checkpoint-set-window'" style="width:650px;height:auto;">
  <!-- begin of toolbar -->
  <div id="checkpoint-set-checker-select-window-toolbar">
    <div class="my-toolbar-button" style="text-align: right;padding: 5px 15px;background: #f6f6f6;">
      <a href="javascript:checkpoint_set_checker_select_save()" class="easyui-linkbutton" iconCls="icon-save" plain="true">保存</a>
    </div>
    <div class="my-toolbar-search" style="margin-top: 10px;display: flex;align-items: center;border-top: 1px solid #ccc;padding: 15px 10px;">
      <label>职员名称</label>&nbsp;
      <div style="margin-right: 10px;">
        <input
            id="checkpoint-set-checker-select-employeeName"
            class="easyui-textbox"
            data-options=""
            style="width:240px;height: 26px;"/>
      </div>
      <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
         onclick="checkpoint_set_checker_select_load()">查询</a>
    </div>
  </div>
  <!-- end of toolbar -->
  <div class="easyui-panel" style="width: 100%;height: 320px;padding: 10px">
    <table id="checkpoint-set-checker-select-datagrid" class="easyui-datagrid"
           data-options="rownumbers:true,
           onDblClickRow: (_, row) => checkpoint_set_checker_select_save(row),
           singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++)if(i != rowIndex)$(this).datagrid('unselectRow', i);}">
      <thead>
      <tr>
        <th data-options="field:'ck',checkbox:true"></th>
        <th data-options="field:'operatorId',width:'31%'">操作员编号</th>
        <th data-options="field:'employeeName',width:'31%'">职员姓名</th>
        <th data-options="field:'operName',width:'32%'">登录名</th>
      </tr>
      </thead>
    </table>
  </div>
</div>
<script type="text/javascript">
  $(function () {
    $('.__checkpoint-set-window').remove()
    setTimeout(function () {
      checkpoint_set_search();
      checkpoint_load_sectionCode();
      $('#checkpoint-set-siteType').combobox({
        onChange (value) {
          $('#checkpoint-set-sectionCode').combotree('clear')
          if (+value === 2) return checkpoint_load_meeting_room(false)
          checkpoint_load_sectionCode(false)
        }
      })
      $('[name="checkpoint-set-editor-siteType"]').change(function () {
        $('#checkpoint-set-editor-sectionCode').combotree('clear')
        if (+$(this).val() === 2) return checkpoint_load_meeting_room(true)
        checkpoint_load_sectionCode(true)
      })
      $('#checkpoint-set-checker-select-employeeName').textbox('textbox').keydown(function (e) {
        e.keyCode === 13 && checkpoint_set_checker_select_load()
      });
    }, 100);
  });

  // 加载展馆展区
  function checkpoint_load_sectionCode(isEditor) {
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/exhibitSection/makeTreeJson',
      dataType: "json",
      async: false,
      data: {
        exhibitCode: exhibitCode_for_pass
      },
      success(data) {
        const loadSearchCtrl = () => {
          $('#checkpoint-set-sectionCode').combotree({
            data,
            valueField: 'sectionCode',
            textField: 'sectionName',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          })
        }
        const loadEditorCtrl = () => {
          $('#checkpoint-set-editor-sectionCode').combotree({
            data,
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          })
        }
        if (isEditor) return loadEditorCtrl()
        if (isEditor === false) return loadSearchCtrl()
        // if not provide isEditor, load both
        loadEditorCtrl()
        loadSearchCtrl()
      }
    })
  }

  // 加载展馆展区
  function checkpoint_load_meeting_room(isEditor) {
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/meetingRoom/getPage',
      dataType: "json",
      data: {
        exhibitCode: exhibitCode_for_pass
      },
      async: false,
      success(data) {
        const _data = data.rows.map(item => {
          const {meetingRoomCode, meetingRoomName} = item
          return {
            id: meetingRoomCode,
            text: meetingRoomName,
          }
        })
        const loadSearchCtrl = () => {
          $('#checkpoint-set-sectionCode').combotree({
            data: _data,
            valueField: 'sectionCode',
            textField: 'sectionName',
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          })
        }
        const loadEditorCtrl = () => {
          $('#checkpoint-set-editor-sectionCode').combotree({
            data: _data,
            panelHeight: 'auto',
            panelMaxHeight: "200px",
            limitToList: true
          })
        }
        if (isEditor) return loadEditorCtrl()
        if (isEditor === false) return loadSearchCtrl()
        // if not provide isEditor, load both
        loadEditorCtrl()
        loadSearchCtrl()
      }
    })
  }

  // 加载列表数据
  function checkpoint_set_search() {
    $('#checkpoint-set-datagrid').datagrid({
      url: '/eip-web-sponsor/checkPoint/getPage',
      queryParams: {
        exhibitCode: exhibitCode_for_pass,
        sectionCode: $('#checkpoint-set-sectionCode').combobox('getValue'),
        siteType: $('#checkpoint-set-siteType').combobox('getValue'),
        startTime: $('#checkpoint-set-startTime').datetimebox('getValue'),
        endTime: $('#checkpoint-set-endTime').datetimebox('getValue'),
      }
    });
  }

  // Boolean2String
  function checkpoint_set_Boolean2String(boolean) {
    return boolean ? '是' : '否'
  }

  // get time range from row
  function checkpoint_set_timeRange(val, row) {
    const {startTime, endTime} = row
    return [(startTime || '').replace(/-/g, '/'), (endTime || '').replace(/-/g, '/')].filter(Boolean).join(' - ')
  }
  // employeeName formatter
  function checkpoint_set_employeeName(val, row) {
    return val || '<span style="opacity: 0.4">预设核销人员</span>'
  }

  // certName formatter
  function checkpoint_set_certName(val, row) {
    const {checkPointCerts} = row
    if (!Array.isArray(checkPointCerts) || !checkPointCerts.length) {
      return '<span style="opacity: 0.4">可核销本届展会所有证件</span>'
    }
    return checkPointCerts.map(item => {
      const {certificateDel} = item
      return +certificateDel === 1 ? `<del style="color: #A2A2A2;">\${item.certName}(已删除)</del>` : item.certName
    }).join('、')
  }

  // 加载可核销证件下拉框数据
  function checkpoint_load_certName(appointCertificateIds='') {
    return $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/certificate/getCertificateAndProject',
      dataType: "json",
      data: {
        exhibitCode: exhibitCode_for_pass,
        appointCertificateIds
      },
      success({data, state}) {
        if (state !== 1) return $.messager.alert('错误', '核销点加载失败！', 'error')
        data.forEach(item => {
          if (item.state === 1) item.text += '(已删除)'
        })
        $('#checkpoint-set-editor-checkPointCerts').combobox({
          valueField: 'code',
          textField: 'text',
          panelHeight: 'auto',
          panelMaxHeight: "200px",
          limitToList: true
        }).combobox('loadData', data)
      }
    })
  }

  // forEach form keys
  function checkpoint_set_formKeysForEach(callback) {
    if (typeof callback !== 'function') throw TypeError('callback require type is function')
    const prefix = 'checkpoint-set-editor-'
    const editorInputs = $(`#checkpoint-set-editor-form [id^="\${prefix}"]`)
    Array.prototype.forEach.call(editorInputs, (item, index) => callback(item.id.replace(prefix, ''), $(item), index))
  }

  // 新增核销点
  function checkpoint_set_add() {
    checkpoint_load_certName()
    checkpoint_set_fullForm()
    $('#checkpoint-set-window').removeAttr('checkPointId').window('open')
  }

  // 保存核销点
  function checkpoint_set_save() {
    const $window = $('#checkpoint-set-window')
    const result = {
      checkPointId: $window.attr('checkPointId'),
      exhibitCode: exhibitCode_for_pass,
      siteType: $('input[name="checkpoint-set-editor-siteType"]:checked').val(),
      checkControl: $('input[name="checkpoint-set-editor-checkControl"]:checked').val(),
    }
    checkpoint_set_formKeysForEach((key, $el) => {
      switch (key) {
        case 'checkPointName':
          result[key] = $el.textbox('getValue')
          break
        case 'sectionCode':
          result[key] = $el.combotree('getValue')
          break
        case 'startTime':
        case 'endTime':
          result[key] = $el.datetimebox('getValue')
          break
        case 'checkPointCerts':
        case 'checkPointOperators':
          result[key] = $el.combobox('getValues')
          break
        case 'setNumber':
        case 'checkNumber':
          result[key] = $el.numberbox('getValue')
          break
        case 'currentLimit':
        case 'convertible':
        case 'checkTimeControl':
          result[key] = $el.prop('checked')
          break
      }
    })
    if (!result['checkPointName'])
      return $.messager.alert('错误', '核销点名称不能为空！', 'error')
    if (!result['sectionCode'])
      return $.messager.alert('错误', '核销位置不能为空！', 'error')
    result.checkPointOperators = result.checkPointOperators.map(operatorId => ({operatorId}))
    result.checkPointCerts = result.checkPointCerts.map(code => {
      return {
        certType: code.charAt(0),
        certId: code.slice(1)
      }
    })
    $.messager.progress()
    $.ajax({
      type: 'post',
      url: '/eip-web-sponsor/checkPoint/save',
      data: JSON.stringify(result),
      dataType: 'json',
      contentType: "application/json",
      success({state}) {
        try {
          if (state !== 1)
            return $.messager.alert('错误', '保存失败！', 'error')
          checkpoint_set_search()
          $.messager.alert('提示', '保存成功！', 'info')
          $window.removeAttr('checkPointId').window('close')
        } catch (e) {
          console.warn(e)
        } finally {
          $.messager.progress('close')
        }
      },
      error() {
        $.messager.alert('错误', '保存失败！', 'error')
        $.messager.progress('close')
      }
    })
  }

  // 填充表单数据
  function checkpoint_set_fullForm(data) {
    data = data || {}
    $('input[name="checkpoint-set-editor-siteType"]').eq(Number(data.siteType === 2)).prop('checked', true).change()
    $('input[name="checkpoint-set-editor-checkControl"]').eq(Number(data.checkControl === 2)).prop('checked', true)
    // nextTick
    checkpoint_set_formKeysForEach((key, $el) => {
      const value = data[key] || ''
      switch (key) {
        case 'checkPointName':
          $el.textbox('setValue', value)
          break
        case 'sectionCode':
          $el.combotree('setValue', value)
          break
        case 'startTime':
        case 'endTime':
          $el.datetimebox('setValue', value)
          break
        case 'checkPointCerts':
          $el.combobox('setValues', (value || []).map(item => item['certTypeAndId']))
          break
        case 'checkPointOperators':
          $el.combobox('setValues', (value || []).map(item => item['operatorId']))
          break
        case 'setNumber':
        case 'checkNumber':
          $el.numberbox('setValue', value)
          break
        case 'currentLimit':
        case 'convertible':
        case 'checkTimeControl':
          $el.prop('checked', !!value)
          break
      }
    })
  }

  // 编辑核销点
  function checkpoint_set_edit() {
    const rows = $('#checkpoint-set-datagrid').datagrid('getSelections')
    if (rows.length === 0) return $.messager.alert('提示', '未选中内容！', 'warning')
    if (rows.length > 1) return $.messager.alert('提示', '单次只能修改一条数据！', 'warning')
    const {checkPointId, checkPointCerts} = rows[0]
    const $window = $('#checkpoint-set-window')
    const getAppointCertificateIds = () => {
      if (!checkPointCerts || !checkPointCerts.length) return ''
      return checkPointCerts.map(item => item.certId).filter(Boolean).toString()
    }
    $.messager.progress()
    checkpoint_load_certName(getAppointCertificateIds()).done(() => {
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/getMoreById',
        data: {checkPointId},
        dataType: 'json',
        success({state, data}) {
          try {
            if (state !== 1)
              return $.messager.alert('错误', '数据加载失败！', 'error')
            checkpoint_set_fullForm(data)
            $window.attr({checkPointId}).window('open')
          } catch (e) {
            console.warn(e)
          } finally {
            $.messager.progress('close')
          }
        },
        error() {
          $.messager.alert('错误', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    })
  }

  // 删除核销点
  function checkpoint_set_del_batch() {
    const rows = $('#checkpoint-set-datagrid').datagrid('getSelections')
    if (rows.length === 0) return $.messager.alert('提示', '未选中内容！', 'warning')
    const innerRequest = r => {
      if (!r) return
      const checkPointIds = rows.map(item => item.checkPointId).toString()
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/batchDelete',
        data: {checkPointIds},
        dataType: 'json',
        success({state}) {
          try {
            if (state !== 1)
              return $.messager.alert('错误', '删除失败！', 'error')
            checkpoint_set_search()
            $.messager.alert('提示', '删除成功！', 'info')
          } catch (e) {
            console.warn(e)
          } finally {
            $.messager.progress('close')
          }
        },
        error() {
          $.messager.alert('错误', '删除失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    $.messager.confirm('提示', '确定删除选中的' + rows.length + '条数据吗？', innerRequest)
  }

  // 核销人员设置
  function checkpoint_set_checker_setting() {
    checkpoint_set_checker_load()
    $('#checkpoint-set-checker-window').window('open')
  }

  function checkpoint_set_checker_load() {
    $('#checkpoint-set-checker-datagrid').datagrid({
      url: '/eip-web-sponsor/checkPoint/getDefaultOperatorPage',
      queryParams: {
        exhibitCode: exhibitCode_for_pass,
      }
    })
  }

  function checkpoint_set_checker_delete() {
    const rows = $('#checkpoint-set-checker-datagrid').datagrid('getSelections')
    if (rows.length === 0) return $.messager.alert('提示', '未选中内容！', 'warning')
    const innerRequest = r => {
      if (!r) return
      const checkPointOperatorIds = rows.map(item => item['checkPointOperatorId']).toString()
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/deleteDefaultOperator',
        data: {checkPointOperatorIds},
        dataType: 'json',
        success({state}) {
          try {
            if (state !== 1)
              return $.messager.alert('错误', '删除失败！', 'error')
            checkpoint_set_checker_load()
            $.messager.alert('提示', '删除成功！', 'info')
          } catch (e) {
            console.warn(e)
          } finally {
            $.messager.progress('close')
          }
        },
        error() {
          $.messager.alert('错误', '删除失败！', 'error')
          $.messager.progress('close')
        }
      })
    }
    $.messager.confirm('提示', '确定删除选中的' + rows.length + '条数据吗？', innerRequest)
  }
  // 选择核销人员
  function checkpoint_set_checker_select_open() {
    checkpoint_set_checker_select_load()
    $('#checkpoint-set-checker-select-window').window('open')
  }

  function checkpoint_set_checker_select_load() {
    $('#checkpoint-set-checker-select-datagrid').datagrid({
      url: '/eip-web-sponsor/operator/getPage',
      queryParams: {
        notCheckOperatorExhibitCode: exhibitCode_for_pass,
        employeeName: $('#checkpoint-set-checker-select-employeeName').textbox('getValue')
      }
    })
  }

  function checkpoint_set_checker_select_save(row) {
    const _request = rows => {
      $.messager.progress()
      $.ajax({
        type: 'post',
        url: '/eip-web-sponsor/checkPoint/addDefaultOperator',
        data: JSON.stringify({
          exhibitCode: exhibitCode_for_pass,
          checkPointOperators: rows.map(item => ({operatorId: item.operatorId}))
        }),
        contentType: 'application/json',
        dataType: 'json',
        success({state}) {
          try {
            if (state !== 1) return $.messager.alert('错误', '保存失败！', 'error')
            checkpoint_set_checker_load()
            $('#checkpoint-set-checker-select-window').window('close')
          } catch (e) {
            $.messager.alert('错误', '保存失败！', 'error')
            console.warn(e)
          } finally {
            $.messager.progress('close')
          }
        },
        error() {
          $.messager.alert('错误', '数据加载失败！', 'error')
          $.messager.progress('close')
        }
      })
    }

    // 双击直接保存
    if (row) return _request([row])
    const rows = $('#checkpoint-set-checker-select-datagrid').datagrid('getSelections')
    if (rows.length === 0) return $.messager.alert('提示', '未选中内容！', 'warning')

    _request(rows)
  }
</script>
