<!-- 活动推广设置 : 文件拆分 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="2022030711"/>
<!-- <meta http-equiv='x-dns-prefetch-control' content='on'>
<link rel='dns-prefetch' href='//placehold.it'> -->
<title>活动推广设置</title>
<meta name="keywords" content="邀请函模板设置,展商邀请排行榜设置,观众登记地址,邀请函海报设置" />
<link rel="stylesheet" href="../../css/colpick.css" type="text/css"/>
<link rel="stylesheet" href="../../css/view/insert_input.css" type="text/css"/>
<script src="../../js/colpick.js"></script>
<script src="../../js/wangEditor.min.js"></script>
<script>var eippath = '${eippath}';</script>
<style>
    .tips-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: rgb(63,164,255);
        background-color: rgb(230,247,255);
        font-style: normal;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        font-weight: bold;
        margin-right: 5px;
    }
    .projectSettings {
        width: 100%;
    }

    .picker {
        position: absolute;
        top: 0px;
    }

    ul, li {
        list-style: none;
    }

    p {
        margin: 0;
        padding: 0;
    }

    .project-info1 > ul > li {
        margin-top: 16px;
        box-sizing: border-box;
        padding-left: 50px;
    }

    label {
        margin-bottom: 10px;
        display: inline-block;
    }

    .ke-container, .ke-container-default {
        height: 256px;
        width: 80% !important;
    }

    .ke-edit {
        height: 230px !important;
    }

    .datagrid-toolbar {
        background: #F4F4F4;
        /*  position: fixed;  */
        /*   width: 100%; */
    }

    .project-info1 {
        margin-top: 35px;
    }

    .project-info-img {
        width: 79%;
        margin-left: 40px;
    }

    .project-info-img img {
        width: 150px;
        height: 150px;
        overflow: hidden;
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }

    .ke-edit-iframe {
        height: 100% !important;
    }

    .product-img {
        position: relative;
        width: 320px;
        height: 224px;
        background-color: #ffffff;
        float: left;
        margin-right: 20px;
        margin-bottom: 20px;
        display: none;
    }

    .product-img img {
        display: block;
        width: 320px;
        height: 168px;
        border: solid 1px #cccccc;
    }

    .ke-content {
        color: #999;
    }

    .template_set {
        box-sizing: border-box;
        padding-left: 10px;
        margin-top: 20px;
    }

    .template_set_pc h6 {
        font-size: 15px;
        color: #333;
        font-weight: normal;
    }

    .template_set_pc ul li {
        display: block;
        box-sizing: border-box;
        margin: 0 50px 10px 50px;
        padding-bottom: 14px;
        overflow: hidden;
    }

    .template_set_pc ul li > label {
        display: block;
    }

    .template_set_pc {
        overflow: hidden;
        margin-bottom: 10px;
    }

    .my-toolbar-button {
        background-color: #f5f5f5;
        padding: 6px 5px;
    }

    .upFileDivButton {
        font-style: normal;
        margin: 0 20px 0 10px;
        display: inline-block;
        position: relative;
    }

    .upFileDivFileButton {
        width: 60px;
    }

    .upFileDivButton > span {
        opacity: 0;
        position: absolute;
        left: 0;
    }

    .upFileDivP {
        display: inline-block;
    }

    .upFileImgDiv {
        width: 80px;
        height: 80px;
        border: solid 1px #cccccc;
        margin-top: 10px;
    }

    .upFileDivButton > em {
        width: 64px;
        height: 30px;
        background-color: #ffffff;
        border-radius: 4px;
        border: solid 1px #b2b2b2;
        color: #333333;
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        font-style: normal;
        display: inline-block;
        cursor: pointer;
    }

    .selectColor > span {
        display: inline-block;
        border-radius: 4px;
        box-sizing: border-box;
        padding: 6px;
        cursor: pointer;
    }

    .selectColorspan {
        border: 1px solid transparent;
    }

    .selectColor > span > i {
        width: 18px;
        height: 18px;
        border-radius: 4px;
        display: inline-block;
        vertical-align: bottom;
    }

    .selectColor {
        display: inline-block;
    }

    .activeBorder {
        border: solid 1px #95b8e7;
    }

    .upFileImgDiv2 {
        width: 300px;
        height: 94px;
        border: solid 1px #cccccc;
        margin-top: 10px;
        margin-bottom: 45px;
    }

    .upFileImgDiv > img, .upFileImgDiv2 > img {
        width: 100%;
        height: 100%;
        display: block;
        overflow: hidden;
    }

    .upBannerStyle {
        margin-top: 16px;
        height: 30px;
        background-image: linear-gradient(0deg, #efefef 0%, #fefefe 100%);
        border-radius: 4px;
        border: solid 1px #95b8e7;
        color: #5567a5;
        font-size: 14px;
        padding: 0 10px;
        outline: none;
        cursor: pointer;
    }

    .upBannerLiStyle {
        border: dotted 1px #cccccc;
        padding: 10px 0px 10px 20px !important;
        margin: 0 50px;
        margin-bottom: 10px;
    }

    .delBannerPc, .hideLink {
        display: none !important;
    }

    .delBannerMb {
        display: none !important;
    }

    .pcHide, .showLink {
        display: block !important;
    }

    .mbHide {
        display: block !important;
    }

    .webSetStyle {
        width: 172px;
        height: 34px;
        background-color: #ffffff;
        border-radius: 4px;
        border: solid 1px #999999;
        display: inline-block;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        color: #666;
        margin-right: 24px;
        cursor: pointer;
    }

    .webSetStyleActive {
        background-color: #f2f5ff;
        border: solid 1px #5567a5;
        color: #5567a5;
    }

    .set-color-div {
        float: left;
        margin-right: 20px;
        margin-bottom: 10px;
    }

    .set-color-div > label {
        margin: 0 16px;
    }

    .set-color-div > i {
        vertical-align: bottom;
        width: 18px;
        height: 18px;
        border-radius: 4px;
        display: inline-block;
        cursor: pointer;
        border: 1px solid #ccc;
    }

    .isShowLinkDiv {
        border: dotted 1px #cccccc;
        padding: 10px 20px !important;
    }

    .showEg {
        display: none;
    }

</style>
<style>
    .textbox .textbox-button-right {
        width: 100%;
    }

    #links-container, #footerLinkList {
        border: dotted 1px #cccccc;
    }

    #links-container .links-item, #footerLinkList .link-item {
        margin: 0 0 10px;
        padding: 10px;
    }

    #footerLinkList .link-item {
        display: flex;
    }

    #footerLinkList .link-item > div:nth-of-type(1) {
        margin-right: 40px;
    }

    #links-container .links-item .up > div {
        margin-right: 30px;
        float: left;
    }

    #links-container .links-item .up > div:last-of-type {
        margin-right: 0;
        float: none;
    }

    #links-container .links-item .links-item-name {
        transform: translateY(3px);
    }

    #links-container .links-item .delete {
        margin-right: 0;
    }

    #links-container .links-item .clear {
        margin-left: -12px;
        margin-right: 0;
    }

    #links-container .links-item .down {
        display: flex;
    }

    #links-container .links-item .down > div {
        flex-basis: 485px;
        border: none;
    }

    #links-container .links-item .down > div > img {
        width: 191px;
        height: 76px;
    }

    #links-container .links-item .down > p {
        margin-top: 30px;
    }
</style>
<style>
    .label-inner {
        margin-bottom: 0;
        vertical-align: top;
        cursor: pointer;
        margin-right: 6px;
    }

    .label-inner:hover {
        text-decoration: underline;
    }

    .line {
        width: auto;
        height: 1px;
        margin: 20px 50px;
        background-color: #ccc;
    }

    .upFileDivP {
        color: #666;
    }

    .c999{
        color: #999;
    }
    .poster-upload__desc {
        margin: 10px 0 20px;
    }
    .poster-upload__desc span:not(.tips-icon) {
        color: rgb(244, 48, 48);
    }
</style>
<!--引入引入kindeditor编辑器相关文件-->
<script charset="utf-8" src="${eippath}/kindeditor/kindeditor-all.js"></script>
<script charset="utf-8" src="${eippath}/kindeditor/lang/zh-CN.js"></script>
<style>
    ::-webkit-scrollbar-track-piece {
        background-color: #f8f8f8
    }

    ::-webkit-scrollbar {
        width: 6px;
        height: 6px
    }

    ::-webkit-scrollbar-thumb {
        background-color: #dddddd;
        background-clip: padding-box;
        min-height: 28px;
        border-radius: 5px
    }

    ::-webkit-scrollbar-thumb:hover {
        background-color: #bbb
    }
    body {
        margin: 0
    }

    #tab-panel {
        width: 100%;
        height: 100vh;
        box-sizing: border-box;
    }

    #poster-config-list{
        width: 783px;
        padding: 10px;
        border-right: 1px #999;
        border-right-style: dashed;
        margin-right: 40px;
        padding-right: 40px;
        overflow-y: auto;
    }
    .poster-field-position {
        display: flex;
        width: 300px;
        justify-content: space-between
    }
    .poster-field-position > label {
        display: inline-flex;
        align-items: center;
    }
    .poster-field-position > label > input {
        margin: 0 4px 0 0;
    }
    .poster-field-style {
        display: flex;
        width: 600px;
        justify-content: space-between;
    }
    .pfs-item{
        display: flex;
        flex-direction: column;
        width: 140px;
    }
    .color-selector{
        width: 30px;
        height: 30px;
        padding: 4px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        display: inline-block;
        cursor: pointer;
        box-sizing: border-box;
        background-clip: content-box;
        position: relative;
    }

    .color-selector::before {
        --size: 4px;
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        border: var(--size) solid transparent;
        border-top-color: #fff;
        transform: translate(-50%, calc(-50% + calc(var(--size) / 2)));
    }
    .colpick{
        z-index: 20000;
    }

    .image-preview{
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 99999;
    }
    .image-preview .mask {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0,0,0, 0.6);
    }
    .image-preview .img-preview {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 768px;
        max-height: 510px;
    }
    .image-preview .img-preview > img {
        width: 100%;
        height: 100%;
        max-width: 768px;
        max-height: 510px;
    }
    .image-preview .img-preview span {
        width: 275px;
        height: 50px;
        background: #fff;
        border-radius: 25px;
        line-height: 50px;
        position: absolute;
        left: 50%;
        bottom: -80px;
        transform: translateX(-50%);
        color: #404145;
        text-align: center;
        cursor: pointer;
    }
</style>
<style>
    .links-item-1 {
        color: #333;
    }

    .hide {
        display: none !important;
    }

    .links-item-1 > div {
        display: inline-block;
    }

    .links-item-1 .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
    }

    .links-item-1 .green {
        background: #007b42;
    }

    .links-item-1 .red {
        background: #f3ba1d;
    }
</style>

<style>
  .css-btn+.css-btn{
    margin-left: 8px;
  }
  .css-btn{
    height: 30px;
    background-image: -webkit-gradient(linear, left bottom, left top, from(#efefef), to(#fefefe));
    background-image: linear-gradient(0deg, #efefef 0%, #fefefe 100%);
    border-radius: 4px;
    border: solid 1px #95b8e7;
    color: #5567a5;
    font-size: 14px;
    padding: 0 10px;
    outline: none;
    box-sizing: border-box;
    cursor: pointer;
  }
</style>
</head>
<body>

<!-- <div class="easyui-tabs" id="tab-panel" data-options="tabPosition:'top'"> -->
<div class="main" id="tab-panel">
  <!-- <div title="邀请函模板设置" style="width:100%;"> -->
  <div id="page-main" class="hide">
    <div style="position:sticky;top:0;z-index:999">
      <div class="my-toolbar-button">
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="location.reload()"
           plain="true">刷新</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="seveTemp4()" plain="true">保存</a>
        <!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-gene" onclick="invitation_batch_gen()" plain="true">批量生成邀请函</a> -->
      </div>
    </div>
    <div class="project-info1" style="height: calc(100% - 42px);overflow-y: auto;margin-top: 0">
      <ul>
        <li class="hide">
          <label>移动端邀请函模板:</label>
          <div class="">
            <input id="projectInfo_invitation" class="easyui-combobox" style="width:350px;height:25px"
              value="invitation-letter-2024" data-options="
              valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList: true,
              data:[
                {id:'invitation-letter-standard',text:'标准模板'},
                {id:'invitation-letter-standard-old',text:'点赞互动模板'},
                {id:'invitation-letter-2024',text:'标准模板2024'}
              ]">
          </div>
        </li>
        <li>
          <label>移动端邀请函顶部背景图</label>
          <div class="upFileDiv">
            <input class="easyui-textbox" id="upTempBgiName2" style="width:350px;">
            <i class="upFileDivButton">
              <em>上传</em>
              <input id="upTempBgi2" class="easyui-filebox upFileDivFileButton" name="upfile"
                     data-options="buttonText:'选择',prompt:''">
            </i>
            <p class="upFileDivP"><span>规格要求：750*890</span><span style="margin-left: 24px;">建议小于200K</span>
            </p>
          </div>
          <div class="upFileImgDiv2 hide" style="width: 286px;height: 338px;margin-bottom: 15px;">
            <img src="" alt="" id="upTempBgiImg2">
          </div>
        </li>
        <li>
          <label>移动端邀请函底部背景图</label>
          <div class="upFileDiv">
            <input class="easyui-textbox" id="upTempBgiName3" style="width:350px;">
            <i class="upFileDivButton">
              <em>上传</em>
              <input id="upTempBgi3" class="easyui-filebox upFileDivFileButton" name="upfile"
                     data-options="buttonText:'选择',prompt:''">
            </i>
            <p class="upFileDivP"><span>规格要求：750像素宽度图片</span><span style="margin-left: 24px;">建议小于300K</span>
            </p>
          </div>
          <div class="upFileImgDiv2 hide" style="width: 286px;height: 276px;margin-bottom: 15px;">
            <img src="" alt="" id="upTempBgiImg3">
          </div>
        </li>
        <li>
          <label>
            底部按钮链接设置
          </label>
          <ul id="footerLinkList">
            <li class="link-item-" style="padding: 20px;display: flex;">
              <div id="btnList" class="btnList">
              </div>
              <a href="#" class="easyui-linkbutton css-btn" onclick="util.btnSet()">添加按钮</a>
              <a href="#" onclick="openPosterSetting()" class="easyui-linkbutton css-btn">海报设置</a>
            </li>
            <script>
              function openPosterSetting() {
                $('.li-qr').parent().show();
                const btns = util.buildBtnData().filter(it => it.type == 'reg');
                // const tmp  = btns.map(it=> it.reg);
                btns.map((it,idx) => {
                  // TODO 强制指向 新的观众登记
                  $('#li-qr' + (idx+1)).find('span').html(it.name || ('观众登记_' + (idx+1)));
                  // .data('reg', it.reg);
                })
                if(btns.length < 1) {
                  $('#li-qr1').parent().hide().end().find('input').prop('checked', false);
                }
                if(btns.length < 2) {
                  $('#li-qr2').parent().hide().end().find('input').prop('checked', false);
                }
                if(btns.length < 3) {
                  $('#li-qr3').parent().hide().end().find('input').prop('checked', false);
                }
                if(btns.length < 4) {
                  $('#li-qr4').parent().hide().end().find('input').prop('checked', false);
                }
                setPreviewConfig() || $('#poster-config-window').window('open');
              }
            </script>
            <!-- 已废弃 -->
            <li class="link-item hide">
              <div>
                <label>预设按钮</label>
                <div class="links-item-1">
                  <div style="color: #999;margin-right: 10px;">观众登记链接</div>
                  <div style="width: 60px;overflow: hidden;">
                    <div class="hide">
                      <span class="dot green"></span>已发布
                    </div>
                    <div class="hide">
                      <span class="dot red"></span>未发布
                    </div>
                  </div>
                  <label style="display: inline-flex;align-items: center;vertical-align: text-top;margin-left: 40px;">
                    <input type="checkbox" onchange="checkEnable(this);" id="enableRegister"/>启用</label>
                </div>
                <div class="links-item-1">
                  <div style="color: #999;margin-right: 20px;">海报分享</div>
                  <label style="display: inline-flex;align-items: center;vertical-align: text-top;margin-left: 126px;">
                    <input type="checkbox" onchange="checkEnable(this);" id="enablePosterFlag"/>启用
                  </label>
                  <a style="margin-left: 10px"  href="javascript:setPreviewConfig()||$('#poster-config-window').window('open')" class="easyui-linkbutton">海报设置</a>
                </div>
              </div>
            </li>
            <!--<li style="padding: 10px;display: none;" id="posterBottomWrap">
              <label>移动端邀请函海报分享底部背景图</label>
              <div class="upFileDiv">
                <input class="easyui-textbox" id="posterBottomBgImg" style="width:350px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="posterBottomBgImgFb" class="easyui-filebox upFileDivFileButton" name="upfile"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <p class="upFileDivP"><span>规格要求：750*1400px,左右留白48px，底部留白100px</span><span
                    style="margin-left: 24px;">建议小于400K</span></p>
              </div>
              <div class="upFileImgDiv2" style="width: 286px;height: 533px;margin-bottom: 10px;">
                <img src="/eip-web-sponsor/img/invitation/post-placeholder.png" alt="" id="posterBottomBgImgUrl">
              </div>
            </li>-- >
            <li class="link-item">
                <div>
                    <label>名称1</label>
                    <div class="upFileDiv">
                        <input class="easyui-textbox" id="footerLinkName1" style="width:200px;" data-options="prompt:'六个字以内',disabled:true,validType:'length[2,5]'">
                    </div>
                </div>
                <div>
                    <label>链接1</label>
                    <div class="upFileDiv">
                        <input class="easyui-textbox" id="footerLinkAddress1" style="width:400px;" data-options="disabled:true,prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                    </div>
                </div>
            </li>-- >
            <li class="link-item hide" id="js-reg">
              <div>
                <label for="">邀请观众身份</label>
                <div style="display: flex;">
                  <div>
                    <input class="easyui-combobox" id="jsf-reg"
                    style="width: 340px;"
                     data-options="
                      valueField:'projRegSetId',textField:'projectNameDesc',panelHeight:'auto',panelMaxHeight:'200px',limitToList: true"
                    ">
                  </div>
                  &nbsp;&nbsp;&nbsp;
                  <a href="javascript:void(0)" onclick="showRegUrl()"
                     style="color: #5567a5;text-decoration: none;display: inline-block;padding-top: 2px;" > 登记地址</a>
                 </div>
              </div>
            </li>
            <li class="link-item">
              <div>
                <label>自定义按钮1</label>
                <div class="upFileDiv">
                  <input class="easyui-textbox" id="footerLinkName2" style="width:200px;"
                         data-options="prompt:'六个字以内',validType:'length[2,5]'">
                </div>
              </div>
              <div>
                <label>链接1</label>
                <div class="upFileDiv">
                  <input class="easyui-textbox" id="footerLinkAddress2" style="width:400px;"
                         data-options="prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                </div>
              </div>
            </li>
            <li class="link-item">
              <div>
                <label>自定义按钮2</label>
                <div class="upFileDiv">
                  <input class="easyui-textbox" id="footerLinkName3" style="width:200px;"
                         data-options="prompt:'六个字以内',validType:'length[2,5]'">
                </div>
              </div>
              <div>
                <label>链接2</label>
                <div class="upFileDiv">
                  <input class="easyui-textbox" id="footerLinkAddress3" style="width:400px;"
                         data-options="prompt: '如：https://www.expo2c.com 或 /eip-web-sponsor'">
                </div>
              </div>
            </li> -->
          </ul>
        </li>
        <li style="display: none;">
          <label>移动端官网首页链接</label>
          <div class="upFileDiv">
            <input class="easyui-textbox" id="officialWebsiteLink" style="width:350px;">
            <p class="upFileDivP" style="margin-left: 10px;">
              <span>填写之后，用户在邀请函上跳转到官网上；若不填写邀请函上则不显示线上展厅按钮</span></p>
          </div>
        </li>
        <li>
          <label>其他功能</label>
          <div class="upFileDiv">
            <div style="display: flex;">
              <div style="color: #999;margin-right: 20px;">点赞功能</div>
              <label style="display: inline-flex;align-items: center;vertical-align: text-top;margin-left: 40px;">
                <input type="checkbox" id="enableLikeFlag"/>启用
              </label>
            </div>
            <div style="display: flex;">
              <div style="color: #999;margin-right: 20px;">评论功能</div>
              <label style="display: inline-flex;align-items: center;vertical-align: text-top;margin-left: 40px;">
                <input type="checkbox" id="enableCommentFlag"/>启用
              </label>
            </div>
          </div>
        </li>
        <div class="line"></div>
        <li>
          <label>邀请函标题选项</label>
          <div id="customTitleContainer" style="display:flex;padding: 10px 0;flex-direction: column"></div>
          <div style="display:flex;padding: 10px 0;">
            <a href="#" class="easyui-linkbutton css-btn" onclick="CustomTitle.add()">添加</a>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <!-- <div title="展商邀请排行榜设置" style="width:100%;height:100%;"> -->
  <div id="page-rank" class="hide">
    <div>
      <div class="my-toolbar-button">
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="loadTempData()"
           plain="true">刷新</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save"
           onclick="saveInvitationEventSetting()"
           plain="true">保存</a>
      </div>
    </div>
    <div class="project-info1">
      <ul>
        <li class="chinese-show hide">
          <label>邀请函热度排行榜顶图</label>
          <div class="upFileDiv">
            <input class="easyui-textbox" id="invitationEventTopBanner" style="width:350px;">
            <i class="upFileDivButton">
              <em>上传</em>
              <input id="invitationEventTopBannerUploader" class="easyui-filebox upFileDivFileButton" name="upfile"
                     data-options="buttonText:'选择',prompt:''">
            </i>
            <p class="upFileDivP"><span>规格要求：750*328</span><span style="margin-left: 24px;">建议小于200K</span>
            </p>
          </div>
          <div class="upFileImgDiv2" style="width: 400px;height: 175px;">
            <img src="" alt="" id="invitationEventTopBannerCover">
          </div>
        </li>
        <li class="chinese-show hide">
          <label>活动规则: <span
              style="color: #999;">（由于展商使用手机端比较频繁，建议排版符合手机端阅读，不使用大段标题样式~）</span></label>
          <div id="invitationEventRules"></div>
        </li>
        <li class="english-show hide">
          <label>邀请函热度排行榜顶图 (英文)</label>
          <div class="upFileDiv">
            <input class="easyui-textbox" id="invitationEventTopBannerEn" style="width:350px;">
            <i class="upFileDivButton">
              <em>上传</em>
              <input id="invitationEventTopBannerEnUploader" class="easyui-filebox upFileDivFileButton" name="upfile"
                     data-options="buttonText:'选择',prompt:''">
            </i>
            <p class="upFileDivP"><span>规格要求：750*328</span><span style="margin-left: 24px;">建议小于200K</span>
            </p>
          </div>
          <div class="upFileImgDiv2" style="width: 400px;height: 175px;">
            <img src="" alt="" id="invitationEventTopBannerEnCover">
          </div>
        </li>
        <li class="english-show hide">
          <label>活动规则 (英文): <span
              style="color: #999;">（由于展商使用手机端比较频繁，建议排版符合手机端阅读，不使用大段标题样式~）</span></label>
          <div id="invitationEventRulesEn"></div>
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- 海报设置 -->
<%@ include file="../common/invitation_set_poster.jsp" %>
<!-- 登记链接 -->
<%@ include file="../common/reg_url_qr.jsp" %>

<!-- 自定义按钮设置 -->
<style>
  #btn-set-window{
    display: none;
  }
	.btn-set-body{
    border: 2px dotted #eee;
    padding:20px;
    margin: 20px;
	}
  .btn-set-body>div>div{
    display: flex;
    flex-direction: column;
  }
  .btn-set-body label>i {
    color: red;
  }
  .btnList>div{
    margin-right: 16px;
  }
  .btnList {
    display: flex;
    margin-top: 4px;
  }
</style>
<div id="btn-set-window"
 data-options="closed:true,modal:true,title:'底部按钮链接配置',width:700
 ,collapsible:true,minimizable:false,maximizable:false">
	<div class="my-toolbar-button datagrid-toolbar">
	  <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-save"
		onclick="util.btnSetSave()" plain="true">确定</a>
	  <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel"
		onclick="util.btnSetClose()" plain="true">取消</a>
	  <a href="javascript:void(0);" class="easyui-linkbutton js-del color-red" style="float:right" iconcls="icon-del-red"
		onclick="util.btnSetDel(this)" plain="true">删除</a>
	</div>
	<div class="btn-set-body" style="clear: both">
		<div style="display: flex;margin-bottom: 10px;">
      <div style="width: 260px">
        <label for=""><i>* </i>名称</label>
        <input class="easyui-textbox" id="jsf-btn-name" data-options="prompt: '最大6个字'" style="width:200px">
      </div>
      <div style="flex: 1">
        <label for="">排序号</label>
        <input class="easyui-numberbox" id="jsf-btn-sort" style="width:200px" data-options="min:0">
      </div>
    </div>
    <div style="margin-bottom: 10px;display: flex;">
      <div style="width: 260px">
        <label for=""><i>* </i>调用功能</label>
        <select id="jsf-btn-type" class="js-btn-type easyui-combobox" data-options="
          panelMaxHeight: 200,panelHeight:'auto',limitToList: true,
          onSelect(record) {
            $('.btn-link-only,.btn-reg-only').addClass('hide');
            if(record.value == 'link') {
              $('.btn-link-only').removeClass('hide');
            } else if(record.value == 'reg'){
              $('.btn-reg-only').removeClass('hide');
            }
          }
        " style="width:200px;">
          <option value="reg">观众登记</option>
          <option value="poster">生成海报</option>
          <option value="link">第三方链接</option>
        </select>
      </div>
      <div class="hide btn-link-only" style="flex: 1">
        <label><i>* </i>链接地址</label>
        <input class="easyui-textbox" id="jsf-btn-link" style="width: 300px">
      </div>
      <div class="hide btn-reg-only" style="flex: 1">
        <label><i>* </i>调用观众登记身份</label>
        <input class="easyui-combobox" id="jsf-btn-reg"
          style="width: 300px;" data-options="
          valueField:'projRegSetId',textField:'projectNameDesc',
          panelHeight:'auto',panelMaxHeight:'200px',limitToList: true" />
      </div>
    </div>
	</div>
</div>
<script src="../../js/view/invitation_set_detail.js?v=250121"></script>

<!-- // editor -->
<script>
  let editorMenus = [
    // 菜单配置
    'head', // 标题
    'bold', // 粗体
    'fontSize', // 字号
    'fontName', // 字体
    'italic', // 斜体
    'underline', // 下划线
    'strikeThrough', // 删除线
    'foreColor', // 文字颜色
    'backColor', // 背景颜色
    'link', // 插入链接
    'list', // 列表
    'justify', // 对齐方式
    'quote', // 引用
    'undo', // 撤销
    'image', // 插入图片
    'redo' // 重复
  ];
  const E = window.wangEditor
  const contactWays = new E('#contactWay')
  contactWays.customConfig.menus = editorMenus;
  contactWays.customConfig.uploadImgMaxSize = 204800
  contactWays.customConfig.zIndex = 1e3
  contactWays.customConfig.uploadImgShowBase64 = false
  contactWays.customConfig.uploadImgServer = '${eippath}/upload/uploadFile' // 填写配置服务器端地址
  contactWays.customConfig.uploadFileName = 'file' // 后端接受上传文件的参数名
  contactWays.customConfig.uploadImgParams = {
    file_type: 'img'
  }
  contactWays.customConfig.uploadImgHooks = {
    customInsert: (insertImgFn, result, contactWays) => {
      let url = result.result.path
      JSON.stringify(url)
      insertImgFn("http://" + window.location.hostname + ":" + window.location.port + '/image' + url)
    }
  }
  contactWays.create()
  const invitationEventRules = new E('#invitationEventRules')
  invitationEventRules.customConfig = Object.assign({}, contactWays.customConfig)
  invitationEventRules.customConfig.menus = [
    // 菜单配置
    'head', // 标题
    'bold', // 粗体
    'fontSize', // 字号
    'fontName', // 字体
    'italic', // 斜体
    'underline', // 下划线
    'strikeThrough', // 删除线
    'foreColor', // 文字颜色
    'backColor', // 背景颜色
    'link', // 插入链接
    'list', // 列表
    'justify', // 对齐方式
    'quote', // 引用
    'undo', // 撤销
    'redo' // 重复
  ]
  invitationEventRules.create()
  const invitationEventRulesEn = new E('#invitationEventRulesEn')
  invitationEventRulesEn.customConfig = Object.assign({}, contactWays.customConfig)
  invitationEventRulesEn.customConfig.menus = [
    // 菜单配置
    'head', // 标题
    'bold', // 粗体
    'fontSize', // 字号
    'fontName', // 字体
    'italic', // 斜体
    'underline', // 下划线
    'strikeThrough', // 删除线
    'foreColor', // 文字颜色
    'backColor', // 背景颜色
    'link', // 插入链接
    'list', // 列表
    'justify', // 对齐方式
    'quote', // 引用
    'undo', // 撤销
    'redo' // 重复
  ]
  invitationEventRulesEn.create()

</script>

<!-- // util -->
<script>
  function err(msg, icon='', throws = true) {
    msg = msg || `请求失败`;
    $.messager.alert('提示', msg, icon || 'error');
    if(throws) throw new Error(msg)
  }
  function setColors(el, callback) {
    let color = $(el).css('background-color')
    let color2 = rgb2hex(color)
    $(el).colpick({
      colorScheme: 'dark',
      layout: 'rgbhex',
      color: color2,
      onSubmit: function (hsb, hex, rgb, el) {
        $(el).css('background-color', '#' + hex);
        $(el).colpickHide();
        callback && callback()
      }
    })
  }
  function rgb2hex(rgb) {
    rgb = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);

    function hex(x) {
      return ("0" + parseInt(x).toString(16)).slice(-2);
    }

    return hex(rgb[1]) + hex(rgb[2]) + hex(rgb[3]);
  }

  function GetQueryString(name) {
    // var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    // var r = window.location.search.substr(1).match(reg);
    // return r != null ? unescape(r[2]) : null;
    return url.get(name);
  }
  // 删除上传文件的赋值
  function exhibitionTemplateDelImg(idPre, idSuffix = '') {
    if (idPre) {
      $('#' + idPre + 'Url' + idSuffix).attr('src', '')
      $('#' + idPre + idSuffix).textbox('clear')
    }
  }
  function check_url(_url) {
    var str = _url;
    var Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
    var objExp = new RegExp(Expression);
    return objExp.test(str)
  }
  function downloadImage(src, filename) {
    const A = document.createElement('A')
    A.href = src
    A.download = filename
    document.body.appendChild(A)
    A.click()
    A.remove()
  }
  function showDomImg(imgId, textboxId, result) {
    if(!result) return;
    if(imgId) $(imgId).attr("src", result.path || '');
    if(textboxId) $(textboxId).textbox("setValue", result.fileName || '')
  }
</script>
 <!-- // init + data -->
<script type="text/javascript">
  var url = (new URL(window.location.href)).searchParams
  // id=0,projectId>0: 排行榜设置
  // id>0,projectId='': 邀请函详情设置
  var id = url.get('id');
  var version = url.get('version') || '1';
  var project_for_pass = url.get('projectId');// top.project_for_pass;
  var is_publish = !!url.get('publish');
  var exhibitCode = url.get('exhibitCode');
  let pcActive = 1; //1 中文 0 英文
  var colorIndex = 0 //自助移动端 选取颜色的下标
  var colorIndexPc = 0 //自助pc主色 选取颜色的下标
  var colorIndex2Pc = 0 //自助pc辅色 选取颜色的下标
  var colorIndex3Pc = 0 //自助pc登录页辅色1 选取颜色的下标
  var colorIndex4Pc = 0 //自助pc登录页辅色2 选取颜色的下标
  var colorIndex5Pc = 0 //自助pc辅色2 选取颜色的下标
  var variablePic = location.origin;
  // 当前选择的观众登记
  var regSevers = [];
  $(function () {
    loadRegServers();
    init();
    // 加载 PC端官网模板设置 手机端官网模板设置 自助服务中心模板设置(3:额外处理全局) 邀请函模板设置 邀请活动设置
    loadTempData(3)
    window.frameElement && (frameElement.style.height = 'calc(100% - 5px)')
  });

</script>

<!-- // 支线 -->
<script>
  // 批量生成展商邀请函
  function invitation_batch_gen() {
    if(!is_publish) {
      $.messager.alert('提示','需发布后才可批量生成展商邀请函','warning');return;
    }
    $.messager.confirm("提示", `即将对 所有已移交客服的展商 生成邀请函, 是否确认 ?`, function (r) {
      if (r) {
    $.ajax({
      url: eippath + "/invitation/batchCreateInvitation",
      type: "POST",
      data: {
        projectId: top.project_for_pass,// TODO 暂时用主项目的
      },
      // dataType: "json",
      success (data) {
        if (data.state == 1) {
          $.messager.alert('提示', '操作成功！', 'info');
        } else {
          $.messager.alert('提示', data.msg || data.message || '操作失败！', 'error');
        }
      },
      error (xhr, ajaxoptions, thrownError) {
        $.messager.alert('提示', '请求失败! ', 'error');
      }
    })
      }
    }).window({width: '440px',icon: 'warning'});
  }
 // 显示观众登记链接 窗口
 function showRegUrl() {
    let tmp = $('#jsf-reg').combobox('getValue');
    if(!tmp) err('请先选择观众身份');
    // tmp: releaseReg projectId targetType isMain regState targetName orgNum
    tmp = regSevers.find(it=> it.projRegSetId === tmp);
    // para: channelTypeId, channelId, projectId, channelName, version, targetType
    channel_details_open2d(1, '', tmp.projectId, '', 1, tmp.targetType)
  }

</script>
<!-- // 主线 -->
<script>
  function syncColorSelector() {
    $('.color-selector').each(function () {
      const $this = $(this)
      const color = $this.css('background-color') || '#000'
      $this.colpick({
        colorScheme: 'dark',
        layout: 'rgbhex',
        color: color.slice(0, 1) === '#' ? color : rgb2hex(color),
        onSubmit: function (hsb, hex, rgb, el) {
          $(el).css('background-color', '#' + hex);
          $(el).colpickHide();
          setPreviewConfig()
        }
      })
    })
  }


  function checkEnable(obj) {
    // if($('#enableRegister').prop('checked') || $('#enablePosterFlag').prop('checked')) {
    //   $('#js-reg').removeClass('hide')
    // } else {
    //   $('#js-reg').addClass('hide')
    // }
    // const regId = $('#jsf-reg').combobox('getValue');
    // if(!regId) {
    //   let tmp = regSevers.find(it=> it.isMain && it.targetType == 1);
    //   if(!tmp && regSevers.length) tmp = regSevers.find(it=> it.targetType == 1);
    //   if(!tmp && regSevers.length) tmp = regSevers[0];
    //   if(tmp)  {
    //     $('#jsf-reg').combobox('setValue', tmp.projRegSetId);
    //   }
    // }
  }

  function isOpenLink(type) {
    let text
    if (type == 1) {
      window.linkManager.changeDisplay($("#enableBlogrollPc").prop('checked'))
    } else if (type == 2) {
      text = $("#enableContactWayPc").prop('checked')
      if (text == true) $('.isOpenContact').show()
      else $('.isOpenContact').hide()
    }
  }


  function init() {
    if(id) {
      $('#page-main').removeClass('hide');
      bindEvent();
      // 移动端邀请函顶部背景图
      $("#upTempBgi2").filebox({
        onChange: function (n, o) {
          var file = $("#upTempBgi2").filebox("files")[0];
          uploadFileImg(file, 3)
        }
      });
      // 移动端邀请函底部背景图
      $("#upTempBgi3").filebox({
        onChange: function (n, o) {
          var file = $("#upTempBgi3").filebox("files")[0];
          uploadFileImg(file, 4)
        }
      });
      // 移动端邀请海报背景图
      $("#posterBottomBgImgFb").filebox({
        onChange: function (n, o) {
          var file = $(this).filebox("files")[0];
          if (file.size / 1024 > 400)
            return $.messager.alert('提醒', '图片大小不能超过400KB', 'warning');
          uploadFileImg(file, 136)
        }
      });
      // $('#enablePosterFlag').change(function () {
      //   var fn = $(this).prop('checked') ? 'show' : 'hide'
      //   $('#posterBottomWrap')[fn]()
      // })
    } else {
      $('#page-rank').removeClass('hide');
      // 邀请函热度排行榜顶图
      $("#invitationEventTopBannerUploader").filebox({
        onChange: function (n, o) {
          uploadFileImg($(this).filebox("files")[0], 21)
        }
      });
      $("#invitationEventTopBannerEnUploader").filebox({
        onChange: function (n, o) {
          uploadFileImg($(this).filebox("files")[0], 1009)
        }
      });
      $('.chinese-show').removeClass('hide')
      $.ajax({
        url: "${eippath}/project/information/getSystemEnVersionSet",
        dataType: "json",
        type: "POST",
        success (data) {
          const isEn = !!data.data
          isEn && $('.english-show').removeClass('hide')
        },
      })
    }
  }

  // save 邀请函模板设置
  function seveTemp4() {
    let errIndex = 0, errInfo = ''
    // const footerData = operationFooterLinks('export')
    // /**
    //  三个名称和链接都是必填项，
    //  如果用户填写了名称1未填写链接1，保存时提醒他填写链接1
    //  填写了链接未填写名称的同样处理；
    //  可以空白，名称和链接都不填时，可以成功保存；
    //  */
    // const result = footerData
    //   .every((item, index) => {
    //     errIndex = index + 1
    //     if ((!item['name'] && !item['link']) || (item['name'] && item['link'])) return true
    //     if (!item['name']) errInfo = '名称'
    //     if (!item['link']) errInfo = '链接'
    //   })
    // if (!result) return $.messager.alert('提示', `请填写\${errInfo}\${errIndex}！`, 'warning')

    const btnSetting = util.buildBtnData() || [];
    const enableRegister = !!btnSetting.find(it=> it.check && it.type == 'reg');
    const enablePosterFlag = !!btnSetting.find(it=> it.check && it.type == 'poster');
    if(btnSetting.find(it=> it.check && it.type == 'reg' && !it.link)) {
      return $.messager.alert('提示', '观众登记按钮未设置！', 'error');
    };
    // const tmp = {
    //   pid: '',
    //   target: '',
    //   reg: $('#jsf-reg').combobox('getValue'),
    // }
    // if(tmp.reg) {
    //   let tmpRow = regSevers.find(it=> it.projRegSetId == tmp.reg) || {};
    //   tmp.pid = tmpRow.projectId || 0;
    //   tmp.target = tmpRow.targetType || 0;
    // }
    $.ajax({
      url: "${eippath}/project/information/saveInvitationSetting",
      data: {
        // 'projectId': GetQueryString("projectId"),
        inviteTemplateId: id,
        // buyerRegPid: tmp.pid,
        // buyerRegTargetType: tmp.target,
        customBtnSetDetail: JSON.stringify(btnSetting),
        // buyerRegDetail
        'invitation': $("#projectInfo_invitation").combobox("getValue"),
        'inviteTopBgImg': $("#upTempBgiName2").textbox("getValue"),
        'inviteTopBgImgUrl': $("#upTempBgiImg2").attr("src"),
        'inviteBottomBgImg': $("#upTempBgiName3").textbox("getValue"),
        'inviteBottomBgImgUrl': $("#upTempBgiImg3").attr("src"),
        'officialWebsiteLink': $("#officialWebsiteLink").textbox('getValue'),
        // 'inviteLinks': JSON.stringify(footerData),
        enableRegister,
        enablePosterFlag,
        'posterBottomBgImg': $('#posterBottomBgImg').textbox("getValue"),
        'posterBottomBgImgUrl': $('#posterBottomBgImgUrl ').attr("src"),
        'enableLikeFlag': $('#enableLikeFlag ').prop("checked"),
        'enableCommentFlag': $('#enableCommentFlag ').prop("checked"),
        'posterSetDetail': getPosterSetDetail(),
        inviteTemplateCustomTitle: CustomTitle.exportPayload(),
      },
      type: "POST",
      dataType: "json",
      success: function (data) {
        if (data.state == 1) {
          $.messager.alert('提示', '保存成功！', 'info');
          CustomTitle.init(CustomTitle.exportPayload())
        } else {
          $.messager.alert('提示', '保存失败！', 'error');
        }
      },
      error: function (xhr, ajaxoptions, thrownError) {
        // alert(xhr.responseText);
      }
    });

  }


  // save 邀请函排行榜设置
  function saveInvitationEventSetting() {
    $.ajax({
      url: eippath + '/project/information/saveInvitationEventSettings',
      data: {
        projectId: project_for_pass,
        invitationEventRules: invitationEventRules.txt.html(),
        invitationEventRulesEn: invitationEventRulesEn.txt.html(),
        invitationEventTopBanner: $("#invitationEventTopBannerCover").attr('src'),
        invitationEventTopBannerEn: $("#invitationEventTopBannerEnCover").attr('src'),
      },
      dataType: "json",
      type: "post",
      success(resp) {
        if (resp.state != 1)
          return $.messager.alert('错误', '数据加载失败！', 'error')
        $.messager.alert('提示', '保存成功！', 'info')
        loadTempData()
      }
    })
  }

  // 当前观众登记列表
  function loadRegServers() {
    regSevers = [];
    // 查询有那些观众登记
    $.ajax({
      url:  eippath + '/admin/projReg/getBuyerRegSetList',///RegSever/projRegSet/getBuyerRegSetList
      dataType: "json",
      type: "POST",
      async: false,
      data: {exhibitCode, queryBuyerRegEn: +version === 2},
      success (data2) {
        if (data2.state == 1) {
          data2.data.map(it=> {
            it.projectNameDesc = it.projectName + ' ' + it.targetTypeName;
            regSevers.push(it);
          })
          $('#jsf-btn-reg').combobox({data: regSevers});
          // $('.links-item-1 .dot').parent().addClass('hide')
          // if (data2.data) { // true
          //   $('.links-item-1 .green').parent().removeClass('hide')
          // } else { // null false
          //   $('.links-item-1 .red').parent().removeClass('hide')
          // }
        } else {
          err(data2.msg);
        }
      },
      error(){ err() }
    })
  }

  //  查询设置 : 邀请函模板 / 邀请函排行榜
  //  接口变更：eip-web-sponsor/project/information/getInformationByProjectId  变成 eip-web-sponsor/project/information/getInviteTemplateSetting  该接口主要处理邀请函模板和排行榜
  function loadTempData(type) {
    $.ajax({
      url: "${eippath}/project/information/getInviteTemplateSetting",
      dataType: "json",
      type: "POST",
      data: {
        inviteTemplateId: id, // 邀请函设置
        projectId: id ? '' : project_for_pass, // 邀请函排行榜
        // type:
      },
      success (data) {
        if (data.state != 1) err(data.msg || data.message || '加载项目配置失败！')
        // 查询项目观众登记是否发布
        loadTempDataSet(data, type)
      },
    });
  }
  // 设置数据 绑定
  function loadTempDataSet(data, type) {
    // 初始化海报数据
    if(id) initPosterSetDetail(null)
    var datas = data.result,reg_pid = '',reg_target = '';
    // 默认按钮
    let tmp = regSevers.find(it=> it.isMain && it.targetType === 1) || regSevers.find(it=> it.targetType === 1) || regSevers[0] || {};
    let defaultRegId = tmp.projRegSetId || '';
    let btns = [
      {
        name: '观众登记',
        sort: 0,
        type: 'reg',
        reg: defaultRegId,
        link: util.buildRegUrl(tmp.projectId, version || 1,tmp.targetType).search,
        check: true,
      },
      {
        name: '生成海报',
        sort: 1,
        type: 'poster',
        reg: '',
        link: '',
        check: true,
      }
    ]
    let inviteTemplateCustomTitle = ''
    datas.forEach(function (element, index) {
      if (element.type == 5) {
        $("#projectInfo_website_index").combobox("setValue", element.content)
      } else if (element.type == 6) { // 固定点赞模板
        // $("#projectInfo_invitation").combobox("setValue", element.content)
      } else if (element.type == 11) {
        $("#upTempBgiName2").textbox("setValue", element.content)
        if(element.pic) {
          $("#upTempBgiImg2").attr("src", element.pic).parent().removeClass('hide')
        } else {
          $("#upTempBgiImg2").attr("src", '').parent().addClass('hide')
        }
      } else if (element.type == 12) {
        $("#upTempBgiName3").textbox("setValue", element.content)
        if(element.pic) {
          $("#upTempBgiImg3").attr("src", element.pic).parent().removeClass('hide')
        } else {
          $("#upTempBgiImg3").attr("src", '').parent().addClass('hide')
        }
      // } else if (element.type == 13) {
      //     if(element.content){
      //         exhibitionTplGlobal.logo = element.pic
      //         $("#upTempLogoName").textbox("setValue", element.content)
      //         $("#upTempLogoImg").attr("src", element.pic)
      //     }
      // } else if (element.type == 14) {
      //     if(element.content) {
      //         exhibitionTplGlobal.mColorMain = element.content
      //         colorIndex = element.enable
      //         exhibitionTplGlobal.setColorPickerVal2('#selfServiceColor', element)
      //     }
      // } else if (element.type == 15) {
      //     if(element.content){
      //         exhibitionTplGlobal.mbg = element.pic
      //         $("#upTempBgiName").textbox("setValue", element.content)
      //         $("#upTempBgiImg").attr("src", element.pic)
      //     }
      // } else if (element.type == 16) {
      //     if(element.content){
      //         exhibitionTplGlobal.mTpl = element.content
      //         $("#mobile-farm-project-temporary").combobox("setValue", element.content)
      //     }
      } else if (element.type == 17) {
        $("#officialWebsiteLink").textbox("setValue", element.content)
      } else if (element.type == 106) {
        // if(id) operationFooterLinks('import', JSON.parse(element.content))
      } else if (element.type == 107) {
        // 邀请函模板: 已经固定了
      } else if (element.type == 113) {
        $('#invitationEventTopBanner').textbox('setValue', element.pic || '')
        $('#invitationEventTopBannerCover').attr('src', element.pic || '')
      } else if (element.type == 114) {
        if(!id) invitationEventRules.txt.html(element.content || '')
      } else if (element.type == 130) {
        $('#enableRegister').prop('checked', element.content === 'true')
      } else if (element.type == 135) {
        $('#enablePosterFlag').prop('checked', element.content === 'true')
      } else if (element.type == 136) {
        $("#posterBottomBgImg").textbox("setValue", element.content)
        $("#posterBottomBgImgUrl").attr("src", element.pic)
        $('#enablePosterFlag').change()
      }else if (element.type == 138) {
        // 加载海报设置
        if(id) initPosterSetDetail(element.content)
      }else if (element.type == 140) { // 启用点赞功能 (邀请函)
        $('#enableLikeFlag').prop('checked', element.content === 'true')
      }else if (element.type == 141) { // 启用评论功能 (邀请函)
        $('#enableCommentFlag').prop('checked', element.content === 'true')
      }else if (element.type == 150) { // 邀请观众身份角色
        reg_target = +element.content
      }else if (element.type == 153) { // 邀请函自定义按钮 定义
        if(element.content) {
          btns = JSON.parse(element.content)
        }
      }else if (element.type == 151) { // 角色身份来源项目
        reg_pid = +element.content
      }else if (element.type == 1007) { // 邀请函标题选项
        inviteTemplateCustomTitle = element.content
      }else if (element.type == 1008) { // 活动规则(英文)
        if(!id) invitationEventRulesEn.txt.html(element.content || '')
      }else if (element.type == 1009) { // 邀请函热度排行榜顶图(英文)
        if (!id) {
          $('#invitationEventTopBannerEn').textbox('setValue', element.pic || '')
          $('#invitationEventTopBannerEnCover').attr('src', element.pic || '')
        }
      }
    });
    util.btns = btns;
    util.renderBtns(btns);
    if(id) {
      // $('#jsf-reg').combobox('clear');
      // if($('#enableRegister').prop('checked') || $('#enablePosterFlag').prop('checked')) {
      //   // 默认设置 + 显示 : 观众登记
      //   $('#js-reg').removeClass('hide');
      //   if(reg_pid && reg_target) {
      //     let tmp = regSevers.find(it=> it.projectId == reg_pid && it.targetType == reg_target);
      //     if(tmp) $('#jsf-reg').combobox('setValue', tmp.projRegSetId || '');
      //   }
      // } else {
      //   $('#js-reg').addClass('hide');
      // }
      CustomTitle.init(inviteTemplateCustomTitle)
      syncColorSelector()
    }
  }

  //上传图片
  function uploadFileImg(file, type) {
    if (!file) err('请选择附件！', 'warning');
    var formData = new FormData();
    formData.append("file", file);
    if (type == 123) {
      if (file.type !== 'image/jpeg') return $.messager.alert('提示', '需要jpg/jpeg格式', 'warning')
      if (file.size > 150 * 1000) return $.messager.alert('提示', '图片不能超过150kb', 'warning')
    } else if (type == 3 || type == 4) {
      if (file.size > 500 * 1000) return $.messager.alert('提示', '图片不能超过500kb', 'warning')
    } else if (type == 136) {
      if(file.type === 'image/png' && file.size > 400 * 1000) return $.messager.alert('提示', 'png格式的图片不能超过400kb', 'warning')
    } else if (file.type === 'image/png' && file.size > 300 * 1000) {
      err('png格式的图片不能超过300kb', 'warning')
    }
    $.ajax({
      url: "${eippath}/upload/uploadFileOss",
      dataType: "json",
      type: "post",
      data: formData,
      processData: false,
      contentType: false,
      success: function (data) {
        if (data.state == 1) {
          if (type == 1) {
            $("#upTempLogoImg").attr("src", data.result.path);
            $("#upTempLogoName").textbox("setValue", data.result.fileName)
          } else if (type == 2) {
            $("#upTempBgiImg").attr("src", data.result.path);
            $("#upTempBgiName").textbox("setValue", data.result.fileName)
          } else if (type == 3) {
            $("#upTempBgiImg2").attr("src", data.result.path).parent().removeClass('hide');
            $("#upTempBgiName2").textbox("setValue", data.result.fileName)
          } else if (type == 4) {
            $("#upTempBgiImg3").attr("src", data.result.path).parent().removeClass('hide');
            $("#upTempBgiName3").textbox("setValue", data.result.fileName);
          } else if (type == 5) {
            $("#websiteTopBgImgUrlPc").attr("src", data.result.path);
            $("#websiteTopBgImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 6) {
            $("#websiteTopLogoUrlPc").attr("src", data.result.path);
            $("#websiteTopLogoPc").textbox("setValue", data.result.fileName)
          } else if (type == 7) {
            $("#websiteTopImgUrlPc").attr("src", data.result.path);
            $("#websiteTopImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 8) {
            $("#websiteBanner1ImgUrlPc").attr("src", data.result.path);
            $("#websiteBanner1ImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 9) {
            $("#websiteBanner2ImgUrlPc").attr("src", data.result.path);
            $("#websiteBanner2ImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 10) {
            $("#websiteBanner3ImgUrlPc").attr("src", data.result.path);
            $("#websiteBanner3ImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 11) {
            $("#websiteBanner4ImgUrlPc").attr("src", data.result.path);
            $("#websiteBanner4ImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 12) {
            $("#websiteInsideTopImgUrlPc").attr("src", data.result.path);
            $("#websiteInsideTopImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 13) {
            $("#websiteTopLogoUrl").attr("src", data.result.path);
            $("#websiteTopLogo").textbox("setValue", data.result.fileName)
          } else if (type == 14) {
            $("#websiteBanner1ImgUrl").attr("src", data.result.path);
            $("#websiteBanner1Img").textbox("setValue", data.result.fileName)
          } else if (type == 15) {
            $("#websiteBanner2ImgUrl").attr("src", data.result.path);
            $("#websiteBanner2Img").textbox("setValue", data.result.fileName)
          } else if (type == 16) {
            $("#websiteBanner3ImgUrl").attr("src", data.result.path);
            $("#websiteBanner3Img").textbox("setValue", data.result.fileName)
          } else if (type == 17) {
            $("#websiteBanner4ImgUrl").attr("src", data.result.path);
            $("#websiteBanner4Img").textbox("setValue", data.result.fileName)
          } else if (type == 18) {
            $("#websiteCopyrightInfoImgUrlPc").attr("src", data.result.path);
            $("#websiteCopyrightInfoImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 19) {
            $("#QrCodeImgUrlPc").attr("src", data.result.path);
            $("#QrCodeNamePc").textbox("setValue", data.result.fileName)
          } else if (type == 20) {
            $("#websiteFaviconUrlPc").attr("src", data.result.path);
            $("#websiteFaviconPc").textbox("setValue", data.result.fileName)
          } else if (type == 21) {
            $("#invitationEventTopBannerCover").attr("src", data.result.path)
            $("#invitationEventTopBanner").textbox("setValue", data.result.fileName)
          } else if (type == 22) { // pc登录页背景图
            $("#upTempBgiImgPc").attr("src", data.result.path)
            $("#upTempBgiNamePc").textbox("setValue", data.result.fileName)
          } else if (type == 123) { // pc端官网 行业模板 底部背景图
            $("#websiteFootBgImgUrlPc").attr("src", data.result.path);
            $("#websiteFootBgImgPc").textbox("setValue", data.result.fileName)
          } else if (type == 136) { // 移动端邀请海报背景图
            $("#posterBottomBgImg").textbox("setValue", data.result.fileName)
            $("#posterBottomBgImgUrl").attr("src", data.result.path)
            setPreviewConfig()
          } else if (type == 1009) {
            $("#invitationEventTopBannerEnCover").attr("src", data.result.path)
            $("#invitationEventTopBannerEn").textbox("setValue", data.result.fileName)
          }

        } else err('上传失败！', 'error');
      },
    });
  }

  var CustomTitle = {
    container: null,
    titleList: [],
    init(inviteTemplateCustomTitle) {
      this.container = $('#customTitleContainer')
      this.titleList = (() => {
        try {
          return JSON.parse(inviteTemplateCustomTitle)
        } catch (e) {
          return []
        }
      })().map(item => ({...item, oldTitle: item.title}))
      this.render()
    },
    get uniq() {
      return Math.random().toString(16).slice(2, 10)
    },
    add() {
      // this.titleList.push({
      //   id: this.uniq,
      //   title: '',
      //   oldTitle: ''
      // })
      // this.render()
      let item = {
        id: this.uniq,
        title: '',
        oldTitle: ''
      }
      this.titleList.push(item);
      this.render(item)
    },
    del(id) {
      const effect = () => {
        this.titleList = this.titleList.filter(it=> it.id != id)
        $('#title-item-' + id).remove();
        this.renderIndex();
      }
      if ((this.titleList.find(it=> it.id == id) || {}).title) {
        return $.messager.confirm('提示', '删除后将无法恢复，确认删除？', r => r && effect())
      }
      effect()
    },
    buildDom(index, id, title) {
      return `
      <div id="title-item-\${id}" class="js-title-item" style="display:flex;">
        <div style="margin-right: 20px;" class="js-title-item-index">\${index + 1}.</div>
        <div class="embed-input-wrapper">
          <label style="margin-bottom: 15px">
            <span class="wrap">
              <textarea class="insert-input" rows="2" cols="100" placeholder="默认标题模板">\${title}</textarea>
              <span class="buttons" style="bottom: 2px;">
                <button onclick="CustomTitle.embedInputInsertText('\${id}', '<展商简称>')">嵌入展商简称</button>
                <button style="flex-basis: 200px;flex-grow: 0;" onclick="CustomTitle.embedInputInsertText('\${id}')">还原</button>
              </span>
            </span>
          </label>
        </div>
        <div style="margin-left: 20px;">
          <a
            href="javascript:void(0);"
            style="color: #EA5252"
            class="easyui-linkbutton"
            onclick="CustomTitle.del('\${id}')"
            iconcls="icon-del-red" plain="true">删除</a>
        </div>
      </div>`
    },
    renderIndex() {
      $('.js-title-item').each((idx, it) => {
        $(it).find('.js-title-item-index').text((idx + 1) + '.')
      })
    },
    render(item) {
      if(item) {
        this.container.append(this.buildDom(this.titleList.length, item.id, item.title))
        this.renderIndex();
        $.parser.parse(this.container);
        return;
      }
      this.container.empty()
      this.titleList.length === 0 ? this.container.hide() : this.container.show()
      const html = this.titleList.map((item, index) => this.buildDom(index, item.id, item.title)).join('\n')
      this.container.html(html)
      $.parser.parse(this.container)
    },
    embedInputPosition: {
      get(textarea) {
        let rangeData = {text: "", start: 0, end: 0};
        if (textarea.setSelectionRange) {
          // W3C
          textarea.focus();
          rangeData.start = textarea.selectionStart;
          rangeData.end = textarea.selectionEnd;
          rangeData.text =
            rangeData.start != rangeData.end
              ? textarea.value.substring(rangeData.start, rangeData.end)
              : "";
        } else if (document.selection) {
          // IE
          textarea.focus();
          var i,
            oS = document.selection.createRange(),
            // Don't: oR = textarea.createTextRange()
            oR = document.body.createTextRange();
          oR.moveToElementText(textarea);

          rangeData.text = oS.text;
          rangeData.bookmark = oS.getBookmark();

          // object.moveStart(sUnit [, iCount])
          // Return Value: Integer that returns the number of units moved.
          for (
            i = 0;
            oR.compareEndPoints("StartToStart", oS) < 0 &&
            oS.moveStart("character", -1) !== 0;
            i++
          ) {
            // Why? You can alert(textarea.value.length)
            if (textarea.value.charAt(i) == "\r") {
              i++;
            }
          }
          rangeData.start = i;
          rangeData.end = rangeData.text.length + rangeData.start;
        }

        return rangeData;
      },
      set(textarea, rangeData) {
        let oR, start, end;
        if (!rangeData) {
          alert("You must get cursor position first.");
        }
        textarea.focus();
        if (textarea.setSelectionRange) {
          // W3C
          textarea.setSelectionRange(rangeData.start, rangeData.end);
        } else if (textarea.createTextRange) {
          // IE
          oR = textarea.createTextRange();

          // Fixbug : ues moveToBookmark()
          // In IE, if cursor position at the end of textarea, the set function don't work
          if (textarea.value.length === rangeData.start) {
            //alert('hello')
            oR.collapse(false);
            oR.select();
          } else {
            oR.moveToBookmark(rangeData.bookmark);
            oR.select();
          }
        }
      },
      add(textarea, rangeData, text) {
        let oValue, nValue, oR, sR, nStart, nEnd, st;
        this.set(textarea, rangeData);

        if (textarea.setSelectionRange) {
          // W3C
          oValue = textarea.value;
          nValue =
            oValue.substring(0, rangeData.start) +
            text +
            oValue.substring(rangeData.end);
          nStart = nEnd = rangeData.start + text.length;
          st = textarea.scrollTop;
          textarea.value = nValue;
          // Fixbug:
          // After textarea.values = nValue, scrollTop value to 0
          if (textarea.scrollTop != st) {
            textarea.scrollTop = st;
          }
          textarea.setSelectionRange(nStart, nEnd);
        } else if (textarea.createTextRange) {
          // IE
          sR = document.selection.createRange();
          sR.text = text;
          sR.setEndPoint("StartToEnd", sR);
          sR.select();
        }
        return textarea.value;
      },
    },
    embedInputInsertText(id, text) {
      const el = $(`#title-item-\${id} textarea`)
      if (!text) {
        const current = this.titleList.find(item => item.id === id)
        return el.val(current ? current.oldTitle : '')
      }
      let pos = this.embedInputPosition.get(el[0]);
      let newVal = this.embedInputPosition.add(el[0], pos, text)
      el.val(newVal)
    },
    exportPayload() {
      return JSON.stringify(this.titleList.map(item => {
        return {
          ...item,
          title: $(`#title-item-\${item.id} textarea`).val(),
          oldTitle: void 0
        }
      }))
    }
  }

  // //对底部链接操作的封装
  // function operationFooterLinks(type, data = null) {
  //   const _J = [2, 3].map(index => [$('#footerLinkName' + index), $('#footerLinkAddress' + index)])
  //   //   const _J = [1,2,3].map(index=>[$('#footerLinkName' + index), $('#footerLinkAddress' + index)])
  //   switch (type) {
  //     case 'export':
  //       return _J.map(item => {
  //         const [$name, $link] = item
  //         return {
  //           name: $name.textbox('getValue'),
  //           link: $link.textbox('getValue'),
  //         }
  //       })
  //     case 'import':
  //       if (!Array.isArray(data)) return
  //       if (data.length > 2) { // 兼容老数据
  //         data.shift()
  //       }
  //       data.forEach((item, index) => {
  //         const [$name, $link] = _J[index]
  //         $name.textbox('setValue', item['name'])
  //         $link.textbox('setValue', item['link'])
  //       })
  //       break
  //   }
  // }
</script>

</body>
</html>