<!-- 活动推广设置 : 文件拆分 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="240513"/>
<title>活动推广设置</title>
<link rel="stylesheet" href="../../css/view/eventMarketSet.css?v=${v}">
<script>var eippath = '${eippath}';</script>
<script src="../../js/view/eventMarketSet.js?v=${v}" async></script>
<script src="${eippath}/js/jquery.qrcode.logo.ex.js"></script>
<style>
  .hide {
    display: none !important;
  }
  #eventList {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 0 20px;
    list-style-type: none;
  }
  .main2-tags {
    display: flex;padding: 0 0 0 20px;margin: -14px 0 10px;
  }
  .tag-type,.tag-role {
    color: #1890FF;
    background-color: #ECF6F9;
    border-radius: 6px;
    margin-right: 11px;
    padding: 1px 13px;
  }
  .tag-role-1 {
    color: #32C56D;
  }
  .tag-role-2 {}
  .tag-type-poster {}
  .tag-type-micro-site {
    color: #7C6BED;
  }
</style>
</head>
<body>
<span loading>加载中...</span>
<div id="app" cloak>
  <div class="sticky">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);" class="easyui-linkbutton"
         onclick="app.add()" plain="true">创建新活动</a>
      <a href="javascript:void(0);" id="js-showData" class="easyui-linkbutton hide"
         onclick="app.showData()" plain="true">查看推广数据</a>
    </div>
  </div>
  <div id="page0" class="page-wrap flex-center hide" style="height: calc(100vh - 38px);position: absolute;top: 0;width: 100%;">
    <center class="page">
      <div style="margin-top: 10%">
        <center style="font-size: 32px;color: #6A6A6A;font-weight: normal;">创建活动，助力展会流量提升</center>
        <center class="page-main flex-center">
          <div>
            <img src="${eippath}/img/imgs/step1.svg" alt="1.建立展商申请 观众申请入口">
            <div>
              1.建立展商申请 观众申请入口
            </div>
          </div>
          <i class="triangle--blue"></i>
          <div>
            <img src="${eippath}/img/imgs/step2.svg" alt="2.新建活动推广">
            <div>
              2.新建活动推广
            </div>
          </div>
          <i class="triangle--blue"></i>
          <div>
            <img src="${eippath}/img/imgs/step3.svg" alt="3.设置活动推广 选择可申请展会">
            <div>
              3.设置活动推广 选择可申请展会
            </div>
          </div>
          <i class="triangle--blue"></i>
          <div>
            <img src="${eippath}/img/imgs/step4.svg" alt="4.分享推广活动 流量随分享增加">
            <div>
              4.分享推广活动 流量随分享增加
            </div>
          </div>
        </center>
        <center class="page-bot">
          <!-- <a href="javascript:void(0);" onclick="app.setApply(1)">设置展商申请</a> -->
          <!-- <a href="javascript:void(0);" onclick="app.setApply(2)">设置观众申请</a> -->
          <a href="javascript:void(0);" class="primary" onclick="app.add()">新建活动推广</a>
        </center>
      </div>
    </center>
  </div>
  <div id="page1" class="page-wrap flex-center hide" style="height: calc(100vh - 38px);position: relative;width: 100%;">
    <div class="page" style="position: absolute;top: 0;">
      <ul style="padding: 20px;" id="eventList">
        <li template id="eventTemplate">
          <div class="title flex-between">
            <span>{{eventName}} - <%--{{releProjectName}} - --%>{{languageVersion}}</span>
            <div class="blue flex">
              <div class="toggle-version a" style="display: none;" onclick="app.toggleVersion('{{eventId}}', '{{enableEnVersion}}')">{{toggleVersionText}}</div>
              <div class="devide" style="display: none;"></div>
              <div class="a" onclick="window.open('{{eventLink}}', '_blank')">预览</div>
              <div class="devide"></div>
              <div onclick="app.showDetail('{{eventId}}')" class="a">编辑</div>
              <div class="devide"></div>
              <div class="a" onclick="app.deleteEvent('{{eventId}}')">删除</div>
            </div>
          </div>
          <div class="main2">
            <div class="main2-tags">
              <div class="tag-role tag-role-{{applyRoleType}}">{{applyRoleTypeDesc}}</div>
              <div class="tag-type tag-type-{{eventTemplateType}}">{{eventTemplateTypeDesc}}</div>
            </div>
            <div style="padding:0 20px 20px;color: #AFAFAF;display: grid;grid-template-columns: 100px 200px 100px 200px;">
              <div>创建时间</div>
              <div>{{createTime}}</div>
              <div>访问量</div>
              <div>{{pageViewCount}}</div>
            </div>
          </div>
          <!-- <div class="main">
            <div>
              <div>访问量</div>
              <div>{{pageViewCount}}</div>
            </div>
            <div>
              <div>参展申请数量</div>
              <div>{{exhibitApplyCount}}</div>
            </div>
            <div>
              <div>观众登记数量</div>
              <div>{{buyerRegCount}}</div>
            </div>
            <div>
              <div>展商邀请观众</div>
              <div>{{exhibitInvitBuyerRegCount}}</div>
            </div>
            <div>
              <div>观众邀请观众</div>
              <div>{{buyerRegInvitBuyerRegCount}}</div>
            </div>
            <div>
              <div>主办方邀请观众</div>
              <div>{{sponsorInvitBuyerRegCount}}</div>
            </div>
          </div> -->
          <div class="bot flex-between">
            <div class="flex">
              <span>推广链接</span>&nbsp;&nbsp;
              <span class="link" style="width: calc( 24vw - 60px );" title="{{eventLink}}">{{eventLink}}</span>
            </div>
            <div>
              <label class="a"  onclick="app.copyAddress('{{eventLink}}')" style="display: inline-flex;align-items: center;margin-right: 32px;">
                <img src="${eippath}/img/imgs/link.svg">
                &nbsp;复制推广链接
              </label>
              <label class="a"  onclick="app.showQr('{{eventId}}', '{{enableEnVersion}}')"  style="display: inline-flex;align-items: center;">
                <img class="event-link-qr" src="${eippath}/img/imgs/qrcode.svg">
                &nbsp;查看分享二维码
              </label>
            </div>
            <!-- <div class="devide"></div> -->
            <!-- <div class="a blue">推广数据</div> -->
          </div>
        </li>
      </ul>
    </div>
  </div>
  <!-- 活动推广链接 -->
  <div id="qr-window" class="easyui-window"
       data-options="closed:true,title:'活动推广链接',modal:true,maximizable: true,collapsed: false,resizable: false,minimizable: true,shadow:false,cls:'el-window'"
       style="width:650px;height: auto;">
    <div id="qr-panel-1" style="padding: 0 16px">
      <div class="all_address">
        <span>中文版地址</span>
        <textarea cols="50" rows="3" id="qr-h5-address" readonly></textarea>
        <button class="btn-primary" onclick="app.copySubscriptionText(2)">
          复制链接
        </button>
        <a id="qr-h5-address-open" style="cursor: pointer;"
           onclick="app.openSubscriptionAddress(2)">直接打开</a>
      </div>
      <div class="all_address">
        <span>英文版地址</span>
        <textarea cols="50" rows="3" id="qr-pc-address" readonly></textarea>
        <button class="btn-primary" onclick="app.copySubscriptionText(1)">
          复制链接
        </button>
        <a id="qr-pc-address-open" style="cursor: pointer;"
           onclick="app.openSubscriptionAddress(1)">直接打开</a>
      </div>
      <div class="address_download">
        <ul>
          <li>
            <div id="qr-code1" class="qr_code"></div>
            <div class="address_download_button">
              <a id="qr-download" download="qrcode.jpg"></a>
            </div>
          </li>
          <li>
            <div class="prompt_text">
              <p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
              <button id="qr-saveQrCode" class="btn-primary" onclick="app.downQr()">
                下载二维码
              </button>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
</body>
</html>