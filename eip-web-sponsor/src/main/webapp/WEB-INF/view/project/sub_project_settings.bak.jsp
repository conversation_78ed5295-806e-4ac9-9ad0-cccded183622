<%--
  Created by IntelliJ IDEA.
  User: Rex_Surprise
  Date: 2021-03-19
  Time: 15:48
  Except for the notes here.
  I wrote none of the notes below.
  The latecomer asks for more happiness  QAQ~
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>

<title>子项目设置</title>
<script src="../../js/wangEditor.min.js"></script>
<link rel="stylesheet" href="../../css/cropping/cropper.min.css">
<link rel="stylesheet" href="../../css/cropping/ImgCropping.css">
<script src="../../js/cropping/cropper.min.js"></script>
<style>
  p {
    padding: 0;
    margin: 0;
  }

  .layout-list .easyui-textbox {
    width: 300px;
    height: 22px;
  }

  .layout-list li:first-child {
    display: flex;
    align-items: center;
  }

  .layout-list li {
    margin: 5px 0;
  }

  .layout-list li label {
    margin-bottom: 4px;
    display: block;
  }

  .layout-list li:first-child div {
    margin-right: 60px;
  }

  #sub-project-panel .upFileDivButton {
    font-style: normal;
    margin: 0 10px;
    display: inline-block;
    position: relative;
  }

  #sub-project-panel .upFileDivFileButton {
    width: 60px;
  }

  #sub-project-panel .upFileDivButton > span {
    opacity: 0;
    position: absolute;
    left: -14px;
  }

  #sub-project-panel .upFileDivP {
    display: inline-block;
  }

  #sub-project-panel .upFileImgDiv {
    border: solid 2px #aaa;
    background-color: #ccc;
    margin-top: 10px;
    position: relative;
  }

  #sub-project-panel .upFileImgDiv img{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }

  #sub-project-panel .upFileImgDiv .placeholder{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    color: #eee;
    font-size: 18px;
    font-weight: bolder;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  #sub-project-panel .upFileDivButton > em, .del-btn {
    width: 64px;
    height: 30px;
    background-color: #ffffff;
    border-radius: 4px;
    border: solid 1px #b2b2b2;
    color: #333333;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    font-style: normal;
    display: inline-block;
    cursor: pointer;
  }

  #sub-project-panel ::-webkit-scrollbar-thumb {
    background-color: #e0ecff;
    border-radius: 3px;
  }

  #sub-project-panel ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .w-e-text-container {
    height: 200px !important;
  }
</style>
<div class="easyui-layout" data-options="fit:true">
  <div data-options="region:'north',border:true" style="height:41px;">
    <!-- begin of toolbar -->
    <div id="project-areasetting-toolbar" class="datagrid-toolbar">
      <div class="my-toolbar-button">
        <a href="javascript:void(0);" id="sub-project-settings-fresh" class="easyui-linkbutton" iconCls="icon-reload"
           onclick="sub_project_settings_fresh()" plain="true">刷新</a>
        <%--<a href="javascript:void(0);" id="sub-project-settings-add" class="easyui-linkbutton" iconCls="icon-add"--%>
        <%--   onclick="sub_project_settings_add()" plain="true">新增</a>--%>
        <%--<a href="javascript:void(0);" id="sub-project-settings-del" class="easyui-linkbutton" iconCls="icon-remove"--%>
        <%--   onclick="sub_project_settings_del()" plain="true">删除</a>--%>
        <a href="javascript:void(0);" id="sub-project-settings-save" class="easyui-linkbutton" iconCls="icon-save"
           onclick="sub_project_settings_save()" plain="true">保存</a>
      </div>
    </div>
    <!-- end of toolbar -->
  </div>
  <div data-options="region:'center',border:false">
    <div class="easyui-layout" data-options="fit:true">
      <div data-options="region:'west',split:true" style="width:200px;padding:10px;position: relative;">
        <ul id="sub-project-tree" class="easyui-tree" data-options="method:'post',animate:true"></ul>
        <div id="sub-project-tree-shade" style="display: none;"></div>
      </div>
      <div data-options="region:'center'">
        <style>
          #subProSetting .tabs-header
          , #subProSetting .tabs-panels { border: none }
          .subProSet-btn {
            padding: 0 15px;background: none;margin-left: 15px;
          }
        </style>
        <div id="subProSetting" class="easyui-tabs" style="width:100%;height:0px;">
          <div title="基本信息设置" style="display:none;"></div>
          <div title="线上展厅API" style="overflow:auto;display:none;"></div>
        </div>
        <script>
          $(function(){
            $('#subProSetting').tabs({
              border: false,
              onSelect(title, index){
                $('#sub-project-panel>[data-id='+ (1-index)+']').hide()
                $('#sub-project-panel>[data-id='+index+']').show()
              }
            });
          })
          function sub_project_set_copy(id){
            copyText2Clipboard($('#' +id).textbox('getValue'));
            function copyText2Clipboard(text, vueInst){
              text = (text || '').trim()
              const input = document.createElement('input');
              input.setAttribute('readonly', 'readonly');
              input.setAttribute('value', text);
              document.body.appendChild(input);
              input.select();
              input.setSelectionRange(0, 9999);
              if (document.execCommand('copy')) {
                document.execCommand('copy');
                // vueInst && vueInst.$message({message: '已复制信息至剪贴板', type: 'success', showClose: !0})
              }
              document.body.removeChild(input);
              return text;
            }
          }
          function sub_project_set_down() {
            const datas = [
              '接口路径',$('#sps-project-api-url').textbox('getValue'),
              '项目ID',$('#sps-project-api-pid').textbox('getValue'),
              '项目名称',$('#sps-project-api-pname').textbox('getValue'),
              'TOKEN',$('#sps-project-api-token').textbox('getValue'),
              '行业网站API文档地址','http://deve.smtcrm.com:8118/web/#/48?page_id=1990',
            ];
            savefiles(datas.join('\n'), '线上展厅API参数文档.txt');
            function savefiles(data, name) {
            //Blob为js的一个对象，表示一个不可变的, 原始数据的类似文件对象，这是创建文件中不可缺少的！
              var urlObject = window.URL || window.webkitURL || window;
              var export_blob = new Blob([data]);
              var save_link = document.createElementNS("http://www.w3.org/1999/xhtml", "a")
              save_link.href = urlObject.createObjectURL(export_blob);
              save_link.download = name;
              save_link.click();
            }
          }
          function sub_project_set_enableApi() {
            const projectId = $('#sps-project-api-pid').textbox('getValue');
            if(projectId) {
              $.ajax({
                  url: eippath + '/project/saveSiteToken',
                  method: 'post',
                  data: { projectId },
                  success(data) {
                    if (data.state !== 1) return $.messager.alert('错误', data.msg, 'error')
                    selectByProjectId(projectId)
                  }
                })
            } else {
              $.messager.alert('提示','请先选择项目', 'warning');
            }
          }
        </script>
        <div id="sub-project-panel" class="easyui-panel" style="padding: 20px;">
          <ul class="layout-list" data-id="0">
            <li class="first">
              <div>
                <label>项目名称</label>
                <p><input class="easyui-textbox" hint="projectName" data-options="readonly:true" id="sps-project-name">
                </p>
              </div>
              <div>
                <label>项目类别</label>
                <p><input class="easyui-textbox" hint="projKindName" data-options="readonly:true" id="sps-project-type">
                </p>
              </div>
            </li>
            <li>
              <label>活动起止时间</label>
              <p>
                <input id="sps-project-start"  hint="activityTimeStart" class="easyui-datetimebox" style="width:170px;height:25px"
                  data-options="onShowPanel:function(){
                    $('#sps-project-start')
                    .datetimebox('spinner')
                    .timespinner('setValue','00:00:00')
                    },
                  ">-<input id="sps-project-end" hint="activityTimeEnd" class="easyui-datetimebox" style="width:170px;height:25px"
                  data-options="onShowPanel:function(){
                    $('#sps-project-end')
                    .datetimebox('spinner')
                    .timespinner('setValue','23:59:59')
                    },
                  "></p>
            </p>
            </li>
            <li>
              <label>项目名称（英文）</label>
              <p><input class="easyui-textbox" hint="projectNameEn" id="sps-project-name-en"></p>
            </li>
            <li>
              <label>项目背景图</label>
              <div class="upFileDiv">
                <input class="easyui-textbox" hint="projectBgImgName" data-options="readonly:'true'"
                       id="project-pic-bg-filename"
                       style="width:350px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="project-pic-bg-btn" class="easyui-filebox upFileDivFileButton" name="upfile"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <a href="javascript:void(0);" class="del-btn" onclick="deleteImage(1)">删除</a>
                &nbsp;&nbsp;
                <p class="upFileDivP">推荐比例4:3， 支持.JPG .JPEG 大小不超过2M，.png格式大小不超过300K
                </p>
              </div>
              <div class="upFileImgDiv" id="project-pic-bg-img-wrapper" style="height: 150px;">
                <img alt="项目背景图" style="height: 150px;" id="project-pic-bg-img" hint="projectBgImgUrl" onerror="this.style.zIndex = -1">
                <div class="placeholder">暂无图片</div>
              </div>
            </li>
            <li>
              <label>项目图片</label>
              <div class="upFileDiv">
                <input class="easyui-textbox" hint="projectPicName" data-options="readonly:'true'"
                       id="project-pic-filename"
                       style="width:350px;">
                <i class="upFileDivButton">
                  <em>上传</em>
                  <input id="project-pic-btn" class="easyui-filebox upFileDivFileButton" name="upfile"
                         data-options="buttonText:'选择',prompt:''">
                </i>
                <a href="javascript:void(0);" class="del-btn" onclick="deleteImage(2)">删除</a>
                &nbsp;&nbsp;
                <p class="upFileDivP">推荐宽度1120px，高度560-840px，支持.JPG .JPEG 大小不超过2M，.png格式大小不超过300K
                </p>
              </div>
              <div class="upFileImgDiv" style="width: 200px; height: 150px;">
                <img alt="项目图片" style="width: 200px; height: 150px;" id="project-pic-img" hint="projectPicUrl" onerror="this.style.zIndex = -1">
                <div class="placeholder">暂无图片</div>
              </div>
            </li>
            <li>
              <label>项目简介</label>
              <div id="project-brief"></div>
            </li>
            <li>
              <label>项目简介（英文）</label>
              <div id="project-brief-en"></div>
            </li>
          </ul>
          <ul class="layout-list" data-id="1" style="display: none;">
            <li>
              <div>
                <label>接口路径</label>
                <p>
                  <input class="easyui-textbox" readonly id="sps-project-api-url" style="width: 600px;">
                  <a href="#" onclick="sub_project_set_copy('sps-project-api-url')"
                   class="easyui-linkbutton subProSet-btn">复制</a>
                </p>
              </div>
            </li>
            <li>
              <label>项目ID</label>
              <p>
                <input class="easyui-textbox" readonly id="sps-project-api-pid"  style="width: 600px;">
                <a href="#" onclick="sub_project_set_copy('sps-project-api-pid')"
                 class="easyui-linkbutton subProSet-btn">复制</a>
              </p>
            </li>
            <li>
              <label>项目名称</label>
              <p>
                <input class="easyui-textbox" readonly id="sps-project-api-pname"  style="width: 600px;">
                <a href="#" onclick="sub_project_set_copy('sps-project-api-pname')"
                 class="easyui-linkbutton subProSet-btn">复制</a>
              </p>
            </li>
            <li>
              <label>TOKEN</label>
              <p>
                <input class="easyui-textbox" readonly id="sps-project-api-token"  style="width: 600px;">
                <a href="#" data-id="1" onclick="sub_project_set_copy('sps-project-api-token')"
                  class="easyui-linkbutton subProSet-btn" style="display: none;">复制</a>
                <a href="#" data-id="0" onclick="sub_project_set_enableApi()"
                 class="easyui-linkbutton subProSet-btn">生成</a>
              </p>
            </li>
            <li style="padding-top: 15px;" class="sps-project-apiDoc">
              <span onclick="sub_project_set_down()" style="padding: 5px 12px; color: #EB7878;border: 1px solid #EB7878;border-radius: 4px;cursor: pointer;">下载线上展厅API参数文档</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <!--选取项目 -->
  <div
      id="select-project-window"
      class="easyui-window"
      data-options="closed:true,title:'选取项目',modal:true,top:80" style="width:700px;">
    <style>
      .my-toolbar-style {
        overflow: hidden;
      }

      .my-toolbar-style ul li {
        width: 40%;
        float: left;
        margin-bottom: 6px;
      }

      .my-toolbar-style ul li label {
        margin: 0 8px;
      }

      .select-pro-search {
        width: 70px;
        height: 28px;
        position: relative;
        display: inline-block;
        top: 75px;
      }

      .link-hover .easyui-linkbutton {
        background: #63a9f8 !important;
        border-color: #ccc !important;
        width: 70px;
        height: 28px;
        color: #fff !important;
      }

      .link-hover .easyui-linkbutton:hover {
        background: #2e82e4 !important;
      }

      .link-hover .easyui-linkbutton.cancel-button {
        border-color: #ccc !important;
        background: #f5f5f5 !important;
        margin-right: 15px !important;
        color: #222222 !important;
      }

      .link-hover .easyui-linkbutton.cancel-button:hover {
        background: #e5e5e5 !important;
      }
    </style>
    <div style="margin: 10px 0; overflow: hidden;">
      <div class="my-toolbar-search link-hover my-toolbar-style" style="width: 100%;" id="select-project-param">
        <ul>
          <li>
            <label>项目类别</label>
            <input id="select-project-projKindCode" class="easyui-combobox" control="combobox" hint="projKindCode"
                   data-options="valueField:'id',textField:'text',limitToList: true,
								  panelHeight:'auto',panelMaxHeight:'200px',
								  ">&nbsp;
          </li>
          <li>
            <label>项目名称</label><input id="select-project-projectName" hint="projectName" class="easyui-textbox"
                                      control="textbox">
          </li>
          <li>
            <label>举办地点&nbsp;</label><input id="select-project-address" hint="f_address" class="easyui-textbox"
                                            control="textbox">
          </li>
          <li>
            <label>举办年份</label><input id="select-project-year" hint="f_year_month" class="easyui-textbox"
                                      control="textbox">
          </li>
          <li>
            <label>开始日期&nbsp;</label><input id="select-project-startDate" hint="f_start_time" class="easyui-datebox"
                                            control="datebox">
          </li>
          <li>
            <label>结束日期</label><input id="select-project-endDate" hint="f_end_time" class="easyui-datebox"
                                      control="datebox">
          </li>
        </ul>
        <a href="javascript:void(0);" class="easyui-linkbutton select-pro-search"
           onclick="select_project_search()">查询</a>
      </div>
      <div class="link-hover" style="float: left;margin: 10px 0 10px 10px;">
        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="select_project_ok()" plain="false">选定</a>
        &nbsp;
        <a href="javascript:void(0);" class="easyui-linkbutton cancel-button" onclick="select_project_return()"
           plain="false">取消</a>
      </div>
    </div>
    <div id="select-project-panel" class="easyui-panel" style="width:100%;height:284px;border: none;">
      <div class="easyui-layout" data-options="fit:true">
        <div data-options="region:'center'">
          <table id="select-project-datagrid" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',
							onDblClickRow:function (rowIndex, rowData) {
		        			select_project_ok_db(rowData);
		 				},">
            <thead>
            <tr>
              <th data-options="field:'ck',checkbox:true"></th>
              <th data-options="field:'projectId',width:20">项目编号</th>
              <th data-options="field:'projectName',width:40">项目名称</th>
              <th data-options="field:'projKindName',width:20">项目类型</th>
              <th data-options="field:'f_year_month',width:20,sortable:true">举办年份</th>
              <th data-options="field:'f_start_time',width:40,sortable:true" formatter="start_end">起止时间</th>
              <th data-options="field:'f_country',width:20" formatter="project_place_format">举办地点</th>
            </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
    <script>
      function start_end(val, row, index) {
        var start = "";
        var end = "";
        if (val != null && val != "null") {
          start = val;
        }
        if (row.f_end_time != null && row.f_end_time != "null") {
          end = row.f_end_time;
        }
        if (start != "" || end != "") {
          return start + ' 至   ' + end;
        } else {
          return "";
        }
      }

      function project_place_format(val, row, index) {
        var country = "";
        var province = "";
        var city = "";
        if (row.country != null && row.country != "null") {
          country = row.country;
        }
        if (row.province != null && row.province != "null") {
          province = " " + row.province;
        }
        if (row.city != null && row.city != "null") {
          city = " " + row.city;
        }
        return country + province + city;
      }
    </script>
  </div>
  <%--图片裁切--%>
  <div class="tailoring-container ImgShow" style="display: none;">
    <div class="black-cloth" onclick="closeTailor(this)"></div>
    <div class="tailoring-content">
      <div class="tailoring-content-one">
        <div class="close-tailoring" onclick="closeTailor(this)">×</div>
      </div>
      <div class="tailoring-content-two">
        <div class="tailoring-box-parcel">
          <img id="tailoringImg">
        </div>
        <div class="preview-box-parcel">
          <p>图片预览：</p>
          <div class="square previewImg"></div>
          <div class="circular previewImg"></div>
        </div>
      </div>
      <div class="tailoring-content-three">
        <button class="l-btn cropper-reset-btn">复位</button>
        <button class="l-btn cropper-rotate-btn">旋转</button>
        <button class="l-btn cropper-scaleX-btn">换向</button>
        <button class="l-btn sureCut" id="sureCut" onclick="uploadCropperImage(this)">确定</button>
      </div>
    </div>
  </div>
</div>
<script>
  const $tailoringImg = $('#tailoringImg')
  window.aspectRatio = 4 / 3; //默认比例
  window.uploadFileImgType = -1
  //初始化裁剪框
  function initCropper() {
    //cropper图片裁剪
    $('#project-pic-bg-img-wrapper').css('width', `\${150 * window.aspectRatio}px`)
    $('#project-pic-bg-img').css('width', `\${150 * window.aspectRatio}px`)
    $tailoringImg.cropper({
      aspectRatio: window.aspectRatio,
      preview: '.previewImg', //预览视图
      guides: false, //裁剪框的虚线(九宫格)
      autoCropArea: 1, //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
      dragCrop: true, //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
      movable: true, //是否允许移动剪裁框
      resizable: true, //是否允许改变裁剪框的大小
      zoomable: true, //是否允许缩放图片大小
      mouseWheelZoom: false, //是否允许通过鼠标滚轮来缩放图片
      touchDragZoom: true, //是否允许通过触摸移动来缩放图片
      rotatable: true, //是否允许旋转图片
      crop: function (e) {
        // 输出结果数据裁剪图像。
      }
    });
    //旋转
    $(".cropper-rotate-btn").on("click", function () {
      $tailoringImg.cropper("rotate", 45);
    });
    //复位
    $(".cropper-reset-btn").on("click", function () {
      $tailoringImg.cropper("reset");
    });
    //换向
    let flagX = true;
    $(".cropper-scaleX-btn").on("click", function () {
      if (flagX) {
        $tailoringImg.cropper("scaleX", -1);
        flagX = false;
      } else {
        $tailoringImg.cropper("scaleX", 1);
        flagX = true;
      }
    });
  }
  //设置裁剪比例
  function setAspectRatio(aspectRatio) {
    $tailoringImg.cropper("aspectRatio", aspectRatio)
  }
  //关闭裁剪框
  function closeTailor() {
    $(".tailoring-container").toggle();
  }
  //开始裁剪图片
  function cropperImage(file, type) {
    // png 不做裁剪
    window.uploadFileImgType = type
    if(file.type === 'image/png') return uploadFileImg(file)
    $('.ImgShow').show()
    $(".tailoring-box-parcel").show();
    $(".preview-box-parcel").show();
    let _reader = new FileReader();
    _reader.readAsDataURL(file)
    _reader.onload = function (evt) {
      let replaceSrc = evt.target.result;
      //更换cropper的图片
      $('#tailoringImg').cropper('replace', replaceSrc, false); //默认false，适应高度，不失真
      $('#replaceImg').cropper('replace', replaceSrc, false);
    }
  }
  //确定提交上传
  function uploadCropperImage(_this) {
    const cas = $('#tailoringImg').cropper('getCroppedCanvas'); //获取被裁剪后的canvas
    const base64url = cas.toDataURL('image/jpeg', '0.0'); // 'image/jpg' 转换为base64地址形式
    const filename = $(_this).attr('filename')
    const file = dataURLtoFile(base64url, filename);
    uploadFileImg(file)
    //关闭裁剪框
    $(".ImgShow").hide();
    $(".tailoring-box-parcel").hide();
    $(".preview-box-parcel").hide();
  }
  //base64转成文件对象
  function dataURLtoFile(dataUrl, filename) { //将base64转换为文件
    let arr = dataUrl.split(','),
      type = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, {type});
  }
</script>
<script>
  const $projectName = $('[hint="projectName"]')
  const $projKindName = $('[hint="projKindName"]')
  const $projectNameEn = $('[hint="projectNameEn"]')
  const $projectPicName = $('[hint="projectPicName"]')
  const $projectPicUrl = $('[hint="projectPicUrl"]')
  const $projectBgImgName = $('[hint="projectBgImgName"]')
  const $projectBgImgUrl = $('[hint="projectBgImgUrl"]')
  const $briefIntroduction = $('[hint="briefIntroduction"]')
  const $briefIntroductionEn = $('[hint="briefIntroductionEn"]')
  const $projectTimeStart = $('[hint="activityTimeStart"]')
  const $projectTimeEnd = $('[hint="activityTimeEnd"]')
  const projectId = project_for_pass
  function sub_project_settings_fresh() {
    $('.easyui-panel').panel('resize')
    renderTree()
  }
  function sub_project_settings_add() {
    const current = $('#sub-project-tree').tree('getSelected')
    if (!current) return $.messager.alert('提示', '先选择主项目，才能添加子项目！', 'warning')
    openSelectProject()
  }
  function sub_project_settings_del() {
    const $subProjectTree = $('#sub-project-tree')
    const currentNode = $subProjectTree.tree('getSelected')
    const projectId = currentNode.projectId
    $.messager.confirm('提示', '将要删除项目：' + currentNode.projectName + '，是否继续？', r => {
      if (!r) return
      $.ajax({
        url: eippath + '/project/removeSubProject',
        method: 'post',
        data: {projectId},
        success(data) {
          if (data.state !== 1) return $.messager.alert('错误', data.msg, 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          renderTree()
          clearInfo()
        }
      })
    })
  }
  function sub_project_settings_save() {
    updateProject()
  }
  //项目选取
  function openSelectProject() {
    var param = {};
    $("#select-project-projKindCode").combobox({
      url: eippath + "/projKind/getAll", //获取数据
      method: "post",
      queryParams: param,
    })
    setEditInfoClear("#select-project-param ul");
    var time = new Date();
    let m = time.getMonth() - 1
    time.setMonth(m)
    let timer = time.toLocaleDateString('zh-Hans-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour12: false
    }).replace(/\//g, '-')
    $('#select-project-startDate').datebox('setValue', timer)
    select_project_search();
    $('#select-project-window').window('open');
  }
  //加载项目选取table
  function select_project_search() {
    var param = {};
    param['notPid'] = project_for_pass;
    setPostData(param, "#select-project-param ul");
    $('#select-project-datagrid').datagrid({
      url: eippath + '/project/selectSubCandidate',
      queryParams: param,
      onLoadSuccess: function (data) {
        $('#select-project-datagrid').datagrid("fitColumns")
      },
      onLoadError: function () {
        $.messager.alert('提示', '数据发送失败', 'error');
      },
    });
  }
  //项目选取确定
  function select_project_ok() {
    var row = $('#select-project-datagrid').datagrid('getSelected');
    if (row) {
      addTreeChild(row)
      select_project_return()
      return
    }
    $.messager.alert('提示', '未选中内容！', 'warning');
  }
  //项目选取 数据双击
  function select_project_ok_db(row) {
    addTreeChild(row)
    select_project_return()
  }
  //往树里面添加节点
  function addTreeChild(node) {
    const $subProjectTree = $('#sub-project-tree')
    const parentId = $subProjectTree.tree('getSelected')['projectId']
    $.ajax({
      url: eippath + '/project/addSubProject?parentId=' + parentId,
      method: 'post',
      contentType: "application/json",
      data: JSON.stringify([node.projectId]),
      success(data) {
        if (data.state !== 1) return $.messager.alert('错误', '子项目添加失败！', 'error')
        $.messager.alert('提示', '添加成功！', 'info', r => {
          renderTree().then(data => {
            const $subProjectTree = $('#sub-project-tree')
            const changeNode = $subProjectTree.tree('find', node.projectId)
            if (changeNode == null) return console.warn('数据异常')
            $subProjectTree.tree('select', changeNode.target)
            setInfoByNode(changeNode)
          })
        })
      }
    })
  }
  //项目选取  关闭弹框
  function select_project_return() {
    $('#select-project-window').window('close');
  }
  function initEditor() {
    const E = window.wangEditor
    window.projectBrief = new E('#project-brief')
    window.projectBriefEn = new E('#project-brief-en')
    const file_type = 'img'
    const uploadPath = '${eippath}/upload/uploadFile'
    const uploadFileName = 'file'
    const uploadImgMaxSize = 204800
    const uploadImgShowBase64 = false
    const customInsert = (insertCallBack, res, _) => insertCallBack(location.origin + '/image' + res.result.path)
    projectBrief.customConfig.uploadImgMaxSize = uploadImgMaxSize
    projectBrief.customConfig.uploadImgShowBase64 = uploadImgShowBase64
    projectBrief.customConfig.uploadImgServer = uploadPath
    projectBrief.customConfig.uploadFileName = uploadFileName
    projectBrief.customConfig.uploadImgParams = {file_type}
    projectBrief.customConfig.uploadImgHooks = {customInsert}
    projectBrief.create()
    projectBriefEn.customConfig.uploadImgMaxSize = uploadImgMaxSize
    projectBriefEn.customConfig.uploadImgShowBase64 = uploadImgShowBase64
    projectBriefEn.customConfig.uploadImgServer = uploadPath
    projectBriefEn.customConfig.uploadFileName = uploadFileName
    projectBriefEn.customConfig.uploadImgParams = {file_type}
    projectBriefEn.customConfig.uploadImgHooks = {customInsert}
    projectBriefEn.create()
  }
  let subProjectSettings_mainProjectId = project_for_pass;
  function renderTree(parentId = project_for_pass) {
    return new Promise((resolve, reject) => {
      const $subProjectTree = $('#sub-project-tree')
      $subProjectTree.tree({
        url: eippath + '/project/selectSubProject?parentId=' + parentId,
        loadFilter(data) {
          return data.data
        },
        onLoadSuccess(data) {
          // const $subProjectTree = $('#sub-project-tree')
          const roots = $subProjectTree.tree('getRoots')
          if(roots && roots.length) {
            $subProjectTree.tree('select', roots[0].target);
            subProjectSettings_mainProjectId = roots[0].projectId;
            selectByProjectId(roots[0].projectId)
          }
          resolve(data)
        },
        onLoadError(e) {
          reject(e)
        }
      })
    })
  }

  function checkFile_subPS(id="") {
    function throws(msg='error') {
      $.messager.alert('提示',msg,'warning');
      throw new Error(msg);
    }
    let msg = ' id required '
    if(!id) throws(msg);
    const $id = $(id);
    const file = $id.filebox("files")[0];
    $id.filebox()
    // console.log('tye', file.type)
    if (!["image/png","image/jpeg","image/jpg"].includes(file.type)){
      throws('很抱歉，只能上传jpg/jpeg/png格式图片!');
    } else if(file.size / 1024 > 2048){
      throws('图片过大,上传失败。')
    } else if(file.type==='image/png' && file.size / 1024 > 300) {
      throws('PNG图片过大,上传失败。')
    }
    return file;
  }
  function initEvent() {
    const $sureCut = $('#sureCut')
    $('#project-pic-bg-btn').filebox({
      onChange(n, o) {
        const file = checkFile_subPS('#project-pic-bg-btn')
        if(file) {
          $sureCut.attr('filename', file.name)
          // setAspectRatio(4 / 3)
          cropperImage(file, 1)
        }
      }
    })
    $('#project-pic-btn').filebox({
      onChange(n, o) {
        const file = checkFile_subPS('#project-pic-btn')
        if(file) {
          $sureCut.attr('filename', file.name)
          // setAspectRatio(4 / 3)
          cropperImage(file, 2)
        }
      }
    })
    $('#sub-project-tree').tree({
      onClick(node) {
        const projectId = node.projectId
        if(subProjectSettings_mainProjectId == projectId) {
          $('#subProSetting .tabs-header .tabs-last').show()
        } else {
          $('#subProSetting').tabs('select',0);
          $('#subProSetting .tabs-header .tabs-last').hide()
        }
        projectId && selectByProjectId(projectId)
      }
    })
  }
  function uploadFileImg(file) {
    if (!file) return $.messager.alert('提示', '请选择图片！', 'warning')
    if(file.type === 'image/png' && file.size > 300 * 1000)
      return $.messager.alert('提示','png格式的图片不能超过300kb','warning')
    //兼容之前已经上传的图片
    function queryData2image(src) {
      const inner = src => {
        if (!src) return '/RegSever/RegPage/images/top-lucency.png'
        const prefix = location.origin
        if (/(http|https)/.test(src)) return src
        else if (/(^\\+|^\/)/.test(src)) return prefix + src
        return prefix + '/image/' + src
      }
      const url = inner(src)
      if (!url) return ''
      try {
        return new URL(url).host.indexOf('aliyuncs.com') !== -1
          ? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
          : url
      } catch (e) {
        return url
      }
    }
    const formData = new FormData();
    formData.append("file", file);
    $.ajax({
      url: "${eippath}/upload/uploadFileOss",
      dataType: "json",
      type: "post",
      data: formData,
      processData: false,
      contentType: false,
      success(data) {
        if (data.state !== 1) return $.messager.alert('提示', '上传失败！', 'error');
        switch (uploadFileImgType) {
          case 1:
            $projectBgImgUrl.attr("src", queryData2image(data.result.path));
            $projectBgImgUrl.css({zIndex: 1})
            $projectBgImgName.textbox("setValue", data.result.fileName)
            break
          case 2:
            $projectPicUrl.attr("src", queryData2image(data.result.path));
            $projectPicName.textbox("setValue", data.result.fileName)
            $projectPicUrl.css({zIndex: 1})
            break
        }
      },
      error() {
        $.messager.alert('提示', '上传失败！', 'error');
      }
    })
  }
  function setInfoByNode(node) {
    let {
      projectName,
      projKindName,
      projectNameEn,
      projectPicName,
      projectPicUrl,
      projectBgImgName,
      projectBgImgUrl,
      briefIntroduction,
      briefIntroductionEn,
      activityTimeStart,
      activityTimeEnd,
      projectId,
      siteToken,
    } = node
    $projectName.textbox('setValue', projectName ? projectName : '')
    $projKindName.textbox('setValue', projKindName ? projKindName : '')
    $projectNameEn.textbox('setValue', projectNameEn ? projectNameEn : '')
    $projectPicName.textbox('setValue', projectPicName ? projectPicName : '')
    $projectPicUrl.attr('src', projectPicUrl ? projectPicUrl : '')
    projectPicUrl && $projectPicUrl.css({zIndex: 1})
    $projectBgImgName.textbox('setValue', projectBgImgName ? projectBgImgName : '')
    $projectBgImgUrl.attr('src', projectBgImgUrl ? projectBgImgUrl : '')
    projectBgImgUrl && $projectBgImgUrl.css({zIndex: 1})
    window.projectBrief.txt.html(briefIntroduction ? briefIntroduction : '')
    window.projectBriefEn.txt.html(briefIntroductionEn ? briefIntroductionEn : '')
    $projectTimeStart.datetimebox('setValue', activityTimeStart ? activityTimeStart : '')
    $projectTimeEnd.datetimebox('setValue', activityTimeEnd ? activityTimeEnd : '')
    $('#sps-project-api-pid').textbox('setValue', projectId || '')
    $('#sps-project-api-pname').textbox('setValue', projectName || '')
    $('#sps-project-api-token').textbox('setValue', siteToken || '')
    $('#sps-project-api-url').textbox('setValue', window.origin + '/eip-web-business/api/set/')
    const tmp = $('#sps-project-api-token').parent();
    if(siteToken) {
      tmp.find('[data-id=1]').show().end().find('[data-id=0]').hide()
      $('#sub-project-panel .sps-project-apiDoc').show();
    } else {
      tmp.find('[data-id=1]').hide().end().find('[data-id=0]').show()
      $('#sub-project-panel .sps-project-apiDoc').hide();
    }
  }
  function updateProject() {
    const $subProjectTree = $('#sub-project-tree')
    const projectId = $subProjectTree.tree('getSelected')['projectId']
    const projectNameEn = $projectNameEn.textbox('getValue')
    const projectPicName = $projectPicName.textbox('getValue')
    const projectPicUrl = $projectPicUrl.attr('src')
    const projectBgImgName = $projectBgImgName.textbox('getValue')
    const projectBgImgUrl = $projectBgImgUrl.attr('src')
    const briefIntroduction = window.projectBrief.txt.html()
    const briefIntroductionEn = window.projectBriefEn.txt.html()
    const postData = {
      projectId,
      projectNameEn,
      projectPicName,
      projectPicUrl,
      projectBgImgName,
      projectBgImgUrl,
      briefIntroduction,
      briefIntroductionEn,
      activityTimeStart: $projectTimeStart.datetimebox('getValue'),
      activityTimeEnd: $projectTimeEnd.datetimebox('getValue'),
    }
    $.ajax({
      url: eippath + '/project/updateProject',
      dataType: "json",
      type: "post",
      data: postData,
      success(data) {
        if (data.state !== 1) return $.messager.alert('错误', '保存失败！', 'error')
        $.messager.alert('提示', '保存成功！', 'info')
        clearInfo()
        renderTree().then(data => {
          const $subProjectTree = $('#sub-project-tree')
          const changeNode = $subProjectTree.tree('find', projectId)
          if (changeNode == null) return console.warn('数据异常')
          $subProjectTree.tree('select', changeNode.target)
          changeNode.target.click()
          // setInfoByNode(changeNode)
        })
      },
      error() {
        $.messager.alert('错误', '保存失败！', 'error')
      }
    })
  }
  function clearInfo() {
    const clear = {
      projectName: '',
      projKindName: '',
      projectNameEn: '',
      projectPicName: '',
      projectPicUrl: '',
      projectBgImgName: '',
      projectBgImgUrl: '',
      briefIntroduction: '',
      briefIntroductionEn: '',
      activityTimeStart: '',
      activityTimeEnd: '',
    }
    setInfoByNode(clear)
  }
  function deleteImage(type) {
    switch (type) {
      case 1:
        $projectBgImgUrl.attr("src", '');
        $projectBgImgName.textbox("setValue", '')
        break
      case 2:
        $projectPicUrl.attr("src", '');
        $projectPicName.textbox("setValue", '')
        break
    }
  }
  function selectByProjectId(projectId){
    $.ajax({
      url: eippath + '/project/selectByProjectId',
      dataType: "json",
      type: "post",
      data: {projectId},
      success(data) {
        setInfoByNode(data)
      },
      error() {
        $.messager.alert('错误', '项目数据加载失败！', 'error')
      }
    })
  }
  function initAspectRatio(){
    $.ajax({
      url: eippath + '/project/information/getInformationByType',
      dataType: "json",
      type: "post",
      data: {projectId, type: 98},
      success(data) {
        try{
          if(data.result !== null && data.result && data.result.content){
            const [w,h] = data.result.content.split('/')
            window.aspectRatio = Number(w) / Number(h)
            $('#scale').text(`\${w}:\${h}`)
          }
        }finally {
          initCropper()
        }
      },
      error() {
        initCropper()
      }
    })
  }
  $(function () {
    initEditor()
    renderTree()
    initEvent()
    initAspectRatio()
  })
</script>
