<%--
  Date: 2021-09-10
  Time: 8:32
  邀请函热度排行
  已迁移☞：/RegSever/RegPage/pages/invitation-letter/invitation-ranking.html
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!doctype html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>邀请函热度排行</title>
  <script type="text/javascript" src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <script src="../../vue/axions.js"></script>
  <script src="../../vue/vue.js"></script>
  <script src="../../vue/vant/vant.min.js"></script>

  <script src="../../js/wx-share.js?v=240416"></script>
  <script>var orgNum = '';</script>

  <script src="../../js/rem-375px.js"></script>
  <link rel="stylesheet" href="../../vue/vant/index.css">
  <link rel="stylesheet" href="../../css/invite-ranking.css">
</head>
<body>
<div id="app">
  <header class="top-header">
    <van-image :src="topImageUrl" width="100%" height="100%"></van-image>
  </header>
  <main class="container" :style="{marginTop: activityRules ? '0': '20px'}">
    <p class="activity-rules" v-if="activityRules">
      <button @click="showRules">活动规则</button>
    </p>
    <div class="ranking-list-header">
      <span>排名</span><span>公司名称</span><span>参观</span><span>人气</span>
    </div>
    <div class="ranking-list">
      <ul class="ranking-list-wrapper">
        <li class="ranking-list-item" v-for="({companyName,buyerCount,readNum, invitationId}, index) in rankingList"
            :key="invitationId">
          <span v-html="ranking(index + 1)"></span><span>{{companyName}}</span><span>{{buyerCount}}</span>
          <span>{{readNum}}</span>
        </li>
      </ul>
    </div>
  </main>
  <button class="share-button" @click="showShare =  true"></button>
  <div class="share-container" v-show="showShare" @click="showShare = false" @touchmove.prevent>
    <van-image width="12rem" height="10rem" src="../../img/invitation/share-cn.png"/>
  </div>
</div>
<script>
  const Axios = axios.create({baseURL: location.origin})

  function getQueryString(name) {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
    const url = decodeURI(decodeURI(window.location.search))
    const r = url.substr(1).match(reg)
    if (r != null) return unescape(r[2])
    return null
  }

  const projectId = getQueryString('projectId')
  const exhibitCode = getQueryString('exhibitCode')
  new Vue({
    data() {
      return {
        rankingList: [],
        topImageUrl: '',
        activityRules: '',
        showShare: false
      }
    },
    mounted() {
      const loading = this.$toast.loading({forbidClick: true, duration: 0, message: '加载中…'})
      this.runShare()
      const formData = new FormData()
      formData.append('pid', projectId)
      formData.append('exhibitCode', exhibitCode)
      Axios.post('/eip-web-business/api/invitation/selectInviteHotNumber', formData).then(({data: resp}) => {
        if (resp.state !== 1) return this.$dialog.alert({message: '数据加载失败！'})
        const invitationLists = resp.rows
        this.rankingList = invitationLists
        const projectInformations = resp.data
        projectInformations.forEach(item => {
          switch (item.type) {
            case 113:
              this.topImageUrl = item.pic || ''
              break;
            case 114:
              if ((item.content || '').replace(/(<\/?p>|<br\/?>|\s+|&nbsp;)/g, '')) {
                this.activityRules = item.content || ''
              }
          }
        });
      }).catch((err) => {
        this.$dialog.alert({message: '数据加载失败！'})
        console.warn(err);
      }).finally(() => loading.clear())
    },
    methods: {
      showRules() {
        this.$dialog.alert({
          width: '95%',
          title: '活动规则',
          message: this.activityRules,
          theme: 'round-button',
          className: 'i-e-r',
          confirmButtonColor: '#e5e5e5',
          allowHtml: true
        })
      },
      runShare() {
        const data = new FormData()
        data.append('projectId', projectId)
        Axios.post('/RegSever/project/queryById', data).then(({data: res}) => {
          const {f_project_name, f_2code_img} = res
          orgNum = res.f_org_num || '';
          const imgUrl = f_2code_img
          const link = location.href
          const desc = '来看看谁邀请的人数最多吧~'
          const title = f_project_name + '-邀请函热度排行'
          share({imgUrl, desc, title, link})
        }).catch((err) => console.warn(err))
      },
    },
    computed: {
      ranking() {
        return rank => rank < 4 ? `<img src="../../img/invitation/\${rank}.png" alt="invitation" style="height:2rem" />` : rank
      }
    }
  }).$mount('#app')
</script>
</body>
</html>
