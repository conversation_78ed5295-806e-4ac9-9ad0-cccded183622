<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style type="text/css">
	.box {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		padding: 0 30px;
	}

	.underway-Task {
		width: 100%;
	}

	.underway-Task-Title {
		width: 100%;
		box-sizing: border-box;
		border-bottom: 1px solid #ccc;
	}

	.underway-Task-Title h6 {
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin: 20px 0 8px 0;
	}

	.underway-Tasks {
		overflow: hidden;
		box-sizing: border-box;
		padding-bottom: 20px;
	}

	.underway-Tasks ul li {
		width: 210px;
		height: 280px;
		background-color: #ffffff;
		box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.2);
		border-radius: 6px;
		float: left;
		margin: 30px 30px 2px 1px;
		list-style: none;
		border: solid 1px rgba(0, 0, 0, 0);
		overflow: hidden;
	}

	.underway-Tasks ul li a {
		display: block;
		width: 100%;
		height: 100%;
		text-decoration: none;
	}

	.underway-Tasks-Logo {
		width: 100%;
		height: 50%;
		overflow: hidden;
		background-color: gray;
	}

	.underway-Tasks-Logo img {
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
	.underway-Tasks-Texe{
		box-sizing: border-box;
		padding: 0 16px;
	}

	.underway-Tasks-Texe h6 {
		font-size: 14px;
		color: #808080;
		margin: 20px 0 8px 0;
	}

	.underway-Tasks-Texe p {
		font-size: 14px;
		line-height: 26px;
		color: #808080;
		width: 100%;
		margin: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.underway-Tasks ul li:hover {
		border: solid 1px #4c72fe;
	}
	.underway-Tasks ul li:hover .underway-Tasks-Texe h6{
		color: #4c72fe;
	}
	ul{
		margin: 0;
		overflow: hidden;
		padding: 0;
	}
</style>
<div class="box">

	<div class="underway-Task">
		<div class="underway-Task-Title">
			<h6>进行中的项目</h6>
		</div>
		<div class="underway-Tasks">
			<ul>
				<li>
					<a href="">
						<div class="underway-Tasks-Logo">
							<img src="" alt="">
						</div>
						<div class="underway-Tasks-Texe">
							<h6>2020杭州机械展</h6>
							<p>展会地点：<span>杭州</span></p>
							<p>展商数量：<span>600</span>家</p>
							<p>观众登记：<span>无需登记</span></p>
						</div>
					</a>
				</li>
				<li>
					<a href="">
						<div class="underway-Tasks-Logo">
							<img src="" alt="">
						</div>
						<div class="underway-Tasks-Texe">
							<h6>2020杭州机械展</h6>
							<p>展会地点：<span>杭州</span></p>
							<p>展商数量：<span>600</span>家</p>
							<p>观众登记：<span>无需登记</span></p>
						</div>
					</a>
				</li>
			</ul>
		</div>
	</div>

	<div class="backlog-Task">
		<div class="underway-Task-Title">
			<h6>进行中的项目</h6>
		</div>
		<div class="backlog-Table">
			<div id="travel-set-reception-select-panel-1" class="easyui-panel" title="" style="width:100%;height:300pxs;padding:10px;">
				<table id="" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				},
	 				onDblClickRow:function (rowIndex, rowData) {			 					
	        				
	 				},
	 				">
					<thead>
						<tr>
							<th data-options="field:'',width:30">序号</th>
							<th data-options="field:'',width:40">任务名称</th>
							<th data-options="field:'',width:40">所属项目</th>
							<th data-options="field:'',width:40">提交公司</th>
							<th data-options="field:'',width:40">任务状态</th>
							<th data-options="field:'',width:40">操作</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>

</div>
