<!-- 展会项目信息 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>

<script src="../../js/wangEditor.min.js"></script>
<style>
    .projectSettings {
        width: 100%;
    }

    ul, li {
        list-style: none;
    }

    .project-info ul li {
        margin-top: 16px;
    }

    label {
        margin-bottom: 10px;
        display: inline-block;
    }

    .ke-container, .ke-container-default {
        height: 256px;
        width: 80% !important;
    }

    .ke-edit {
        height: 230px !important;
    }

    .datagrid-toolbar {
        background: #F4F4F4;
    }
    .project-info{
		margin-top: 15px;
		padding: 0 20px;
	}
    .project-info-img {
        width: 79%;
        margin-left: 40px;
    }

    .project-info-img img {
        width: 150px;
        height: 150px;
        overflow: hidden;
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }

    .ke-edit-iframe {
        height: 100% !important;
    }

    .product-img {
        position: relative;
        width: 320px;
        height: 224px;
        background-color: #ffffff;
        float: left;
        margin-right: 20px;
        margin-bottom: 20px;
        display: none;
    }
    .product-img img {
        display: block;
        width: 320px;
        height: 168px;
        border: solid 1px #cccccc;
    }
    .ke-content {
        color: #999;
    }
	.my-toolbar-button{
		background-color: #f5f5f5;
		padding: 6px 5px;
	}
	.booth-set{
		width: 1000px;
	}
	.booth-set>ul>li{
		    width: 50%;
		    float: left;
			margin-top: 14px;
	}
	.booth-set>ul>li>label{
		width: 30%;
		text-align: right;
		margin-right: 20px;
	}
	.booth-set-radio{
		width: 16px;
		height: 16px;
		vertical-align: bottom;
		margin-right: 8px;
	}
	.w-e-text-container{z-index: 100 !important;}
</style>
<!--引入引入kindeditor编辑器相关文件-->
<script charset="utf-8" src="${pageContext.request.contextPath}/kindeditor/kindeditor-all.js"></script>
<script charset="utf-8" src="${pageContext.request.contextPath}/kindeditor/lang/zh-CN.js"></script>
<div class="easyui-tabs" id="tab-panel" style="width:100%;height:100%;padding:5px;" data-options="tabPosition:'top'">
	<div title="展会基本信息" style="width:100%;height:100%;">
		<iframe src="" id="projectInformation_baseInfo" frameborder="0" style="width:100%;height:100%;"></iframe>
	</div>
	<div title="展会项目信息" style="width:100%;height:100%;">
		<div>
		     <div class="my-toolbar-button">
		     	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="retrieveProjectData(2)" plain="true">刷新</a>
		     	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="submitInfomation2()" plain="true">保存</a>
		     </div>
		 </div>
		<div class="project-info" id="projectInformation_project-info_projectSet">
				<ul>
		        <li class="projectSettings">
		            <label>主办方介绍:</label>
		            <!-- <input id="organizer_checkbox" type="checkbox" checked="checked">是否启用 -->
		            <div id="organizer_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
		            <div id="organizer_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
		        </li>
		        <li class="projectSettings">
		            <label>展会介绍:</label>
		            <!-- <input id="show_checkbox" type="checkbox" checked="checked">是否启用 -->
								<div id="show_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
		            <div id="show_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
		        </li>
		        <li class="projectSettings">
		            <label>展馆介绍:</label>
		            <!-- <input id="pavilion_checkbox" type="checkbox" checked="checked">是否启用 -->
								<div id="pavilion_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
		            <div id="pavilion_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
		        </li>
						<li class="projectSettings enEnable hide">
								<label>主办方介绍(英文):</label>
								<div id="organizerEn_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
								<div id="organizerEn_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
						</li>
						<li class="projectSettings enEnable hide">
								<label>展会介绍(英文):</label>
								<div id="showEn_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
								<div id="showEn_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
						</li>
						<li class="projectSettings enEnable hide">
								<label>展馆介绍(英文):</label>
								<div id="pavilionEn_editor1" style="border: 1px solid #ccc;background-color: #f1f1f1;"></div>
								<div id="pavilionEn_editor" style="border: 1px solid #ccc;min-height: 300px;"></div>
						</li>
		    </ul>
		</div>
	</div>
	<div title="客服联系方式信息" style="width:100%;height:100%;">
		<div id="">
		     <div class="my-toolbar-button">
		     	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="retrieveProjectData(3)" plain="true">刷新</a>
		     	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="customerInfoSave()" plain="true">保存</a>
		     </div>
		 </div>
		 <p style="line-height: 36px;color: #FF4444;font-size: 14px;box-sizing: border-box;padding-left: 20px;">客服联系方式通过文本框自定义编辑，尽量简洁。当输入QQ号时请以大括号包裹编写，如{QQ：123456}，并且不对QQ进行任何字体或颜色的修改</p>
		<div class="project-info" id="customerInfoText">

		</div>
	</div>
</div>

<script type="text/javascript">
	var variablePic = window.origin;
	var dataTape = [];
	const E = window.wangEditor
	const menus = [
	// 菜单配置
	'head', // 标题
	'bold', // 粗体
	'fontSize', // 字号
	'fontName', // 字体
	'italic', // 斜体
	'underline', // 下划线
	'strikeThrough', // 删除线
	'foreColor', // 文字颜色
	'backColor', // 背景颜色
	'link', // 插入链接
	'list', // 列表
	'justify', // 对齐方式
	'quote', // 引用
	'image', // 插入图片
	'undo', // 撤销
	'redo' // 重复
	];
	const configs = {
		uploadImgShowBase64: false,
		menus,
		uploadImgMaxSize: 204800,
		uploadImgServer: '${eippath}/upload/uploadFile',// 填写配置服务器端地址
		uploadFileName: 'file',
		uploadImgParams: {
			file_type: 'img'
		},
		uploadImgHooks: {
			customInsert: (insertImgFn, result, organizer) => {
				let url = result.result.path
				JSON.stringify(url)
				insertImgFn(variablePic + '/image'+url)
			}
		}
	}
	const organizer = new E('#organizer_editor1','#organizer_editor')
	Object.keys(configs).map(k => {
		organizer.customConfig[k] = configs[k];
	})
	organizer.create()
	const exProfile = new E('#show_editor1','#show_editor')
	Object.keys(configs).map(k => {
		exProfile.customConfig[k] = configs[k];
	})
	exProfile.create()
	const pavilion = new E('#pavilion_editor1','#pavilion_editor')
	Object.keys(configs).map(k => {
		pavilion.customConfig[k] = configs[k];
	})
	pavilion.create()
	const organizerEn = new E('#organizerEn_editor1','#organizerEn_editor')
	Object.keys(configs).map(k => {
		organizerEn.customConfig[k] = configs[k];
	})
	organizerEn.create()
	const exProfileEn = new E('#showEn_editor1','#showEn_editor')
	Object.keys(configs).map(k => {
		exProfileEn.customConfig[k] = configs[k];
	})
	exProfileEn.create()
	const pavilionEn = new E('#pavilionEn_editor1','#pavilionEn_editor')
	Object.keys(configs).map(k => {
		pavilionEn.customConfig[k] = configs[k];
	})
	pavilionEn.create()
	const editorbox = new E('#customerInfoText')
	Object.keys(configs).map(k => {
		editorbox.customConfig[k] = configs[k];
	})
	editorbox.create()
	$('#tab-panel').tabs({
		onSelect(title,index){
			if(title=="展会项目信息"){
			}else if(title=="客服联系方式信息"){
				// const E = window.wangEditor
				// const editorbox = new E('#customerInfoText')
				// editorbox.create()
			}
			return false;	// 阻止关闭
		}
	});
	$(function () {
		$('#projectInformation_baseInfo').attr('src','../exhibit/base_info?exhibitCode='+ exhibitCode_for_pass+ '&pid=' +project_for_pass)
		retrieveProjectData(2);
		retrieveProjectData(3);
		projectInformation_getEnSetting();
	});
	function GetQueryString(name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
			var r = window.location.search.substr(1).match(reg);
			return r != null ? unescape(r[2]) : null;
	}
	//刷新模板
	function retrieveProjectData(type) {
		var businessType = null;
		if(type == 2){//展会基本介绍
			businessType = "EXHIBITION_BASE_INFO";
		}else if(type == 3){//客服
			businessType = "CUSTOMER_SERVICE_CONTACT";
		}
		$.ajax({
			type: "POST",
			url: "${eippath}/project/information/getInformationByProjectId",
			data: {
				"projectId": GetQueryString("projectId"),
				"businessType": businessType
			},
			success: function (data) {
				if (data.state === 1) {
					datas = JSON.parse(JSON.stringify(data))
					if(type==2){
						dataTape = datas.result;
						$.each(dataTape, function (i, n) {
							//1 主办方介绍 2展会介绍 3展馆介绍 4展会图片
							if (this.type === 1) {//主办方介绍
								organizer.txt.html(this.content);
								// $("#organizer_checkbox").prop("checked", this.enable === 1);
							} else if (this.type === 2) {//2展会介绍
								exProfile.txt.html(this.content);
								// $("#show_checkbox").prop("checked", this.enable === 1);
							} else if (this.type === 3) {//3展馆介绍
								pavilion.txt.html(this.content);
								// $("#pavilion_checkbox").prop("checked", this.enable === 1);
							} else if (this.type === 1004) {
								organizerEn.txt.html(this.content);
							} else if (this.type === 1005) {
								exProfileEn.txt.html(this.content);
							} else if (this.type === 1006) {
								pavilionEn.txt.html(this.content);
							}
						});
					}else if(type==3){
						var text = ""
						$.each(datas.result,function(key,data){
							if(data.type==10){ text = data.content}
						})
						editorbox.txt.html(text)
					}
				}
			},
			error: function (err) {
			}
		});
	}
	//展会项目信息提交信息
	function submitInfomation2() {
			var formData = {};
			formData['projectId'] = GetQueryString("projectId");
			// 1 主办方介绍
			var organizer1 = organizer.txt.html();
			// var organizerEnable = $("#organizer_checkbox").prop("checked") ? 1 : 0;
			formData['organizer'] = organizer1;
			// formData['organizerEnable'] = organizerEnable;
			//2展会介绍
			var show = exProfile.txt.html();
			// var showEnable = $("#show_checkbox").prop("checked") ? 1 : 0;
			formData['show'] = show;
			// formData['showEnable'] = showEnable;
			// 3展馆介绍
			var pavilion1 = pavilion.txt.html();
			// var pavilionEnable = $("#pavilion_checkbox").prop("checked") ? 1 : 0;
			formData['pavilion'] = pavilion1;
			// formData['pavilionEnable'] = pavilionEnable;
			formData['organizerEn'] = organizerEn.txt.html();
			formData['showEn'] 			= exProfileEn.txt.html();
			formData['pavilionEn']  = pavilionEn.txt.html();
			$.ajax({
					url: "${eippath}/project/information/saveExhibitionBaseInfoSet",//原始接口：/project/information/saveItemInformation
					data: formData,
					type: "POST",
					dataType: "json",
					success: function (resp) {
							retrieveProjectData(2);
							$.messager.alert('提示', '保存成功！', 'info');
					},
					error: function (xhr, ajaxoptions, thrownError) {
							// alert(xhr.responseText);
					}
			});
	}
	function projectInformation_getEnSetting() {
		$.ajax({
		  url: "/eip-web-sponsor/project/information/getSystemEnVersionSet",
			dataType: "json",
			type: "POST",
			// async: false,
			success (data) {
				if(!data || data.state!==1){
					$.messager.alert('提示', data.message || data.msg || '请求失败！', 'error');
					return;
				}
				if(data.data) { // 启用了
					$('#projectInformation_project-info_projectSet .enEnable').removeClass('hide')
				}
			},
		});
	}

    //保存客服信息
	function customerInfoSave(){
		$.ajax({
		    url: "${eippath}/project/information/saveCustomerService",
			dataType: "json",
			type: "POST",
		    data: {
				"projectId":GetQueryString("projectId"),
				"customerService": editorbox.txt.html()
			},
		    success: function (data) {
				if(data.state==1){
					$.messager.alert('提示', '保存成功！', 'info');
				}else{
					$.messager.alert('提示', '会员中心模板保存失败！', 'error');
				}
		    },
		    error: function (xhr, ajaxoptions, thrownError) {
		        //alert(xhr.responseText);
		    }
		});
	}

</script>