<!-- 线上行业网站设置 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ include file="../common/iframe_head.jsp" %>
<!-- 静态资源版本 -->
<c:set var="v" value="250522" />
    <!-- <meta http-equiv='x-dns-prefetch-control' content='on'>
    <link rel='dns-prefetch' href='//placehold.it'> -->
	<title>PC官网模板设置 - 门户网站</title>
	<link rel="stylesheet" href="../../css/colpick.css" type="text/css" />
	<link rel="stylesheet" href="${eippath}/css/view/exhibitionTemplate5.css?${v}">
	<script>var eippath = '${eippath}'</script>
    <style>
        .pcLink-item, .pcBanner-item{
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false">
		<div style="width:100%;height:100%;">
			<div style="position: sticky; top:0; z-index: 999">
				<div class="my-toolbar-button">
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="util.fresh()" plain="true">刷新</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="util.save()" plain="true">保存</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-link" onclick="openGatewayPc('门户')" plain="true">电脑版官网地址</a>
				</div>
			</div>
			<div class="template_set_pc" style="margin-top: 20px">
                <!-- <ul>
                    <li>
                        <span class='webSetStyle webSetStyleActive' onclick='util.setLang(1,this)'>中文版网站设置</span>
                        <span class='webSetStyle' onclick='util.setLang(0,this)'>英文版网站设置</span>
                    </li>
                </ul> -->
                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this)">
                        <b>基础设置 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 20px 0 0;">
                        <li style='display:flex;align-items: center;'>
                            <div>
                                <label style="width: 100%;">PC端官网首页模板</label>
                                <div style="width: 325px;">
                                    <label color="#333">供求版</label>
                                    <!-- <a href="javascript:;" class="easyui-linkbutton upBannerStyle" onclick="util.goStandard()">切换标准版</a> -->
                                </div>
                                <!-- <select id="jsf-tpl" class="easyui-combobox" style="width:350px;height:30px">
                                    <!--option value="PCTemplate1">食品模板</option>
                                    <option value="PCTemplate2">电商模板</option>
                                    <option value="PCTemplate3">行业模板</option>
                                    <option-- value="PCTemplate4">行业模板2</option-- >
                                    <option value="PCTemplate5" selected>行业模板3</option>
                                    <option value="hideMenu">不启用</option>
                                </select> -->
                            </div>
                            <div>
                                <label style="width: 100%;">子项目背景图裁切比例(未使用)</label>
                                <select id="jsf-bgRate" class="easyui-combobox" data-options="panelHeight:100" style="width:350px;height:30px">
                                    <option value="4/3" selected>4:3</option>
                                    <option value="9/5">9:5</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <label>PC端官网色调设置</label>
                            <div class="set-color-div">
                                <label style="color: #333;">主色调</label>
                                <i class="color-box" id='jsf-color0'
                                onclick='util.setColors(this,0)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">辅色1</label>
                                <i class="color-box" id='jsf-color1'
                                onclick='util.setColors(this,1)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">辅色2 </label>
                                <i class="color-box" id='jsf-color2'
                                onclick='util.setColors(this,2)'></i>
                            </div>
                            <div class="set-color-div">
                                <label style="color: #333;">点缀色</label>
                                <i class="color-box" id='jsf-color3'
                                onclick='util.setColors(this,3)'></i>
                            </div>
                        </li>
                        <li style='display:flex;align-items: center;'>
                            <div>
                                <label style="width: 100%;">搜索引擎搜索标题</label>
                                <input id="jsf-welcome" class="easyui-textbox" data-options="prompt: '展之科技'" style="width:350px;height:30px">
                            </div>
                            <div>
                                <label style="width: 100%;">搜索关键词</label>
                                <input id="jsf-title" class="easyui-textbox" data-options="prompt: '如：展览，观众，展商申请。'" style="width:350px;height:30px">
                            </div>
                        </li>
                        <li style="color: #666;">设置搜索标题、关键词，可以让您的网站更容易被百度、必应等搜索引擎搜索到</li>
                        <!-- <li>
                            <label>注册会员调用观众登记
                                <input type="checkbox" id="websiteRegServerEnable">
                            </label>
                        </li> -->
                        <style>
                            .tpl-list-preview{ display:flex !important; }
                                .tpl-list-preview label{ width: 100%; }
                                .tpl-list-preview>div>div>div:hover,
                                .tpl-list-preview>div>div>div.hover{
                                    border-color: #95b8e7;
                                }
                                .tpl-list-preview>div>div{
                                    display:flex;
                                }
                                .tpl-list-preview>div>div>div{
                                    cursor: pointer;
                                    display:flex;align-items: center;justify-content: center;
                                    box-sizing: border-box;
                                    min-width: 93px;
                                    height: 93px;
                                    background: #fff;
                                    border: 1px solid #fff;
                                    border-radius: 4px;
                                    margin-right: 6px;
                                }
                                .tpl-list-preview img{ max-height: 79px; }
                        </style>
                        <li class="tpl-list-preview">
                            <div style="margin-right: 40px;width: 350px;">
                                <label>产品库样式<span class='showEg'>（英文版）</span></label>
                                <div id="tpl-list-preview-prod">
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-1.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-2.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/prod-preview-3.png" alt="" srcset=""></div>
                                </div>
                            </div>
                            <div>
                                <label>展商名录样式<span class='showEg'>（英文版）</span></label>
                                <div id="tpl-list-preview-corp">
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/corp-preview-1.png" alt="" srcset=""></div>
                                    <div onclick="$(this).addClass('hover').siblings().removeClass('hover')">
                                        <img src="../../img/corp-preview-2.png" alt="" srcset=""></div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this)">
                        <b>导航栏设置 <span class='showEg'>（英文版）</span></b>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <ul style="padding: 20px 0 0;">
                        <!-- <li style="margin-bottom: 0;padding-bottom: 5px;">
                            <label class="inline-block">
                                <input type="checkbox" id="jsf-cateEnable" value="1">
                                启用行业分类
                            </label>
                        </li> -->
                        <li style="margin-bottom: 0;padding-bottom: 0;">
                            <div id="bannerSetList" class="bannerSetList">
                            </div>
                        </li>
                        <li>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.bannerSetAdd()">添加更多导航栏</a>
                        </li>
                    </ul>
                </div>

                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <label></label>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <label class="module-titleLabel"><input type="checkbox" id="jsf-pcBanner-enable"><b> banner上传 <span class='showEg'>（英文版）</span></b></label>
                    <ul style="padding: 10px 0 0;">
                        <li style="margin-bottom: 0;">
                            <label>要求：banner高度最好控制在180-700之间，并 <span style="color:#FA6734">上传相应尺寸的banner图片</span>，如高度200，请上传1920*200的图片以避免样式错误。大小不超过300k。</label>
                        </li>
                        <li>
                            <label>banner高度</label>
                            <input class="easyui-numberbox" id="jsf-pcBanner-height" data-options="min:180,max:700,prompt: '180-700'" style="width:350px;height:30px">
                        </li>
                        <li class="upBannerLiStyle">
                            <div id="pcBannerList">
                            </div>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.pcBannerAdd()">上传更多banner</a>
                        </li>
                        <li id="pcBanner-inner">
                            <div>
                                <label class="tip">导航栏内页banner上传图片</label>
                                <div class="upFileDiv">
                                    <input class="easyui-textbox pcBanner-innerImgName"
                                        data-options="readonly:'true'" control='textbox' style="width:350px;">
                                    <i class="upFileDivButton">
                                        <em>上传</em>
                                        <input class="easyui-filebox upFileDivFileButton"
                                            name="upfile" data-options="buttonText:'选择',prompt:''">
                                    </i>
                                    <i class="upFileDivButton" style="margin-left: 0;" onclick="util.delImg('pcBanner-inner')">
                                        <em>删除</em>
                                    </i>
                                </div>
                            </div>
                            <div class="upFileImgDiv" style="width: 232px;height: 60px;">
                                <img src="" alt="" class="pcBanner-innerImgUrl">
                            </div>
                        </li>
                    </ul>
                </div>

                <div style="margin: 0 50px">
                    <div class="module-title flex-between" onclick="util.toggleModule(this);">
                        <label></label>
                        <span class="eu-icon icon-arrow-down"></span>
                    </div>
                    <label class="module-titleLabel"><input type="checkbox" id="jsf-pcLink-enable"><b> 广告位bannner <span class='showEg'>（英文版）</span></b></label>
                    <ul style="padding: 10px 0 0;">
                        <!-- <li style="margin-bottom: 0;">
                            <label>要求：jpg或jpeg格式，规格要求：192*76，大小不超过50k，<span style="color:#FA6734">必须填写</span>友情链接名称及地址，否则保存不上</label>
                        </li> -->
                        <li class="upBannerLiStyle">
                            <div id="pcLinkList">
                            </div>
                            <a href="#" class="easyui-linkbutton upBannerStyle" onclick="util.pcLinkAdd()">添加广告位banner</a>
                        </li>
                    </ul>
                </div>
			</div>
		</div>
	</div>
</div>
<div id="loading-over" class="over2"></div>
<div id="loading-layout" class="layout2"><img src="${eippath}/img/loading.gif" /></div>
<%@ include file="../common/project_widow_templateGateway.jsp" %>

<%@ include file="../common/window_gatewayPc.jsp" %>
<script src="../../js/colpick.js"></script>
<script src="../../js/wangEditor.min.js"></script>
<script src="../../js/view/project_templateGateway.js?${v}"></script>
</body>
</html>