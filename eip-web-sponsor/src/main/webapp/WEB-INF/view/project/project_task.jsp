<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />

<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="project-task-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="project_task_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="project_task_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-edit" onclick="project_task_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_task_del_batch()" plain="true">删除</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-view" onclick="project_task_view()" plain="true">查看</a>
            </div>
            <div class="my-toolbar-search">
            	<input id="project-task-checkbox-createOperId" type="checkbox" style="vertical-align:middle;"><label for="project-task-checkbox-createOperId">我创建的任务</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<input id="project-task-checkbox-taskState" type="checkbox" style="vertical-align:middle;"><label for="project-task-checkbox-taskState">未完成的任务</label>&nbsp;
                <label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="project-task-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select> &nbsp;&nbsp;&nbsp;&nbsp;
            	<%-- <label>任务类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-task-search-combobox-task-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/taskKind/getAll'" style="width:175px;height:25px"></select> --%>
            	<label>任务类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-task-search-combobox-task-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/taskKind/getAllByOrgnum'" style="width:175px;height:25px"></select>
            	<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="project_task_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="project-task-datagrid" class="easyui-datagrid" toolbar="#project-task-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			project_task_view_db(rowData);
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'taskKindName',width:80">任务类别</th>
					<th data-options="field:'projectName',width:120">项目名称</th>
					<th data-options="field:'taskTopic',width:120">任务主题</th>
					<th data-options="field:'startTime',width:100">开始时间</th>
					<th data-options="field:'endTime',width:100">截止时间</th>
					<th data-options="field:'memo',width:120">备注</th>
					<th data-options="field:'taskState',width:60" formatter="project_task_formatTaskState">进程</th>
					<th data-options="field:'do',width:80,align:'center'" formatter="project_task_formatDo">操作</th>
				</tr>
			</thead>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="project-task-window" class="easyui-window" toolbar="#project-task-window-toolbar" data-options="closed:true,title:'项目任务内容',modal:true" style="width:572px;">
	<!-- begin of toolbar -->
	<div id="project-task-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="project-task-add" class="easyui-linkbutton" iconCls="icon-add" onclick="project_task_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" id="project-task-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="project_task_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" id="project-task-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="project_task_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" id="project-task-save" class="easyui-linkbutton" iconCls="icon-save" onclick="project_task_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="project-task-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="project_task_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="project-task-panel-1" class="easyui-panel" title="基本信息" style="width:558px;height:150px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<%-- <li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>任务类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-task-combobox-task-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/taskKind/getAll'" style="width:175px;height:25px"></select></li> --%>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>任务类别</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-task-combobox-task-kind" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/taskKind/getAllByOrgnum'" style="width:175px;height:25px"></select></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<select id="project-task-combobox-project" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/project/getAll'" style="width:175px;height:25px"></select></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>任务主题</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-task-textbox-taskTopic" class="easyui-textbox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>开始时间</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-task-textbox-startTime" class="easyui-datetimebox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>截止时间</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-task-textbox-endTime" class="easyui-datetimebox" style="width:175px;height:25px"></li>
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>备注</label>&#12288;&#12288;&nbsp;&nbsp;&nbsp;&nbsp;<input id="project-task-textbox-memo" class="easyui-textbox" style="width:175px;height:25px"></li>
		</ul>
    </div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	var project_task_edit_mode;
	var project_task_button_edit_mode;
	var project_task_alocate_id = -1;
	var project_task_window_close_set = false;
	var project_task_save_id;
	var project_task_save_taskState;
	$(function(){
		var cnt = 0;
		$('#project-task-combobox-keyword-project').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				//$(this).combobox('select', project_for_pass);
				//$('#exhibitor-contract-combobox-keyword-project').combobox('select', '189');
				if(cnt == 0){
					//exhibitor_contract_fresh();
					cnt++;
				}

			}

		});

	});
	$(function(){
		project_task_fresh();
	});
	function project_task_formatTaskState(val, row){
		if(val == 1)return '进行中';
		else if(val == 2)return '已结束';
	}
	function project_task_formatDo(val, row){
		//return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='project_task_proceed(\"" + row.taskKindName + "\"," + row.taskId + "," + row.projectId + ")'>跟进</a>";
		return "<a href='javascript:void(0);' style='color:blue;font-weight:bold;' onclick='project_task_proceed(\"" +row.taskKindCode+"\",\""+ row.taskKindName + "\"," + row.taskId + "," + row.projectId + ")'>跟进</a>";
	}
	var project_task_projectId_for_pass = null;
	function project_task_proceed(taskKindCode,taskKindName, taskId, projectId){
		project_task_projectId_for_pass = projectId;
		if(taskKindCode == 'FASCIA'){
			closeTab("楣板管理");
			addTab("楣板管理", eippath + "/backstage/sever/sever_lintel", "icon-tip", false);
		}else if(taskKindCode == 'CATA'){
			closeTab("会刊管理");
			addTab("会刊管理", eippath + "/backstage/sever/sever_proceedings", "icon-tip", false);
		}else if(taskKindCode == 'TOOL'){
			closeTab("展具管理");
			addTab("展具管理", eippath + "/backstage/sever/sever_appliance", "icon-tip", false);
		}else if(taskKindCode == 'BUILD'){
			closeTab("搭建管理");
			addTab("搭建管理", eippath + "/backstage/sever/sever_build", "icon-tip", false);
		}else if(taskKindCode == 'LOGI'){
			closeTab("物流管理");
			addTab("物流管理", eippath + "/backstage/sever/sever_logistics", "icon-tip", false);
		}else if(taskKindCode == 'TRANS'){
			closeTab("翻译管理");
			addTab("翻译管理", eippath + "/backstage/sever/sever_translate", "icon-tip", false);
		}else if(taskKindCode == 'MEET'){
			closeTab("会议管理");
			addTab("会议管理", eippath + "/backstage/sever/sever_meeting", "icon-tip", false);
		}else if(taskKindCode == 'AIR'){
			closeTab("机票管理");
			addTab("机票管理", eippath + "/backstage/sever/sever_ticket", "icon-tip", false);
		}else if(taskKindCode == 'reception'){
			closeTab("地接管理");
			addTab("地接管理", eippath + "/backstage/sever/sever_reception", "icon-tip", false);
		}else if(taskKindCode == 'INSURE'){
			closeTab("保险管理");
			addTab("保险管理", eippath + "/backstage/sever/sever_insurance", "icon-tip", false);
		}else if(taskKindCode == 'VISA'){
			closeTab("签证管理");
			addTab("签证管理", eippath + "/backstage/sever/sever_visa", "icon-tip", false);
		}else if(taskKindCode == 'HOTEL'){
			closeTab("酒店管理");
			addTab("酒店管理", eippath + "/backstage/sever/sever_hotel", "icon-tip", false);
		}else if(taskKindCode == 'PERSON'){
			closeTab("人员管理");
			addTab("人员管理", eippath + "/backstage/sever/serve_person", "icon-tip", false);
		}else if(taskKindCode == 'COMPANY'){
			closeTab("公司介绍");
			addTab("公司介绍", eippath + "/backstage//project/companyInfo", "icon-tip", false);
		}else if(taskKindCode == 'PROD'){
			closeTab("产品介绍");
			addTab("产品介绍", eippath + "/backstage/project/productInfo", "icon-tip", false);
		}else if(taskKindCode == 'BOOTH'){
			closeTab("展具管理");
			addTab("展具管理", eippath + "/backstage/sever/sever_appliance", "icon-tip", false);
		}else if(taskKindCode == 'CERT'){
			closeTab("进馆证管理");
			addTab("进馆证管理", eippath + "/backstage/sever/sever_cert", "icon-tip", false);
		}else if(taskKindCode == 'INVITE'){
			closeTab("邀请函");
			addTab("邀请函", eippath + "/backstage/sever/sever_invitation", "icon-tip", false);
		}
	}
/* 	function project_task_proceed(taskKindName, taskId, projectId){
		project_task_projectId_for_pass = projectId;
		if(taskKindName == '楣板'){
			closeTab("楣板管理");
			addTab("楣板管理", eippath + "/backstage/sever/sever_lintel", "icon-tip", false);
		}else if(taskKindName == '会刊'){
			closeTab("会刊管理");
			addTab("会刊管理", eippath + "/backstage/sever/sever_proceedings", "icon-tip", false);
		}else if(taskKindName == '展具'){
			closeTab("展具管理");
			addTab("展具管理", eippath + "/backstage/sever/sever_appliance", "icon-tip", false);
		}else if(taskKindName == '搭建'){
			closeTab("搭建管理");
			addTab("搭建管理", eippath + "/backstage/sever/sever_build", "icon-tip", false);
		}else if(taskKindName == '物流'){
			closeTab("物流管理");
			addTab("物流管理", eippath + "/backstage/sever/sever_logistics", "icon-tip", false);
		}else if(taskKindName == '翻译'){
			closeTab("翻译管理");
			addTab("翻译管理", eippath + "/backstage/sever/sever_translate", "icon-tip", false);
		}else if(taskKindName == '会议'){
			closeTab("会议管理");
			addTab("会议管理", eippath + "/backstage/sever/sever_meeting", "icon-tip", false);
		}else if(taskKindName == '机票'){
			closeTab("机票管理");
			addTab("机票管理", eippath + "/backstage/sever/sever_ticket", "icon-tip", false);
		}else if(taskKindName == '地接'){
			closeTab("地接管理");
			addTab("地接管理", eippath + "/backstage/sever/sever_reception", "icon-tip", false);
		}else if(taskKindName == '保险'){
			closeTab("保险管理");
			addTab("保险管理", eippath + "/backstage/sever/sever_insurance", "icon-tip", false);
		}else if(taskKindName == '签证'){
			closeTab("签证管理");
			addTab("签证管理", eippath + "/backstage/sever/sever_visa", "icon-tip", false);
		}else if(taskKindName == '酒店'){
			closeTab("酒店管理");
			addTab("酒店管理", eippath + "/backstage/sever/sever_hotel", "icon-tip", false);
		}else if(taskKindName == '人员确认'){
			closeTab("人员管理");
			addTab("人员管理", eippath + "/backstage/sever/serve_person", "icon-tip", false);
		}
	} */
	function project_task_fresh(){
		$('#project-task-datagrid').datagrid({
			url: eippath + '/task/getList?key=0',
			onLoadSuccess: function(data){
				if(project_task_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].taskId == project_task_alocate_id){
							$(this).datagrid('selectRow', i);
							project_task_alocate_id = -1;
							break;
						}
					}
				}

		        $(this).datagrid("fixRownumber");
			}
		});
	}

	$.extend($.fn.datagrid.methods, {
	    fixRownumber : function (jq) {
	        return jq.each(function () {
	            var panel = $(this).datagrid("getPanel");
	            //获取最后一行的number容器,并拷贝一份
	            var clone = $(".datagrid-cell-rownumber", panel).last().clone();
	            //由于在某些浏览器里面,是不支持获取隐藏元素的宽度,所以取巧一下
	            clone.css({
	                "position" : "absolute",
	                left : -1000
	            }).appendTo("body");
	            var width = clone.width("auto").width();
	            //默认宽度是25,所以只有大于25的时候才进行fix
	            if (width > 25) {
	                //多加5个像素,保持一点边距
	                $(".datagrid-header-rownumber,.datagrid-cell-rownumber", panel).width(width + 5);
	                //修改了宽度之后,需要对容器进行重新计算,所以调用resize
	                $(this).datagrid("resize");
	                //一些清理工作
	                clone.remove();
	                clone = null;
	            } else {
	                //还原成默认状态
	                $(".datagrid-header-rownumber,.datagrid-cell-rownumber", panel).removeAttr("style");
	            }
	        });
	    }
	});

	function project_task_search(){
		console.log('开始查询任务');
		var vCreateOperId = -1, vTaskState = -1;
		if(document.getElementById('project-task-checkbox-createOperId').checked)vCreateOperId = 0;
		if(document.getElementById('project-task-checkbox-taskState').checked)vTaskState = 1;
		$('#project-task-datagrid').datagrid({
			url: eippath + '/task/getList?key=1',
			queryParams: {
				createOperId: vCreateOperId,
				taskState: vTaskState,
				projectId:$('#project-task-combobox-keyword-project').combobox('getValue'),
				taskKindCode:$('#project-task-search-combobox-task-kind').combobox('getValue')
            }
		});
	}
	function project_task_window_close(){
		$('#project-task-window').window({
			onBeforeClose: function(){
				if(project_task_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#project-task-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				//project_task_fresh();
				project_task_search();
			}
		});
	}
	function project_task_add(){
		if(!project_task_window_close_set){
			project_task_window_close();
			project_task_window_close_set = true;
		}
		$.ajax({
			url: eippath + '/task/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			contentType: "application/json",
			data: JSON.stringify({}),	//发送数据
			async: true,
			success: function(data){
				project_task_alocate_id = -1;
				$('#project-task-window').window('open');
				project_task_save_id = data.id;
				$("#project-task-textbox-taskTopic").textbox("setValue", '');
				$("#project-task-textbox-startTime").textbox("setValue", '');
				$("#project-task-textbox-endTime").textbox("setValue", '');
				$("#project-task-textbox-memo").textbox("setValue", '');
				project_task_save_taskState = 1;
				project_task_edit_mode = "Add";
				project_task_setEditInfoReadOnly(1);
				project_task_setButtonEditMode(1);
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	setInterval("getLoc()",50);
	var flag = false;
	function getLoc(){
		var rows = $('#project-task-datagrid').datagrid('getSelections');
		if (rows.length > 1){
		       	$(".row-lenght").css("opacity","0.5")
		       flag =true;
		     }else{
				 $(".row-lenght").css("opacity","1")
			   flag = false;
		     }
	}

	function project_task_mod(){
		if (flag)return;
		project_task_mod_or_view(1);
	}
	function project_task_mod_or_view(flag){
		if(!project_task_window_close_set){
			project_task_window_close();
			project_task_window_close_set = true;
		}
		var row = $('#project-task-datagrid').datagrid('getSelected');
		if (row){
			project_task_alocate_id = row.taskId;
			$('#project-task-window').window('open');
			project_task_save_id = row.taskId;
			$('#project-task-combobox-task-kind').combobox('setValue', row.taskKindCode);
			$('#project-task-combobox-project').combobox('setValue', row.projectId);
			$("#project-task-textbox-taskTopic").textbox("setValue", row.taskTopic);
			$("#project-task-textbox-startTime").textbox("setValue", row.startTime);
			$("#project-task-textbox-endTime").textbox("setValue", row.endTime);
			$("#project-task-textbox-memo").textbox("setValue", row.memo);
			project_task_save_taskState = row.taskState;
			project_task_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_task_mod2(){
		project_task_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function project_task_edit(flag){
		project_task_edit_mode = "Mod";
		project_task_setEditInfoReadOnly(flag);
		project_task_setButtonEditMode(flag);
	}
	function project_task_view(){
		if (flag)return;
		project_task_mod_or_view(2);
	}

	function project_task_view_db(row){
		if(!project_task_window_close_set){
			project_task_window_close();
			project_task_window_close_set = true;
		}
		project_task_alocate_id = row.taskId;
		$('#project-task-window').window('open');
		project_task_save_id = row.taskId;
		$('#project-task-combobox-task-kind').combobox('setValue', row.taskKindCode);
		$('#project-task-combobox-project').combobox('setValue', row.projectId);
		$("#project-task-textbox-taskTopic").textbox("setValue", row.taskTopic);
		$("#project-task-textbox-startTime").textbox("setValue", row.startTime);
		$("#project-task-textbox-endTime").textbox("setValue", row.endTime);
		$("#project-task-textbox-memo").textbox("setValue", row.memo);
		project_task_save_taskState = row.taskState;
		project_task_edit(2);
	}
	function project_task_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/task/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {taskId: project_task_save_id},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							project_task_alocate_id = -1;
							$('#project-task-window').window('close');
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function project_task_del_batch(){
		var rows = $('#project-task-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {taskId: rows[i].taskId};
			}
			//'当前选择的项目任务数为：' + rows.length +
			$.messager.confirm('提示', '确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/task/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								//project_task_fresh();
								project_task_search();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function project_task_save(){
		if($('#project-task-combobox-task-kind').combobox('getValue') == ''){
			$.messager.alert('提示','任务类别不能为空！','warning');
			return;
		}
		if($('#project-task-combobox-project').combobox('getValue') == ''){
			$.messager.alert('提示','项目名称不能为空！','warning');
			return;
		}
		var postData = {};
		postData['editMode'] = project_task_edit_mode;
		postData['taskId'] = project_task_save_id;
		postData['taskKindCode'] = $('#project-task-combobox-task-kind').combobox('getValue');
		postData['projectId'] = $('#project-task-combobox-project').combobox('getValue');
		postData['taskTopic'] = $("#project-task-textbox-taskTopic").val();
		if($("#project-task-textbox-startTime").val() != '')postData['startTime'] = $("#project-task-textbox-startTime").val();
		if($("#project-task-textbox-endTime").val() != '')postData['endTime'] = $("#project-task-textbox-endTime").val();
		postData['memo'] = $("#project-task-textbox-memo").val();
		postData['taskState'] = project_task_save_taskState;
		$.ajax({
			url: eippath + '/task/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					project_task_edit(2);
					project_task_alocate_id = postData['taskId'];
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function project_task_cancel(){
		$('#project-task-window').window('close', true);
	}
	//设置编辑页面可编辑属性
	function project_task_setEditInfoReadOnly(flag){
		if(flag == 1){
			$('#project-task-combobox-task-kind').combobox('enable');
			$('#project-task-combobox-project').combobox('enable');
			$('#project-task-textbox-taskTopic').textbox('textbox').attr('readonly', false);
			$('#project-task-textbox-startTime').textbox('enable');
			$('#project-task-textbox-endTime').textbox('enable');
			$('#project-task-textbox-memo').textbox('textbox').attr('readonly', false);
		}else if(flag == 2){
			$('#project-task-combobox-project-kind').combobox('disable');
			$('#project-task-combobox-project').combobox('disable');
			$('#project-task-textbox-taskTopic').textbox('textbox').attr('readonly', true);
			$('#project-task-textbox-startTime').textbox('disable');
			$('#project-task-textbox-endTime').textbox('disable');
			$('#project-task-textbox-memo').textbox('textbox').attr('readonly', true);
		}
	}
	//按钮有效 flag 1 编辑 2 查看
	function project_task_setButtonEditMode(flag){
		if(flag == 1){
			$("#project-task-add").linkbutton('disable');
			$("#project-task-mod2").linkbutton('disable');
			$("#project-task-del").linkbutton('disable');
			$("#project-task-save").linkbutton('enable');
			$("#project-task-cancel").linkbutton('enable');
		}else if(flag == 2){
			$("#project-task-add").linkbutton('enable');
			$("#project-task-mod2").linkbutton('enable');
			$("#project-task-del").linkbutton('enable');
			$("#project-task-save").linkbutton('disable');
			$("#project-task-cancel").linkbutton('disable');
		}
		project_task_button_edit_mode = flag;
	}
</script>


<!--
<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
	</style>
<table id="project_task-datagrid" class="easyui-datagrid" 	 toolbar="#project_task_tab"
data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
</table>
<div id="project_task_tab">

	<form action=""  style="float: left;margin-top: 5px;">
	<label><input name="Fruit" type="checkbox" value="" />所有任务 </label>
	<label><input name="Fruit" type="checkbox" value="" />我创建的任务 </label>
	<label><input name="Fruit" type="checkbox" value="" />未完成的任务 </label>
	<label><input name="Fruit" type="checkbox" value="" />所有任务</label>
	</form>
	<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >查询</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="$('#w').window('open')">新增</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-edit" plain="true"  onclick="$('#w').window('open')">修改</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true"  onclick="$('#w').window('open')">删除</a>
</div>



	<div id="w" class="easyui-window" title="新增任务" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:500px;height:auto;padding:10px;">
	<div style="margin:20px 0;"></div>
	<div style="width:100%;max-width:400px;">
		<div style="margin-bottom:20px">
			<label for="username" class="label-top">名称:</label>
			<input id="username" class="easyui-validatebox tb" data-options="prompt:'Enter User Name.',required:true,validType:'length[3,10]'">
		</div>
		<div style="margin-bottom:20px">
			<label for="email" class="label-top">类别:</label>
			<input id="email" class="easyui-validatebox tb" data-options="prompt:'Enter a valid email.',required:true,validType:'email'">
		</div>
		<div style="margin-bottom:20px">
			<label for="url" class="label-top">时间:</label>
			<input id="url" class="easyui-validatebox tb" data-options="prompt:'Enter your URL.',required:true,validType:'url'">
		</div>
		<div style="margin-bottom:20px">
			<label for="phone" class="label-top">地点:</label>
			<input id="phone" class="easyui-validatebox tb" data-options="prompt:'Enter your phone number.',required:true">
		</div>
	</div>

	<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">确认</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">关闭</a>


	</div>
	-->
<script>
		$(function(){
		//	project_task()
		});


		function project_task(){
			//获取用户列表
			    $("#project_task-datagrid").datagrid({
			    	url:eippath+ '/data/data.json',
			    	method:'post',
			        queryParams://每次请求的参数
			            {
			                cmd: 'demo',
			                strWhere: 'test',
			                key:'0',
			            },
			        pagination:true,
			        multiSort:true,
			        fitColumns:true,
			        fit:true,
			        pagination: true,//允许分页
			        rownumbers: true,//行号
			        singleSelect: false,//只选择一行
			        pageSize: 20,//每一页数据数量
			        checkOnSelect: false,
			        pageList: [20,30],
			        columns: [[{
			            field: 'f_id',
			            checkbox: true,
			        },
			        {
			            field: "f_task_kind_name",//关联 t_task_kind表f_task_kind_code字段
			            title: "任务类别",
			            align: "center",
			            width: 50
			        }, {
			            field: "f_exihibit_name", //  关联t_exhibiti (展会表)f_project_id（字段）
			            title: "项目名称",
			            align: "center",
			            width: 100
			        }, {
			            field: "f_task_topic",
			            title: "任务主题",
			            align: "center",
			            width: 100,
			        }, {
			            field: "f_start_time",
			            title: "开始时间",
			            align: "center",
			            width: 100,
			            formatter: function (val, row) {
		                	return "2018-8-18 09:30:00";

			            }
			        }, {
			            field: "f_end_time",
			            title: "截止时间",
			            align: "center",
			            width: 80,
			            formatter: function (val, row) {

			            	return "2018-8-18 09:30:00";
			            }
			        }, {
			            field: "f_memo",
			            title: "备注",
			            align: "center",
			            width: 80,

			        },  {
			            field: "f_task_kind_code",
			            title: "操作",
			            align: "center",
			            width:50,
			            formatter: function (val, row) {
			            	  return "<a href='javascript:void(0);' style='color:blue;'  onclick='project_task_open("+val+")'>跟进</a>";
			            }
			        }
			        ]],

			        //点击每一行的时候触发
			        //onClickRow: function (rowIndex, rowData) {
			        //    alert(rowData["UserId"]);
			        //}
			    });
		}
		function project_exhibitor(){
			addTab("展商服务管理中心", eippath + "/backstage/project/exhibitor_service", "icon-tip", false);
		}
		function project_task_open(value){
			switch(value){
			case 1:
			  break;
			case 2:
				console.log("yes");
			  break;
			case 3://楣板
				addTab("楣板管理中心", eippath + "/backstage/project/project_lintel", "icon-tip", false);
				  break;
			case 4://会刊
				addTab("会刊管理中心", eippath + "/backstage/project/project_proceedings", "icon-tip", false);
				  break;
			default:
			}
		}


	</script>