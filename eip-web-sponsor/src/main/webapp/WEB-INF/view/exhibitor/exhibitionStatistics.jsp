<!--展具统计汇总  -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
	fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
	fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
	fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
	fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
	fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
		else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
		else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
		else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
		else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
		else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
		else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
		else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
		else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
		else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
		else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
	}
%>
<%@ include file="../common/project.jsp" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style scoped="scoped">
    .tb{
        width:100%;
        margin:0;
        padding:5px 4px;
        border:1px solid #ccc;
        box-sizing:border-box;
    }
</style>
<style>
.sever-exhibitionStatistics-datagrid {
    height: calc(100% - 80px) !important;
}
.sever-exhibitionStatistics-datagrid .datagrid-header-row .datagrid-cell>span {
    white-space: normal !important;
    word-wrap: normal !important;
}
/* .sever-exhibitionStatistics-datagrid .datagrid-body .sumTable td{ */
#sever-exhibitionStatistics-total .sumTable td{
    border-width: 1px 1px 0 0;height: 40px;
}
.sever-exhibitionStatistics-datagrid .datagrid-body{
    position: relative;
}
.sever-exhibitionStatistics-datagrid .datagrid-row-selected td{
    background-color: #ffe48d !important;
}
</style>
<div style="background: #F4F4F4;">

    <div class="my-toolbar-button">
        <!-- <a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="sever_exhibitionStatistics_search()">刷新</a> -->
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
        <!-- 	<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  >新增</a>
            <a href="#" class="easyui-linkbutton" iconCls="icon-edit" plain="true"  >修改</a> -->
        <a href="javascript:void(0);"  class="easyui-linkbutton" iconCls="icon-download-all" plain="true"  onclick="sever_exhibitionStatistics_pre_export()">导出</a>
    </div>
    <div class="my-toolbar-search">
        <div style="display: none">
            <span style="display:inline-block; width: 96px;">项目名称：</span>
            <input id="sever-exhibitionStatistics-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px">
            <a href="javascript:void(0);" class="easyui-linkbutton"
               style="width:35px;height:25px" onclick="exhibitor_popup_project($('#sever-exhibitionStatistics-combobox-keyword-project'))">...</a>&nbsp;&nbsp;&nbsp;&nbsp;
        </div>
		<div style="float: left;margin-right: 10px; display: none;">
			<span style="display:none; width: 70px;">公司名称</span>
			<input  id="sever-exhibitionStatistics-keyword-company" class="easyui-textbox"  style="width:125px;height:25px;display: none">&nbsp;&nbsp;&nbsp;&nbsp;
		</div>
		<div style="width: 220px;float: left; display: none;" id="is_fu_section_graph_set_kywy13">
			<span style="display:inline-block; width: 70px;">客服专员</span>
			<select id="sever-exhibitionStatistics-keyword-customerServiceId13" class="easyui-combobox"
			data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'"
			style="width:125px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
		</div>
		<div style="display: none;">
			<span style="display:inline-block; width: 50px;">展位号</span>
			<input  id="sever-exhibitionStatistics-keyword-boothNum" class="easyui-textbox"  style="width:125px;height:25px;">&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="display:inline-block; width: 70px;">展具名称</span>
			<input  id="sever-exhibitionStatistics-keyword-toolName" class="easyui-textbox"  style="width:125px;height:25px;">&nbsp;&nbsp;&nbsp;&nbsp;
		</div>
        <div style="display: inline-block;">
            <label>审核时间</label>&nbsp;&nbsp;
            <input id="sever-exhibitionStatistics-keyword-start" class="easyui-datetimebox" style="width:170px;height:25px">
                -
            <input id="sever-exhibitionStatistics-keyword-end" class="easyui-datetimebox" style="width:170px;height:25px">
        </div>
        <script>
        $(function() {
            $("#sever-exhibitionStatistics-keyword-start").datetimebox({
                onShowPanel(){
                    $("#sever-exhibitionStatistics-keyword-start")
                    .datetimebox("spinner")
                    .timespinner("setValue","00:00:00")
                },
            });
            $("#sever-exhibitionStatistics-keyword-end").datetimebox({
                onShowPanel(){
                    $("#sever-exhibitionStatistics-keyword-end")
                    .datetimebox("spinner")
                    .timespinner("setValue","23:59:59")
                },
            });
        })
        </script>
        <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="sever_exhibitionStatistics_search()">查询</a>
        <div style="clear: both;"><!-- 不要删掉:bug:10165 --></div>
    </div>
</div>
<div class="easyui-panel" style="height:91%;padding:0px;">
    <table id="sever-exhibitionStatistics-datagrid" class="easyui-datagrid" style="height:calc(100% - 40px)"
           data-options="cls:'sever-exhibitionStatistics-datagrid',rownumbers:true,singleSelect:false,pageSize:20,pagination:false,multiSort:true,fit:true,method:'post',selectOnCheck:true,
           rowStyler(index,row){
                if (0 === row.taskDtlState){
                    return 'color: #888;';
                }
            },
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},"
           toolbar="#sever-exhibitionStatistics-toolbar">
        <thead>
            <tr>
                <!-- <th data-options="field:'',checkbox:true"></th> -->
                <th field="companyName" width="80" align="center">公司名称</th>
                <th field="boothArea" width="60" align="center">面积</th>
                <th field="boothNum" width="80" align="center">展位号</th>
                <th field="fasciaName" width="80" align="center">楣板</th>

            </tr>
        </thead>
    </table>
    <div id="sever-exhibitionStatistics-total" style="height: 40px;line-height: 40px;overflow: auto;">
    </div>
    <div id="sever-exhibitionStatistics-pager" class="easyui-pagination" style="background:#efefef;border:1px solid #ccc;"></div>
</div>
    <!-- <table id="serve-person-datagrid" class="easyui-datagrid" toolbar="#serve-person-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,
        		fitColumns:false,fit:true,method:'post',selectOnCheck:true,autoRowHeight:true,nowrap:false,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},onDblClickRow:function (rowIndex, rowData) {

	        					serve_person_view_db(rowData);
	 						},">
        	<thead> -->
<!-- <div id="meibanhz" class="easyui-window" title="楣板确认" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:500px;height:auto;padding:10px;">
<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#meibanhz').window('close')">关闭</a>
</div> -->


<!-- 导出选择弹窗 -->
<style>
	.sever-exhibitionStatistics-export-body{
		padding:20px
	}
	.sever-exhibitionStatistics-export-body>*{
		cursor: pointer;
	}
	.sever-exhibitionStatistics-export-window .toolbar-right{
		display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse;
	}
    #sever-exhibitionStatistics-export-tip {
		color: black;
        line-height: 30px;
        margin: -10px 0 4px 4px;
		font-size: 15px;
	}
	.fix-easyui-warning .messager-question{
		background-position: -97px 0 !important;
	}
</style>
<div id="sever-exhibitionStatistics-export-window"
 data-options="closed:true,modal:true,title:'选择导出内容',width:330
 ,collapsible:false,minimizable:false,maximizable:false"
 style="display: none;">
	<div class="my-toolbar-button datagrid-toolbar toolbar-right-">
        <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-save"
          onclick="sever_exhibitionStatistics_export(this)" plain="true">确定</a>
	  <a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-cancel"
		onclick="sever_exhibitionStatistics_export_close()" plain="true">取消</a>
	</div>
	<div class="sever-exhibitionStatistics-export-body">
        <div id="sever-exhibitionStatistics-export-tip">已选择<span></span>条数据, 请选择导出方式</div>
		<input name="sever-exhibitionStatistics-export-radio" id="sever-exhibitionStatistics-export-excel" type="radio" value="1" checked>
		<label for="sever-exhibitionStatistics-export-excel">当前excel</label> &nbsp;&nbsp;&nbsp;
		<input name="sever-exhibitionStatistics-export-radio" id="sever-exhibitionStatistics-export-excelExtra" type="radio" value="2">
		<label for="sever-exhibitionStatistics-export-excelExtra">当前excel和附件</label>
	</div>
</div>
<script>
    var f_org_num = '${sessionScope.f_org_num}';
    // $(function(){
    //     $('input.easyui-validatebox').validatebox({
    //         validateOnCreate: false,
    //         err: function(target, message, action){
    //             var opts = $(target).validatebox('options');
    //             message = message || opts.prompt;
    //             $.fn.validatebox.defaults.err(target, message, action);
    //         }
    //     });
    // });

    function sever_exhibitionStatistics_export_close() {
        $('#sever-exhibitionStatistics-export-window').window('close')
    }

    // 预导出: 选择导出内容
    window.g_server_exhibitionStatistics_export_rows = '';
    function sever_exhibitionStatistics_pre_export() {
        const rows = $('#sever-exhibitionStatistics-datagrid').datagrid('getSelections') || [];
	    g_server_exhibitionStatistics_export_rows = rows;
        if(!rows || rows.length<1) {
            g_server_exhibitionStatistics_export_rows = '';
            $('#sever-exhibitionStatistics-export-tip span').html($('#sever-exhibitionStatistics-pager').pagination('options').total);
            $.messager.confirm({
                title: '提示',
                msg: '您未选择数据, 是否导出全部数据 ?',
                fn(r) {
                    if(r) {
                        $('#sever-exhibitionStatistics-export-window').window({
                            closed:false,
                            cls:'sever-exhibitionStatistics-export-window',
                            onBeforeOpen: function() {
                                let w = $('.sever-exhibitionStatistics-export-window')
                                if(w.length>1){
                                    for (let i = 0; i < w.length-1; i++) {
                                        w[i].remove()
                                    }
                                }
                            }
                        })
                    }
                }
            }).window({width: 400,cls: 'fix-easyui-warning'})
        } else {
            $('#sever-exhibitionStatistics-export-tip span').html(rows.length);
            $('#sever-exhibitionStatistics-export-window').window({
                closed:false,
                cls:'sever-exhibitionStatistics-export-window',
                onBeforeOpen: function() {
                    let w = $('.sever-exhibitionStatistics-export-window')
                    if(w.length>1){
                        for (let i = 0; i < w.length-1; i++) {
                            w[i].remove()
                        }
                    }
                }
            }).window('center');
        }

    }
    function sever_exhibitionStatistics_export(obj) {
        if(f_org_num==null||f_org_num==''){
            $.messager.alert('提示','获取登录信息失败，请重新登录','warning');
            return;
        }
        function getRadioValue(obj,radioName) {
            var chkRadio = $('#sever-exhibitionStatistics-export-window :radio[name='+radioName+']')
            // var chkRadio = $(obj).parent().parent().find(':radio[name='+radioName+']')
            // var chkRadio = document.getElementsByName(radioName);
            for (var i = 0; i < chkRadio.length; i++) {
                if (chkRadio[i].checked)
                return chkRadio[i].value;
            }
        }
        var exportFlag = getRadioValue(obj,'sever-exhibitionStatistics-export-radio');
        if(exportFlag == '1' || exportFlag == '2'){
            sever_exhibitionStatistics_download(exportFlag)
            sever_exhibitionStatistics_export_close()
        }else{
            $.messager.alert('提示','请选择导出方式','warning');
            return
        }
    }
    function sever_exhibitionStatistics_search(){
        if(f_org_num==null||f_org_num==''){
            $.messager.alert('提示','获取登录信息失败，请重新登录','warning');
            return;
        }
        if($('#sever-exhibitionStatistics-combobox-keyword-project').combobox('getValue') == ''){
            alert("请选择项目名称");
            return;
        }
        sever_exhibitionStatistics_datagrid_load()
        // initExhibitions()
    }
    //改变一列中 内容的颜色
    function stylerone(value, row, index) {
        return 'background-color:#ffe48d;';//  'color:#ffe48d';
    }
    // 显示的列
    let exhibitionStatColumns = []
    // 额外显示的列
    var exhibitionStatExtraColumns = new Map()
    function initExhibitions(){
        if(f_org_num==null||f_org_num==''){
            $.messager.alert('提示','获取登录信息失败，请重新登录','warning');
            return;
        }
        $.ajax({
            // url: eippath + '/serveTool/getExhibitions',
            url: eippath + '/serveTool/getDynamicToolList',
            type:"post",
            async:false,
            datatype:'json',
            // data:{ exhibitCode: exhibitCode_for_pass,f_project_id:project_for_pass },
            data:{ exhibitCode: exhibitCode_for_pass},
            success:function(data){
                exhibitionStatColumns = [
                    {field:"_ck",checkbox:true },
                    {field:"companyName",title: '公司名称',width:200 },
					{field:"companyNameEn",title: '公司英文名',width:200 },
					{field:"ownerName",title: '业绩归属人',width:80 },
					{field:"customerServiceName",title: '客服专员',width:80 },
                    {field:"checkOperName",title: '审核人',width:80 },
                    {field:"checkTime",title: '审核时间',width:150 },
					{field:"sectionName",title: '展馆',width:100 },
					{field:"boothNum",title: '展位号',width:100 },
                    {field:"boothArea",title: '面积',width:80,align: 'right' },
					{field:"openInfoName",title: '开口情况',width:80 },
                    {field:"fasciaName",title: '楣板',width:100 },
                    {field:"toolTotalPrice",title: '展具费用合计',width:120,formatter: function(value,row,index){
                        return row.toolTotalPrice ? (+row.toolTotalPrice || 0).toFixed(2) : ''
                    },align: 'right'},
                ];
                exhibitionStatExtraColumns = new Map()
                console.log('column data ',data)
                data.rows.forEach(item=>{
                    // console.log(item);
                    let itemDtl={};
                    let isDefault = item.isDefault;
                    //bug 8458 把item.toolCode变成item.toolId
                    itemDtl['field']=item.toolId+(isDefault?"isDefault":'')
                    itemDtl['title']=item.toolName+""+item.toolType
                    itemDtl['width']=80
                    itemDtl['styler'] = function(value,row,index){
                        return 'background-color:#f9f9f9;';
                    }
                    // itemDtl['width']='auto'
                    //默认添加背景颜色
                    if (isDefault) itemDtl['styler']=stylerone
                    exhibitionStatExtraColumns.set(itemDtl['field'],itemDtl)
                })
                sever_exhibitionStatistics_datagrid_load();
                // init pager
                let $pg = $('#sever-exhibitionStatistics-pager')
                $pg.pagination({
                    // total: datas.total,
                    pageSize: 20,
                    // pageList: [10,20,30,40,50],
                    pageList: [10,20,50,100,200,500],
                    pageNumber: 1,
                    onSelectPage:function(pageNumber, pageSize){
                        $pg.pagination('loading');
                        // console.log('pageNumber:'+pageNumber+',pageSize:'+pageSize);
                        sever_exhibitionStatistics_datagrid_load(pageNumber,pageSize)
                        $pg.pagination('loaded');
                    }
                });
            }
        });
    }
    function sever_exhibitionStatistics_datagrid_load(page=1,rows=20){
        let $dg = $('#sever-exhibitionStatistics-datagrid')
        $dg.datagrid('loading')
        let $pg = $('#sever-exhibitionStatistics-pager')
        let param = {
            checkTimeStart:$('#sever-exhibitionStatistics-keyword-start').datetimebox('getValue'),
            checkTimeEnd:$('#sever-exhibitionStatistics-keyword-end').datetimebox('getValue'),
        	exhibitCode:exhibitCode_for_pass,
            f_project_id: $('#sever-exhibitionStatistics-combobox-keyword-project').combobox('getValue'),
            f_company_name:$("#sever-exhibitionStatistics-keyword-company").textbox("getValue"),
            customerServiceId:$('#sever-exhibitionStatistics-keyword-customerServiceId13').combobox('getValue'),
            f_booth_num:$("#sever-exhibitionStatistics-keyword-boothNum").textbox("getValue"),
            // f_confirm:4
            page,
            rows
        }
        // let datas
        let flag = $.ajaxSettings.async
        $.ajaxSettings.async = true
        $.post(eippath + '/serveTool/getExhibitionStatistics',param,function(data,status,xhr){
            // $('#sever-exhibitionStatistics-datagrid').datagrid(options)
            data.rows =  data.rows || [];
            let extraCols = new Map()
            // console.log('data',$(this).datagrid('getData'))
            Object.keys(data.data || {}).map(k => {
                if(exhibitionStatExtraColumns.has(k) && !extraCols.has(k)) {
                    extraCols.set(k,exhibitionStatExtraColumns.get(k))
                }
            });
            data.rows.forEach(item => {
                Array.from(extraCols).forEach(it => {
                   if(!item.hasOwnProperty(it)) item[it] = '';
                })
            })
            // data.rows.forEach(item => {
            //     for(let k in item){
            //         if(exhibitionStatExtraColumns.has(k) && !extraCols.has(k) && (item[k] || item[k] !== 0)){
            //             extraCols.set(k,exhibitionStatExtraColumns.get(k))
            //         }
            //     }
            // })
            let cols = [];
            Array.from(extraCols).forEach(it => {
                if(it[0].endsWith('isDefault')){
                    cols.push(it[1])
                }
            })
            Array.from(extraCols).forEach(it => {
                if(!it[0].endsWith('isDefault')){
                    cols.push(it[1])
                }
            })
            const options = {
                columns: [exhibitionStatColumns.concat([...cols])],
                // columns: [exhibitionStatColumns.concat([...extraCols.values()])],
                onLoadSuccess: function() {
                    $dg.datagrid('loaded')
                    $pg.pagination('loaded')
                }
            }
            // 8列内铺满
            if(extraCols.size < 8){
                options.fitColumns = true
                // options.scrollbarSize = 0
            }else{
                options.fitColumns = false
                // options.scrollbarSize = 0
            }
            $dg.datagrid(options).datagrid('loadData',data).datagrid('loaded')

            const all = data.data || {}
            const tr = $('.sever-exhibitionStatistics-datagrid').find('.datagrid-header-inner:eq(1) tr:eq(0)').clone()
            tr.find('td').each((idx,it)=>{
                let field = options.columns[0][idx].field
                if(field === 'toolTotalPrice'){
                    $(it).find('.datagrid-cell').html('<span>'+ (+all['allToolTatolPrice'] || 0).toFixed(2) +'</span>');
                }  else {
                    $(it).find('.datagrid-cell').html('<span>'+ (idx ? (all[field] || '') : '总计') +'</span>');
                }
            })
            $('#sever-exhibitionStatistics-total').html('<table class="datagrid-footer datagrid-btable sumTable" cellspacing="0" cellpadding="0" border="0" style=""><tr><td class="datagrid-td-rownumber"><div class="datagrid-cell-rownumber"></div></td>' + tr.html() + '</tr></table>')
            // const $body = $('.sever-exhibitionStatistics-datagrid').find('.datagrid-body:eq(1)');
            // if($body.find('table.sumTable') && $body.find('table.sumTable').length) {
            //     $body.find('.sumTable').html('<tr>' + tr.html() + '</tr>')
            // } else {
            //     $body.append('<table class="datagrid-btable sumTable" cellspacing="0" cellpadding="0" border="0"  style="position:absolute;bottom:0;left:0;"><tr>' + tr.html() + '</tr></table>')
            // }
            $pg.pagination({
                total: data.total,
                pageNumber: page,
                pageSize: rows,
            })
        },'json');
        $.ajaxSettings.async = flag
        // return datas
    }

    $(function(){
        setTimeout(function(){
            $('#sever-exhibitionStatistics-combobox-keyword-project').combobox({
                valueField: 'id',
                textField: 'text',
                panelHeight: '200px',
                data: [{'id':project_for_pass,'text':exihibit_name_for_pass}],
                onLoadSuccess: function(data){
                    $(this).combobox('select', project_for_pass);
                    initExhibitions()
                    // sever_exhibitionStatistics_search();
                }
            });
        }, 100);
		var jurisdictions13 = <%=fu_section_graph_set_kywy%> ;
		if(jurisdictions13){
			$("#is_fu_section_graph_set_kywy13").show()
		}else{
			$("#is_fu_section_graph_set_kywy13").hide()
		}
    });

    // function exhibit_have_tools_formatToolPic(val, row){
    //     if(val != null && val != '')
    //         return '<image src=\"http://' + location.hostname+':'+location.port + '/image/' + val + '\" style="width:100px;height:100px" />';
    // }

   /*===================post请求下载文件
    * options:{
    * url:'',  //下载地址
    * data:{name:value}, //要发送的数据
    * method:'post'
    * }
    */
    var postExhibitionStatisticsDownLoadFile = function (options) {
        var config = $.extend(true, { method: 'post' }, options);
        var $iframe = $('<iframe id="down-file-iframe" />');
        var $form = $('<form target="down-file-iframe" method="' + config.method + '" />');
        $form.attr('action', config.url);
        for (var key in config.data) {
            $form.append('<input type="hidden" name="' + key + '" value="' + config.data[key] + '" />');
        }
        $iframe.append($form);
        $(document.body).append($iframe);
        $form[0].submit();
        $iframe.remove();
    }

    function sever_exhibitionStatistics_download(exportExcelFlag) {
        // var param = {
        //     exportExcelFlag
        // };
        // param['f_project_id']= $('#sever-exhibitionStatistics-combobox-keyword-project').combobox('getValue'),
        // param['f_company_name']= $("#sever-exhibitionStatistics-keyword-company").textbox("getValue"),
        // param['f_booth_num']= $("#sever-exhibitionStatistics-keyword-boothNum").textbox("getValue"),
        // param['customerServiceId']= $("#sever-exhibitionStatistics-keyword-customerServiceId13").combobox("getValue"),
        // param['f_exhibit_code']= exhibitCode_for_pass;
        // param['exhibitCode']= exhibitCode_for_pass;
        // param['f_confirm']= 4;  // 0未启动，1启动，2草稿，3已提交，4审核 ，9驳回
        // postExhibitionStatisticsDownLoadFile({
        //     url:eippath + '/serveTool/exportExhibitionStatistics',
        //     data:param,
        //     method:'post'
        // });
        sever_exhibitionStatistics_download_helper(exportExcelFlag);
    }

    // function sever_exhibitionStatistics_formatIsDefault(val, row){
    //     return val==1?'默认': '增租';
    // }
    // function sever_exhibitionStatistics_datagrid_download() {
    //     var  pid =  $('#sever-exhibitionStatistics-combobox-keyword-project').combobox('getValue');
    //     if(pid == ''){
    //         alert("请选择项目名称");
    //         return;
    //     }
    //     $.ajax({
    //         url: eippath + '/serveTool/getSTDList?key=1',
    //         type:"post",
    //         async:false,
    //         datatype:'json',
    //         data:{f_project_id:pid,
    //             f_company_name:$("#sever-exhibitionStatistics-keyword-company").textbox("getValue"),
    //             f_booth_num:$("#sever-exhibitionStatistics-keyword-boothNum").textbox("getValue"),
    //             f_tool_name:$("#sever-exhibitionStatistics-keyword-toolName").textbox("getValue"),
    //             f_person:$("#sever-exhibitionStatistics-keyword-person").textbox("getValue"),
    //             page:1,rows:999999
    //             ,f_confirm:4
    //         },
    //         xhrFields:{withCredentials:true},
    //         success:function(data){
    //             var arr= eval('('+data+')');
    //             var  rows = arr.rows;
    //             var total = arr.total;
    //             if(total > 0){
    //                 // console.log(JSON.stringify(rows));
    //                 JSONToCSVConvertorZhanJu(rows, "Download", true);
    //             }else{
    //                 $.messager.alert('提示','当前项目没有数据！','warning');
    //             }

    //         },error:function(data){

    //             return;
    //         }
    //     });

    // }

    // function JSONToCSVConvertorZhanJu(JSONData, ReportTitle, ShowLabel) {
    //     //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
    //     /* 	var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData)
    //                 : JSONData;
    //          for (var i = 0; i < arrData.length; i++) {
    //                  delete arrData[i].f_tool_detial_id;
    //                 delete arrData[i].f_sever_tool_id;
    //                 delete arrData[i].f_tool_id;
    //                 delete arrData[i].f_is_recovery;
    //                 //delete arrData[i].f_is_default;
    //                 delete arrData[i].f_onsite_price;
    //                 delete arrData[i].f_default_name;
    //                 delete arrData[i].f_serve_booth_id;
    //                 delete arrData[i].f_exhibit_code;
    //                 delete arrData[i].f_client_id;
    //                 delete arrData[i].f_project_id;
    //                 delete arrData[i].f_section_code;
    //                 delete arrData[i].f_project_name;
    //                 delete arrData[i].f_org_num;
    //                 delete arrData[i].f_tool_pic;
    //         }  */
    //     var arrDataOld = typeof JSONData != 'object' ? JSON.parse(JSONData)
    //         : JSONData;
    //     var arrData = [];
    //     for (var i = 0; i < arrDataOld.length; i++) {
    //         var isDefault = '';
    //         if(arrDataOld[i].f_is_default == 1) isDefault = '默认';
    //         else isDefault = '增租';
    //         var str = {
    //             f_company_name:arrDataOld[i].f_company_name,
    //             f_section_name:arrDataOld[i].f_section_name,
    //             f_booth_num:arrDataOld[i].f_booth_num,
    //             f_tool_code:arrDataOld[i].f_tool_code,
    //             f_tool_name:arrDataOld[i].f_tool_name,
    //             f_tool_type:arrDataOld[i].f_tool_type,
    //             f_preorder_price:arrDataOld[i].f_preorder_price,
    //             f_tool_amount:arrDataOld[i].f_tool_amount,
    //             f_total_price:arrDataOld[i].f_total_price,
    //             f_is_default:isDefault,
    //             f_person:arrDataOld[i].f_person,
    //             f_exihibit_name:arrDataOld[i].f_exihibit_name,
    //             memo:arrDataOld[i].f_memo
    //         }
    //         arrData.push(str);
    //     }
    //     var CSV = '';
    //     //Set Report title in first row or line
    //     /* CSV += ReportTitle + '\r\n\n'; */
    //     //This condition will generate the Label/Header
    //     if (ShowLabel) {
    //         var row = "";
    //         //This loop will extract the label from 1st index of on array
    //         for ( var index in arrData[0]) {
    //             //Now convert each value to string and comma-seprated
    //             row += index + ',';
    //         }
    //         row = row.slice(0, -1);
    //         row = row
    //             //.replace("serveFasciaId","序号")
    //             .replace("f_company_name","公司名称")
    //             .replace("f_section_name","展馆")
    //             .replace("f_booth_num","展位号")
    //             .replace("f_tool_code","展具编号")
    //             .replace("f_tool_name","展具名称")
    //             .replace("f_tool_type","规格")
    //             .replace("f_preorder_price","单价")
    //             .replace("f_tool_amount","数量")
    //             .replace("f_total_price","总价")
    //             .replace("f_is_default","展具性质")
    //             .replace("f_person","业务员")
    //             .replace("f_exihibit_name","展会名称")
    //             .replace("f_memo","备注");
    //         console.log(row);
    //         //append Label row with line break
    //         CSV += row + '\r\n';
    //     }
    //     //1st loop is to extract each row
    //     for (var i = 0; i < arrData.length; i++) {
    //         var row = "";
    //         //2nd loop will extract each column and convert it in string comma-seprated
    //         for ( var index in arrData[i]) {
    //             row += '"' + arrData[i][index] + '",';
    //         }
    //         row.slice(0, row.length - 1);
    //         //add a line break after each row
    //         row = row.replace(/"null"/g,"").replace(/#/g,"×");
    //         console.log(row);
    //         CSV += row + '\r\n';
    //     }
    //     if (CSV == '') {
    //         alert("Invalid data");
    //         return;
    //     }
    //     var mathstr = Math.random();
    //     //Generate a file name
    //     var fileName = "展具明细导出数据-"+moment().format('YYYY年MM月DD日');
    //     //this will remove the blank-spaces from the title and replace it with an underscore
    //     fileName += ReportTitle.replace(/ /g, "_").replace("Download","");
    //     console.log(fileName);
    //     //Initialize file format you want csv or xls
    //     var uri = 'data:text/csv;charset=utf-8,\uFEFF' + encodeURI(CSV);
    //     // Now the little tricky part.
    //     // you can use either>> window.open(uri);
    //     // but this will not work in some browsers
    //     // or you will not get the correct file extension
    //     //this trick will generate a temp <a /> tag
    //     var link = document.createElement("a");
    //     link.href = uri;
    //     //set the visibility hidden so it will not effect on your web-layout
    //     link.style = "visibility:hidden";
    //     link.download = fileName + ".csv";
    //     //this part will append the anchor tag and remove it after automatic click
    //     document.body.appendChild(link);
    //     link.click();
    //     document.body.removeChild(link);
    // }
    // function sever_exhibitionStatistics_sum_datagrid_download() {
    //     var  pid =  $('#sever-exhibitionStatistics-combobox-keyword-project').combobox('getValue');
    //     if(pid == ''){
    //         alert("请选择项目名称");
    //         return;
    //     }
    //     $.ajax({
    //         url: eippath + '/serveTool/getSTDSum?key=1',
    //         type:"post",
    //         async:false,
    //         datatype:'json',
    //         data:{f_project_id:pid,page:1,rows:999999
    //             ,f_confirm:4
    //         },
    //         xhrFields:{withCredentials:true},
    //         success:function(data){
    //             var arr= eval('('+data+')');
    //             var  rows = arr.rows;
    //             var total = arr.total;
    //             if(total > 0){
    //                 // console.log(JSON.stringify(rows));
    //                 JSONToCSVConvertorZhanJuSum(rows, "Download", true);
    //             }else{
    //                 $.messager.alert('提示','当前项目没有数据！','warning');
    //             }

    //         },error:function(data){

    //             return;
    //         }
    //     });
    // }
    // function JSONToCSVConvertorZhanJuSum(JSONData, ReportTitle, ShowLabel) {
    //     //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
    //     var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData)
    //         : JSONData;
    //     for (var i = 0; i < arrData.length; i++) {
    //         delete arrData[i].f_tool_detial_id;
    //         delete arrData[i].f_sever_tool_id;
    //         delete arrData[i].f_tool_id;
    //         delete arrData[i].f_is_recovery;
    //         delete arrData[i].f_is_default;
    //         delete arrData[i].f_onsite_price;
    //         delete arrData[i].f_default_name;
    //         delete arrData[i].f_serve_booth_id;
    //         delete arrData[i].f_exhibit_code;
    //         delete arrData[i].f_client_id;
    //         delete arrData[i].f_project_id;
    //         delete arrData[i].f_section_code;
    //         delete arrData[i].f_project_name;
    //         delete arrData[i].f_org_num;

    //         delete arrData[i].f_company_name;
    //         delete arrData[i].f_section_name;
    //         delete arrData[i].f_booth_num;
    //         delete arrData[i].f_tool_pic;
    //         delete arrData[i].f_preorder_price;
    //         delete arrData[i].f_total_price;
    //         delete arrData[i].f_person;
    //         delete arrData[i].f_exihibit_name;
    //         delete arrData[i].f_memo;
    //     }
    //     var CSV = '';
    //     //Set Report title in first row or line

    //     /* CSV += ReportTitle + '\r\n\n'; */

    //     //This condition will generate the Label/Header
    //     if (ShowLabel) {
    //         var row = "";

    //         //This loop will extract the label from 1st index of on array
    //         for ( var index in arrData[0]) {

    //             //Now convert each value to string and comma-seprated
    //             row += index + ',';
    //         }

    //         row = row.slice(0, -1);
    //         row = row
    //             //.replace("serveFasciaId","序号")
    //             .replace("f_tool_code","展具编号")
    //             .replace("f_tool_name","展具名称")
    //             .replace("f_tool_type","规格")
    //             .replace("f_tool_amount","数量")
    //         ;
    //         console.log(row);
    //         //append Label row with line break
    //         CSV += row + '\r\n';

    //     }
    //     //1st loop is to extract each row
    //     for (var i = 0; i < arrData.length; i++) {
    //         var row = "";

    //         //2nd loop will extract each column and convert it in string comma-seprated
    //         for ( var index in arrData[i]) {
    //             row += '"' + arrData[i][index] + '",';
    //         }

    //         row.slice(0, row.length - 1);

    //         //add a line break after each row

    //         row = row.replace(/"null"/g,"").replace(/#/g,"×");
    //         console.log(row);
    //         CSV += row + '\r\n';
    //     }

    //     if (CSV == '') {
    //         alert("Invalid data");
    //         return;
    //     }
    //     var mathstr = Math.random();
    //     //Generate a file name
    //     var fileName = "展具明细汇总导出数据-"+moment().format('YYYY年MM月DD日');
    //     //this will remove the blank-spaces from the title and replace it with an underscore
    //     fileName += ReportTitle.replace(/ /g, "_").replace("Download","");
    //     console.log(fileName);

    //     //Initialize file format you want csv or xls
    //     var uri = 'data:text/csv;charset=utf-8,\uFEFF' + encodeURI(CSV);

    //     // Now the little tricky part.
    //     // you can use either>> window.open(uri);
    //     // but this will not work in some browsers
    //     // or you will not get the correct file extension

    //     //this trick will generate a temp <a /> tag
    //     var link = document.createElement("a");
    //     link.href = uri;

    //     //set the visibility hidden so it will not effect on your web-layout
    //     link.style = "visibility:hidden";
    //     link.download = fileName + ".csv";

    //     //this part will append the anchor tag and remove it after automatic click
    //     document.body.appendChild(link);
    //     link.click();
    //     document.body.removeChild(link);
    // }
</script>