<!--展商费用管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%-- <%@ include file="../common/project.jsp" %> --%>
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
	fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
	fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
	fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
	fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
	fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
		else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
		else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
		else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
		else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
		else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
		else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
		else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
		else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
		else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
		else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
	}
%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style type="text/css">
	.posf{
		position: fixed;
		width: inherit;
		line-height: 34px;
		background-color: #fff;
		z-index: 3;
		box-sizing: border-box;
		border-bottom: 1px solid #ccc;
	}
	.window{
		position: fixed !important;
	}
	#exhibitor-cost-window>div:nth-of-type(2){
		margin-top: 40px;
	}
</style>
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibitor-cost-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_cost_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_cost_add()" plain="true">新增</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_cost_mod()" plain="true">修改</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_cost_del_batch()" plain="true">删除</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="exhibitor_cost_view()" plain="true">查看</a>
            </div>
            <div class="my-toolbar-search">
				<div style="display: none">
					<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select id="exhibitor-cost-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select>
                    <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="exhibitor_cost_select_company()" >选取</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <!-- <a href="javascript:void(0);" class="easyui-linkbutton"
					style="width:35px;height:25px" onclick="exhibitor_popup_project($('#exhibitor-cost-combobox-keyword-project'))">...</a> -->&nbsp;&nbsp;&nbsp;&nbsp;
					<label>费用号</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-costCode" class="my-text" style="width:120px">&nbsp;
					<label>业务员</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-staffName" class="my-text" style="width:120px">&nbsp;
				</div>
                &nbsp;&nbsp;&nbsp;&nbsp;
            	<label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-companyName" class="my-text" style="width:120px">
                &nbsp;&nbsp;&nbsp;&nbsp;
				<div style="float: left;margin-right: 10px;">
					<label>费用类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select id="exhibitor-cost-orderAmountType-status" class="easyui-combobox" style="width:175px;height:25px">
					    <option value="">未选择</option>
					    <option value="PERSON">人员费用</option>
					    <option value="BOOTH">展位费用</option>
						<option value="OTHER">其他费用</option>
					</select>&nbsp;&nbsp;&nbsp;&nbsp;
				</div>
				<div style="float: left;display: none;" id="exhibitor-cost-combobox-keyword-customerServiceIdDiv">
					<span style="display:inline-block; width: 70px;">客服专员</span>
					<select id="exhibitor-cost-combobox-keyword-customerServiceId" class="easyui-combobox"
					data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'"
					style="width:125px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
				</div>
                <label>审核状态</label>&nbsp;&nbsp;&nbsp;&nbsp;
                <select id="exhibitor-cost-approval-status" class="easyui-combobox" style="width:175px;height:25px">
                    <option value="-1">未选择</option>
                    <option value="1">是</option>
                    <option value="0">否</option>
                </select>

								<div style="display: inline-block;">
								<style>
									#exhibitor-cost-combotree-childProjectId+.combo{
										margin-right: 0 !important;
									}
								</style>
									<label>项目</label>&nbsp;&nbsp;&nbsp;&nbsp;
								<div style="position: relative;display: inline-block;margin-right: 10px;">
									<input id="exhibitor-cost-combotree-childProjectId" class="easyui-combotree" style="width:320px;height:25px;">
									<a href="javascript:;" onclick="$('#exhibitor-cost-combotree-childProjectId').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
								</div>
								</div>
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="exhibitor-cost-datagrid" class="easyui-datagrid" toolbar="#exhibitor-cost-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',
        	selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			exhibitor_cost_view_db(rowData);
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'projectName',width:120,formatter:(value,row,index) => {
						value = value || ''
						return value.length > 16 ? value.substr(0,16) + '...' : value
				}">项目</th>
					<th data-options="field:'orderBSId',width:70">费用号</th>
					<th data-options="field:'clientId',width:60">公司编号</th>
					<th data-options="field:'clientName',width:120">公司名称</th>
					<th data-options="field:'customerServiceName',width:60">客服专员</th>
					<th data-options="field:'signDate',width:120">签约日期</th>
					<th data-options="field:'orderAmountType',width:120" formatter="exhibitor_cost_orderAmountType">费用类型</th>
					<th data-options="field:'amountRMBTotal',width:80" align="right">总费用(折合人民币)</th>
					<!-- <th data-options="field:'amountRMB',width:80" align="right">后期增加费用</th> -->
					<th data-options="field:'operName',width:60">业绩归属人</th>
					<th data-options="field:'memo',width:120">备注</th>
					<th data-options="field:'isChecked',width:80" formatter="exhibitor_cost_formatIsChecked">是否审核</th>
					<th data-options="field:'checkTime',width:100">审核时间</th>
					<th data-options="field:'checkerName',width:100">审核人</th>
				</tr>
			</thead>
			<tbody>
	    </tbody>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar" data-options="closed:true,title:'展商费用内容',modal:true" style="height:60%;width:800px;">
		<!-- begin of toolbar -->
		<div id="exhibitor-cost-window-toolbar" class="posf">
		    <div class="my-toolbar-button">
	        	<!-- <a href="javascript:void(0);" id="exhibitor-cost-add" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_cost_add()" plain="true">新增</a> -->
	        	<!-- <a href="javascript:void(0);" id="exhibitor-cost-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_cost_mod2()" plain="true">修改</a> -->
	        	<!-- <a href="javascript:void(0);" id="exhibitor-cost-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_cost_del()" plain="true">删除</a> -->
	        	<a href="javascript:void(0);" id="exhibitor-cost-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_save()" plain="true">保存</a>
	        	<!-- <a href="javascript:void(0);" id="exhibitor-cost-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_cancel()" plain="true">取消</a> -->
	        	<a href="javascript:void(0);" id="exhibitor-cost-check" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_check()" plain="true">审核</a>
	        	<!-- <a href="javascript:void(0);" id="exhibitor-cost-uncheck" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_uncheck()" plain="true">撤销</a> -->
		    </div>
	    </div>
	    <!-- end of toolbar -->
		    <div id="exhibitor-cost-panel-2" class="easyui-panel" title="基本信息" style="width:97.6%;padding:10px;"
		         data-options="region:'north',split:true,collapsible:true,collapsed:false,closable:false">
				<ul style="list-style:none;margin:0;padding:0;border:0">

					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>公司编号</label></div>
						<input id="exhibitor-cost-textbox-clientId" hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:126px;height:25px">
						<a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="exhibitor_cost_select_company()" >选取</a>
					</li>

					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label></div>
						<input id="exhibitor-cost-textbox-companyName" hint="clientName" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
					</li>

					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>项目</label></div>
						<input id="exhibitor-cost-combotree-projectId" hint="projectId" class="easyui-combotree" control="combotree" tag="0" style="width:175px;height:25px">
					</li>

					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>费用号</label></div>
						<input id="exhibitor-cost-textbox-orderCode" hint="orderBSId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
					</li>
					<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
						<input hint="costTotal" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
					</li> -->
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>签约日期</label></div>
						<input hint="signDate" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>业绩归属人</label></div>
						<input id="exhibitor-cost-combobox-operatorId" hint="ownerId" class="easyui-combobox" control="combobox" tag="0" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
						<input id="exhibitor-cost-combobox-memo" hint="memo" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
					</li>
				</ul>
			</div>
				<div id="exhibitor-cost-panel-3" class="easyui-panel" title="后期增加费用" style="width:97.6%;height:290px;padding:0px;"
				     data-options="region:'north',split:true,collapsible:true,collapsed:false,closable:false">
					<div class="easyui-layout" data-options="fit:true">
						<div data-options="region:'north'" style="height:52px;padding:12px;">
							<a href="javascript:void(0);" id="exhibitor-cost-add2" class="easyui-linkbutton" onclick="exhibitor_cost_add2()" plain="false" style="width:60px;height:25px">添加</a>
					        <a href="javascript:void(0);" id="exhibitor-cost-del2" class="easyui-linkbutton" onclick="exhibitor_cost_del2()" plain="false" style="width:60px;height:25px">删除</a>
						</div>
						<div data-options="region:'center'">
							<table id="exhibitor-cost-datagrid-prod" class="easyui-datagrid"
					        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
					        		onClickRow: exhibitor_cost_onClickRow, onAfterEdit: exhibitor_cost_onChange, onUnselect: exhibitor_cost_onUnselect">
					        	<thead>
									<tr>
										<th data-options="field:'ck',checkbox:true"></th>
										<th data-options="field:'prodName',width:40">产品名称</th>
										<!-- <th data-options="field:'prodPrice',width:30">标准报价</th> -->
										<th data-options="field:'prodSpec',width:30"  editor="{type:'text',options:{required:true}}">产品规格</th>
										<!-- <th data-options="field:'discount',width:30"  editor="{type:'text',options:{required:true}}">折扣%</th> -->
										<th data-options="field:'price',width:30" editor="{type:'text',options:{required:true}}">单价</th>
										<th data-options="field:'unit',width:40">单位</th>
										<th data-options="field:'curencyCode',width:30" editor="{type:'combobox', options:{panelHeight:'auto',url:'${eippath}/fnCurrency/getAll',valueField:'id', textField:'text',method:'post' }}">币种</th>
										<th data-options="field:'qty',width:30"  editor="{type:'text',options:{required:true,validType:['intOrFloat','length[1,12]']}}">数量</th>
										<th data-options="field:'amount',width:30" editor="{type:'text',options:{required:true,validType:['isBlank','intOrFloat','length[1,16]']}}">金额</th>
									</tr>
								</thead>
					        </table>
						</div>
						<div data-options="region:'south'" style="height:52px;padding:12px;">
							<div class="" id="exhibitor-cost-amount-text"></div>
						</div>
					</div>
				</div>
				<!-- <div id="exhibitor-cost-panel-4" class="easyui-panel" title="费用合计" style="width:97.6%;height:150px;padding:0px;"
				     data-options="region:'north',split:true,collapsible:true,collapsed:false,closable:false">
					<div class="easyui-layout" data-options="fit:true">
						<div data-options="region:'center'">
						  <table id="exhibitor-cost-datagrid-prod-total" class="easyui-datagrid"
					        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
					        		">
					        	<thead>
									<tr>
										<th data-options="field:'curencyName',width:30">币种</th>
										<th data-options="field:'amount',width:30">汇总金额</th>
									</tr>
								</thead>
					        </table>
						</div>
					</div>
				</div> -->
			</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-select-prod" class="easyui-window" data-options="closed:true,title:'选取产品',modal:true" style="width:872px;">
	<div id="exhibitor-cost-panel-select-prod" class="easyui-panel" style="width:858px;height:584px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-cost-datagrid-select-prod" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodCode',width:20">产品代码</th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodSpec',width:40">产品规格</th>
							<th data-options="field:'prodPrice',width:30">项目产品单价</th>
							<th data-options="field:'lowDiscount',width:30">最低折扣%</th>
							<th data-options="field:'lowPrice',width:30">最低价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:58px; padding:12px;">
	   			<div class="my-toolbar-search">
	            	<!-- <label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-select-prod" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search_select_prod()">查询</a>
	                 --><div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_ok_select_prod()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_return_select_prod()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true" style="width:572px;">
	<div id="exhibitor-cost-panel-select-company" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-cost-datagrid-select-company" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'clientId',width:20">公司编号</th>
							<th data-options="field:'companyName',width:40">公司名称</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-select-company" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search_select_company()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_ok_select_company()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_return_select_company()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-select-booth" class="easyui-window" data-options="closed:true,title:'选取展位',modal:true" style="width:572px;">
	<div id="exhibitor-cost-panel-select-booth" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-cost-datagrid-select-booth" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'boothCode',width:20">展位号</th>
							<th data-options="field:'typeName',width:40">展位类型</th>
							<th data-options="field:'boothSpec',width:30">展位规格</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>展位号查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-select-booth" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search_select_booth()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_ok_select_booth()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_return_select_booth()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">

	var exhibitor_cost_edit_mode;
	var exhibitor_cost_button_edit_mode;
	var exhibitor_cost_alocate_id = -1;
	var exhibitor_cost_window_close_set = false;
	var exhibitor_cost_save_id;
	var exhibitor_cost_projCltId;
	$(function(){
		 var cnt = 0;
		$('#exhibitor-cost-combobox-keyword-project').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				$(this).combobox('select', project_for_pass);
				if(cnt == 0){
					setTimeout(() => {
						exhibitor_cost_fresh();
					}, 200);
					cnt++;
				}
				$(".datagrid-header-check").html("");
			}
		});
		var exhibitor_cost_jurisdictions = <%=fu_section_graph_set_kywy%> ;
		if(exhibitor_cost_jurisdictions){
			$("#exhibitor-cost-combobox-keyword-customerServiceIdDiv").show()
		}else{
			$("#exhibitor-cost-combobox-keyword-customerServiceIdDiv").hide()
		}
		loadExhibitorCostCombotreeSubpro()
	});
	function loadExhibitorCostCombotreeSubpro() {
			const $subProjectTree = $('#exhibitor-cost-combotree-childProjectId')
			const $subProjectTree2 = $('#exhibitor-cost-combotree-projectId')
			$subProjectTree.combotree({
				url: eippath + '/project/selectSubProject?parentId=' + getQueryString("projectId"),
				loadFilter(data) {
					const tmp = data.data;
          if(!tmp[0].children || !tmp[0].children.length){
            $('#exhibitor-cost-combotree-childProjectId').parent().hide()
            .prev('label').hide();
          }
          return tmp
        },
				async: false,
				onLoadError(e) {
					$.messager.alert('子项目加载失败！')
				},
			});
			$subProjectTree2.combotree({
				url: eippath + '/project/selectSubProject?parentId=' + getQueryString("projectId"),
				loadFilter(data) {
					let tmp = data.data;
					// if(data && data.data && data.data.length>1){
					//	tmp = [data.data[0]]
					// }
					// tmp[0].children = null;
					return tmp
				},
				async: false,
				onLoadError(e) {
					$.messager.alert('子项目加载失败！')
				},
			})
		}
	function exhibitor_cost_operatorId(){
// 		$('#exhibitor-cost-combobox-operatorId').combobox({
// 			valueField: 'id',
// 			textField: 'text',
// 			panelHeight: 'auto',
// 			url: eippath + '/operator/selectByProjectId',
// 			queryParams: {
// 				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue')
//             }
// 		});
	}
	function exhibitor_cost_fresh(){
		exhibitor_cost_search()
		// $('#exhibitor-cost-datagrid').datagrid({
		// 	url: eippath + '/orderBS/getList?key=0',
		// 	queryParams: {
		// 		projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
    //     isChecked: $('#exhibitor-cost-approval-status').combobox('getValue'),
		// 		exhibitCode: exhibitCode_for_pass,
    //   },
		// 	onLoadSuccess: function(data){
		// 		if(exhibitor_cost_alocate_id == -1)$(this).datagrid('selectRow', -1);
		// 		else{
		// 			var rows = $(this).datagrid("getRows");
		// 			for(var i = 0; i < rows.length; i++){
		// 				if(rows[i].orderBSId == exhibitor_cost_alocate_id){
		// 					$(this).datagrid('selectRow', i);
		// 					exhibitor_cost_alocate_id = -1;
		// 					break;
		// 				}
		// 			}
		// 		}
		// 	}
		// });
	}
	function exhibitor_cost_search(){
		$('#exhibitor-cost-datagrid').datagrid({
			url: eippath + '/orderBS/getList?key=1',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				orderCode: $('#exhibitor-cost-keyword-costCode').val(),
                isChecked: $('#exhibitor-cost-approval-status').val(),
				companyName: $('#exhibitor-cost-keyword-companyName').val(),
				orderAmountType: $('#exhibitor-cost-orderAmountType-status').val(),
				customerServiceId: $('#exhibitor-cost-combobox-keyword-customerServiceId').combobox('getValue'),
				childProjectId: $('#exhibitor-cost-combotree-childProjectId').combotree('getValue'),
				exhibitCode: exhibitCode_for_pass,
      },
			onLoadSuccess: function(data){
				if(exhibitor_cost_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].orderBSId == exhibitor_cost_alocate_id){
							$(this).datagrid('selectRow', i);
							exhibitor_cost_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function exhibitor_cost_formatIsChecked(val, row){
		if(val == false || val == null)return '否';
		else return '是';
	}
	function exhibitor_cost_orderAmountType(val, row){
		if(val == 'BOOTH') return '展位费用';
		else if(val == 'PERSON') return '人员费用';
		else if(val == 'OTHER') return '其他费用';
	}
	function exhibitor_cost_formatDo(val, row){
		if(row.isPayment == false || row.isPayment == null)
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_cost_payment(' + row.costId + ')">确认到账</a>';
	}
	function exhibitor_cost_payment(costId){
		var vcostId = costId;
		$.messager.confirm('提示', '确定到账吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/cost/payment',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {costId: vcostId},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','到账成功！','info');
							exhibitor_cost_fresh();
						}else{
							$.messager.alert('提示','到账失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	function exhibitor_cost_window_close(){
		$('#exhibitor-cost-window').window({
			onBeforeClose: function(){
				if(exhibitor_cost_button_edit_mode == 1){
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r){
						if (r){
							$('#exhibitor-cost-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
	                	}
					});
					return false;
                }
			},
			onClose: function(){
				exhibitor_cost_fresh();
			}
		});
	}
	// 主: 添加
	function exhibitor_cost_add(){
		if(!exhibitor_cost_window_close_set){
			exhibitor_cost_window_close();
			exhibitor_cost_window_close_set = true;
		}
		$("#exhibitor-cost-panel-3").panel({title: "其他费用"})
		$.ajax({
			url: eippath + '/orderBS/getSysId',
			dataType: "json",	//返回数据类型
			type: "post",
			contentType: "application/json",
			data: {},	//发送数据
			async: true,
			success: function(data){
				exhibitor_cost_alocate_id = -1;
				$('#exhibitor-cost-window').window('open');
				exhibitor_cost_save_id = data.id;
				exhibitor_cost_operatorId();
				exhibitor_cost_setEditInfoClear();
				$('#exhibitor-cost-combotree-projectId').combotree('setValue', project_for_pass || '')
				$('#exhibitor-cost-datagrid-prod').datagrid({url: eippath + '/data/clear_datagrid.json'});
				exhibitor_cost_edit_mode = "Add";
				exhibitor_cost_setEditInfoReadOnly(1);
				exhibitor_cost_setButtonEditMode(1);
				exhibitor_cost_setChecked('add');
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_mod(){
		exhibitor_cost_mod_or_view(1);
	}
	var orderAmountTypes = ""
	function exhibitor_cost_mod_or_view(flag){
		orderAmountType = ""
		if(!exhibitor_cost_window_close_set){
			exhibitor_cost_window_close();
			exhibitor_cost_window_close_set = true;
		}
		var row = $('#exhibitor-cost-datagrid').datagrid('getSelected');
		var orderAmountType = row.orderAmountType
		if(flag == 1) {
			exhibitor_cost_setChecked(row.isChecked ? 'edit_checked' : 'edit');
		} else {
			exhibitor_cost_setChecked('view')
		}
		if(orderAmountType=="OTHER") $("#exhibitor-cost-panel-3").panel({title: "其他费用"})
		else if(orderAmountType=="BOOTH") $("#exhibitor-cost-panel-3").panel({title: "展位费用"})
		else if(orderAmountType=="PERSON") $("#exhibitor-cost-panel-3").panel({title: "人员费用"})
		if (row){
			exhibitor_cost_alocate_id = row.orderBSId;
			$('#exhibitor-cost-window').window('open');
			//exhibitor_cost_save_id = row.costId;
			exhibitor_cost_save_id = row.orderBSId;
			exhibitor_cost_projCltId = row.projCltId;
			exhibitor_cost_operatorId();
			exhibitor_cost_setEditInfoInit(row);
			$('#exhibitor-cost-datagrid-prod').datagrid({
				url: eippath + '/orderDtlBS/selectByOrderBSId?orderBSId=' + row.orderBSId
				,onLoadSuccess: function(data){
					exhibitor_cost_statistics();//费用合计
				}
				,loadFilter: function(data){
					(data || []).map(it=> {
						it.curencyCode = it.curencyCode || 'RMB';
						it.curencyName = it.curencyName || '人民币';
						return it;
					})
					// exhibitor_cost_mod_or_view : state,rows
					return data;
				}
			});
			//获取相关的订单费用（合同费用明细）
			$('#exhibitor-cost-datagrid-contract').datagrid({
				url: eippath + '/order/selectDtlByProjectIdClientId?projectId=' + row.projectId + '&clientId=' + row.clientId

			});
			exhibitor_cost_edit(flag);
			orderAmountTypes = row.orderAmountType
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_mod2(){
		exhibitor_cost_edit(1);
	}
	var exhibitor_mode = ''; // add, view, edit, edit_checked
	function exhibitor_cost_setChecked(mode = ''){
		if(mode) exhibitor_mode = mode;
		// $("#exhibitor-cost-uncheck").linkbutton('disable');
		$("#exhibitor-cost-check").linkbutton('enable');
	}
	// 主: 修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function exhibitor_cost_edit(flag){
		if(flag == 2) exhibitor_mode = 'view';
		exhibitor_cost_edit_mode = "Mod";
		exhibitor_cost_setEditInfoReadOnly(flag);
		exhibitor_cost_setButtonEditMode(flag);
		$("#exhibitor-cost-combotree-projectId").combotree("disable")
	}
	// 主: 查看
	function exhibitor_cost_view(){
		console.log("查看订单详情");
		exhibitor_cost_mod_or_view(2);
	}
	function exhibitor_cost_view_db(row){
		if(!exhibitor_cost_window_close_set){
			exhibitor_cost_window_close();
			exhibitor_cost_window_close_set = true;
		}
			exhibitor_cost_alocate_id = row.orderBSId;
			$('#exhibitor-cost-window').window('open');
			//exhibitor_cost_save_id = row.costId;
			exhibitor_cost_save_id = row.orderBSId;
			exhibitor_cost_projCltId = row.projCltId;
			exhibitor_cost_operatorId();
			exhibitor_cost_setEditInfoInit(row);
			$('#exhibitor-cost-datagrid-prod').datagrid({
				url: eippath + '/orderDtlBS/selectByOrderBSId?orderBSId=' + row.orderBSId
				,onLoadSuccess: function(data){
					exhibitor_cost_statistics();//费用合计
				}
				,loadFilter: function(data){
					(data || []).map(it=> {
						it.curencyCode = it.curencyCode || 'RMB';
						it.curencyName = it.curencyName || '人民币';
						return it;
					})
					// exhibitor_cost_view_db : state,rows
					return data;
				}
			});
			//获取相关的订单费用（合同费用明细）
			$('#exhibitor-cost-datagrid-contract').datagrid({
				url: eippath + '/order/selectDtlByProjectIdClientId?projectId=' + row.projectId + '&clientId=' + row.clientId
			});
			exhibitor_cost_edit(1);


	}
	function exhibitor_cost_del(){
		$.messager.confirm('提示', '确定删除吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/orderBS/delete',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {orderBSId: exhibitor_cost_save_id},	//发送数据
					async: true,
					success: function(data){
						if(data.state == 1){
							$.messager.alert('提示','删除成功！','info');
							exhibitor_cost_alocate_id = -1;
							$('#exhibitor-cost-window').window('close');
						}else{
							$.messager.alert('提示','删除失败！','error');
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
            }
		});
	}
	function exhibitor_cost_del_batch(){
		var rows = $('#exhibitor-cost-datagrid').datagrid('getSelections');
		if (rows.length > 0){
			var postData = [];
			for(var i = 0; i < rows.length; i++){
				postData[i] = {orderBSId: rows[i].orderBSId};
			}
			$.messager.confirm('提示', '当前选择的费用数为：' + rows.length + '，确定删除吗？', function(r){
				if (r){
					$.ajax({
						url: eippath + '/orderBS/deleteBatch',
						dataType: "json",	//返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData),	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								$.messager.alert('提示','删除成功！','info');
								exhibitor_cost_fresh();
							}else{
								$.messager.alert('提示','删除失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
	            }
			});
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_save2(tip = true){
		//alert(exhibitor_cost_editIndex);
		console.log('保存事件开始');
		if(exhibitor_cost_editIndex != null){
		  $('#exhibitor-cost-datagrid-prod').datagrid("endEdit",exhibitor_cost_editIndex);
		}
		$('#exhibitor-cost-datagrid-prod').datagrid("getRows");
		var postData = {};
		postData['editMode'] = exhibitor_cost_edit_mode;
		postData['orderBSId'] = exhibitor_cost_save_id;
		postData['projectId'] = $('#exhibitor-cost-combotree-projectId').combotree('getValue'); // $('#exhibitor-cost-combobox-keyword-project').combobox('getValue');
		postData['projCltId'] = exhibitor_cost_projCltId;
		postData['orderAmountType']='OTHER';
		$("#exhibitor-cost-panel-2 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});
		postData['orderCode'] = $('#exhibitor-cost-textbox-orderCode').val();
		postData['ownerId'] = $('#exhibitor-cost-combobox-operatorId').val();
		//postData['memo'] = $('#exhibitor-cost-combobox-operatorId').val();
		/* var v = $("#exhibitor-cost-amount").val();
		if(v != '')postData['amountRMB'] =  v;
		var v = $("#exhibitor-cost-amountForeign").val();
		if(v != '')postData['amountForeign'] =  v;
		var v = $("#exhibitor-cost-combobox-currencyForeign").combobox("getValue");
		if(v != '')postData['currencyForeign'] =  v; */


		var rows = $('#exhibitor-cost-datagrid-prod').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].unit'] = rows[i].unit;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].primaryKey'] = rows[i].primaryKey;
			}
		}
		/*$("#exhibitor-cost-panel-3 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});*/
		$.ajax({
			url: eippath + '/orderBS/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					if(tip) $.messager.alert('提示','保存成功！','info');
					exhibitor_cost_edit(2);
					exhibitor_cost_alocate_id = postData['orderBSId'];
					if(tip) $('#exhibitor-cost-window').window('close', true);
				}else{
					$.messager.alert('提示','保存失败！','error');
					throw new Error('---');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
				throw new Error('---');
			}
		});
	}
	function exhibitor_cost_save(tip = true){
		console.log("开始保存展商费用内容");
		if($("#exhibitor-cost-textbox-clientId").val() == ''){
			$.messager.alert('提示','公司信息不能为空！','warning');
			throw new Error('---');
			return;
		}
		exhibitor_cost_save2(tip);
		// $.ajax({
		// 	url: eippath + '/orderBS/isExistOnly',
		// 	dataType: "json",	//返回数据类型
		// 	type: "post",
		// 	data: {
		// 		orderBSId: exhibitor_cost_save_id,
		// 		projectId: $('#exhibitor-cost-combotree-projectId').combotree('getValue'),// $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
		// 		clientId: $("#exhibitor-cost-textbox-clientId").val()
		// 	},	//发送数据
		// 	async: false,
		// 	success: function(data){
		// 		if(data.state == 1){
		// 			$.messager.alert('提示','该公司已经存在费用！','error');
		// 			throw new Error('---');
		// 		}else{
		// 			exhibitor_cost_save2(tip);
		// 		}
		// 	},
		// 	error: function(){
		// 		$.messager.alert('提示','数据发送失败！','error');
		// 		throw new Error('---');
		// 	}
		// });
	}
	var dataIsUpdateExpense;
	function exhibitor_cost_check(){
		// 保存一下
		exhibitor_cost_save(false);
		isUpdateExpense2()
		console.log("开始审核" + dataIsUpdateExpense);
	}
	//判断人员费是否和订单表是否不一样用于更新人员费用
	function isUpdateExpense2(){
		dataIsUpdateExpense = ""
		$.ajax({
			url: eippath  + '/orderBS/isPersonCostDifferent',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				clientId: $("#exhibitor-cost-textbox-clientId").val(),
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				orderAmountType: orderAmountTypes
			}, //发送数据
			async: true,
			success: function(data) {
				dataIsUpdateExpense = data.result //true不一样 false一样
				if(dataIsUpdateExpense){
					$.messager.confirm('提示', '费用发生改变是否继续审核?', function(r){
						if (r){
							$.ajax({
								url: eippath + '/order/orderBSCheck',
								dataType: "json",	//返回数据类型
								type: "post",
								data: {
									orderBSId: exhibitor_cost_save_id,
									clientId: $("#exhibitor-cost-textbox-clientId").val()
								},	//发送数据
								async: true,
								success: function(data){
									if(data.state == 1){
										//生成销售订单成功。
										$.messager.alert('提示','审核成功！','info');
										exhibitor_cost_setButtonEditMode(3);
										exhibitor_cost_setChecked('edit_checked');
										exhibitor_cost_cancel();
									}else if(data.state == -1){
										$.messager.alert('提示','登录超时，审核失败','error');
									}
									else{
										$.messager.alert('提示','审核失败！','error');

									}
								},
								error: function(){
									$.messager.alert('提示','数据发送失败！','error');
								}
							});
						}
					});
				}else{
					$.ajax({
						url: eippath + '/order/orderBSCheck',
						dataType: "json",	//返回数据类型
						type: "post",
						data: {
							orderBSId: exhibitor_cost_save_id,
							clientId: $("#exhibitor-cost-textbox-clientId").val()
						},	//发送数据
						async: true,
						success: function(data){
							if(data.state == 1){
								//生成销售订单成功。
								$.messager.alert('提示','审核成功！','info');
								exhibitorCost_isChecked = true;
								exhibitor_cost_setButtonEditMode(3);
								exhibitor_cost_setChecked('edit_checked');
								exhibitor_cost_cancel();
							}else if(data.state == -1){
								$.messager.alert('提示','登录超时，审核失败','error');
							}
							else{
								$.messager.alert('提示','审核失败！','error');
							}
						},
						error: function(){
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
				}
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}
	function exhibitor_cost_uncheck(){
		exhibitor_cost_setButtonEditMode(2);
	}
	function exhibitor_cost_cancel(){
		$('#exhibitor-cost-window').window('close', true);
	}
	//设置编辑页面内容清空
	function exhibitor_cost_setEditInfoClear(){
		$("#exhibitor-cost-panel-2 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "combotree")$($(this).find("input")[0]).combotree("clear");
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
		/* $("#exhibitor-cost-amount").textbox("setValue", '');
		$("#exhibitor-cost-amountForeign").textbox("setValue", '');
		$("#exhibitor-cost-combobox-currencyForeign").combobox("setValue", ''); */
		/*$("#exhibitor-cost-panel-3 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});*/
	}
	//设置编辑页面内容初始化
	function exhibitor_cost_setEditInfoInit(row){
		$("#exhibitor-cost-panel-2 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "combotree")$($(this).find("input")[0]).combotree("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
		/* $("#exhibitor-cost-amount").textbox("setValue", row['amountRMB']);
		$("#exhibitor-cost-amountForeign").textbox("setValue", row['amountForeign']);
		$("#exhibitor-cost-combobox-currencyForeign").combobox("setValue", row['currencyForeign']); */
		/*$("#exhibitor-cost-panel-3 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});*/
	}
	//设置编辑页面可编辑属性
	function exhibitor_cost_setEditInfoReadOnly(flag){
		var readonly = true, able = 'disable';
		if(flag == 1){
			readonly = false;
			able = 'enable';
		}
		$("#exhibitor-cost-panel-2 ul").find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
			else if(control == "combotree")$($(this).find("input")[0]).combotree(able);
			else if(control == "checkbox"){
				if(flag == 1)$($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
		/*$("#exhibitor-cost-panel-3 ul").find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
		});*/
		$("#exhibitor-cost-textbox-orderCode").attr("readonly", true)
	}
	//按钮有效 flag 1 编辑 2 查看 3 审核通过
	function exhibitor_cost_setButtonEditMode(flag){
		if(flag == 1){
			$("#exhibitor-cost-add").linkbutton('disable');
			$("#exhibitor-cost-mod2").linkbutton('disable');
			$("#exhibitor-cost-del").linkbutton('disable');
			$("#exhibitor-cost-save").linkbutton('enable');
			$("#exhibitor-cost-cancel").linkbutton('enable');
			$("#exhibitor-cost-add2").linkbutton('enable');
			$("#exhibitor-cost-del2").linkbutton('enable');
			// $("#exhibitor-cost-check").linkbutton('disable');
			// $("#exhibitor-cost-uncheck").linkbutton('disable');
		}else if(flag == 2){
			$("#exhibitor-cost-add").linkbutton('enable');
			$("#exhibitor-cost-mod2").linkbutton('enable');
			$("#exhibitor-cost-del").linkbutton('enable');
			$("#exhibitor-cost-save").linkbutton('disable');
			$("#exhibitor-cost-cancel").linkbutton('disable');
			$("#exhibitor-cost-add2").linkbutton('disable');
			$("#exhibitor-cost-del2").linkbutton('disable');
			// $("#exhibitor-cost-check").linkbutton('enable');
			// $("#exhibitor-cost-uncheck").linkbutton('disable');
		}else if(flag == 3){
			$("#exhibitor-cost-add").linkbutton('disable');
			$("#exhibitor-cost-mod2").linkbutton('disable');
			$("#exhibitor-cost-del").linkbutton('disable');
			$("#exhibitor-cost-save").linkbutton('disable');
			$("#exhibitor-cost-cancel").linkbutton('disable');
			$("#exhibitor-cost-add2").linkbutton('disable');
			$("#exhibitor-cost-del2").linkbutton('disable');
			// $("#exhibitor-cost-check").linkbutton('disable');
			// $("#exhibitor-cost-uncheck").linkbutton('enable');
		}
		exhibitor_cost_button_edit_mode = flag;
	}
	function exhibitor_cost_select_company(){
		if(exhibitor_cost_button_edit_mode == 2)return;
		$('#exhibitor-cost-window-select-company').window('open');
		$('#exhibitor-cost-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=0',
			queryParams: {
				exhibitCode:exhibitCode_for_pass,
				queryProjectId: $('#exhibitor-cost-combotree-projectId').combotree('getValue')
            }
		});
	}
	function exhibitor_cost_search_select_company(){
		$('#exhibitor-cost-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=1',
			queryParams: {
				exhibitCode:exhibitCode_for_pass,
				queryProjectId: $('#exhibitor-cost-combotree-projectId').combotree('getValue'),
				companyName: $('#exhibitor-cost-keyword-select-company').val()
            }
		});
	}
	function exhibitor_cost_ok_select_company(){
		var row = $('#exhibitor-cost-datagrid-select-company').datagrid('getSelected');
		if (row){
			$("#exhibitor-cost-textbox-clientId").textbox("setValue", row.clientId);
			$("#exhibitor-cost-textbox-companyName").textbox("setValue", row.companyName);
			exhibitor_cost_projCltId = row.projCltId;
			if(row.contractProjectId){
				$('#exhibitor-cost-combotree-projectId').combotree('setValue',row.contractProjectId);
			}
			$('#exhibitor-cost-window-select-company').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_return_select_company(){
		$('#exhibitor-cost-window-select-company').window('close');
	}
	function exhibitor_cost_select_booth(){
		if(exhibitor_cost_button_edit_mode == 2)return;
		$('#exhibitor-cost-window-select-booth').window('open');
		$('#exhibitor-cost-datagrid-select-booth').datagrid({
			url: eippath + '/exhibitBooth/getBoothList?key=0',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue')
            }
		});
	}
	function exhibitor_cost_search_select_booth(){
		$('#exhibitor-cost-datagrid-select-booth').datagrid({
			url: eippath + '/exhibitBooth/getBoothList?key=1',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				boothCode: $('#exhibitor-cost-keyword-select-booth').val()
            }
		});
	}
	function exhibitor_cost_ok_select_booth(){
		var rows = $('#exhibitor-cost-datagrid-select-booth').datagrid('getSelections');
		if (rows.length > 0){
			var str = $("#exhibitor-cost-textbox-boothCode").val();
			var canDot = true;
			if(str == "")canDot = false;
			for(var i = 0; i < rows.length; i++){
				if(str.indexOf("[" + rows[i].boothCode + "]") == -1){
					if(canDot)str += ",";
					canDot = true;
					str += "[" + rows[i].boothCode + "]";
				}
			}
			$("#exhibitor-cost-textbox-boothCode").textbox("setValue", str);
			$('#exhibitor-cost-window-select-booth').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_return_select_booth(){
		$('#exhibitor-cost-window-select-booth').window('close');
	}
	function exhibitor_cost_add2(){
		if(exhibitor_cost_button_edit_mode == 2)return;
		$('#exhibitor-cost-window-select-prod').window('open');
		$('#exhibitor-cost-datagrid-select-prod').datagrid({
			url: eippath + '/projProduct/selectByProjectId',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue')
      },
			loadFilter(data) {
				(data || []).map(it=> {
					it.curencyCode = it.curencyCode || 'RMB';
					return it;
				})
				return data;
			},
		});
	}
	function exhibitor_cost_del2(){
		if(exhibitor_cost_button_edit_mode == 2)return;
		var rows = $('#exhibitor-cost-datagrid-prod').datagrid('getSelections');
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				var rowIndex = $('#exhibitor-cost-datagrid-prod').datagrid('getRowIndex', rows[i]);
				$('#exhibitor-cost-datagrid-prod').datagrid('deleteRow', rowIndex);
			}
			exhibitor_cost_statistics();
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_search_select_prod(){

	}
	function exhibitor_cost_ok_select_prod(){
		var rows = $('#exhibitor-cost-datagrid-select-prod').datagrid('getSelections');
		var rows2 = $("#exhibitor-cost-datagrid-prod").datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				flag = 0;
				for(var j = 0; j < rows2.length; j++){
					if(rows2[j].prodCode == rows[i].prodCode && rows2[j].primaryKey == rows[i].primaryKeyBs){
						flag = 1;
						break;
					}
				}
				if(flag == 0){
					$('#exhibitor-cost-datagrid-prod').datagrid('appendRow', {
						prodCode: rows[i].prodCode,
						prodName: rows[i].prodName,
						prodPrice: rows[i].prodPrice,
						unit: rows[i].prodUnit,
						prodSpec: rows[i].prodSpec,
						discount: 100,
						price: rows[i].prodPrice,
						curencyCode: rows[i].curencyCode,
						qty: 1,
						amount: rows[i].prodPrice,
						primaryKey:rows[i].primaryKeyBs
					});
				}
			}
			$('#exhibitor-cost-window-select-prod').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
		exhibitor_cost_statistics();
	}
	function exhibitor_cost_return_select_prod(){
		$('#exhibitor-cost-window-select-prod').window('close');
	}
</script>




<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}

	</style>






<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});

		});
		function reflash_re(){
			$('.easyui-window').panel('destroy');
			reflush();
		}
		function  checkfeiyong(){
			$('#exhibitor-cost-window').window('open');
		}
		var exhibitor_cost_editIndex = null;
		function exhibitor_cost_onClickRow(rowIndex, rowData) {
			console.log("产品明细点击事件3");
			var l = $(this).datagrid('getRows').length;
			for(var i = 0; i < l; i++){
       			if(i != rowIndex)$(this).datagrid('unselectRow', i);
       		}
			//alert(exhibitor_cost_editIndex+"||"+rowIndex)
			if(exhibitor_cost_button_edit_mode == 1){
				if(exhibitor_cost_editIndex != null && exhibitor_cost_editIndex != rowIndex){
					$(this).datagrid('endEdit', exhibitor_cost_editIndex);
					$(this).datagrid('beginEdit', rowIndex);
					exhibitor_cost_editIndex = rowIndex;
				}else if(exhibitor_cost_editIndex == null  ){
					$(this).datagrid('beginEdit', rowIndex);
					exhibitor_cost_editIndex = rowIndex;
				}else if(exhibitor_cost_editIndex == rowIndex){
					$(this).datagrid('beginEdit', rowIndex);
					exhibitor_cost_editIndex = rowIndex;
				}

			}

		}
		function exhibitor_cost_onChange(rowIndex, rowData, changes){
			console.log("行编辑结束，触发事件");
			//var row = $(this).datagrid('getSelected');
			//var row = rowData;
			console.log(rowData)
			console.log(changes)
			if(rowData.prodName != '展具'){
				rowData.amount=rowData.price*rowData.qty;//计算值
			}else{
				rowData.qty = 1
				rowData.amount=rowData.price;
				console.log(rowData.amount)
			}
				$(this).datagrid('updateRow',{index:rowIndex,row:rowData})

			//$(this).datagrid('updateRow',{index:rowIndex,row:rowData});//写入行数据
			$(this).datagrid('refreshRow', rowIndex);//刷新显示的数据
			//if (row){
				//alert(rowData.amount);
			//}
			//计算合计值
			var rows = $(this).datagrid("getRows");
			var total = 0;
			var totalForeign = 0;
			var currencyForeign = '';
			if (rows.length > 0){
				for(var i = 0; i < rows.length; i++){
					if(rows[i].curencyCode=='RMB'){
						total += rows[i].amount;
					}else{
						totalForeign += rows[i].amount;
						if(currencyForeign == ''){
							currencyForeign = rows[i].curencyCode;
						}
					}

				}
			}
			/* $("#exhibitor-cost-amount").numberbox('setValue',total);
			$("#exhibitor-cost-amountForeign").numberbox('setValue',totalForeign);
			$("#exhibitor-cost-combobox-currencyForeign").combobox('setValue',currencyForeign); */
			exhibitor_cost_statistics();
		}
		function exhibitor_cost_onUnselect(rowIndex,rowData){
			if(exhibitor_cost_button_edit_mode == 2)return;
			//只有一行数据时，自动计算合计值
			var l = $(this).datagrid('getRows').length;
			if(l == 1){
				if(rowData.prodName != '展具'){
					rowData.amount=rowData.price*rowData.qty*rowData.discount/100;//计算值

				}
				else{
						rowData.amount=rowData.amount*1;

				}

				$(this).datagrid('updateRow',{index:rowIndex,row:rowData});//写入行数据
				$(this).datagrid('refreshRow', rowIndex);//刷新显示的数据
				var total = 0;
				var rows = $(this).datagrid("getRows");
				var totalForeign = 0;
				var currencyForeign = '';
				if (rows.length > 0){
					for(var i = 0; i < rows.length; i++){
						if(rows[i].curencyCode=='RMB'){
							total += rows[i].amount;
						}else{
							totalForeign += rows[i].amount;
							if(currencyForeign == ''){
								currencyForeign = rows[i].curencyCode;
							}
						}

					}
				}
				/* $("#exhibitor-cost-amount").numberbox('setValue',total);
				$("#exhibitor-cost-amountForeign").numberbox('setValue',totalForeign);
				$("#exhibitor-cost-combobox-currencyForeign").combobox('setValue',currencyForeign); */


			}
			exhibitor_cost_statistics();
		}


		/* function exhibitor_cost_statistics(){
			var rows = $("#exhibitor-cost-datagrid-prod").datagrid("getRows");
			let sorted = groupBy(rows, function(item){
			    return [item.currenryCode];
			});
			alert(JSON.stringify(sorted))
			if (rows.length > 0){
				for(var i = 0; i < rows.length; i++){


				}
			}
		}


		function groupBy( array , f ) {
		    let groups = {};
		    array.forEach( function( o ) {
		        let group = JSON.stringify( f(o) );
		        groups[group] = groups[group] || [];
		        groups[group].push( o );
		    });
		    return Object.keys(groups).map( function( group ) {
		        return groups[group];
		    });
		} */
		function exhibitor_cost_statistics(){
			//alert("a")
			var rows = $("#exhibitor-cost-datagrid-prod").datagrid("getRows");
			var newData = [];
			//alert(JSON.stringify(rows))
			if (rows.length > 0){
				for(var i = 0; i < rows.length; i++){
					rows[i].amount = +rows[i].amount || 0;
					if(newData.length>0){
						var flag = true;
						for(var x in newData){
							newData[x].amount = +newData[x].amount || 0;
							if(rows[i].curencyCode == newData[x].curencyCode){
								newData[x].amount = Number(newData[x].amount)+Number(rows[i].amount);
								flag = false;
								break;
							}
						}
						if(flag){
							var str ={curencyCode: rows[i].curencyCode,
									  curencyName: rows[i].curencyName,
									  amount: rows[i].amount}
							newData.push(str);
						}
					}else{
						var str ={curencyCode: rows[i].curencyCode,
								  curencyName: rows[i].curencyName,
								  amount: rows[i].amount}
						newData.push(str);
					}
				}
			}
			//alert(JSON.stringify(newData))
			var tableData = {};
			tableData['total']=newData.length;
			tableData['rows']=newData;

			var html = ""
			var curency = ''
			if(newData.length>0){
				for(var x in newData){
					if(newData[x].curencyName!="" && newData[x].curencyName!=undefined){
						html += newData[x].amount +'（'+ newData[x].curencyName +'）&nbsp;&nbsp;'
					}else{
						html += newData[x].amount +'（'+ newData[x].curencyCode +'）&nbsp;&nbsp;'
					}

			    }
			}else{
				html = ""
			}
			var ptext = '<p>合计：&nbsp;'+ html +'</p>'
			$("#exhibitor-cost-amount-text").html(ptext)

			//$("#exhibitor-cost-datagrid-prod-total").datagrid("loadData",tableData);
		}
	</script>
