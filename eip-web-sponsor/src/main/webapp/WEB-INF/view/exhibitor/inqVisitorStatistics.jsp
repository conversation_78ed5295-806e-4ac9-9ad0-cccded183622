<%--
  User: ZzzHhYyy
  Date: 2020-04-28
  Time: 10:16
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
  <title>邀请函访客统计</title>
  <!-- import Vue before Element -->
  <script src="../../vue/vue.js"></script>
  <script src="../../vue/axions.js"></script>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="../../vue/element-ui/index.css">
  <!-- 引入组件库 -->
  <script src="../../vue/element-ui/index.js"></script>
  <script src="../../js/jquery.min.js"></script>
  <script src="../../js/jquery.qrcode.logo.ex.js"></script>
  <style type="text/css">
    body {
      margin: 0;
    }

    .title {
      width: 100%;
      height: 60px;
      line-height: 60px;
    }

    .title h6 {
      font-size: 18px;
      color: #354052;
      box-sizing: border-box;
      width: 1200px;
      margin: auto;
      font-weight: normal;
    }

    .top-name {
      height: 72px;
      background-color: #36a1db;
    }

    .top-name h5 {
      font-size: 16px;
      color: #fff;
      line-height: 72px;
      margin: 0;
      text-align: center;
    }

    .box {
      box-sizing: border-box;
      width: 1200px;
      margin: auto;
      overflow: hidden;
    }

    .block {
      height: 55px;
      display: flex;
      align-items: center;
    }

    /*.search-content:first-of-type{margin-left: 0;}*/
    /*.search-content{float: left;margin-left: 40px;margin-top: 20px;margin-bottom: 20px;}*/
    /*.search-content label{float: left;font-size: 14px;color: #666666;font-weight: bold;line-height: 26px;margin-right: 22px;}*/
    /*!*.search-content input{width: 300px;height: 30px;background-color: #ffffff;border-radius: 4px;border: solid 1px #cccccc;float: left;}*!*/
    /*.search-content>.el-input{width: auto;float: left;}*/
    /*.search-content>button{width: 70px;background-color: #4c82fe;border-radius: 4px;padding: 0;}*/
    /*.search-content1>button{width: 70px;background-color: #fff;color: #666; border-radius: 4px;padding: 0;}*/
    .QR-code-div {
      width: 156px;
      height: 182px;
      background-color: #ffffff;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      border: solid 1px #e5e5e5;
      position: absolute;
      top: 166px;
      right: 30px;
      z-index: 999;
    }

    .QR-code-div > p {
      font-size: 14px;
      margin: 5px 0;
      color: #0f90d2;
      text-align: center;
      line-height: 24px;
    }

    #invite-code canvas {
      width: 140px;
      height: 140px;
    }
    .operation-container{
      width: 500px;
      display: flex;
      align-items: center;
      margin: 20px 0;
    }
    .operation-container .el-button{
      margin-left:20px;
    }
  </style>
</head>
<body>
<div class="title">
  <h6>邀请函热度统计</h6>
</div>
<div class="top-name">
  <h5>邀请函统计明细</h5>
</div>
<div class="box">
  <div class="QR-code-div">
    <div class="windown-img" id="invite-code" style="margin: 6px 6px 0;"></div>
    <p>微信扫码分享</p>
  </div>
  <div id="app">
    <div class="operation-container">
      <el-select placeholder="请选择邀请函模板" style="width: 300px;" size="small"
        v-model="inviteTemplateId" clearable>
        <el-option v-for="it in tpls" :key="it.inviteTemplateId" :label="it.inviteTemplateName"
         :value="it.inviteTemplateId"></el-option>
      </el-select>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <el-input placeholder="请输入公司名称" style="width: 300px;" size="small" v-model="keyWords" clearable
                @keyup.native.enter="reloadTable()" @clear="reloadTable()"></el-input>
      <el-button type="primary" @click="reloadTable()" size="small">查询</el-button>
      <el-button @click="exportByStatistics" size="small">导出</el-button>
    </div>
    <el-table
      ref="singleTable"
      :data="tableData"
      height="calc(100vh - 260px)"
      border
      highlight-current-row
      show-summary
      @sort-change="sortTable"
      :summary-method="getSummaries"
      style="width: 100%">
      <el-table-column type="index" label="序号" width="100"></el-table-column>
      <el-table-column property="companyName" label="所属公司"></el-table-column>
      <el-table-column property="readNum" label="邀请函浏览量" sortable="custom"></el-table-column>
      <el-table-column property="likeCount" label="邀请函点赞量" sortable="custom"></el-table-column>
      <el-table-column property="commCount" label="邀请函评论量" sortable="custom"></el-table-column>
      <el-table-column property="buyerCount" label="观众登记量" sortable="custom"></el-table-column>
    </el-table>
    <div class="block">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="[20, 50, 100]"
                     :page-size="pageSize"
                     layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
  </div>
</div>
</body>
<script>
  function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var url = decodeURI(decodeURI(window.location.search))
    var r = url.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return '';
  }

  var vm = new Vue({
    el: '#app',
    data: {
      tableData: [],
      keyWords: "",
      inviteTemplateId: '',
      total: 0,
      pid: getQueryString("projectId") || '',
      eid: getQueryString("exhibitCode") || '',
      tpls: [], // [{inviteTemplateId,inviteTemplateName,..}]
      currentPage: 1,
      pageSize: 20,
      requestHistory: {}
    },
    methods: {
      setCurrent(row) {
        this.$refs.singleTable.setCurrentRow(row);
      },
      obj2fd(obj) {
        if (typeof obj !== 'object') return;
        const formData = new FormData();
        Object.keys(obj).forEach(key => formData.append(key, obj[key]));
        return formData;
      },
      reloadTable() {
        this.getTableData({
          keyWords: this.keyWords,
          inviteTemplateId: this.inviteTemplateId,
        })
      },
      getTableData(params) {
        axios.get('${pageContext.request.contextPath}/invitation/getByStatistics', {
          params: Object.assign(this.requestHistory, {...params})
        }).then(response => {
          // this.keyWords = ""
          this.tableData = response.data.result
          this.total = response.data.pageUtil.totalRow
        }, response => {
          console.log("error");
        });
      },
      async initInviteTpls() {
        const {data} = await axios.post('${pageContext.request.contextPath}/inviteTemplate/getInviteTemplateList', this.obj2fd({
          exhibitCode: this.eid,
          // projectId: pid || '',
          // publishFlag: true,
         })
        ).catch(_ => console.log("error"));
        this.tpls = data.data || [];
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getTableData({rows: val})
      },
      handleCurrentChange(val) {
        this.currentPage = val;
        this.getTableData({page: val})
      },
      getSummaries(param) {
        const {columns, data} = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          } else if (index < 2) {
            // sums[index] = 'N/A';
            sums[index] = '';
            return;
          }
          let reduce = 0;
          const values = data.map(item => item[column.property]);
          if (!values.every(value => isNaN(value))) {
            // sums[index] = '合计 ';
            sums[index] = reduce + values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            // sums[index] += ' ';
          } else {
            // sums[index] = 'N/A';
            sums[index] = '';
          }
        });
        return sums;
      },
      exportByStatistics() {
        location.href = "${pageContext.request.contextPath}/invitation/exportByStatistics?pid=" + this.pid +"&exhibitCode="+ this.eid + "&keyWords=" + this.keyWords;
      },
      sortTable({order, prop}){
        const params = {
          order: order ? order.slice(0, -6) : '',
          sort: prop
        }
        this.getTableData(params)
      }
    },
    mounted() {
      this.getTableData({
        pid: this.pid,
        exhibitCode: this.eid,
        page: this.currentPage,
        rows: this.pageSize,
        keyWords: this.keyWords
      })
      this.initInviteTpls();
    }
  })
  $(function () {
    //兼容之前已经上传的图片
    function queryData2image(src) {
      const inner = src => {
        if (!src) return '/RegSever/RegPage/images/top-lucency.png'
        const prefix = location.origin
        src = src.replace(/\\/g, '/')
        if (/^(https?:)?\/\//.test(src)) return src
        if (src.startsWith('/')) return prefix + src
        return prefix + '/image/' + src
      }
      const url = inner(src)
      if (!url) return ''
      try {
        return new URL(url).host.indexOf('aliyuncs.com') !== -1
          ? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
          : url
      } catch (e) {
        return url
      }
    }

    function getLogoImgs(projectId, version) {
      let QRCodeLogo = ''
      $.ajax({
        url: '/eip-web-sponsor/api/project/getProjectLogo',
        data: {projectId},
        type: "post",
        async: false,
        success(data) {
          if (data.state !== 1) return
          const buyerLogoKey = version === 2 ? 'buyerRegEnLogo' : 'buyerRegCnLogo'
          const result = data.data || {}
          QRCodeLogo = queryData2image(result[buyerLogoKey] || result['exhibitionLogo'])
        },
        error(err) {
          console.warn(err)
        }
      });
      return queryData2image(QRCodeLogo)
    }


    const url = new URL(location.href)
    url.pathname = '/RegSever/RegPage/pages/invitation-letter/invitation-ranking.html'
    $('#invite-code').qrcode({
      src: getLogoImgs(vm.pid),
      text: url.toString(),
      correctLevel: 0,
      width: '1000',
      height: '1000',
      imgWidth : 200,
      imgHeight : 200,
    });
  })
</script>
</html>
