<!--展商订单管理管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibitor-cost-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_cost_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_cost_mod()" plain="true">修改</a>
            </div>
            <div class="my-toolbar-search">
            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="exhibitor-cost-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-companyName" class="my-text" style="width:200px">
            	<a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="exhibitor_cost_select_company()" >选取</a>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-staffName" class="my-text" style="width:200px">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="exhibitor-cost-datagrid" class="easyui-datagrid" toolbar="#exhibitor-cost-toolbar" 
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'staffId',width:60">人员编号</th>
					<th data-options="field:'staffName',width:80">人员姓名</th>
					<th data-options="field:'companyName',width:120">公司名称</th>
					<th data-options="field:'boothCode',width:80">展位号</th>
					<th data-options="field:'personConfim',width:70,align:'center'" formatter="exhibitor_cost_formatPersonConfim">人员基本资料</th>
					<th data-options="field:'tripConfim',width:70,align:'center'" formatter="exhibitor_cost_formatTripConfim">行程</th>
					<th data-options="field:'visaConfim',width:70,align:'center'">签证</th>
					<th data-options="field:'airTicketConfim',width:70,align:'center'" formatter="exhibitor_cost_formatAirTicketConfim">机票</th>
					<th data-options="field:'hotelConfim',width:70,align:'center'" formatter="exhibitor_cost_formatHotelConfim">酒店</th>
					<th data-options="field:'insuranceConfim',width:70,align:'center'">保险</th>
				</tr>
			</thead>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true" style="width:572px;">
	<div id="exhibitor-cost-panel-select-company" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-cost-datagrid-select-company" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'clientId',width:20">公司编号</th>
							<th data-options="field:'companyName',width:40">公司名称</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-keyword-select-company" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_cost_search_select_company()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_ok_select_company()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_return_select_company()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar" data-options="closed:true,title:'修改',modal:true" style="width:292px;">
	<!-- begin of toolbar -->
	<div id="exhibitor-cost-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="exhibitor-cost-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="exhibitor-cost-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-cost-panel-1" class="easyui-panel" title="基本信息" style="width:278px;height:90px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-cost-textbox-staffName" class="easyui-textbox" style="width:175px;height:25px"></li>
       	</ul>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-jiudian" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar-jiudian" data-options="modal:true,closed:true,title:'酒店'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-cost-window-toolbar-jiudian">
	    <div class="my-toolbar-button">
	       	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_jiudian_save()" plain="true">保存</a>
	       	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_jiudian_cancel()" plain="true">取消</a>
	    </div>
	</div>
	<!-- end of toolbar -->
    <div id="exhibitor-cost-panel-jiudian-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	 
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        </ul>
    </div>
	<div id="exhibitor-cost-panel-jiudian-2" class="easyui-panel" title="酒店信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>酒店名称</label></div>
				<input hint="hotelName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>酒店地址</label></div>
				<input hint="hotelAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>入住时间</label></div>
				<input hint="entryTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>离开时间</label></div>
				<input hint="leaveTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>入住天数</label></div>
				<input hint="stayTime" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>房间号</label></div>
				<input hint="roomNum" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
				<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
			</li>
        </ul>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-renyuan" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar-renyuan" data-options="modal:true,closed:true,title:'人员基本资料'" style="width:1610px;height:820px;">
	<!-- begin of toolbar -->
	<div id="exhibitor-cost-window-toolbar-renyuan">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_renyuan_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_renyuan_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center'">
    <div id="exhibitor-cost-panel-renyuan-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>文化程度</label></div>
        		<input hint="educationId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_education_id',method:'post'">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>职位</label></div>
        		<input hint="positionId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_position_id',method:'post'">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>单位电话</label></div>
        		<input hint="workPhone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>电子邮箱</label></div>
        		<input hint="email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>身份证号 </label></div>
        		<input hint="idNumber" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>身份证签发机关</label></div>
        		<input hint="idIssue" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	        	<div style="width:120px;height:25px;display:inline-block;"><label>家庭住址</label></div>
	        	<input hint="homeAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
	       	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>现住地址</label></div>
        		<input hint="nowAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">	
        		<div style="width:120px;height:25px;display:inline-block;"><label>民族</label></div>
        		<input hint="nationId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_nation_id',method:'post'">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>可将几国外语</label></div>
        		<input hint="abilityLanguage" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label></div>
        		<input hint="isHeel" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
        		<input hint="memo" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        </ul>
    </div>
	
   	<div id="exhibitor-cost-panel-renyuan-2" class="easyui-panel" title="护照信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名拼音</label></div>
        		<input hint="pinyinName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>性别</label></div>
        		<input hint="sexId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=Sex',method:'post'">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>护照类型</label></div>
        		<input hint="passTypeId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_pass_type_id',method:'post'">
			</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>护照号码</label></div>
        		<input hint="passNumber" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出生日期</label></div>
        		<input hint="bornData" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出生地</label></div>
        		<input hint="bornAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签发地点 </label></div>
        		<input hint="passAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签发日期</label></div>
        		<input hint="passTime" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	        	<div style="width:120px;height:25px;display:inline-block;"><label>有效期</label></div>
	        	<input hint="passValidity" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
	       	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:100%;display:inline-block;"><label>曾去国家/地区</label></div>
        		<input hint="countryGone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:100%;display:inline-block;"><lable >拒签历史</lable></div>
        		<input hint="refusHistory" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">	
        		<div style="width:120px;height:100%;display:inline-block;"><lable >是否有旧护照</lable></div>
        		<input hint="haveOldPass" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:100%;display:inline-block;"><lable >旧护照信息</lable></div>
        		<input hint="oldPass" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:640px;height:105px;float:left;margin-right:15px;margin-bottom:5px;">	
        		<div style="width:120px;height:25px;display:inline-block;"><lable >护照照片</lable></div>
        		<input id="exhibitor-cost-passImg" hint="passImg" class="easyui-textbox" control="textbox" tag="0" style="width:1px;height:25px;">
        		<div style="width:320px;height:25px;display:inline-block;">
        			<input id="exhibitor-cost-renyuan-upfile" class="easyui-filebox" name="upfile" data-options="buttonText:'选择',prompt:'选择一个图片(jpg,png格式)...'" style="width:250px;height:25px">
        			<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_cost_renyuan_upload()" style="width:60px;height:25px">上传</a>
        		</div>
        		<div style="width:100px;height:105px;display:none;">
        			<image id="exhibitor-cost-renyuan-image" scr="" onclick="exhibitor_cost_renyuan_imagebig()" style="width:80px;height:100px" />
        		</div>
        	</li>
        </ul>
	</div>
	
	</div>
	<div data-options="region:'east'" title="护照照片" style="width:600px;height:100%">
		<image id="exhibitor-cost-renyuan-image-big" scr="" style="width:98%;height:98%" />
	</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-renyuan-image" class="easyui-window" data-options="modal:false,closed:true,title:'护照照片'" style="width:700px;height:800px;">
	<!-- <image id="exhibitor-cost-renyuan-image-big" scr="" style="width:100%;height:100%" /> -->
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-xingcheng" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar-xingcheng" data-options="modal:true,closed:true,title:'行程'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-cost-window-toolbar-xingcheng">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_xingcheng_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_xingcheng_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-cost-panel-xingcheng-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label></div>
        		<input hint="isHeel" type="checkbox" class="my-checkbox" control="checkbox" tag="1" onclick="return false">
        	</li>
        </ul>
    </div>	
	<div id="exhibitor-cost-panel-xingcheng-2" class="easyui-panel" title="行程信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>起始地</label></div>
        		<input hint="beginPlace" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>目的地</label></div>
				<input hint="endPlace" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		    	<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
		        <input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
	</div>	
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-cost-window-jipiao" class="easyui-window" toolbar="#exhibitor-cost-window-toolbar-jipiao" data-options="modal:true,closed:true,title:'机票'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-cost-window-toolbar-jipiao">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_cost_jipiao_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_cost_jipiao_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-cost-panel-jipiao-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>姓名拼音</label></div>
        		<input hint="pinyinName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
       		</li>
       	 	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>性别</label></div>
        		<input hint="sexId" class="easyui-combobox" control="combobox2" tag="1" readonly style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=Sex',method:'post'">
       		</li>
       		<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>护照类型</label></div>
        		<input hint="passTypeId" class="easyui-combobox" control="combobox2" tag="1" readonly style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_pass_type_id',method:'post'">
       		</li>
	       	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>护照号码</label></div>
        		<input hint="passNumber" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出生日期</label></div>
        		<input hint="bornData" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
        	</li>
	       	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签发日期</label></div>
        		<input hint="passTime" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	        	<div style="width:120px;height:25px;display:inline-block;"><label>有效期</label></div>
	        	<input hint="passValidity" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
	       	</li>
        </ul>
    </div>
	<div id="exhibitor-cost-panel-jipiao-2" class="easyui-panel" title="航班信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>航班信息</label></div>
        		<input hint="airInfo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li> 
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
        		<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出票状态</label></div>
            	<input hint="buyState" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'0',text:'未出'},{id:'1',text:'已出'}]">
        	</li>
        </ul>
	</div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	var exhibitor_cost_alocate_id = -1;
	var exhibitor_cost_save_id;
	var exhibitor_cost_clientId;
	var exhibitor_cost_staffId;
	var exhibitor_cost_edit_mode_jiudian;
	var exhibitor_cost_jiudian_taskId;
	var exhibitor_cost_save_serveHotelId;
	var exhibitor_cost_edit_mode_renyuan;
	var exhibitor_cost_renyuan_taskId;
	var exhibitor_cost_save_servePersonId;
	var exhibitor_cost_edit_mode_xingcheng;
	var exhibitor_cost_jxingcheng_taskId;
	var exhibitor_cost_save_tripId;
	var exhibitor_cost_edit_mode_jipiao;
	var exhibitor_cost_jipiao_taskId;
	var exhibitor_cost_save_serveAirTicketId;
	$(function(){
		var cnt = 0;
		$('#exhibitor-cost-combobox-keyword-project').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				if(cnt == 0){
					exhibitor_cost_fresh();
					cnt++;
				}
			}
		});
	});
	function exhibitor_cost_fresh(){
		$('#exhibitor-cost-datagrid').datagrid({
			url: eippath + '/staffInfo/getList?key=0',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue')
            },
			onLoadSuccess: function(data){
				if(exhibitor_cost_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].staffId == exhibitor_cost_alocate_id){
							$(this).datagrid('selectRow', i);
							exhibitor_cost_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function exhibitor_cost_search(){
		if($("#exhibitor-cost-keyword-companyName").val() == '')exhibitor_cost_clientId = null;
		$('#exhibitor-cost-datagrid').datagrid({
			url: eippath + '/staffInfo/getList?key=1',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				clientId: exhibitor_cost_clientId,
				staffName: $('#exhibitor-cost-keyword-staffName').val()
            }
		});
	}
	function exhibitor_cost_formatPersonConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_cost_person(' + row.staffId + ',' + row.servePersonId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_cost_formatTripConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_cost_trip(' + row.staffId + ',' + row.servePersonId + ',' + row.tripId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_cost_formatHotelConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_cost_hotel(' + row.staffId + ',' + row.servePersonId + ',' + row.serveHotelId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_cost_formatAirTicketConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_cost_airTicket(' + row.staffId + ',' + row.servePersonId + ',' + row.serveAirTicketId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_cost_airTicket(staffId, servePersonId, serveAirTicketId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "10"
			},	//发送数据
			async: true,
			success: function(data){
				//data.id == 0
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_cost_staffId = staffId;
					exhibitor_cost_jipiao_taskId = data.id;
					$("#exhibitor-cost-window-jipiao").window('open');
					if(servePersonId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-jipiao-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-jipiao-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_cost_save_serveAirTicketId = serveAirTicketId;
					if(serveAirTicketId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-jipiao-2 ul");
						exhibitor_cost_edit_mode_jipiao = "Add";
					}else{
						exhibitor_cost_edit_mode_jipiao = "Mod";
						var vServeAirTicketId = serveAirTicketId;
						$.ajax({
							url: eippath + '/serveAirTicket/selectByServeAirTicketId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveAirTicketId: vServeAirTicketId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-jipiao-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_jipiao_save(){
		var postData = {};
		postData['editMode'] = exhibitor_cost_edit_mode_jipiao;
		postData['staffId'] = exhibitor_cost_staffId;
		postData['serveAirTicketId'] = exhibitor_cost_save_serveAirTicketId;
		postData['taskId'] = exhibitor_cost_jipiao_taskId;
		exhibitor_cost_postData(postData, "#exhibitor-cost-panel-jipiao-2 ul");
		$.ajax({
			url: eippath + '/serveAirTicket/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-cost-window-jipiao').window('close', true);
					exhibitor_cost_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_jipiao_cancel(){
		$('#exhibitor-cost-window-jipiao').window('close');
	}
	function exhibitor_cost_person(staffId, servePersonId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "14"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_cost_staffId = staffId;
					exhibitor_cost_renyuan_taskId = data.id;
					$("#exhibitor-cost-window-renyuan").window('open');
					$("#exhibitor-cost-passImg").next().hide();
					exhibitor_cost_save_servePersonId = servePersonId;
					if(servePersonId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-renyuan-1 ul");
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-renyuan-2 ul");
						$("#exhibitor-cost-renyuan-image").attr("src", ' ');
						$("#exhibitor-cost-renyuan-image-big").attr("src", ' ');
						exhibitor_cost_edit_mode_renyuan = "Add";
					}else{
						exhibitor_cost_edit_mode_renyuan = "Mod";
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-renyuan-1 ul", data);
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-renyuan-2 ul", data);
								$("#exhibitor-cost-renyuan-image").attr("src", eippath + '/imgfile/' + data.passImg);
								$("#exhibitor-cost-renyuan-image-big").attr("src", eippath + '/imgfile/' + data.passImg);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_renyuan_upload(){
		var file = $("#exhibitor-cost-renyuan-upfile").filebox("files")[0];
		if(file == null){
			$.messager.alert('提示','请选择图片！','warning');
			return;
		}
		var formData = new FormData();
		formData.append("upfile", file);
		$.ajax({
			url: "${eippath}/servePerson/upload",
			dataType: "json",	//返回数据类型
			type: "post",
			data: formData,	//发送数据
			//告诉jQuery不要去处理发送的数据
        	processData: false,
        	//告诉jQuery不要去设置Content-Type请求头
        	contentType: false,
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','上传成功！','info');
					$("#exhibitor-cost-renyuan-image").attr("src", eippath + '/imgfile/' + data.file);
					$("#exhibitor-cost-renyuan-image-big").attr("src", eippath + '/imgfile/' + data.file);
					$("#exhibitor-cost-passImg").textbox("setValue", data.file);
				}
				else $.messager.alert('提示','上传失败！','error');
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_renyuan_imagebig(){
		$('#exhibitor-cost-window-renyuan-image').window('open');
	}
	function exhibitor_cost_renyuan_save(){
		var postData = {};
		postData['editMode'] = exhibitor_cost_edit_mode_renyuan;
		postData['staffId'] = exhibitor_cost_staffId;
		postData['servePersonId'] = exhibitor_cost_save_servePersonId;
		postData['taskId'] = exhibitor_cost_renyuan_taskId;
		exhibitor_cost_postData(postData, "#exhibitor-cost-panel-renyuan-1 ul");
		exhibitor_cost_postData(postData, "#exhibitor-cost-panel-renyuan-2 ul");
		$.ajax({
			url: eippath + '/servePerson/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-cost-window-renyuan').window('close', true);
					exhibitor_cost_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_renyuan_cancel(){
		$('#exhibitor-cost-window-renyuan').window('close');
	}
	function exhibitor_cost_trip(staffId, servePersonId, tripId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "15"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_cost_staffId = staffId;
					exhibitor_cost_xingcheng_taskId = data.id;
					$("#exhibitor-cost-window-xingcheng").window('open');
					if(servePersonId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-xingcheng-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-xingcheng-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_cost_save_tripId = tripId;
					if(tripId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-xingcheng-2 ul");
						exhibitor_cost_edit_mode_xingcheng = "Add";
					}else{
						exhibitor_cost_edit_mode_xingcheng = "Mod";
						var vServeTripId = tripId;
						$.ajax({
							url: eippath + '/serveTrip/selectByServeTripId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveTripId: vServeTripId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-xingcheng-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_xingcheng_save(){
		var postData = {};
		postData['editMode'] = exhibitor_cost_edit_mode_xingcheng;
		postData['staffId'] = exhibitor_cost_staffId;
		postData['serveTripId'] = exhibitor_cost_save_tripId;
		postData['taskId'] = exhibitor_cost_xingcheng_taskId;
		exhibitor_cost_postData(postData, "#exhibitor-cost-panel-xingcheng-2 ul");
		$.ajax({
			url: eippath + '/serveTrip/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-cost-window-xingcheng').window('close', true);
					exhibitor_cost_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_xingcheng_cancel(){
		$('#exhibitor-cost-window-xingcheng').window('close');
	}
	function exhibitor_cost_hotel(staffId, servePersonId, serveHotelId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "2"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_cost_staffId = staffId;
					exhibitor_cost_jiudian_taskId = data.id;
					$("#exhibitor-cost-window-jiudian").window('open');
					if(servePersonId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-jiudian-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-jiudian-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_cost_save_serveHotelId = serveHotelId;
					if(serveHotelId == null){
						exhibitor_cost_setEditInfoClear("#exhibitor-cost-panel-jiudian-2 ul");
						exhibitor_cost_edit_mode_jiudian = "Add";
					}else{
						exhibitor_cost_edit_mode_jiudian = "Mod";
						var vServeHotelId = serveHotelId;
						$.ajax({
							url: eippath + '/serveHotel/selectByServeHotelId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveHotelId: vServeHotelId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_cost_setEditInfoInit("#exhibitor-cost-panel-jiudian-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_jiudian_save(){
		var postData = {};
		postData['editMode'] = exhibitor_cost_edit_mode_jiudian;
		postData['staffId'] = exhibitor_cost_staffId;
		postData['serveHotelId'] = exhibitor_cost_save_serveHotelId;
		postData['taskId'] = exhibitor_cost_jiudian_taskId;
		exhibitor_cost_postData(postData, "#exhibitor-cost-panel-jiudian-2 ul");
		$.ajax({
			url: eippath + '/serveHotel/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-cost-window-jiudian').window('close', true);
					exhibitor_cost_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_postData(postData, ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox2"){
				var v = $($(this).find("input")[0]).combobox("getText")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});
	}
	function exhibitor_cost_jiudian_cancel(){
		$('#exhibitor-cost-window-jiudian').window('close');
	}
	function exhibitor_cost_select_company(){
		$('#exhibitor-cost-window-select-company').window('open');
		$('#exhibitor-cost-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=0',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue')
            }
		});
	}
	function exhibitor_cost_search_select_company(){
		$('#exhibitor-cost-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=1',
			queryParams: {
				projectId: $('#exhibitor-cost-combobox-keyword-project').combobox('getValue'),
				companyName: $('#exhibitor-cost-keyword-select-company').val()
            }
		});
	}
	function exhibitor_cost_ok_select_company(){
		var row = $('#exhibitor-cost-datagrid-select-company').datagrid('getSelected');
		if (row){
			exhibitor_cost_clientId = row.clientId;
			$("#exhibitor-cost-keyword-companyName").val(row.companyName);
			$('#exhibitor-cost-window-select-company').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_return_select_company(){
		$('#exhibitor-cost-window-select-company').window('close');
	}
	function exhibitor_cost_mod(){
		var row = $('#exhibitor-cost-datagrid').datagrid('getSelected');
		if (row){
			exhibitor_cost_alocate_id = row.staffId;
			$('#exhibitor-cost-window').window('open');
			exhibitor_cost_save_id = row.staffId;
			$("#exhibitor-cost-textbox-staffName").textbox("setValue", row['staffName']);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_cost_save(){
		var postData = {};
		postData['staffId'] = exhibitor_cost_save_id;
		postData['staffName'] = $("#exhibitor-cost-textbox-staffName").val();
		$.ajax({
			url: eippath + '/staffInfo/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-cost-window').window('close', true);
					exhibitor_cost_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_cost_cancel(){
		$('#exhibitor-cost-window').window('close', true);
	}
	//设置编辑页面内容清空
	function exhibitor_cost_setEditInfoClear(ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
	}
	//设置编辑页面内容初始化
	function exhibitor_cost_setEditInfoInit(ul, row){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "combobox2")$($(this).find("input")[0]).combobox("setText", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
	}
</script>




<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
		
	</style>
	
		
	
	<div id="qianzheng" class="easyui-window" title="签证" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:700px;height:auto;padding:1px;">
 		 <!-- begin of toolbar -->
			<div id="all-project-window-toolbar">
			    <div class="my-toolbar-button">
		        	<a href="javascript:void(0);" id="all-project-add" class="easyui-linkbutton" iconCls="icon-add" onclick="" plain="true">新增</a>
		        	<a href="javascript:void(0);" id="all-project-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="" plain="true">修改</a>
		        	<a href="javascript:void(0);" id="all-project-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="" plain="true">删除</a>
		        	<a href="javascript:void(0);" id="all-project-save" class="easyui-linkbutton" iconCls="icon-save" onclick="" plain="true">保存</a>
		        	<a href="javascript:void(0);" id="all-project-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="" plain="true">取消</a>
			    </div>
		    </div>
		    <div id="all-project-panel-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:15px;">
				      <form id="contract-form">
				        <ul style="list-style:none;margin:0;padding:0;">
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<input id="f_project_id" name="f_project_id" type="hidden"  >
				        		<input id="f_contract_id" name="f_contract_id"  class="easyui-textbox" style="width:175px;height:25px">
				        	</li>
				        	 
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<input id="f_booth_amount" name="f_booth_amount" class="easyui-textbox" style="width:175px;height:25px">
				        	</li>
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>办理方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
					        			<option>代办</option>
					        			<option>自办</option>
					        			<option>公司办</option>
					        	</select>
				        	</li>
				        </ul>
				        </form>
		    </div>	
			<div id="all-project-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:15px;">
					      <form id="contract-form">
					        <ul style="list-style:none;margin:0;padding:0;">
					        	 <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>签证类型</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_client_id" name="f_client_id" class="easyui-textbox"   style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>预定签证日期</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_client_name" name="f_client_name" class="easyui-textbox"   style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>押金</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_booth_code" name="f_booth_code" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>签证国家/地区</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_booth_amount" name="f_booth_amount" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>签证进度</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
							        			<option>待办</option>
							        			<option>材料不齐</option>
							        			<option>送签</option>
							        			<option>出签</option>
							        			<option>拒签</option>
							        	</select>
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>拒签原因</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	 <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:490px;height:100%">
					        	</li>
					        </ul>
					        </form>
			    </div>	
			    <div id="all-project-panel-1" class="easyui-panel" title="签证材料" style="width:100%;height:300px;padding:1px;">
			    	<table id="tt" class="easyui-datagrid"  
						   data-options="rownumbers:true,singleSelect:false,pageSize:10,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'"
						   toolbar="#">
						<thead>
							<tr>
								<th data-options="field:'ck',checkbox:true"></th>
								<th field="2" width="110" align="center">材料类型</th>
								<th field="3" width="90" align="center">数量</th>
								<th field="4" width="80" align="center">附件</th>
								<th field="5" width="80" align="center">状态</th>
								<th field="5" width="80" align="center">编辑</th>
							</tr>
						</thead>
						<tbody>
							 <tr>
						      <td>杭州信息</td>
						      <td>9301</td>
						      <td>杭州海派家俱有限公司</td>
						      <td>刘备</td>
						      <td>A120</td>
						     </tr>
						    <tr>
						      <td>杭州信息</td>
						      <td>9321</td>
						      <td>上海海科汽车有限公司</td>
						      <td>宣策</td>
						      <td>A220</td>
						     </tr>
						    
							
						</tbody>
					</table>
			    	
			    </div>
 	</div>
	
	
	<div id="baoxian" class="easyui-window" title="保险确认" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:700px;height:auto;padding:1px;">
  				<!-- begin of toolbar -->
			<div id="all-project-window-toolbar">
			    <div class="my-toolbar-button">
		        	<a href="javascript:void(0);" id="all-project-add" class="easyui-linkbutton" iconCls="icon-add" onclick="" plain="true">新增</a>
		        	<a href="javascript:void(0);" id="all-project-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="" plain="true">修改</a>
		        	<a href="javascript:void(0);" id="all-project-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="" plain="true">删除</a>
		        	<a href="javascript:void(0);" id="all-project-save" class="easyui-linkbutton" iconCls="icon-save" onclick="" plain="true">保存</a>
		        	<a href="javascript:void(0);" id="all-project-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="" plain="true">取消</a>
			    </div>
		    </div>
		    <div id="all-project-panel-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:15px;">
				      <form id="contract-form">
				        <ul style="list-style:none;margin:0;padding:0;">
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<input id="f_project_id" name="f_project_id" type="hidden"  >
				        		<input id="f_contract_id" name="f_contract_id"  class="easyui-textbox" style="width:175px;height:25px">
				        	</li>
				        	 
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<input id="f_booth_amount" name="f_booth_amount" class="easyui-textbox" style="width:175px;height:25px">
				        	</li>
				        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
				        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
					        			<option>跟团</option>
					        			<option>不跟团</option>
					        	</select>
				        	</li>
				        </ul>
				        </form>
		    </div>	
			<div id="all-project-panel-1" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:15px;">
					      <form id="contract-form">
					        <ul style="list-style:none;margin:0;padding:0;">
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>投保人类型</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
					        			<option>参展商</option>
					        			<option>搭建商</option>
					        			<option>主办方</option>
					        		</select>
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_project_id" name="f_project_id" type="hidden"  >
					        		<input id="f_contract_id" name="f_contract_id"  class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	 <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>展位号</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_client_id" name="f_client_id" class="easyui-textbox"   style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>搭建商名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_client_name" name="f_client_name" class="easyui-textbox"   style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_booth_code" name="f_booth_code" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        		<div style="width:120px;height:25px;display:inline-block;"><label>公司地址</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        		<input id="f_booth_amount" name="f_booth_amount" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>电子邮箱</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>确认电子邮箱</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>固定电话</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>手机</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>投保金额</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>赔付金额</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:175px;height:25px">
					        	</li>
					        	 <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
					        			<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
					        			<input id="f_booth_price" name="f_booth_price" data-options="prompt:''" class="easyui-textbox" style="width:490px;height:100%">
					        	</li>
					        </ul>
					        </form>
			    </div>
	</div>
	
<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
		});
		function reflash_re(){
			$('.easyui-window').panel('destroy');
			reflush();
		}
	</script>
	
	 	 