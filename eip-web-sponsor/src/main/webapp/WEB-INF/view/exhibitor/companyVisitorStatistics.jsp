<%--
  User: ZzzHhYyy
  Date: 2020-04-28
  Time: 10:16
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
	<head>
		<title>公司访客统计</title>
		<!-- import Vue before Element -->
		<script src="../../vue/vue.js"></script>
		<script src="../../vue/axions.js"></script>
		<!-- 引入样式 -->
		<link rel="stylesheet" href="../../vue/element-ui/index.css">
		<!-- 引入组件库 -->
		<script src="../../vue/element-ui/index.js"></script>
	</head>
	<style type="text/css">
		body{margin: 0;}
		.title{width: 100%;height: 60px;line-height: 60px;}
		.title h6{font-size: 18px;color: #354052;box-sizing: border-box;padding-left: 360px;margin: 0;font-weight: normal;}
		.top-name{height: 72px;background-color: #36a1db;}
		.top-name h5{font-size: 16px;color: #fff;line-height: 72px;margin: 0;text-align: center;}
		.box{box-sizing: border-box;padding: 0 320px;overflow: hidden;}
		.search-content{float: left;margin-left: 40px;margin-top: 40px;margin-bottom: 20px;}
		.search-content label{float: left;font-size: 14px;color: #666666;font-weight: bold;line-height: 26px;margin-right: 22px;}
		.search-content input{width: 181px;height: 26px;background-color: #ffffff;border-radius: 4px;border: solid 1px #cccccc;float: left;}
	    .search-content>.el-input{width: auto;float: left;}
		.search-content>button{width: 70px;height: 26px;background-color: #4c82fe;border-radius: 4px;padding: 0;}
		.search-content1>button{width: 70px;height: 26px;background-color: #fff;color: #666; border-radius: 4px;padding: 0;}
	</style>
	<body>
		<div class="title">
			<h6>公司访客浏览量统计</h6>
		</div>
		<div class="top-name">
			<h5>公司访客统计明细</h5>
		</div>
		<div class="box">
			<div id="app">
				<template>
				<div class="search-content">
					<label>请输入会刊公司名称、展品名称、展品类型搜索</label>
					<el-input placeholder="请输入会刊公司名称/展品名称/展品类型" v-model="keyWords"></el-input>
				</div>
				<div class="search-content">
					<el-button type="primary" @click="getTableData">查询</el-button>
				</div>
				<div class="search-content search-content1">
					<el-button @click="exportByStatistics">导出</el-button>
				</div>
				<div class="search-content search-content1">
					<!-- `checked` 为 true 或 false -->
					<el-checkbox v-model="checked">包含初始量和倍数</el-checkbox>
				</div>
				<el-table ref="singleTable" :data="tableData"  border show-summary  :summary-method="getSummaries"  highlight-current-row style="width: 100%">
					<el-table-column type="index" label="序号" width="100"></el-table-column>
					<el-table-column property="companyName" label="所属公司"></el-table-column>
					<el-table-column label="公司浏览量" property="readNum" >
						<template slot-scope="scope">
							<span v-if="!checked">{{scope.row.readNum}} </span>
							<span v-else>{{scope.row.readNum+scope.row.readNumInit| readNumFilters}}  </span>
						</template>
					</el-table-column>
					<el-table-column property="collectionsCount" label="公司收藏量">
						<template slot-scope="scope">
							<span v-if="!checked">{{scope.row.collectionsCount}} </span>
							<span v-else>{{scope.row.collectionsCount+scope.row.collectNumInit | collectFilters}}</span>
						</template>
					</el-table-column>
					<el-table-column property="allProReadNum" label="公司总浏览量">
						<template slot-scope="scope">
							<span v-if="!checked">{{scope.row.allProductReadNum+scope.row.readNum}} </span>
							<span v-else>{{scope.row.allProductReadNum+scope.row.readNum+scope.row.readNumInit | readNumFilters}}  </span>
						</template>
					</el-table-column>
					<el-table-column property="allCollectionsCount" label="公司总收藏量">
						<template slot-scope="scope">
							<span v-if="!checked">{{scope.row.collectionsCount+scope.row.allProductCollectionsCount}} </span>
							<span v-else>{{scope.row.collectionsCount+scope.row.allProductCollectionsCount+scope.row.collectNumInit | collectFilters}}  </span>
						</template>
					</el-table-column>
					<el-table-column property="allInqCount" label="公司总询盘量"></el-table-column>
				</el-table>
				<div class="block">
					<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
					 :page-sizes="[20, 50, 100, 150]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
					</el-pagination>
				</div>
				</template>
			</div>

		</div>
	</body>
	<script>
		function getQueryString(name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
			var url = decodeURI(decodeURI(window.location.search))
			var r = url.substr(1).match(reg);
			if (r != null) return unescape(r[2]);
			return null;
		}
		let readMultiple=1;
		let	collectMultiple=1;
		var vm = new Vue({
			el: '#app',
			data: {
				tableData: [],
				keyWords: "",
				total: 0,
				currentPage: 1,
				pageSize: 20,
				checked:false,
			},
			methods: {
				setCurrent(row) {
					this.$refs.singleTable.setCurrentRow(row);
				},
				getTableData() {
					axios.get('${pageContext.request.contextPath}/serveCompany/getByStatistics', {
						params: {
							projectId: getQueryString("projectId") || '',
							exhibitCode: getQueryString("exhibitCode") || '',
							keyWords: this.keyWords || '',
							page: this.currentPage,
							rows: this.pageSize
						}
					}).then(response => {
						// this.keyWords = ""
						this.tableData = response.data.result
						this.total = response.data.pageUtil.totalRow
					})
				},
				handleSizeChange(val) {
					this.pageSize = val;
					this.getTableData()
				},
				handleCurrentChange(val) {
					this.currentPage = val;
					this.getTableData()
				},
				showSetting(){
					axios.get('${pageContext.request.contextPath}/exhibition/selectExhibitSetting', {
						params: {
							exhibitCode: getQueryString("exhibitCode"),
							taskKindCode: 'BrowseInitialSettings',
						}
					}).then(response => {
						let data = response.data;
						for (var i = 0; i < data.length; i++) {
							if(data[i].param=='readMultiple'){//浏览量基数
								readMultiple=data[i].value
							}else if(data[i].param=='collectMultiple'){//收藏基数
								collectMultiple=data[i].value
							}
						}

					})
				},
				getSummaries(param) {
					const { columns, data } = param;
					const sums = [];
					columns.forEach((column, index) => {
						if (index === 0) {
							sums[index] = '本页浏览量统计';
							return;
						}else if (index < 2) {
							// sums[index] = 'N/A';
							return;
						}
						let reduce =0;
						//全部浏览量
						if ('allProReadNum'==column.property){
							const values = data.map(item => item['readNum']);
							reduce = values.reduce((prev, curr) => {
								const value = Number(curr);
								if (!isNaN(value)) {
									return prev + curr;
								} else {
									return prev;
								}
							}, 0);
						}else if ('allCollectionsCount'==column.property){
							const values = data.map(item => item['collectionsCount']);
							reduce = values.reduce((prev, curr) => {
								const value = Number(curr);
								if (!isNaN(value)) {
									return prev + curr;
								} else {
									return prev;
								}
							}, 0);
						}
						const values = data.map(item => item[column.property]);
						if (!values.every(value => isNaN(value))) {
							sums[index] = '合计 ';
							sums[index] += reduce+values.reduce((prev, curr) => {
								const value = Number(curr);
								if (!isNaN(value)) {
									return prev + curr;
								} else {
									return prev;
								}
							}, 0);
							// sums[index] += ' ';
						} else {
							// sums[index] = 'N/A';
						}
					});

					return sums;
				},
				exportByStatistics(){
					if (this.checked){
						location.href="${pageContext.request.contextPath}/serveCompany/exportByStatistics?projectId="
								+getQueryString("projectId")+"&exhibitCode="+getQueryString("exhibitCode")+"&keyWords="+this.keyWords+"&eqMultiple="+this.checked;
					}else {
						location.href="${pageContext.request.contextPath}/serveCompany/exportByStatistics?projectId="
								+getQueryString("projectId")+"&exhibitCode="+getQueryString("exhibitCode")+"&keyWords="+this.keyWords;
					}
				}
			},
			mounted() {
				this.getTableData();
				this.showSetting();
			},
			filters: {
				readNumFilters:function (readNum) {
					return Math.ceil(readMultiple*readNum);
				},
				collectFilters:function (collectNum) {
					return Math.ceil(collectMultiple*collectNum);
				},
			}
		})
	</script>
</html>
