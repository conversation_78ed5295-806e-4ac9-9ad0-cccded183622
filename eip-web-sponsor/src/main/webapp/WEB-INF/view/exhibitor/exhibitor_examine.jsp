<!--展位申报资料审核导入 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
	<!--<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>-->
	<div class="easyui-panel" style="padding:5px;">
		<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="examine-select" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" onclick="exhibitor_catalog_search()" >查询</a>
	</div>
	<div class="easyui-tabs" id="examine-booth" style="width:100%;height:94%;margin-top:4px;">
		<div title="展位申请表" style="padding:1px">
			<table id="examine-booth-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_booth">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号1116</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_booth">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" onclick=" " >查询</a>
				</div>
			</div>	
		</div>
		<div title="楣板申请表" style="padding:1px">
			<table id="examine-meiban-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_meiban">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_meiban">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
		<div title="会刊申请表" style="padding:1px" id="huikan">
			<table id="examine-huikan-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！' 
					,selectOnCheck:true
	        		,onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				}," 
					toolbar="#exhibitor_huikan">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_serve_catalogue_id" width="80" hidden="true" >会刊ID</th>
						<th field="f_company_name" width="80">公司名称</th>
						<th field="f_contacts_name" width="80">联系人名称</th>
						<th field="f_tel" width="100" align="right">公司电话</th>
						<th field="f_fax" width="100">公司传真</th>
						<th field="f_company_profile" width="100">公司简介</th>
						<th field="f_catalogue_logo" width="100">文件名称</th>
						<th field="1" width="100"  formatter="exhibitor_down">预览</th>
						<th field="f_client_id" width="100" hidden="true">客户编号</th>
						<th field="f_project_id" width="100" hidden="true">项目编号</th>
						<th field="f_catalogue_confim" width="80"  align="center" formatter="exhibitor_huikan_state">状态</th>
						<th data-options="field:'do',width:100,align:'center'" formatter="exhibitor_examine">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_huikan" >
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" plain="true" onclick="exhibitor_huikan_view()">查看</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<!--注释掉搜索框 因为外部有搜索框  -->
	            	<!-- <label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a> -->
				</div>
			</div>	
		</div>
		<div title="展具增租申请" style="padding:1px">
			<table id="examine-zhanju-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_zhanju">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_zhanju" >
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
		<div title="特装报展表" style="padding:1px">
			<table id="examine-tezhuang-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_tezhuang">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_tezhuang">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
		<div title="物流申请表" style="padding:1px">
			<table id="examine-wuliu-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_wuliu">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_wuliu">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
		<div title="翻译申请表"  style="padding:1px">
			<table id="examine-fanyi-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_fanyi">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_fanyi">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-check" plain="true"  >查看</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
		<div title="会议申请表" style="padding:1px">
			<table id="examine-huiyi-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},"
					toolbar="#exhibitor_huiyi">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_huiyi">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="exhibitor_data_view()" plain="true">查看</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div>
			</div>	
		</div>
	</div>
	

<!-- begin of easyui-window -->
<div id="exhibitor-huikan-window" class="easyui-window" toolbar="#exhibitor-huikan-window-toolbar" data-options="closed:true,title:'会刊申请内容',modal:true" style="height:auto;;width:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-huikan-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="exhibitor-huikan-add" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_huikan_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" id="exhibitor-huikan-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_huikan_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" id="exhibitor-huikan-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_huikan_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" id="exhibitor-huikan-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_huikan_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="exhibitor-huikan-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_huikan_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-huikan-panel-1" class="easyui-panel" title="基本信息" style="width:670px;height:auto;padding:10px;">
    	<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label></div>
				<input hint="f_company_name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司名称(EN)</label></div>
				<input hint="f_company_english" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>联系人姓名</label></div>
				<input hint="f_contacts_name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>联系人姓名(EN)</label></div>
				<input hint="f_contacts_en" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>职务</label></div>
				<input hint="f_post" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司电话</label></div>
				<input hint="f_tel" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>传真</label></div>
				<input hint="f_fax" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>E-Mail</label></div>
				<input hint="f_email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>产品类型</label></div>
				<input hint="f_type_product" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>产品类型(EN)</label></div>
				<input hint="f_type_product_en" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司简介</label></div>
				<input hint="f_company_profile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司简介(EN)</label></div>
				<input hint="f_company_profile_en" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司网址</label></div>
				<input hint="f_site" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
				<input hint="f_memo" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
		</ul>
    </div>
    <div id="exhibitor-huikan-panel-2" class="easyui-panel" title="其他信息" style="width:670px;height:auto;padding:10px;">
    	<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:185px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司logo</label></div>
				<image id="exhibitor-huikan-logo" scr="" style="width:175px;height:175px" />
			</li>
		</ul>
    </div>
</div>
<!-- end of easyui-window -->



<script type="text/javascript">
	var exhibitor_boothinfo_alocate_id = -1;
	var exhibitor_boothinfo_save_id;
	var exhibitor_boothinfo_clientId;
	$(function(){
		var cnt = 0;
		$('#examine-select').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				if(cnt == 0){
					catalog_fresh();
					cnt++;
				}
				 $('#examine-booth').tabs('select','会刊申请表');
				 console.log('会刊申请表');
			}
		});
	});
	function catalog_fresh(){
		$('#examine-huikan-table').datagrid({
			url: eippath + '/catalog/getList?key=0',
			queryParams: {
				projectId: $('#examine-select').combobox('getValue')
            },
			onLoadSuccess: function(data){
				 
			}
		});
	}
	
	function exhibitor_catalog_search(){
		$('#examine-huikan-table').datagrid({
			url: eippath + '/catalog/getList?key=0',
			queryParams: {
				projectId: $('#examine-select').combobox('getValue')
            },
			onLoadSuccess: function(data){
				 
			}
		});
	}
	function exhibitor_examine(val, row){
		var  serve_type="f_serve_catalogue_id";
		var change = row.f_is_change;
		if(row.f_catalogue_confim == 1){
			/* return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'0\')">撤审</a>'; */
					
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
			+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
			'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
			+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
					
		}/* else if(row.f_catalogue_confim == 0){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
					'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
		} */else if(row.f_catalogue_confim == 2 ){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
			+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'0\')">撤审</a>'+
			'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
			+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
		}else if(row.f_catalogue_confim ==3){
			if(change==0){
				return '<a href="javascript:void(0);" style="color:#ccc;font-weight:bold;">审核 </a>'+
				'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
				+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
			}else{
				return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
				+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
				'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
				+row.f_project_id+'\',\''+row.f_serve_catalogue_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
			}
		}
			
		}
	function exhibitor_down(val,row){
		var  id = row.f_serve_catalogue_id;
			//表示当前状态是  客户已提交资料但是未曾审核
			return '<a href="javascript:void(0);" style=" font-weight:bold;" onclick="downfile('+id+')" >'+'查看'+'</a>';
		
		
	}
	function  downfile(id){
		 window.location.href = "http://"+ window.location.hostname+":8080/eip-web-business/catalog/download?fileType="+ id ;
	}
	function exhibitor_huikan_state(val,row){
		var  state = row.f_catalogue_confim;
		var change = row.f_is_change;
		var  why = row.f_reason;
		if(state==1){
			//表示当前状态是  客户已提交资料但是未曾审核
			
			return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'未审核'+'</a>';
		}else if(state ==2){
			//表示当前状态是 客户提交并通过审核
			return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'已审核'+'</a>';
		}else if(state ==3  ){
			//当前状态表示  客户提交资料但是被驳回  如果f_ischange=0 则表示 驳回之后客户没有提交新的资料
			//则不允许重新审核
			if(change==0){
				return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'驳回：'+why+'</a>';
			}else if(change==1){
				return  '<a href="javascript:void(0);" style=" font-weight:bold;">客户已重新上传</a>';
			}
		} 
		
		
		
	}
	function exhibitor_bytype_examine(pid,cid,serve_type,key){
		var  f_project_id = pid;
		var  f_client_id  = cid;
		var  serve_type = serve_type;
		var  key = key;
		$.messager.confirm('提示', '确定审核通过吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/examine/examine_bytype',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {f_project_id: f_project_id,sid:f_client_id,serve_type:serve_type,key:key},	//发送数据
					async: true,
					success: function(data){
						if(data.result == 1){
							if(key==1){
								$.messager.alert('提示','审核成功！','info');
								catalog_fresh();
							}else{
								$.messager.alert('提示','撤审成功！','info');
								catalog_fresh();
							}
							
						}else{
							if(key==1){
								$.messager.alert('提示','审核失败！','error');
							}else{
								$.messager.alert('提示','撤审失败！','error');
							}
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	function exhibitor_bytype_reject(pid,sid,serve_type,key){
		var  f_project_id = pid;
		var  sid  = sid;
		var  serve_type = serve_type;
		$.messager.prompt('提示', '请输入驳回原因：', function(r){
			if (r){
				var reason = r;//这里显示的是驳回原因
				$.ajax({
					url: eippath + '/examine/examine_reject',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {pid: f_project_id,sid:sid,serve_type:serve_type,reason:reason},	//发送数据
					async: true,
					success: function(data){
						if(data.result == 1){
							if(key==1){
								$.messager.alert('提示','驳回成功！','info');
								catalog_fresh();
							}else{
								$.messager.alert('提示','驳回成功！','info');
								catalog_fresh();
							}
							
						}else{
							if(key==1){
								$.messager.alert('提示','驳回失败！','error');
							}else{
								$.messager.alert('提示','驳回失败！','error');
							}
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	
	function exhibitor_huikan_mod(){
		exhibitor_huikan_mod_or_view(1);
	}
	function exhibitor_huikan_mod_or_view(flag){
		/*if(!exhibitor_huikan_window_close_set){
			exhibitor_huikan_window_close();
			exhibitor_huikan_window_close_set = true;
		}*/
		var row = $('#examine-huikan-table').datagrid('getSelected');
		if (row){
			exhibitor_huikan_alocate_id = row.f_serve_catalogue_id;
			$('#exhibitor-huikan-window').window('open');
			exhibitor_huikan_save_id = row.f_serve_catalogue_id;
			exhibitor_huikan_setEditInfoInit(row);
			exhibitor_huikan_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_huikan_mod2(){
		exhibitor_huikan_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function exhibitor_huikan_edit(flag){
		exhibitor_huikan_edit_mode = "Mod";
		exhibitor_huikan_setEditInfoReadOnly(flag);
		//exhibitor_huikan_setButtonEditMode(flag);
	}
	function exhibitor_huikan_view(){
		exhibitor_huikan_mod_or_view(2);
	}
	
	//设置编辑页面内容初始化
	function exhibitor_huikan_setEditInfoInit(row){
		$("#exhibitor-huikan-panel-1 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
		$("#exhibitor-huikan-logo").attr("src", eippath + '/imgfile/' + row['f_catalogue_logo']);
	}
	//设置编辑页面可编辑属性
	function exhibitor_huikan_setEditInfoReadOnly(flag){
		var readonly = true, able = 'disable';
		if(flag == 1){
			readonly = false;
			able = 'enable';
		}
		$("#exhibitor-huikan-panel-1 ul").find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
			else if(control == "checkbox"){
				if(flag == 1)$($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
	}
</script>	