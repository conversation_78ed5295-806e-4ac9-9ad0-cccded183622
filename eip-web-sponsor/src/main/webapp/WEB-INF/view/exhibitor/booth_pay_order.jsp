<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>预定支付订单</title>
	<script src="${eippath}/js/variable.js"></script>
	<link rel="stylesheet" type="text/css" href="${eippath}/jquery-easyui-*******/themes/default/easyui.css" />
	<link rel="stylesheet" type="text/css" href="${eippath}/jquery-easyui-*******/themes/icon.css?2022" />
	<script type="text/javascript" src="${eippath}/jquery-easyui-*******/jquery.min.js"></script>
	<script type="text/javascript" src="${eippath}/jquery-easyui-*******/jquery.easyui.min.js"></script>
	<script type="text/javascript" src="${eippath}/jquery-easyui-*******/locale/easyui-lang-zh_CN.js"></script>
	<script src="${eippath}/js/view/booth_orderPay.js?v=240702"></script>
	<style>
		.window-shadow{
			height: auto !important;
			/* max-height: 80vh; */
		}
		.datagrid-cell, .datagrid-cell-group, .datagrid-header-rownumber, .datagrid-cell-rownumber{
			padding: 0 5px !important;
		}
		.fakeLink {
			color: #0000ee;cursor: pointer;font-size: 14px;
		}
		.my-toolbar-button {
    	padding: 3px 5px;
		}
		.my-toolbar-search{
			padding: 0 5px 10px 12px;
		}
			.my-toolbar-search-p p{
				display: inline-block;
				margin-top: 4px;
				margin-bottom: 0;
			}
		.alex-form {
			display: flex;
			flex-wrap: wrap;
			padding: 20px 0;
		}
			.alex-form	.alex-form-item {
				flex-basis: 50%;
				margin-bottom: 10px;
				display: flex;
				/* align-items: center; */
			}
				.alex-form	.form-item__label{
					flex-basis: 90px;
					text-align: right;
					margin-right: 10px;
				}
				.alex-form	.alex-form-item:last-child {
					margin-bottom: 20px;
				}
				.alex-form	.full {
					flex-basis: 100%;
				}
	</style>
</head>
<body>
<!-- TODO 跳转 -->
<div class="easyui-layout booth-pay" data-options="fit:true">
	<div data-options="region:'center',border:false" id="main">
		<div id="booth-pay-toolbar">
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconcls="icon-view"
				 onclick="app.view()"  plain="true">查看</a>
			</div>
			<form id="booth-pay-search" class="my-toolbar-search my-toolbar-search-p">
				<p><label>公司名称</label>&nbsp;&nbsp;
					<input id="booth-pay-search-companyName" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;</p>
				<p><label>展位号</label>&nbsp;&nbsp;
					<input id="booth-pay-search-boothNum" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;</p>
				<p><label>订单ID</label>&nbsp;&nbsp;
					<input id="booth-pay-search-preOrderIdLike" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;</p>
				<p><label>订单创建时间</label>&nbsp;&nbsp;
					<input id="booth-pay-search-orderCreateStartTime" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>
					<span>-</span>
					<input id="booth-pay-search-orderCreateEndTime" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
					</p><br />
				<p><label>支付状态</label>&nbsp;&nbsp;
						<select id="booth-pay-search-payOrderState" class="easyui-combobox" style="width:100px;" data-options="panelHeight:'auto'">
							<option value="">全部</option>
							<option value="0">未支付</option>
							<option value="1">已支付</option>
							<option value="2">取消支付</option>
							<option value="3">已退款</option>
						</select>&nbsp;&nbsp;&nbsp;&nbsp;
						</p>
				<p><label>支付完成时间</label>&nbsp;&nbsp;
					<input id="booth-pay-search-paySuccessStartTime" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>
					<span>-</span>
					<input id="booth-pay-search-paySuccessEndTime" class="easyui-datebox" style="width:120px;height: 25px;" data-options="panelHeight:'auto'"/>&nbsp;&nbsp;&nbsp;&nbsp;
					</p>
				<p><label>支付方式</label>&nbsp;&nbsp;
					<select id="booth-pay-search-payType" class="easyui-combobox" style="width:100px;" data-options="panelHeight:'auto',onChange(n,o) {
						if(n) {
							$('#booth-pay-search-orderTradeIdLike').textbox({
								disabled: false,
								value: ''
							})
						} else {
							$('#booth-pay-search-orderTradeIdLike').textbox({
								disabled: true,
								value: ''
							})
						}
					}">
						<option value="">全部</option>
						<option value="0">微信</option>
						<option value="1">支付宝</option>
						<option value="2">网银</option>
						<option value="3">手动到款</option>
						<option value="4">优惠卷</option>
						<option value="5">邀请码</option>
						<option value="6">优惠码</option>
						<option value="10">微信小程序</option>
					</select>&nbsp;&nbsp;&nbsp;&nbsp;
					</p>
				<p><label>支付订单ID</label>&nbsp;&nbsp;
					<input id="booth-pay-search-orderTradeIdLike"  data-options="disabled: false" class="my-text easyui-textbox" style="width:120px;height:25px">&nbsp;&nbsp;&nbsp;&nbsp;
					</p>
				<p>&nbsp;&nbsp;<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="app.search()">查询</a></p>
			</form>
		</div>
		<table id="booth-pay-datagrid" class="easyui-datagrid" toolbar="#booth-pay-toolbar"></table>
	</div>
</div>

<%-- 查看弹窗  --%>
<style>
#view-window{
	display: none;
}
.hide {
	display: none !important;
}
</style>
<div id="view-window" class="booth-pay easyui-window" title="预定支付订单"
 data-options="closed:true,modal:true">
	<div class="my-toolbar-button datagrid-toolbar">
		<a href="javascript:void(0);"
			class="easyui-linkbutton"
			iconCls="icon-refund"
			id="js-refund"
			class="hide"
			onclick="app.refund()"
			plain="true"
			style="margin-right: 6px;">退款</a>
		<a href="javascript:void(0);"
			class="easyui-linkbutton"
			iconCls="icon-wechat"
			onclick="app.getDetail()"
			plain="true"
			style="margin-right: 6px;">查询支付状态</a>
	</div>
	<div class="easyui-panel view-order" data-options="headerCls: 'view-order'"  style="width: 100%" title="订单信息">
		<div class="alex-form">
			<div class="alex-form-item full">
				<span class="form-item__label">项目</span>
				<span class="form-item__value" data-key="exhibitName"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">订单编号</span>
				<span class="form-item__value" data-key="boothPreOrderId"></span>
			</div>
			<!-- <div class="alex-form-item">
				<span class="form-item__label">订单状态</span>
				<span class="form-item__value" data-key="payOrderState"></span>
			</div> -->
			<div class="alex-form-item full">
				<span class="form-item__label">订单创建时间</span>
				<span class="form-item__value" data-key="orderCreateTime"></span>
			</div>
			<div class="alex-form-item full">
				<span class="form-item__label">公司名称</span>
				<span class="form-item__value" data-key="companyName"></span>
			</div>
			<div class="alex-form-item full">
				<span class="form-item__label">展位号</span>
				<span class="form-item__value" data-key="boothNum"></span>
			</div>
			<div class="alex-form-item ">
				<span class="form-item__label">支付定金</span>
				<span class="form-item__value" data-key="payMoney"></span>
			</div>
		</div>
	</div>
	<div class="easyui-panel view-wechat" data-options="headerCls: 'view-wechat'" style="width: 100%;display: none" title="支付信息">
		<div class="alex-form">
			<div class="alex-form-item">
				<span class="form-item__label">支付方式</span>
				<span class="form-item__value" data-key="payTypeName"></span>
			</div>
			<!-- <div class="alex-form-item full"> -->
			<div class="alex-form-item">
				<span class="form-item__label">支付订单ID</span>
				<span class="form-item__value" data-key="transactionId"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">交易状态</span>
				<span class="form-item__value" data-key="payOrderState"></span>
			</div>
			<div class="alex-form-item">
				<span class="form-item__label">支付完成时间</span>
				<span class="form-item__value" data-key="paySuccessTime"></span>
			</div>
			<div class="alex-form-item hide js-refundOnly">
				<span class="form-item__label">退款单ID</span>
				<span class="form-item__value" data-key="refundOrderId"></span>
			</div>
			<div class="alex-form-item hide js-refundOnly">
				<span class="form-item__label">退款时间</span>
				<span class="form-item__value" data-key="refundTime"></span>
			</div>
		</div>
	</div>
</div>
</body>
</html>