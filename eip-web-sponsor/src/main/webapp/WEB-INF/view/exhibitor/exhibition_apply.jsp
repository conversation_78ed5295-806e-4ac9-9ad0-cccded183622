
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibition-apply-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibition_apply_fresh()" plain="true">刷新</a>
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibition_apply_add()" plain="true">新增</a> -->
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibition_apply_mod()" plain="true">修改</a> -->
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibition_apply_del_batch()" plain="true">删除</a> -->
            	<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="exhibition_apply_view()" plain="true">查看</a> -->
            </div>
             <div class="my-toolbar-search">
             	<label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibition-apply-search-company" class="my-text" style="width:200px">&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>状态</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="exhibition-apply-search-state" class="easyui-combobox" panelMaxHeight="100px" data-options="editable:false,panelHeight:'auto'" style="width:175px;height:25px">
            		<option value="0">未处理</option>
            		<option value="1">已处理</option>
            	</select>&nbsp;&nbsp;&nbsp;&nbsp;
            	
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibition_apply_fresh()">查询</a>
            </div> 
        </div>
        <!-- end of toolbar -->
        <table id="exhibition-apply-datagrid"  toolbar="#exhibition-apply-toolbar"
        	data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',
        		selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			<!-- exhibitor_contract_mod(); -->
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'company',width:150">公司名称</th>
					<th data-options="field:'area',width:70">展位需求面积</th>
					<th data-options="field:'linkman',width:60">联系人</th>
					<th data-options="field:'sex',width:60">性别</th>
					<th data-options="field:'position',width:60">职务</th>
					 <th data-options="field:'tel',width:80">电话</th>
					<th data-options="field:'phone',width:80">手机</th>
					<th data-options="field:'email',width:120">邮箱</th>
					<th data-options="field:'applyTime',width:120">申请时间</th>
					<th data-options="field:'state',width:80" formatter="exhibition_apply_formatState">状态</th>
					<th data-options="field:'handlePersonName',width:60">处理人</th>
					<th data-options="field:'handleTime',width:120">处理时间</th>
				<!-- 	<th data-options="field:'isPayment',width:80" formatter="exhibitor_contract_formatIsPayment">首付款是否到账</th>
					<th data-options="field:'paymentData',width:150">首付款到账时间</th> -->
					<th data-options="field:'do',width:80,align:'center'" formatter="exhibition_apply_formatDo">操作</th>
				</tr>
			</thead>
        </table>
    </div>
</div>





<script type="text/javascript">
	/*  var exhibitor_contract_edit_mode;
	var exhibitor_contract_button_edit_mode;	
	var exhibitor_contract_window_close_set = false;
	var exhibitor_contract_save_id;
	var exhibitor_contract_projCltId;  */
	
	var operatorId='${sessionScope.operatorId}';
	var exhibition_apply_alocate_id = -1;
	function exhibition_apply_formatState(val, row){
		if(val == 0)return '未处理';
		else return '已处理';
	}
	function exhibition_apply_formatDo(val, row){
		if(row.state==0){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibition_apply_mod_handle('
					+row.exhibitionApplyId+','+row.state+')">转为已处理</a>';
		}else{
			return '已处理';
		}
		

	}
	
	
	$(function(){
		
		 exhibition_apply_fresh();
		
	});
	

	function exhibition_apply_fresh(){
		
		$('#exhibition-apply-datagrid').datagrid({
			url: eippath + '/exhibitionApply/getList?key=1',
			queryParams: {
				exhibitCode: exhibitCode_for_pass,
				state:$('#exhibition-apply-search-state').val(),
				company:$('#exhibition-apply-search-company').val()
            },		
		 	onLoadSuccess: function(data){
				
				if(exhibition_apply_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].exhibitionApplyId == exhibition_apply_alocate_id){
							$(this).datagrid('selectRow', i);
							exhibition_apply_alocate_id = -1;
							break;
						}
					}
				}
			} 
		});
	}
	
	
	function exhibition_apply_mod_handle(id,state){
	
		$.ajax({
			url: eippath + '/exhibitionApply/updateState',
			type: "post",
			dataType:"json",  
			data:{
				exhibitionApplyId:id,
				state:1,
				handlePerson:operatorId
			},
			async: true,
			success: function(data){
				if(data.state >0){
					$.messager.alert('提示','操作成功！','info');
					exhibition_apply_fresh();
					
				}else{
					$.messager.alert('提示','操作失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	
	
	
	
	
	
</script>