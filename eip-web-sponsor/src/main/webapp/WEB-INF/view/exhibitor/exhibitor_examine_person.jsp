<!--人员资料审核导入管理 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
	<!--<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>-->
	<div class="easyui-panel" style="padding:5px;">
		<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="examine-person-select" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" onclick="exhibitor_person_search()" >查询</a>
	</div>
	<div class="easyui-tabs" style=" width:100%;height:100%">
		<div title="人员明细确认表" style="padding:1px">
			<table id="examine-person-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'
					,emptyMsg:'未查询到相关记录！'  ,selectOnCheck:true,
	        		onClickRow: function (rowIndex, rowData) {
	        			var l = $(this).datagrid('getRows').length;
	        			for(var i = 0; i < l; i++){
	               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	               		}
	 				}," 
					toolbar="#exhibitor_renyuan">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="f_company_name" width="180" align="right">公司名称</th>
						<th field="f_company_english" width="180" align="right">公司名称(EN)</th>
						<th field="f_name" width="100">人员姓名</th>
						<th field="f_work_phone" width="100">单位电话</th>
						<th field="f_mobile" width="100">手机号</th>
						<th field="f_id_number" width="80" align="right">身份证号</th>
						<th field="f_catalogue_confim" width="80"  align="center" formatter="staff_person_state">状态</th>
						<th data-options="field:'do',width:100,align:'center'" formatter="staff_examine">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_renyuan">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" plain="true" onclick="exhibitor_person_view()">查看</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            <!-- <div class="my-toolbar-search">
	            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
				    <select id="exhibitor-contract-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
					合同号：<input  iconCls="icon-search" />	
					<a href="#" class="easyui-linkbutton"    style="height:25px" >查询</a>
				</div> -->
			</div>	
		</div>
		<div title="签证资料" style="padding:1px">
			<table id="examine-qianzheng-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_qianzheng">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_qianzheng" >
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
			</div>	
		</div>
		<div title="机票申请表" style="padding:1px">
			<table id="examine-jipiao-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_jipiao">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_jipiao">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	             
			</div>	
		</div>
		<div title="地接申请表" style="padding:1px">
			<table id="examine-dijie-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_dijie">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_dijie">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	             
			</div>	
		</div>
		<div title="酒店申请表" style="padding:1px">
			<table id="examine-jiudian-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_jiudian">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_jiudian">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	            
			</div>	
		</div>
		<div title="保险申报表" style="padding:1px">
			<table id="examine-baoxian-table" class="easyui-datagrid" 
					data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',emptyMsg:'未查询到相关记录！'"
					toolbar="#exhibitor_baoxian">
				<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th field="f_contract_id" width="80">合同号</th>
						<th field="f_client_id" width="80">公司编号</th>
						<th field="listprice" width="180" align="right">公司名称</th>
						<th field="f_booth_code" width="100">展位号</th>
						<th field="f_booth_amount" width="100">展位数量</th>
						<th field="f_staff_amount" width="100">人员总数</th>
						<th field="unitcost" width="80" align="right">签约日期</th>
						<th field="attr1" width="100">合同金额</th>
						<th field="1" width="100" align="center">首付款已到否</th>
						<th field="2" width="110" align="center">首付款到帐日期</th>
						<th field="3" width="80" align="center">操作</th>
					</tr>
				</thead>
			</table>
			<div id="exhibitor_baoxian">
				<div class="my-toolbar-button">
					<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="Contract_add_window()">新增</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
					<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
					<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
	            </div>
	             
			</div>	
		</div>
	</div>
	
<!-- begin of easyui-window -->
<div id="exhibitor-person-window" class="easyui-window" toolbar="#exhibitor-person-window-toolbar" data-options="closed:true,title:'会刊申请内容',modal:true" style="height:auto;;width:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-person-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="exhibitor-person-add" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_person_add()" plain="true">新增</a>
        	<a href="javascript:void(0);" id="exhibitor-person-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_person_mod2()" plain="true">修改</a>
        	<a href="javascript:void(0);" id="exhibitor-person-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_person_del()" plain="true">删除</a>
        	<a href="javascript:void(0);" id="exhibitor-person-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_person_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="exhibitor-person-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_person_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-person-panel-1" class="easyui-panel" title="基本信息" style="width:670px;height:auto;padding:10px;">
    	<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label></div>
				<input hint="f_company_name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司名称(EN)</label></div>
				<input hint="f_company_english" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>人员姓名</label></div>
				<input hint="f_name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>姓名拼音</label></div>
				<input hint="f_pinyin_name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>文化程度</label></div>
				<input hint="f_education_id" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>职位</label></div>
				<input hint="f_position_id" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司电话</label></div>
				<input hint="f_work_phone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>手机号</label></div>
				<input hint="f_mobile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>E-Mail</label></div>
				<input hint="f_email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>身份证号</label></div>
				<input hint="f_id_number" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>身份证签发单位</label></div>
				<input hint="f_id_issue" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>家庭住址</label></div>
				<input hint="f_home_adress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>现住址</label></div>
				<input hint="f_now_adress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>民族</label></div>
				<input hint="f_nation_id" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>外语能力</label></div>
				<input hint="f_ability_language" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>性别</label></div>
				<input hint="f_sex_id" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>护照类型</label></div>
				<input hint="f_pass_type_id" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>护照号码</label></div>
				<input hint="f_pass_number" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>护照签发地</label></div>
				<input hint="f_pass_adress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>护照签发期</label></div>
				<input hint="f_pass_time" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>护照有效期</label></div>
				<input hint="f_pass_validity" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>出生日期</label></div>
				<input hint="f_born_data" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>出生地</label></div>
				<input hint="f_born_adress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
		</ul>
    </div>
    <!-- <div id="exhibitor-person-panel-2" class="easyui-panel" title="其他信息" style="width:670px;height:auto;padding:10px;">
    	<ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:185px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>公司logo</label></div>
				<image id="exhibitor-person-logo" scr="" style="width:175px;height:175px" />
			</li>
		</ul>
    </div> -->
</div>
<!-- end of easyui-window -->	
<script type="text/javascript">
	var exhibitor_staff_alocate_id = -1;
	var exhibitor_staff_save_id;
	var exhibitor_staff_clientId;
	$(function(){
		var cnt = 0;
		$('#examine-person-select').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				if(cnt == 0){
					person_fresh();
					cnt++;
				}
				 $('#examine-booth').tabs('select','人员明细确认表');
			}
		});
	});
	function person_fresh(){
		$('#examine-person-table').datagrid({
			url: eippath + '/person/getList?key=0',
			queryParams: {
				projectId: $('#examine-select').combobox('getValue')
            },
			onLoadSuccess: function(data){
				 
			}
		});
	}
	
	function exhibitor_person_search(){
		  $('#examine-person-table').datagrid({
			url: eippath + '/person/getList?key=0',
			queryParams: {
				projectId: $('#examine-person-select').combobox('getValue')
            },
			onLoadSuccess: function(data){
				 
			}
		});  
	}
	function staff_examine(val, row){
		var  serve_type="f_serve_person_id";
		var change = row.f_is_change;
		var  state = row.f_person_confim;
		if(state == 1){
			/* return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'0\')">撤审</a>'; */
					
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
			+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
			'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
			+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
					
		}/* else if(row.f_person_confim == 0){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
					'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
					+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
		} */else if(state == 2 ){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
			+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'0\')">撤审</a>'+
			'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
			+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
		}else if(state ==3){
			if(change==0){
				return '<a href="javascript:void(0);" style="color:#ccc;font-weight:bold;">审核 </a>'+
				'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
				+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
			}else{
				return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_bytype_examine(\''
				+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'1\')">审核 </a>'+
				'<a href="javascript:void(0);" style="color:red;font-weight:bold;" onclick="exhibitor_bytype_reject(\''
				+row.f_project_id+'\',\''+row.f_serve_person_id+'\',\''+serve_type+ '\',\'2\')">&nbsp; 驳回</a>';
			}
		}
			
		}
	function staff_person_state(val,row){
		var  state = row.f_person_confim;
		var change = row.f_is_change;
		var  why = row.f_reason;
		if(state==1){
			//表示当前状态是  客户已提交资料但是未曾审核
			return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'未审核'+'</a>';
		
		}else if(state ==2){
			//表示当前状态是 客户提交并通过审核
			return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'已审核'+'</a>';
		}else if(state ==3  ){
			//当前状态表示  客户提交资料但是被驳回  如果f_ischange=0 则表示 驳回之后客户没有提交新的资料
			//则不允许重新审核
			if(change==0){
				return '<a href="javascript:void(0);" style=" font-weight:bold;">'+'驳回：'+why+'</a>';
			}else if(change==1){
				return  '<a href="javascript:void(0);" style=" font-weight:bold;">客户已重新上传</a>';
			}
		} 
		
		
		
	}
	function exhibitor_bytype_examine(pid,cid,serve_type,key){
		var  f_project_id = pid;
		var  f_client_id  = cid;
		var  serve_type = serve_type;
		var  key = key;
		$.messager.confirm('提示', '确定审核通过吗？', function(r){
			if (r){
				$.ajax({
					url: eippath + '/examine/examine_bytype',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {f_project_id: f_project_id,sid:f_client_id,serve_type:serve_type,key:key},	//发送数据
					async: true,
					success: function(data){
						if(data.result == 1){
							if(key==1){
								$.messager.alert('提示','审核成功！','info');
								person_fresh();
							}else{
								$.messager.alert('提示','撤审成功！','info');
								person_fresh();
							}
							
						}else{
							if(key==1){
								$.messager.alert('提示','审核失败！','error');
							}else{
								$.messager.alert('提示','撤审失败！','error');
							}
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	function exhibitor_bytype_reject(pid,sid,serve_type,key){
		var  f_project_id = pid;
		var  sid  = sid;
		var  serve_type = serve_type;
		$.messager.prompt('提示', '请输入驳回原因：', function(r){
			if (r){
				var reason = r;//这里显示的是驳回原因
				$.ajax({
					url: eippath + '/examine/examine_reject',
					dataType: "json",	//返回数据类型
					type: "post",
					data: {pid: f_project_id,sid:sid,serve_type:serve_type,reason:reason},	//发送数据
					async: true,
					success: function(data){
						if(data.result == 1){
							if(key==1){
								$.messager.alert('提示','驳回成功！','info');
								person_fresh();
							}else{
								$.messager.alert('提示','驳回成功！','info');
								person_fresh();
							}
							
						}else{
							if(key==1){
								$.messager.alert('提示','驳回失败！','error');
							}else{
								$.messager.alert('提示','驳回失败！','error');
							}
						}
					},
					error: function(){
						$.messager.alert('提示','数据发送失败！','error');
					}
				});
			}
		});
	}
	/*查看人员  */
	function exhibitor_person_view(){
		exhibitor_person_mod_or_view(2);
	}
	function exhibitor_person_mod_or_view(flag){
		/*if(!exhibitor_person_window_close_set){
			exhibitor_person_window_close();
			exhibitor_person_window_close_set = true;
		}*/
		var row = $('#examine-person-table').datagrid('getSelected');
		if (row){
			exhibitor_person_alocate_id = row.f_serve_catalogue_id;
			$('#exhibitor-person-window').window('open');
			exhibitor_person_save_id = row.f_serve_catalogue_id;
			exhibitor_person_setEditInfoInit(row);
			exhibitor_person_edit(flag);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function exhibitor_person_edit(flag){
		exhibitor_person_edit_mode = "Mod";
		exhibitor_person_setEditInfoReadOnly(flag);
		//exhibitor_person_setButtonEditMode(flag);
	}
	//设置编辑页面内容初始化
	function exhibitor_person_setEditInfoInit(row){
		$("#exhibitor-person-panel-1 ul").find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
		$("#exhibitor-person-logo").attr("src", eippath + '/imgfile/' + row['f_catalogue_logo']);
	}
	//设置编辑页面可编辑属性
	function exhibitor_person_setEditInfoReadOnly(flag){
		var readonly = true, able = 'disable';
		if(flag == 1){
			readonly = false;
			able = 'enable';
		}
		$("#exhibitor-person-panel-1 ul").find("li").each(function(){
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if(control == "datebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox(able);
			else if(control == "combobox")$($(this).find("input")[0]).combobox(able);
			else if(control == "checkbox"){
				if(flag == 1)$($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
	}
</script>	