<%--
  User: ZzzHhYyy
  Date: 2020-04-28
  Time: 10:16
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>展品访客统计</title>
    <!-- import Vue before Element -->
    <script src="../../vue/vue.js"></script>
    <script src="../../vue/axions.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../vue/element-ui/index.css">
    <!-- 引入组件库 -->
    <script src="../../vue/element-ui/index.js"></script>
	<style type="text/css">
		body{margin: 0;}
		.title{width: 100%;height: 60px;line-height: 60px;}
		.title h6{font-size: 18px;color: #354052;box-sizing: border-box;padding-left: 360px;margin: 0;font-weight: normal;}
		.top-name{height: 72px;background-color: #36a1db;}
		.top-name h5{font-size: 16px;color: #fff;line-height: 72px;margin: 0;text-align: center;}
		.box{box-sizing: border-box;padding: 0 320px;overflow: hidden;}
		.search-content{float: left;margin-left: 40px;margin-top: 40px;margin-bottom: 20px;}
		.search-content label{float: left;font-size: 14px;color: #666666;font-weight: bold;line-height: 26px;margin-right: 22px;}
		.search-content input{width: 181px;height: 26px;background-color: #ffffff;border-radius: 4px;border: solid 1px #cccccc;float: left;}
	    .search-content>.el-input{width: auto;float: left;}
		.search-content>button{width: 70px;height: 26px;background-color: #4c82fe;border-radius: 4px;padding: 0;}
		.search-content1>button{width: 70px;height: 26px;background-color: #fff;color: #666; border-radius: 4px;padding: 0;}
	</style>
</head>
<body>
<div class="title">
	<h6>展品访客浏览量统计</h6>
</div>
<div class="top-name">
	<h5>展品访客统计明细</h5>
</div>
<div class="box">
<div id="app">
	<div class="search-content">
		<label>请输入会刊公司名称、展品名称、展品类型搜索</label>
		<el-input placeholder="请输入会刊公司名称/展品名称/展品类型" v-model="keyWords"></el-input>
	</div>
	<div class="search-content">
		<el-button type="primary" @click="getTableData">查询</el-button>
	</div>
	<div class="search-content search-content1">
		<el-button @click="exportByStatistics">导出</el-button>
	</div>
    <el-table ref="singleTable" :data="tableData" show-summary  :summary-method="getSummaries"
              :row-class-name="tableRowClassName"
              highlight-current-row style="width: 100%">
        <el-table-column type="index" label="序号" width="100"></el-table-column>
        <el-table-column property="productName" label="展品名称"></el-table-column>
        <el-table-column property="companyName" label="所属公司"></el-table-column>
        <el-table-column property="productTypeName" label="展品类别"></el-table-column>
        <el-table-column property="sectionName" label="所属展区"></el-table-column>
        <el-table-column property="boothNum" label="展位号"></el-table-column>
        <el-table-column property="readNum" label="展品浏览量"></el-table-column>
        <el-table-column property="collectionsCount" label="展品收藏量"></el-table-column>
        <el-table-column property="inqCount" label="展品询盘量"></el-table-column>
    </el-table>
    <div class="block">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
        </el-pagination>
    </div>
</div>
</div>
</body>
<script>
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var url = decodeURI(decodeURI(window.location.search))
        var r = url.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    var vm = new Vue({
        el: '#app',
        data: {
            tableData: [],
            keyWords: "",
            total:0,
            currentPage:1,
            pageSize:20,
        },
        methods: {
            setCurrent(row) {
                this.$refs.singleTable.setCurrentRow(row);
            },
            getTableData() {
                axios.get('${pageContext.request.contextPath}/serveProduct/getByStatistics', {
                    params: {
                        projectId: getQueryString("projectId") || '',
                        exhibitCode: getQueryString("exhibitCode") || '',
                        keyWords: this.keyWords || '',
                        page: this.currentPage,
                        rows: this.pageSize
                    }
                }).then(response => {
                    this.keyWords = ""
                    this.tableData = response.data.result
                    this.total = response.data.pageUtil.totalRow
                }, response => {
                    console.log("error");
                });
            },
            handleSizeChange(val) {
                this.pageSize=val;
                this.getTableData()
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.currentPage=val;
                this.getTableData()
                console.log(`当前页: ${val}`);
            },
            tableRowClassName({row, rowIndex}) {
                if (rowIndex %2== 1) {
                    return 'warning-row';
                } else {
                    return 'success-row';
                }
            },
            getSummaries(param) {
                const { columns, data } = param;
                const sums = [];
                columns.forEach((column, index) => {
                    if (index === 0) {
                        sums[index] = '本页浏览量统计';
                        return;
                    }else if (index<6){
                        // sums[index] = 'N/A';
                        return;
                    }
                    let reduce =0;
                    const values = data.map(item => item[column.property]);
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = '合计 ';
                        sums[index] += reduce+values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                        // sums[index] += ' ';
                    } else {
                        // sums[index] = 'N/A';
                    }
                });
                return sums;
            },
            exportByStatistics(){
                location.href="${pageContext.request.contextPath}/serveProduct/exportByStatistics?projectId="
                    +getQueryString("projectId")+"&exhibitCode="+getQueryString("exhibitCode")+"&keyWords="+this.keyWords;
            }
        },
        mounted() {
            this.getTableData();
        }
    })
</script>
</html>
