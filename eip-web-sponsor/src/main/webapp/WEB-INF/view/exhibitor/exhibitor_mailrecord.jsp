<!--邮寄记录管理 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />


<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
	</style>
<table id="tt" class="easyui-datagrid"  。
		toolbar="#tb9"
		data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'"
		>
	<thead>
		<tr>
			<th data-options="field:'ck',checkbox:true"></th>
			<th field="itemid" width="80">编号</th>
			<th field="productid" width="120">资料证件类别</th>
			<th field="listprice" width="180" align="right">邮寄地址</th>
			<th field="unitcost" width="80" align="right">联系人</th>
			<th field="attr1" width="150">手机号</th>
			<th field="1" width="110" align="center">电话</th>
			<th field="2" width="110" align="center">状态</th>
			<th field="3" width="90" align="center">寄出时间</th>
			<th field="4" width="80" align="center">快递单号</th>
		</tr>
	</thead>
	<tbody>
		 <tr>
	      <td>杭州信息</td>
	      <td>210</td>
	      <td>签证</td>
	      <td>下沙六号大街</td>
	      <td>吕蒙</td>
	      <td>15200215461</td>
	      <td>0571-86788784</td>
	      <td>签收</td>
	      <td>2018/08/07</td>
	      <td>8451124545</td>
	    </tr>
	    <tr>
	      <td>杭州信息</td>
	      <td>210</td>
	      <td>签证</td>
	      <td>西湖景区</td>
	      <td>韩信</td>
	      <td>15200215461</td>
	      <td>0571-86788784</td>
	      <td>在途</td>
	      <td>2018/08/07</td>
	      <td>A4514254</td>
	    </tr>
		
	</tbody>
</table>
<div id="tb9">
	
	公司名称：<input  iconCls="icon-search"/>	
	手机号：<input  iconCls="icon-search"/>
	寄出时间：<input  iconCls="icon-search"/>至<input  iconCls="icon-search"/>
	<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >查询</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导入</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-ok" plain="true"  >导出</a>
	
	<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
</div>


	 
	<div id="w" class="easyui-window" title="新增任务" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:500px;height:auto;padding:10px;">
	<div style="margin:20px 0;"></div>
	<div style="width:100%;max-width:400px;">
		<div style="margin-bottom:20px">
			<label for="username" class="label-top">名称:</label>
			<input id="username" class="easyui-validatebox tb" data-options="prompt:'Enter User Name.',required:true,validType:'length[3,10]'">
		</div>
		<div style="margin-bottom:20px">
			<label for="email" class="label-top">类别:</label>
			<input id="email" class="easyui-validatebox tb" data-options="prompt:'Enter a valid email.',required:true,validType:'email'">
		</div>
		<div style="margin-bottom:20px">
			<label for="url" class="label-top">时间:</label>
			<input id="url" class="easyui-validatebox tb" data-options="prompt:'Enter your URL.',required:true,validType:'url'">
		</div>
		<div style="margin-bottom:20px">
			<label for="phone" class="label-top">地点:</label>
			<input id="phone" class="easyui-validatebox tb" data-options="prompt:'Enter your phone number.',required:true">
		</div>
	</div>
		
	<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">确认</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">关闭</a>

	
	</div>
	
<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
		});
	</script>