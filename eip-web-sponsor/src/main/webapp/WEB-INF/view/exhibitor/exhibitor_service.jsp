<!--展商服务管理中心  -->
<%@ page contentType="text/html;charset=UTF-8" %>
	
	 
	<div style="width: 80%;margin-left: 10%;min-height: 300px;border:1px solid  #00BBEE;  ">
	
	<span style="font-size: 22px;margin-left: 20px; float: left;">LOGO</span>
	<span style="font-size: 22px;margin-left: 20px; float: left">2018上海家居展----展商服务管理中心</span>
		<span style="margin-left: 360px;">当前展会:</span>
		<select id="state" class="easyui-combobox" name="state"  labelPosition="top" style="width:200px;">
				<option value="AL">测试展会2</option>
				<option value="AK">测试展会3</option>
				<option value="AZ">测试展会4</option>
				<option value="AR">测试展会5</option>
				<option value="CA">测试展会6</option>
				<option value="CO">测试展会7</option>
			</select>
		  <a href="#" class="easyui-linkbutton" iconCls="icon-reload"onclick="reflush()" >刷新</a>
	 
		<div style="margin-top: 20px;">
			<span style=" margin-left: 50px;">开展时间：2018年9月11日</span>
		<span style="float: right; margin-right: 50px;">闭展时间：2018年9月11日</span>
		<table class="easyui-datagrid" style="width: 520px;">
			<thead>
				<tr>
				<th data-options="field:'1',width:160">职责</th>
				<th data-options="field:'2',width:180">姓名</th>
				<th data-options="field:'3',width:160">电话</th>
			</tr>
			</thead>
			<tbody>
				<tr>
				<td>负责人</td>
				<td>张三</td>
				<td>15355468115</td>
				</tr>
				<tr>
				<td>外联</td>
				<td>张三</td>
				<td>15355468115</td>
				</tr>
				<tr>
				<td>客服</td>
				<td>张三</td>
				<td>15355468115</td>
				</tr>
				
				<tr>
				<td>签证</td>
				<td>张三</td>
				<td>15355468115</td>
				</tr>
				<tr>
				<td>商旅</td>
				<td>张三</td>
				<td>15355468115</td>
				</tr>
			</tbody>
		</table>
		</div>
	</div> 
	<div style="width: 80%;margin-left: 10%;border:1px solid  #00BBEE; margin-top: 50px;">
		<div>
			<h2>展商服务进度</h2>
			
		</div>
			<div>
				<span style="height: 40px; width: 120px;border:1px solid  #31B0D5;"><a href="#" title="打开展商合同管理" class="easyui-tooltip">展位销售进度:60%</a></span>
				<span style="height: 40px; width: 120px;border:1px solid  #31B0D5;"><a href="#" title="打开展位信息管理" class="easyui-tooltip">展位服务:60%</a></span>
				<span style="height: 40px; width: 120px;border:1px solid  #31B0D5;"><a href="#" title="打开人员信息管理" class="easyui-tooltip">人员服务:60%</a></span>
		
			</div>
		 <p>观众总数量:26438人</p> 
		 <p>展位总数:特装：102个  标摊：632个</p>
		 <p>已售展位:特装：78个  标摊：497个</p>
		  <p>展位服务进度:特装已报展：65家  楣板确认：465个</p>
		  <p style="margin-left: 100px;">会刊已提交572个</p>
		  <p>展具增租103家</p>
		  <p>人员总数：<span style="background-color: #0092DC; color: #E6E6E6;">参展人数73人</span> </p>
		  <p>人员资料：<span style="background-color: #0092DC; color: #E6E6E6;"> 已提交资料 68人</span></p>
		  <p>签证办理：<span style="background-color: #0092DC; color: #E6E6E6;"> 资料不齐5人</span> <span style="background-color: #449D44; color: #E6E6E6;">待送签13人</span> 已送签 18人 已出签28人</p>
		  <p>机票办理： <span style="background-color: #0092DC; color: #E6E6E6;">资料不齐8人</span> 待出票 43 人 已出票 22 人</p>
		  <p>酒店办理：<span style="background-color: #0092DC; color: #E6E6E6;"> 未确认6人</span> 确认中 29 人  已确认 38 人</p>
		  <div style="padding:10px 200px">
			
		</div>
		  
		  
	</div> 
	
	<script type="text/javascript">
		function setvalue(){
			$.messager.prompt('SetValue','Please input the value(CO,NV,UT,etc):',function(v){
				if (v){
					$('#state').combobox('setValue',v);
				}
			});
		}
	</script>

