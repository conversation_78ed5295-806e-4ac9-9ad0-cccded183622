<!--人员信息管理 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibitor-personnelinfo-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_personnelinfo_fresh()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_personnelinfo_mod()" plain="true">修改</a>
            </div>
            <div class="my-toolbar-search">
            	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
            	<select id="exhibitor-personnelinfo-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-personnelinfo-keyword-companyName" class="my-text" style="width:200px">
            	<a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="exhibitor_personnelinfo_select_company()" >选取</a>&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-personnelinfo-keyword-staffName" class="my-text" style="width:200px">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_personnelinfo_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="exhibitor-personnelinfo-datagrid" class="easyui-datagrid" toolbar="#exhibitor-personnelinfo-toolbar" 
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'staffId',width:60">人员编号</th>
					<th data-options="field:'staffName',width:80">人员姓名</th>
					<th data-options="field:'companyName',width:120">公司名称</th>
					<th data-options="field:'boothCode',width:80">展位号</th>
					<th data-options="field:'personConfim',width:70,align:'center'" formatter="exhibitor_personnelinfo_formatPersonConfim">人员确认</th>
					<!-- <th data-options="field:'personstate',width:70,align:'center'" formatter="exhibitor_personnelinfo_state">人员状态</th> -->
					<th data-options="field:'tripConfim',width:70,align:'center'"   formatter="exhibitor_personnelinfo_formatTripConfim">行程确认</th>
					<th data-options="field:'visaConfim',width:70,align:'center'"   formatter="exhibitor_personnelinfo_formatVisaConfim">签证确认</th>
					<th data-options="field:'airTicketConfim',width:70,align:'center'" formatter="exhibitor_personnelinfo_formatAirTicketConfim">机票确认</th>
					<th data-options="field:'hotelConfim',width:70,align:'center'" formatter="exhibitor_personnelinfo_formatHotelConfim">酒店确认</th>
					<th data-options="field:'insuranceConfim',width:70,align:'center'" formatter="exhibitor_insur_window">保险</th>
				</tr>
			</thead>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true" style="width:572px;">
	<div id="exhibitor-personnelinfo-panel-select-company" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-personnelinfo-datagrid-select-company" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'clientId',width:20">公司编号</th>
							<th data-options="field:'companyName',width:40">公司名称</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-personnelinfo-keyword-select-company" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_personnelinfo_search_select_company()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_personnelinfo_ok_select_company()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_personnelinfo_return_select_company()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window" class="easyui-window" toolbar="#exhibitor-personnelinfo-window-toolbar" data-options="closed:true,title:'修改',modal:true" style="width:292px;">
	<!-- begin of toolbar -->
	<div id="exhibitor-personnelinfo-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" id="exhibitor-personnelinfo-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_personnelinfo_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" id="exhibitor-personnelinfo-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_personnelinfo_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-personnelinfo-panel-1" class="easyui-panel" title="基本信息" style="width:278px;height:90px;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:250px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"><label>姓名</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-personnelinfo-textbox-staffName" class="easyui-textbox" style="width:175px;height:25px"></li>
       	</ul>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-jiudian" class="easyui-window" toolbar="#exhibitor-personnelinfo-window-toolbar-jiudian" data-options="modal:true,closed:true,title:'酒店'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-personnelinfo-window-toolbar-jiudian">
	    <div class="my-toolbar-button">
	       	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_personnelinfo_jiudian_save()" plain="true">保存</a>
	       	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_personnelinfo_jiudian_cancel()" plain="true">取消</a>
	    </div>
	</div>
	<!-- end of toolbar -->
    <div id="exhibitor-personnelinfo-panel-jiudian-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	 
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        </ul>
    </div>
	<div id="exhibitor-personnelinfo-panel-jiudian-2" class="easyui-panel" title="酒店信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>酒店名称</label></div>
				<input hint="hotelName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>酒店地址</label></div>
				<input hint="hotelAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>入住时间</label></div>
				<input hint="entryTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>离开时间</label></div>
				<input hint="leaveTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>入住天数</label></div>
				<input hint="stayTime" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>房间号</label></div>
				<input hint="roomNum" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
				<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
			</li>
        </ul>
	</div>
	<div id="exhibitor-contract-panel-3" class="easyui-panel" title="酒店费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-hotal-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-renyuan" class="easyui-window" toolbar="#exhibitor-personnelinfo-window-toolbar-renyuan" data-options="modal:true,closed:true,title:'人员基本资料'" style="width:1610px;height:820px;">
	<!-- begin of toolbar -->
	<div id="exhibitor-personnelinfo-window-toolbar-renyuan">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_personnelinfo_renyuan_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_personnelinfo_renyuan_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div class="easyui-layout" data-options="fit:true">
	    <div data-options="region:'center'">
		    <div id="exhibitor-personnelinfo-panel-renyuan-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
		        <ul style="list-style:none;margin:0;padding:0;">
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
		        		<input hint="name" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>文化程度</label></div>
		        		<input hint="educationId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
		        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_education_id',method:'post'">
					</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>职位</label></div>
		        		<input hint="positionId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
		        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_position_id',method:'post'">
					</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>单位电话</label></div>
		        		<input hint="workPhone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
		        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>电子邮箱</label></div>
		        		<input hint="email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>身份证号 </label></div>
		        		<input hint="idNumber" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>身份证签发机关</label></div>
		        		<input hint="idIssue" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
			        	<div style="width:120px;height:25px;display:inline-block;"><label>家庭住址</label></div>
			        	<input hint="homeAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
			       	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>现住地址</label></div>
		        		<input hint="nowAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">	
		        		<div style="width:120px;height:25px;display:inline-block;"><label>民族</label></div>
		        		<input hint="nationId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
		        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_nation_id',method:'post'">
					</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>可将几国外语</label></div>
		        		<input hint="abilityLanguage" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label></div>
		        		<input hint="isHeel" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
		        		<input hint="memo" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        </ul>
		    </div>
			
		   	<div id="exhibitor-personnelinfo-panel-renyuan-2" class="easyui-panel" title="护照信息" style="width:100%;height:auto;padding:10px;">
		        <ul style="list-style:none;margin:0;padding:0;">
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名拼音</label></div>
		        		<input hint="pinyinName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>性别</label></div>
		        		<input hint="sexId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
		        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=Sex',method:'post'">
					</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>护照类型</label></div>
		        		<input hint="passTypeId" class="easyui-combobox" control="combobox2" tag="0" style="width:175px;height:25px" 
		        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_pass_type_id',method:'post'">
					</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>护照号码</label></div>
		        		<input hint="passNumber" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>出生日期</label></div>
		        		<input hint="bornData" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>出生地</label></div>
		        		<input hint="bornAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>签发地点 </label></div>
		        		<input hint="passAdress" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:25px;display:inline-block;"><label>签发日期</label></div>
		        		<input hint="passTime" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
			        	<div style="width:120px;height:25px;display:inline-block;"><label>有效期</label></div>
			        	<input hint="passValidity" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
			       	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:100%;display:inline-block;"><label>曾去国家/地区</label></div>
		        		<input hint="countryGone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:100%;display:inline-block;"><lable >拒签历史</lable></div>
		        		<input hint="refusHistory" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">	
		        		<div style="width:120px;height:100%;display:inline-block;"><lable >是否有旧护照</lable></div>
		        		<input hint="haveOldPass" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
		        	</li>
		        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
		        		<div style="width:120px;height:100%;display:inline-block;"><lable >旧护照信息</lable></div>
		        		<input hint="oldPass" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
		        	</li>
		        	<li style="width:640px;height:105px;float:left;margin-right:15px;margin-bottom:5px;">	
		        		<div style="width:120px;height:25px;display:inline-block;"><lable >护照照片</lable></div>
		        		<input id="exhibitor-personnelinfo-passImg" hint="passImg" class="easyui-textbox" control="textbox" tag="0" style="width:1px;height:25px;">
		        		<div style="width:320px;height:25px;display:inline-block;">
		        			<input id="exhibitor-personnelinfo-renyuan-upfile" class="easyui-filebox" name="upfile" data-options="buttonText:'选择',prompt:'选择一个图片(jpg,png格式)...'" style="width:250px;height:25px">
		        			<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_personnelinfo_renyuan_upload()" style="width:60px;height:25px">上传</a>
		        		</div>
		        		<div style="width:100px;height:105px;display:none;">
		        			<image id="exhibitor-personnelinfo-renyuan-image" scr="" onclick="exhibitor_personnelinfo_renyuan_imagebig()" style="width:80px;height:100px" />
		        		</div>
		        	</li>
		        </ul>
			</div>
			<div id="exhibitor-contract-panel-3" class="easyui-panel" title="人员费用管理" style="width:100%;height:200px;padding:2px;">
				<div class="easyui-layout" data-options="fit:true">
					<div data-options="region:'center'" style="height:257px;">
						<table id="exhibitor-person-datagrid-cost" class="easyui-datagrid"
				        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
				        		onClickRow: function (rowIndex, rowData) {
				        			var l = $(this).datagrid('getRows').length;
				        			for(var i = 0; i < l; i++){
				               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
				               		}
				 				},">
				        	<thead>
								<tr>
									<th data-options="field:'ck',checkbox:true"></th>
									<th data-options="field:'prodName',width:40">产品名称</th>
									<th data-options="field:'prodPrice',width:30">标准报价</th>
									<th data-options="field:'prodSpec',width:30">产品规格</th>
									<th data-options="field:'discount',width:30">折扣%</th>
									<th data-options="field:'price',width:30">单价</th>
									<th data-options="field:'curencyCode',width:30">币种</th>
									<th data-options="field:'qty',width:30">数量</th>
									<th data-options="field:'amount',width:30">金额</th>
								</tr>
							</thead>
				        </table>
					</div>
					<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
						<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
					</div> -->
				</div>
			</div>
		
		</div>
		<div data-options="region:'east'" title="护照照片" style="width:600px;height:100%">
			<image id="exhibitor-personnelinfo-renyuan-image-big" scr="" style="width:98%;height:98%" />
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-renyuan-image" class="easyui-window" data-options="modal:false,closed:true,title:'护照照片'" style="width:700px;height:800px;">
	<!-- <image id="exhibitor-personnelinfo-renyuan-image-big" scr="" style="width:100%;height:100%" /> -->
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-xingcheng" class="easyui-window" toolbar="#exhibitor-personnelinfo-window-toolbar-xingcheng" data-options="modal:true,closed:true,title:'行程'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-personnelinfo-window-toolbar-xingcheng">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_personnelinfo_xingcheng_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_personnelinfo_xingcheng_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-personnelinfo-panel-xingcheng-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label></div>
        		<input hint="isHeel" type="checkbox" class="my-checkbox" control="checkbox" tag="1" onclick="return false">
        	</li>
        </ul>
    </div>	
	<div id="exhibitor-personnelinfo-panel-xingcheng-2" class="easyui-panel" title="行程信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>行程</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="tripDtl" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[
        			{id:'1',text:'全程'},{id:'2',text:'半程'},{id:'3',text:'自定义'}]">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>起始地</label></div>
        		<input hint="beginPlace" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>目的地</label></div>
				<input hint="endPlace" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		    	<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
		        <input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
	</div>	
	<div id="exhibitor-personnelinfo-panel-xingcheng-3" class="easyui-panel" title="行程费用管理" style="width:100%;height:300px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'north'" style="height:52px;padding:12px;">
				<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_personnelinfo_trip_add2()" plain="false" style="width:60px;height:25px">添加</a>
		        <a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_personnelinfo_trip_del2()" plain="false" style="width:60px;height:25px">删除</a>
			</div>
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-trip-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex){
		               				$(this).datagrid('unselectRow', i).datagrid('endEdit', i);
		               			}
		               		}
		               		$(this).datagrid('beginEdit', rowIndex);
		               		exhibitor_trip_datagrid_setEditing(rowIndex);
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30,editor:{type:'numberbox',options:{precision:2}}">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30,editor:'numberbox'">数量</th>
							<th data-options="field:'amount',width:30,editor:{type:'numberbox',options:{precision:2}}">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-personnelinfo-window-jipiao" class="easyui-window" toolbar="#exhibitor-personnelinfo-window-toolbar-jipiao" data-options="modal:true,closed:true,title:'机票'" style="width:700px;height:auto;">
	<!-- begin of toolbar -->
	<div id="exhibitor-personnelinfo-window-toolbar-jipiao">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_personnelinfo_jipiao_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_personnelinfo_jipiao_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <!-- end of toolbar -->
    <div id="exhibitor-personnelinfo-panel-jipiao-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>姓名拼音</label></div>
        		<input hint="pinyinName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
       		</li>
       	 	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>性别</label></div>
        		<input hint="sexId" class="easyui-combobox" control="combobox2" tag="1" readonly style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=Sex',method:'post'">
       		</li>
       		<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	       		<div style="width:120px;height:25px;display:inline-block;"><label>护照类型</label></div>
        		<input hint="passTypeId" class="easyui-combobox" control="combobox2" tag="1" readonly style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',url:'${eippath}/itemCode/selectByItemKindCode?itemKindCode=f_pass_type_id',method:'post'">
       		</li>
	       	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>护照号码</label></div>
        		<input hint="passNumber" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出生日期</label></div>
        		<input hint="bornData" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
        	</li>
	       	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签发日期</label></div>
        		<input hint="passTime" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	        	<div style="width:120px;height:25px;display:inline-block;"><label>有效期</label></div>
	        	<input hint="passValidity" class="easyui-datebox" control="datebox" tag="1" readonly style="width:175px;height:25px">
	       	</li>
        </ul>
    </div>
	<div id="exhibitor-personnelinfo-panel-jipiao-2" class="easyui-panel" title="航班信息" style="width:100%;height:auto;padding:10px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>航班信息</label></div>
        		<input hint="airInfo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li> 
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label></div>
        		<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>出票状态</label></div>
            	<input hint="buyState" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'0',text:'未出'},{id:'1',text:'已出'}]">
        	</li>
        </ul>
	</div>
	<div id="exhibitor-contract-panel-3" class="easyui-panel" title="机票费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-jipiao-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
	
</div>
<!-- end of easyui-window -->
<!-- 选择费用以及产品 -->
<!-- begin of easyui-window -->
<div id="exhibitor-person-window-select-prod" class="easyui-window" data-options="closed:true,title:'选取产品',modal:true" style="width:872px;">
	<div id="exhibitor-person-panel-select-prod" class="easyui-panel" style="width:858px;height:584px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-person-datagrid-select-prod" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodCode',width:20">产品代码</th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">项目产品单价</th>
							<th data-options="field:'lowDiscount',width:30">最低折扣%</th>
							<th data-options="field:'lowPrice',width:30">最低价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<!-- <label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-contract-keyword-select-prod" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search_select_prod()">查询</a>
	                 --><div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_person_ok_select_prod()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_person_return_select_prod()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-baoxian" class="easyui-window" title="保险确认" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:700px;height:auto;padding:1px;">
  	<div id="exhibitor-dajian-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_baoxian_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_baoxian_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-baoxian-panel-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label></div>
        		<input hint="isHeel" type="checkbox" class="my-checkbox" control="checkbox" tag="1" onclick="return false">
        	</li>
        </ul>
    </div>	
	<div id="exhibitor-baoxian-panel-2" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>投保人类型</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'0',text:'参展商'},
        			{id:'1',text:'搭建商'},{id:'2',text:'主办方'}]">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="companyName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	 <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展位号</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="bootnCode" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>搭建商名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="contract" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司地址</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>电子邮箱</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>确认电子邮箱</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>固定电话</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="phone" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>手机</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="mobile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>投保金额</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="insMoney" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>赔付金额</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="insPay" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	 <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li>
        </ul>
    </div>
    <div id="exhibitor-baoxian-panel-3" class="easyui-panel" title="保险费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-baoxian-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-qianzheng" class="easyui-window" title="签证" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:700px;height:auto;padding:1px;">
	<div id="exhibitor-qianzheng-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_qianzheng_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_qianzheng_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-qianzheng-panel-1" class="easyui-panel" title="人员信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>姓名</label></div>
        		<input hint="name" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
        		<input hint="mobile" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>办理方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[{id:'0',text:'代办'},
        			{id:'1',text:'自办'},{id:'2',text:'公司办'}]">
        	</li>
        </ul>
    </div>
	<div id="exhibitor-qianzheng-panel-2" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	 <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签证类型</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaType" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[
        			{id:'1',text:'入境签证'},{id:'2',text:'出入境签证'}]">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>预定签证日期</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaOrder" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>押金</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaDeposit" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签证国家/地区</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaCountry" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>签证进度</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaStage" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px" 
        			data-options="valueField:'id',textField:'text',panelHeight:'auto',data:[
        			{id:'1',text:'待办'},{id:'2',text:'材料不齐'},{id:'3',text:'送签'},{id:'4',text:'出签'}
        			,{id:'5',text:'拒签'}]">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>拒签原因</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaReason" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	 <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="visaMemo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
        	</li>
        </ul>
    </div>	
    <div id="exhibitor-qianzheng-panel-3" class="easyui-panel" title="签证材料" style="width:100%;height:300px;padding:1px;">
    	<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'north'" style="height:52px;padding:12px;">
				<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_qianzheng_init()" plain="false" style="width:60px;height:25px">初始化</a>
			</div>
			<div data-options="region:'center'" style="height:257px;">
		    	<table id="exhibitor-qianzheng-datagrid-cost" class="easyui-datagrid"  
					data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex){
		               				$(this).datagrid('unselectRow', i).datagrid('endEdit', i);
		               			}
		               		}
		               		$(this).datagrid('beginEdit', rowIndex);
		 				},">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'dataNumber',width:30">材料编号</th>
							<th data-options="field:'dataName',width:40">材料名称</th>
							<th data-options="field:'dataAmount',width:30,editor:'numberbox'">材料数量</th>
							<th data-options="field:'dataState',width:30,
								formatter:function(value, row){
									if(value == 1)return '未到';
									else if(value == 2)return '已到';
								},
								editor:{
									type:'combobox',
									options:{
										valueField:'id',
										textField:'text',
										panelHeight:'auto',data:[
        								{id:'1',text:'未到'},{id:'2',text:'已到'}]
        							}
								}">材料状态</th>
							<th data-options="field:'dataMemo',width:50,editor:'textbox'">材料备注</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	var exhibitor_personnelinfo_alocate_id = -1;
	var exhibitor_personnelinfo_save_id;
	var exhibitor_personnelinfo_clientId;
	var exhibitor_personnelinfo_staffId;
	var exhibitor_personnelinfo_edit_mode_jiudian;
	var exhibitor_personnelinfo_jiudian_taskId;
	var exhibitor_personnelinfo_save_serveHotelId;
	var exhibitor_personnelinfo_edit_mode_renyuan;
	var exhibitor_personnelinfo_renyuan_taskId;
	var exhibitor_personnelinfo_save_servePersonId;
	var exhibitor_personnelinfo_edit_mode_xingcheng;
	var exhibitor_personnelinfo_jxingcheng_taskId;
	var exhibitor_personnelinfo_save_tripId;
	var exhibitor_personnelinfo_edit_mode_jipiao;
	var exhibitor_personnelinfo_jipiao_taskId;
	var exhibitor_personnelinfo_save_serveAirTicketId;
	$(function(){
		var cnt = 0;
		$('#exhibitor-personnelinfo-combobox-keyword-project').combobox({
			valueField: 'id',
			textField: 'text',
			panelHeight: '200px',
			url: eippath + '/project/getAllSelectOne',
			onLoadSuccess: function(data){
				if(cnt == 0){
					exhibitor_personnelinfo_fresh();
					cnt++;
				}
			}
		});
	});
	function exhibitor_personnelinfo_fresh(){
		$('#exhibitor-personnelinfo-datagrid').datagrid({
			url: eippath + '/staffInfo/getList?key=0',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue')
            },
			onLoadSuccess: function(data){
				if(exhibitor_personnelinfo_alocate_id == -1)$(this).datagrid('selectRow', -1);
				else{
					var rows = $(this).datagrid("getRows");
					for(var i = 0; i < rows.length; i++){
						if(rows[i].staffId == exhibitor_personnelinfo_alocate_id){
							$(this).datagrid('selectRow', i);
							exhibitor_personnelinfo_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function exhibitor_personnelinfo_search(){
		if($("#exhibitor-personnelinfo-keyword-companyName").val() == '')exhibitor_personnelinfo_clientId = null;
		$('#exhibitor-personnelinfo-datagrid').datagrid({
			url: eippath + '/staffInfo/getList?key=1',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				clientId: exhibitor_personnelinfo_clientId,
				staffName: $('#exhibitor-personnelinfo-keyword-staffName').val()
            }
		});
	}
	function exhibitor_personnelinfo_formatPersonConfim(val, row){
		if(val == 0 || val == null){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_person(' + row.staffId + ',' + row.servePersonId + ')">编辑</a>';
		}else if(val == 1){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_person(' + row.staffId + ',' + row.servePersonId + ')">编辑</a>';
		}else if(val == 2){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_person(' + row.staffId + ',' + row.servePersonId + ')">编辑</a>';
		}else if(val == 3){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_person(' + row.staffId + ',' + row.servePersonId + ')">编辑</a>';
		}
		
	}
	function exhibitor_insur_window(val, row){
		
		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinefo_issure(' + row.staffId + ',' + row.servePersonId + ',' + row.serveInsuranceId + ')">编辑</a>';
		
	}
	/* function exhibitor_personnelinfo_state(val, row){
		var state = row.personConfim;
		if(state == 0 || state == null){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" >未上传</a>';
		}else if(state == 1){
			return '<a href="javascript:void(0);" style="color:red;font-weight:bold;" >未审核</a>';
		}else if(state == 2){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" >已审核</a>';
		}else if(state == 3){
			return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" >已驳回</a>';
		}
		
	} */
	function exhibitor_personnelinfo_formatTripConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_trip(' + row.staffId + ',' + row.servePersonId + ',' + row.tripId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_personnelinfo_formatHotelConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_hotel(' + row.staffId + ',' + row.servePersonId + ',' + row.serveHotelId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_personnelinfo_formatAirTicketConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_airTicket(' + row.staffId + ',' + row.servePersonId + ',' + row.serveAirTicketId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_personnelinfo_formatVisaConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_personnelinfo_visa(' + row.staffId + ',' + row.servePersonId + ',' + row.serveVisaId + ')">编辑</a>';
		else return '已确认';
	}
	var exhibitor_personnelinfo_save_serveVisaId;
	var exhibitor_personnelinfo_edit_mode_qianzheng;
	function exhibitor_personnelinfo_visa(staffId, servePersonId, serveVisaId){
		exhibitor_personnelinfo_staffId = staffId;
		$("#exhibitor-qianzheng").window("open");
		if(servePersonId == null){
			exhibitor_personnelinfo_setEditInfoClear("#exhibitor-qianzheng-panel-1 ul");
		}else{
			var vServePersonId = servePersonId;
			$.ajax({
				url: eippath + '/servePerson/selectByServePersonId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {servePersonId: vServePersonId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_personnelinfo_setEditInfoInit("#exhibitor-qianzheng-panel-1 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}
		exhibitor_personnelinfo_save_serveVisaId = serveVisaId;
		if(serveVisaId == null){
			exhibitor_personnelinfo_setEditInfoClear("#exhibitor-qianzheng-panel-2 ul");
			exhibitor_personnelinfo_edit_mode_qianzheng = "Add";
			$('#exhibitor-qianzheng-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_personnelinfo_edit_mode_qianzheng = "Mod";
			var vServeVisaId = serveVisaId;
			$.ajax({
				url: eippath + '/serveVisa/selectByServeVisaId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveVisaId: vServeVisaId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_personnelinfo_setEditInfoInit("#exhibitor-qianzheng-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			$('#exhibitor-qianzheng-datagrid-cost').datagrid({
				url: eippath + '/visaDtl/selectByServeVisaId',
				queryParams: {
					serveVisaId: vServeVisaId
	            }
			});
		}
	}
	function exhibitor_qianzheng_init(){
		$('#exhibitor-qianzheng-datagrid-cost').datagrid({url: eippath + '/visaData/init'});
	}
	function exhibitor_qianzheng_save(){
		var l = $('#exhibitor-qianzheng-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-qianzheng-datagrid-cost').datagrid('endEdit', i);
   		}
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_qianzheng;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['serveVisaId'] = exhibitor_personnelinfo_save_serveVisaId;
		postData['projectId'] = $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue');
		exhibitor_personnelinfo_postData(postData, "#exhibitor-qianzheng-panel-2 ul");
		
		var rows = $('#exhibitor-qianzheng-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['visaDtls[' + i + '].dataNumber'] = rows[i].dataNumber;
				postData['visaDtls[' + i + '].dataName'] = rows[i].dataName;
				postData['visaDtls[' + i + '].dataAmount'] = rows[i].dataAmount;
				postData['visaDtls[' + i + '].dataState'] = rows[i].dataState;
				postData['visaDtls[' + i + '].dataMemo'] = rows[i].dataMemo;
			}
		}
		$.ajax({
			url: eippath + '/serveVisa/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-qianzheng').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_qianzheng_cancel(){
		$('#exhibitor-qianzheng').window('close');
	}
	
	var exhibitor_personnelinfo_save_serveInsuranceId;
	var exhibitor_personnelinfo_edit_mode_baoxian;
	function exhibitor_personnelinefo_issure(staffId, servePersonId, serveInsuranceId){
		exhibitor_personnelinfo_staffId = staffId;
		$("#exhibitor-baoxian").window('open');
		if(servePersonId == null){
			exhibitor_personnelinfo_setEditInfoClear("#exhibitor-baoxian-panel-1 ul");
		}else{
			var vServePersonId = servePersonId;
			$.ajax({
				url: eippath + '/servePerson/selectByServePersonId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {servePersonId: vServePersonId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_personnelinfo_setEditInfoInit("#exhibitor-baoxian-panel-1 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}
		exhibitor_personnelinfo_save_serveInsuranceId = serveInsuranceId;
		if(serveInsuranceId == null){
			exhibitor_personnelinfo_setEditInfoClear("#exhibitor-baoxian-panel-2 ul");
			exhibitor_personnelinfo_edit_mode_baoxian = "Add";
		}else{
			exhibitor_personnelinfo_edit_mode_baoxian = "Mod";
			var vServeInsuranceId = serveInsuranceId;
			$.ajax({
				url: eippath + '/serveInsurance/selectByServeInsuranceId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveInsuranceId: vServeInsuranceId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_personnelinfo_setEditInfoInit("#exhibitor-baoxian-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
		}
	}
	function exhibitor_baoxian_save(){
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_baoxian;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['serveInsuranceId'] = exhibitor_personnelinfo_save_serveInsuranceId;
		postData['projectId'] = $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue');
		exhibitor_personnelinfo_postData(postData, "#exhibitor-baoxian-panel-2 ul");
		$.ajax({
			url: eippath + '/serveInsurance/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-baoxian').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_baoxian_cancel(){
		$('#exhibitor-baoxian').window('close');
	}
	function exhibitor_personnelinfo_airTicket(staffId, servePersonId, serveAirTicketId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "10"
			},	//发送数据
			async: true,
			success: function(data){
				//data.id == 0
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_personnelinfo_staffId = staffId;
					exhibitor_personnelinfo_jipiao_taskId = data.id;
					$("#exhibitor-personnelinfo-window-jipiao").window('open');
					if(servePersonId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-jipiao-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-jipiao-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_personnelinfo_save_serveAirTicketId = serveAirTicketId;
					if(serveAirTicketId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-jipiao-2 ul");
						exhibitor_personnelinfo_edit_mode_jipiao = "Add";
					}else{
						exhibitor_personnelinfo_edit_mode_jipiao = "Mod";
						var vServeAirTicketId = serveAirTicketId;
						$.ajax({
							url: eippath + '/serveAirTicket/selectByServeAirTicketId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveAirTicketId: vServeAirTicketId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-jipiao-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
		load_bs_productByType('机票','exhibitor-jipiao-datagrid-cost');
		
	}
	function exhibitor_personnelinfo_jipiao_save(){
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_jipiao;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['serveAirTicketId'] = exhibitor_personnelinfo_save_serveAirTicketId;
		postData['taskId'] = exhibitor_personnelinfo_jipiao_taskId;
		postData['projectId'] = $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue');
		exhibitor_personnelinfo_postData(postData, "#exhibitor-personnelinfo-panel-jipiao-2 ul");
		$.ajax({
			url: eippath + '/serveAirTicket/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-personnelinfo-window-jipiao').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_jipiao_cancel(){
		$('#exhibitor-personnelinfo-window-jipiao').window('close');
	}
	function exhibitor_personnelinfo_person(staffId, servePersonId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "14"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_personnelinfo_staffId = staffId;
					exhibitor_personnelinfo_renyuan_taskId = data.id;
					$("#exhibitor-personnelinfo-window-renyuan").window('open');
					$("#exhibitor-personnelinfo-passImg").next().hide();
					exhibitor_personnelinfo_save_servePersonId = servePersonId;
					if(servePersonId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-renyuan-1 ul");
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-renyuan-2 ul");
						$("#exhibitor-personnelinfo-renyuan-image").attr("src", ' ');
						$("#exhibitor-personnelinfo-renyuan-image-big").attr("src", ' ');
						exhibitor_personnelinfo_edit_mode_renyuan = "Add";
					}else{
						exhibitor_personnelinfo_edit_mode_renyuan = "Mod";
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-renyuan-1 ul", data);
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-renyuan-2 ul", data);
								$("#exhibitor-personnelinfo-renyuan-image").attr("src", eippath + '/imgfile/' + data.passImg);
								$("#exhibitor-personnelinfo-renyuan-image-big").attr("src", eippath + '/imgfile/' + data.passImg);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
		load_bs_productByType('人员','exhibitor-person-datagrid-cost');
	}
	function exhibitor_personnelinfo_renyuan_upload(){
		var file = $("#exhibitor-personnelinfo-renyuan-upfile").filebox("files")[0];
		if(file == null){
			$.messager.alert('提示','请选择图片！','warning');
			return;
		}
		var formData = new FormData();
		formData.append("upfile", file);
		$.ajax({
			url: "${eippath}/servePerson/upload",
			dataType: "json",	//返回数据类型
			type: "post",
			data: formData,	//发送数据
			//告诉jQuery不要去处理发送的数据
        	processData: false,
        	//告诉jQuery不要去设置Content-Type请求头
        	contentType: false,
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','上传成功！','info');
					$("#exhibitor-personnelinfo-renyuan-image").attr("src", eippath + '/imgfile/' + data.file);
					$("#exhibitor-personnelinfo-renyuan-image-big").attr("src", eippath + '/imgfile/' + data.file);
					$("#exhibitor-personnelinfo-passImg").textbox("setValue", data.file);
				}
				else $.messager.alert('提示','上传失败！','error');
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_renyuan_imagebig(){
		$('#exhibitor-personnelinfo-window-renyuan-image').window('open');
	}
	function exhibitor_personnelinfo_renyuan_save(){
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_renyuan;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['servePersonId'] = exhibitor_personnelinfo_save_servePersonId;
		postData['taskId'] = exhibitor_personnelinfo_renyuan_taskId;
		exhibitor_personnelinfo_postData(postData, "#exhibitor-personnelinfo-panel-renyuan-1 ul");
		exhibitor_personnelinfo_postData(postData, "#exhibitor-personnelinfo-panel-renyuan-2 ul");
		$.ajax({
			url: eippath + '/servePerson/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-personnelinfo-window-renyuan').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_renyuan_cancel(){
		$('#exhibitor-personnelinfo-window-renyuan').window('close');
	}
	function exhibitor_personnelinfo_trip(staffId, servePersonId, tripId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "15"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_personnelinfo_staffId = staffId;
					exhibitor_personnelinfo_xingcheng_taskId = data.id;
					$("#exhibitor-personnelinfo-window-xingcheng").window('open');
					if(servePersonId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-xingcheng-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-xingcheng-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_personnelinfo_save_tripId = tripId;
					if(tripId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-xingcheng-2 ul");
						exhibitor_personnelinfo_edit_mode_xingcheng = "Add";
						$('#exhibitor-trip-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
					}else{
						exhibitor_personnelinfo_edit_mode_xingcheng = "Mod";
						var vServeTripId = tripId;
						$.ajax({
							url: eippath + '/serveTrip/selectByServeTripId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveTripId: vServeTripId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-xingcheng-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
						exhibitor_personnelinfo_prodcost(vServeTripId, '行程');
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_prodcost(serviceId, serviceType){
		$('#exhibitor-trip-datagrid-cost').datagrid({
			url: eippath + '/orderDtlBS/selectByServiceIdAndServiceType',
			queryParams: {
				serviceId: serviceId,
				serviceType: serviceType
            }
		}); 
	}
	var exhibitor_personnelinfo_datagrid_dist_id;
	function exhibitor_personnelinfo_trip_add2(){
		exhibitor_personnelinfo_open_select_prod('行程');
		exhibitor_personnelinfo_datagrid_dist_id = 'exhibitor-trip-datagrid-cost';
	}
	function exhibitor_personnelinfo_open_select_prod(vType){
		$('#exhibitor-person-window-select-prod').window('open');
		$('#exhibitor-person-datagrid-select-prod').datagrid({
			url: eippath + '/projProduct/selectByProjectIdByType',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				type: vType
            }
		});
	}
	function exhibitor_person_ok_select_prod(){
		var rows = $('#exhibitor-person-datagrid-select-prod').datagrid('getSelections');
		var rows2 = $("#" + exhibitor_personnelinfo_datagrid_dist_id).datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				flag = 0;
				for(var j = 0; j < rows2.length; j++){
					if(rows2[j].prodCode == rows[i].prodCode){
						flag = 1;
						break;
					}
				}
				if(flag == 0){
					$("#" + exhibitor_personnelinfo_datagrid_dist_id).datagrid('appendRow', {
						prodCode: rows[i].prodCode,
						prodName: rows[i].prodName,
						prodPrice: rows[i].prodPrice,
						prodSpec: '',
						discount: 100,
						price: rows[i].prodPrice,
						curencyCode: rows[i].curencyCode,
						qty: 1,
						amount: rows[i].prodPrice,
						serviceType: rows[i].type
					});
				}
			}
			$('#exhibitor-person-window-select-prod').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_person_return_select_prod(){
		$('#exhibitor-person-window-select-prod').window('close');
	}
	function exhibitor_personnelinfo_trip_del2(){
		exhibitor_personnelinfo_datagrid_dist_id = 'exhibitor-trip-datagrid-cost';
		exhibitor_personnelinfo_select_prod_del();
	}
	function exhibitor_personnelinfo_select_prod_del(){
		var rows = $('#' + exhibitor_personnelinfo_datagrid_dist_id).datagrid('getSelections');
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				var rowIndex = $('#' + exhibitor_personnelinfo_datagrid_dist_id).datagrid('getRowIndex', rows[i]);
				$('#' + exhibitor_personnelinfo_datagrid_dist_id).datagrid('deleteRow', rowIndex);
			}
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_trip_datagrid_setEditing(rowIndex){
		var editors = $('#exhibitor-trip-datagrid-cost').datagrid('getEditors', rowIndex);
		var n1 = $(editors[0].target);
        var n2 = $(editors[1].target);
        var n3 = $(editors[2].target);
        n1.add(n2).numberbox({
            onChange: function(){
                var cost = n1.numberbox('getValue') * n2.numberbox('getValue');
                n3.numberbox('setValue', cost);
            }
        });
		
		/*var priceEditor = editors[0];
		var qtyEditor = editors[1];
		var amountEditor = editors[2];
		priceEditor.target.bind('change', function(){
			calculate();
		});
		qtyEditor.target.bind('change', function(){
			calculate();
		});
  				
   		function calculate(){
   			var cost = (priceEditor.target.val())*(qtyEditor.target.val());
   			$(amountEditor.target).numberbox('setValue', cost);
   		}*/
   	}
	function exhibitor_personnelinfo_xingcheng_save(){
		var l = $('#exhibitor-trip-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-trip-datagrid-cost').datagrid('endEdit', i);
   		}
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_xingcheng;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['serveTripId'] = exhibitor_personnelinfo_save_tripId;
		postData['taskId'] = exhibitor_personnelinfo_xingcheng_taskId;
		postData['projectId'] = $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue');
		exhibitor_personnelinfo_postData(postData, "#exhibitor-personnelinfo-panel-xingcheng-2 ul");
		
		var rows = $('#exhibitor-trip-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].serviceType'] = rows[i].serviceType;
			}
		}
		$.ajax({
			url: eippath + '/serveTrip/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-personnelinfo-window-xingcheng').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_xingcheng_cancel(){
		$('#exhibitor-personnelinfo-window-xingcheng').window('close');
	}
	function exhibitor_personnelinfo_hotel(staffId, servePersonId, serveHotelId){
		//先判断任务是否创建
		$.ajax({
			url: eippath + '/task/selectTaskIdByProjectIdAndTaskKindCode',
			dataType: "json",	//返回数据类型
			type: "post",
			data: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				taskKindCode: "2"
			},	//发送数据
			async: true,
			success: function(data){
				if(data.id == -1)$.messager.alert('提示','还未创建该任务！','warning');
				else{
					exhibitor_personnelinfo_staffId = staffId;
					exhibitor_personnelinfo_jiudian_taskId = data.id;
					$("#exhibitor-personnelinfo-window-jiudian").window('open');
					if(servePersonId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-jiudian-1 ul");
					}else{
						var vServePersonId = servePersonId;
						$.ajax({
							url: eippath + '/servePerson/selectByServePersonId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {servePersonId: vServePersonId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-jiudian-1 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
					exhibitor_personnelinfo_save_serveHotelId = serveHotelId;
					if(serveHotelId == null){
						exhibitor_personnelinfo_setEditInfoClear("#exhibitor-personnelinfo-panel-jiudian-2 ul");
						exhibitor_personnelinfo_edit_mode_jiudian = "Add";
					}else{
						exhibitor_personnelinfo_edit_mode_jiudian = "Mod";
						var vServeHotelId = serveHotelId;
						$.ajax({
							url: eippath + '/serveHotel/selectByServeHotelId',
							dataType: "json",	//返回数据类型
							type: "post",
							data: {serveHotelId: vServeHotelId},	//发送数据
							async: true,
							success: function(data){
								exhibitor_personnelinfo_setEditInfoInit("#exhibitor-personnelinfo-panel-jiudian-2 ul", data);
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
		load_bs_productByType('酒店','exhibitor-hotal-datagrid-cost');
	}
	function exhibitor_personnelinfo_jiudian_save(){
		var postData = {};
		postData['editMode'] = exhibitor_personnelinfo_edit_mode_jiudian;
		postData['staffId'] = exhibitor_personnelinfo_staffId;
		postData['serveHotelId'] = exhibitor_personnelinfo_save_serveHotelId;
		postData['projectId'] = $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue');
		postData['taskId'] = exhibitor_personnelinfo_jiudian_taskId;
		exhibitor_personnelinfo_postData(postData, "#exhibitor-personnelinfo-panel-jiudian-2 ul");
		$.ajax({
			url: eippath + '/serveHotel/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-personnelinfo-window-jiudian').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_postData(postData, ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox2"){
				var v = $($(this).find("input")[0]).combobox("getText")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});
	}
	function exhibitor_personnelinfo_jiudian_cancel(){
		$('#exhibitor-personnelinfo-window-jiudian').window('close');
	}
	
	function exhibitor_personnelinfo_select_company(){
		$('#exhibitor-personnelinfo-window-select-company').window('open');
		$('#exhibitor-personnelinfo-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=0',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue')
            }
		});
	}
	function exhibitor_personnelinfo_search_select_company(){
		$('#exhibitor-personnelinfo-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=1',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				companyName: $('#exhibitor-personnelinfo-keyword-select-company').val()
            }
		});
	}
	function exhibitor_personnelinfo_ok_select_company(){
		var row = $('#exhibitor-personnelinfo-datagrid-select-company').datagrid('getSelected');
		if (row){
			exhibitor_personnelinfo_clientId = row.clientId;
			$("#exhibitor-personnelinfo-keyword-companyName").val(row.companyName);
			$('#exhibitor-personnelinfo-window-select-company').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_personnelinfo_return_select_company(){
		$('#exhibitor-personnelinfo-window-select-company').window('close');
	}
	function exhibitor_personnelinfo_mod(){
		var row = $('#exhibitor-personnelinfo-datagrid').datagrid('getSelected');
		if (row){
			exhibitor_personnelinfo_alocate_id = row.staffId;
			$('#exhibitor-personnelinfo-window').window('open');
			exhibitor_personnelinfo_save_id = row.staffId;
			$("#exhibitor-personnelinfo-textbox-staffName").textbox("setValue", row['staffName']);
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_personnelinfo_save(){
		var postData = {};
		postData['staffId'] = exhibitor_personnelinfo_save_id;
		postData['staffName'] = $("#exhibitor-personnelinfo-textbox-staffName").val();
		$.ajax({
			url: eippath + '/staffInfo/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_contract_alocate_id = postData['staffId'];
					$('#exhibitor-personnelinfo-window').window('close', true);
					exhibitor_personnelinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_personnelinfo_cancel(){
		$('#exhibitor-personnelinfo-window').window('close', true);
	}
	//设置编辑页面内容清空
	function exhibitor_personnelinfo_setEditInfoClear(ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
	}
	//设置编辑页面内容初始化
	function exhibitor_personnelinfo_setEditInfoInit(ul, row){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "combobox2")$($(this).find("input")[0]).combobox("setText", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
	}
	
	function exhibitor_contract_add2(){
		$('#exhibitor-trip-datagrid-cost').datagrid({
			url: eippath + '/projProduct/selectByProjectIdByType',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				type:'行程'
            }
		}); 
	}
	function load_bs_productByType(type,datagrid){
		$('#'+datagrid).datagrid({
			url: eippath + '/projProduct/selectByProjectIdByType',
			queryParams: {
				projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
				type:type
            }
		}); 
	}
	function exhibitor_contract_ok_select_prod(){
		var rows = $('#exhibitor-person-datagrid-select-prod').datagrid('getSelections');
		var rows2 = $("#exhibitor-person-datagrid-prod").datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				flag = 0;
				for(var j = 0; j < rows2.length; j++){
					if(rows2[j].prodCode == rows[i].prodCode){
						flag = 1;
						break;
					}
				}
				if(flag == 0){
					$('#exhibitor-person-datagrid-prod').datagrid('appendRow', {
						prodCode: rows[i].prodCode,
						prodName: rows[i].prodName,
						prodPrice: rows[i].prodPrice,
						prodSpec: '',
						discount: 100,
						price: rows[i].prodPrice,
						curencyCode: rows[i].curencyCode,
						qty: 1,
						amount: rows[i].prodPrice
					});
				}
			}
			$('#exhibitor-person-window-select-prod').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
</script>




<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
		
	</style>
	
<script>
	
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
		});
		function reflash_re(){
			$('.easyui-window').panel('destroy');
			reflush();
		}
	</script>
	
	 	 