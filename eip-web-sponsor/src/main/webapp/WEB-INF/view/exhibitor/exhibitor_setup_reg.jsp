<!--展商登记设置 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<c:set var="exhibitionApplicationIsBrand" value="${'BRAND'.equals(project.projKindCode)}"/>
<script src="${eippath}/js/jquery.qrcode.logo.ex.js" type="text/javascript" charset="utf-8"></script>
<%@ include file="../common/reg_url.jsp" %>
<style type="text/css">
  .all_address {
    box-sizing: border-box;
    padding-bottom: 10px;
    margin: 8px 30px 0 30px;
    border-bottom: 1px solid #ccc;
  }

  .all_address h6 {
    font-size: 16px;
    font-weight: normal;
    color: #333333;
    margin-bottom: 8px;
  }

  .all_address textarea {
    width: 100%;
    border: none;
    resize: none;
    outline: none;
    font-size: 14px;
    color: #808080;
  }

  .btn-primary {
    width: 84px;
    height: 32px;
    border-radius: 6px;
    font-size: 14px;
    border: solid 1px #4c72fe;
    color: #4c72fe;
    background-color: #fff;
    outline: none;
    cursor: pointer;
  }

  .all_address > a {
    width: 84px;
    height: 30px;
    background-color: #fff;
    border-radius: 6px;
    border: solid 1px #999999;
    display: inline-block;
    text-align: center;
    line-height: 30px;
    margin-left: 20px;
    color: #666666;
  }

  .all_address > a:hover {
    color: #666666;
  }

  .address_download {
    overflow: hidden;
    margin: 12px 30px 15px;
  }

  .address_download ul li {
    float: left;
    margin-right: 20px;
  }

  .am-btn {
    font-size: 14px;
    width: 100%;
    color: #333333;
    border: none;
    background-color: #fff;
    height: 26px;
    cursor: pointer;
    outline: none;
  }

  .prompt_text {
    width: 144px;
    font-size: 16px;
    line-height: 26px;
    color: #333333;
    margin-left: 14px;
    margin-top: 29px;
  }

  .register-exhibitionReg, .setdiv, .register-releaseReg, .register-address {
    width: 288px !important;
    height: 216px !important;
    border-radius: 10px;
    border: solid 1px #4c72fe;
    transition: transform 0.6s;
  }

  .register-exhibitionReg :hover img, .setdiv :hover img, .register-releaseReg :hover img, .register-address :hover img {
    transform: scale(1.1);
  }

  .detialdiv a {
    font-size: 24px;
    color: #4c72fe;
    display: block;
    margin-top: 20px;
  }

  .detialdiv img {
    margin: 42px auto auto;
  }

  #exhibition-application-regAddress-code canvas {
    width: 140px;
  }
</style>
<script>
  var exhibitCode = exhibitCode_for_pass;//展会code
  var orgNum = org_num_for_pass
  var pagePrefix = 'exhibition-application'
  var pagePrefix_T = 'exhibitionApplication'
  window.targetType = 4 //对象类型 2 供应商 3 采购商 4 展商登记 6 商家
  var origin = !1 ? 'http://*************:8088' : location.origin
  var exhibitionApplicationProjectList = []


  //兼容之前已经上传的图片
  function queryData2image(src) {
    const inner = src => {
      if (!src) return '/RegSever/RegPage/images/top-lucency.png'
      const prefix = location.origin
      if (/(http|https)/.test(src)) return src
      else if (/(^\\+|^\/)/.test(src)) return prefix + src
      return prefix + '/image/' + src
    }
    const url = inner(src)
    if (!url) return ''
    try {
      return new URL(url).host.indexOf('aliyuncs.com') !== -1
        ? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
        : url
    } catch (e) {
      return url
    }
  }

  //基本信息设置
  // function EAOpenBasicSet(projectId, regCutoffDate, regEnglish, targetType) {
  //   __$('basic-setting').window('open').attr('data-pid', projectId)
  //   __$('regcuttof').textbox('setValue', regCutoffDate)
  //   __$('regEnglish').prop('checked', regEnglish)
  //   window.targetType = targetType
  // }
  function EASaveBasicSet() {
    const projRegSetId = __$('basic-setting').data('projRegSetId') || ''
    const projectId = __$('projectId').combobox('getValue');
    const regCutoffDate = __$('regcuttof').textbox('getValue');
    const regEnglish = __$('regEnglish').prop('checked')
    if (!projectId)
      return $.messager.alert('提示', '请选择项目', 'warning')
    if (!regCutoffDate)
      return $.messager.alert('提示', '请选择时间', 'warning')
    $.ajax({
      url: origin + '/RegSever/projRegSet/saveExhibitorRegSet',
      data: {
        projectId,
        projRegSetId,
        regCutoffDate,
        regEnglish,
        targetType,
        exhibitCode,
      },
      type: 'post',
      success(res) {
        if (res.state !== 1) {
          return $.messager.alert('提示', res.msg || '保存失败!', 'error')
        }
        $.messager.alert('提示', '保存成功!', 'info')
        __$('basic-setting').removeAttr('data-pid').window('close')
        exhibitionApplicationLoadTable()
      },
      error(err) {
        $.messager.alert('提示', '数据加载失败', 'error')
        console.warn(err)
      }
    })
  }

  //登记设置
  function EAOpenRegSet(projectId, version, targetType) {
    var regSet = variableReg +
      '/web-reg-server/pc/exhibit-apply-settings.html?EID=' + exhibitCode +
      '&orgnum=' + orgNum + '&target=' + targetType + '&pid='+ projectId+ '&version=' + version
    window.open(regSet,'_blank')
  }

  //发布
  function EAReleaseSet(projRegSetId, _releaseReg) {
    const release = releaseReg => {
      $.ajax({
        url: origin + '/RegSever/projRegSet/saveBuyerReleaseReg',
        data: {
          projRegSetId,
          releaseReg,
        },
        type: 'post',
        success(res) {
          if (res.state !== 1) {
            return $.messager.alert('提示', '发布失败' + (res.state === 2 ? '，请先在基本信息设置中设置登记截止时间！' : '！'), 'error')
          }
          $.messager.alert('提示', '发布成功!', 'info')
          __$('basic-setting').removeAttr('data-pid').window('close')
          exhibitionApplicationLoadTable()
        },
        error(err) {
          $.messager.alert('提示', '数据加载失败', 'error')
          console.warn(err)
        }
      })
    }
    if (!_releaseReg) return release(!_releaseReg)
    $.messager.confirm('提示', '确定撤销发布嘛？', r => r && release(!_releaseReg))
  }

  //登记地址
  function EARegAddress(projectId, version, targetType) {
    const search = '?EID=' + exhibitCode + '&target='+ targetType +'&orgnum=' + orgNum + '&pid='+ projectId + '&version=' + version + '&cid=&ctid=1'
    __$('regAddress-code').empty();
    const mobileAddress = variableReg + '/web-reg-server/mobile/exhibit-apply-m.html' + search
    __$('regAddress-mobile').val(mobileAddress)
    __$('regAddress-mobile-open').attr('href', mobileAddress)
    $('canvas').css("width", "170px")
    __$('regAddress-code').qrcode({
      src: queryData2image(EAGetLogoImage(version)),
      text: mobileAddress,
      correctLevel: 0,
      width: '1000', //二维码的宽度
      height: '1000', //二维码的高度
      imgWidth : 200,			 //图片宽
      imgHeight : 200,			 //图片高
    })
    const computerAddress = variableReg + '/web-reg-server/pc/exhibit-apply.html' + search
    __$('regAddress-computer').val(computerAddress)
    __$('regAddress-computer-open').attr('href', computerAddress)
    __$('regAddress-window').window({title: version === 2 ? '展商英文申请地址' : '展商中文申请地址'}).window('open').attr({version})
  }

  // DS(Data Synchronization)
  function EAOpenDataSync(projRegSetId) {
    const row = EAGetCurrentRow(projRegSetId)
    __$('data-sync-window').window('open').data({projRegSetId})
    __$('synGroup').combotree('loadData', []).combotree('clear')
    __$('synTeam').combobox({
      url: eippath + "/project/getWorkTeamByProjectId", //获取数据
      method: 'post',
      queryParams: {projectId: row.projectId},
      loadFilter(_data) {
        return [{id: '', text: ''}, ..._data]
      },
      onLoadSuccess: function (workTeamData) {
        if (workTeamData.length > 0 && row['synTeam']) {
          $(this).combobox('setValue', row['synTeam'])
          __$('synGroup').combotree('setValue', row['synGroup'] || '')
        }
      },
      onChange(workTeamId, oldValue) {
        if (!+workTeamId) return __$('synGroup').combotree('loadData', [])
        __$('synGroup').combotree({
          url: '/RegSever/teamCltGroup/getGroupByTeamId',
          method: "post",
          queryParams: {workTeamId},
          loadFilter(_data) {
            if (_data.state === 1)
              return [{id: '', text: ''}, ..._data.data]
            return []
          },
        })
      }
    })
    __$('synData')
      .change(function () {
        __$('showSync')['hide show'.split(' ')[+$(this).prop('checked')]]()
      })
      .prop('checked', row['synData'] !== 0)
      .change()
  }
  function EASaveSynData() {
    const projRegSetId = __$('data-sync-window').data('projRegSetId')
    const synData = +__$('synData').prop('checked')
    const synTeam = __$('synTeam').combobox('getValue')
    const synGroup = __$('synGroup').combotree('getValue')
    $.ajax({
      url: '/RegSever/projRegSet/saveBuyerSync',
      type: 'post',
      dataType: 'json',
      data: {projRegSetId, synData, synTeam, synGroup},
      success(data) {
        if (data.state !== 1)
          return reject('保存失败！')
        $.messager.alert('提示', '保存成功!', 'info')
        exhibitionApplicationLoadTable()
        __$('data-sync-window').removeAttr('data-pid').window('close')
      },
      error(err) {
        $.messager.alert('提示', '数据加载失败', 'error')
        console.warn(err)
      }
    })
  }
  //加载表格数据
  function exhibitionApplicationLoadTable(first) {
    const projectName = !first ? __$('search-project').textbox('getValue') : ''
    __$('datagrid').datagrid({
      url: origin + '/RegSever/projRegSet/getExhibitorList',
      queryParams: {exhibitCode, projectName},
      loadFilter(data) {
        if (data.state !== 1) {
          $.messager.alert('错误', '数据加载失败！', 'error')
          return []
        }
        return data.data
      },
      onLoadSuccess(data){
        if (Array.isArray(data.rows) && data.rows.length > 0)
          targetType = data.rows[0]['targetType'] || 4

        const total = data.total
        __$('table-layout').find('.datagrid')[total ? "show" : "hide"]()
        __$('blank')[!total ? "show" : "hide"]()
        !total && first && EAOpenSettingWindow()
      }
    })
  }

  function EACreateExhibitorRegSet(brandProjectId) {
    const getNotExhibitorRegSetProjectByBrandId = (brandProjectId, callback) => {
      $.ajax({
        url: '/RegSever/projRegSet/getNotExhibitorRegSetProjectByBrandId',
        type: 'post',
        dataType: 'json',
        data: {brandProjectId},
        success(data) {
          if (data.state !== 1)
            return reject('保存失败！')
          callback(data.data || 0)
        },
        error(err) {
          $.messager.alert('提示', '数据加载失败', 'error')
        }
      })
    }
    const createExhibitorRegSetByBrandProject = (brandProjectId) => {
      $.ajax({
        url: '/RegSever/projRegSet/createExhibitorRegSetByBrandProject',
        type: 'post',
        dataType: 'json',
        data: {brandProjectId, operEntrance: '参展申请设置'},
        success(data) {
          if (data.state !== 1)
            return reject('保存失败！')
          $.messager.alert('提示', '保存成功!', 'info')
          exhibitionApplicationLoadTable()
        },
        error(err) {
          $.messager.alert('提示', '数据加载失败', 'error')
        }
      })
    }
    getNotExhibitorRegSetProjectByBrandId(brandProjectId, count => {
      if (count <= 0 ) return $.messager.alert('提示', '没有可以创建的创建参展申请', 'warning')
      $.messager.confirm('提示', `即将为\${count}个项目创建参展申请，是否确定?`, r => r && createExhibitorRegSetByBrandProject(brandProjectId))
    })
  }

  $(function () {
    exhibitionApplicationLoadTable(!0)
    __$('projectId').combobox({
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      queryParams: {exhibitCode},
      onLoadSuccess(data) {
        exhibitionApplicationProjectList = data
      }
    })
  })

  //减小jQuery选择器
  function __$(selector, type = '#') {
    return $(type + pagePrefix + '-' + selector)
  }

  //create button
  function __CBTN(_click, text, exp = '') {
    if (!text) return
    return '<a href="javascript:void(0);" style="color:#00f;font-weight:bold;" onclick="' + _click + '">' + text + '</a>' + exp
  }

  //copy link
  function EACopyLinkAddress(target) {
    __$(target).select()
    document.execCommand('copy')
  }
  function EADownloadQRCode() {
    var type = 'png'
    var imgdata = __$('regAddress-code').find('canvas')[0].toDataURL("image/png")
    var suffixType = function (type) {
      type = type.toLocaleLowerCase().replace(/jpg/i, 'jpeg');
      var r = type.match(/png|jpeg|bmp|gif/)[0];
      return 'image/' + r;
    }
    imgdata = imgdata.replace(suffixType(type), 'image/octet-stream');
    //3.0 将图片保存到本地
    var saveFile = function (data, filename) {
      var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
      save_link.href = data;
      save_link.download = filename;
      var event = document.createEvent('MouseEvents')
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      save_link.dispatchEvent(event)
    }
    const versionText = __$('regAddress-window').attr('version') === '2' ? '英文版': '中文版'
    saveFile(imgdata, `参展申请\${versionText}二维码.\${type}`)
  }

  // 获取二维码logo
  function EAGetLogoImage(f_version){
    let logo
    $.ajax({
      url:  origin + '/RegSever/projRegTemp/queryAppointTemp',
      data: {
        f_project_id: project_for_pass,
        f_version,
        f_target: targetType,
        appointTemp: 'f_2code_img'
      },
      type: 'post',
      async: false,  //同步，防止重复提交
      xhrFields: {
        withCredentials:true
      },
      success: function({data, state}) {
        if(state !== 1)return
        logo = data['f_2code_img']
      }
    })
    return logo
  }

  function EAOpenRegChannelLink(projectId, targetType, projectName) {
    const isExist = addTab('展商邀请渠道', `/eip-web-sponsor/backstage/exhibitor/exhibitor_reg_channel?projectName=\${projectName}&targetType=\${targetType}`) === '__EXISTS__'
    if (isExist && typeof exhibitor_channel_details_setQuery === 'function') {
      exhibitor_channel_details_setQuery({targetType, projectName}, true)
    }
  }
  // 打开基本信息弹窗
  function EAOpenSettingWindow(projRegSetId) {
    const isAdd = !projRegSetId
    const $settingWindow = __$('basic-setting')
    if (isAdd) {
      const defaultVal = '${exhibition.endDate}'
      const regCutoffDate = defaultVal ? defaultVal + ' 00:00:00' : ''
      // 新增的第一个默认上主项目
      const presetsProjectId = !__$('datagrid').datagrid('getData').total ? project_for_pass : ''
      $settingWindow.window({title: '新增参展申请项目'}).window('open')
      const existsProjectList = __$('datagrid').datagrid('getRows').map(item => item.projectId)
      __$("projectId")
        .combobox('loadData', exhibitionApplicationProjectList.filter(item => !existsProjectList.includes(item.projectId)))
        .combobox('setValue', presetsProjectId)
        .combobox('enable')
      __$("regcuttof").textbox('setValue', regCutoffDate || '')
      __$("regEnglish").prop('checked', false)
      return
    }
    const row = EAGetCurrentRow(projRegSetId)
    __$("projectId")
      .combobox('loadData', exhibitionApplicationProjectList)
      .combobox('setValue', row.projectId)
      .combobox('disable')
    __$("regcuttof").textbox('setValue', row.regCutoffDate)
    __$("regEnglish").prop('checked', !!row.regEnglish)
    $settingWindow.window({title: '基本信息设置'}).window('open').data({projRegSetId}).data({cutoffDate: row.regCutoffDate})
  }
  function EAGetCurrentRow(projRegSetId) {
    const {rows} = __$('datagrid').datagrid('getData')
    const result = rows.find(item => item['projRegSetId'] === projRegSetId)
    if (!result) {
      $.messager.alert('错误', '数据异常，请刷新重试', 'error')
      throw Error(`Can not found current row`)
    }
    return result
  }
  // 删除登记设置
  function EADeleteRegSet() {
    const rows = __$('datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '未选中内容！', 'warning')
    const projRegSetIds = rows.map(item => item.projRegSetId).toString()
    const _request = () => {
      $.messager.progress()
      $.ajax({
        url: '/RegSever/projRegSet/delete',
        dataType: "json",	//返回数据类型
        type: "post",
        data: {projRegSetIds, operEntrance: "展商参展申请设置"},	//发送数据
        success({state}) {
          if (state !== 1) return $.messager.alert('提示', '删除失败！', 'error')
          $.messager.alert('提示', '删除成功！', 'info')
          exhibitionApplicationLoadTable(!0)
        },
        error() {
          $.messager.alert('提示', '数据发送失败！', 'error')
          $.messager.progress('close')
        }
      }).done(() => $.messager.progress('close'))
    }
    $.messager.confirm('提示',  '确定删除吗？', r => r && _request())
  }
</script>

<style>
  #exhibition-application-toolbar {
    height: 84px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  #exhibition-application-toolbar label {
    margin-right: 20px;
    padding-left: 15px;
  }

  #exhibition-application-toolbar a {
    width: 70px;
    height: 25px;
    margin-left: 20px;
  }

  #exhibition-application-toolbar .exhibition-application-table-toolbar {
      flex-basis: 100%;
  }
  #exhibition-application-toolbar .exhibition-application-table-toolbar a {
      margin-left: 0;
  }

  #exhibition-application-blank{
     display: flex;
     align-items: center;
     justify-content: center;
     flex-direction: column;
  }
  #exhibition-application-blank h3{
      color: #666;
      font-size: 18px;
      font-weight: normal;
  }
  #exhibition-application-blank a{
      line-height: 35px;
      color: #fff;
      display: block;
      width: fit-content;
      padding: 0 15px;
      height: 35px;
      border-radius: 4px;
      background: #1685ed;
      border: none;
  }
</style>
<div id="exhibition-application-toolbar">
  <label for="exhibition-application-search-project">项目名称</label>
  <input id="exhibition-application-search-project"
         class="easyui-textbox"
         style="width:200px;height: 25px;"
         data-options="
          inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents, {
            keypress: function(e) {
               if(e.key === 'Enter')exhibitionApplicationLoadTable()
            }
        })"/>
  <a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitionApplicationLoadTable()">查询</a>
  <div class="exhibition-application-table-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="reflush()"
         plain="true">刷新</a>
      <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="EAOpenSettingWindow()"
         plain="true">新增</a>
      <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="EADeleteRegSet()"
         plain="true">删除</a>
    </div>
  </div>
</div>
<div class="easyui-layout" data-options="fit:true">
  <div id="exhibition-application-table-layout" data-options="region:'center'" title="" style="width:100%;height:100%;">
    <table id="exhibition-application-datagrid"
           toolbar="#exhibition-application-toolbar"
           data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
            onClickRow: function (rowIndex, rowData) {
              var l = $(this).datagrid('getRows').length;
              for(var i = 0; i < l; i++){
                if(i != rowIndex)$(this).datagrid('unselectRow', i);
              }
            }">
      <thead>
      <tr>
        <c:if test="${exhibitionApplicationIsBrand}">
          <th data-options="field:'projectName',width:80,align:'center'">参展申请模板</th>
          <th data-options="field:'regCutoffDate',width:130,align:'center'"
              formatter="(v, r)=>__CBTN(`EAOpenSettingWindow('\${r.projRegSetId}')`, v ? `截止至\${v.replace(/-/g,'/')}` : '设置')">
            基本信息设置
          </th>
          <th data-options="field:'1',width:180,align:'center'"
              formatter="(_, r)=>__CBTN(`EAOpenRegSet(\${r.projectId}, 1, \${r.targetType})`, '中文版设置', r.regEnglish ? '<i style=\'margin-right:20px\'/>' : '') + (r.regEnglish ? __CBTN(`EAOpenRegSet(\${r.projectId}, 2, \${r.targetType})`, '英文版设置') : '')">
            登记设置
          </th>
          <th data-options="field:'2',width:180,align:'center'"
              formatter="(_, r)=>__CBTN(`EACreateExhibitorRegSet(\${r.projectId})`, r.projKindCode === 'BRAND' && '为品牌下项目创建参展申请')">
            批量操作
          </th>
        </c:if>
        <c:if test="${!exhibitionApplicationIsBrand}">
          <th data-options="field:'projectName',width:80,align:'center'">项目名称</th>
          <th data-options="field:'isMain',width:80,align:'center'"
              formatter="v=>'否是'.split('')[v *　1]">是否主项目
          </th>
          <th data-options="field:'regCutoffDate',width:130,align:'center'"
              formatter="(v, r)=>__CBTN(`EAOpenSettingWindow('\${r.projRegSetId}')`, v ? `截止至\${v.replace(/-/g,'/')}` : '设置')">
            基本信息设置
          </th>
          <th data-options="field:'1',width:180,align:'center'"
              formatter="(_, r)=>__CBTN(`EAOpenRegSet(\${r.projectId}, 1, \${r.targetType})`, '中文版设置', r.regEnglish ? '<i style=\'margin-right:20px\'/>' : '') + (r.regEnglish ? __CBTN(`EAOpenRegSet(\${r.projectId}, 2, \${r.targetType})`, '英文版设置') : '')">
            登记设置
          </th>
          <th data-options="field:'releaseReg',width:80,align:'center'"
              formatter="(v, r)=>__CBTN(`EAReleaseSet('\${r.projRegSetId}', \${v}, \${r.targetType})`, '发布*撤销'.split('*')[v *　1])">发布
          </th>
          <th data-options="field:'2',width:180,align:'center'"
              formatter="(_, r)=>__CBTN(`EAOpenRegChannelLink(\${r.projectId}, 4, '\${r.projectName}')`, '查看地址')">
            申请地址
          </th>
          <th data-options="field:'projectId',width:80,align:'center'"
              formatter="(v, r)=>__CBTN(`EAOpenDataSync('\${r.projRegSetId}')`, '设置')">数据同步
          </th>
        </c:if>
      </tr>
      </thead>
    </table>
    <div id="exhibition-application-blank" style="width:100%;height:100%;">
      <img src="/eip-web-crm/pages/crm/img/project-placeholder.png" alt=""/>
      <h3>请点击新增参展申请项目，设置参展申请以获取展商信息</h3>
      <a href="javascript:EAOpenSettingWindow()">新增参展申请项目</a>
    </div>
  </div>
</div>
<!--登记地址-->
<div id="exhibition-application-regAddress-window" class="easyui-dialog"
     data-options="closed:true,title:'申请地址',modal:true,shadow:false"
     style="width:600px;">
  <div id="exhibition-application-regAddress-panel" class="easyui-panel" title="">
    <div class="all_address">
      <h6>手机申请地址</h6>
      <textarea cols="50" rows="3" id="exhibition-application-regAddress-mobile" readonly="readonly"></textarea>
      <button class="btn-primary" onclick="EACopyLinkAddress('regAddress-mobile')">复制链接</button>
      <a id="exhibition-application-regAddress-mobile-open" href="" target="_blank">直接打开</a>
    </div>
    <div class="all_address">
      <h6>电脑申请地址</h6>
      <textarea cols="50" rows="3" id="exhibition-application-regAddress-computer" readonly="readonly"></textarea>
      <button class="btn-primary" onclick="EACopyLinkAddress('regAddress-computer')">复制链接</button>
      <a id="exhibition-application-regAddress-computer-open" href="" target="_blank">直接打开</a>
    </div>
    <div class="address_download">
      <ul>
        <li>
          <div id="exhibition-application-regAddress-code" class="qr_code"></div>
          <div class="address_download_button">
            <a id="exhibition-application-regAddress-download" download="qrcode.jpg"></a>
            <button id="exhibition-application-regAddress-saveQrCode" class="am-btn" onclick="EADownloadQRCode()">下载</button>
          </div>
        </li>
        <li>
          <div class="prompt_text">
            <p>左侧手机登记二维码点击下载即可手机扫码可预览</p>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>
<!--设置登记截止时间-->
<div id="exhibition-application-basic-setting"
     class="easyui-window"
     data-options="closed:true,title:'基本信息设置',modal:true,maximizable: false,resizable: false,minimizable: false"
     style="width:400px;">
  <div id="exhibition-application-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-save"
         onclick="EASaveBasicSet()"
         plain="true"
         style="margin-right: 6px;">保存</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-cancel"
         onclick="__$('basic-setting').removeAttr('data-pid').window('close')"
         plain="true"
         style="margin-right: 6px;">取消</a>
    </div>
  </div>
  <div class="easyui-panel"
       style="display: flex;flex-direction: column;padding: 10px;">
    <div style="margin-top: 10px;">
      <label style="width:100px;display: inline-block;text-align: right;margin-right: 10px;">
        <font style="color: red;">*</font>项目名称
      </label>
      <input
          class="easyui-combobox"
          style="width:200px;height:25px"
          id="exhibition-application-projectId"
          data-options="valueField:'projectId',textField:'projectName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>
    </div>
    <div style="margin-top: 10px;">
      <label style="width:100px;display: inline-block;text-align: right;margin-right: 10px;">
        <font style="color: red;">*</font>登记截止时间
      </label>
      <input id="exhibition-application-regcuttof" class="easyui-datetimebox" style="width:200px;height:25px;">
      <span style="display: inline-block;width: 30px;"></span>
    </div>
    <div style="margin: 10px 0;">
      <label><span style="width:100px;display: inline-block;text-align: right;margin-right: 10px;">启用英文登记</span><input type="checkbox" id="exhibition-application-regEnglish"></label>
    </div>
  </div>
</div>
<!--数据同步-->
<div id="exhibition-application-data-sync-window" class="easyui-dialog" title="数据同步"
     data-options="modal:true,closed:true,shadow:false,"
     style="width:800px;">
  <div id="exhibition-application-data-sync-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-save"
         onclick="EASaveSynData()"
         plain="true"
         style="margin-right: 6px;">保存</a>
      <a href="javascript:void(0);"
         class="easyui-linkbutton"
         iconCls="icon-cancel"
         onclick="__$('data-sync-window').removeAttr('data-pid').window('close')"
         plain="true"
         style="margin-right: 6px;">取消</a>
    </div>
  </div>
  <div id="exhibition-application-data-sync-panel" class="easyui-panel" style="width: 100%;">
    <h3 class="block-title">数据同步回CRM</h3>
    <ul class="setting-list">
      <li>
        <label style="display: flex;margin-left: 102px;margin-bottom: 20px;">
          <input class="my-checkbox" type="checkbox" id="exhibition-application-synData" value="0">
          <span style="color: #333;margin-left: 13px;line-height: 22px;">数据同步到数据总览</span>
        </label>
      </li>
      <li id="exhibition-application-showSync" style="display:none;">
        <div style="display: flex;">
          <div style="margin-left: 47px; width: 100%;" class="team-group">
            <input id="exhibition-application-synTeam"
                   style="width: 225px;"
                   class="easyui-combobox"
                   data-options="
										 valueField: 'id',
										 textField: 'text',
										 width: 225,
										 label: '选择团队',
										 panelHeight:'auto',
										 panelMaxHeight:'200px',
										 limitToList:true"/>
            <input id="exhibition-application-synGroup"
                   class="easyui-combotree"
                   style="width: 225px;"
                   data-options="
										 width: 225,
										 label: '选择分组',
										 panelHeight:'auto',
										 panelMaxHeight:'200px',
										 limitToList:true"/>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>
<style>
  #exhibition-application-window-toolbar .my-toolbar-button,
  #exhibition-application-data-sync-toolbar .my-toolbar-button {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 36px;
    background-color: #f5f5f5;
  }

  #exhibition-application-data-sync-panel .block-title {
    margin: 0;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ccc;
    border-left: none;
    border-right: none;
    padding-left: 1em;
  }

  #exhibition-application-data-sync-panel .setting-list {
    list-style: none;
    margin: 0;
    padding: 20px;
  }

  #exhibition-application-data-sync-panel .team-group label {
    text-align: right !important;
    height: 30px !important;
    padding-right: 11px !important;
    line-height: 30px !important;
  }

  #exhibition-application-data-sync-panel .team-group {
    padding-bottom: 30px;
  }

  #exhibition-application-data-sync-panel .team-group span.combo {
    width: 225px !important;
  }

  .panel.combo-p.panel-htop .combobox-item {
    height: 20px;
  }

  .panel.combo-p.panel-htop .tree > li:first-child .tree-node .tree-icon.tree-file {
    background: none;
  }
</style>