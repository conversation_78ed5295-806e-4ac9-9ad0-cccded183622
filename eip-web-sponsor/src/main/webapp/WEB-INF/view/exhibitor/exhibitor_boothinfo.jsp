<!--1展位信息管理管理520 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
	fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
	fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
	fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
	fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
	fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
		else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
		else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
		else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
		else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
		else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
		else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
		else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
		else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
		else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
		else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
	}
%>
<%@ include file="../common/project.jsp" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
	.row{
		width: 98%;
		float: left;
		margin-left: 1%;
		margin-top: 5px;
	}
	.col-5{
		display:inline-block;
		width: 50%;
		float: left;
	}
	.col-3{
		display:inline-block;
		float: left;
		  width: 27%;
	}
	.col-7{
		/*display:inline-block;*/
		float: left;
		display:inline-block;
		width: 70%;
	}
	</style>
<div class="easyui-layout" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibitor-boothinfo-toolbar">
            <div class="my-toolbar-button">
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_boothinfo_fresh()" plain="true">刷新</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download-all" onclick="exhibitor_boothinfo_export()" plain="true">导出</a>
            </div>
            <div class="my-toolbar-search">
            	<%--<div style="display: none">--%>
				<%--	<label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;--%>
				<%--	<select id="exhibitor-boothinfo-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select> <a--%>
				<%--		href="javascript:void(0);" class="easyui-linkbutton"--%>
				<%--		style="width:35px;height:25px" onclick="exhibitor_popup_project($('#exhibitor-boothinfo-combobox-keyword-project'))">...</a>&nbsp;&nbsp;&nbsp;&nbsp;--%>
				<%--</div>--%>
				<div style="float: left;margin-right: 10px;">
					<label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-boothinfo-keyword-companyName" class="my-text" style="width:100px">
					<!-- <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px" onclick="exhibitor_boothinfo_select_company()" >选取</a> -->
					&nbsp;&nbsp;
				</div>
				<div style="float: left; display: none;" id="exhibition_apply_customerServiceId">
					<span style="display:inline-block; width: 70px;">客服专员</span>
					<select id="exhibition_boothinfo-keyword-customerServiceId" class="easyui-combobox"
					data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'"
					style="width:125px;height:25px"></select>&nbsp;&nbsp;&nbsp;&nbsp;
				</div>
        <label>展位号</label>&nbsp;&nbsp;&nbsp;
					<input id="exhibitor-boothinfo-keyword-boothCode" class="my-text" style="width:100px">&nbsp;&nbsp;
				<label>展位类型</label>&nbsp;&nbsp;&nbsp;
					<input id="exhibitor-boothinfo-combobox-typeCode" class="easyui-combobox" style="width:110px;height: 26px;">&nbsp;&nbsp;
					<script>
						$(function() {
							$.ajax({
								url:'${eippath}/boothType/getAll?exhibitCode=' + exhibitCode_for_pass,
								dataType : "json", //返回数据类型
								type:"post",
								async: false,
							}).done(data => {
								// project_boothdata_typeData = data;
								$('#exhibitor-boothinfo-combobox-typeCode').combobox({
									limitToList:true,panelHeight:'auto',panelMaxHeight:300,valueField: 'id',textField: 'text'
								}).combobox('loadData',data);
							}).
							fail(err => {
								console.log('err',err);
								$.messager.alert('提示','获取展位类型失败！','error');
							})
						})
					</script>
				<div style="display: inline-block;">
					<style>
						#exhibitor-boothinfo-combotree-subpro+.combo{
							margin-right: 0 !important;
						}
					</style>
					<label>项目</label>&nbsp;&nbsp;&nbsp;
					<div style="position: relative;display: inline-block;margin-right: 10px;">
						<select id="exhibitor-boothinfo-combotree-subpro" class="easyui-combotree" style="width:320px;height:25px;"></select>
						<a href="javascript:;" onclick="$('#exhibitor-boothinfo-combotree-subpro').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
					</div>
				</div>
				<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_boothinfo_search()">查询</a>
            </div>
        </div>
        <!-- end of toolbar -->
        <table id="exhibitor-boothinfo-datagrid" class="easyui-datagrid" toolbar="#exhibitor-boothinfo-toolbar"
        	data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,
        	 					fitColumns:false,fit:true,method:'post',selectOnCheck:true,remoteSort: false,
        	        		onClickRow: function (rowIndex, rowData) {
        	        			var l = $(this).datagrid('getRows').length;
        	        			for(var i = 0; i < l; i++){
        	    			if(i != rowIndex)$(this).datagrid('unselectRow', i);
        	    		}
        	 				},">
        	<thead>
<%--				<tr>--%>
<%--					<th data-options="field:'ck',checkbox:true"></th>--%>
<%--					<th data-options="field:'clientId',width:60">公司编号</th>--%>
<%--					<th data-options="field:'clientName',width:120">公司名称</th>--%>
<%--					<th data-options="field:'contractCode',width:120">合同号</th>--%>
<%--					<th data-options="field:'section',width:80">展馆</th>--%>
<%--					<th data-options="field:'boothNum',width:80">展位号</th>--%>
<%--					<th data-options="field:'boothTypeCode',width:80">展位类型</th>--%>
<%--					<th data-options="field:'boothArea',width:80">面积</th>--%>
<%--					<th data-options="field:'qtyConverted',width:80">折算标摊数</th>--%>
<%--					<th data-options="field:'personNum',width:80">人员个数</th>--%>
<%--					<!-- <th data-options="field:'seesawConfirm',width:80,formatter:formatterIsNull">楣板</th>--%>
<%--					<th data-options="field:'cataConfirm',width:80,formatter:formatterIsNull">会刊</th>--%>
<%--					<th data-options="field:'toolConfirm',width:80,formatter:formatterIsNull">展具</th>--%>
<%--					<th data-options="field:'buildConfirm',width:80,formatter:formatterIsNull">搭建</th>--%>
<%--					<th data-options="field:'logiConfirm',width:80,formatter:formatterIsNull">物流</th>--%>
<%--					<th data-options="field:'visaConfirm',width:80,formatter:formatterIsNull">代办签证</th>--%>
<%--					<th data-options="field:'airConfirm',width:80,formatter:formatterIsNull">机票</th>--%>
<%--					<th data-options="field:'hotelConfirm',width:80,formatter:formatterIsNull">酒店</th>--%>
<%--					<th data-options="field:'insureConfirm',width:80,formatter:formatterIsNull">保险</th> -->--%>
<%--					<th data-options="field:'boothCode',width:80">相关附件</th>--%>
<%--					--%>
<%--				</tr>--%>
			</thead>
        </table>
    </div>
</div>
<!-- begin of easyui-window -->
<div id="exhibitor-boothinfo-window-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true" style="width:572px;">
	<div id="exhibitor-boothinfo-panel-select-company" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
	   		<div data-options="region:'center'">
	   			<table id="exhibitor-boothinfo-datagrid-select-company" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'clientId',width:20">公司编号</th>
							<th data-options="field:'companyName',width:40">公司名称</th>
						</tr>
					</thead>
		        </table>
	   		</div>
	   		<div data-options="region:'south'" style="height:57px;padding:12px;">
	   			<div class="my-toolbar-search">
	            	<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-boothinfo-keyword-select-company" class="my-text" style="width:150px">&nbsp;
	                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_boothinfo_search_select_company()">查询</a>
	                <div style="float:right;">
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_boothinfo_ok_select_company()" plain="false" style="width:60px;height:25px">确定</a>
			        	&nbsp;
			        	<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_boothinfo_return_select_company()" plain="false" style="width:60px;height:25px">返回</a>
				    </div>
	            </div>
	   		</div>
	   	</div>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window   iconCls:'icon-save' -->
<div id="exhibitor-huikan1" class="easyui-window" title="会刊"  data-options="modal:true,closed:true" style="width:700px;height:auto;padding:2px;">
	<div id="exhibitor-huikan-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_huikan_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_huikan_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-huikan-panel-1" class="easyui-panel" title="公司信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input id="exhibitor-huikan-companyName" hint="companyName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称（）</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="companyName" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        </ul>
    </div>
	<div id="exhibitor-huikan-panel-2" class="easyui-panel" title="会刊信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="contactsName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人（EN）</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="contactsEn" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>职务</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="post" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>职务（EN）</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="postEn" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
		    <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>电话</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="tel" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>传真</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="fax" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>邮箱</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="email" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>网址</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="site" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>产品类型</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="typeProduct" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>产品类型（EN）</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="typeProductEn" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>公司简介</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="companyProfile" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>公司简介（EN）</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="companyProfileEn" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>

        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		       		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
		        	<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
   	</div>
	<div id="exhibitor-huikan-panel-3" class="easyui-panel" title="会刊费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-huikan-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-meiban" class="easyui-window" title="楣板"  data-options="modal:true,closed:true" style="width:700px;height:auto;padding:2px;">
	<div id="exhibitor-meiban-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_meiban_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_meiban_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-meiban-panel-1" class="easyui-panel" title="公司信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input id="exhibitor-meiban-companyName" hint="companyName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
	        			<option>跟团</option>
	        			<option>不跟团</option>
	        	</select>
        	</li> -->
        </ul>
    </div>
	<div id="exhibitor-meiban-panel-2" class="easyui-panel" title="楣板信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展位号</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="boothCode" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>楣板名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="fasciaName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>楣板条数</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="fasciaAmount" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>展位规格</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="fasciaSpecies" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>展位面积</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="boothArea" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
		    <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		        <div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
		        <input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
   	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-dajian" class="easyui-window" title="搭建"  data-options="modal:true,closed:true" style="width:700px;height:auto;padding:2px;">
	<div id="exhibitor-dajian-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_dajian_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_dajian_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-dajian-panel-1" class="easyui-panel" title="公司信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input id="exhibitor-dajian-companyName" hint="companyName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
	        			<option>跟团</option>
	        			<option>不跟团</option>
	        	</select>
        	</li> -->
        </ul>
    </div>
	<div id="exhibitor-dajian-panel-2" class="easyui-panel" title="搭建信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>客户名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展馆</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展位类型</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展位号</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展位个数</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>展位规格</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>面积</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>开口情况</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>门楣信息</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="lintel" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>标准配置</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>制作工厂</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="factory" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li><li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>设计师</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="designer" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>业绩归属人</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>合同号</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li><li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>签约日期</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>跟进提醒</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li><li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>搭建入场时间</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="enterTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>搭建结束时间</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="leaveTime" class="easyui-datetimebox" control="datetimebox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		       		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
		        	<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
   </div>
	<div id="exhibitor-dajian-panel-3" class="easyui-panel" title="搭建费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-dajian-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-wuliu" class="easyui-window" title="物流"  data-options="modal:true,closed:true" style="width:700px;height:auto;padding:2px;">
	<div id="exhibitor-wuliu-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_wuliu_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_wuliu_cancel()" plain="true">取消</a>
	    </div>
    </div>
    <div id="exhibitor-wuliu-panel-1" class="easyui-panel" title="公司信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input id="exhibitor-wuliu-companyName" hint="companyName" class="easyui-textbox" control="textbox" tag="1" readonly style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系人</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>联系方式</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="clientId" class="easyui-textbox" control="textbox" tag="1" style="width:175px;height:25px">
        	</li>
        	<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>是否跟团</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<select id="f_contract_id" name="f_contract_id"  class="easyui-combobox" style="width:175px;height:25px">
	        			<option>跟团</option>
	        			<option>不跟团</option>
	        	</select>
        	</li> -->
        </ul>
    </div>
	<div id="exhibitor-wuliu-panel-2" class="easyui-panel" title="物流信息" style="width:100%;height:auto;padding:15px;">
        <ul style="list-style:none;margin:0;padding:0;">
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        		<div style="width:120px;height:25px;display:inline-block;"><label>客户名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>出发地</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="startPlace" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>目的地</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="destination" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>运输品规格</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="typeGoods" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>单价</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
        	</li><li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>个数</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="goodsAmount" class="easyui-numberbox" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        			<div style="width:120px;height:25px;display:inline-block;"><label>总价</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
        			<input hint="totalPrice" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
        	</li>
        	<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
		       		<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
		        	<input hint="memo" class="easyui-textbox" data-options="multiline:true" control="textbox" tag="0" style="width:490px;height:100%">
		    </li>
        </ul>
	</div>
	<div id="exhibitor-wuliu-panel-3" class="easyui-panel" title="物流费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-wuliu-datagrid-cost" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window 现在的展位信息 -->
<div id="exhibitor-zhanju" class="easyui-window" title="展具增租" data-options="modal:true,closed:true" style="width:800px;height:auto;padding:2px;">
	<div id="exhibitor-zhanju-window-toolbar">
	    <div class="my-toolbar-button">
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_zhanju_save()" plain="true">保存</a>
        	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_zhanju_cancel()" plain="true">取消</a>
	    </div>
    </div>
	<div id="exhibitor-zhanju-panel-1" class="easyui-panel" title="展会信息" style="width:100%;height:auto;padding:15px;">
		<ul style="list-style:none;margin:0;padding:0;">
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>展会名称</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
	     	</li>
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="XX" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px">
	     	</li>
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>总价</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="toolMoney" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
	     	</li>
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>押金</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="deposit" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
	     	</li>
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>押金已退</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="depsReturn" type="checkbox" class="my-checkbox" control="checkbox" tag="0">
	     	</li>
	     	<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
	     		<div style="width:120px;height:25px;display:inline-block;"><label>退款金额</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
	     		<input hint="returnMoney" class="easyui-numberbox" data-options="precision:2" control="numberbox" tag="0" style="width:175px;height:25px">
	      	</li>
		</ul>
	</div>
	<div id="exhibitor-zhanju-panel-2" class="easyui-panel"  style="width:100%;height:300px;padding:0px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'north'" style="height:52px;padding:12px;">
				<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_zhanju_init()" plain="false" style="width:60px;height:25px">初始化</a>
			</div>
			<div data-options="region:'center'" style="height:257px;">
		    	<table id="exhibitor-zhanju-datagrid-cost" class="easyui-datagrid"
					data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex){
		               				$(this).datagrid('unselectRow', i).datagrid('endEdit', i);
		               			}
		               		}
		               		$(this).datagrid('beginEdit', rowIndex);
		 				},">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'toolId',width:30">展具编号</th>
							<th data-options="field:'toolName',width:40">展具名称</th>
							<th data-options="field:'toolPrice',width:30">单价</th>
							<th data-options="field:'toolAmount',width:30,editor:'numberbox'">数量</th>
							<th data-options="field:'totalPrice',width:40,editor:{type:'numberbox',options:{precision:2}}">总价</th>
							<th data-options="field:'isRecovery',width:30,
								formatter:function(value, row){
									if(value == 1)return '是';
									else if(value == 0)return '否';
								},
								editor:{
									type:'combobox',
									options:{
										valueField:'id',
										textField:'text',
										panelHeight:'auto',data:[
        								{id:'1',text:'是'},{id:'0',text:'否'}]
        							}
								}">是否回收</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
	<div id="exhibitor-zhanju-panel-3" class="easyui-panel" title="展具费用管理" style="width:100%;height:200px;padding:2px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'" style="height:257px;">
				<table id="exhibitor-zhanju-datagrid-cost-2" class="easyui-datagrid"
		        	data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
		        		onClickRow: function (rowIndex, rowData) {
		        			var l = $(this).datagrid('getRows').length;
		        			for(var i = 0; i < l; i++){
		               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
		               		}
		 				},">
		        	<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">标准报价</th>
							<th data-options="field:'prodSpec',width:30">产品规格</th>
							<th data-options="field:'discount',width:30">折扣%</th>
							<th data-options="field:'price',width:30">单价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
							<th data-options="field:'qty',width:30">数量</th>
							<th data-options="field:'amount',width:30">金额</th>
						</tr>
					</thead>
		        </table>
			</div>
			<!-- <div data-options="region:'south'" style="height:50px;padding:10px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>合同总价</label></div>
				<input id="exhibitor-contract-amount" hint="amount" class="easyui-numberbox" precision="2" control="numberbox" tag="0" style="width:175px;height:25px">
			</div> -->
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<script type="text/javascript">
	var exhibitor_boothinfo_alocate_id = -1;
	var exhibitor_boothinfo_save_id;
	var exhibitor_boothinfo_clientId;
	var isHaveJurisdiction_exhibitor_boothinfo = <%=fu_section_graph_set_kywy%> ;
	$(function(){
		if(isHaveJurisdiction_exhibitor_boothinfo){
			$("#exhibition_apply_customerServiceId").show()
		}else{
			$("#exhibition_apply_customerServiceId").hide()
		}
		var cnt = 0;
		// $('#exhibitor-boothinfo-combobox-keyword-project').combobox({
		// 	valueField: 'id',
		// 	textField: 'text',
		// 	panelHeight: '200px',
		// 	url: eippath + '/project/getAllSelectOne',
		// 	onLoadSuccess: function(data){
		// 		$(this).combobox('select', project_for_pass);
		// 		if(cnt == 0){
		// 			getBoothinfoList()
		// 			//  exhibitor_boothinfo_fresh();
		// 			cnt++;
		// 		}
		// 	}
		// });
	});

	function exhibitor_boothinfo_export() {
		var lengths = $("#exhibitor-boothinfo-datagrid").datagrid("getRows").length;
		if(lengths>0){
			location.href=eippath + '/boothInfo/exportOfBoothInfo?projectId='+ project_for_pass
		}else{
			$.messager.alert('提示','暂无数据！','warning');
		}

	}

	function exhibitor_boothinfo_fresh(first=false){
		if(!first) { // 清空查询条件
			$("#exhibitor-boothinfo-keyword-companyName").val('')
			$('#exhibition_boothinfo-keyword-customerServiceId').combobox('clear')
			$('#exhibitor-boothinfo-keyword-boothCode').val('')
			$('#exhibitor-boothinfo-combotree-subpro').combotree('clear')
		}

		// $.ajax({
		//     url: eippath + '/boothInfo/getList?key=0',
		//     type:"post",
		//     async:false,
		//     datatype:'json',
		//     data:{projectId:$('#exhibitor-boothinfo-combobox-keyword-project').combobox('getValue')},
		//     success:function(data){
		// 		var allData = [];
		// 		var eachData;
		//         if(data.state == 1){
		// 			data.result.forEach(item=>{
		// 				eachData = item
		// 				let datas = eval('('+item.comfirmStateJson+')');
		// 				if(item.comfirmStateJson!=null && item.comfirmStateJson!=""){
		// 			        Object.keys(datas).forEach(function(key){
		// 			            eachData[key] = datas[key]
		// 			        });
		// 					allData.push(eachData)
		// 				}
	 //                })
		// 		}
		// 		$('#exhibitor-boothinfo-datagrid').datagrid({
		// 		    data: allData
		// 		});
		//     }
		// });
		$('#exhibitor-boothinfo-datagrid').datagrid({
			url: eippath + '/boothInfo/getList?key=0',
			queryParams: {
				projectId: project_for_pass,
				exhibitCode: exhibitCode_for_pass,
            },
			onLoadSuccess: function(data){
                if(exhibitor_boothinfo_alocate_id === -1){
				    $(this).datagrid('selectRow', -1);
                } else{
					var rows = $(this).datagrid("getRows");
					console.log(rows)
					for(var i = 0; i < rows.length; i++){
						if(rows[i].boothInfoId === exhibitor_boothinfo_alocate_id){
							$(this).datagrid('selectRow', i);
							exhibitor_boothinfo_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}
	function getBoothinfoList(first=false){
	    $.ajax({
	        url: eippath + '/examine/makeTreeJson',
	        type:"post",
	        async:false,
	       datatype:'json',
	        data:{projectId:project_for_pass },
	        success:function(data){
	            let columns = [];
	            let columnsOne=[
	                {field:"ck",checkbox:"true"},
	                {field:"projectName",title: '项目',width:150, formatter: (value,row,index) => {
											value = value || ''
											return value.length > 16 ? value.substr(0,16) + '...' : value
									}},
	                {field:"clientId",title: '公司编号',width:100 },
	                {field:"clientName",title: '公司名称',width:200 },
					{field:"customerServiceName",title: '客服专员',width:200 },
					{field:"operName",title: '业绩归属人',width:200 },
					{field:"deliveryAddress",title: '邮寄地址',width:200 },
	                {field:"contractCode",title: '合同号',width:200 },
	                {field:"section",title: '展馆',width:150 },
	                {field:"boothNum",title: '展位号',width:150,sortable:"true"},
	                {field:"boothTypeName",title: '展位类型',width:120 },
	                {field:"boothArea",title: '面积',width:70 },
	                {field:"qtyConverted",title: '展位个数',width:80 },
	                {field:"personNum",title: '人员个数',width:80 },
					{field:"boothSpec",title: '展位规格',width:100 },
					{field:"linkman",title: '联系人',width:100 },
					{field:"linkmanEmail",title: '邮箱',width:180 },
					{field:"linkmanMobile",title: '手机',width:150 },
	            ];
				var datas = eval('('+data+')');
	            datas.forEach(item=>{
					item.children.forEach(item=>{
						let itemDtl={};
						itemDtl['field']= item.taskKindCode;
						itemDtl['title']= item.text;
						itemDtl['formatter']= formatterIsNull;
						itemDtl['width']=100
						columnsOne.push(itemDtl)
					})
	            })
	            columns.push(columnsOne);
	            $('#exhibitor-boothinfo-datagrid').datagrid({
	                columns:columns
	            })
	           exhibitor_boothinfo_fresh(first);
	        }
	    });
	}
	function exhibitor_boothinfo_search(){
		// if($("#exhibitor-boothinfo-keyword-companyName").val() == '')exhibitor_boothinfo_clientId = null;
		$('#exhibitor-boothinfo-datagrid').datagrid({
			url: eippath + '/boothInfo/getList?key=1',
			queryParams: {
				projectId: project_for_pass,
				customerServiceId: $('#exhibition_boothinfo-keyword-customerServiceId').combobox('getValue'),
				// clientId: exhibitor_boothinfo_clientId,
				companyName: $("#exhibitor-boothinfo-keyword-companyName").val() || '',
				boothCode: $('#exhibitor-boothinfo-keyword-boothCode').val(),
				typeCode: $('#exhibitor-boothinfo-combobox-typeCode').combotree('getValue'),
				childProjectId: $('#exhibitor-boothinfo-combotree-subpro').combotree('getValue'),
				exhibitCode: exhibitCode_for_pass,
            }
		});
	}
	function exhibitor_boothinfo_formatFasciaConfim(val, row){
		if(val == 0 || val == null)return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_fascia(' + row.serveFasciaId + ')">编辑</a>';
		else return '已确认';
	}
	function exhibitor_boothinfo_select_company(){
		$('#exhibitor-boothinfo-window-select-company').window('open');
		$('#exhibitor-boothinfo-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=0',
			queryParams: {
				projectId: project_for_pass
            }
		});
	}
	function exhibitor_boothinfo_search_select_company(){
		$('#exhibitor-boothinfo-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=1',
			queryParams: {
				projectId: project_for_pass,
				companyName: $('#exhibitor-boothinfo-keyword-select-company').val()
            }
		});
	}
	function exhibitor_boothinfo_ok_select_company(){
		var row = $('#exhibitor-boothinfo-datagrid-select-company').datagrid('getSelected');
		if (row){
			exhibitor_boothinfo_clientId = row.clientId;
			$("#exhibitor-boothinfo-keyword-companyName").val(row.companyName);
			$('#exhibitor-boothinfo-window-select-company').window('close');
		}else{
			$.messager.alert('提示','未选中内容！','warning');
		}
	}
	function exhibitor_boothinfo_return_select_company(){
		$('#exhibitor-boothinfo-window-select-company').window('close');
	}
	function exhibitor_boothinfo_formatFasciaConfim(val, row){

		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_Fascia(' + row.boothInfoId + ',\'' + row.companyName + '\',' + row.serveFasciaId + ')">编辑</a>';

	}
	var exhibitor_boothinfo_save_serveFasciaId;
	var exhibitor_boothinfo_edit_mode_meiban;
	function  exhibitor_boothinfo_Fascia(boothInfoId, companyName, serveFasciaId){
		$("#exhibitor-meiban").window("open");
		//load_bs_productByType('楣板','exhibitor-meiban-datagrid-cost');
		$("#exhibitor-meiban-companyName").textbox("setValue", companyName);
		exhibitor_boothinfo_boothInfoId = boothInfoId;
		exhibitor_boothinfo_save_serveFasciaId = serveFasciaId;
		if(serveFasciaId == null){
			exhibitor_boothinfo_setEditInfoClear("#exhibitor-meiban-panel-2 ul");
			exhibitor_boothinfo_edit_mode_meiban = "Add";
			//$('#exhibitor-trip-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_boothinfo_edit_mode_meiban = "Mod";
			var vServeFasciaId = serveFasciaId;
			$.ajax({
				url: eippath + '/serveFascia/selectByServeFasciaId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveFasciaId: vServeFasciaId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_boothinfo_setEditInfoInit("#exhibitor-meiban-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			//exhibitor_boothinfo_prodcost(vServeTripId, '行程');
		}
	}
	function exhibitor_meiban_save(){
		/*var l = $('#exhibitor-trip-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-trip-datagrid-cost').datagrid('endEdit', i);
   		}*/
		var postData = {};
		postData['editMode'] = exhibitor_boothinfo_edit_mode_meiban;
		postData['boothInfoId'] = exhibitor_boothinfo_boothInfoId;
		postData['serveFasciaId'] = exhibitor_boothinfo_save_serveFasciaId;
		postData['projectId'] = project_for_pass
		//postData['taskId'] = exhibitor_personnelinfo_xingcheng_taskId;
		exhibitor_boothinfo_postData(postData, "#exhibitor-meiban-panel-2 ul");
		/*
		var rows = $('#exhibitor-trip-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].serviceType'] = rows[i].serviceType;
			}
		}*/
		$.ajax({
			url: eippath + '/serveFascia/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_boothinfo_alocate_id = postData['boothInfoId'];
					$('#exhibitor-meiban').window('close', true);
					exhibitor_boothinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_huikan_cancel(){
		$('#exhibitor-meiban').window('close');
	}
	function  exhibitor_boothinfo_formatcatalogueConfim(val,row){

		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_Catalogue(' + row.boothInfoId + ',\'' + row.companyName + '\',' + row.serveCatalogueId + ')">编辑</a>';

	}
	var exhibitor_boothinfo_boothInfoId;
	var exhibitor_boothinfo_save_serveCatalogueId;
	var exhibitor_boothinfo_edit_mode_huikan;
	function  exhibitor_boothinfo_Catalogue(boothInfoId, companyName, serveCatalogueId){
		$("#exhibitor-huikan1").window("open");
		//load_bs_productByType('会刊','exhibitor-huikan-datagrid-cost');
		$("#exhibitor-huikan-companyName").textbox("setValue", companyName);
		exhibitor_boothinfo_boothInfoId = boothInfoId;
		exhibitor_boothinfo_save_serveCatalogueId = serveCatalogueId;
		if(serveCatalogueId == null){
			exhibitor_boothinfo_setEditInfoClear("#exhibitor-huikan-panel-2 ul");
			exhibitor_boothinfo_edit_mode_huikan = "Add";
			//$('#exhibitor-trip-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_boothinfo_edit_mode_huikan = "Mod";
			var vServeCatalogueId = serveCatalogueId;
			$.ajax({
				url: eippath + '/serveCatalogue/selectByServeCatalogueId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveCatalogueId: vServeCatalogueId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_boothinfo_setEditInfoInit("#exhibitor-huikan-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			//exhibitor_boothinfo_prodcost(vServeTripId, '行程');
		}
	}
	function exhibitor_huikan_save(){
		/*var l = $('#exhibitor-trip-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-trip-datagrid-cost').datagrid('endEdit', i);
   		}*/
		var postData = {};
		postData['editMode'] = exhibitor_boothinfo_edit_mode_huikan;
		postData['boothInfoId'] = exhibitor_boothinfo_boothInfoId;
		postData['serveCatalogueId'] = exhibitor_boothinfo_save_serveCatalogueId;
		postData['projectId'] = project_for_pass
		//postData['taskId'] = exhibitor_personnelinfo_xingcheng_taskId;
		exhibitor_boothinfo_postData(postData, "#exhibitor-huikan-panel-2 ul");
		/*
		var rows = $('#exhibitor-trip-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].serviceType'] = rows[i].serviceType;
			}
		}*/
		$.ajax({
			url: eippath + '/serveCatalogue/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_boothinfo_alocate_id = postData['boothInfoId'];
					$('#exhibitor-huikan').window('close', true);
					exhibitor_boothinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_huikan_cancel(){
		$('#exhibitor-huikan').window('close');
	}
	function  exhibitor_boothinfo_formattoolConfim(val,row){

		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_zhanju(' + row.boothInfoId + ',\'' + row.companyName + '\',' + row.toolId + ')">编辑</a>';

	}
	function  exhibitor_boothinfo_formatconstructConfim(val,row){

		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_construct(' + row.boothInfoId + ',\'' + row.companyName + '\',' + row.serveConstructId + ')">编辑</a>';

	}
	var exhibitor_boothinfo_save_serveConstructId;
	var exhibitor_boothinfo_edit_mode_dajian;
	function  exhibitor_boothinfo_construct(boothInfoId, companyName, serveConstructId){
		$("#exhibitor-dajian").window("open");
		//load_bs_productByType('搭建','exhibitor-dajian-datagrid-cost');
		$("#exhibitor-dajian-companyName").textbox("setValue", companyName);
		exhibitor_boothinfo_boothInfoId = boothInfoId;
		exhibitor_boothinfo_save_serveConstructId = serveConstructId;
		if(serveConstructId == null){
			exhibitor_boothinfo_setEditInfoClear("#exhibitor-dajian-panel-2 ul");
			exhibitor_boothinfo_edit_mode_dajian = "Add";
			//$('#exhibitor-trip-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_boothinfo_edit_mode_dajian = "Mod";
			var vServeConstructId = serveConstructId;
			$.ajax({
				url: eippath + '/serveConstruct/selectByServeConstructId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveConstructId: vServeConstructId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_boothinfo_setEditInfoInit("#exhibitor-dajian-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			//exhibitor_boothinfo_prodcost(vServeTripId, '行程');
		}
	}
	function exhibitor_dajian_save(){
		/*var l = $('#exhibitor-trip-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-trip-datagrid-cost').datagrid('endEdit', i);
   		}*/
		var postData = {};
		postData['editMode'] = exhibitor_boothinfo_edit_mode_dajian;
		postData['boothInfoId'] = exhibitor_boothinfo_boothInfoId;
		postData['serveConstructId'] = exhibitor_boothinfo_save_serveConstructId;
		postData['projectId'] = project_for_pass;
		//postData['taskId'] = exhibitor_personnelinfo_xingcheng_taskId;
		exhibitor_boothinfo_postData(postData, "#exhibitor-dajian-panel-2 ul");
		/*
		var rows = $('#exhibitor-trip-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].serviceType'] = rows[i].serviceType;
			}
		}*/
		$.ajax({
			url: eippath + '/serveConstruct/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_boothinfo_alocate_id = postData['boothInfoId'];
					$('#exhibitor-dajian').window('close', true);
					exhibitor_boothinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_dajian_cancel(){
		$('#exhibitor-dajian').window('close');
	}
	function  exhibitor_boothinfo_formatlogisticsConfim(val,row){

		return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_boothinfo_logistics(' + row.boothInfoId + ',\'' + row.companyName + '\',' + row.serveLogisticsId + ')">编辑</a>';

	}
	var exhibitor_boothinfo_save_serveLogisticsId;
	var exhibitor_boothinfo_edit_mode_wuliu;
	function  exhibitor_boothinfo_logistics(boothInfoId, companyName, serveLogisticsId){
		$("#exhibitor-wuliu").window("open");
		//load_bs_productByType('物流','exhibitor-wuliu-datagrid-cost');
		$("#exhibitor-wuliu-companyName").textbox("setValue", companyName);
		exhibitor_boothinfo_boothInfoId = boothInfoId;
		exhibitor_boothinfo_save_serveLogisticsId = serveLogisticsId;
		if(serveLogisticsId == null){
			exhibitor_boothinfo_setEditInfoClear("#exhibitor-wuliu-panel-2 ul");
			exhibitor_boothinfo_edit_mode_wuliu = "Add";
			//$('#exhibitor-trip-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_boothinfo_edit_mode_wuliu = "Mod";
			var vServeLogisticsId = serveLogisticsId;
			$.ajax({
				url: eippath + '/serveLogistics/selectByServeLogisticsId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveLogisticsId: vServeLogisticsId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_boothinfo_setEditInfoInit("#exhibitor-wuliu-panel-2 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			//exhibitor_boothinfo_prodcost(vServeTripId, '行程');
		}
	}
	function exhibitor_wuliu_save(){
		/*var l = $('#exhibitor-trip-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-trip-datagrid-cost').datagrid('endEdit', i);
   		}*/
		var postData = {};
		postData['editMode'] = exhibitor_boothinfo_edit_mode_wuliu;
		postData['boothInfoId'] = exhibitor_boothinfo_boothInfoId;
		postData['serveLogisticsId'] = exhibitor_boothinfo_save_serveLogisticsId;
		postData['projectId'] = project_for_pass
		//postData['taskId'] = exhibitor_personnelinfo_xingcheng_taskId;
		exhibitor_boothinfo_postData(postData, "#exhibitor-wuliu-panel-2 ul");
		/*
		var rows = $('#exhibitor-trip-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['orderDtlBSs[' + i + '].prodCode'] = rows[i].prodCode;
				postData['orderDtlBSs[' + i + '].prodName'] = rows[i].prodName;
				postData['orderDtlBSs[' + i + '].prodPrice'] = rows[i].prodPrice;
				postData['orderDtlBSs[' + i + '].prodSpec'] = rows[i].prodSpec;
				postData['orderDtlBSs[' + i + '].discount'] = rows[i].discount;
				postData['orderDtlBSs[' + i + '].price'] = rows[i].price;
				postData['orderDtlBSs[' + i + '].curencyCode'] = rows[i].curencyCode;
				postData['orderDtlBSs[' + i + '].qty'] = rows[i].qty;
				postData['orderDtlBSs[' + i + '].amount'] = rows[i].amount;
				postData['orderDtlBSs[' + i + '].serviceType'] = rows[i].serviceType;
			}
		}*/
		$.ajax({
			url: eippath + '/serveLogistics/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_boothinfo_alocate_id = postData['boothInfoId'];
					$('#exhibitor-wuliu').window('close', true);
					exhibitor_boothinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_wuliu_cancel(){
		$('#exhibitor-wuliu').window('close');
	}
	function exhibitor_zhanju_init(){
		$('#exhibitor-zhanju-datagrid-cost').datagrid({url: eippath + '/exhibitTool/init'});
	}
	var exhibitor_boothinfo_save_serveToolId;
	var exhibitor_boothinfo_edit_mode_zhanju;
	function exhibitor_boothinfo_zhanju(boothInfoId, companyName, serveToolId){
		$("#exhibitor-zhanju").window("open");
		exhibitor_boothinfo_boothInfoId = boothInfoId;
		exhibitor_boothinfo_save_serveToolId = serveToolId;
		if(serveToolId == null){
			exhibitor_boothinfo_setEditInfoClear("#exhibitor-zhanju-panel-1 ul");
			exhibitor_boothinfo_edit_mode_zhanju = "Add";
			$('#exhibitor-zhanju-datagrid-cost').datagrid({url: eippath + '/data/clear_datagrid.json'});
		}else{
			exhibitor_boothinfo_edit_mode_zhanju = "Mod";
			var vServeToolId = serveToolId;
			$.ajax({
				url: eippath + '/serveTool/selectByServeToolId',
				dataType: "json",	//返回数据类型
				type: "post",
				data: {serveToolId: vServeToolId},	//发送数据
				async: true,
				success: function(data){
					exhibitor_boothinfo_setEditInfoInit("#exhibitor-zhanju-panel-1 ul", data);
				},
				error: function(){
					$.messager.alert('提示','数据发送失败！','error');
				}
			});
			$('#exhibitor-zhanju-datagrid-cost').datagrid({
				url: eippath + '/toolDetial/selectByServeVisaId',
				queryParams: {
					serveToolId: vServeToolId
	            }
			});
		}
	}
	function exhibitor_zhanju_save(){
		var l = $('#exhibitor-zhanju-datagrid-cost').datagrid('getRows').length;
		for(var i = 0; i < l; i++){
   			$('#exhibitor-zhanju-datagrid-cost').datagrid('endEdit', i);
   		}
		var postData = {};
		postData['editMode'] = exhibitor_boothinfo_edit_mode_zhanju;
		postData['boothInfoId'] = exhibitor_boothinfo_boothInfoId;
		postData['serveToolId'] = exhibitor_boothinfo_save_serveToolId;
		postData['projectId'] = project_for_pass
		exhibitor_boothinfo_postData(postData, "#exhibitor-zhanju-panel-1 ul");
		var rows = $('#exhibitor-zhanju-datagrid-cost').datagrid("getRows");
		if (rows.length > 0){
			for(var i = 0; i < rows.length; i++){
				postData['toolDetials[' + i + '].toolId'] = rows[i].toolId;
				postData['toolDetials[' + i + '].toolName'] = rows[i].toolName;
				postData['toolDetials[' + i + '].toolPrice'] = rows[i].toolPrice;
				postData['toolDetials[' + i + '].toolAmount'] = rows[i].toolAmount;
				postData['toolDetials[' + i + '].totalPrice'] = rows[i].totalPrice;
				postData['toolDetials[' + i + '].isRecovery'] = rows[i].isRecovery;
			}
		}
		$.ajax({
			url: eippath + '/serveTool/save',
			dataType: "json",	//返回数据类型
			type: "post",
			data: postData,	//发送数据
			async: true,
			success: function(data){
				if(data.state == 1){
					$.messager.alert('提示','保存成功！','info');
					exhibitor_boothinfo_alocate_id = postData['boothInfoId'];
					$('#exhibitor-zhanju').window('close', true);
					exhibitor_boothinfo_fresh();
				}else{
					$.messager.alert('提示','保存失败！','error');
				}
			},
			error: function(){
				$.messager.alert('提示','数据发送失败！','error');
			}
		});
	}
	function exhibitor_zhanju_cancel(){
		$('#exhibitor-zhanju').window('close');
	}

	//设置编辑页面内容清空
	function exhibitor_boothinfo_setEditInfoClear(ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", '');
			else if(control == "checkbox")$(this).find("input")[0].checked = false;
		});
	}
	//设置编辑页面内容初始化
	function exhibitor_boothinfo_setEditInfoInit(ul, row){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "numberbox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "datetimebox")$($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if(control == "combobox")$($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if(control == "combobox2")$($(this).find("input")[0]).combobox("setText", row[hint]);
			else if(control == "checkbox")$(this).find("input")[0].checked = row[hint];
		});
	}
	function exhibitor_boothinfo_postData(postData, ul){
		$(ul).find("li").each(function(){
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if(control == "textbox")postData[hint] = $($(this).find("input")[0]).val();
			else if(control == "numberbox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "datetimebox"){
				var v = $($(this).find("input")[0]).val();
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox"){
				var v = $($(this).find("input")[0]).combobox("getValue")
				if(v != '')postData[hint] =  v;
			}else if(control == "combobox2"){
				var v = $($(this).find("input")[0]).combobox("getText")
				if(v != '')postData[hint] =  v;
			}else if(control == "checkbox"){
				var v = $(this).find("input")[0].checked
				postData[hint] =  v;
			}
		});
	}
</script>


<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
			loadExhibitorBoothinfoCombotreeSubpro()
			getBoothinfoList(true)
		});

		$('#win').window('open');

		function load_bs_productByType(type,datagrid){
			$('#'+datagrid).datagrid({
				url: eippath + '/projProduct/selectByProjectIdByType',
				queryParams: {
					projectId: $('#exhibitor-personnelinfo-combobox-keyword-project').combobox('getValue'),
					type:type
	            }
			});
		}
        function subSpace(str) {
            if (str === "" || str === "null" || str == null || str === undefined || str === "undefined") {
                return true
            } else {
                return false
            }
        }
        //判断datagrid是否为空--显示，超长溢出tip提示
        function formatterIsNull(val) {
            if (subSpace(val)) {
                return '\\';
            }else{
                return "<div style='width: 100%;height:30px;margin:0;line-height:30px;overflow: hidden;text-overflow:ellipsis;white-space:nowrap;' title='" + val + "'>" + val + "</div>"
            }
        }
		function loadExhibitorBoothinfoCombotreeSubpro(){
			const $subProjectTree = $('#exhibitor-boothinfo-combotree-subpro')
			$subProjectTree.combotree({
				url: eippath + '/project/selectSubProject?parentId=' + getQueryString("projectId"),
				loadFilter(data) {
          const tmp = data.data;
          if(!tmp[0].children || !tmp[0].children.length){
            $('#exhibitor-boothinfo-combotree-subpro').parent().hide()
            .prev('label').hide();
          }
          return tmp
        },
				onLoadError(e) {
					$.messager.alert('子项目加载失败！')
				},
			})
		}
	</script>