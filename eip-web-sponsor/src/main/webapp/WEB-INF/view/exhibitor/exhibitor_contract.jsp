<!--展商合同管理520-huang -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.List" %>
<%
	boolean fu_section_graph_set_yd = false, fu_section_graph_set_ydbl = false,
	fu_section_graph_set_ht = false, fu_section_graph_set_htbl = false,
	fu_section_graph_set_preorder = false, fu_section_graph_set_contract = false,
	fu_section_graph_set_payorder = false, fu_section_graph_set_booth = false,
	fu_section_graph_set_booth_type = false, fu_section_graph_set_draw = false,
	fu_booth_data_examine_center = false,fu_exhibitor_customer_mgr = false,
	fu_section_graph_set_reserve = false,fu_section_graph_set_kywy=false;
	List<String> funCodes = (List<String>)session.getAttribute("funCodes");
	for(String s : funCodes){
		if(s.equals("fu_section_graph_set_yd"))fu_section_graph_set_yd = true;
		else if(s.equals("fu_section_graph_set_ydbl"))fu_section_graph_set_ydbl = true;
		else if(s.equals("fu_section_graph_set_ht"))fu_section_graph_set_ht = true;
		else if(s.equals("fu_section_graph_set_htbl"))fu_section_graph_set_htbl = true;
		else if(s.equals("fu_section_graph_set_preorder"))fu_section_graph_set_preorder = true;
		else if(s.equals("fu_section_graph_set_contract"))fu_section_graph_set_contract = true;
		else if(s.equals("fu_section_graph_set_payorder"))fu_section_graph_set_payorder = true;
		else if(s.equals("fu_section_graph_set_booth"))fu_section_graph_set_booth = true;
		else if(s.equals("fu_section_graph_set_booth_type"))fu_section_graph_set_booth_type = true;
		else if(s.equals("fu_section_graph_set_draw"))fu_section_graph_set_draw = true;
		else if(s.equals("fu_section_graph_set_reserve"))fu_section_graph_set_reserve = true;
		else if(s.equals("fu_section_graph_set_kywy"))fu_section_graph_set_kywy = true;
		else if(s.equals("fu_booth_data_examine_center"))fu_booth_data_examine_center = true;
		else if(s.equals("fu_exhibitor_customer_mgr"))fu_exhibitor_customer_mgr = true;
	}
%>
<%@ include file="../common/project.jsp" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<script src="../../js/xlsx.core.min.js"></script>
<script src="../../js/md5.js"></script>
<style type="text/css">
	.panel-noscroll {
	    overflow: hidden;
	    overflow-y: auto;
	}
	.posf{
		position: fixed;
		width: inherit;
		line-height: 34px;
		background-color: #fff;
		z-index: 3;
		box-sizing: border-box;
		border-bottom: 1px solid #ccc;
	}
	.window{
		position: fixed !important;
	}
	.exhibitor_contract_infoes{
		overflow: hidden;
		line-height: 32px;
	}
	.exhibitor_contract_infoes>p{
		display: inline-block;
		float: left;
		font-size: 14px;
		color: #5567a5;
		margin-left: 10px;
		margin-right: 20px;
	}
	#personnelInformationShade{
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}
	.my-toolbar-search>label{
		margin-right: 10px;
	}
	.my-toolbar-search>input{
		margin-right: 15px;
	}
	.my-toolbar-search .combo{
		margin-right: 10px;
	}
</style>
<div class="easyui-layout" data-options="fit:true">
	<div data-options="region:'center',border:false">
		<!-- begin of toolbar -->
		<div id="exhibitor-contract-toolbar">
			<div class="my-toolbar-button">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_contract_fresh()"
				 plain="true">刷新</a>
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_contract_add()" plain="true">新增</a> -->
				<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-edit" onclick="exhibitor_contract_mod()"
				 plain="true">修改</a>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_contract_del_batch()"
				 plain="true">删除</a>
				<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-view" onclick="exhibitor_contract_view()"
				 plain="true">查看</a>
				<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-view" onclick="exhibitor_contract_booth_sure_batch()"
				 plain="true">批量展位确认</a>
				<a href="javascript:void(0);" class="easyui-linkbutton row-lenght" iconCls="icon-view" onclick="exhibitor_contract_generate_task_detail_batch()"
				 plain="true">批量启动服务流程</a>
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="exhibitor_contract_import()"
				 plain="true">客户会员导入</a><input type="file" onchange="importf(this)" id="choiseexcel" hidden="hidden" /> -->
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-view" onclick="exhibitor_boothNum_import()"
				   plain="true">展位导入</a><input type="file" onchange="importfBoothNum(this)" id="boothNumChoiseexcel" hidden="hidden" /> -->
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-menu3" onclick="exhibitorWindowInvoiceSetting.open()"
				   plain="true">设置展商开票流程</a> -->
			</div>
			<div class="my-toolbar-search">
				<div style="display: none">
					<label>项目名称</label>
					<select id="exhibitor-contract-combobox-keyword-project" class="easyui-combobox" style="width:175px;height:25px"></select>
					<a href="javascript:void(0);" class="easyui-linkbutton" style="width:35px;height:25px" onclick="exhibitor_popup_project($('#exhibitor-contract-combobox-keyword-project'))">...</a>&nbsp;&nbsp;&nbsp;&nbsp;
				</div>
				<div style="float: left;margin-right: 10px;">
					<label>展位类型</label>
					<select id="exhibitor_contract_combobox-booth-type" class="easyui-combobox" style="width:150px;height:25px"></select>
				</div>
				<div style="width: 220px;float: left; display: none;" id="is_fu_section_graph_set_kywy15">
					<span style="display:inline-block; width: 70px;">客服专员</span>
					<select id="sever-lintel-keyword-customerServiceId15" class="easyui-combobox"
					data-options="valueField:'id',textField:'text', panelHeight:'200px',url:'${eippath}/operator/getAll'"
					style="width:100px;height:25px"></select>
				</div>
				<div style="display: inline-block;">
					<label>公司名称</label>&nbsp;&nbsp;<input id="exhibitor-contract-keyword-contractCode" class="my-text" style="width:150px">&nbsp;&nbsp;
				</div>
				<div style="display: inline-block;">
					<label>展馆展区</label>
					<!-- <input id="exhibitor-contract-keyword-section" class="my-text" style="width:80px"> -->
					<div style="position: relative;display: inline-block;margin-right: 10px;">
						<input id="exhibitor-contract-keyword-sectionCode" class="easyui-combotree" style="width:160px;height: 26px;" data-options="valueField: 'sectionCode',textField: 'sectionName'
						,panelHeight: 'auto',panelMaxHeight:500
						,editable:true,limitToList:true"/>
						<a href="javascript:;" onclick="$('#exhibitor-contract-keyword-sectionCode').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 38px;"></a>
					</div>
				</div>
				<div style="display: inline-block;">
					<label>展位号</label>&nbsp;&nbsp;<input id="exhibitor-contract-keyword-booth-num" class="my-text" style="width:80px">&nbsp;</div>
				<div style="display: inline-block;">
					<label>业绩归属人</label>
					<select id="exhibitor-contract-combotree-belonger" class="easyui-combobox" data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'" style="width:100px;height:25px;"></select>
				</div>
				<!-- <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search()">查询</a> -->
				<script>
					$(function() {
						// 展区初始化
						$.ajax({
							url: eippath + '/exhibitSection/makeTreeJson',
							dataType: "json",	//返回数据类型
							type: "post",
							data: { exhibitCode: exhibitCode_for_pass },
							// async: false,
							success: function (data) {
								$('#exhibitor-contract-keyword-sectionCode,#exhibitor_contract_serve_booth_sectionCode').combotree({data:data});
								// $('#exhibitor-contract-keyword-sectionCode').combotree('loadData', data)
								// $('#exhibitor_contract_serve_booth_sectionCode').combotree('loadData', JSON.parse(JSON.stringify(data)));
							},
						})
					})
				</script>
				<div style="margin-top: 10px;margin-left: 27px;">
					<style>
						#exhibitor-contract-combotree-subpro+.combo{
							margin-right: 0 !important;
						}
					</style>
					<label>项目</label>
					<div style="position: relative;display: inline-block;margin-right: 10px;">
						<select id="exhibitor-contract-combotree-subpro" class="easyui-combotree" style="width:320px;height:25px;"></select>
						<a href="javascript:;" onclick="$('#exhibitor-contract-combotree-subpro').combotree('clear')" class="textbox-icon icon-close" style="width: 26px; height: 26px;position: absolute;right: 30px;"></a>
					</div>
					<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search()">查询</a>
				</div>
			</div>
		</div>
		<!-- end of toolbar -->
		<table id="exhibitor-contract-datagrid" class="easyui-datagrid" toolbar="#exhibitor-contract-toolbar" data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',
        		selectOnCheck:true,
        		onClickRow: function (rowIndex, rowData) {
        			var l = $(this).datagrid('getRows').length;
        			for(var i = 0; i < l; i++){
               			if(i != rowIndex)$(this).datagrid('unselectRow', i);
               		}
 				},
 				onDblClickRow:function (rowIndex, rowData) {
        			//exhibitor_contract_mod();
        			exhibitor_contract_view_db(rowData);
 				},">
			<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'projectName',width:120,formatter:(value,row,index) => {
							value = value || ''
							return value.length > 16 ? value.substr(0,16) + '...' : value
					}">项目</th>
					<!-- <th data-options="field:'contractCode',width:70">合同号</th> -->
					<th data-options="field:'clientId',width:60">公司编号</th>
					<th data-options="field:'clientName',width:120" formatter="exhibitor_contract_clientName">公司名称</th>
					<th data-options="field:'signDate',width:110">签约日期</th>
					<!-- <th data-options="field:'amount',width:80">合同总价</th> -->
					<th data-options="field:'boothNum',width:80">展位号</th>
					<th data-options="field:'boothTypeName',width:80">展位类型</th>
					<th data-options="field:'boothArea',width:70,align:'right'">展位面积</th>
					<th data-options="field:'openInfoName',width:80">开口情况</th>
					<th data-options="field:'personNum',width:70,align:'right'">人员个数</th>
					<th data-options="field:'operName',width:60">业绩归属人</th>
					<th data-options="field:'customerServiceName',width:60">客服专员</th>
					<th data-options="field:'userName',width:100">会员账号</th>
					<th data-options="field:'memo',width:120">备注</th>
					<th data-options="field:'do2',width:100" formatter="exhibitor_contract_formatDo2">展位</th>
					<th data-options="field:'do',width:180,align:'left'" formatter="exhibitor_contract_formatDo">服务流程</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
<!-- begin of easyui-window -->
<div id="exhibitor-contract-window" class="easyui-window" toolbar="#exhibitor-contract-window-toolbar" data-options="closed:true,title:'展商参展内容',modal:true"
 style="height:480px;width:688px;">
	<!-- begin of toolbar -->
	<div id="exhibitor-contract-window-toolbar" class="posf">
		<div class="my-toolbar-button">
			<!-- <a href="javascript:void(0);" id="exhibitor-contract-add" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_contract_add()"
			 plain="true">新增</a>
			<a href="javascript:void(0);" id="exhibitor-contract-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_contract_mod2()"
			 plain="true">修改</a>
			<a href="javascript:void(0);" id="exhibitor-contract-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_contract_del()"
			 plain="true">删除</a>-->
			<a href="javascript:void(0);" id="exhibitor-contract-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_contract_save()"
			 plain="true">保存</a>
			<a href="javascript:void(0);" id="exhibitor-contract-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_contract_cancel()"
			 plain="true">取消</a>
		</div>
	</div>
	<!-- end of toolbar -->
	<div class="easyui-layout" data-options="fit:true">
		<div data-options="region:'center'" style="margin-top: 40px;">
			<div id="exhibitor-contract-panel-2" class="easyui-panel" title="基本信息" style="width:100%;height:auto;padding:10px;margin-top: 8px;">
				<ul style="list-style:none;margin:0;padding:0;">
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;display: none;">
						<div style="width:120px;height:25px;display:inline-block;"><label>项目</label></div>
						<input id="exhibitor-contract-combotree-projectId" hint="projectId" control="combotree" class="easyui-combotree" style="width:175px;height:25px;">
					</li>
					<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;"></li> -->
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>公司编号</label></div>
						<input id="exhibitor-contract-textbox-clientId" hint="clientId" class="easyui-textbox" control="textbox" tag="1"
						 style="width:175px;height:25px">
						<!-- <a href="javascript:void(0);" class="easyui-linkbutton" style="width:45px;height:25px;" onclick="exhibitor_contract_select_company()">选取</a> -->
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>公司名称</label></div>
						<input id="exhibitor-contract-textbox-companyName" hint="clientName" class="easyui-textbox" control="textbox" tag="1"
						 style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>签约日期</label></div>
						<input hint="signDate" class="easyui-datebox" control="datebox" tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>业绩归属人</label></div>
						<input id="exhibitor-contract-combobox-operatorId" hint="ownerId" class="easyui-combobox" control="combobox" tag="0"
						 data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>客服专员</label></div>
						<input id="exhibitor-contract-combobox-customerServiceId" hint="customerServiceId" class="easyui-combobox" control="combobox" tag="0"
						 data-options="valueField:'id',textField:'text',panelHeight:'200px',url:'${eippath}/operator/getAll'" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>对方联系人</label></div>
						<input id="exhibitor-contract-combobox-linkman" hint="linkman" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>电话</label></div>
						<input id="exhibitor-contract-combobox-linkmanTel" hint="linkmanTel" class="easyui-textbox" control="textbox" tag="0"
						 style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>手机</label></div>
						<input id="exhibitor-contract-combobox-linkmanMobile" hint="linkmanMobile" class="easyui-textbox" control="textbox"
						 tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>邮箱</label></div>
						<input id="exhibitor-contract-combobox-linkmanEmail" hint="linkmanEmail" class="easyui-textbox" control="textbox"
						 tag="0" style="width:175px;height:25px">
					</li>
					<!-- <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>传真</label></div>
						<input id="exhibitor-contract-combobox-linkmanFax" hint="linkmanFax" class="easyui-textbox" control="textbox" tag="0"
						 style="width:175px;height:25px">
					</li> -->
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>网址</label></div>
						<input id="exhibitor-contract-combobox-linkmanWebsite" hint="linkmanWebsite" class="easyui-textbox" control="textbox"
						 tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>QQ</label></div>
						<input id="exhibitor-contract-combobox-linkmanQQ" hint="linkmanQQ" class="easyui-textbox" control="textbox" tag="0"
						 style="width:175px;height:25px">
					</li>
					<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>微信</label></div>
						<input id="exhibitor-contract-combobox-linkmanWechat" hint="linkmanWechat" class="easyui-textbox" control="textbox"
						 tag="0" style="width:175px;height:25px">
					</li>
					<li style="width:640px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>邮寄地址</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
						<input id="exhibitor-contract-combobox-deliveryAddress" hint="deliveryAddress" class="easyui-textbox"
							   control="textbox" tag="0" style="width:490px;height:25px">
					</li>
					<li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>备注</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
						<input id="exhibitor-contract-combobox-memo" hint="memo" class="easyui-textbox" data-options="multiline:true"
						 control="textbox" tag="0" style="width:490px;height:100%">
					</li>
					<!-- <li style="width:640px;height:55px;float:left;margin-right:15px;margin-bottom:5px;">
						<div style="width:120px;height:25px;display:inline-block;"><label>参展范围</label>&nbsp;&nbsp;&nbsp;&nbsp;</div>
						<input id="exhibitor-contract-textbox-exhibitProductRange" hint="exhibitProductRange" class="easyui-textbox" data-options="multiline:true"
						 control="textbox" tag="0" style="width:490px;height:100%">
					</li> -->
				</ul>
			</div>
		</div>
	</div>
</div>

<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-contract-window-select-prod" class="easyui-window" data-options="closed:true,title:'选取产品',modal:true"
 style="width:872px;">
	<div id="exhibitor-contract-panel-select-prod" class="easyui-panel" style="width:800px;height:500px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'">
				<table id="exhibitor-contract-datagrid-select-prod" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:false,multiSort:true,fitColumns:true,fit:true,method:'post'">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'prodCode',width:20">产品代码</th>
							<th data-options="field:'prodName',width:40">产品名称</th>
							<th data-options="field:'prodPrice',width:30">项目产品单价</th>
							<th data-options="field:'lowDiscount',width:30">最低折扣%</th>
							<th data-options="field:'lowPrice',width:30">最低价</th>
							<th data-options="field:'curencyCode',width:30">币种</th>
						</tr>
					</thead>
				</table>
			</div>
			<div data-options="region:'south'" style="height:57px;padding:12px;">
				<div class="my-toolbar-search">
					<div style="float:right;">
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_ok_select_prod()" plain="false"
						 style="width:60px;height:25px">确定</a>
						&nbsp;
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_return_select_prod()" plain="false"
						 style="width:60px;height:25px">返回</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-contract-window-select-company" class="easyui-window" data-options="closed:true,title:'选取公司',modal:true"
 style="width:572px;">
	<div >
		<div class="my-toolbar-search">
			<label>关键字查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-contract-keyword-select-company" class="my-text"
			 style="width:150px">&nbsp;
			<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search_select_company()">查询</a>
			&nbsp;&nbsp;<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_ok_select_company()" plain="false"
				 style="width:60px;height:25px">确定</a>
				&nbsp;
				<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_return_select_company()"
				 plain="false" style="width:60px;height:25px">返回</a>
		</div>
	</div>
	<div id="exhibitor-contract-panel-select-company" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">

			<div data-options="region:'center'">
				<table id="exhibitor-contract-datagrid-select-company" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'clientId',width:20">公司编号</th>
							<th data-options="field:'companyName',width:40">公司名称</th>
						</tr>
					</thead>
				</table>
			</div>

		</div>
	</div>
</div>
<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-contract-window-select-booth" class="easyui-window" data-options="closed:true,title:'选取展位',modal:true"
 style="width:572px;">
	<div id="exhibitor-contract-panel-select-booth" class="easyui-panel" style="width:558px;height:284px;">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'">
				<table id="exhibitor-contract-datagrid-select-booth" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'">
					<thead>
						<tr>
							<th data-options="field:'ck',checkbox:true"></th>
							<th data-options="field:'boothCode',width:20">展位号</th>
							<th data-options="field:'typeName',width:40">展位类型</th>
							<th data-options="field:'boothSpec',width:30">展位规格</th>
						</tr>
					</thead>
				</table>
			</div>
			<div data-options="region:'south'" style="height:57px;padding:12px;">
				<div class="my-toolbar-search">
					<label>展位号查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-contract-keyword-select-booth" class="my-text"
					 style="width:150px">&nbsp;
					<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search_select_booth()">查询</a>
					<div style="float:right;">
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_ok_select_booth()" plain="false"
						 style="width:60px;height:25px">确定</a>
						&nbsp;
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_return_select_booth()" plain="false"
						 style="width:60px;height:25px">返回</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- end of easyui-window -->
<!-- begin of easyui-window -->
<div id="exhibitor-contract-window-select-section-booth" class="easyui-window" data-options="closed:true,title:'选取展位',modal:true,border:false"
	 style="width:75%;height:75%;">
	<div id="exhibitor-contract-panel-select-section-booth" class="easyui-panel" data-options="fit:true">
		<div class="easyui-layout" data-options="fit:true">
			<div data-options="region:'center'">
				<table id="exhibitor-contract-datagrid-section-booth" class="easyui-datagrid" data-options="rownumbers:true,
					singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',
					onDblClickRow:function (rowIndex, rowData) {
        				exhibitor_contract_ok_select_section_booth2([rowData]);
 					}
					">
					<thead>
					<tr>
						<th data-options="field:'ck',checkbox:true"></th>
						<th data-options="field:'boothCode',width:20">展位号</th>
						<th data-options="field:'typeName',width:40">展位类型</th>
						<th data-options="field:'boothSpec',width:30">展位规格</th>
					</tr>
					</thead>
				</table>
			</div>
			<div data-options="region:'north'" style="height:57px;padding:12px;">
				<div class="my-toolbar-search">
					<label>展位号查询</label>&nbsp;&nbsp;&nbsp;&nbsp;<input id="exhibitor-contract-keyword-select-section-booth" class="my-text"
																	   style="width:150px">&nbsp;
					<label>展位类型查询</label>&nbsp;&nbsp;&nbsp;&nbsp;
					<select id="exhibitor-contract-keyword-booth-type" class="easyui-combobox" style="width:175px;height:25px"></select>&nbsp;
					<a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="exhibitor_contract_search_select_section_booth()">查询</a>
					<div style="float:right;">
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_ok_select_section_booth()" plain="false"
						   style="width:60px;height:25px">确定</a>
						&nbsp;
						<a href="javascript:void(0);" class="easyui-linkbutton" onclick="exhibitor_contract_return_select_section_booth()" plain="false"
						   style="width:60px;height:25px">返回</a>
					</div>
				</div>
			</div>
			<div data-options="region:'west',split:true" style="width:230px;padding:12px;" >
				<ul id="exhibitor-contract-boothdata-tree" class="easyui-tree" data-options="method:'post',animate:true"></ul>

			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
    $("#exhibitor-contract-keyword-select-section-booth").keypress(function (e) {
        if (e.which == 13) {
            exhibitor_contract_search_select_section_booth();
        }
    })
</script>
<!-- end of easyui-window -->
<!-- begin of easyui-window确认展位 -->
<div id="exhibitor-contract-window-serve-booth" class="easyui-window" toolbar="#exhibitor-contract-window-serve-booth-toolbar"
 data-options="closed:true,title:'确认参展需求',modal:true,border:false,fit:false" style="height:600px;width:860px;overflow: auto;">
    <div id="cc" class="easyui-accordion" data-options="collapsed:false,collapsible:false">
        <!-- begin of toolbar -->
        <div id="exhibitor-contract-window-serve-booth-toolbar"  data-options="collapsed:false,collapsible:false" style="position: fixed;z-index: 1;">
        	<div class="my-toolbar-button">
        		<!-- <a href="javascript:void(0);" id="exhibitor-contract-add" class="easyui-linkbutton" iconCls="icon-add" onclick="exhibitor_contract_add()" plain="true">新增</a>
                <a href="javascript:void(0);" id="exhibitor-contract-mod2" class="easyui-linkbutton" iconCls="icon-edit" onclick="exhibitor_contract_mod2()" plain="true">修改</a>
                <a href="javascript:void(0);" id="exhibitor-contract-del" class="easyui-linkbutton" iconCls="icon-remove" onclick="exhibitor_contract_del()" plain="true">删除</a> -->
        		<a href="javascript:void(0);" id="exhibitor-contract-save" class="easyui-linkbutton" iconCls="icon-save" onclick="exhibitor_contract_serve_booth_save()" plain="true">确定</a>
        		<a href="javascript:void(0);" id="exhibitor-contract-cancel" class="easyui-linkbutton" iconCls="icon-cancel" onclick="exhibitor_contract_close_serve_booth()" plain="true">取消</a>
        	</div>
        </div>
        <!-- end of toolbar -->
		<div id="exhibitor-contract-serve-booth-panel-3" style="padding: 0px 10px;height: auto;margin-top: 38px;" data-options="collapsed:false,collapsible:false">
			<ul style="list-style:none;margin:0;padding:0;overflow: hidden;">
				<li style="width:110px;height:25px;line-height: 25px;float:left;margin-right:15px;">
					<div style="width:80px;display:inline-block;">特殊身份<label></label></div>
				</li>
				<li style="width:400px;height:25px;line-height: 25px;float:left;margin-right:15px;">
					<div style="width:300px;display:inline-block;">
						<label>领队&nbsp;</label>
						<input  id="exhibitor_serveBooth_identity_1"
								type="checkbox" class="my-checkbox" style="vertical-align: middle;" control="checkbox" tag="0" />
						&nbsp;&nbsp;&nbsp;
						<label>无展位只考察&nbsp;</label>
						<input  id="exhibitor_serveBooth_identity_2"
								type="checkbox" class="my-checkbox" style="vertical-align: middle;" control="checkbox" tag="0"  />
					</div>
				</li>
			</ul>
		</div>
		<!-- 展位信息开始 -->
		<div id="exhibitor-contract-serve-booth-panel-1" data-options="collapsed:false,collapsible:false" title="展位信息">
			<ul style="list-style:none;margin:0;box-sizing: border-box;padding: 10px 15px;overflow: hidden;">
			<li style="width:100%;float:left;margin-right:15px;margin-bottom: 5px;">
				<div style="width:120px;height:25px;display:inline-block;">展位号<label></label></div>
				<input hint="f_booth_num" id="exhibitor_contract_serve_booth_num" prompt="手工输入时需敲回车确定展位" class="easyui-tagbox" control="tagbox" tag="0"  style="width: 580px;">
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-add" plain="true"  onclick="exhibitor_contract_select_section_booth();"></a>
			</li>
			<li style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;"><label>展位类型</label></div>
				<select id="exhibitor_contract_serve_booth_type" class="easyui-combobox" style="width:175px;height:25px" data-options="onSelect: function(dataNew,dataOld){
					if (exhibitor_contract_form_showing != true){
						exhibitor_contract_booth_type_code = $('#exhibitor_contract_serve_booth_type').combobox('getValue');
						console.log(dataNew)
						$.ajax({
							url: eippath + '/boothType/selectByTypeCode',
							dataType: 'json',	//返回数据类型
							type: 'post',
							data: {typeCode: dataNew.typeCode},	//发送数据
							async: false,
							success: function(data){
								var isTexeNull = $('#exhibitor_contract_serve_booth_num').tagbox('getValues').length;
								$('#exhibitor_contract_serve_booth_spec').textbox('setValue', data.boothSpec || '');
								$('#exhibitor_contract_serve_cert_num').textbox('setValue', data.certNum || '');
								$('#exhibitor_contract_serve_invite_num').textbox('setValue', data.inviteNum || '');
							},
							error: function(){
								$.messager.alert('提示','数据发送失败！','error');
							}
						});
					}
				}"></select>
			</li>
			<li style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">展馆展区<label></label></div>
				<!-- <input hint="f_section" id="exhibitor_contract_serve_booth_section" class="easyui-textbox" control="textbox" tag="0"
					   readonly style="width:175px;height:25px"> -->
				<input id="exhibitor_contract_serve_booth_sectionCode" hint="f_section"
						class="easyui-combotree"  style="width:175px;height:25px"
						data-options="valueField: 'sectionCode',textField: 'sectionName',panelHeight: 'auto'
						,panelMaxHeight:500,editable:true,limitToList:true">
			</li>
			<li style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">展位规格<label></label></div>
				<input hint="f_booth_spec" id="exhibitor_contract_serve_booth_spec" class="easyui-textbox" control="textbox" tag="0" data-options="prompt:'如 3*12'"
					   style="width:175px;height:25px">
			</li>
			<li style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">展位面积<label></label></div>
				<input hint="f_booth_area" id="exhibitor_contract_serve_booth_area" class="easyui-textbox" control="textbox" tag="0"
					   style="width:175px;height:25px">
			</li>
			<li id="exhibitor_contract_serve_cert_num_li" style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">进馆证数量<label></label></div>
				<input hint="f_cert_num" id="exhibitor_contract_serve_cert_num"  class="easyui-numberbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li id="exhibitor_contract_serve_invite_num_li" style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">邀请函数量<label></label></div>
				<input hint="f_invite_num" id="exhibitor_contract_serve_invite_num"  class="easyui-numberbox" control="textbox" tag="0" style="width:175px;height:25px">
			</li>
			<li id="exhibitor_contract_serve_booth_count_li" style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">展位数量<label></label></div>
				<input hint="" id="exhibitor_contract_serve_booth_count"  class="easyui-numberbox" control="textbox" tag="0" data-options="disabled:true" style="width:175px;height:25px">
			</li>
			<li style="width:300px;float:left;margin-right:15px;margin-bottom:5px;">
				<div style="width:120px;height:25px;display:inline-block;">开口情况<label></label></div>
				<input hint="openInfoId" id="exhibitor_contract_serve_openInfoId"  class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px">
			</li>
			<li style="width:100%;float:left;margin-right:15px;margin-bottom: 5px;">
				<div style="width:120px;height:25px;display:inline-block;">参展范围<label></label></div>
				<input hint="exhibitProductRange" id="exhibitor_contract_serve_exhibitProductRange" class="easyui-textbox" control="textbox" tag="0"  style="width: 580px;height:25px">
			</li>
		</ul>
		</div>
		<!-- 展位信息结束 -->
		<!-- <div data-options="collapsed:false,collapsible:false" title="展位费用" style="height: 260px;box-sizing: border-box;padding: 8px;" id="exhibitor-contract-serve-booth-panel-2">
			<table id="exhibitor-BoothPrices-datagrid" class="easyui-datagrid" data-options="rownumbers:true,singleSelect:true,multiSort:true,fitColumns:true,fit:true,selectOnCheck:true,">
				<thead>
				<tr>
					<th data-options="field:'prodName',width:60,align:'center'">产品名称</th>
					<th data-options="field:'prodSpec',width:60,align:'center',editor:'textbox'">产品规格</th>
					<th data-options="field:'price',width:60,align:'center',editor:{type:'numberbox',options:{precision:2}}">单价</th>
					<th data-options="field:'curencyName',width:60,align:'center'">币种</th>
					<th data-options="field:'qty',width:60,align:'center',editor:{type:'numberbox'}">数量</th>
					<th data-options="field:'amount',width:60,align:'center',editor:{type:'numberbox',options:{precision:2}}">金额</th>
				</tr>
				</thead>
			</table>
		</div> -->
		<div id="personnelInformation" data-options="collapsed:false,collapsible:false" style="position: relative;" title ="人员信息 (请直接在表格内填写人数)">
				<div data-options="border:false" style="overflow: hidden;box-sizing: border-box; height:auto;padding:10px 10px 0;">
					<ul style="list-style:none;margin:0;padding:0;">
						<li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
							<div style="width:120px;height:25px;display:inline-block;">人员数量<label></label></div>
							<input  id = "exhibitor_contract_serve_person_num"  readonly="readonly" style="width:100px;height:25px;border: 0px;">
						</li>
					</ul>
				</div>
				<div data-options="border:false" style="padding:10px;height: 180px;">
					<table id="exhibitor-serveBoothDtl-datagrid" class="easyui-datagrid"
						   data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:false,multiSort:true,fitColumns:true,fit:true,method:'post',selectOnCheck:true,
					onClickRow: function (rowIndex, rowData) {
						exhibitor_serveBoothDtl_datagrid_onClickRow(rowIndex);
						var l = $(this).datagrid('getRows').length;
						for(var i = 0; i < l; i++){
							if(i != rowIndex)$(this).datagrid('unselectRow', i);
						}
					},
					onDblClickRow:function (rowIndex, rowData) {

					},
					">
						<thead>
						<tr>
							<th data-options="field:'tripName',width:30,align:'center'">行程名称</th>
							<th data-options="field:'qty',width:30,align:'center',editor:{type:'numberbox',options:{
							onChange:function(newValue,oldValue){
								exhibitor_serveBoothDtl_format_qty_change(newValue,oldValue)
							}
							}}"
								formatter="exhibitor_serveBoothDtl_format_qty">人员数量</th>
							<th data-options="field:'tripDetail',width:60,align:'center',">行程描述</th>
						</tr>
						</thead>
					</table>
				</div>
		    <div id="personnelInformationShade"></div>
		</div>
    </div>
</div>

<script type="text/javascript">
	var exhibitor_contract_edit_mode;
	var exhibitor_contract_button_edit_mode;
	var exhibitor_contract_alocate_id = -1;
	var exhibitor_contract_window_close_set = false;
	var exhibitor_contract_save_id;
	var exhibitor_contract_projCltId;
	var exhibitor_contract_form_showing = true;
    var openInfoIds;
	var boothArea = 0; //展位总面积
	var allBoothAata; //所有展位号数据
	var isOne = 0 //阻止第一次执行下拉事件
	var jurisdictions15 = <%=fu_section_graph_set_kywy%> ;
	var fu_booth_data_examine_center = <%=fu_booth_data_examine_center%> ;
	var fu_exhibitor_customer_mgr = <%=fu_exhibitor_customer_mgr%> ;
	function isSearchTypes2(){
		if (isSearchType2) {
			isSearchType2 = false
			$("#exhibitor-contract-keyword-contractCode").val(cpName2)
			cpPid2 && $("#exhibitor-contract-combotree-subpro").combotree('setValue',cpPid2);
			exhibitor_contract_search()
		}
	}
	$(function() {
		var cnt = 0;
        $('#exhibitor-contract-combobox-keyword-project').combobox({
            valueField: 'id',
            textField: 'text',
            panelHeight: '200px',
			data: [{'id':project_for_pass,'text':exihibit_name_for_pass}],
            onLoadSuccess: function (data) {
                $(this).combobox('select', project_for_pass);
                if (cnt === 0) {
                   exhibitor_contract_fresh();
                    cnt++;
                }
            },
        });

		if(jurisdictions15){
			$("#is_fu_section_graph_set_kywy15").show()
		}else{
			$("#is_fu_section_graph_set_kywy15").hide()
		}

        cnt = 0;
        $('#exhibitor_contract_combobox-booth-type').combobox({
            valueField: 'typeCode',
            textField: 'typeName',
            panelHeight: '200px',
			async:false,
            url: eippath + '/boothType/selectByProjectId',
            queryParams: {
                f_project_id: project_for_pass
            },
            onLoadSuccess: function(data) {

                if (cnt === 0) {
                    cnt++;
                } else if (cnt > 0) {
                    exhibitor_contract_form_showing = false;
                    cnt++;
                }
            },
        });
		$('#exhibitor_contract_serve_openInfoId').combobox({
          valueField: 'id',
          textField: 'text',
          panelHeight: 'auto',
			async:false,
            url: eippath + '/itemCode/selectByItemKindCodeElse',
            queryParams: {
                itemKindCode: 'open_info',
				projectId:getQueryString("projectId")
            },
			onSelect: function(data){
				console.log(data)
				if(isOne == 1){
					if(BoothPricesDatagridData.length!= 0){
						if(data.id == 1){
						BoothPricesDatagridData[0].amount = BoothPricesDatagridData[0].price*0
						BoothPricesDatagridData[0].qty = 0
						}else if(data.id == 2){
						BoothPricesDatagridData[0].amount = BoothPricesDatagridData[0].price*1
						BoothPricesDatagridData[0].qty = 1
						}else if(data.id == 3){
						BoothPricesDatagridData[0].amount = BoothPricesDatagridData[0].price*2
						BoothPricesDatagridData[0].qty = 2
						}else if(data.id == 4){
						BoothPricesDatagridData[0].amount = BoothPricesDatagridData[0].price*3
						BoothPricesDatagridData[0].qty = 3
						}
						// $('#exhibitor-BoothPrices-datagrid').datagrid({
						// 	data: BoothPricesDatagridData,
						// 	onLoadSuccess:function(data){
						// 		$('#exhibitor-BoothPrices-datagrid').datagrid('resize');
						// 	}
						// });
					}
				}
			}
        });
		loadExhibitorContractCombotreeSubpro()
	});



    function exhibitor_contract_boothdata_tree_fresh(){
        $('#exhibitor-contract-boothdata-tree').tree({
            url: eippath + '/exhibitSection/makeTreeJson?exhibitCode=' + exhibitCode_for_pass,
            onClick: function(node){
                $('#exhibitor-contract-datagrid-section-booth').datagrid({
                    url: eippath + '/exhibitBooth/getListBySectionCode1?sectionCode=' + node.sectionCode +'&exhibitCode='+ exhibitCode_for_pass + '&projectId='+getQueryString("projectId")
                });
            },
			onLoadSuccess:function(node, data){
				allBoothAata = data
			}
        });
    };
	$("#exhibitor-contract-keyword-contractCode").keypress(function(e) {
		if (e.which == 13) {
			exhibitor_contract_search();
		}
	});
	$("#exhibitor-contract-keyword-select-company").keypress(function(e) {
		if (e.which == 13) {
			exhibitor_contract_search_select_company();
		}
	});
	function getQueryString(name) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
		var url=decodeURI(decodeURI(window.location.search))
		var r = url.substr(1).match(reg);
		if (r != null) return unescape(r[2]); return null;

	}

	function exhibitor_contract_fresh() {
		$('#exhibitor-contract-datagrid').datagrid({
			url: eippath + '/serveBooth/getList?key=0',
			queryParams: {
				projectId: project_for_pass,
				exhibitCode: exhibitCode_for_pass,
				contractCode: $('#exhibitor-contract-keyword-contractCode').val(),
				serveBoothId: getQueryString("recordCode")
      },
			onLoadSuccess: function(data) {
				if (exhibitor_contract_alocate_id == -1) $(this).datagrid('selectRow', -1);
				else {
					var rows = $(this).datagrid("getRows");
					for (var i = 0; i < rows.length; i++) {
						if (rows[i].contractId == exhibitor_contract_alocate_id) {
							$(this).datagrid('selectRow', i);
							exhibitor_contract_alocate_id = -1;
							break;
						}
					}
				}
				isSearchTypes2()
			}
		});
	}
    // 点击查询
	function exhibitor_contract_search() {
		// if ($('#exhibitor-contract-combobox-keyword-project').combobox('getValue') == '') {
		// 	alert("请选择项目名称");
		// 	return;
		// }

		// let  belongerVal= $('#exhibitor-contract-combotree-belonger').combobox('getValue')
		// console.log('查询',belongerVal)

		$('#exhibitor-contract-datagrid').datagrid({
			url: eippath + '/serveBooth/getList?key=1',
			queryParams: {
				projectId: project_for_pass,
				owner_id:$('#exhibitor-contract-combotree-belonger').combobox('getValue'),
				contractCode: $('#exhibitor-contract-keyword-contractCode').val(),
				sectionCode: $('#exhibitor-contract-keyword-sectionCode').combotree('getValue'),
                // section: $('#exhibitor-contract-keyword-section').val(),
                boothNum: $('#exhibitor-contract-keyword-booth-num').val(),
				customerServiceId:$('#sever-lintel-keyword-customerServiceId15').combobox('getValue'),
                boothTypeCode: $('#exhibitor_contract_combobox-booth-type').combobox('getValue'),
				childProjectId: $('#exhibitor-contract-combotree-subpro').combotree('getValue'),
				exhibitCode: exhibitCode_for_pass,
			}
		});
	}

	function exhibitor_contract_formatIsPayment(val, row) {
		if (val == false || val == null) return '否';
		else return '是';
	}
	function exhibitor_contract_clientName(val, row){
		if(row.clientName != null && row.clientName!=""){
			return fu_exhibitor_customer_mgr ? ('<a href="javascript:void(0);" style="color:blue;" onclick="exhibitor_contract_open_client_management(\''+ row.clientName +'\',true,' + row.projectId + ')">'+ row.clientName +'</a>') : row.clientName;
		}
	}

	function exhibitor_contract_formatDo2(val, row) {
		//console.log('格式显示是否确认参展');
		var returnString;
		if (!row.f_is_serve) {
			if (!row.boothConfirmed) {
				returnString =
					'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;" onclick="exhibitor_contract_open_serve_booth(' +
					row.serveBoothId +
					',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
					');">展位确认</a>';
				return returnString; //  += '&nbsp;启动服务流程';
			} else {
				returnString =
					'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;opacity:0.6;" onclick="exhibitor_contract_open_serve_booth(' +
					row.serveBoothId +
					',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
					');">再次确认</a>';
				return returnString; //  +=
					// '&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_contract_generate_task_detail(' +
					// row.serveBoothId + ');">启动服务流程</a>';
			}
		} else {
			returnString =
				'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;opacity:0.6;" onclick="exhibitor_contract_open_serve_booth(' +
				row.serveBoothId +
				',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
				');">再次确认</a>';
			return returnString; // +=
				// '&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_contract_generate_task_detail(' +
				// row.serveBoothId + ');">重新启动服务流程</a>';
		}
	}
	function exhibitor_contract_formatDo(val, row) {
		//console.log('格式显示是否开启流程');
		var returnString = '';
		if (row.f_is_serve == false || row.f_is_serve == null) {
			if (row.boothConfirmed == null ||row.boothConfirmed == false) {
				// returnString =
				// 	'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;" onclick="exhibitor_contract_open_serve_booth(' +
				// 	row.serveBoothId +
				// 	',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
				// 	');">确认参展需求</a>';
				// return returnString += '&nbsp;启动服务';
				return returnString; //  += '&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;display:inline-block;" onclick="exhibitor_contract_open_examine_center(' + row.serveBoothId + ',' + row.projectId + ',' + row.clientId + ',\'' + row.clientName + '\');">查看服务状态</a>';
			} else {
				// returnString =
				// 	'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;opacity:0.6;" onclick="exhibitor_contract_open_serve_booth(' +
				// 	row.serveBoothId +
				// 	',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
				// 	');">重新确认参展需求</a>';
				return returnString +=
					'&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="exhibitor_contract_generate_task_detail(' +
					row.serveBoothId + ');">启动服务</a>';
					//  + '&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;" onclick="exhibitor_contract_open_examine_center(' +
					// row.serveBoothId + ',' + row.projectId + ',' + row.clientId + ',\'' + row.clientName + '\');">查看服务状态</a>';
			}
		} else {
			// returnString =
			// 	'<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;opacity:0.6;" onclick="exhibitor_contract_open_serve_booth(' +
			// 	row.serveBoothId +
			// 	',' + row.projectId + ',' + row.clientId + ',' + row.projCltId + ',' + row.ownerId +
			// 	');">重新确认参展需求</a>';
			return returnString +=
				'&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;opacity:0.6;" onclick="exhibitor_contract_generate_task_detail(' +
				row.serveBoothId + ');">重启服务</a>'
				+  (fu_booth_data_examine_center ? ('&nbsp;<a href="javascript:void(0);" style="color:blue;font-weight:bold;width:9em;margin-left:1em;display:inline-block;" onclick="exhibitor_contract_open_examine_center(' +
					row.serveBoothId + ',' + row.projectId + ',' + row.clientId + ',\'' + row.clientName + '\');">查看服务状态</a>') : '');
		}
	}

	function exhibitor_contract_payment(contractId) {
		var vContractId = contractId;
		$.messager.confirm('提示', '确定启动服务流程，生成该展商对应的一系列任务类型吗？', function(r) {
			if (r) {
				$.ajax({
					url: eippath + '/contract/payment',
					dataType: "json", //返回数据类型
					type: "post",
					data: {
						contractId: vContractId
					}, //发送数据
					async: true,
					success: function(data) {
						if (data.state == 1) {
							$.messager.alert('提示', '到账成功！', 'info');
							exhibitor_contract_search();
						} else {
							$.messager.alert('提示', '生成失败！', 'error');
						}
					},
					error: function() {
						$.messager.alert('提示', '数据发送失败！', 'error');
					}
				});
			}
		});
	}

	function exhibitor_contract_booth_sure_batch () {
		console.log('批量展位确认开始');
		var rows = $('#exhibitor-contract-datagrid').datagrid('getSelections');
		if (rows.length > 0) {
			// var postData = [];
			// var flag = '';
			// for (var i = 0; i < rows.length; i++) {
			// 	if(rows[i].boothConfirmed == null ||rows[i].boothConfirmed == false){
			// 		flag = rows[i].clientId;
			// 		break;
			// 	}
			// 	postData[i] = {
			// 		contractId: rows[i].serveBoothId
			// 	};
			// }
			// if(flag!=''){
			// 	$.messager.alert('提示', '编号为'+flag+'的公司未确认展位，请先确认', 'warning');
			// 	return;
			// }
			let msg = '展位确认'
			$.messager.confirm('提示', '当前选择的记录数为：' + rows.length + '，确定进行'+msg+'吗？', function(r) {
				if (r) {
					$.ajax({
						url: eippath + '/serveBooth/batchConfirmBooth',
						dataType: "json", //返回数据类型
						type: "post",
						// contentType: "application/json",
						// data: JSON.stringify(postData), //发送数据
						data: {
							serveBoothIds: rows.map(it=> it.serveBoothId).join(),
							exhibitCode: exhibitCode_for_pass,
						},
						async: true,
						success: function(data) {
							if (data.state == 1) {
								if(data.data) {
									const tmpSum = data.data || {};
									const tmpFail = (+tmpSum.existConfirmNum || 0) + (+tmpSum.failNum || 0);
									// existConfirmNum failNum successNum
									msg = '成功为'+ tmpSum.successNum +'条参展记录批量确认展位。' +
											(tmpFail ? ('未处理参展记录 '+ tmpFail +' 条（失败'+ (+tmpSum.failNum ||0) + '，已确认展位'+(+tmpSum.existConfirmNum ||0)+'）') : '');
									$.messager.alert('提示', msg, 'warning').window({width:380});
								} else {
									$.messager.alert('提示', msg+ '成功！', 'info')
								}
								exhibitor_contract_search();
								//exhibitor_contract_fresh();
							} else {
								$.messager.alert('提示', msg + '失败！', 'error');
							}
						},
						error: function() {
							$.messager.alert('提示', '数据发送失败！', 'error');
						}
					});
				}
			});
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}
	function exhibitor_contract_generate_task_detail_batch() {
		console.log('批量启动服务流程开始');
		var rows = $('#exhibitor-contract-datagrid').datagrid('getSelections');
		if (rows.length > 0) {
			var postData = [];
			var flag = '';
			for (var i = 0; i < rows.length; i++) {
				if(rows[i].boothConfirmed == null ||rows[i].boothConfirmed == false){
					flag = rows[i].clientId;
					break;
				}
				postData[i] = {
					contractId: rows[i].serveBoothId
				};
			}
			if(flag!=''){
				$.messager.alert('提示', '编号为'+flag+'的公司未确认展位，请先确认', 'warning');
				return;
			}
			$.messager.confirm('提示', '当前选择的记录数为：' + rows.length + '，确定开启服务流程吗？', function(r) {
				if (r) {
					$.ajax({
						url: eippath + '/contract/generateTaskDetailBatch',
						dataType: "json", //返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData), //发送数据
						async: true,
						success: function(data) {
							if (data.state == 1) {
								if(data.data) {
									$.messager.alert('提示','启动服务流程成功 ' + (+data.data.successNum || 0) + ' 个,<br>失败 '+ (+data.data.failNum || 0)+' 个','warning').window({width: 380});
								} else {
									$.messager.alert('提示', '启动服务流程成功！', 'info');
								}
								exhibitor_contract_search();
								//exhibitor_contract_fresh();
							} else {
								$.messager.alert('提示', data.message || data.msg || '启动服务流程失败！', 'error');
							}
						},
						error: function() {
							$.messager.alert('提示', '数据发送失败！', 'error');
						}
					});
				}
			});
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function exhibitor_contract_generate_task_detail(contractId) {
		//alert(contractId);
		console.log('开始启动服务流程' + contractId);
		var vContractId = contractId;
		$.messager.confirm('提示', '确认参展需求成功！ 确定启动服务流程，生成该展商对应的一系列任务类型吗？', function(r) {
			if (r) {
				$.ajax({
					url: eippath + '/contract/generateTaskDetail',
					dataType: "json", //返回数据类型
					type: "post",
					data: {
						contractId: vContractId
					}, //发送数据
					async: true,
					success: function(data) {
						if (data.state == 1) {
							$.messager.alert('提示', '启动该记录的服务流程成功！', 'info');
							exhibitor_contract_search();
						} else if (data.state == 2) {
							$.messager.alert('提示', '该记录的服务流程已被启动过！', 'info');
							exhibitor_contract_search();
						}else if (data.state == 3) {
							$.messager.alert('提示', '启动失败，请先设置项目服务任务！', 'info');
							exhibitor_contract_search();
						} else {
							$.messager.alert('提示', '启动该记录的服务流程失败！', 'error');
						}
					},
					error: function() {
						$.messager.alert('提示', '数据发送失败！', 'error');
					}
				});
			}
		});
	}

	function exhibitor_contract_window_close() {
		$('#exhibitor-contract-window').window({
			onBeforeClose: function() {
				if (exhibitor_contract_button_edit_mode == 1) {
					$.messager.confirm('提示', '正在编辑状态未保存，确定要退出吗？', function(r) {
						if (r) {
							$('#exhibitor-contract-window').window('close', true); //这里调用close方法，true表示面板被关闭的时候忽略onBeforeClose回调函数。
						}
					});
					return false;
				}
			},
			onClose: function() {
				exhibitor_contract_search();
			}
		});
	}

	function exhibitor_contract_add() {
		if (!exhibitor_contract_window_close_set) {
			exhibitor_contract_window_close();
			exhibitor_contract_window_close_set = true;
		}
		$.ajax({
			url: eippath + '/serveBooth/getSysId',
			dataType: "json", //返回数据类型
			type: "post",
			contentType: "application/json",
			data: {}, //发送数据
			async: true,
			success: function(data) {
				exhibitor_contract_alocate_id = -1;
				emptyLinkman()
				$('#exhibitor-contract-window').window('open');
				exhibitor_contract_save_id = data.id;
				exhibitor_contract_setEditInfoClear();
				$('#exhibitor-contract-combobox-operatorId').combobox('setValue',"")
				$('#exhibitor-contract-datagrid-prod').datagrid({
					url: eippath + '/data/clear_datagrid.json'
				});
				exhibitor_contract_edit_mode = "Add";
				exhibitor_contract_setEditInfoReadOnly(1);
				exhibitor_contract_setButtonEditMode(1);

				$("#exhibitor-contract-combotree-projectId").combotree('setValue',project_for_pass);
				$("#exhibitor-contract-combobox-operatorId").combobox('setValue',${operator.operatorId})
				$("#exhibitor-contract-combobox-customerServiceId").combobox('setValue',${operator.operatorId})
				if(jurisdictions15){
					$($("#exhibitor-contract-combobox-operatorId").parent("li").find("input")[0]).combobox('enable')
					$($("#exhibitor-contract-combobox-customerServiceId").parent("li").find("input")[0]).combobox('enable')
				}else{
					$($("#exhibitor-contract-combobox-operatorId").parent("li").find("input")[0]).combobox('disable')
					$($("#exhibitor-contract-combobox-customerServiceId").parent("li").find("input")[0]).combobox('disable')
				}
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}
	//清空下拉框
	function emptyLinkman(){
		$('#exhibitor-contract-combobox-linkman').combobox({
			valueField: 'personName',
			textField: 'personName',
			panelHeight: '200px',
			data: []
		});
	}
	//清空下拉框
	function setValLinkman(parentClientId){
		$.ajax({
			url: eippath + '/client/selectLinkman',
			dataType: "json", //返回数据类型
			type: "post",
			async: false,
			data: {
				parentClientId: parentClientId,
				clientTypeCode:'C_LINKMAN'
			}, //发送数据
			success: function(data) {
				console.log(data)
				$('#exhibitor-contract-combobox-linkman').combobox({
					valueField: 'personName',
					textField: 'personName',
					panelHeight: '200px',
					data: data.result,
					onSelect:function(record){
						$("#exhibitor-contract-combobox-linkmanTel").textbox("setValue",record.tel)
						if(record.mobile!=null && record.mobile!='') $("#exhibitor-contract-combobox-linkmanMobile").textbox("setValue",record.mobile)
						else $("#exhibitor-contract-combobox-linkmanMobile").textbox("setValue",record.mobile2)
						$("#exhibitor-contract-combobox-linkmanEmail").textbox("setValue",record.email)
						$("#exhibitor-contract-combobox-linkmanQQ").textbox("setValue",record.qq)
						$("#exhibitor-contract-combobox-linkmanWechat").textbox("setValue",record.wechat)
					}
				});
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}
	function exhibitor_contract_mod() {
		exhibitor_contract_mod_or_view(1);
		if(jurisdictions15){
			$($("#exhibitor-contract-combobox-operatorId").parent("li").find("input")[0]).combobox('enable')
			$($("#exhibitor-contract-combobox-customerServiceId").parent("li").find("input")[0]).combobox('enable')
			$($("#exhibitor-contract-combobox-linkman").parent("li").find("input")[0]).combobox('enable')
		}else{
			$($("#exhibitor-contract-combobox-operatorId").parent("li").find("input")[0]).combobox('disable')
			$($("#exhibitor-contract-combobox-customerServiceId").parent("li").find("input")[0]).combobox('disable')
			$($("#exhibitor-contract-combobox-linkman").parent("li").find("input")[0]).combobox('disable')
		}
	}
	function exhibitor_contract_mod_or_view(flag) {
		if (!exhibitor_contract_window_close_set) {
			exhibitor_contract_window_close();
			exhibitor_contract_window_close_set = true;
		}
		var row = $('#exhibitor-contract-datagrid').datagrid('getSelected');
		if (row) {
			emptyLinkman()
			setValLinkman(row.clientId)
			exhibitor_contract_alocate_id = row.serveBoothId;
			exhibitor_contract_projCltId = row.projCltId;
			$('#exhibitor-contract-window').window('open');
			exhibitor_contract_save_id = row.serveBoothId;
			exhibitor_contract_setEditInfoInit(row);
			$('#exhibitor-contract-datagrid-prod').datagrid({
				url: eippath + '/contractDtl/selectByContractId?contractId=' + row.contractId
			});
			exhibitor_contract_edit(flag);

		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function exhibitor_contract_mod2() {
		exhibitor_contract_edit(1);
	}
	//修改 (flag=1: 编辑页面  flag=2: 查看页面)
	function exhibitor_contract_edit(flag) {
		exhibitor_contract_edit_mode = "Mod";
		exhibitor_contract_setEditInfoReadOnly(flag);
		$('#exhibitor-contract-combotree-projectId').combotree('disable')
		exhibitor_contract_setButtonEditMode(flag);
	}

	function exhibitor_contract_view() {
		exhibitor_contract_mod_or_view(2);
	}
	function exhibitor_contract_view_db(row) {
		if (!exhibitor_contract_window_close_set) {
			exhibitor_contract_window_close();
			exhibitor_contract_window_close_set = true;
		}
		exhibitor_contract_alocate_id = row.serveBoothId;
		$('#exhibitor-contract-window').window('open');
		exhibitor_contract_save_id = row.serveBoothId;
		exhibitor_contract_setEditInfoInit(row);
		$('#exhibitor-contract-datagrid-prod').datagrid({
			url: eippath + '/contractDtl/selectByContractId?contractId=' + row.contractId
		});
		exhibitor_contract_edit(2);

	}

	function exhibitor_contract_del() {
		console.log('删除事件开始');
		$.messager.confirm('提示', '确定删除吗？', function(r) {
			if (r) {
				$.ajax({
					url: eippath + '/serveBooth/delete',
					dataType: "json", //返回数据类型
					type: "post",
					data: {
						serveBoothId: exhibitor_contract_save_id
					}, //发送数据
					async: true,
					success: function(data) {
						if (data.state == 1) {
							$.messager.alert('提示', '删除成功！', 'info');
							exhibitor_contract_alocate_id = -1;
							$('#exhibitor-contract-window').window('close');
						} else {
							$.messager.alert('提示', '删除失败！', 'error');
						}
					},
					error: function() {
						$.messager.alert('提示', '数据发送失败！', 'error');
					}
				});
			}
		});
	}

	function exhibitor_contract_del_batch() {
		console.log('批量删除开始');
		var rows = $('#exhibitor-contract-datagrid').datagrid('getSelections');
		if (rows.length > 0) {
			var postData = [];
			for (var i = 0; i < rows.length; i++) {
				postData[i] = {
					serveBoothId: rows[i].serveBoothId
				};
			}
			$.messager.confirm('提示', '删除参展记录会同时删除已启动的一系列服务流程，此操作不可逆，确认删除吗？', function(r) {
				if (r) {
					$.ajax({
						url: eippath + '/serveBooth/deleteBatch',
						dataType: "json", //返回数据类型
						type: "post",
						contentType: "application/json",
						data: JSON.stringify(postData), //发送数据
						async: true,
						success: function(data) {
							if (data.state >= 0) {
								$.messager.alert('提示', '删除成功！', 'info');
								exhibitor_contract_search();
								//exhibitor_contract_fresh();
							} else {
								$.messager.alert('提示', '删除失败！', 'error');
							}
						},
						error: function() {
							$.messager.alert('提示', '数据发送失败！', 'error');
						}
					});
				}
			});
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	// 添加后直接打开确认
	function exhibitor_contract_confirm_after_add() {
		$.ajax({
			url: eippath + '/serveBooth/selectByServeBoothId',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				serveBoothId: exhibitor_contract_alocate_id
			},
			async: true,
			success: function(data) {
				console.log(' data ', data)
				if (data.state >= 0) {
					const row = data.result
					exhibitor_contract_open_serve_booth(row.serveBoothId, row.projectId,row.clientId,row.projCltId,row.ownerId)
				} else {
					$.messager.alert('提示', data.msg || data.message, 'error');
				}
			},
			error: function(e) {
				$.messager.alert('提示', e.getMessage() || '加载数据失败！', 'error');
			}
		});
	}
	function exhibitor_contract_save2() {
		console.log('保存提交数据');
		if ($("#exhibitor-contract-textbox-clientId").val() == '') {
			$.messager.alert('提示', '公司信息不能为空！', 'warning');
			return;
		}
		const pid = $("#exhibitor-contract-combotree-projectId").combotree('getValue') || ''
		if (!pid) {
			$.messager.alert('提示', '项目信息不能为空！', 'warning');
			return;
		}
		var postData = {};
		postData['editMode'] = exhibitor_contract_edit_mode;
		postData['serveBoothId'] = exhibitor_contract_save_id;
		postData['projectId'] = pid ; // $('#exhibitor-contract-combobox-keyword-project').combobox('getValue');
		postData['projCltId'] = exhibitor_contract_projCltId;
		$("#exhibitor-contract-panel-2 ul").find("li").each(function() {
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if (control == "textbox") postData[hint] = $($(this).find("input")[0]).val();
			else if (control == "numberbox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "datebox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "datetimebox") {
				var v = $($(this).find("input")[0]).val();
				if (v != '') postData[hint] = v;
			} else if (control == "combobox") {
				var v = $($(this).find("input")[0]).combobox("getValue")
				if (v != '') postData[hint] = v;
			} else if (control == "checkbox") {
				var v = $(this).find("input")[0].checked
				postData[hint] = v;
			}
		});
		postData['clientId'] = $("#exhibitor-contract-textbox-clientId").val();
		var names = $('#exhibitor-contract-combobox-linkman').combobox('getValue')
		//判断是否已经存在该展商参展记录
		if(exhibitor_contract_edit_mode == "Add"){
			var exhibitor_contract_already_exist = false;
			$.ajax({
				url: eippath + '/serveBooth/existExhibitor',
				dataType: "json", //返回数据类型
				type: "post",
				data: postData, //发送数据
				async: false,
				success: function(data) {
					if (data.state > 0) {
						exhibitor_contract_already_exist = true;
					} else {
						$.ajax({
							url: eippath + '/serveBooth/save',
							dataType: "json", //返回数据类型
							type: "post",
							data: postData, //发送数据
							async: true,
							success: function(data) {
								if (data.state == 1) {
									$.messager.alert('提示', '保存成功！', 'info', exhibitor_contract_confirm_after_add);
									exhibitor_contract_edit(2);
									if (postData['editMode'] == 'Add') {
										exhibitor_contract_alocate_id = data.serveBoothId;
									} else {
										exhibitor_contract_alocate_id = postData['serveBoothId'];
									}
									if(names!=null && names!="" && names!=" "){
										exhibitor_contract_save3()
									}
									exhibitor_contract_fresh();
									exhibitor_contract_cancel()
								}else{
									$.messager.alert('提示', '保存失败！', 'error');
								}
							},
							error: function() {
								$.messager.alert('提示', '数据发送失败！', 'error');
							}
						});

					}
				},
				error: function() {
					$.messager.alert('提示', '数据发送失败！', 'error');
				}
			});
			if(exhibitor_contract_already_exist){
					delete postData.editMode
					delete postData.serveBoothId
					// 直接去重覆盖保存 不打开确认参展
					$.ajax({
							url: eippath + '/serveBooth/updateRepetServeBooth',
							dataType: "json", //返回数据类型
							type: "post",
							data: postData, //发送数据
							async: true,
							success: function(data) {
									if (data.state == 1) {
											$.messager.alert('提示', '保存成功！', 'info');
											exhibitor_contract_edit(2);
											if(names!=null && names!="" && names!=" "){
												exhibitor_contract_save3()
											}
											exhibitor_contract_fresh();
									} else {
											$.messager.alert('提示', '保存失败！', 'error');
									}
							},
							error: function() {
									$.messager.alert('提示', '数据发送失败！', 'error');
							}
					});
            //     $.messager.confirm('提示', '已存在该展商参展记录，是否重复添加?', function(r){
            //         if (r){
            //             //alert('confirmed: '+r);
						// //两次serveBooth/save请求代码一样。
            //             $.ajax({
            //                 url: eippath + '/serveBooth/save',
            //                 dataType: "json", //返回数据类型
            //                 type: "post",
            //                 data: postData, //发送数据
            //                 async: true,
            //                 success: function(data) {
            //                     if (data.state == 1) {
            //                         $.messager.alert('提示', '保存成功！', 'info',exhibitor_contract_confirm_after_add);
            //                         exhibitor_contract_edit(2);
            //                         if (postData['editMode'] == 'Add') {
            //                             exhibitor_contract_alocate_id = data.serveBoothId;
            //                         } else {
            //                             exhibitor_contract_alocate_id = postData['serveBoothId'];
            //                         }
						// 												if(names!=null && names!="" && names!=" "){
						// 													exhibitor_contract_save3()
						// 												}
            //                         exhibitor_contract_fresh();
            //                     } else {
            //                         $.messager.alert('提示', '保存失败！', 'error');
            //                     }
            //                 },
            //                 error: function() {
            //                     $.messager.alert('提示', '数据发送失败！', 'error');
            //                 }
            //             });
            //         }
            //     });
			}
				exhibitor_contract_cancel()
				return;
		}
			//两次serveBooth/save请求代码一样。
			$.ajax({
				url: eippath + '/serveBooth/save',
				dataType: "json", //返回数据类型
				type: "post",
				data: postData, //发送数据
				async: true,
				success: function(data) {
					if (data.state == 1) {
						$.messager.alert('提示', '保存成功！', 'info');
						exhibitor_contract_edit(2);
						if (postData['editMode'] == 'Add') {
							exhibitor_contract_alocate_id = data.serveBoothId;
						} else {
							exhibitor_contract_alocate_id = postData['serveBoothId'];
						}
						exhibitor_contract_fresh();
						exhibitor_contract_cancel()
					}else{
						$.messager.alert('提示', '保存失败！', 'error');
					}
				},
				error: function() {
					$.messager.alert('提示', '数据发送失败！', 'error');
				}
			});
			row = $('#exhibitor-contract-datagrid').datagrid('getSelected')
			if(row.linkman!=null&&row.linkman!=""){
				exhibitor_contract_save3()
			}else{
				if(names!=null && names!="" && names!=" "){
					exhibitor_contract_save3()
				}
			}
	}
	//保存联系人
	function exhibitor_contract_save3(){
		var postData = {};
		postData['personName'] = $('#exhibitor-contract-combobox-linkman').combobox('getValue');
		postData['parentClientId'] = $('#exhibitor-contract-textbox-clientId').textbox('getValue');
		postData['tel'] = $('#exhibitor-contract-combobox-linkmanTel').textbox('getValue');
		postData['mobile'] = $('#exhibitor-contract-combobox-linkmanMobile').textbox('getValue');
		postData['email'] = $('#exhibitor-contract-combobox-linkmanEmail').textbox('getValue');
		postData['qq'] = $('#exhibitor-contract-combobox-linkmanQQ').textbox('getValue');
		postData['wechat'] = $('#exhibitor-contract-combobox-linkmanWechat').textbox('getValue');
		postData['isMain'] = false
		postData['companyName'] = $('#exhibitor-contract-textbox-companyName').textbox('getValue');
		postData['clientTypeCode'] = 'C_LINKMAN';
		postData['isPerson'] = true;
		$.ajax({
			url: eippath + '/client/saveLinkman',
			dataType: "json", //返回数据类型
			type: "post",
			data: postData, //发送数据
			async: true,
			success: function(data) {
				if (data.state != 0) {

				} else {
					$.messager.alert('提示', '联系人保存失败！', 'error');
				}
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}

	function exhibitor_contract_save() {
		exhibitor_contract_save2();
	}

	function exhibitor_contract_cancel() {
		$('#exhibitor-contract-window').window('close', true);
	}
	//设置编辑页面内容清空
	function exhibitor_contract_setEditInfoClear() {
		$("#exhibitor-contract-panel-2 ul").find("li").each(function() {
			var control = $(this).find("input").attr("control");
			if (control == "textbox") $($(this).find("input")[0]).textbox("setValue", '');
			else if (control == "numberbox") $($(this).find("input")[0]).textbox("setValue", '');
			else if (control == "datebox") $($(this).find("input")[0]).textbox("setValue", '');
			else if (control == "datetimebox") $($(this).find("input")[0]).textbox("setValue", '');
			else if (control == "checkbox") $(this).find("input")[0].checked = false;
		});
		$("#exhibitor-contract-amount").textbox("setValue", '');
	}
	//设置编辑页面内容初始化
	function exhibitor_contract_setEditInfoInit(row) {
		$("#exhibitor-contract-panel-2 ul").find("li").each(function() {
			var control = $(this).find("input").attr("control");
			var hint = $(this).find("input").attr("hint");
			if (control == "textbox") $($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if (control == "numberbox") $($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if (control == "datebox") $($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if (control == "datetimebox") $($(this).find("input")[0]).textbox("setValue", row[hint]);
			else if (control == "combobox") $($(this).find("input")[0]).combobox("setValue", row[hint]);
			else if (control == "combotree") $($(this).find("input")[0]).combotree("setValue", row[hint]);
			else if (control == "checkbox") $(this).find("input")[0].checked = row[hint];
		});
		$("#exhibitor-contract-amount").textbox("setValue", row['amount']);
	}
	//设置编辑页面可编辑属性
	function exhibitor_contract_setEditInfoReadOnly(flag) {
		console.log('设置为' + flag);
		var readonly = true,
			able = 'disable';
		if (flag == 1) {
			readonly = false;
			able = 'enable';
		}
		$("#exhibitor-contract-panel-2 ul").find("li").each(function() {
			var tag = $(this).find("input").attr("tag");
			var control = $(this).find("input").attr("control");
			if (control == "textbox") $($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) || readonly);
			else if (control == "numberbox") $($(this).find("input")[0]).textbox('textbox').attr('readonly', (tag == 1) ||
				readonly);
			else if (control == "datebox") $($(this).find("input")[0]).textbox(able);
			else if (control == "datetimebox") $($(this).find("input")[0]).textbox(able);
			else if (control == "combobox") $($(this).find("input")[0]).combobox(able);
			else if (control == "combotree") $($(this).find("input")[0]).combotree(able);
			else if (control == "checkbox") {
				if (flag == 1) $($(this).find("input")[0]).removeAttr("onclick");
				else $($(this).find("input")[0]).attr("onclick", "return false");
			};
		});
	}
	//按钮有效 flag 1 编辑 2 查看
	function exhibitor_contract_setButtonEditMode(flag) {
		if (flag == 1) {
			$("#exhibitor-contract-add").linkbutton('disable');
			$("#exhibitor-contract-mod2").linkbutton('disable');
			$("#exhibitor-contract-del").linkbutton('disable');
			$("#exhibitor-contract-save").linkbutton('enable');
			// $("#exhibitor-contract-cancel").linkbutton('enable');
		} else if (flag == 2) {
			$("#exhibitor-contract-add").linkbutton('enable');
			$("#exhibitor-contract-mod2").linkbutton('enable');
			$("#exhibitor-contract-del").linkbutton('enable');
			$("#exhibitor-contract-save").linkbutton('disable');
			// $("#exhibitor-contract-cancel").linkbutton('disable');
		}
		exhibitor_contract_button_edit_mode = flag;
	}

	function exhibitor_contract_select_company() {
		if (exhibitor_contract_button_edit_mode == 2) return;
		$('#exhibitor-contract-window-select-company').window('open');
		$('#exhibitor-contract-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=0',
			queryParams: {
				queryProjectId: $('#exhibitor-contract-combotree-projectId').combotree('getValue')
			}
		});
		/* 表头去掉多选框 */
		$(".datagrid-header-check").html("");
	}

	function exhibitor_contract_search_select_company() {
		$('#exhibitor-contract-datagrid-select-company').datagrid({
			url: eippath + '/client/getTraderList?key=1',
			queryParams: {
				queryProjectId: $('#exhibitor-contract-combotree-projectId').combotree('getValue'),
				companyName: $('#exhibitor-contract-keyword-select-company').val()
			}
		});
	}

	function exhibitor_contract_ok_select_company() {
		var row = $('#exhibitor-contract-datagrid-select-company').datagrid('getSelected');
		if (row) {
			$("#exhibitor-contract-textbox-clientId").textbox("setValue", row.clientId);
			$("#exhibitor-contract-textbox-companyName").textbox("setValue", row.companyName);
			exhibitor_contract_projCltId = row.projCltId;
			$('#exhibitor-contract-window-select-company').window('close');
			setValLinkman(row.clientId)
			$("#exhibitor-contract-combobox-linkman").combobox('enable')

		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function exhibitor_contract_return_select_company() {
		$('#exhibitor-contract-window-select-company').window('close');
	}
    function exhibitor_contract_select_section_booth(){
		exhibitor_contract_boothdata_tree_fresh();
		$('#exhibitor-contract-datagrid-section-booth').datagrid('loadData', { total: 0, rows: [] });
		$('#exhibitor-contract-window-select-section-booth').window('open');
	}
	function exhibitor_contract_select_booth() {
		if (exhibitor_contract_button_edit_mode == 2) return;
		$('#exhibitor-contract-window-select-booth').window('open');
		$('#exhibitor-contract-datagrid-select-booth').datagrid({
			url: eippath + '/exhibitBooth/getBoothList?key=0',
			queryParams: {
				projectId: $('#exhibitor-contract-combobox-keyword-project').combobox('getValue')
			}
		});
	}
    function  exhibitor_contract_search_select_section_booth(){
        $('#exhibitor-contract-datagrid-section-booth').datagrid({
            url: eippath + '/exhibitBooth/getBoothListByMap?key=1',
            queryParams: {
                typeCode: $('#exhibitor-contract-keyword-booth-type').combobox('getValue'),
                exhibitCode: exhibitCode_for_pass,
				projectId: getQueryString("projectId"),
                boothCode: $('#exhibitor-contract-keyword-select-section-booth').val()
            }
        });
	}

	function exhibitor_contract_search_select_booth() {
		$('#exhibitor-contract-datagrid-select-booth').datagrid({
			url: eippath + '/exhibitBooth/getBoothList?key=1',
			queryParams: {
				projectId: $('#exhibitor-contract-combobox-keyword-project').combobox('getValue'),
				boothCode: $('#exhibitor-contract-keyword-select-booth').val()
			}
		});
	}

	function exhibitor_contract_ok_select_booth() {
		var rows = $('#exhibitor-contract-datagrid-select-booth').datagrid('getSelections');
		if (rows.length > 0) {
			var str = $("#exhibitor-contract-textbox-boothCode").val();
			var canDot = true;
			if (str == "") canDot = false;
			for (var i = 0; i < rows.length; i++) {
				if (str.indexOf("[" + rows[i].boothCode + "]") == -1) {
					if (canDot) str += ",";
					canDot = true;
					str += "[" + rows[i].boothCode + "]";
				}
			}
			$("#exhibitor-contract-textbox-boothCode").textbox("setValue", str);
			$('#exhibitor-contract-window-select-booth').window('close');
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}
	// //记录选取行的数据
	// var section-booth-data = []

	function exhibitor_contract_ok_select_section_booth() {
		var rows = $('#exhibitor-contract-datagrid-section-booth').datagrid('getChecked');
		exhibitor_contract_ok_select_section_booth2(rows)
	}
	function exhibitor_contract_ok_select_section_booth2(rows) {
		console.log(rows)
		if (rows != null) {
        	var boothCode=[];
			$.each(rows, function(index, item){
				boothCode.push(item.boothCode);
				boothArea+=item.boothArea;
				if (index==0){
					var isTexeNull = $("#exhibitor_contract_serve_booth_num").tagbox("getValues").length;
					if(isTexeNull == 0){ //有展位号有值时不修改这里的值 bug 9998 避免改动很多，特殊处理只显示展区不显示拼接的 item.sectionName
						$('#exhibitor_contract_serve_booth_sectionCode').combotree('setValue', item.sectionCode);
						// $('#exhibitor_contract_serve_booth_section').textbox('setValue', item.fillSectionName);
						$('#exhibitor_contract_serve_booth_type').combobox('setValue', item.typeCode);
						$('#exhibitor_contract_serve_booth_spec').textbox("setValue", item.boothSpec);
					}
				}
			});
			var boothCodeOld = $("#exhibitor_contract_serve_booth_num").tagbox("getValues").filter(it=> it);
			var diff = boothCode.filter(function (val) { return boothCodeOld.indexOf(val) === -1 })
			Array.prototype.push.apply(boothCodeOld, diff);
			$("#exhibitor_contract_serve_booth_area").textbox("setValue", boothArea);
			$("#exhibitor_contract_serve_booth_num").tagbox("setValues", boothCodeOld);
			$('#exhibitor-contract-window-select-section-booth').window('close');
		}else {
            $.messager.alert('提示', '未选中内容！', 'warning');
        }
    }

	function exhibitor_contract_return_select_booth() {
		$('#exhibitor-contract-window-select-booth').window('close');
	}
    function exhibitor_contract_return_select_section_booth() {
        $('#exhibitor-contract-window-select-section-booth').window('close');
    }

	function exhibitor_contract_add2() {
		if (exhibitor_contract_button_edit_mode == 2) return;
		$('#exhibitor-contract-window-select-prod').window('open');
		$('#exhibitor-contract-datagrid-select-prod').datagrid({
			url: eippath + '/projProduct/selectByProjectId',
			queryParams: {
				projectId: $('#exhibitor-contract-combobox-keyword-project').combobox('getValue')
			}
		});
	}

	function exhibitor_contract_del2() {
		if (exhibitor_contract_button_edit_mode == 2) return;
		var rows = $('#exhibitor-contract-datagrid-prod').datagrid('getSelections');
		if (rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				var rowIndex = $('#exhibitor-contract-datagrid-prod').datagrid('getRowIndex', rows[i]);
				$('#exhibitor-contract-datagrid-prod').datagrid('deleteRow', rowIndex);
			}
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function exhibitor_contract_search_select_prod() {

	}

	function exhibitor_contract_ok_select_prod() {
		var rows = $('#exhibitor-contract-datagrid-select-prod').datagrid('getSelections');
		var rows2 = $("#exhibitor-contract-datagrid-prod").datagrid("getRows");
		if (rows.length > 0) {
			for (var i = 0; i < rows.length; i++) {
				flag = 0;
				for (var j = 0; j < rows2.length; j++) {
					if (rows2[j].prodCode == rows[i].prodCode) {
						flag = 1;
						break;
					}
				}
				if (flag == 0) {
					$('#exhibitor-contract-datagrid-prod').datagrid('appendRow', {
						prodCode: rows[i].prodCode,
						prodName: rows[i].prodName,
						prodPrice: rows[i].prodPrice,
						prodSpec: '',
						discount: 100,
						price: rows[i].prodPrice,
						curencyCode: rows[i].curencyCode,
						qty: 1,
						amount: rows[i].prodPrice
					});
				}
			}
			$('#exhibitor-contract-window-select-prod').window('close');
		} else {
			$.messager.alert('提示', '未选中内容！', 'warning');
		}
	}

	function exhibitor_contract_return_select_prod() {
		$('#exhibitor-contract-window-select-prod').window('close');
	}
	var select_server_booth_contractId = 0;
	var select_server_booth_projectId = 0;
	var select_server_booth_clientId = 0;
	var select_server_booth_projCltId = 0;
	var select_server_booth_ownerId = 0;
	var select_server_booth_serveBoothId = 0;
    var oppens = false
	function exhibitor_contract_open_serve_booth(contractId, projectId, clientId, projCltId, ownerId) {
		isOne = 0
		oppens = false
		console.log('点击确定展位按钮');
		select_server_booth_contractId = contractId;
		select_server_booth_projectId = projectId;
		select_server_booth_clientId = clientId;
		select_server_booth_projCltId = projCltId;
		select_server_booth_ownerId = ownerId;
		select_server_booth_serveBoothId = 0;
		var cnt = 0;
		exhibitor_contract_form_showing = true;
		$("#exhibitor_contract_serve_openInfoId").combobox("setValue","1");
		$("#exhibitor_contract_serve_booth_num").tagbox("clear");
		let flag = $.ajaxSettings.async
		$.ajaxSettings.async = false
		$('#exhibitor_contract_serve_booth_type').combobox({
			valueField: 'typeCode',
			textField: 'typeName',
			panelHeight: '200px', //auto
			url: eippath + '/boothType/selectByProjectId',
			queryParams: {
				f_project_id: projectId
			},
			onLoadSuccess: function(data) {
				exhibitor_contract_form_showing = false;
			},
			onChange: function(data){
				$("#exhibitor_contract_serve_openInfoId").combobox("setValue","1");
			}
		});
		$.ajaxSettings.async = flag;
        $('#exhibitor-contract-keyword-booth-type').combobox({
            valueField: 'typeCode',
            textField: 'typeName',
            panelHeight: '200px', //auto
            url: eippath + '/boothType/selectByProjectId',
            queryParams: {
                f_project_id: projectId
            }
        });
		WhetherEntered();
		exhibitor_contract_serve_booth_query(contractId);
		loadTaskData()
		boothPrice()
		exhibitor_contract_boothdata_tree_fresh() //打开弹窗获取所有展区数据
		//绑定onchange事件
		$('#exhibitor_contract_serve_openInfoId').combobox('setValue',openInfoIds);
		isOne = 1
	}
	function WhetherEntered(){
		$.ajax({
			url: eippath + '/taskDetail/selectTaskDetail',
			dataType: "json",
			type: "post",
			data: {
				projCltId: select_server_booth_projCltId,
				taskKindCode: 'PERSON',
				serveBoothId: select_server_booth_contractId
				},
			async: true,
			success: function(data){
				if(data.state == 1){
					if(data.result.confirm == 4){
						$("#personnelInformationShade").show()
					}else{
						$("#personnelInformationShade").hide()
					}
				}
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
	}
	var BoothPricesDatagridData = []
	function boothPrice(){
		$.ajax({
			url: eippath + '/orderBS/getBoothOrder',
			dataType: "json",
			type: "post",
			data: {
					projectId: select_server_booth_projectId,
					clientId:  select_server_booth_clientId,
				   },
			async: true,
			success: function(data){
				if(data.state == 1){
					BoothPricesDatagridData = []
					BoothPricesDatagridData = data.result
					// $('#exhibitor-BoothPrices-datagrid').datagrid({
					// 	data: data.result,
					// });
					// $('#exhibitor-BoothPrices-datagrid').datagrid('resize');
				}
			},
			error: function() {
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
		// $('#exhibitor-BoothPrices-datagrid').datagrid('resize');
	}

	// 展位费用操作
	var fieldData = ""
	var valuesDatas = ""
	var indexDatas = ""
	// $('#exhibitor-BoothPrices-datagrid').datagrid({
	// 	onClickRow: function (rowIndex, rowData) {
	// 		exhibitor_BoothPrices_datagrid_onClickRow(rowIndex);
	// 		var l = $(this).datagrid('getRows').length;
	// 		for(var i = 0; i < l; i++){
	// 			if(i != rowIndex)$(this).datagrid('unselectRow', i);
	// 		}
	// 	},
	// 	onClickCell: function(index,field,value){
	// 		fieldData=""
	// 		valuesDatas = ""
	// 		fieldData = field
	// 		valuesDatas = value
	// 	    console.log(index + field + value)
	// 	},
	// 	onAfterEdit: function(index,row,changes){
	// 		if(changes.length!=0){
	// 			if(fieldData == "price"){
	// 				var totals = ""
	// 				var qty1 = 0
	// 				var price1 = 0
	// 				if(changes!="" && changes!=null){
	// 					if(row.qty!="" && row.qty!=null && row.qty!= NaN) qty1 = row.qty
	// 					else qty1 = 0
	// 					if(changes.price!=" " && changes.price!=null && changes.price!= NaN && changes.price!= undefined && changes.price!= 'undefined') price1 = changes.price
	// 					else price1 = row.price
	// 					console.log(changes)
	// 					console.log(changes.price)

	// 					totals = price1 * qty1
	// 					row.amount = totals
	// 				}
	// 			}else if(fieldData == "qty"){
	// 				var totals2 = ""
	// 				var qty2 = 0
	// 				var price2 = 0
	// 				if(changes!="" && changes!=null){
	// 					if(row.price!="" && row.price!=null && row.price!= NaN) price2 = row.price
	// 					else price2 = 0
	// 					if(changes.qty!="" && changes.qty!=null && changes.qty!= NaN && changes.qty!= undefined) qty2 = changes.qty
	// 					else qty2 = row.qty
	// 					var totals = price2 * qty2
	// 					row.amount = totals
	// 				}
	// 			}else if(fieldData = "amount"){
	// 				var qty3 = 0
	// 				var amount3 = 0
	// 				if(changes!="" && changes!=null){
	// 					if(row.qty!="" && row.qty!=null && row.qty!= NaN) qty3 = row.qty
	// 					else qty3 = 0
	// 					if(changes.amount!="" && changes.amount!=null && changes.amount!= NaN && changes.amount!= undefined && changes.amount!= 'undefined') amount3 = changes.amount
	// 					else amount3 = row.amount
	// 					var prices = amount3/qty3
	// 					console.log(amount3 + " amount3")
	// 					console.log(changes+ " changes")
	// 					console.log(changes.amount + " changes.amount")
	// 					console.log(row.qty + " row.qty")
	// 					console.log(prices + " prices")
	// 					if(prices != NaN && prices == "NaN") row.price = prices
	// 					else row.price = 0

	// 				}
	// 			}
	// 			indexDatas = index
	// 			BoothPricesDatagridData.splice(index,1,row);
	// 		}
	// 		$('#exhibitor-BoothPrices-datagrid').datagrid({
	// 			data: BoothPricesDatagridData,
	// 			onLoadSuccess:function(data){
	// 				$('#exhibitor-BoothPrices-datagrid').datagrid('resize');
	// 			}
	// 		});
	// 	},
	// });
	function exhibitor_contract_close_serve_booth() {
		$('#exhibitor-contract-window-serve-booth').window('close');
	}
	function project_boothType_fresh() {
		$('#project-boothType-datagrid').datagrid({
			url: eippath + '/boothType/getList?key=0',
			queryParams: {
				exhibitCode: exhibitCode_for_pass
			},
			onLoadSuccess: function(data) {
				if (project_boothType_alocate_id == -1) $(this).datagrid('selectRow', -1);
				else {
					var rows = $(this).datagrid("getRows");
					for (var i = 0; i < rows.length; i++) {
						if (rows[i].typeCode == project_boothType_alocate_id) {
							$(this).datagrid('selectRow', i);
							project_boothType_alocate_id = -1;
							break;
						}
					}
				}
			}
		});
	}

	// 展位费用:开口费及展位费
	// $('#exhibitor-BoothPrices-datagrid').datagrid({
	// 	onDblClickCell: function(index,field,value){
	// 		console.log(index + field + value)
	// 		// $(this).datagrid('beginEdit', index);
	// 		// var ed = $(this).datagrid('getEditor', {index:index,field:field});
	// 		// $(ed.target).focus();
	// 	}
	// });




	function exhibitor_contract_serve_booth_query(contractId){
		//$("#exhibitor-contract-serve-booth-panel-1").panel("expand");
		$("#exhibitor-contract-serve-booth-panel-1").panel("open");
		//$("#exhibitor-contract-serve-booth-panel-2").panel("open");
		exhibitor_serveBooth_identity_change();//监听选中事件
		  //目前contractId变量存的是serveBoothId
	  $.ajax({
			url: eippath + '/contract/queryServerBooth',
			dataType: "json",
			type: "post",
			async:false,
			data: {contractId: contractId},	//发送数据
			success: function(data){
				if(data!=null&&data!="null") {
					//alert(data.boothConfirmed);
					var boothNum = (data.boothNum || '').trim();
					if (boothNum) $('#exhibitor_contract_serve_booth_num').tagbox('setValues', boothNum.split(","));
					$('#exhibitor_contract_serve_booth_area').textbox('setValue', data.boothArea);
					boothArea = data.boothArea
					$('#exhibitor_contract_serve_booth_type').combobox('select', data.boothTypeCode);
					$('#exhibitor_contract_serve_booth_sectionCode').combotree('setValue', data.sectionCode || data.section || '');
					// $('#exhibitor_contract_serve_booth_section').textbox('setValue', data.section);
					if(data.boothSpec) {
						$('#exhibitor_contract_serve_booth_spec').textbox('setValue', data.boothSpec);
					}
					if(data.certNum || data.inviteNum) {
						$('#exhibitor_contract_serve_cert_num').textbox('setValue', +data.certNum || '');
						$('#exhibitor_contract_serve_invite_num').textbox('setValue', +data.inviteNum || '');
					} else {
						if(!data.boothTypeCode) {
							$('#exhibitor_contract_serve_cert_num').textbox('clear');
							$('#exhibitor_contract_serve_invite_num').textbox('clear');
						}
					}
					select_server_booth_serveBoothId = data.serveBoothId;
					$('#exhibitor_contract_serve_person_num').val(data.personNum);
					$('#exhibitor_contract_serve_exhibitProductRange').textbox('setValue',data.exhibitProductRange);
					if (data.identity == 1) {
						$('#exhibitor_serveBooth_identity_1').prop("checked", true);
						$("#exhibitor-contract-serve-booth-panel-1").panel("close");
						//$("#exhibitor-contract-serve-booth-panel-2").panel("close");
					} else if (data.identity == 2) {
						$('#exhibitor_serveBooth_identity_2').prop("checked", true);
						$("#exhibitor-contract-serve-booth-panel-1").panel("close");
						//$("#exhibitor-contract-serve-booth-panel-2").panel("close");
					} else {
						$('#exhibitor_serveBooth_identity_1').prop("checked", false);
						$('#exhibitor_serveBooth_identity_2').prop("checked", false);
					}
				}else{
					$('#exhibitor_contract_serve_booth_num').textbox('setValue','');
					$('#exhibitor_contract_serve_booth_area').textbox('setValue','');
					$('#exhibitor_contract_serve_booth_sectionCode').combotree('setValue','');
					// $('#exhibitor_contract_serve_booth_section').textbox('setValue','');
					$('#exhibitor_contract_serve_booth_spec').textbox('setValue','');
					$('#exhibitor_contract_serve_cert_num').textbox('setValue','');
					$('#exhibitor_contract_serve_invite_num').textbox('setValue','');
					$('#exhibitor_contract_serve_exhibitProductRange').textbox('clear');
					select_server_booth_serveBoothId = 0;
					$('#exhibitor_contract_serve_person_num').val(0);
					$('#exhibitor_serveBooth_identity_1').prop("checked",false);
					$('#exhibitor_serveBooth_identity_2').prop("checked",false);
				}
				exhibitor_serveBoothDtl_query(contractId);
				$('#exhibitor-contract-window-serve-booth').window('open');
				calculate();
				oppens = true;
				openInfoIds = data.openInfoId;
				var a = $('#exhibitor_contract_serve_booth_type').combobox('getValue');
				// $('#exhibitor_contract_serve_openInfoId').combobox('setValue',toString(data.openInfoId));
			}
		});
	}


	function exhibitor_serveBoothDtl_query(serveBoothId){
		$('#exhibitor-serveBoothDtl-datagrid').datagrid({
			url: eippath + '/serveBooth/selectDtlAndTrip',
			queryParams: {
				serveBoothId: serveBoothId,
				projectId: select_server_booth_projectId,
				clientId:select_server_booth_clientId
			},
			onLoadSuccess: function(data) {
				if(data.total==0&&PERSON){
					$.messager.alert('提示','无法查询到行程信息，请确认是否设置？','warning');
				}
				$('#exhibitor-serveBoothDtl-datagrid').datagrid('resize');
			}
		});
	}


	function calculate(){
		var s = ""
		$('#exhibitor_contract_serve_booth_spec').textbox().next('span').find('input').blur(function() {
			var s1 = $('#exhibitor_contract_serve_booth_spec').textbox('getValue');
			var s2 = s1.split("*")[0];
			var s3 = s1.split("*")[1];
			if(s1=="" || s1== null){
				$.messager.alert('提示','请按照格式输入');
				return;
			}else if(s3=="" || s3== null){
				$.messager.alert('提示','请按照格式输入');
				return;
			}
			var value1 = parseFloat(s2).toFixed(2);
			var value2 = parseFloat(s3).toFixed(2);
			var xsd1 = value1.toString();
			var xsd2 = value2.toString();
			var f1 = 0;
			var f2 = 0;
			f = xsd1 + '*' + xsd2
			s = xsd1 * xsd2
			x = s.toFixed(2)
			//$('#exhibitor_contract_serve_booth_area').textbox('setValue', x);
			$('#exhibitor_contract_serve_booth_spec').textbox('setValue', f);
		});
	}

	/**
	 * 进馆证生效
	 * @type {boolean}
	 */
	let CERT=false;
	/**
	 * 邀请函生效
	 * @type {boolean}
	 */
	let INVITE = false;
	/**
	 * 行程生效
	 * @type {boolean}
	 */
	let PERSON=false;
	/**
	 * 展位配置
	 * @type {boolean}
	 */
	let BOOTH=false;

	function loadTaskData() {
		$.ajax({
			url: eippath + '/task/getList',
			dataType: "json",
			type: "post",
			data:{key:1,projectId:select_server_booth_projectId,page:1,rows:20},
			success: function (data) {
				if (data.total>0){
					const rows=data.rows;
					rows.forEach(function (element, index) {
						if (element.taskKindCode=="CERT"){//进馆证生效
							CERT=true;
						}else if(element.taskKindCode=="INVITE") {//行程生效
							INVITE = true;
						}else if(element.taskKindCode=="PERSON"){//行程生效
							PERSON=true;
						}else if(element.taskKindCode=="BOOTH"){//展位配置
							BOOTH=true;
						}
					});
				}
				showOrHide();
			}
		})
	}
	function showOrHide() {
		if (!CERT){
			$("#exhibitor_contract_serve_cert_num_li").hide()
		}
		if (!INVITE){
			$("#exhibitor_contract_serve_invite_num_li").hide()
		}
		if (!PERSON){
			//$("#personnelInformation").hide()
			$("#personnelInformation").parent().hide()
		}
		if (BOOTH){

		}
	}


	function exhibitor_contract_serve_booth_save(){
		  exhibitor_serveBoothDtl_datagrid_endEditing();
		  console.log('保存展位确认');
		  var identity = '';
		  var check1 = $('#exhibitor_serveBooth_identity_1').is(':checked');
		  var check2 = $('#exhibitor_serveBooth_identity_2').is(':checked');
		  var boothNum = $('#exhibitor_contract_serve_booth_num').tagbox('getValues');
		  boothNum=boothNum.join(',');
		  var boothType = $('#exhibitor_contract_serve_booth_type').combobox('getValue');
		  var boothArea = $('#exhibitor_contract_serve_booth_area').textbox('getValue');
		  var personNum = $('#exhibitor_contract_serve_person_num').val();
		  var certNum = $('#exhibitor_contract_serve_cert_num').textbox('getValue');
		  var inviteNum = $('#exhibitor_contract_serve_invite_num').textbox('getValue');
		  var sectionCode = $('#exhibitor_contract_serve_booth_sectionCode').combotree('getValue'); // 展馆code/错误的code
			var section = $('#exhibitor_contract_serve_booth_sectionCode').combotree('getText'); // 展馆名称/手输的名字
			if(section) {
				var tmp = $('#exhibitor_contract_serve_booth_sectionCode').combotree('tree').tree('getSelected');
				if(tmp && tmp.sectionName=== section ) { // 已有展馆
					section = '';
				} else { // 不存在的展馆
					sectionCode = '';
				}
			} else {
				sectionCode = '';
			}
		  var boothSpec = $('#exhibitor_contract_serve_booth_spec').textbox('getValue');
		  var openInfoId = $('#exhibitor_contract_serve_openInfoId').combobox('getValue');
		  var exhibitProductRange = $('#exhibitor_contract_serve_exhibitProductRange').textbox('getValue');
		  if(check1){identity = 1;}
		  if(check2){identity = 2;}
		  if(identity==''){ //如果两个都没有被选中
			  // if(boothNum==''){
				 //  $.messager.alert('提示','请输入展位号！','warning');
				 //  return;
			  // }
			  // if(boothType==''){
				 //  $.messager.alert('提示','请选择展位类型！','warning');
				 //  return;
			  // }
			  // if(boothArea==''||boothArea<=0){
				 //  $.messager.alert('提示','请输入面积！','warning');
				 //  return;
			  // }
			  if (CERT&&certNum==''){
				  $.messager.alert('提示','请先到 信息设置-展位类型设置 菜单设置 进馆证数量！','warning');
				  return;
			  }
			  if (INVITE&&inviteNum==''){
				  $.messager.alert('提示','请先到 信息设置-展位类型设置 菜单设置 邀请函数量！','warning');
				  return;
			  }
			  if(BOOTH){
				  var flag2 = queryExistDefaultTool(boothType);
				  if(flag2==-1){
					  return;
				  }else if(flag2==0){
					  $.messager.alert('提示','该类型展位还未设置标配展具，请先到 信息设置-展位类型设置 菜单设置','warning');
					  return;
				  }
			  }
		  }
		  var postData = { serveBoothId:select_server_booth_serveBoothId,
							contractId:select_server_booth_contractId,
							projectId: select_server_booth_projectId,
							clientId:select_server_booth_clientId,
							projCltId:select_server_booth_projCltId,
							ownerId:select_server_booth_ownerId,
							boothTypeCode:boothType,
							personNum,
							boothNum,
							boothArea,
							section,
							sectionCode,
							boothSpec,
							exhibitProductRange: exhibitProductRange.trim(),
							certNum,
			  			inviteNum,
							identity,
							boothConfirmed:true  ,//展位已确认
							openInfoId,//开口
				         };
		  var rows = $('#exhibitor-serveBoothDtl-datagrid').datagrid("getRows");
		  if (rows.length > 0){
				for(var i = 0; i < rows.length; i++){
					postData['serveBoothDtlList[' + i + '].serveBoothDtlId'] = rows[i].serveBoothDtlId;
					postData['serveBoothDtlList[' + i + '].serveBoothId'] = select_server_booth_serveBoothId;
					postData['serveBoothDtlList[' + i + '].qty'] = rows[i].qty;
					postData['serveBoothDtlList[' + i + '].primaryKeyBS'] = rows[i].projTripId;
				}
			}

		  //目前contractId变量存的是serveBoothId
		  //有展位号时判断展位是否有重复
		  var flag = queryExistByBoothNum(select_server_booth_serveBoothId,select_server_booth_projectId,boothNum);
		  if(flag>0 && boothNum){
		  	$.messager.confirm('提示',msg+'展位已被确认,是否继续保存？',function(r){
			  if(r){
					$.ajax({
							url: eippath + '/contract/saveServerBooth',
							dataType: "json",	//返回数据类型
							type: "post",
							data: postData,
							async: true,
							success: function(data){
								if(data.state == 1){
									exhibitor_contract_close_serve_booth();
									exhibitor_contract_search();
									exhibitor_contract_generate_task_detail(select_server_booth_contractId);

								}else{
									$.messager.alert('提示','保存失败！','error');
								}
							},
							error: function() {
								$.messager.alert('提示', '数据发送失败！', 'error');
							}
						});
				  }
		    });
		  }else {
			$.ajax({
				url: eippath + '/contract/saveServerBooth',
				dataType: "json",	//返回数据类型
				type: "post",
				data: postData,
				async: true,
				success: function(data){
					if(data.state == 1){
						exhibitor_contract_close_serve_booth();
						exhibitor_contract_search();
						exhibitor_contract_generate_task_detail(select_server_booth_contractId);
					}else{
						$.messager.alert('提示','保存失败！','error');
					}
				},
				error: function() {
					$.messager.alert('提示', '数据发送失败！', 'error');
				}
			});
		  }
	}

	// 展位号删除事件
	$('#exhibitor_contract_serve_booth_num').tagbox({
	    // onRemoveTag(value){
			// 	param.id = 2;
			// 	param.language = 'js';
			// },
			onChange(n,o) {
				$('#exhibitor_contract_serve_booth_count').textbox('setValue', (n || []).length);
			},
	});
	// $("#exhibitor_contract_serve_booth_num").blur(function(){
	// 	var boothNum = $('#exhibitor_contract_serve_booth_num').tagbox('getValues').filter(it=> it);
	// 	$('#exhibitor_contract_serve_booth_count').textbox('setValues', boothNum.length);
	// 	// boothNum=boothNum.join(',');
	// 	// if(boothNum!=''){ //有展位号时判断展位是否有重复
	// 	// var flag = queryExistByBoothNum(select_server_booth_serveBoothId,select_server_booth_projectId,boothNum);
	// 	//     if(flag>0){
	// 	// 	  $("#exhibitor_contract_serve_booth_num").tagbox("clear");
	// 	// 	  $.messager.alert('提示',msg+'展位已被确认！','warning');
	// 	//    }
	// 	// }
	// })
	var msg='';
	function queryExistByBoothNum(serveBoothId, projectId, boothNum) {
		var flag = 0;
		$.ajax({
			url: eippath + '/serveBooth/queryExistByBoothNum',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				serveBoothId: serveBoothId,
				projectId: projectId,
				boothNum: boothNum
			}, //发送数据
			async: false,
			success: function(data) {
				flag = data.state;
				msg=data.msg;
			},
			error: function() {
				flag = -1;
				$.messager.alert('提示', '数据发送失败！', 'error');
			}
		});
		return flag;
	}

	function queryExistDefaultTool(boothType){
		$.ajax({
			url: eippath + '/exhibition/selectExhibitSettingParam',
			dataType: "json",
			type: "post",
			data: {exhibitCode:getQueryString("exhibitCode"),param:'isBareGround',taskKindCode:'UNIVERSAL'},
			async: false,
			success: function(data){
				if (data.data==='1'){
					$.ajax({
						url: eippath + '/defaultTool/getList',
						dataType: "json",	//返回数据类型
						type: "post",
						data: { boothTypeCode:boothType},	//发送数据
						async: false,
						success: function(data){
							//alert(data.length)
							return data.length;
						},
						error: function(){
							return -1;
							$.messager.alert('提示','数据发送失败！','error');
						}
					});
				}else return 1;
			}
		});
	}


	function exhibitor_contract_import() {
		document.getElementById('choiseexcel').click()
	}
	function exhibitor_boothNum_import() {
		document.getElementById('boothNumChoiseexcel').click()
	}

	// var wb; //读取完成的数据
	// var rABS = false; //是否将文件读取为二进制字符串
	//开始导入
	// function importf(obj) {
	// 	console.log('开始导入');
	// 	//有时候操作员会忘记点同步展会，导致生成的会员看不出参展的展会，每次先调一次同步展会的服务（可能存在的弊端是：展会的url地址、图片地址会变化）
 //        //推送到展之云会员中心
 //        console.log("eipbusinesspath:"+eipbusinesspath);
 //        var myHostName = window.location.hostname;
 //        var myPort = window.location.port;

 //        var flag = false;
 //        $.ajax({
 //            url : eipbusinesspath + '/tradeProject/insert',
 //            dataType : "json", //返回数据类型
 //            type : "post",
 //            data : {
 //                f_project_id : project_for_pass,
 //                f_exhibit_code : exhibitCode_for_pass,
 //                f_project_name : exihibit_name_for_pass,
 //                f_server_ip : myHostName,
 //                f_server_port : myPort,
 //                f_project_logo : exhibit_logo_for_pass,
 //                f_create_org_id : org_num_for_pass,
 //                f_start_date:exhibit_startDate_for_pass,
	// 			f_end_date:exhibit_endDate_for_pass,
	// 			f_exhibit_place:exhibit_exhibitPlace_for_pass
 //            }, //发送数据
 //            async : false,
 //            success : function(data) {
 //            	var  state = data.result;
 //            	if(state>0)flag = true;
 //            },
 //            error : function() {
 //            }
 //        });
 //        if(!flag){
 //        	$.messager.alert('提示', '数据发送失败！', 'error');
	// 		return;
	// 	}


	// 	if (!obj.files) {
	// 		return;
	// 	}
	// 	var f = obj.files[0];
	// 	var reader = new FileReader();
	// 	reader.onload = function(e) {
	// 		var data = e.target.result;
	// 		if (rABS) {
	// 			wb = XLSX.read(btoa(fixdata(data)), { //手动转化
	// 				type: 'base64'
	// 			});
	// 		} else {
	// 			wb = XLSX.read(data, {
	// 				type: 'binary'
	// 			});
	// 		}
	// 		/**
	// 		 * wb.SheetNames[0]是获取Sheets中第一个Sheet的名字
	// 		 * wb.Sheets[Sheet名]获取第一个Sheet的数据
	// 		 */
	// 		var excelJson = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
	// 		//JSON.stringify( excelJson )
	// 		for (i = 0; i < excelJson.length; i++) {
	// 			excelJson[i].初始密码 = hex_md5(excelJson[i].初始密码).toUpperCase();
	// 		}

	// 		var strs = JSON.stringify(excelJson);
	// 		strs = strs.replace(/手机号/g, "linkmanTel").replace(/初始密码/g, "sectionCode").replace(/公司名称/g, "clientName").replace(
	// 			/展馆/g, "section").replace(/展位号/g, "boothNum");
	// 		upload(strs);
	// 		$("#choiseexcel").val('');
	// 		//          document.getElementById("excelContent").innerHTML= JSON.stringify( excelJson );
	// 	};
	// 	if (rABS) {
	// 		reader.readAsArrayBuffer(f);
	// 	} else {
	// 		reader.readAsBinaryString(f);
	// 	}
	// }

	// function upload(strs) {
	// 	$.ajax({
	// 		type: "post",
	// 		url: eippath + "/serveBooth/import?pid=" + project_for_pass + "&exhibitCode=" + exhibitCode_for_pass,
	// 		datatype: 'json',
	// 		contentType: "application/json",
	// 		data: strs,
	// 		xhrFields: {
	// 			withCredentials: true
	// 		},
	// 		//	 data:$('#Buyer_reg').serialize(),
	// 		before: function() {
	// 			$.messager.alert('提示', '导入中。。请稍候', 'info');
	// 		},
	// 		success: function(data) {
	// 			//session过期，服务端不返回数据，得重新登录一次
	// 			//var res = JSON.parse(data)
	// 			if (data.state == 1) {
	// 				$.messager.alert('提示', '导入成功！', 'info');
	// 				exhibitor_contract_search();
	// 				//exhibitor_contract_fresh();
	// 			} else {
	// 				$.messager.alert('提示', '导入失败！', 'error');
	// 			}
	// 		},
	// 		error: function(data) {
	// 			console.log("error");
	// 		}
	// 	});

	// }

	function importfBoothNum(obj) {
		console.log('开始导入');
		if (!obj.files) {
			return;
		}
		var file = obj.files[0];
		document.getElementById('boothNumChoiseexcel').value = '';
		var formData = new window.FormData();
		formData.append('file', file);
		formData.append('pid', getQueryString("projectId"));
		formData.append('exhibitCode', getQueryString("exhibitCode"));
		$.ajax({
			url: eippath + "/serveBooth/boothNumsImport",
			type: "post",
			data: formData,
			processData: false,  // 不处理数据
			contentType: false,   // 不设置内容类型
			beforeSend: function() {
				$.messager.alert('提示', '导入中。。请稍候', 'info');
			},
			success: function(data) {
				$.messager.alert('提示', '导入成功！', 'info');
			},
			error: function(data) {
				console.log("error");
			}
		});

	}

	var exhibitor_serveBoothDtl_datagrid_editIndex = null;
	function exhibitor_serveBoothDtl_datagrid_onClickRow(index){
		if(exhibitor_serveBoothDtl_datagrid_editIndex != null && exhibitor_serveBoothDtl_datagrid_editIndex != index){
			exhibitor_serveBoothDtl_datagrid_endEditing();
			$('#exhibitor-serveBoothDtl-datagrid').datagrid('beginEdit', index);
			exhibitor_serveBoothDtl_datagrid_editIndex = index;
		}else{
			$('#exhibitor-serveBoothDtl-datagrid').datagrid('beginEdit', index);
			exhibitor_serveBoothDtl_datagrid_editIndex = index;
		}
		//给编辑框绑定失去焦点事件
		$(".datagrid-row-editing .textbox-text").on("blur",function(e){
	       exhibitor_serveBoothDtl_datagrid_endEditing();//失去焦点后要结束编辑，否则获取不到刚录入的新数据
	       var rows = $('#exhibitor-serveBoothDtl-datagrid').datagrid("getRows");
			  var num = 0;
			  if (rows.length > 0){
					for(var i = 0; i < rows.length; i++){
						if(rows[i].qty>0){
							num += Number(rows[i].qty); //计算总人数
						}
					}
				}
			 $("#exhibitor_contract_serve_person_num").val(num);
		});
	}
	function exhibitor_serveBoothDtl_datagrid_endEditing(){
		if(exhibitor_serveBoothDtl_datagrid_editIndex!=null){
			$('#exhibitor-serveBoothDtl-datagrid').datagrid('endEdit', exhibitor_serveBoothDtl_datagrid_editIndex);
		}
	}
	//展位费用编辑
	var exhibitor_BoothPrices_datagrid_editIndex = null;
	function exhibitor_BoothPrices_datagrid_onClickRow(index){
		if(exhibitor_BoothPrices_datagrid_editIndex != null && exhibitor_BoothPrices_datagrid_editIndex != index){
			exhibitor_BoothPrices_datagrid_endEditing();
			$('#exhibitor-BoothPrices-datagrid').datagrid('beginEdit', index);
			exhibitor_BoothPrices_datagrid_editIndex = index;
		}else{
			$('#exhibitor-BoothPrices-datagrid').datagrid('beginEdit', index);
			exhibitor_BoothPrices_datagrid_editIndex = index;
		}
		//给编辑框绑定失去焦点事件
		$(".datagrid-row-editing .textbox-text").on("blur",function(e){
		   exhibitor_BoothPrices_datagrid_endEditing();//失去焦点后要结束编辑，否则获取不到刚录入的新数据
		   var rows = $('#exhibitor-BoothPrices-datagrid').datagrid("getRows");
		});
	}
	function exhibitor_BoothPrices_datagrid_endEditing(){
		if(exhibitor_BoothPrices_datagrid_editIndex!=null){
			$('#exhibitor-BoothPrices-datagrid').datagrid('endEdit', exhibitor_BoothPrices_datagrid_editIndex);
		}
	}
	//展位费用编辑结束
	function exhibitor_serveBoothDtl_format_qty(value,row,index){
		if(value==null||value==''){
			return 0;
		}else{
			return value;
		}
	}
	function exhibitor_serveBooth_identity_change(){

		$("#exhibitor_serveBooth_identity_1").change(function() {

			var check = $('#exhibitor_serveBooth_identity_1').is(':checked');
			if(check){
				$('#exhibitor_serveBooth_identity_2').prop("checked",false);
				//$("#exhibitor-contract-serve-booth-panel-1").panel("collapse");
				$("#exhibitor-contract-serve-booth-panel-1").panel("close");
				//$("#exhibitor-contract-serve-booth-panel-2").panel("close");
			}else{
				//$("#exhibitor-contract-serve-booth-panel-1").panel("expand");
				$("#exhibitor-contract-serve-booth-panel-1").panel("open");
				//$("#exhibitor-contract-serve-booth-panel-2").panel("open");
			}
		});
		$("#exhibitor_serveBooth_identity_2").change(function() {
			var check = $('#exhibitor_serveBooth_identity_2').is(':checked');
			if(check){
				//$('#exhibitor_serveBooth_identity_1').checked = false;
				$('#exhibitor_serveBooth_identity_1').prop("checked",false);
				//$("#exhibitor-contract-serve-booth-panel-1").panel("collapse");
				$("#exhibitor-contract-serve-booth-panel-1").panel("close");
				//$("#exhibitor-contract-serve-booth-panel-2").panel("close");
			}else{
				//$("#exhibitor-contract-serve-booth-panel-1").panel("expand");
				$("#exhibitor-contract-serve-booth-panel-1").panel("open");
				//$("#exhibitor-contract-serve-booth-panel-2").panel("open");
			}
		});
	}

	function exhibitor_serveBoothDtl_format_qty_change(newValue,oldValue){
		 /*  var rows = $('#exhibitor-serveBoothDtl-datagrid').datagrid("getRows");
		  var num = 0;
		  if (rows.length > 0){
				for(var i = 0; i < rows.length; i++){
					if(rows[i].qty>0){
						num += Number(rows[i].qty);
					}
				}
			}
		 $("#exhibitor_contract_serve_person_num").val(num); */
	}
    function exhibitionType(){
		$("#exhibitionType").window("open");
	}

	function loadExhibitorContractCombotreeSubpro(){
		const $subProjectTree = $('#exhibitor-contract-combotree-subpro,#exhibitor-contract-combotree-projectId')
		// const $subProjectTree = $('#exhibitor-contract-combotree-projectId')
		$subProjectTree.combotree({
			url: eippath + '/project/selectSubProject?parentId=' + getQueryString("projectId"),
			loadFilter(data) {
				let tmp = data.data;
				if(!tmp[0].children || !tmp[0].children.length){
					$('#exhibitor-contract-combotree-subpro').parent().hide()
					.prev('label').hide();
				}
				// if(data && data.data && data.data.length>1){
				// 	tmp = [data.data[0]]
				// }
				// tmp[0].children = null;
				return tmp
			},
			async: false,
			onLoadError(e) {
				$.messager.alert('子项目加载失败！')
			},
		});
	}
</script>