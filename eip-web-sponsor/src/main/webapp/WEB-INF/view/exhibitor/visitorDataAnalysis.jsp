<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />

<style type="text/css">
	.exhibit-name {
		border-bottom: 1px solid #cccccc;
		text-align: center;
	}

	.exhibit-name h6 {
		font-size: 20px;
		color: #333333;
		margin-top: 40px;
		margin-bottom: 10px;
		font-weight: normal;
	}

	.statistics-item {
		overflow: hidden;
	}

	.statistics-item ul li {
		height: 216px;
		border-radius: 10px;
		/* width: 288px; */
		/* float: left;
		margin-right: 8%; */
		margin-top: 50px;
		margin-left: 0;margin-right: 0;width: auto;
		margin-left: 0;margin-right: 0;width: auto;
		margin-left: 0;margin-right: 0;width: auto;
		margin-left: 0;margin-right: 0;width: auto;
		cursor: pointer;
	}

	.bc6cbee4 {
		background-color: #6cbee4;
	}

	.bc7ed159 {
		background-color: #7ed159;
	}

	.bc59d0d1 {
		background-color: #59d0d1;
	}

	.statistics-item ul li p {
		font-size: 24px;
		color: #ffffff;
		text-align: center;
	}

	.statistics-img {
		width: 76px;
		height: 67px;
		margin: 0 auto;
		margin-top: 43px;
	}

	.initializeTheNumber {
		border-top: 1px dashed #6666;
		margin-top: 40px;
		box-sizing: border-box;
		margin: 60px 200px 0;
	}

	.view-settings {
		width: 50%;
		float: left;
	}

	.view-settings h6 {
		margin: 0;
		font-size: 16px;
		color: #4d4d4d;
		margin-top: 20px;
	}

	.view-settings ul li {
		margin-top: 10px;
	}

	.view-settings ul li label {
		font-size: 14px;
		color: #4d4d4d;
	}

	.view-settings ul li input {
		height: 24px;
		background-color: #ffffff;
		border-radius: 2px;
		border: solid 1px #cccccc;
		outline: none;
		padding-left: 6px;
	}
.view-settings ul li span{
		color: #999;
	}
	.ml15 {
		margin-left: 15px;
	}

	.ml56 {
		margin-left: 56px;
	}

	.submit-button {
		height: 30px;
		font-size: 14px;
		border: none;
		color: #fff;
		background-color: #4dc0f7;
		border-radius: 3px;
		margin-top: 10px;
	}

	.submit-button:hover {
		background-color: #53bced;
	}
</style>
<div class="exhibit-name">
	<h6>当前：<span id="exhibit-name"></span></h6>
</div>
<div class="statistics-item" style="margin: 0 auto;width: 70.3125vw;">
	<ul style="display: grid;grid-template-columns: 1fr 1fr 1fr 1fr;grid-gap: 0 50px;">
		<li onclick="comHref()" class="bc6cbee4" style="">
			<div class="statistics-img"><img src="../../img/imgs/img1.png"></div>
			<p>公司访客流量统计</p>
		</li>
		<li onclick="proHref()" class="bc7ed159" style="">
			<div class="statistics-img"><img src="../../img/imgs/img2.png"></div>
			<p>展品访客流量统计</p>
		</li>
		<li onclick="inqHref()" class="bc59d0d1" style="">
			<div class="statistics-img"><img src="../../img/imgs/img3.png"></div>
			<p>邀请函热度统计</p>
		</li>
		<li onclick="goComments()" class="" style="background-color: #5999D1;">
			<div class="statistics-img"><img src="../../img/imgs/img4.png"></div>
			<p>访客评论管理</p>
		</li>
	</ul>
</div>
<div id="initializeTheNumber" class="initializeTheNumber" style="width: 70.3125vw;margin: 60px auto 0;">
	<div class="view-settings">
		<h6>浏览初始量设置</h6>
		<ul>
			<li>
				<label>基数最小值</label>
				<input type="number" step="1" min="0" max="999" id="readMinNumber"
				 class="ml15"  onchange="this.value=Math.max(0,Math.min(999,parseInt(this.value)))">
				<span>基数最小值0~999</span>
			</li>
			<li>
				<label>基数最大值</label>
				<input type="number" step="1" min="0" max="999" id="readMaxNumber"
				 class="ml15"  onchange="this.value=Math.max(0,Math.min(999,parseInt(this.value)))">
				<span>基数最大值0~999</span>
			</li>
			<li>
				<label>倍数</label>
				<input type="number" step="1" min="1" max="9" id="readMultiple"
				 class="ml56"  onchange="this.value=Math.max(1,Math.min(9,parseInt(this.value)))">
				<span>倍数范围1~9</span>
			</li>
		</ul>
	</div>
	<div class="view-settings">
		<h6>收藏初始量设置</h6>
		<ul>
			<li>
				<label>基数最小值</label>
				<input type="number" step="1" min="0" max="99"  id="collectMinNumber" class="ml15"
					onchange="this.value=Math.max(0,Math.min(99,parseInt(this.value)))">
				<span>基数最小值0~99</span>
			</li>
			<li>
				<label>基数最大值</label>
				<input type="number" step="1" min="0" max="99" id="collectMaxNumber" class="ml15" onchange="this.value=Math.max(0,Math.min(99,parseInt(this.value)))">
				<span>基数最大值0~99</span>
			</li>
			<li>
				<label>倍数</label>
				<input type="number" step="1" min="1" max="9" id="collectMultiple" class="ml56" onchange="this.value=Math.max(1,Math.min(9,parseInt(this.value)))">
				<span>倍数范围1~9</span>
			</li>
		</ul>
	</div>
	<button onclick="initializeTheNumberSubmit()" class="submit-button"> 确定 </button>
	<button onclick="initReadNumAndCollect()" style="margin-left: 20px;border: 1px solid #999999;color: #333333 !important;BACKGROUND: #fffffe!important;" class="submit-button"> 初始化所有展品展商浏览量基数</button>
</div>


<script>
	$("#exhibit-name").html(exihibit_name_for_pass)
	/**
	 * 公司访客流量统计
	 */
	function comHref() {
		// window.open("${eippath}/backstage/project/companyVisitorStatistics?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
		window.open("${eippath}/pages/exhibitorHtml/companyVisitorStatistics.html?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
	}
	/**
	 * 展品访客流量统计
	 */
	function proHref() {
		// window.open("${eippath}/backstage/project/productVisitorStatistics?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
		window.open("${eippath}/pages/exhibitorHtml/productVisitorStatistics.html?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
	}
	// 访客评论管理
	function goComments() {
		// window.open("${eippath}/pages/projectHtml/invitationComments.html?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
		top.addTab('访客评论管理', "${eippath}/pages/projectHtml/invitationComments.html?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass, '' , true , true)
	}
	/**
	 * 邀请函访客统计
	 */
	function inqHref() {
		window.open("${eippath}/backstage/project/inqVisitorStatistics?projectId=" + project_for_pass+'&exhibitCode='+exhibitCode_for_pass);
	}

	/**
	 *提交设置
	 */
	function initializeTheNumberSubmit() {
		let postData = {}
		var param = ['readMinNumber', 'readMaxNumber', 'readMultiple', 'collectMinNumber', 'collectMaxNumber',
			'collectMultiple'
		];
		for (var i = 0; i < 6; i++) {
			postData['exhibitSettings[' + i + '].exhitbitSettingId'] = $('#' + param[i]).attr("exhitbitSettingId");
			postData['exhibitSettings[' + i + '].exhibitCode'] = exhibitCode_for_pass;
			postData['exhibitSettings[' + i + '].taskKindCode'] = 'BrowseInitialSettings';
			postData['exhibitSettings[' + i + '].paramKind'] = 'value';
			postData['exhibitSettings[' + i + '].param'] = param[i];
			postData['exhibitSettings[' + i + '].paramMemo'] = i < 3 ? '浏览量基数设置' : '收藏量基数设置'
			postData['exhibitSettings[' + i + '].value'] = $('#' + param[i]).val();
		}
		$.ajax({
			url: '${eippath}/exhibition/saveExhibitSetting',
			dataType: "json", //返回数据类型
			type: "post",
			data: postData, //发送数据
			success: function(data) {
				if (data.state === 1) {
					$.messager.alert('提示', '保存成功！', 'info');
					showSetting("BrowseInitialSettings")
				} else {
					$.messager.alert('提示', '开启失败！', 'error');
				}
			}
		});
	}
	function initReadNumAndCollect() {
		let postData = {}
		postData["exhibitCode"]=exhibitCode_for_pass;
		postData["projectId"]=project_for_pass;
		postData["readMinNumber"]=$("#readMinNumber").val();
		postData["readMaxNumber"]=$("#readMaxNumber").val();
		postData["collectMinNumber"]=$("#collectMinNumber").val();
		postData["collectMaxNumber"]=$("#collectMaxNumber").val();
		$.ajax({
			url: '${eippath}/serveCompany/initReadNumAndCollect',
			dataType: "json", //返回数据类型
			type: "post",
			data: postData, //发送数据
			success: function(data) {
				if(data.state == 1){
					$.messager.alert('提示', '初始化成功！', 'info');
				}else{
					$.messager.alert('提示',data.message || data.msg || '初始化失败！','error');
				}
			}
		});
	}
	/**
	 * 查询是否勾选
	 */
	function showSetting(taskKindCode) {
		$.ajax({
			url: '${eippath}/exhibition/selectExhibitSetting',
			dataType: "json", //返回数据类型
			type: "post",
			data: {
				exhibitCode: exhibitCode_for_pass,
				taskKindCode: taskKindCode
			},
			success: function(data) {
				for (var i = 0; i < data.length; i++) {
					if (data[i].taskKindCode === 'BrowseInitialSettings') {
						$('#' + data[i].param).val(data[i].value);
						$('#' + data[i].param).attr("exhitbitSettingId", data[i].exhitbitSettingId);
					}
				}
			}
		});
	}
	showSetting("BrowseInitialSettings")
</script>
