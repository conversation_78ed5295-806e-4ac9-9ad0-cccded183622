<!--展位公告管理 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
		
	</style>
<table id="tt" class="easyui-datagrid"  
			data-options="rownumbers:true,singleSelect:false,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post'"
		 iconCls="icon-save"
		toolbar="#tb12">
	<thead>
		<tr>
			<th data-options="field:'ck',checkbox:true"></th>
			<th field="itemid" width="80">序号</th>
			<th field="productid" width="150">公告主题</th>
			<th field="listprice" width="80" align="right">公告时间</th>
			<th field="unitcost" width="80" align="right">撤消时间</th>
			<th field="attr1" width="150" align="center">文档附件</th>
			<th field="1" width="110" align="center">操作</th>
		</tr>
	</thead>
	<tbody>
		 <tr>
	      <td data-options="field:'ck',checkbox:true"></td>
	      <td>203</td>
	      <td>展位物流通知</td>
	      <td>2019-9-12</td>
	      <td>2019-9-20</td>
	      <td><a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#wendang').window('open')">打开</a></td>
	      <td><a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#bianji').window('open')">编辑</a></td>
	    </tr>
	    
	</tbody>
</table>









<div id="tb12">
	<a href="#" class="easyui-linkbutton" iconCls="icon-reload" plain="true" onclick="reflush()">刷新</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-add" plain="true"  >新增</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-search" plain="true"  >修改</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-no" plain="true"  >删除</a>
	
	<!--<a href="#" class="easyui-linkbutton" iconCls="icon-cut" plain="true" onclick="javascript:alert('Cut')">Cut</a>
	<a href="#" class="easyui-linkbutton" iconCls="icon-save" plain="true" onclick="javascript:alert('Save')">Save</a>-->
</div>


	
	<div id="wendang" class="easyui-window" title="文档附件" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:500px;height:auto;padding:10px;">
 
		
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('wendang').window('close')">确认</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#wendang').window('close')">关闭</a>

	
	</div>
	
	<div id="bianji" class="easyui-window" title="编辑公告" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:500px;height:auto;padding:10px;">
 
		
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('bianji').window('close')">确认</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#bianji').window('close')">关闭</a>

	
	</div>
	
<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
		});
</script>
	
	 	 