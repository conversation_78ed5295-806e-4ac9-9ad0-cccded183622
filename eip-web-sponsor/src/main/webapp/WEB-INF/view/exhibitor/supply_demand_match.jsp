<!--供求匹配结果 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}" />
<style>
	.supply_demmand_match {}
	.supply_demmand_match .title {
		height: 72px;
		background-color: #36a1db;
		display: flex;
		justify-content: center;
		align-items: center;
		color:#fff;
		font-size: 16px;
	}
	.supply_demmand_match .toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>
<div class="easyui-layout supply_demmand_match" data-options="fit:true">
    <div data-options="region:'center',border:false">
    	<!-- begin of toolbar -->
        <div id="exhibitor-cost-toolbar" class="exhibitor-cost-toolbar">
			<div class="my-toolbar-search">
				<label>选择项目</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input class="easyui-combobox"   style="width:275px;height:25px"
					   id="projectName"
					   data-options="valueField:'projectId',textField:'projectName',panelHeight:'auto'">&nbsp;
				<label>显示匹配结果</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<select class="easyui-combobox" style="width:175px;height:25px" id="showtype">
					<option value="按需方">按需方</option>
					<option value="按供方">按供方</option>

				</select>
			</div>
			<div class="title" id="title">
				按采方
			</div>
            <div class="my-toolbar-button toolbar">
            	<div style="display: none"><a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="getTableData()" plain="true">刷新</a>
            	<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-del" onclick="exhibitor_cost_mod()" plain="true">删除</a>
				</div>
				<a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download-all"  plain="true" onclick="exhibitor_boothinfo_export()">导出</a>
            </div>
            <div class="my-toolbar-search">
            	<label>甲方单位</label>&nbsp;&nbsp;&nbsp;&nbsp;
				<input class="easyui-textbox" id="supply_companyName" style="width:200px" placeholder="请输入关键词">&nbsp;&nbsp;&nbsp;&nbsp;
            	<label>乙方单位</label>&nbsp;&nbsp;
				<input class="easyui-textbox" style="width:200px" id="demand_companyName" placeholder="请输入关键词">&nbsp;&nbsp;&nbsp;&nbsp;
				<select data-options="panelHeight:'auto'" class="easyui-combobox" style="width:60px" id="matchNumRule">
					<option value="0" >&lt;</option>
					<option value="1" >&gt;</option>
					<option value="2" >=</option>

				</select>&nbsp;
				<input class="easyui-textbox" type="number" style="width:200px" id="matchNum">&nbsp;
                <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px" onclick="advancequery()">查询</a>
            </div>
        </div>
        <!-- 乙方： 需求方， 甲方： 供求 -->

        <table id="exhibitor-cost-datagrid" class="easyui-datagrid" toolbar=".exhibitor-cost-toolbar"
        	data-options="remoteSort:true,multiSort:false,rownumbers:true,singleSelect:false,pageSize:20,pagination:true,fitColumns:true,fit:true,method:'post',
        	 onClickRow: function (rowIndex, rowData) {
					var l = $(this).datagrid('getRows').length;
					for(var i = 0; i < l; i++){
						if(i != rowIndex)$(this).datagrid('unselectRow', i);
					}
				}
				">
        	<thead>
				<tr>
					<th data-options="field:'ck',checkbox:true"></th>
					<th data-options="field:'demand_companyName',width:60">乙方单位</th>
					<th data-options="field:'demand_country',width:80">乙方国别</th>
					<th data-options="field:'demand_linkman',width:120">乙方联系人</th>
					<th data-options="field:'demand_tel',width:80">乙方电话</th>
					<th data-options="field:'demand_email',width:70,align:'center'" >乙方电子邮箱</th>
					<th data-options="field:'demand_need',width:70,align:'center'" >乙方采购需求</th>
					<th data-options="field:'matchNum',width:70,align:'center'">匹配数量</th>
					<th data-options="field:'supply_companyName',width:70,align:'center'" >甲方单位</th>
					<th data-options="field:'supply_country',width:70,align:'center'" >甲方国别</th>
					<th data-options="field:'supply_linkman',width:70,align:'center'">甲方联系人</th>
					<th data-options="field:'supply_tel',width:70,align:'center'">甲方电话</th>
					<th data-options="field:'supply_email',width:70,align:'center'">甲方电子邮箱</th>
					<th data-options="field:'supply_need',width:70,align:'center'">甲方供应需求</th>
				</tr>
			</thead>
        </table>
    </div>
</div>

<script type="text/javascript">
	var exhibitor_cost_alocate_id = -1;
	var exhibitor_cost_save_id;
	var exhibitor_cost_clientId;
	var exhibitor_cost_staffId;
	var exhibitor_cost_edit_mode_jiudian;
	var exhibitor_cost_jiudian_taskId;
	var exhibitor_cost_save_serveHotelId;
	var exhibitor_cost_edit_mode_renyuan;
	var exhibitor_cost_renyuan_taskId;
	var exhibitor_cost_save_servePersonId;
	var exhibitor_cost_edit_mode_xingcheng;
	var exhibitor_cost_jxingcheng_taskId;
	var exhibitor_cost_save_tripId;
	var exhibitor_cost_edit_mode_jipiao;
	var exhibitor_cost_jipiao_taskId;
	var exhibitor_cost_save_serveAirTicketId;
	var url = (new URL(window.location.href)).searchParams
	console.log(eippath)
	var exhibitor_projectId = parseInt(url.get('projectId'))
	console.log(exhibitor_projectId)
		$(function(){
		getTableData()
		propjectname()
	});
/*	$('#showtype').change(function () {
		console.log($(this).val())
	})*/
	$('#title').text('按需方需求搜索匹配结果')
	$('#showtype').combobox({
		onChange: function(param){
			$('#title').text(param+'需求搜索匹配结果')
		}
	});
	$('#projectName').combobox({
		onChange: function(param){
			getTableData({childProjectId: parseInt(param)})
		}
	});
	function advancequery() {
		let queryParams = {
			childProjectId:  $('#projectName').combobox('getValue'),
			supply_companyName: $('#supply_companyName').textbox('getValue'),
			demand_companyName: $('#demand_companyName').textbox('getValue'),
			matchNumRule: parseInt($('#matchNumRule').combobox('getValue')),
			matchNum: $('#matchNum').textbox('getValue') ==='' ? 0 : parseInt($('#matchNum').textbox('getValue')),
		}
		getTableData(queryParams)

	}
	function propjectname() {
		$('#projectName').combobox({
			url: eippath + '/project/getProjectAndChildList',
			method: 'post',
			queryParams: {
				projectId: exhibitor_projectId
			}
		})
	}
	function getTableData(options={}) {
		var queryParams = Object.assign({}, {projectId: exhibitor_projectId}, options)
		$('#exhibitor-cost-datagrid').datagrid({
			url: eippath + '/match/supplyAndDemand',
			queryParams: queryParams,
			onLoadSuccess: function(data){

			}
		});
	}
	function exhibitor_boothinfo_export() {
		console.log(1)
		var lengths = $("#exhibitor-cost-datagrid").datagrid("getRows").length;
		console.log(lengths)
		if(lengths>0){
			location.href=eippath + '/boothInfo/exportOfBoothInfo?projectId='+exhibitor_projectId;
		}else{
			$.messager.alert('提示','暂无数据！','warning');
		}

	}
</script>






	
	 	 