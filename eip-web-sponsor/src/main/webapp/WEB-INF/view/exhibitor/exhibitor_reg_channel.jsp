<!-- 渠道基础表 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="eippath" value="${pageContext.request.contextPath}"/>
<script src="${eippath}/js/exceljs.min.js" type="text/javascript" charset="utf-8"></script>
<script src="${eippath}/js/FileSaver.js" type="text/javascript" charset="utf-8"></script>
<%@ include file="../common/reg_url.jsp" %>
<style type="text/css">
    .all_address {
        box-sizing: border-box;
        padding-bottom: 10px;
        margin: 8px 30px 0 30px;
        border-bottom: 1px solid #ccc;
    }

    .all_address h6 {
        font-size: 16px;
        font-weight: normal;
        color: #333333;
        margin-bottom: 8px;
    }

    .all_address textarea {
        width: 100%;
        border: none;
        resize: none;
        outline: none;
        font-size: 14px;
        color: #808080;
    }

    .btn-primary {
        width: 84px;
        height: 32px;
        border-radius: 6px;
        font-size: 14px;
        border: solid 1px #4c72fe;
        color: #4c72fe;
        background-color: #fff;
        outline: none;
        cursor: pointer;
    }

    .all_address > a {
        width: 84px;
        height: 30px;
        background-color: #fff;
        border-radius: 6px;
        border: solid 1px #999999;
        display: inline-block;
        text-align: center;
        line-height: 30px;
        margin-left: 20px;
        color: #666666;
    }

    .all_address > a:hover {
        color: #666666;
    }

    .address_download {
        overflow: hidden;
        margin: 12px 30px 15px;
    }

    .address_download ul li {
        float: left;
        margin-right: 20px;
    }

    .am-btn {
        font-size: 14px;
        width: 100%;
        color: #333333;
        border: none;
        background-color: #fff;
        height: 26px;
        cursor: pointer;
        outline: none;
    }

    .prompt_text {
        width: 144px;
        font-size: 16px;
        line-height: 26px;
        color: #333333;
        margin-left: 14px;
        margin-top: 29px;
    }

    .register-exhibitionReg, .setdiv, .register-releaseReg, .register-address {
        width: 288px !important;
        height: 216px !important;
        border-radius: 10px;
        border: solid 1px #4c72fe;
        transition: transform 0.6s;
    }

    .register-exhibitionReg :hover img, .setdiv :hover img, .register-releaseReg :hover img, .register-address :hover img {
        transform: scale(1.1);
    }

    .detialdiv a {
        font-size: 24px;
        color: #4c72fe;
        display: block;
        margin-top: 20px;
    }

    .detialdiv img {
        margin: auto;
        margin-top: 42px;
    }

    .tips-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: rgb(63, 164, 255);
        background-color: rgb(230, 247, 255);
        font-style: normal;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        font-weight: bold;
        margin-right: 5px;
    }
</style>
<style>
    .my-text {
        border-radius: 4px;
        width: 150px;
        height: 25px;
        box-sizing: border-box;
    }
</style>
<div class="easyui-layout" data-options="fit:true">
  <div data-options="region:'center',border:false" toolbar="#exhibitor-channel-details-toolbar">
    <!-- begin of toolbar -->
    <div id="exhibitor-channel-details-toolbar" style="width: 100%">
      <div class="my-toolbar-search" style="min-width: 1160px">
        <label>项目名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
        <input id="exhibitor-channel-details-search-projectName" class="my-text">&nbsp;
        <%--<label>身份</label>&nbsp;&nbsp;&nbsp;&nbsp;
        <input
            class="easyui-combobox"
            style="width:150px;height:25px"
            id="exhibitor-channel-details-search-targetType"
            data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,"/>&nbsp;--%>
        <label>渠道类型</label>&nbsp;&nbsp;&nbsp;&nbsp;
        <input id="exhibitor-channel-details-search-channelTypeName" class="my-text">&nbsp;
        <label>渠道名称</label>&nbsp;&nbsp;&nbsp;&nbsp;
        <input id="exhibitor-channel-details-search-channelName" class="my-text">&nbsp;
        <a href="javascript:void(0);" class="easyui-linkbutton" style="width:70px;height:25px"
           onclick="exhibitor_channel_details_search()">查询</a>
      </div>
      <div class="my-toolbar-button">
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-reload" onclick="exhibitor_channel_details_fresh()"
           plain="true">刷新</a>
        <a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-add"
           onclick="exhibitor_channel_details_set_add()"
           plain="true">新增</a>
        <a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-edit"
           onclick="exhibitor_channel_details_set_edit()"
           plain="true">修改</a>
        <a href="javascript:void(0);" class="easyui-linkbutton show-of-tab show-of-tab-0" iconCls="icon-remove"
           onclick="exhibitor_channel_details_set_delete()"
           plain="true">删除</a>
        <a href="javascript:void(0);" class="easyui-linkbutton" iconCls="icon-download-all"
           onclick="exhibitor_channel_details_export()" plain="true">导出</a>
      </div>
    </div>
    <!-- end of toolbar -->
    <table id="exhibitor-channel-details-datagrid" class="easyui-datagrid"
           data-options="rownumbers:true,pageSize:20,pagination:true,multiSort:true,fitColumns:true,fit:true,method:'post',scrollbarSize:0">
      <thead>
      <tr>
        <th data-options="field:'ck',checkbox:true"></th>
        <th data-options="field:'channelId',width:20">渠道ID</th>
        <th data-options="field:'projectName',width:40">项目名称</th>
        <th data-options="field:'targetName',width:30,align:'center'">身份</th>
        <th data-options="field:'channelTypeName',width:40" formatter="exhibitor_channel_details_channelTypeName">渠道类型</th>
        <th data-options="field:'channelName',width:40">渠道名称</th>
        <th data-options="field:'2',width:50" formatter="exhibitor_channel_details_regAddressBtn">登记地址</th>
        <%--<th data-options="field:'3',width:40" formatter="exhibitor_channel_details_dataPush">渠道获客接口设置</th>--%>
      </tr>
      </thead>
    </table>
  </div>
</div>
<!-- 登记链接 -->
<%@ include file="../common/reg_url_qr.jsp" %>
<div id="exhibitor-channel-details-set-window" class="easyui-window" toolbar="#exhibitor-channel-details-set-window-toolbar"
     data-options="closed:true,title:'渠道',modal:true,shadow: false" style="width:700px;height:auto;">
  <div id="exhibitor-channel-details-set-window-toolbar">
    <div class="my-toolbar-button">
      <a href="javascript:void(0);" hint="save" class="easyui-linkbutton" iconCls="icon-save"
         onclick="exhibitor_channel_details_set_save()" plain="true">保存</a>
      <a href="javascript:void(0);" hint="cancel" class="easyui-linkbutton" iconCls="icon-cancel"
         onclick="exhibitor_channel_details_set_cancel()" plain="true">取消</a>
    </div>
  </div>
  <div id="exhibitor-channel-details-set-panel-1" class="easyui-panel" title="基本信息"
       style="width:100%;height:auto;padding:10px;">
    <ul style="list-style:none;margin:0;padding:0;">
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        <div style="width:120px;height:25px;display:inline-block;"><label>项目</label></div>
        <input hint="projectId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
               id="exhibitor-channel-details-set-panel-projectId"
               data-options="valueField:'id',textField:'text',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true,">
      </li>
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        <div style="width:120px;height:25px;display:inline-block;"><label>适用身份</label></div>
        <input class="easyui-combobox" style="width:175px;height:25px" id="exhibitor-channel-details-set-panel-targetList"
               data-options="valueField:'targetId',textField:'targetName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
      </li>
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        <div style="width:120px;height:25px;display:inline-block;"><label>渠道类型</label></div>
        <input hint="channelTypeId" class="easyui-combobox" control="combobox" tag="0" style="width:175px;height:25px"
               id="exhibitor-channel-details-set-panel-channelTypeId"
               data-options="valueField:'channelTypeId',textField:'channelTypeName',panelHeight:'auto',panelMaxHeight:'200px',limitToList:true">
      </li>
      <li style="width:300px;height:25px;float:left;margin-right:15px;margin-bottom:5px;">
        <div style="width:120px;height:25px;display:inline-block;"><label>渠道名称</label></div>
        <input hint="channelName" class="easyui-textbox" control="textbox" tag="0" style="width:175px;height:25px"
               id="exhibitor-channel-details-set-panel-channelName">
      </li>
    </ul>
  </div>
</div>

<script type="text/javascript">
  var exhibitor_channel_details_query = {}
  var exhibitor_channel_details_config = {
    projectId: '',
    targetType: ''
  }
  setTimeout(() => {
    exhibitor_channel_details_setQuery(null, true)
    $('#exhibitor-channel-details-set-panel-channelTypeId').combobox({
      url: eippath + '/channelType/getList',
      loadFilter(data) {
        return data
      },
    })
    exhibitor_channel_details_load_project()
  }, 0);

  function exhibitor_channel_details_setQuery(query, refresh) {
    exhibitor_channel_details_query = query || {projectName: '${projectName}', targetType: '${targetType}'}
    $('#exhibitor-channel-details-search-projectName').val(exhibitor_channel_details_query.projectName)
    if (refresh) {
      exhibitor_channel_details_fresh()
    }
  }

  function exhibitor_channel_details_load_project(id, callback) {
    id = id || '#exhibitor-channel-details-inviteCodePanel-contractProjectId'
    $.ajax({
      url: eippath + '/project/selectByExhibitCode',
      data: {
        exhibitCode: exhibitCode_for_pass
      },
      type: 'post',
      success(resp) {
        try {
          const data = JSON.parse(resp.replace(/\t/g, '    '))
          $(id).combobox('loadData', data)
          callback && callback(data)
        } catch (e) {
          console.log(e, resp)
          $.messager.alert('错误', '数据加载失败！', 'error')
        }
      },
      error(err) {
        console.log(err)
        $.messager.alert('错误', '数据加载失败！', 'error')
      }
    })
  }

  function exhibitor_channel_details_load_roles(regProjectId, setDefault) {
    $("#exhibitor-channel-details-set-panel-targetList").combobox({
      url: '/RegSever/projRegSet/getRegTarget',
      queryParams: {regProjectId},
      method: 'POST',
      loadFilter(data) {
        data.data.push({
          targetId: 4,
          targetName: '展商',
        })
        return data.data
      },
      onLoadSuccess({data}) {
        if (!setDefault) return
        // const item = data[0]
        // if (!item) return
        // $(this).combobox('setValue', item.targetId)
        $(this).combobox('setValue', 4)
      }
    })
  }

  function exhibitor_channel_details_fresh() {
    exhibitor_channel_details_search(true)
  }

  function exhibitor_channel_details_search(flag) {
    $.ajax({
      url: '/eip-web-sponsor/project/getAllByExhibitCode',
      type: "post",
      data: {exhibitCode: exhibitCode_for_pass},
      datatype: 'json',
      success(resp) {
        try {
          window.AllProjectByExhibitCode = JSON.parse(resp.replace(/\t/g, '    '))
        } catch (e) {
          console.log(e, resp)
          window.AllProjectByExhibitCode = []
          $.messager.alert('警告', '项目名存在坏字符！', 'warning')
        }
        const $channelTypeName = $('#exhibitor-channel-details-search-channelTypeName')
        if (flag) $channelTypeName.val("");
        $('#exhibitor-channel-details-datagrid').datagrid({
          url: '/eip-web-sponsor/channel/getChannelTargetList',
          queryParams: {
            channelTypeName: $channelTypeName.val(),
            channelName: $('#exhibitor-channel-details-search-channelName').val(),
            projectName: $('#exhibitor-channel-details-search-projectName').val(),
            exhibitCode: exhibitCode_for_pass,
            // targetType: $('#exhibitor-channel-details-search-targetType').combobox('getValue'),
            actorCode: 'Trader'
          },
          onLoadSuccess(data) {
            if (exhibitor_channel_details_query.targetType && exhibitor_channel_details_query.projectName) {
              const rows = data.rows
              if (rows.length) {
                channel_details_open2d(1, ``, rows[0].projectId, '', 1, rows[0].targetType, 'exhibit')
                exhibitor_channel_details_query = {}
              }
            }
          }
        });
      },
      error(err) {
        $.messager.alert('数据加载失败！')
      },
    })
  }

  function exhibitor_channel_details_channelTypeName(val, row) {
    const isColor = row.channelTypeId === 4 || row.channelTypeId === 1 || !row.channelTypeId
    const t = !val && row.channelTypeId === 4 ? '扫码即到场' : val
    return isColor ? `<span style="color: #4c72fe;">\${t}</span>` : t
  }

  function exhibitor_channel_details_regAddressBtn(val, row) {
    let {regEnglish, channelName, channelTypeId, channelId, projectId, targetType} = row
    channelId = channelId || '``'
    channelTypeId = channelTypeId || '``'
    channelName = channelName || ''
    return '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_open2d(' + channelTypeId + ',' + channelId + ',' + projectId + ',\'' + channelName + '\', 1, ' + targetType + ',\'exhibit\')">中文版地址</a>' + '<i style="margin-right: 40px"/>' +
      (regEnglish ? '<a href="javascript:void(0);" style="color:blue;font-weight:bold;" onclick="channel_details_open2d(' + channelTypeId + ',' + channelId + ',' + projectId + ',\'' + channelName + '\', 2, ' + targetType + ',\'exhibit\')">英文版地址</a>' : '')
  }

  function exhibitor_channel_details_get_project_logo(projectId, version, targetType = 4) {
    function queryData2image(src) {
      const inner = src => {
        if (!src) return '/RegSever/RegPage/images/top-lucency.png'
        const prefix = location.origin
        src = src.replace(/\\/g, '/')
        if (/^(https?:)?\/\//.test(src)) return src
        if (src.startsWith('/')) return prefix + src
        return prefix + '/image/' + src
      }
      const url = inner(src)
      if (!url) return ''
      try {
        return new URL(url).host.indexOf('aliyuncs.com') !== -1
          ? `/eip-web-sponsor/upload/ossOut?ossUrl=\${encodeURIComponent(url)}`
          : url
      } catch (e) {
        return url
      }
    }
    let QRCodeLogo = ''
    $.ajax({
      url: '/eip-web-sponsor/api/project/getProjectLogo',
      data: {projectId, targetType},
      type: "post",
      async: false,
      success(data) {
        if (data.state !== 1) return
        const buyerLogoKey = version === 2 ? 'buyerRegEnLogo' : 'buyerRegCnLogo'
        const result = data.data || {}
        QRCodeLogo = queryData2image(result[buyerLogoKey] || result['exhibitionLogo'])
      },
      error(err) {
        console.warn(err)
      }
    })
    return queryData2image(QRCodeLogo)
  }
  function exhibitor_channel_details_get_cached_logoFn() {
    const cache = {}
    return function (pid, version, target) {
      let key = pid + '-' + version
      if (target) key += '-' + target
      if (cache[key]) return cache[key]
      return (cache[key] = exhibitor_channel_details_get_project_logo(pid, version, target))
    }
  }
  function exhibitor_channel_details_export() {
    const orgnum = org_num_for_pass;
    const eid = exhibitCode_for_pass;
    const computerURI = variableReg + '/web-reg-server/pc/exhibit-apply.html'
    const mobileURI = variableReg + '/web-reg-server/mobile/exhibit-apply-m.html'
    const logoFn = exhibitor_channel_details_get_cached_logoFn()

    const $table = $('#exhibitor-channel-details-datagrid')
    const rows = $table.datagrid('getSelections')
    if (!rows.length) {
      const data = ($table.datagrid('getData') || {}).rows || []
      if (!data.length) return $.messager.alert('提示', '没有可导出的数据', 'warning')
      return $.messager.confirm('提示', '是否导出全部数据？', r => r && main(data))
    }
    main(rows)


    async function exports(list) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('邀请渠道')
      let nextIndex = 0
      const head = getValuesFromRow(null)
      worksheet.addRow(head).height = 30
      const cnQRIndex = head.indexOf('中文版二维码')
      const enQRIndex = head.indexOf('英文版二维码')
      nextIndex = 1
      for (const index in list) {
        const item = list[index]
        const {mobileURL, logo, mobileURLEN, logoEN} = item
        const row = worksheet.addRow(getValuesFromRow(item, index))
        row.height = 150
        const imgId = workbook.addImage({
          base64: await getQRCodeDataURL(mobileURL, logo),
          extension: 'png',
        })
        worksheet.addImage(imgId, {
          tl: {col: cnQRIndex, row: nextIndex},
          br: {col: cnQRIndex + 1, row: nextIndex + 1},
          editAs: 'oneCell'
        })
        if (mobileURLEN && logoEN) {
          const imgId = workbook.addImage({
            base64: await getQRCodeDataURL(mobileURLEN, logoEN),
            extension: 'png',
          })
          worksheet.addImage(imgId, {
            tl: {col: enQRIndex, row: nextIndex},
            br: {col: enQRIndex + 1, row: nextIndex + 1},
            editAs: 'oneCell'
          })
        }
        nextIndex++
      }
      worksheet.columns = head.map((_, index) => (index > 1 ? {width: 30} : {}))
      // 将所有单元格文本设置为居中
      worksheet.eachRow({ includeEmpty: true }, function(row, rowNumber) {
        row.eachCell({ includeEmpty: true }, function(cell, colNumber) {
          if (rowNumber === 1) {
            cell.font = { bold: true }
          }
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });
      workbook.xlsx.writeBuffer().then(buffer=>saveAs(new Blob([buffer], {type: "application/octet-stream"}), "自定义渠道.xlsx"))
    }

    function getRegEnglishByPid(pid) {
      const project = window.AllProjectByExhibitCode.find(item => item.projectId === pid)
      return project && project.regEnglish
    }

    function makeURLSearch(row, version) {
      const {channelTypeId, channelId, projectId, targetType} = row
      return `?EID=\${eid}&target=\${targetType}&orgnum=\${orgnum}&pid=\${projectId}&version=\${version}&cid=\${channelId}&ctid=\${channelTypeId}`;
    }

    function getQRCodeDataURL(content, logo) {
      const option = {
        src: logo,
        text: content,
        correctLevel: 0,
        width: 800,
        height: 800,
        imgWidth: 200,
        imgHeight: 200
      }
      const $el = $('<div/>').qrcode(option)
      const canvas = $el.find('canvas')[0]
      if (!logo) {
        const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
        $el.remove()
        return dataURL
      }
      // append logo to canvas
      const image = new Image()
      image.src = logo
      let _resolve
      const promise = new Promise(resolve => _resolve = resolve)
      image.onload = () => {
        const ctx = canvas.getContext('2d')
        ctx.drawImage(image, (option.width - option.imgWidth) / 2, (option.height - option.imgHeight) / 2, option.imgWidth, option.imgHeight)
        const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
        _resolve(dataURL)
        $el.remove()
      }
      image.onerror = err => {
        console.error(err)
        const dataURL = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, "")
        _resolve(dataURL)
        $el.remove()
      }
      return promise
    }

    function getValuesFromRow(row, index) {
      // 返回表头
      if (!row) {
        return [
          '序号',
          '渠道ID',
          '项目名称',
          '身份',
          '渠道类型',
          '渠道名称',
          '中文版手机端登记地址',
          '中文版PC端登记地址',
          '中文版二维码',
          '英文版手机端登记地址',
          '英文版PC端登记地址',
          '英文版二维码',
        ]
      }
      const {
        channelId,
        projectName,
        targetName,
        channelTypeName,
        channelName,
        computerURL,
        mobileURL,
        computerURLEN,
        mobileURLEN
      } = row
      return [
        index + 1,
        channelId,
        projectName,
        targetName,
        channelTypeName,
        channelName,
        mobileURL,
        computerURL,
        '',
        mobileURLEN,
        computerURLEN,
        '',
      ]
    }

    function getLogoFn() {
      const cache = {}
      return function (pid, version) {
        const key = pid + '-' + version
        if (cache[key]) return cache[key]
        getLogoImg(pid, version, 4)
        return (cache[key] = queryData2image(QRCodeLogos))
      }
    }

    function main(rows) {
      const list = rows.map(item => {
        const temp = {...item}
        const regEnglish = getRegEnglishByPid(temp.projectId)
        temp.computerURL = computerURI + makeURLSearch(temp, 1)
        temp.mobileURL = mobileURI + makeURLSearch(temp, 1)
        temp.logo = logoFn(temp.projectId, 1, 4)
        if (regEnglish) {
          temp.computerURLEN = computerURI + makeURLSearch(temp, 2)
          temp.mobileURLEN = mobileURI + makeURLSearch(temp, 2)
          temp.logoEN = logoFn(temp.projectId, 2, 4)
        }
        if (temp.channelTypeId === 4) temp.channelTypeName = '扫码即到场'
        return temp
      })
      exports(list)
    }
  }

  function exhibitor_channel_details_set_getOne(channelId, callback) {
    $.ajax({
      url: '/eip-web-sponsor/channel/getMoreById',
      data: {channelId},
      type: 'post',
      success({data, state}) {
        if (state !== 1) return $.messager.alert('错误', '数据加载失败！', 'error')
        callback && callback(data)
      },
      error(err) {
        console.log(err)
        $.messager.alert('错误', '数据加载失败！', 'error')
      }
    })
  }

  function exhibitor_channel_details_set_add() {
    $('#exhibitor-channel-details-set-panel-targetList').combobox('clear').combobox('disable')
    $('#exhibitor-channel-details-set-panel-channelTypeId').combobox('clear')
    exhibitor_channel_details_load_project('#exhibitor-channel-details-set-panel-projectId')
    $("#exhibitor-channel-details-set-panel-channelName").textbox("clear");
    const $project = $("#exhibitor-channel-details-set-panel-projectId")
    const projectId = $project.combobox("getValue") || project_for_pass;
    $project.combobox("setValue", projectId).combobox('enable');
    exhibitor_channel_details_load_roles(projectId, true)
    $('#exhibitor-channel-details-set-window').window('open');
  }

  function exhibitor_channel_details_set_edit() {
    const rows = $('#exhibitor-channel-details-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '未选中内容！', 'warning')
    if (rows.length !== 1) return $.messager.alert('提示', '每次只能编辑一条数据！', 'warning')
    const {channelId} = rows[0]
    if (!channelId) return $.messager.alert('提示', '主办方渠道不允许编辑！', 'warning')
    exhibitor_channel_details_set_getOne(channelId, data => {
      const {projectId, channelTypeId, channelName, targetList} = data
      if (targetList && targetList.length > 1) {
        return $.messager.confirm('提示', '该渠道已被多角色身份使用，是否立刻跳转至邀请渠道设置进行手动设置?！', r => {
          if (!r) return
          top.addTab('邀请渠道设置', eippath + '/backstage/project/channel_set', '', 0)
        })
      }
      exhibitor_channel_details_load_project('#exhibitor-channel-details-set-panel-projectId')
      exhibitor_channel_details_load_roles(projectId, true)
      const $formWindow = $('#exhibitor-channel-details-set-window')
      $("#exhibitor-channel-details-set-panel-projectId").combobox("setValue", projectId).combobox('disable')
      $("#exhibitor-channel-details-set-panel-targetList").combobox('setValue', targetList[0] || '1').combobox('disable')
      $("#exhibitor-channel-details-set-panel-channelTypeId").combobox("setValue", channelTypeId);
      $("#exhibitor-channel-details-set-panel-channelName").textbox("setValue", channelName)
      $formWindow.window('open').data({channelId});
    })
  }

  function exhibitor_channel_details_set_delete() {
    const rows = $('#exhibitor-channel-details-datagrid').datagrid('getSelections')
    if (!rows.length) return $.messager.alert('提示', '未选中内容！', 'warning')
    if (rows.length !== 1) return $.messager.alert('提示', '每次只能删除一条数据！', 'warning')
    const {channelId} = rows[0]
    if (!channelId) return $.messager.alert('提示', '主办方渠道不允许删除！', 'warning')
    exhibitor_channel_details_set_getOne(channelId, data => {
      const {targetList} = data
      if (targetList && targetList.length > 1) {
        return $.messager.confirm('提示', '该渠道已被多角色身份使用，是否立刻跳转至邀请渠道设置进行手动设置?！', r => {
          if (!r) return
          top.addTab('邀请渠道设置', eippath + '/backstage/project/channel_set', '', 0)
        })
      }
      $.messager.confirm('提示', '确定删除吗？', function (r) {
        if (r) {
          $.ajax({
            url: eippath + '/channel/delete',
            dataType: "json",
            type: "post",
            data: {channelId},
            success(data) {
              if (data.state === 1) {
                $.messager.alert('提示', '删除成功！', 'info');
                exhibitor_channel_details_search(true)
              } else {
                $.messager.alert('提示', '删除失败！', 'error');
              }
            },
            error() {
              $.messager.alert('提示', '数据发送失败！', 'error');
            }
          });
        }
      });
    })
  }

  function exhibitor_channel_details_set_save() {
    const $formWindow = $('#exhibitor-channel-details-set-window')
    var projectId = $("#exhibitor-channel-details-set-panel-projectId").combobox("getValue");
    var channelTypeId = $("#exhibitor-channel-details-set-panel-channelTypeId").combobox("getValue");
    var channelName = $("#exhibitor-channel-details-set-panel-channelName").textbox("getValue");
    var targetList = $("#exhibitor-channel-details-set-panel-targetList").combobox("getValue");
    if (!projectId) return $.messager.alert('提示', '项目不能为空！', 'warning')
    if (!targetList) return $.messager.alert('提示', '适用身份不能为空！', 'warning')
    if (!channelTypeId) return $.messager.alert('提示', '渠道类型不能为空！', 'warning')
    if (!channelName) return $.messager.alert('提示', '渠道名称不能为空！', 'warning')
    if (+channelTypeId === 4) {
      return $.messager.confirm('提示', '是否确认使用扫码及到场渠道？如启用闸机/PDA请避免使用该渠道。', f => f && _request())
    }
    _request()

    function _request() {
      var postData = {};
      postData['channelId'] = $formWindow.data('channelId') || '';
      postData['projectId'] = projectId;
      postData['channelTypeId'] = channelTypeId;
      postData['channelName'] = channelName;
      postData['targetList'] = [targetList];
      $.messager.progress()
      $.ajax({
        url: '/eip-web-sponsor/channel/save',
        dataType: "json",	//返回数据类型
        type: "post",
        contentType: "application/json",
        data: JSON.stringify(postData),	//发送数据
        success(data) {
          $.messager.progress('close')
          if (data.state === 1) {
            $.messager.alert('提示', '保存成功！', 'info');
            $formWindow.window('close', true).removeData('channelId');
            exhibitor_channel_details_search(true)
          } else {
            $.messager.alert('提示', '保存失败！', 'error');
          }
        },
        error() {
          $.messager.alert('提示', '数据发送失败！', 'error');
          $.messager.progress('close')
        }
      });
    }
  }

  function exhibitor_channel_details_set_cancel() {
    $('#exhibitor-channel-details-set-window').window('close', true);
  }

</script>