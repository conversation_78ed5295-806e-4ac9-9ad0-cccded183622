package com.eip.facade.sponsor.vo;

import com.eip.common.entity.BaseEntity;

import java.util.List;

/**
 * 省市vo
 *
 * @author: ZzzHhYyy
 * @Date: 2019-12-20 9:28
 */
public class AreaVo extends BaseEntity {
    /**
     * 省份
     */
    private String province;
    /***
     * 市
     */
    private List<String> citys;

    public String getProvince() {
        return province;
    }

    public AreaVo setProvince(String province) {
        this.province = province;
        return this;
    }

    public List<String> getCitys() {
        return citys;
    }

    public AreaVo setCitys(List<String> citys) {
        this.citys = citys;
        return this;
    }
}
