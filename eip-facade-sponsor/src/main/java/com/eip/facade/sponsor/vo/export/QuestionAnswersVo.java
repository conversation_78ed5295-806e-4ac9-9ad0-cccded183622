package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @USER: zwd
 * @DATE: 2024-11-11 17:45
 * @DESCRIPTION: 问卷导出
 */
@HeadRowHeight(25)
@ContentRowHeight(30)
@ColumnWidth(15)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QuestionAnswersVo implements Serializable {

    private static final long serialVersionUID = 8370788229377131812L;


    /**
     * 问题名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "问卷C端显示原始问题")
    private String questionName;
    /**
     * 问题Id
     */
    @ExcelProperty(value = "问题代号")
    private String questionId;

    /**
     * 问题类型
     */
    @ExcelProperty(value = "问题类型")
    private String questionTypeName;

    /**
     * 是否必填
     */
    @ExcelProperty(value = "必填")
    private String requiredName;

    /**
     * 答案名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "问卷C端显示原始答案")
    private String answersName;

    /**
     * 答案类型名称
     */
    @ExcelProperty(value = "答案类型")
    private String answersTypeName;

    /**
     * 答案Id
     */
    @ExcelProperty(value = "答案代号")
    private String answersId;


    /**
     * 输入项内容
     */
    @ExcelProperty(value = "输入项内容")
    private String inputName;


}
