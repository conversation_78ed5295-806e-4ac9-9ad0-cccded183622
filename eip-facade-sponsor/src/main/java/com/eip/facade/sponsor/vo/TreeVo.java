package com.eip.facade.sponsor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-01-16 18:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreeVo implements Serializable {

    private static final long serialVersionUID = -8113905405538039301L;

    private Integer id;

    private String code;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long longId;

    private String text;

    private String type;

    private Integer state;

    private List<TreeVo> children = Lists.newArrayList();
}
