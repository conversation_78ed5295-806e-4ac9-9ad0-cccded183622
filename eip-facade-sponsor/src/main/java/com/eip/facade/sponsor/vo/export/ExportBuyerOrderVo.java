package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExportBuyerOrderVo implements Serializable {

    @ExcelProperty(value = "序号")
    private Integer serialNumber;

    @ExcelProperty(value = "项目")
    private String projectName;

    @ExcelProperty(value = "姓名")
    private String buyerName;

    @ExcelProperty(value = "登记号码")
    private String regNum;

    @ExcelProperty(value = "胸卡号")
    private String barCode;

    @ExcelProperty(value = "订单ID")
    private String orderTradeId;

    @ExcelProperty(value = "付款金额")
    private BigDecimal totalFree;
    /**
     * 0 未支付   1 支付  2 取消支付 3 已退款（已申请退款）
     */
    @ExcelProperty(value = "支付状态")
    private String orderPayState;

    @ExcelProperty(value = "订单创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelProperty(value = "订单完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timeEnd;

    @ExcelProperty(value = "微信订单ID")
    private String transactionId;

}
