/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-06-28 9:22
 * @description：采购需求
 */
package com.eip.facade.sponsor.vo;

import cn.hutool.core.date.DateUnit;
import com.eip.common.util.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-06-28 9:22
 * @description：采购需求
 */
@Data
public class RequirementVo implements Serializable {
    private static final long serialVersionUID = -5982248817388136374L;
    /**
     * 采购需求ID
     */
    private Integer requireId;
    /**
     * 项目ID
     */
    private Integer projectId;
    /**
     * 行业ID
     */
    private Integer tradeId;
    /**
     * 产品类型
     */
    private Integer productTypeBase;
    /**
     * 求购标题
     */
    private String title;
    /**
     * 求购描述
     */
    private String description;
    /**
     * 求购数量
     */
    private Integer quantity;
    /**
     * 产品图片1
     */
    private String productPic1;
    /**
     * 产品图片2
     */
    private String productPic2;
    /**
     * 产品图片3
     */
    private String productPic3;
    /**
     * 产品关键词
     */
    private String productKeyword;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkmanPhone;
    /**
     * 联系人邮箱
     */
    private String linkmanEmail;
    /**
     * 联络方式保密程度
     * MERCHANT_OPEN：商户公开   ALL_USERS_OPEN：所有用户公开    KEEP_SECRET:保密
     */
    private String contactSecretDegree;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 有效期至
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validUntil;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Integer checkTime;
    /**
     * 会员ID
     */
    private Integer zzyuserId;

    /**
     * 审核状态 1 草稿  2 审核  3 未审核通过
     */
    private Integer checkState;

    /**
     * 未审核通过
     */
    private Integer checkMemo;

    /**
     * 审核Id
     */
    private Integer checkId;

    /**
     * 收藏数
     */
    private Integer collectNum;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 国别
     */
    private String country;

    /**
     * 行业名称
     */
    private String tradeName;
    /**
     * 行业名称英文
     */
    private String tradeNameEn;
    /**
     * 产品类型名称
     */
    private String productTypeBaseName;

    /**
     * 产品类型名称英文
     */
    private String productTypeBaseNameEn;
    /**
     * 查询我收藏的求购
     */
    private Boolean favorite;

    /**
     * 发布时间别名
     */
    private String publicTimeAlias;

    /**
     * 有效期别名
     */
    private String validUntilAlias;

    /**
     * 剩余天数
     */
    private Long remainDay;

    /**
     * 会员中的公司logo
     */
    private String logo;

    /**
     * 会员中的公司图片
     */
    private String pic;

    /**
     * 用户名
     */
    private String userName;



    public Long getRemainDay() {
        if(this.getValidUntil()!=null){
            remainDay = cn.hutool.core.date.DateUtil.between(new Date(),this.getValidUntil(), DateUnit.DAY);
        }
        return remainDay;
    }

    public void setRemainDay(Long remainDay) {
        this.remainDay = remainDay;
    }

    public String getPublicTimeAlias() {
        if(publishTime!=null){
            publicTimeAlias = DateUtil.date2Str(publishTime, "yyyy/MM/dd");
        }
        return publicTimeAlias;
    }

    public void setPublicTimeAlias(String publicTimeAlias) {
        this.publicTimeAlias = publicTimeAlias;
    }

    public String getValidUntilAlias() {
        if(validUntil!=null){
            validUntilAlias = DateUtil.date2Str(validUntil, "yyyy/MM/dd");
        }
        return validUntilAlias;
    }

    public void setValidUntilAlias(String validUntilAlias) {
        this.validUntilAlias = validUntilAlias;
    }
}
