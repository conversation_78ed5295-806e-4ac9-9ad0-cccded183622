package com.eip.facade.sponsor.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BuyerRegVo implements Serializable {
	/**
	 * 观众id
	 */
	private Integer buyerId;
	/**
	 * 机构号
	 */
	private Integer orgNum;
	/**
	 * 展会代号
	 */
	private String exhibitCode;
	/**
	 * 登记姓名
	 */
	private String buyerName;
	/**
	 * 性别
	 */
	private String sex;
	/**
	 * 手机
	 */
	private String mobile;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 胸卡号
	 */
	private String barCode;
	/**
	 * 登记时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date regTime;
	/**
	 * 项目id
	 */
	private Integer projectId;
	/**
	 * 是否入场
	 */
	private Boolean isEntrance;
	/**
	 * 入场时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date entranceDate;
	/**
	 * 数据类型，观众为 A  进馆证数据为E
	 */
	@Column(name = "f_type")
	private String type;
	/**
	 * 登记号码
	 */
	private String regNum;

	/**
	 * 登记方式 1 手机  2 邮箱
	 */
	private Integer regType;
	/**
	 * 登记版本  1 中文  2 英文
	 */
	private Integer version;
	/**
	 * 审核状态 0 待审核  1 审核通过（默认为1）  2 已驳回  3 已拒绝
	 */
	private Integer checkState;
	/**
	 * 会员id
	 */
	private Integer zzyuserId;
	/**
	 * 观众证件list
	 */
	private List<BuyerCertificateVo> buyerCertificateList;
}
