package com.eip.facade.sponsor.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsSendVo implements Serializable {
    /**
     * 数据id
     */
    private  Integer id;
    /**
     * 姓名
     */
    private  String name;
    /**
     * 性别
     */
    private String  sex;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 会员号
     */
    private String userName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SmsSendVo smsSendVo = (SmsSendVo) o;
        return Objects.equals(id, smsSendVo.id) &&
                Objects.equals(mobile, smsSendVo.mobile);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, mobile);
    }
}
