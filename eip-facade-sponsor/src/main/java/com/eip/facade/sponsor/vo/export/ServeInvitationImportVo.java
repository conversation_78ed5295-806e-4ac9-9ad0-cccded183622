package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.eip.common.easyExcel.annotation.ExcelDynamicField;
import lombok.Data;

import java.io.Serializable;

/**
 * @USER: zwd
 * @DATE: 2024-07-10 13:58
 * @DESCRIPTION: 邀请函
 */
@Data
public class ServeInvitationImportVo implements Serializable {

    private static final long serialVersionUID = -5099413347068450975L;

    @ExcelProperty(value = "项目")
    @ExcelDynamicField(field = "f_project_name",fieldName ="项目" ,sort = 0,demoData = "杭州科技会展")
    private String projectName;

    @ExcelProperty(value = "公司名称")
    @ExcelDynamicField(field = "f_exhibitor_name",fieldName ="公司名称" ,sort = 1,demoData = "杭州展之信息技术有限公司")
    private String exhibitorName;

    @ExcelProperty(value = "公司英文名称")
    @ExcelDynamicField(field = "f_exhibitor_en_name",fieldName ="公司英文名称",sort =2,demoData = "Exhibition Information")
    private String exhibitorEnName;

    @ExcelProperty(value = "公司地址")
    @ExcelDynamicField(field = "f_exhibitor_address",fieldName ="公司地址",sort =3,demoData = "浙江省杭州经济技术开发区白杨街道")
    private String exhibitorAddress;

    @ExcelProperty(value = "公司英文地址")
    @ExcelDynamicField(field = "f_exhibitor_en_address",fieldName ="公司英文地址",sort =4,demoData = "Hangzhou Economic and Technological Development Zone")
    private String exhibitorEnAddress;

    @ExcelProperty(value = "公司电话")
    @ExcelDynamicField(field = "f_exhibitor_phone",fieldName ="公司电话",sort =5,demoData = "010-652321")
    private String exhibitorPhone;

    @ExcelProperty(value = "公司座机")
    @ExcelDynamicField(field = "f_exhibitor_tel",fieldName ="公司座机",sort =6,demoData = "010-652321")
    private String exhibitorTel;

    @ExcelProperty(value = "邮编")
    @ExcelDynamicField(field = "f_postcode",fieldName ="邮编",sort =7,demoData = "311100")
    private String postcode;

    @ExcelProperty(value = "传真")
    @ExcelDynamicField(field = "f_fax",fieldName ="传真",sort =8,demoData = "010-652321")
    private String fax;

    @ExcelProperty(value = "网址")
    @ExcelDynamicField(field = "f_website",fieldName ="网址",sort =9,demoData = "http://www.expo2c.com/new/pc_officialWeb/index.html")
    private String website;

    @ExcelProperty(value = "展位号")
    @ExcelDynamicField(field = "f_booth_num",fieldName ="展位号",sort =10,ignore = true,demoData = "A-1")
    private String boothNum;

    @ExcelProperty(value = "职务")
    @ExcelDynamicField(field = "f_position",fieldName ="职务",sort =11,demoData = "销售经理")
    private String position;

    @ExcelProperty(value = "英文职务")
    @ExcelDynamicField(field = "f_post_en",fieldName ="英文职务",sort =12,demoData = "sales manager")
    private String postEn;

    @ExcelProperty(value = "国籍")
    @ExcelDynamicField(field = "f_nationality",fieldName ="国籍",sort =13,demoData = "China")
    private String nationality;

    @ExcelProperty(value = "中文姓名")
    @ExcelDynamicField(field = "f_name_cn",fieldName ="中文姓名",sort =14,demoData = "张三")
    private String nameCn;

    @ExcelProperty(value = "姓名全拼")
    @ExcelDynamicField(field = "f_pingying_name",fieldName ="姓名全拼",sort =15,demoData = "zhangsan")
    private String pingyingName;

    @ExcelProperty(value = "姓氏拼音")
    @ExcelDynamicField(field = "f_pingying_surname",fieldName ="姓氏拼音",sort =16,demoData = "zhang")
    private String pingyingSurname;

    @ExcelProperty(value = "名字拼音")
    @ExcelDynamicField(field = "f_pingying_firstname",fieldName ="名字拼音",sort =17,demoData = "san")
    private String pingyingFirstname;

    @ExcelProperty(value = "英文姓")
    @ExcelDynamicField(field = "f_surname",fieldName ="英文姓",sort =18,demoData = "zhang")
    private String surname;

    @ExcelProperty(value = "英文名")
    @ExcelDynamicField(field = "f_name",fieldName ="英文名",sort =19,demoData = "san")
    private String name;

    @ExcelProperty(value = "性别")
    @ExcelDynamicField(field = "f_sex",fieldName ="性别",sort =20,dropDown = true,demoData = "男")
    private String sex;

    @ExcelProperty(value = "出生日期")
    @ExcelDynamicField(field = "f_borth_data",fieldName ="出生日期",sort =21,demoData = "1987-05-06")
    private String borthData;

    @ExcelProperty(value = "出生地")
    @ExcelDynamicField(field = "f_birthplace",fieldName ="出生地",sort =22,demoData = "杭州")
    private String birthplace;

    @ExcelProperty(value = "护照类型")
    @ExcelDynamicField(field = "f_passport_type",fieldName ="护照类型",sort =23,dropDown = true,dropDownValue = {"因公类型","因私类型"},demoData = "因公类型")
    private String passportType;

    @ExcelProperty(value = "护照号")
    @ExcelDynamicField(field = "f_passport_num",fieldName ="护照号",sort =24,demoData = "*********")
    private String passportNum;

    @ExcelProperty(value = "护照签发地")
    @ExcelDynamicField(field = "f_passport_issuing_place",fieldName ="护照签发地",sort =25,demoData = "上海")
    private String passportIssuingPlace;

    @ExcelProperty(value = "护照签发日期")
    @ExcelDynamicField(field = "f_passport_issuing_date",fieldName ="护照签发日期",sort =26,demoData = "2020-06-06")
    private String passportIssuingDate;

    @ExcelProperty(value = "护照有效期")
    @ExcelDynamicField(field = "f_passport_expiration_date",fieldName ="护照有效期",sort =27,demoData = "2030-06-06")
    private String passportExpirationDate;

    @ExcelProperty(value = "大使馆")
    @ExcelDynamicField(field = "f_embassy",fieldName ="大使馆",sort =28,dropDown = true,dropDownValue = {"上海","广州","北京","其他"},demoData = "上海")
    private String embassy;

    @ExcelProperty(value = "手机号")
    @ExcelDynamicField(field = "f_mobile",fieldName ="手机号",sort =29,demoData = "13100000000")
    private String mobile;

    @ExcelProperty(value = "邮箱")
    @ExcelDynamicField(field = "f_email",fieldName ="邮箱",sort =30,demoData = "<EMAIL>")
    private String email;

    @ExcelProperty(value = "联系人")
    @ExcelDynamicField(field = "f_contacts",fieldName ="联系人",sort =31,demoData = "张三")
    private String contacts;

    @ExcelProperty(value = "入境日期")
    @ExcelDynamicField(field = "f_go_date",fieldName ="入境日期",sort =32,demoData = "2023-06-12")
    private String goDate;

    @ExcelProperty(value = "离境日期")
    @ExcelDynamicField(field = "f_back_date",fieldName ="离境日期",sort =33,demoData = "2023-06-18")
    private String backDate;

    @ExcelProperty(value = "预计停留天数")
    @ExcelDynamicField(field = "f_estimated_of_stay",fieldName ="预计停留天数",sort =34,demoData = "6")
    private String estimatedOfStay;
}
