package com.eip.facade.sponsor.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 会员等级返回前端实体
 * @author： hfl
 * @date：2021-10-09 13:49
 * @File: MemberGradeVo
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MemberGradeVo implements Serializable {

    private Long memberGradeId;

    private String memberGradeName;

    private Boolean newMemberDefaultGrade;

    private Boolean oldMemberDefaultGrade;

    private String defaultNewMember;

    private String defaultOldMember;
}
