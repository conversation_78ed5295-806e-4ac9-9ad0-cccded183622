package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExportCertificateConvertibleVo implements Serializable {


    @ExcelProperty(value = "序号")
    private Integer serialNumber;
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String personName;
    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄")
    private Integer age;
    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    private String sex;
    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位")
    private String companyName;
    /**
     * 职称
     */
    @ExcelProperty(value = "职称")
    private String jobTitle;
    /**
     * 是否来自基层 true 是 false 否
     */
    @ExcelProperty(value = "来自基层")
    private String fromBasic;
    /**
     * 单位所在地
     */
    @ExcelProperty(value = "单位所在地")
    private String companyAddress;
    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;
    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;
    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;
    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目")
    private String projectName;

    /**
     * 观众证件id
     */
    @ExcelProperty(value = "证件ID")
    private String buyerCertificateId;
    /**
     * 证件名称
     */
    @ExcelProperty(value = "证件名称")
    private String certificateName;
    /**
     * 已兑换
     */
    @ExcelProperty(value = "是否兑换")
    private String isConvertible;
    /**
     * 兑换时间
     */
    @ExcelProperty(value = "兑换时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date convertibleTime;
    /**
     * 证件所属人
     */
    @ExcelProperty(value = "证件所属人")
    private String buyerName;
    /**
     * 证件所属登记号码
     */
    @ExcelProperty(value = "证件所属人登记号码")
    private String regNum;
    /**
     * 证件购买人
     */
    @ExcelProperty(value = "证件购买人")
    private String orderBuyerName;
    /**
     * 证件购买人登记号码
     */
    @ExcelProperty(value = "证件购买人登记号码")
    private String orderRegNum;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改时间
     */
    @ExcelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
