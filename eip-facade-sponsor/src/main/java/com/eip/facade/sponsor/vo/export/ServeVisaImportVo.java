package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.eip.common.easyExcel.annotation.ExcelDynamicField;
import lombok.Data;

import java.io.Serializable;

/**
 * @USER: zwd
 * @DATE: 2024-07-10 19:16
 * @DESCRIPTION:
 */
@Data
public class ServeVisaImportVo implements Serializable {

    private static final long serialVersionUID = -2519734025661583246L;

    @ExcelProperty(value = "项目")
    @ExcelDynamicField(field = "f_project_name",fieldName ="项目" ,sort = 0,demoData = "杭州科技会展")
    private String projectName;

    @ExcelProperty(value = "公司名称")
    @ExcelDynamicField(field = "f_company_name",fieldName ="公司名称" ,sort = 0,demoData = "杭州展之信息技术有限公司")
    private String companyName;

    @ExcelProperty(value = "姓名")
    @ExcelDynamicField(field = "f_name",fieldName ="姓名",sort = 1,demoData = "张三")
    private String name;

    @ExcelProperty(value = "性别")
    @ExcelDynamicField(field = "f_sex_id",fieldName ="性别",sort = 2,dropDown = true,demoData = "男")
    private String sexId;

    @ExcelProperty(value = "职务")
    @ExcelDynamicField(field = "f_position_id",fieldName ="职务",sort = 3,demoData = "销售经理")
    private String positionId;

    /**
     * 1 入境
     * 2 出入证
     */
    @ExcelProperty(value = "签证类型")
    @ExcelDynamicField(field = "f_visa_type",fieldName ="签证类型",sort = 4,dropDown = true,dropDownValue = {"入境签证","出入证签证"},demoData = "出入证签证")
    private String visaType;


    @ExcelProperty(value = "签证国家")
    @ExcelDynamicField(field = "f_visa_country",fieldName ="签证国家",sort = 5,demoData = "美国")
    private String visaCountry;

    @ExcelProperty(value = "手机")
    @ExcelDynamicField(field = "f_mobile",fieldName ="手机",sort = 6,demoData = "13100000000")
    private String mobile;

    @ExcelProperty(value = "电子邮箱")
    @ExcelDynamicField(field = "f_email",fieldName ="电子邮箱",sort = 7,demoData = "<EMAIL>")
    private String email;

    @ExcelProperty(value = "单位电话")
    @ExcelDynamicField(field = "f_work_phone",fieldName ="单位电话",sort = 8,demoData = "056-612354")
    private String workPhone;

    @ExcelProperty(value = "家庭住址")
    @ExcelDynamicField(field = "f_home_adress",fieldName ="家庭住址",sort = 9,demoData = "杭州钱塘区")
    private String homeAdress;

    @ExcelProperty(value = "现住地址")
    @ExcelDynamicField(field = "f_now_adress",fieldName ="现住地址",sort = 10,demoData = "杭州钱塘区")
    private String nowAdress;

    @ExcelProperty(value = "民族")
    @ExcelDynamicField(field = "f_nation_id",fieldName ="民族",sort = 11,demoData = "汉")
    private String nationId;

    @ExcelProperty(value = "身份证号")
    @ExcelDynamicField(field = "f_id_number",fieldName ="身份证号",sort = 12,demoData = "412636199506084521")
    private String idNumber;

    @ExcelProperty(value = "身份证签发机关")
    @ExcelDynamicField(field = "f_id_issue",fieldName ="身份证签发机关",sort = 13,demoData = "杭州钱塘区派出所")
    private String idIssue;

    @ExcelProperty(value = "文化程度")
    @ExcelDynamicField(field = "f_education_id",fieldName ="文化程度",sort = 14,dropDown = true,demoData = "本科")
    private String educationId;

    @ExcelProperty(value = "备注")
    @ExcelDynamicField(field = "f_memo",fieldName ="备注",sort = 15,dropDown = true,demoData = "无")
    private String memo;

    @ExcelProperty(value = "姓名拼音")
    @ExcelDynamicField(field = "f_pinyin_name",fieldName ="姓名拼音",sort = 16,demoData = "zhansan")
    private String pinyinName;


    @ExcelProperty(value = "护照号码")
    @ExcelDynamicField(field = "f_pass_number",fieldName ="护照号码",sort = 17,demoData = "ED4125469")
    private String passNumber;


    @ExcelProperty(value = "签发地点")
    @ExcelDynamicField(field = "f_pass_adress",fieldName ="签发地点",sort = 18,demoData = "上海")
    private String passAdress;


    @ExcelProperty(value = "签发日期")
    @ExcelDynamicField(field = "f_pass_time",fieldName ="签发日期",sort = 19,demoData = "2022-05-20")
    private String passTime;


    @ExcelProperty(value = "护照有效期")
    @ExcelDynamicField(field = "f_pass_validity",fieldName ="护照有效期",sort = 20,demoData = "2025-06-20")
    private String passValidity;


    @ExcelProperty(value = "曾去国家")
    @ExcelDynamicField(field = "f_country_gone",fieldName ="曾去国家",sort = 21,demoData = "迪拜,法国")
    private String countryGone;


    @ExcelProperty(value = "出生日期")
    @ExcelDynamicField(field = "f_born_data",fieldName ="出生日期",sort = 22,demoData = "1992-06-23")
    private String bornData;


    @ExcelProperty(value = "出生地")
    @ExcelDynamicField(field = "f_born_adress",fieldName ="出生地",sort = 23,demoData = "浙江杭州")
    private String bornAdress;

    @ExcelProperty(value = "旧护照")
    @ExcelDynamicField(field = "f_have_old_pass",fieldName ="旧护照",sort = 24,dropDown = true,dropDownValue = {"有","无"},demoData = "有")
    private String haveOldPass;

    @ExcelProperty(value = "旧护照信息")
    @ExcelDynamicField(field = "f_old_pass",fieldName ="旧护照信息",sort = 25,demoData = "护照更新了名字")
    private String oldPass;

    @ExcelProperty(value = "拒签历史")
    @ExcelDynamicField(field = "f_refus_history",fieldName ="拒签历史",sort = 26,demoData = "2022年拒签美国")
    private String refusHistory;


}
