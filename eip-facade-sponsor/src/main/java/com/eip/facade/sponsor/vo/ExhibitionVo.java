package com.eip.facade.sponsor.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 返回前端的实体
 */
@Data
public class ExhibitionVo implements Serializable {

	private static final long serialVersionUID = -1931654120650507495L;

	private String exhibitCode;
	private String exihibitName;
	private Integer exhibitKindId;
	private String exhibitKindName;
	private String approveDate;
	private String exhibitPlace;
	private String startDate;
	private String endDate;
	private Integer exhibitState;
	private String yearMonth;
	private Integer exhibitLevel;
	private Boolean isable;
	private String exhibitLogo;
	private String exhbitLayoutImg;
	private Boolean release;
	/**
	 * 展位预订保留天数
	 */
	private Integer preorderReserveDays;
	/**
	 * 展位合同保留天数
	 */
	private Integer contractReserveDays;
	private Integer f_org_num;
	private Boolean releaseReg;
	private String  regCutoffDate;
	private Boolean exhibitionReg;
	private Integer projectId;
	private String exhibitAbbr;
	/**
	 * 展会模板号，目前定义（1内展，2外展）
	 */
	private Integer exhibitTemplate;

	/**
	 * 是否启用展位图
	 */
	private Boolean openExhibitMap;

	/**
	 * 是否开启会员自助服务
	 */
	private Boolean enableMemberService;

	/**
	 * 国家
	 */
	private String country;
	/**
	 * 省份
	 */
	private String province;
	/**
	 * 城市
	 */
	private String city;

	private String serverIp;
	private String serverPort;
	private String url;

	/**
	 * 信息门户类别代号 exhibition 展会 website 行业网站
	 */
	private String  kindCode;

	/**
	 * 币种
	 */
	private String foreignCurrency;

	/**
	 * 外币币种名称
	 */
	private String foreignCurrencyName;

}
