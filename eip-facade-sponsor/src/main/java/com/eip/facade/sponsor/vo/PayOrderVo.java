package com.eip.facade.sponsor.vo;

import com.eip.common.util.PublicUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayOrderVo implements Serializable {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 退款单id
     */
    private String refundId;

    /**
     * 订单状态  1 支付状态  2 退款状态
     */
    private Integer orderState;

    /**
     * 支付状态、代号
     */
    private String stateCode;

    /**
     * 状态名称
     */
    private String stateName;

    /**
     * 支付完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 退款完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    /**
     * 支付方式：0  微信 1 支付宝 2 网银 3 手动到款
     */
    private String payType;

    /**
     * 支付方式名称
     */
    private String payTypeName;

    public String getPayTypeName() {
        if(StringUtils.isBlank(payType)) return "";
        payTypeName = PublicUtil.getPayTypeName(payType);
        return payTypeName;
    }
}
