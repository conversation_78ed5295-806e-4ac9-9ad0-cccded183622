/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-04-11 11:19
 * @description：展位图设置显示项
 */
package com.eip.facade.sponsor.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-04-11 11:19
 * @description：展位图设置显示项
 */
@Data
public class BoothMapShowSettingVo implements Serializable {

    private static final long serialVersionUID = 2494759055565388634L;

    private Long id;
    /**
     * clientPreOrderPage 展商定展界面
     * sponsorManagerPage 主办方管理界面
     * largeScreenPage 大屏轮播界面
     * onLineBoothMapPage 线上展位导图
     */
    private String showInterface;
    /**
     * mouseMove  鼠标移入 显示展商信息
     * boothMapShow  展位图上显示展商信息
     * boothShowOriPrice 展位显示原价
     */
    private String showMotion;
    private Boolean showFlag;

    private String exhibitCode;

}
