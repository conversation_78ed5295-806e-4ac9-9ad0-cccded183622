/**
 * @company ：杭州展之科技
 * <AUTHOR>
 * @date ：Created in 2022-12-01 17:36
 * @description：
 */
package com.eip.facade.sponsor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-12-01 17:36
 * @description：
 */
@Data
public class IntegralTypeVo implements Serializable {
    private static final long serialVersionUID = 6164454194726944763L;

    @Id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long integralTypeId;

    /**
     * 积分类型名称
     */
    private String integralName;

    /**
     * 有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validTime;

    /**
     * 积分有效周期类型  0代表无限制  1 代表每1年清空 数字以此类推
     */
    private Integer validCycleType;

    /**
     * 机构号
     */
    private Integer orgNum;
}
