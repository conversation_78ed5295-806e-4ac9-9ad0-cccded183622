package com.eip.facade.sponsor.vo;

import lombok.Data;

import java.io.Serializable;
/**
 * 参与任务的客户
 *
 */
@Data
public class TaskClientVo implements Serializable{
	
	/**
	 * 客户id
	 */
	private Integer clientId;
	/**
	 * 公司名称
	 */
	private String companyName;
	/**
	 * 公司名称 英文
	 */
	private String companyNameEn;
	/**
	 * 任务id
	 */
	private Integer taskId;
	/**
	 * 任务明细id
	 */
	private Integer taskDtlId;
	/**
	 * 任务明细状态
	 */
	private Integer confirm;

	/**
	 * 项目名称
	 */
	private String projectName;
	/**
	 * 项目名称 英文
	 */
	private String projectNameEn;

	/**
	 * 展位号
	 */
	private String boothCode;

	/**
	 * 任务项目Id
	 */
	private Integer taskProjectId;

	/**
	 * 任务名称
	 */
	private String taskTopic;
	/**
	 * 任务名称 英文
	 */
	private String taskTopicEn;

	
}

