package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExportPunchEventDetailVo implements Serializable {

    @ExcelProperty(value = "序号",index = 0)
    private Integer serialNumber;
    /**
     * 会员名称
     */
    @ExcelProperty(value = "打卡会员号",index = 1)
    private String zzyUserName;
    /**
     * 打卡观众名称
     */
    @ExcelProperty(value = "打卡观众",index = 2)
    private String buyerName;
    /**
     * 登记号码
     */
    @ExcelProperty(value = "登记号码",index = 3)
    private String regNum;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称",index = 4)
    private String eventClockName;

    /**
     * 打卡点名称
     */
    @ExcelProperty(value = "打卡点",index = 5)
    private String punchPointName;

    /**
     * 打卡方式
     */
    @ExcelProperty(value = "打卡方式",index = 6)
    private String punchModeName;

    /**
     * 打卡点类型  1 必打卡  2 可选打卡   3 独立打卡点（不计入总打卡次数）
     */
    @ExcelProperty(value = "打卡点类型",index = 7)
    private String punchPointTypeName;

    /**
     * 打卡时间
     */
    @ExcelProperty(value = "打卡时间",index = 8)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date punchTime;

}
