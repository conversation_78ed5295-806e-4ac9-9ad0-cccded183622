package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.eip.common.enums.CertificateCodeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExportCertificateCheckVo implements Serializable {

    @ExcelProperty(value = "序号")
    private Integer serialNumber;
    /**
     * 观众证件id
     */
   /* @ExcelProperty(value = "证件ID")
    private String buyerCertId;*/
    /**
     * 胸卡号
     */
    @ExcelProperty(value = "胸卡号")
    private String certCode;
    /**
     * 核销数据来源
     */
    @ExcelProperty(value = "核销点类型")
    private String checkFromName;
    /**
     * 证件领取人姓名
     */
    @ExcelProperty(value = "姓名")
    private String buyerName;
    /**
     * 领取人登记号码
     */
    @ExcelProperty(value = "登记号码")
    private String regNum;
    /**
     * 证件所属项目名称
     */
    @ExcelProperty(value = "项目")
    private String projectName;
    /**
     * 证件名称
     */
    @ExcelProperty(value = "证件名称")
    private String certName;
    /**
     * 核销点名称
     */
    @ExcelProperty(value = "核销点")
    private String checkPointName;
    /**
     * 核销人名称
     */
    @ExcelProperty(value = "核销人")
    private String checkerName;
    /**
     * 核销时间
     */
    @ExcelProperty(value = "核销时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

}
