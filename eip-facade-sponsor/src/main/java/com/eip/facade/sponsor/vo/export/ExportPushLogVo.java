package com.eip.facade.sponsor.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExportPushLogVo implements Serializable {

	@ExcelProperty(value = "序号")
	private Integer serialNumber;

	@ExcelProperty(value = "项目")
	private String projectName;

	@ExcelProperty(value = "推送类型")
	private String certTypeName;

	@ExcelProperty(value = "推送证件")
	private String certName;

	@ExcelProperty(value = "胸卡号")
	private String barCode;

	@ExcelProperty(value = "第三方服务商")
	private String apiTypeName;

	@ExcelProperty(value = "推送时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@ExcelProperty(value = "推送结果")
	private String pushStateName;

	@ExcelProperty(value = "推送备注")
	private String responseData;


}
