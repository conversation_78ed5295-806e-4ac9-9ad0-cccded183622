package com.eip.facade.sponsor.vo;

import com.eip.facade.sponsor.entity.Awoke;

/**
 * @author: ZzzHhYyy
 * @Date: 2020-01-02 14:36
 */
public class AwokeVo extends Awoke {
    /**
     * 操作员名称
     */
    private String operName;

    private String exhibitCode;

    private Integer projectId;

    public String getOperName() {
        return operName;
    }

    public void setOperName(String operName) {
        this.operName = operName;
    }

    public String getExhibitCode() {
        return exhibitCode;
    }

    public void setExhibitCode(String exhibitCode) {
        this.exhibitCode = exhibitCode;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }
}
