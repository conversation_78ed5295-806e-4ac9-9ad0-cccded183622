package com.eip.common.aop;

import com.alibaba.fastjson.JSON;
import com.eip.common.exception.NoNeedHandlerException;
import com.eip.common.exception.monitor.bean.MonitorInfo;
import com.eip.common.exception.monitor.enums.ExceptionLevel;
import com.eip.common.exception.monitor.ExceptionMonitorHelper;
import com.eip.common.util.request.NetworkUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@PropertySource("classpath:system.properties")
public class LogAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    @Value("${system.log.aopEnable}")
    private boolean aopEnable;

    @Value("${sys.caveat.mail.mailEnable}")
    private boolean mailEnable;

    //切点，这里如果需要配置多个切入点用“||”
    @Pointcut("execution(* com.eip.web.*.controller..*.*(..))")
    public void controllerAspect() {
    }

    /**
     * 前置通知
     *
     * @param joinPoint 切点
     */
    @Before("controllerAspect()")
    public void doBefore(JoinPoint joinPoint) {
        if (logger.isInfoEnabled() && aopEnable) {
            logger.info("before " + joinPoint);
        }
    }

    /**
     * 环绕通知,使用在方法aspect()上注册的切入点
     *
     * @param joinPoint
     * @return
     */
    @Around("controllerAspect()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object proceed = joinPoint.proceed();
        long end = System.currentTimeMillis();
        if (logger.isInfoEnabled() && aopEnable) {
            logger.info("around " + joinPoint + "\tUse time : " + (end - start) + " ms!");
        }
        return proceed;
    }

    /**
     * 后置通知
     *
     * @param joinPoint 切点
     */
    @After("controllerAspect()")
    public void after(JoinPoint joinPoint) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = NetworkUtil.getIpAddress(request);
        try {
            if (logger.isInfoEnabled() && aopEnable) {
                logger.info("请求方法:" + (joinPoint.getTarget().getClass().getName() + "." + joinPoint.getSignature().getName() + "()") + ".");
                logger.info("请求IP:" + ip);
            }
        } catch (Exception e) {
            logger.error("==后置通知异常==");
            logger.error("异常信息:{}", e.getMessage());
        }
    }

    /**
     * 配置后置返回通知
     *
     * @param joinPoint
     */
    @AfterReturning("controllerAspect()")
    public void afterReturn(JoinPoint joinPoint) {
        if (logger.isInfoEnabled() && aopEnable) {
            logger.info("afterReturn " + joinPoint);
        }
    }

    /**
     * 异常通知 用于拦截记录异常日志
     *
     * @param joinPoint
     * @param e
     */
    @AfterThrowing(pointcut = "controllerAspect()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = NetworkUtil.getIpAddress(request);
        StringBuilder params = new StringBuilder();
        if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {
            for (int i = 0; i < joinPoint.getArgs().length; i++) {
                params.append(JSON.toJSONString(joinPoint.getArgs()[i])).append(";");
            }
        }
        //发送邮件
        if (mailEnable && !(e instanceof NoNeedHandlerException)) {
            MonitorInfo monitorInfo = new MonitorInfo();
            monitorInfo.setMethodName(request.getRequestURI());
            monitorInfo.setParams(request.getParameterMap());
            monitorInfo.setException((Exception) e);
            monitorInfo.setExceptionLevel(ExceptionLevel.SeriousError);
            ExceptionMonitorHelper.monitor(monitorInfo);
        }
        e.printStackTrace();
        logger.error("异常代码:" + e.getClass().getName());
        logger.error("异常信息:" + e.getMessage());
        logger.error("异常方法:" + (joinPoint.getTarget().getClass().getName() + "." + joinPoint.getSignature().getName() + "()") + ".");
        logger.error("请求IP:" + ip);
//        logger.error("请求参数:" + params);
//        logger.error("异常方法:{}异常代码:{}异常信息:{}参数:{}", joinPoint.getTarget().getClass().getName() +
//                joinPoint.getSignature().getName(), e.getClass().getName(), e.getMessage(), params.toString());
    }
}
