package com.eip.common.aop;

import com.alibaba.fastjson.JSONObject;
import com.eip.common.annotation.CheckSubmitForm;
import com.eip.common.util.ReflectionUtils;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.cach.CacheObject;
import com.eip.common.util.cache.MapCache;
import com.eip.common.util.page.wrapper.WrapMapper;
import com.eip.common.util.page.wrapper.Wrapper;
import com.eip.common.util.request.RequestHolder;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpSession;
import java.lang.reflect.Constructor;
import java.util.HashMap;

/**
 * 防止表单二次提交
 *
 * @author: ZzzHhYyy
 * @Date: 2019-12-23 13:46
 */
@Component
@Aspect
public class ReSubmitAspect {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final MapCache SINGLE = MapCache.single();

    private static final String KEY_ID = "formId";

    //切点，这里如果需要配置多个切入点用“||”
    @Pointcut("@annotation(com.eip.common.annotation.CheckSubmitForm)")
    public void annotationAspect() {
    }

    @Around("annotationAspect()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        CheckSubmitForm checkSubmitForm = methodSignature.getMethod().getAnnotation(CheckSubmitForm.class);
        if (checkSubmitForm == null) {
            return joinPoint.proceed();
        } else {
            Object[] args = joinPoint.getArgs();
            String formId = null;
            String userId = null;
            for (Object arg : args) {
                if (arg == null) continue;
                if (StringUtils.isEmpty(formId)) {
                    JSONObject jsonObject = null;
                    try {
                        jsonObject = JSONObject.parseObject(JSONObject.toJSONString(arg));
                    }catch (Exception e){}
                    if (jsonObject != null && !StringUtils.isEmpty(jsonObject.getString(KEY_ID))) {
                        formId = jsonObject.getString(KEY_ID);
                    } else {
                        formId = formId == null ? methodSignature.getDeclaringTypeName()+"."+methodSignature.getMethod().getName() : formId;
                    }
                }
                if (StringUtils.isEmpty(userId)) {
                    HttpSession session = RequestHolder.getSession();
                    /*Object operator = session.getAttribute("operator");
                    if (operator!=null){
                        HashMap<String, Object> objAttr = ReflectionUtils.getObjAttr(operator);
                        userId= String.valueOf( objAttr.get("operatorId"));
                    }*/
                    Object operatorId = session.getAttribute("operatorId");
                    userId = operatorId != null ? String.valueOf(operatorId) : null;

                    if(StringUtils.isBlank(userId)){
                        userId=session.getId();
                    }
                }
            }
            if (!StringUtils.isEmpty(formId) && !StringUtils.isEmpty(userId)) {
                Class<?> returnType = ((MethodSignature) joinPoint.getSignature()).getMethod().getReturnType();
                String redisKey = ((MethodSignature) joinPoint.getSignature()).getMethod().getName().concat(":").concat(userId).concat(":").concat(formId);
                logger.info("resubmit {},{}", redisKey, checkSubmitForm.delaySeconds());
                CacheObject cacheObject = SINGLE.getCacheObj(redisKey);
                if (cacheObject != null) {
                    Constructor<?> declaredConstructor = returnType.getDeclaredConstructor();
                    declaredConstructor.setAccessible(true);
                    if (declaredConstructor.newInstance() instanceof AjaxResponse)
                        return AjaxResponse.failure("操作过于频繁，请稍后重试");
                    if (declaredConstructor.newInstance() instanceof Wrapper)
                        return WrapMapper.error("操作过于频繁，请稍后重试");
                }
                SINGLE.cleanExpired();
                SINGLE.set(redisKey, 1, checkSubmitForm.delaySeconds());
            } else {
                logger.error("重复表单提交检验 失效: 参数错误:fromId-" + formId + ",uicId-" + userId);
            }
            return joinPoint.proceed();
        }
    }
}

