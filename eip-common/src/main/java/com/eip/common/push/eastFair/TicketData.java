package com.eip.common.push.eastFair;

import lombok.Data;

import java.io.Serializable;
@Data
public class TicketData implements Serializable {

	private String message;
	private String code;
	private Long ticketId;
	/*private String name;
	private String mobile;
	private String idNumber;
	private String rfid;
	private String cardNo;
	private String photoUrl;
	private String numberType;
	private String company;
	private int ticketStatus;
	private String ticketTypeName;
	private List<TicketTime> ticketTime;
	private int faceStatus;
	private String country;
	private String province;
	private int registerChannel;*/
}
