package com.eip.common.cach;

import java.io.Serializable;

/**
 * @USER: zwd
 * @DATE: 2023-04-03 14:30
 * @DESCRIPTION:
 */
public class CacheObject implements Serializable {

    private static final long serialVersionUID = 8428740471327837877L;

    private String key;
    private Object value;
    private long expired;

    public CacheObject(String key, Object value, long expired) {
        this.key = key;
        this.value = value;
        this.expired = expired;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public long getExpired() {
        return expired;
    }

    public void setExpired(long expired) {
        this.expired = expired;
    }
}
