package com.eip.web.vip.controller.api;

import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.WxReqParamDto;
import com.eip.facade.vip.entity.ShortLink;
import com.eip.facade.vip.service.ShortLinkService;
import com.eip.facade.vip.service.WxAuthorService;
import com.eip.web.vip.controller.common.CommonController;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-09-13 13:35
 * @DESCRIPTION:
 */
@RestController
@RequestMapping(value = "/api/shortLink")
public class ApiShortLinkController extends CommonController {
    @Autowired
    private WxAuthorService wxAuthorService;
    @Autowired
    private ShortLinkService shortLinkService;

    /**
     * 获取短链
     * @return
     */
    @RequestMapping(value = "/getShortLink",method = RequestMethod.POST)
    public AjaxResponse getShortLink(WxReqParamDto wxReqParamDto){
        String host = StringUtils.trim(wxReqParamDto.getHost());
        if(StringUtils.isBlank(host)) return AjaxResponse.failure("域名为空");
        String originalLink = StringUtils.trim(wxReqParamDto.getLinkUrl());
        if(StringUtils.isBlank(originalLink)) return AjaxResponse.failure("链接为空");
        Integer orgNum = wxReqParamDto.getOrgNum();
        if(orgNum == null) return AjaxResponse.failure("机构号为空");
        return wxAuthorService.getShortLink(wxReqParamDto);
    }


    /**
     * 获取小程序分享二维码
     * @param wxReqParamDto
     * @return
     */
    @RequestMapping(value = "/getWxUnlimitedQRCode",method = RequestMethod.POST)
    public AjaxResponse getUnlimitedQRCode(WxReqParamDto wxReqParamDto){
        Integer orgNum = wxReqParamDto.getOrgNum();
        if(orgNum == null) return AjaxResponse.failure("机构号为空");
        if(Objects.isNull(wxReqParamDto.getWxQrCodeDto())) return AjaxResponse.failure("获取二维码参数为空");
        return wxAuthorService.generatorWxMiniQrCode(wxReqParamDto);
    }

    /**
     * 根据短链获取原始链接
     * @param wxReqParamDto
     * @return
     */
    @RequestMapping(value = "/getOriginalLink",method = RequestMethod.POST)
    public AjaxResponse getOriginalLink(WxReqParamDto wxReqParamDto){
        String queryShortUrl = wxReqParamDto.getQueryShortUrl();
        if(StringUtils.isBlank(queryShortUrl)) return AjaxResponse.failure("短链参数为空");
        Map<String,Object> paramMap =  Maps.newHashMapWithExpectedSize(6);
        paramMap.put("shortLink",queryShortUrl);
        List<ShortLink> shortLinkList = shortLinkService.queryShortUrlByMd5(paramMap);
        paramMap.clear();
        paramMap.put("originalLink",null);
        if(CollectionUtils.isNotEmpty(shortLinkList)){
            String originalLinkUrl = shortLinkList.get(0).getOriginalLink();
            paramMap.put("originalLink",originalLinkUrl);
        }
        return AjaxResponse.success(paramMap);
    }

}
