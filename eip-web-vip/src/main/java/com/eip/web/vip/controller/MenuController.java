package com.eip.web.vip.controller;

import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.MenuDto;
import com.eip.facade.vip.entity.Menu;
import com.eip.facade.vip.entity.MenuList;
import com.eip.facade.vip.service.MenuService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.facade.vip.vo.ExhibitorCenterMenuVo;
import com.eip.facade.vip.vo.MenuVo;
import com.eip.facade.vip.vo.TraderTemplateRefVo;
import com.eip.web.vip.controller.common.CommonController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-06-20 17:20
 * @DESCRIPTION:
 */
@RestController
@RequestMapping(value = "/menu")
public class MenuController extends CommonController {

    @Autowired
    private MenuService menuService;


    /**
     * 获取个人会员中心菜单
     * @return
     */
    @RequestMapping(value = "/getPersonalCenterMenus",method = RequestMethod.POST)
    public AjaxResponse getPersonalCenterMenus(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        List<MenuVo> menuVoList = menuService.getPersonalCenterMenus(menuDto);
        return AjaxResponse.success(menuVoList);
    }

    /**
     * 获取企业会员中心菜单
     * @return
     */
    @RequestMapping(value = "/getEnterpriseCenterMenus",method = RequestMethod.POST)
    public AjaxResponse getEnterpriseCenterMenus(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        List<MenuVo> menuVoList = menuService.getEnterpriseCenterMenus(menuDto);
        return AjaxResponse.success(menuVoList);
    }


    /**
     * 获取会员中心菜单
     * @return
     */
    @RequestMapping(value = "/getMenus",method = RequestMethod.POST)
    public AjaxResponse getMenus(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        menuDto.setFilter10000OrgNum(userInfo.getFocusOrgNum());
        List<Menu> menus = menuService.getMenu(menuDto);
        MenuList menuList = new MenuList();
        menuList.setMenus(menus);
        return AjaxResponse.success(menuList);
    }

    /**
     * 获取展商自助服务中心菜单
     * @param menuDto
     * @return
     */
    @RequestMapping(value = "/getExhibitorCenterMenu",method = RequestMethod.POST)
    public AjaxResponse getExhibitorCenterMenu(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        if(menuDto.getExhibitorCenterTemplateId() == null) return AjaxResponse.create(ParamExceptionEnum.PARAM_MISS);
        List<ExhibitorCenterMenuVo> exhibitorCenterMenuList =  menuService.getExhibitorCenterMenu(menuDto);
        return AjaxResponse.success(exhibitorCenterMenuList);
    }

    /**
     * 获取展商自助服务中心菜单内容
     * @param menuDto
     * @return
     */
    @RequestMapping(value = "/getExhibitorCenterContent",method = RequestMethod.POST)
    public AjaxResponse getExhibitorCenterContent(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        if(menuDto.getExhibitorCenterMenuId() == null) return AjaxResponse.create(ParamExceptionEnum.PARAM_MISS);
        ExhibitorCenterMenuVo exhibitorCenterMenuVo =  menuService.getExhibitorCenterContent(menuDto);
        return AjaxResponse.success(exhibitorCenterMenuVo);
    }

    /**
     * 获取展商自助服务中心工作台设置
     * @param menuDto
     * @return
     */
    @RequestMapping(value = "/getExhibitorCenterWorkBenchContent",method = RequestMethod.POST)
    public AjaxResponse getExhibitorCenterWorkBenchContent(MenuDto menuDto){
        AuthLoginRespVo userInfo = getUserInfo();
        if(Objects.isNull(userInfo)) return AjaxResponse.create(CommonResponseStateEnum.NOT_LOGIN);
        if(menuDto.getExhibitorCenterMenuId() == null) return AjaxResponse.create(ParamExceptionEnum.PARAM_MISS);
        List<TraderTemplateRefVo> traderTemplateRefVoList = menuService.getExhibitorCenterWorkBenchContent(menuDto);
        return AjaxResponse.success(traderTemplateRefVoList);
    }

}
