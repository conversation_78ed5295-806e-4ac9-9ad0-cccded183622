package com.eip.web.vip.controller.api;

import com.eip.common.enums.*;
import com.eip.common.exception.BusinessException;
import com.eip.common.exception.SystemAuthException;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.exception.enums.SystemAuthExceptionEnum;
import com.eip.common.util.RedisUtil;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.request.RequestUtil;
import com.eip.facade.vip.dto.AuthLoginReqDto;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.dto.api.ApiMemberLoginTokenDto;
import com.eip.facade.vip.entity.ZzyUser;
import com.eip.facade.vip.service.AuthService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.web.vip.async.ZzyUserIntegralAsync;
import com.eip.web.vip.controller.common.CommonController;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-12-27 9:10
 * @DESCRIPTION: 登录、注册、登出、修改个人信息
 */
@RestController
@RequestMapping(value = "/api/auth")
public class ApiAuthController extends CommonController {

    @Autowired
    private AuthService authService;
    @Autowired
    private ZzyUserIntegralAsync zzyUserIntegralAsync;



    /**
     * 登录接口
     * @return
     */
    @RequestMapping(value = "/login",method = RequestMethod.POST)
    public AjaxResponse login(AuthLoginReqDto authLoginReqDto){
        String loginType = authLoginReqDto.getLoginType();
        Integer orgNum = authLoginReqDto.getOrgNum();
        if(StringUtils.isBlank(loginType))
            throw new SystemAuthException(SystemAuthExceptionEnum.LOGIN_TYPE_EMPTY);

        if(!SignTypeEnum.contain(loginType))
            throw new SystemAuthException(SystemAuthExceptionEnum.UNKNOWN_LOGIN_TYPE_EMPTY);

        if(orgNum == null)
            throw new BusinessException(ParamExceptionEnum.ORG_NUM_EMPTY);

        ZzyUser zzyUser = null;

        //注意：如果自己系统内部调用则不需要前端传递过来该标识 如果第三方前端对接该接口 则系统默认为第三方前端调取
        authLoginReqDto.setThirdLoginFlag(true);

        //用户账号
        String username = authLoginReqDto.getUsername();
        //账号密码登录
        if(SignTypeEnum.ACCOUNT_LOGIN.getCode().equals(loginType)) {
            String passWord = authLoginReqDto.getPassword();
            if(StringUtils.isBlank(username) || StringUtils.isBlank(passWord))
                throw new SystemAuthException(SystemAuthExceptionEnum.ACCOUNT_PWD_EMPTY);

            AjaxResponse resp = authService.accountLogin(authLoginReqDto);
            if(resp.getState() !=1) return resp;

            zzyUser = (ZzyUser)resp.getData();
        }
        //手机、邮箱登录
        if(SignTypeEnum.CAPTCHA_LOGIN.getCode().equals(loginType)){
            if(StringUtils.isBlank(username))
                throw new SystemAuthException(SystemAuthExceptionEnum.MOBILE_OR_EMAIL_EMPTY);

            String code = authLoginReqDto.getCode();
            if(StringUtils.isBlank(code))
                throw new SystemAuthException(SystemAuthExceptionEnum.CAPTCHA_CODE_EMPTY);

            AjaxResponse resp =  authService.captchaLogin(authLoginReqDto);
            if(resp.getState() !=1) return resp;

            zzyUser = (ZzyUser)resp.getData();
        }
        if(Objects.isNull(zzyUser)) throw new SystemAuthException(SystemAuthExceptionEnum.MEMBER_NOT_EXISTS);
        zzyUser.setFocusOrgNum(orgNum);
        zzyUser.setF_project_id(authLoginReqDto.getProjectId());
        zzyUser.setThirdLoginFlag(authLoginReqDto.getThirdLoginFlag());
        AjaxResponse resp = authService.buildAccessTokenToRedis(null, zzyUser, requestHead());
        if(resp.getState() ==1){
            //发放积分
            Map<String,Object> integralMap = Maps.newHashMapWithExpectedSize(20);
            integralMap.put("requestIntegralUrl", RequestUtil.getSystemUrl());
            zzyUserIntegralAsync.sendLoginIntegral(integralMap,zzyUser,authLoginReqDto);
        }
        return resp;
    }

    /**
     * 获取登录客户端类型
     * @return
     */
    private Map<String,Object> requestHead(){
        Map<String, Object> redisHashMap = Maps.newHashMapWithExpectedSize(20);
        redisHashMap.put("loginClientType",SignClientTypeEnum.PC.getCode());
        if(RequestUtil.isMobileClient()) redisHashMap.put("loginClientType",SignClientTypeEnum.MOBILE.getCode());
        return redisHashMap;
    }

    /**
     * 退出登录接口
     * @return
     */
    @RequestMapping(value = "/logout",method = RequestMethod.POST)
    public AjaxResponse logout(){
        AjaxResponse resp = getTokenFromRequest();
        if(resp.getState()!=1) return resp;
        authService.logout(String.valueOf(resp.getData()));
        return AjaxResponse.success();
    }


    /**
     * 通过token返回用户登录信息
     * @return
     */
    @RequestMapping(value = "/getUserInfo",method = RequestMethod.POST)
    public AjaxResponse getUserInfo(ProjectDto projectDto){
        //获取登录token
        AjaxResponse resp = getTokenFromRequest();
        if(resp.getState()!=1) return resp;
        projectDto.setAuthToken(String.valueOf(resp.getData()));
        //根据token获取登录用户信息
        AuthLoginRespVo userInfo = getMemberLoginInfo(String.valueOf(resp.getData()));
        return authService.getMemberLoginUserInfo(userInfo,projectDto);
    }

    /**
     * 会员注册统一接口
     * @param authLoginReqDto
     * @memo 新注册进来的用户默认为：0 未知用户  2 展商  4 观众
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public AjaxResponse registered(AuthLoginReqDto authLoginReqDto) {
        Integer orgNum = authLoginReqDto.getOrgNum();
        if(orgNum == null)
            throw new BusinessException(ParamExceptionEnum.ORG_NUM_EMPTY);

        String sources = authLoginReqDto.getSources();
        if(StringUtils.isBlank(sources))
            throw new SystemAuthException(SystemAuthExceptionEnum.SOURCES_EMPTY);

        if(!RegisterSourceEnum.checkContains(sources))
            throw new SystemAuthException(SystemAuthExceptionEnum.UNKNOWN_SOURCES_EMPTY);

        //第三方授权，查询到没有授权过，需要注册并验证码验证码
        Integer socialType = authLoginReqDto.getSocialType();//授权类型
        String loginType = authLoginReqDto.getLoginType();
        if(StringUtils.isNotBlank(loginType)
                && SignTypeEnum.SOCIAL_LOGIN.getCode().equals(loginType)){

            String openId = authLoginReqDto.getOpenId();
            if(socialType==null)
                throw new SystemAuthException(SystemAuthExceptionEnum.THIRD_SOCIAL_TYPE_EMPTY);
            if(StringUtils.isBlank(openId))
                throw new SystemAuthException(SystemAuthExceptionEnum.THIRD_SOCIAL_AUTH_OPENID_EMPTY);
        }

        String username = authLoginReqDto.getUsername();

        if(RegisterSourceEnum.BUYER_REG.getCode().equals(sources)
                || RegisterSourceEnum.TRADER_REG.getCode().equals(sources)){//观众登记\参展申请

            if(StringUtils.isBlank(username))
                throw new SystemAuthException(SystemAuthExceptionEnum.MOBILE_OR_EMAIL_EMPTY);
            if(authLoginReqDto.getProjectId() == null)
                throw new BusinessException(ParamExceptionEnum.PROJECT_ID_EMPTY);
            if(!(socialType!=null && 34 == socialType)){
                if(StringUtils.isBlank(authLoginReqDto.getCode()))
                    throw new SystemAuthException(SystemAuthExceptionEnum.THIRD_SOCIAL_AUTH_TEMP_CODE_EMPTY);
            }
        }else if(RegisterSourceEnum.BUSINESS.getCode().equals(sources)
                || RegisterSourceEnum.VIP.getCode().equals(sources)
                || RegisterSourceEnum.SITE_TRADE.getCode().equals(sources)){//展商自助 行业网站

            if(StringUtils.isBlank(username))
                throw new SystemAuthException(SystemAuthExceptionEnum.MOBILE_OR_EMAIL_EMPTY);
            if(StringUtils.isBlank(authLoginReqDto.getCode()))
                throw new SystemAuthException(SystemAuthExceptionEnum.CAPTCHA_CODE_EMPTY);

        }else if(RegisterSourceEnum.WX_MINI_REG.getCode().equals(sources)){ //微信小程序
            /**
             * 点击一键登录点调取后端接口获取openId和手机号 然后在调取注册接口
             *  参数：
             *  orgNum 【机构号】、
             *  userName 【手机号】
             * 	sources 【来源，默认：WX_MINI_REG】、
             * 	sendType 【发送类型： 1 手机 2 邮箱】、
             * 	loginType 【默认：SOCIAL_LOGIN】、
             * 	socialType 【第三方授权类型,默认：34】、
             * 	openId 【微信openId】
             */
            if(StringUtils.isBlank(authLoginReqDto.getUsername())) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        }
//        else if(RegisterSourceEnum.VIP.getCode().equals(sources)){//会员中心
//            if(StringUtils.isBlank(authLoginReqDto.getUsername()) || StringUtils.isBlank(authLoginReqDto.getCode()))
//                return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
//        }
        authLoginReqDto.setThirdLoginFlag(true);
        authLoginReqDto.setRequestHeadMap(requestHead());
        AjaxResponse ajaxResponse = authService.register(authLoginReqDto);
        if(ajaxResponse.getState() > 0){
            Map<String, Object> objectMap = (Map<String, Object>) ajaxResponse.getData();
            if(MapUtils.isNotEmpty(objectMap)){
                if(Objects.nonNull(objectMap.get("zzyUserInfo"))){
                    AuthLoginRespVo authLoginRespVo = (AuthLoginRespVo)objectMap.get("zzyUserInfo");
                    authLoginRespVo.setProjectId(authLoginReqDto.getProjectId());
                    Map<String,Object> integralMap = Maps.newHashMap();
                    integralMap.put("requestIntegralUrl", RequestUtil.getSystemUrl());
                    zzyUserIntegralAsync.sendRegisterIntegral(authLoginRespVo,integralMap);
                }
            }
        }
        return ajaxResponse;
    }


    /**
     * 解密加密后的token
     * @return
     */
    @RequestMapping(value = "/decryptToken",method = RequestMethod.POST)
    public AjaxResponse decryptToken(){
        return getTokenFromRequest();
    }


    /**
     * 获取会员免登陆Token
     * @return
     */
    @RequestMapping(value = "/getMemberAuthToken",method = RequestMethod.POST)
    public AjaxResponse getMemberAuthToken(@RequestBody ApiMemberLoginTokenDto apiMemberLoginTokenDto){
        if(apiMemberLoginTokenDto.getZzyUserId() == null
           || StringUtils.isBlank(apiMemberLoginTokenDto.getLoginClientType())
           || StringUtils.isBlank(apiMemberLoginTokenDto.getSources())) {
            AjaxResponse resp = AjaxResponse.create(ApiResponseStateEnum.PARAM_MISS.getState());
            authService.addApiLog(apiMemberLoginTokenDto, resp, null, ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),apiMemberLoginTokenDto.getZzyUserId());
            return resp;
        }
        if(StringUtils.isNotBlank(apiMemberLoginTokenDto.getSources()) && RegisterSourceEnum.TRADER_REG.getCode().equals(apiMemberLoginTokenDto.getSources())){
            if(apiMemberLoginTokenDto.getProjectId() == null){
                AjaxResponse resp = AjaxResponse.create(ApiResponseStateEnum.PARAM_MISS.getState());
                authService.addApiLog(apiMemberLoginTokenDto, resp, null, ApiMethodEnum.GET_MEMBER_LOGIN.getCode(),apiMemberLoginTokenDto.getZzyUserId());
                return resp;
            }
        }
        return authService.getMemberAuthToken(apiMemberLoginTokenDto);
    }


}
