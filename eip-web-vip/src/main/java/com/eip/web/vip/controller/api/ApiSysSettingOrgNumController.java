package com.eip.web.vip.controller.api;

import com.eip.common.enums.CommonResponseStateEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.request.RequestUtil;
import com.eip.facade.vip.dto.SocialUserBindDto;
import com.eip.facade.vip.entity.SysSettingOrg;
import com.eip.facade.vip.service.SysSettingOrgService;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-07-03 17:34
 * @DESCRIPTION: 公共设置项
 */
@RestController
@RequestMapping(value = "/api/sysSettingOrgNum")
public class ApiSysSettingOrgNumController{

    private final Logger logger = LoggerFactory.getLogger(ApiSysSettingOrgNumController.class);

    @Autowired
    private SysSettingOrgService sysSettingOrgService;

    /**
     * 查询是否允许会员注册
     * @param settingCode
     * @param orgNum
     * @return
     */
    @RequestMapping(value = "/getValue", method = RequestMethod.POST)
    public AjaxResponse getValue(@RequestParam("settingCode") String settingCode, @RequestParam("orgNum") Integer orgNum){
        if(StringUtils.isBlank(settingCode) || orgNum == null){
            return AjaxResponse.failure("参数缺失");
        }
        Map<String, Object> resultMap = Maps.newHashMap();
        SysSettingOrg sysSettingOrg = new SysSettingOrg();
        sysSettingOrg.setSettingCode(settingCode);
        sysSettingOrg.setOrgNum(orgNum);
        SysSettingOrg sysSetOrg = sysSettingOrgService.query(sysSettingOrg);
        if(Objects.nonNull(sysSetOrg)){
            resultMap.put(settingCode,sysSetOrg.getSettingValue());
        }
        return AjaxResponse.success(resultMap);
    }

    /**
     * 获取社交登录类型
     * @param socialUserBindDto
     * @return
     */
    @RequestMapping(value = "/getSocialLogin",method = RequestMethod.POST)
    public AjaxResponse getSocialLogin(HttpServletRequest request,SocialUserBindDto socialUserBindDto){
        if(socialUserBindDto.getOrgNum() == null) return AjaxResponse.failure("机构号不能为空");
        boolean mobileClient = RequestUtil.isMobileClient();
        socialUserBindDto.setMobileClient(mobileClient);
//        boolean weChatBrowser = RequestUtil.isWechat(request);
//        socialUserBindDto.setWeChatBrowser(weChatBrowser);
        List<Map<String,Object>> socialList =  sysSettingOrgService.getSocialLogin(socialUserBindDto);
        return AjaxResponse.success(socialList);
    }

    /**
     * 获取社交登录类型
     * @param socialUserBindDto
     * @return
     */
    @RequestMapping(value = "/getSocialAppId",method = RequestMethod.POST)
    public AjaxResponse getSocialAppId(HttpServletRequest request,SocialUserBindDto socialUserBindDto){
        if(socialUserBindDto.getOrgNum() == null
                || socialUserBindDto.getSocialType() == null
        ) return AjaxResponse.create(CommonResponseStateEnum.PARAM_MISS);
        String appId = sysSettingOrgService.getSocialAppId(socialUserBindDto);
        return AjaxResponse.success("",appId);
    }
}
