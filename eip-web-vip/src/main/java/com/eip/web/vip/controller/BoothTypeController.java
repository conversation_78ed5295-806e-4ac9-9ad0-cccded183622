package com.eip.web.vip.controller;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.entity.BoothType;
import com.eip.facade.vip.service.BoothTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping("/boothType")
public class BoothTypeController extends BaseController {

	@Autowired
	private BoothTypeService boothTypeService;
	
	@RequestMapping(value = "/getAll", method = RequestMethod.POST)
	public void getAll(String exhibitCode, HttpServletResponse response){
//		List<BoothType> list = boothTypeService.selectAll(exhibitCode);
//		Boolean canDot = false;
//		StringBuilder json = new StringBuilder().append("[");
//		for(BoothType b : list){
//			if(canDot){
//				json.append(",");
//			}
//			canDot = true;
//			json.append("{\"id\":\"" + b.getTypeCode() + "\",\"text\":\"" + b.getTypeName() + "\"}");
//		}
//		json.append("]");
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println(json.toString());
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return;
	}

	@ResponseBody
	@RequestMapping(value = "/isExistTypeCode", method = RequestMethod.POST)
	public AjaxResponse isExistTypeCode(int id, String typeCode, HttpServletResponse response){
//		int state = 0;
//		if(boothTypeService.isExistTypeCode(id, typeCode))state = 1;
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"state\":" + state + "}");
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return AjaxResponse.interfaceDeactivation();
	}

	@ResponseBody
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public AjaxResponse save(String editMode, BoothType boothType, HttpServletResponse response){
//		int state = 0;
//		if(editMode.equals("Add")){
//			int id = boothTypeService.getSysId("orgdata_t_booth_type_f_id", true);
//			boothType.setId(id);
//			try{
//				state = boothTypeService.insert(boothType);
//			}catch(Exception e){
//				logger.error(e.toString(), e);
//				state = 0;
//			}
//		}else if(editMode.equals("Mod")){
//			try{
//				state = boothTypeService.update(boothType);
//			}catch(Exception e){
//				logger.error(e.toString(), e);
//				state = 0;
//			}
//		}
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"state\":" + state + "}");
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return AjaxResponse.interfaceDeactivation();
	}

	@ResponseBody
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public AjaxResponse delete(int id, HttpServletResponse response){
//		int state = 0;
//		try{
//			state = boothTypeService.delete(id);
//		}catch(Exception e){
//			logger.error(e.toString(), e);
//			state = 0;
//		}
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"state\":" + state + "}");
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return AjaxResponse.interfaceDeactivation();
	}

	@ResponseBody
	@RequestMapping(value = "/deleteBatch", method = RequestMethod.POST)
	public AjaxResponse deleteBatch(@RequestBody List<BoothType> boothTypes, HttpServletResponse response){
//		int state = 0;
//		try{
//			state = boothTypeService.deleteBatch(boothTypes);
//		}catch(Exception e){
//			logger.error(e.toString(), e);
//			state = 0;
//		}
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"state\":" + state + "}");
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return AjaxResponse.interfaceDeactivation();
	}

	@ResponseBody
	@RequestMapping(value = "/getList", method = RequestMethod.POST)
	public AjaxResponse getList(PageParam pageParam, int key, String typeName, String exhibitCode, HttpServletResponse response){
//		Boolean b = false;
//		if(key == 1){
//			b = true;
//		}
//		int total = boothTypeService.count(b, typeName, exhibitCode);
//		List<BoothType> list = boothTypeService.select(pageParam, b, typeName, exhibitCode);
//		ObjectMapper mapper = new ObjectMapper();
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"total\":" + total + ",\"rows\":" + mapper.writeValueAsString(list) + "}");
//		} catch (JsonProcessingException e) {
//			logger.error(e.toString(), e);
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return AjaxResponse.interfaceDeactivation();
	}
	
	@RequestMapping(value = "/getSysId", method = RequestMethod.POST)
	public void getSysId(HttpServletResponse response){
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println("{\"id\":" + boothTypeService.getSysId("orgdata_t_booth_type_f_id", false) + "}");
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return;
	}
	
	@RequestMapping(value = "/selectByTypeCode", method = RequestMethod.POST)
	public void selectByTypeCode(String typeCode, HttpServletResponse response){
//		BoothType boothType = boothTypeService.selectByTypeCode(typeCode);
//		ObjectMapper mapper = new ObjectMapper();
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println(mapper.writeValueAsString(boothType));
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return;
	}
	
	@RequestMapping(value = "/getByBoothSpec", method = RequestMethod.POST)
	public void getByBoothSpec(String exhibitCode, String boothSpec, HttpServletResponse response){
//		StringBuilder json = new StringBuilder().append("[");
//		BoothType boothType = boothTypeService.selectStandard(exhibitCode, boothSpec);
//		if(boothType != null){
//			json.append("{\"id\":\"" + boothType.getTypeCode() + "\",\"text\":\"" + boothType.getTypeName() + "\"}");
//		}else{
//			List<BoothType> list = boothTypeService.selectNotStandard(exhibitCode);
//			Boolean canDot = false;
//			for(BoothType b : list){
//				if(canDot){
//					json.append(",");
//				}
//				canDot = true;
//				json.append("{\"id\":\"" + b.getTypeCode() + "\",\"text\":\"" + b.getTypeName() + "\"}");
//			}
//		}
//		json.append("]");
//		response.setContentType("text/html;charset=UTF-8");
//		try {
//			response.getWriter().println(json.toString());
//		} catch (IOException e) {
//			logger.error(e.toString(), e);
//		}
		return;
	}
	
}
