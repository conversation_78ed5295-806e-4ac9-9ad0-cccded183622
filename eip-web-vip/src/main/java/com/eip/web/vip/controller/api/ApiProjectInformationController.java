package com.eip.web.vip.controller.api;

import com.eip.common.exception.BusinessException;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.page.wrapper.Wrapper;
import com.eip.facade.vip.dto.queryParam.ProjectInformationQueryParam;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.service.ProjectInformationService;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.vo.ProjectInformationVo;
import com.eip.web.vip.controller.common.CommonController;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2023-07-19 9:49
 * @DESCRIPTION:
 */
@RestController
@RequestMapping(value = "/api/projectInformation")
public class ApiProjectInformationController extends CommonController {

    @Autowired
    private ProjectInformationService projectInformationService;
    @Autowired
    private ProjectService projectService;

    /**
     * 根据项目id 获取项目信息
     * @param queryParam
     * @return
     */
    @RequestMapping(value = "/getInformationByProjectId",method = RequestMethod.POST)
    public Wrapper getInformationByProjectId(ProjectInformationQueryParam queryParam) {
        Integer projectId = queryParam.getProjectId();
        Integer orgNum = queryParam.getOrgNum();
        if(projectId == null) throw new BusinessException(ParamExceptionEnum.PROJECT_ID_EMPTY);
        //会员中心 过滤敏感设置项
        if(0 == projectId && orgNum == null) throw new BusinessException(ParamExceptionEnum.ORG_NUM_EMPTY);
        //展商自助服务中心
        if(projectId > 0){
            Project byId = projectService.getById(projectId);
            if(Objects.isNull(byId)) throw new BusinessException(ParamExceptionEnum.PROJECT_NOT_EXIST);
            queryParam.setOrgNum(byId.getF_org_num());
        }
        List<ProjectInformationVo> projectInformationList = projectInformationService.selectProjectInformationVo(queryParam);
        return handleResult(projectInformationList);
    }

    /**
     * 根据项目id 获取项目信息
     * @param queryParam
     * @return
     */
    @RequestMapping(value = "/getExhibitorCenterSetInfo",method = RequestMethod.POST)
    public AjaxResponse getExhibitorCenterSetInfo(ProjectInformationQueryParam queryParam) {
        Integer projectId = queryParam.getProjectId();
        String clientType = queryParam.getClientType();
        String businessType = queryParam.getBusinessType();
        if(projectId == null || StringUtils.isBlank(clientType) || StringUtils.isBlank(businessType)) return AjaxResponse.create(ParamExceptionEnum.PARAM_MISS);
        if(projectId == 0) return AjaxResponse.create(ParamExceptionEnum.INVALID_PARAMETER_VALUE);
        return projectInformationService.getExhibitorCenterSetInfo(queryParam);
    }



}
