package com.eip.web.vip.controller.api;

import com.eip.facade.vip.entity.ShortLink;
import com.eip.facade.vip.service.ShortLinkService;
import com.eip.web.vip.controller.common.CommonController;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: zwd
 * @DATE: 2024-09-13 10:57
 * @DESCRIPTION: 短链服务
 */
@RestController
@RequestMapping(value = "/r")
public class ApiShortLinkServerController extends CommonController {

    @Autowired
    private ShortLinkService shortLinkService;


    /**
     * 通过短链返回长链接并重定向
     *
     * @param shortLinkUrl
     * @return
     */
    @RequestMapping(value = "/{shortLinkUrl}")
    public void dealShortLink(@PathVariable("shortLinkUrl") String shortLinkUrl, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(12);
        paramMap.put("shortLink", shortLinkUrl);
        List<ShortLink> shortLinkList = shortLinkService.queryShortUrlByMd5(paramMap);
        if (CollectionUtils.isNotEmpty(shortLinkList)) {
            ShortLink shortLink = shortLinkList.get(0);
            if (Objects.nonNull(shortLink) && StringUtils.isNotBlank(shortLink.getOriginalLink())) {
                response.setHeader("Location", getSystemUrl(request) + shortLink.getOriginalLink());
                response.sendError(HttpServletResponse.SC_MOVED_TEMPORARILY);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    public String getSystemUrl(HttpServletRequest request) {
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();
        String requestUrl = "//" + serverName;
        if (serverPort != 80) {
            requestUrl = requestUrl + ":" + serverPort;
        }
        if (!requestUrl.endsWith("/")) requestUrl += "/";
        return requestUrl;
    }
}
