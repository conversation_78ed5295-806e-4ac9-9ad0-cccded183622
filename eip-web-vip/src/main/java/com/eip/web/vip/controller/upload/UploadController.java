package com.eip.web.vip.controller.upload;

import cn.hutool.http.HttpUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.eip.common.controller.BaseController;
import com.eip.common.enums.SysSettingEnum;
import com.eip.common.enums.oss.AliOssAccountEnum;
import com.eip.common.util.FileUploadUtil;
import com.eip.common.util.PdfXssDetectionUtils;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.common.util.oss.OssDatabaseUtils;
import com.eip.common.util.page.wrapper.Wrapper;
import com.eip.facade.vip.entity.SysSetting;
import com.eip.facade.vip.service.SysSettingService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 上传文件
 *
 * @author: ZzzHhYyy
 * @Date: 2019/12/5 16:26
 */
@RestController
@RequestMapping("/upload")
public class UploadController extends BaseController {
    @Autowired
    private SysSettingService sysSettingService;


    /**
     * 上传单个文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public Wrapper uploadFile(@RequestParam("file") MultipartFile file) throws Exception {
       // return handleResult(FileUploadUtil.uploadFile(file));
        AjaxResponse resp = PdfXssDetectionUtils.filterDocumentSecurity(file);
        if(resp.getState()!=1) return errorResult(resp.getMsg());
        String filePath = sysSettingService.getValue(SysSettingEnum.FileUploadLocalPath.getCode());
    	return handleResult(FileUploadUtil.uploadFile(filePath,file,true));
    }



    /**
     * 上传图片
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadFileOss", method = RequestMethod.POST)
    public Wrapper uploadFileOss(@RequestParam("file") MultipartFile file,@RequestParam(value = "compressFlag",required = false) Boolean compressFlag) throws Exception {
//        SysSetting sysSetting = new SysSetting();
//        OssDatabaseUtils ossDatabaseUtils = new OssDatabaseUtils();
//
//        sysSetting.setSettingCode("OssAccessKeyId");
//        List<SysSetting> accessKeyId = sysSettingService.select(sysSetting);
//        if(CollectionUtils.isNotEmpty(accessKeyId) && StringUtils.isNotBlank(accessKeyId.get(0).getSettingValue())) ossDatabaseUtils.setACCESS_KEY_ID(accessKeyId.get(0).getSettingValue());
//
//        sysSetting.setSettingCode("OssAccessKeySecret");
//        List<SysSetting> accessKeySecret = sysSettingService.select(sysSetting);
//        if(CollectionUtils.isNotEmpty(accessKeySecret) && StringUtils.isNotBlank(accessKeySecret.get(0).getSettingValue())) ossDatabaseUtils.setACCESS_KEY_SECRET(accessKeySecret.get(0).getSettingValue());
//
//        sysSetting.setSettingCode("OssBucketName");
//        List<SysSetting> bucketName = sysSettingService.select(sysSetting);
//        if(CollectionUtils.isNotEmpty(bucketName) && StringUtils.isNotBlank(bucketName.get(0).getSettingValue())) ossDatabaseUtils.setBUCKET_NAME(bucketName.get(0).getSettingValue());
//
//        sysSetting.setSettingCode("OssEndpoint");
//        List<SysSetting> endpoint = sysSettingService.select(sysSetting);
//        if(CollectionUtils.isNotEmpty(endpoint) && StringUtils.isNotBlank(endpoint.get(0).getSettingValue())) ossDatabaseUtils.setENDPOINT(endpoint.get(0).getSettingValue());
        AjaxResponse resp = PdfXssDetectionUtils.filterDocumentSecurity(file);
        if(resp.getState()!=1) return errorResult(resp.getMsg());
        return handleResult(FileUploadUtil.uploadFileOss(file,getAndBuildOssAccount(),compressFlag));
    }

    /**
     * 获取并构建阿里云账号
     * @return
     */
    private OssDatabaseUtils getAndBuildOssAccount(){
        Map<String, Object> ossDatabaseUtilMap = sysSettingService.buildOssDataBase();
        if(MapUtils.isEmpty(ossDatabaseUtilMap)) return null;
        OssDatabaseUtils ossDatabaseUtils = new OssDatabaseUtils();
        ossDatabaseUtils.setACCESS_KEY_ID(String.valueOf(ossDatabaseUtilMap.get(AliOssAccountEnum.ACCESS_KEY_ID.getCode())));
        ossDatabaseUtils.setACCESS_KEY_SECRET(String.valueOf(ossDatabaseUtilMap.get(AliOssAccountEnum.ACCESS_KEY_SECRET.getCode())));
        ossDatabaseUtils.setBUCKET_NAME(String.valueOf(ossDatabaseUtilMap.get(AliOssAccountEnum.BUCKET_NAME.getCode())));
        ossDatabaseUtils.setENDPOINT(String.valueOf(ossDatabaseUtilMap.get(AliOssAccountEnum.ENDPOINT.getCode())));
        return ossDatabaseUtils;
    }

    /**
     * 上传图片
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadImg", method = RequestMethod.POST)
    public Wrapper uploadImg(@RequestParam("file") MultipartFile file) throws Exception {
        AjaxResponse resp = PdfXssDetectionUtils.filterDocumentSecurity(file);
        if(resp.getState()!=1) return errorResult(resp.getMsg());
        String filePath = sysSettingService.getValue(SysSettingEnum.FileUploadLocalPath.getCode());
        return handleResult(FileUploadUtil.uploadImg(filePath,file));
    }

    /**
     * 上传多个文件
     *
     * @param files
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadFiles", method = RequestMethod.POST)
    public Wrapper uploadFile(@RequestParam("files") MultipartFile[] files) throws Exception {
        String filePath = sysSettingService.getValue(SysSettingEnum.FileUploadLocalPath.getCode());
        return handleResult(FileUploadUtil.uploadFiles(filePath,files));
    }

    /**
     * 上传文件  动态选择存储服务器 （oss和本地）
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/uploadFileDynamic", method = RequestMethod.POST)
    public Wrapper uploadFileDynamic(@RequestParam("file") MultipartFile file,@RequestParam(value = "compressFlag",required = false,defaultValue = "true") Boolean compressFlag) throws Exception {
        SysSetting sysSetting = new SysSetting();
        sysSetting.setSettingCode("OssUsing");
        List<SysSetting> OssUsing = sysSettingService.select(sysSetting);
        if(CollectionUtils.isNotEmpty(OssUsing) && "1".equals(OssUsing.get(0).getSettingValue())){ //启用oss上传
            return uploadFileOss(file,compressFlag);
        }
        String filePath = sysSettingService.getValue(SysSettingEnum.FileUploadLocalPath.getCode());
        HashMap<String, String> map = FileUploadUtil.uploadFile(filePath,file, true);
        String path = map.get("path");
        if(StringUtils.isNotBlank(path))path = File.separator+"image"+path;
        map.put("path", path);
        return handleResult(map);
    }

    /**
     * oss图片后台请求输出
     * @param ossUrl
     * @param response
     */
    @RequestMapping(value = "/ossOut")
    public void ossOut(String ossUrl, HttpServletResponse response, HttpServletRequest request) {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            if (StringUtils.isBlank(ossUrl)) return;
            if(!HttpUtil.isHttp(ossUrl) && !HttpUtil.isHttps(ossUrl)){//如果没有http或http开口 则直接追加
                ossUrl = request.getScheme() + ":"+ossUrl;
            }
            URL url = new URL(ossUrl);
            SysSetting ossBucketName = sysSettingService.getById("OssBucketName");
            if (ossBucketName == null || StringUtils.isBlank(ossBucketName.getSettingValue())) return;
            SysSetting ossEndpoint = sysSettingService.getById("OssEndpoint");
            if (ossEndpoint == null || StringUtils.isBlank(ossEndpoint.getSettingValue())) return;
            String host = ossBucketName.getSettingValue() + "." + ossEndpoint.getSettingValue();
            if (!host.equals(url.getHost())) return;

            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            // 设置网络连接超时时间
            httpURLConnection.setConnectTimeout(3000);
            // 设置应用程序要从网络连接读取数据
            httpURLConnection.setDoInput(true);
            String referer = request.getHeader("Referer");
            if (StringUtils.isNotBlank(referer)) {
                httpURLConnection.setRequestProperty("Referer", referer);
            }
            httpURLConnection.setRequestMethod("GET");
            int responseCode = httpURLConnection.getResponseCode();
            if (responseCode == 200) {
                // 从服务器返回一个输入流
                inputStream = httpURLConnection.getInputStream();
            }
            // 给图片设置缓存
            response.setDateHeader("Expires", System.currentTimeMillis() + 1000L * 3600 * 24 * 365);
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "public, max-age=" + (3600 * 24 * 365));
            response.setHeader("Content-type", "image/png"); //设置发送到客户端的响应内容类型
            outputStream = response.getOutputStream();
            byte[] buff = new byte[1024];//byte数组用于存放图片字节数据
            int len = 0;
            while ((len = inputStream.read(buff)) != -1) {
                outputStream.write(buff, 0, len);
            }
            outputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
