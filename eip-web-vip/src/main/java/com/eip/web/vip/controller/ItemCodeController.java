package com.eip.web.vip.controller;

import com.eip.common.controller.BaseController;
import com.eip.common.entity.PageParam;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.ItemCodeDto;
import com.eip.facade.vip.entity.ItemCode;
import com.eip.facade.vip.service.ItemCodeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Controller
@RequestMapping("/itemCode")
public class ItemCodeController extends BaseController {

	@Autowired
	private ItemCodeService itemCodeService;


	@ResponseBody
	@RequestMapping(value = "/getList", method = RequestMethod.POST)
	public AjaxResponse getList(PageParam pageParam, ItemCodeDto itemCodeDto){
		List<ItemCode> itemCodes = null;
		int total = itemCodeService.count(itemCodeDto);
		if(total > 0){
			itemCodes = itemCodeService.select(pageParam,itemCodeDto);
		}
		return AjaxResponse.pageSuccess(total,itemCodes);
	}

	@RequestMapping(value = "/selectByItemKindCodeElse")
	public void selectByItemKindCodeElse(ItemCodeDto itemCodeDto, HttpServletResponse response){
		List<ItemCode> list = itemCodeService.selectByItemKindCode(itemCodeDto);
		ObjectMapper mapper = new ObjectMapper();
		response.setContentType("text/html;charset=UTF-8");
		try {
			response.getWriter().println(mapper.writeValueAsString(list));
		} catch (IOException e) {
			logger.error(e.toString(), e);
		}
	}

	@RequestMapping(value = "/selectByItemKindCode")
	public void selectByItemKindCode(ItemCodeDto itemCodeDto, HttpServletResponse response){
		List<ItemCode> list = itemCodeService.selectZzysysByItemKindCode(itemCodeDto);
		ObjectMapper mapper = new ObjectMapper();
		response.setContentType("text/html;charset=UTF-8");
		try {
			response.getWriter().println(mapper.writeValueAsString(list));
		} catch (IOException e) {
			logger.error(e.toString(), e);
		}
	}
	


}
