package com.eip.web.vip.controller;

import com.eip.common.entity.PageParam;
import com.eip.common.exception.BusinessException;
import com.eip.common.exception.enums.ParamExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.ProjectDto;
import com.eip.facade.vip.entity.Project;
import com.eip.facade.vip.entity.ProjectPic;
import com.eip.facade.vip.service.ProjectService;
import com.eip.facade.vip.vo.api.brandWebSiteVo.BrandProjectVo;
import com.eip.web.vip.controller.common.CommonController;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @company    ：杭州展之科技
 * <AUTHOR>
 * @date       ：Created in 2022-07-18 15:42
 * @description：项目
 */
@RestController
@RequestMapping(value = "/project")
public class ProjectController extends CommonController {

    @Autowired
    private ProjectService projectService;

    /**
     * 获取列表数据
     * @param pageParam
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getPage",method = RequestMethod.POST)
    public AjaxResponse getPage(PageParam pageParam, ProjectDto projectDto){
        projectDto.setOrgNum(getUserInfo().getFocusOrgNum());
        projectDto.setCommonId(getUserInfo().getZzyUserId());
        List<Project> projectList = Lists.newArrayList();
        int count  = projectService.countPorject(projectDto);
        if(count > 0){
            projectList =  projectService.getPage(pageParam,projectDto);
        }
        return AjaxResponse.pageSuccess(count,projectList);
    }

    /**
     * 保存项目
     * @param project
     * @return
     */
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public AjaxResponse save(Project project){
        if(StringUtils.isBlank(project.getF_project_name()) || project.getTradeId() == null) return AjaxResponse.paramMiss();
        project.setCreateZzyuserId(getUserInfo().getZzyUserId());
        project.setF_org_num(getUserInfo().getFocusOrgNum());
        return projectService.save(project);
    }

    /**
     * 根据项目ID删除项目
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    public AjaxResponse delete(ProjectDto projectDto){
        if(projectDto.getProjectId() == null || projectDto.getCommonId() == null) return AjaxResponse.paramMiss();
        return projectService.deleteProjectById(projectDto);
    }

    /**
     * 创建的展会自动以今年为准 自动往后加2年
     * 如： 2022  2023 2024
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/getQueryCondition",method = RequestMethod.POST)
    public AjaxResponse getQueryCondition(ProjectDto projectDto){
        Integer commonId = projectDto.getCommonId();
        if(commonId == null || projectDto.getOrgNum() == null) return AjaxResponse.paramMiss();
        return projectService.getQueryCondition(projectDto);
    }

    /**
     * 根据项目Id查询项目
     * 如： 2022  2023 2024
     * @param projectDto
     * @return
     */
    @RequestMapping(value = "/selectProjectById",method = RequestMethod.POST)
    public AjaxResponse selectProjectById(ProjectDto projectDto){
        Integer projectId = projectDto.getProjectId();
        if(projectId == null) return AjaxResponse.paramMiss();
        return projectService.selectProjectById(projectDto);
    }

    /**
     * 根据项目Id更新其它字段值
     * @return
     */
    @RequestMapping(value = "/updateFieldValue",method = RequestMethod.POST)
    public AjaxResponse updateFieldValue(Project project){
        Integer projectId = project.getF_project_id();
        if(projectId == null) return AjaxResponse.paramMiss();
        return projectService.updateFieldValue(project);
    }

    /**
     * 保存项目图片
     * @return
     */
    @RequestMapping(value = "/saveProjectPic",method = RequestMethod.POST)
    public AjaxResponse saveProjectPic(ProjectPic projectPic){
        Integer projectId = projectPic.getProjectId();
        if(projectId == null) return AjaxResponse.paramMiss();
        return projectService.saveProjectPic(projectPic);
    }

    /**
     * 根据项目图片ID删除项目图片
     * @return
     */
    @RequestMapping(value = "/deleteProjectPicById",method = RequestMethod.POST)
    public AjaxResponse deleteProjectPicById(ProjectPic projectPic){
        Integer projectPicId = projectPic.getProjectPicId();
        if(projectPicId == null) return AjaxResponse.paramMiss();
        return projectService.deleteProjectPicById(projectPic);
    }

    /**
     * 更新项目发布状态 和 项目审核状态
     * @return
     */
    @RequestMapping(value = "/updateProjectState",method = RequestMethod.POST)
    public AjaxResponse updateProjectState(ProjectDto projectDto){
        Integer projectId = projectDto.getProjectId();
        if(projectId == null || projectDto.getProjectPublishFlag()==null) return AjaxResponse.paramMiss();
        return projectService.updateProjectState(projectDto);
    }

    /**
     * 根据机构查询该机构 一级的品牌项目
     * @param orgNum
     * @return
     */
    @RequestMapping(value = "/getTopLevelBrandProject",method = RequestMethod.POST)
    public AjaxResponse getTopLevelBrandProject(Integer orgNum) {
        if (orgNum == null) throw new BusinessException(ParamExceptionEnum.BUSINESS_ID_EMPTY);
        List<BrandProjectVo> list = projectService.getTopLevelBrandProject(orgNum);
        return AjaxResponse.success(list);
    }

}
