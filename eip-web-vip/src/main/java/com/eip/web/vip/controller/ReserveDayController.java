package com.eip.web.vip.controller;

import com.eip.common.controller.BaseController;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.entity.ReserveDay;
import com.eip.facade.vip.service.ReserveDayService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@RequestMapping("/reserveDay")
public class ReserveDayController extends BaseController {

	@Autowired
	private ReserveDayService reserveDayService;
	
	@RequestMapping(value = "/selectByExhibitCode", method = RequestMethod.POST)
	public void selectByExhibitCode(String exhibitCode, HttpServletResponse response){
		ReserveDay reserveDay = null;
		if(StringUtils.isNotBlank(exhibitCode)){
			reserveDay = reserveDayService.selectByExhibitCode(exhibitCode);
		}
		ObjectMapper mapper = new ObjectMapper();
		response.setContentType("text/html;charset=UTF-8");
		try {
			response.getWriter().println(mapper.writeValueAsString(reserveDay));
		} catch (Exception e) {
			logger.error("查询异常：{}", e);
		}
	}

	@ResponseBody
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public AjaxResponse updateScale(ReserveDay reserveDay){
		if(StringUtils.isBlank(reserveDay.getExhibitCode())) return AjaxResponse.failure("展会Code为空");
		int state = reserveDayService.update(reserveDay);
		return AjaxResponse.create(state);
	}
	
}
