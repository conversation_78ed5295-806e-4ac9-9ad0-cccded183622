package com.eip.web.vip.controller;

import com.eip.common.exception.SystemAuthException;
import com.eip.common.exception.enums.SystemAuthExceptionEnum;
import com.eip.common.util.ajax.AjaxResponse;
import com.eip.facade.vip.dto.TaskDtlDto;
import com.eip.facade.vip.service.TaskService;
import com.eip.facade.vip.vo.AuthLoginRespVo;
import com.eip.web.vip.controller.common.CommonController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
@RequestMapping("/task")
public class TaskController extends CommonController {

	 @Resource
	 private TaskService taskService;

	/**
	 * 获取机构设置任务
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/getOrgNumSetTask",method = RequestMethod.POST)
	 public AjaxResponse getOrgNumSetTask(TaskDtlDto taskDtlDto){
		AuthLoginRespVo userInfo = getUserInfo();
		if(Objects.isNull(userInfo)) throw new SystemAuthException(SystemAuthExceptionEnum.LOGIN_EXPIRED);
		taskDtlDto.setOrgNum(userInfo.getFocusOrgNum());
		List<Map<String,String>> taskKindOrgNumList = taskService.getOrgNumSetTask(taskDtlDto);
		return AjaxResponse.success(taskKindOrgNumList);
	 }
}
